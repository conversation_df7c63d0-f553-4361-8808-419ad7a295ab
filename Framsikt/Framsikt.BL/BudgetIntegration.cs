#pragma warning disable CS86<PERSON>
#pragma warning disable CS8602
#pragma warning disable CS8619
#pragma warning disable CS8625

using Aspose.Cells;
using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;
using System.Text;

namespace Framsikt.BL
{
    public class BudgetIntegration : IBudgetIntegration
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUtility _utility;
        private readonly IAppDataCache _cache;
        private readonly IBackendRequest _backendJob;
        private readonly IStaffPlanning _staffPlanning;
        private readonly IAzureBlobHelper _azureBlobHelper;
        private readonly Dictionary<string, string> columnMapping = new Dictionary<string, string>()
        {
            { "TenantId", "fk_tenant_id" },
            { "budget_year", "budget_year" },
            { "UserId", "user_id" },
            { "fk_account_code", "fk_account_code" },
            { "fk_department_code", "fk_department_code" },
            { "fk_function_code", "fk_function_code" },
            { "fk_project_code", "fk_project_code" },
            { "free_dim_1", "free_dim_1" },
            { "free_dim_2", "free_dim_2" },
            { "free_dim_3", "free_dim_3" },
            { "free_dim_4", "free_dim_4" },
            { "period", "period" },
            { "amount", "amount" },
            { "updated", "updated" },
            { "updated_by", "updated_by" }
        };

        public BudgetIntegration(IUnitOfWork uow, IUtility util, IAppDataCache cache, IBackendRequest backendJob, IStaffPlanning staffPlanning, IAzureBlobHelper azureBlobHelper)
        {
            _unitOfWork = uow;
            _utility = util;
            _cache = cache;
            _backendJob = backendJob;
            _staffPlanning = staffPlanning;
            _azureBlobHelper = azureBlobHelper;
        }

        public async Task<JArray> GetBatches(string userId, int budgetYear, bool isOriginalBudget)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            List<integnew_export_visma_postbatches> lstVisma = new List<integnew_export_visma_postbatches>();
            var dataset = (from a in lstVisma
                           select new
                           {
                               key = string.Empty,
                               value = string.Empty
                           }).ToList();

            dynamic arr = new JArray();
            if (tenantData.erp_type.ToLower() == "Agresso".ToString().ToLower())
            {
                List<integ_agrpostback_batches> lstAgresso = await _unitOfWork.BudgetIntegrationRepository.GetBatchesAgresso(userDetails.tenant_id, budgetYear, isOriginalBudget);
                dataset = (from a in lstAgresso
                           select new
                           {
                               key = a.Batchid,
                               value = a.Batchid
                           }).Distinct().ToList();
            }
            else
            {
                lstVisma = await _unitOfWork.BudgetIntegrationRepository.GetBatchesVisma(userDetails.tenant_id, budgetYear, isOriginalBudget);
                dataset = (from a in lstVisma
                           select new
                           {
                               key = a.batch_id,
                               value = a.batch_id
                           }).Distinct().ToList();
            }

            foreach (var row in dataset)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.key;
                rowObj.value = row.value;
                arr.Add(rowObj);
            }
            return arr;
        }

        public async Task<JObject> GetBatchCorrectionsById(string userId, string batchId, int skip, int take,
                                                           string error, string dim1Type, string dim2Type, string dim1Id, string dim2Id,
                                                           string sortKey, string sortOrder, int budgetYear, bool isOriginalBudget)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetInteg");
            bool disableSendBatch = true;
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            var batchDetailsById = await _unitOfWork.BudgetIntegrationRepository.GetBatchCorrectionsByIdAsync(userDetails.tenant_id, batchId);
            var integrationErrorTypes = await _unitOfWork.BudgetIntegrationRepository.GetIntegrationErrorTypes();

            var dataset = (from a in batchDetailsById
                           join b in integrationErrorTypes on a.fk_error_type equals b.pk_error_type into p1
                           from p2 in p1.DefaultIfEmpty()
                           select new BudgetIntegrationsBatchCorrections
                           {
                               pkId = a.pk_id,
                               error = p2 == null ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_account_code")).Value).LangText : p2.error_name,
                               dim1Type = a.dim_1_type == null ? "" : (a.dim_1_type.ToLower() == "fk_department_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_department_code")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_account_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_account_code")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_function_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_function_code")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_project_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_project_code")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_freedim1_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_1")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_freedim2_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_2")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_freedim3_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_3")).Value).LangText :
                                          a.dim_1_type.ToLower() == "fk_freedim4_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_4")).Value).LangText : ""),
                               dim2Type = a.dim_2_type == null ? "" : (a.dim_2_type.ToLower() == "fk_department_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_department_code")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_account_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_account_code")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_function_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_function_code")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_project_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_project_code")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_freedim1_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_1")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_freedim2_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_2")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_freedim3_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_3")).Value).LangText :
                                          a.dim_2_type.ToLower() == "fk_freedim4_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_4")).Value).LangText : ""),
                               dim1Id = a.dim_1_id == null ? "" : a.dim_1_id,
                               dim2Id = a.dim_2_id == null ? "" : a.dim_2_id,
                               changeDimension = a.dim_to_change == null ? "" : (a.dim_to_change.ToLower() == "fk_department_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_department_code")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_account_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_account_code")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_function_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_function_code")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_project_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_project_code")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_freedim1_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_1")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_freedim2_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_2")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_freedim3_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_3")).Value).LangText :
                                                 a.dim_to_change.ToLower() == "fk_freedim4_code" ? ((langStringValue.FirstOrDefault(v => v.Key == "dim_type_fk_freedim_4")).Value).LangText : ""),
                               changeDimensionType = a.dim_to_change == null ? "" : a.dim_to_change,
                               newId = a.id_new,
                               keep = a.keep,
                               fixed_error = a.isFixed
                           }).ToList();

            dataset = SearchBatchCorrectionsById(dataset, error, dim1Type, dim2Type, dim1Id, dim2Id);

            dataset = SortBatchCorrectionsById(dataset, sortKey, sortOrder);
            int count = dataset.Count;
            if (isOriginalBudget)
            {
                    var flagValue = tenantDBContext.tco_parameters.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.param_value == budgetYear.ToString() && x.param_name.ToLower() == "LOCK_ORIGINAL_BUDGET".ToLower() && x.active == 1);
                    disableSendBatch = flagValue == null;
            }
            else
            {
                if (string.IsNullOrEmpty(batchId))
                {
                    disableSendBatch = true;
                }
                else if (tenantData.erp_type.ToLower() == "Agresso".ToString().ToLower())
                {
                    var Agresso = await _unitOfWork.BudgetIntegrationRepository.GetBatchDetailsByIdAgresso(userDetails.tenant_id, batchId);
                    disableSendBatch = !Agresso.error_fixed;
                }
                else
                {
                    var Visma = await _unitOfWork.BudgetIntegrationRepository.GetBatchDetailsByIdVisma(userDetails.tenant_id, batchId);
                    disableSendBatch = !Visma.error_fixed;
                }
            }

            if (!(skip == 0 && take == 0))
            {
                dataset = dataset.Skip(skip).Take(take).ToList();
            }
            dynamic arr = new JArray();
            foreach (var row in dataset)
            {
                dynamic rowObj = new JObject();
                rowObj.pkId = row.pkId;
                rowObj.errorType = row.error;
                rowObj.dim1Type = row.dim1Type;
                rowObj.dim2Type = row.dim2Type;
                rowObj.dim1Id = row.dim1Id;
                rowObj.dim2Id = row.dim2Id;

                dynamic changeDimension = new JObject();
                changeDimension.key = row.changeDimension;
                changeDimension.value = row.changeDimension;
                rowObj.changeDimension = changeDimension;

                rowObj.changeDimensionType = row.changeDimensionType;

                dynamic newId = new JObject();
                newId.key = row.newId;
                newId.value = row.newId;
                rowObj.newId = newId;

                rowObj.keepCheck = row.keep;
                rowObj.disableEdit = false;
                arr.Add(rowObj);
            }

            dynamic obj = new JObject();
            obj.Add("data", arr);
            obj.Add("totalRows", count);
            obj.Add("disableSendBatch", disableSendBatch);
            return obj;
        }

        public static List<BudgetIntegrationsBatchCorrections> SearchBatchCorrectionsById(List<BudgetIntegrationsBatchCorrections> lst, string error, string dim1Type, string dim2Type, string dim1Id, string dim2Id)
        {
            if (!string.IsNullOrEmpty(error))
            {
                lst = lst.Where(x => x.error.ToLower().Contains(error.ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(dim1Type))
            {
                lst = lst.Where(x => x.dim1Type.ToLower().Contains(dim1Type.ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(dim2Type))
            {
                lst = lst.Where(x => x.dim2Type.ToLower().Contains(dim2Type.ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(dim1Id))
            {
                lst = lst.Where(x => x.dim1Id.ToLower().Contains(dim1Id.ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(dim2Id))
            {
                lst = lst.Where(x => x.dim2Id.ToLower().Contains(dim2Id.ToLower())).ToList();
            }

            return lst;
        }

        public static List<BudgetIntegrationsBatchCorrections> SortBatchCorrectionsById(List<BudgetIntegrationsBatchCorrections> lst, string sortKey, string sortOrder)
        {
            switch (sortKey)
            {
                case "errorType":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.error).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.error).ToList();
                            break;
                    }
                    break;

                case "dim1Type":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.dim1Type).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.dim1Type).ToList();
                            break;
                    }
                    break;

                case "dim2Type":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.dim2Type).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.dim2Type).ToList();
                            break;
                    }
                    break;

                case "dim1Id":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.dim1Id).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.dim1Id).ToList();
                            break;
                    }
                    break;

                case "dim2Id":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.dim2Id).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.dim2Id).ToList();
                            break;
                    }
                    break;

                case "changeDimension.key":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.changeDimension).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.changeDimension).ToList();
                            break;
                    }
                    break;

                case "newId.key":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.newId).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.newId).ToList();
                            break;
                    }
                    break;

                case "keepCheck":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.keep).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.keep).ToList();
                            break;
                    }
                    break;
            }

            return lst;
        }

        public JArray GetBatchCorrectionsColumns(string userId)
        {
            return GetBatchCorrectionsColumnsAsync(userId).GetAwaiter().GetResult();
        }

        public async Task<JArray> GetBatchCorrectionsColumnsAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetInteg");

            dynamic columns = new JArray();
            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_error")).Value).LangText, "errorType", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<span title='#:errorType#'>#if(errorType){# #:errorType# #}#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_dim1_type")).Value).LangText, "dim1Type", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<span>#if(dim1Type){# #:dim1Type# #}#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_dim2_type")).Value).LangText, "dim2Type", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<span>#if(dim2Type){# #:dim2Type# #}#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_dim1_id")).Value).LangText, "dim1Id", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<span>#if(dim1Id){# #:dim1Id# #}#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_dim2_id")).Value).LangText, "dim2Id", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<span>#if(dim2Id){# #:dim2Id# #}#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_change_dim")).Value).LangText, "changeDimension.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "#:changeDimension.key#",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_new_id")).Value).LangText, "newId.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<span>#if(newId.key== '' ){#<span class='no-wrap'>" + ((langStringValue.FirstOrDefault(v => v.Key == "budget_integration_select_value")).Value).LangText + "</span>#}else{#<span class='no-wrap' > #=newId.key# </span>#}#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_check")).Value).LangText, "keepCheck", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, "<div class='chkbox-display-plan'><input type =\"checkbox\" name=\"keepCheckbox\" id=\"keepCheck_#:pkId#\" ng-click =\"vm.keepCheckbox(this)\" ng-disabled =\"#:disableEdit == true#\"  class=\"chkbx\" #= keepCheck ? checked='checked' : '' # /></div>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(" ", "search", false, false, "", false, 50,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, null,
                                    null, null, null, false, null, 0, true, false));

            return columns;
        }

        private static List<BudgetIntegrationsBatchBudgetChanges> GetMonthData(List<BudgetIntegrationsBatchBudgetChanges> dataset, List<int> monthData, int i)
        {
            return (from a in dataset
                    where a.period == monthData[i]
                    group a by new { a.accCode, a.deptCode, a.funcCode, a.projCode, a.fd1Code, a.fd2Code, a.fd3Code, a.fd4Code, a.desc, a.adjustCode } into g
                    select new BudgetIntegrationsBatchBudgetChanges
                    {
                        //pkId = g.Key.pk_id,
                        accCode = g.Key.accCode,
                        deptCode = g.Key.deptCode,
                        funcCode = g.Key.funcCode,
                        projCode = g.Key.projCode,
                        fd1Code = g.Key.fd1Code,
                        fd2Code = g.Key.fd2Code,
                        fd3Code = g.Key.fd3Code,
                        fd4Code = g.Key.fd4Code,
                        desc = g.Key.desc,
                        adjustCode = g.Key.adjustCode,
                        jan = (i == 0) ? g.Sum(x => x.amt) : 0,
                        feb = (i == 1) ? g.Sum(x => x.amt) : 0,
                        mar = (i == 2) ? g.Sum(x => x.amt) : 0,
                        apr = (i == 3) ? g.Sum(x => x.amt) : 0,
                        may = (i == 4) ? g.Sum(x => x.amt) : 0,
                        jun = (i == 5) ? g.Sum(x => x.amt) : 0,
                        jul = (i == 6) ? g.Sum(x => x.amt) : 0,
                        aug = (i == 7) ? g.Sum(x => x.amt) : 0,
                        sep = (i == 8) ? g.Sum(x => x.amt) : 0,
                        oct = (i == 9) ? g.Sum(x => x.amt) : 0,
                        nov = (i == 10) ? g.Sum(x => x.amt) : 0,
                        dec = (i == 11) ? g.Sum(x => x.amt) : 0,
                    }).ToList();
        }

        public async Task<JObject> GetBatchBudgetChangesById(string userId, string batchId, int skip, int take,int budgetYear, BudgetIntegrationsBatchBudgetChanges searchParams)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"Entered the Contoller Level - {DateTime.UtcNow}"); 

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);

            sb.AppendLine($"Obtained userDetails and tenantData - {DateTime.UtcNow}");

            var dataset = new List<BudgetIntegrationsBatchBudgetChanges>();
            int yr = budgetYear * 100;
            List<int> monthData = new List<int> { yr + 1, yr + 2, yr + 3, yr + 4, yr + 5, yr + 6, yr + 7, yr + 8, yr + 9, yr + 10, yr + 11, yr + 12 };
            if (tenantData.erp_type.ToLower() == "Agresso".ToString().ToLower())
            {
                List<integ_agrpostback_bud> lstAgresso = await _unitOfWork.BudgetIntegrationRepository.GetBatchBudgetChangesByIdAgresso(userDetails.tenant_id, batchId);

                sb.AppendLine($"Fetched GetBatchBudgetChangesByIdAgresso data - {DateTime.UtcNow}");

                dataset = (from a in lstAgresso
                           group a by new { a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, a.description, a.fk_adjustment_code, a.amount, a.period } into g
                           select new BudgetIntegrationsBatchBudgetChanges
                           {
                               //pkId = g.Key.pk_id,
                               accCode = g.Key.fk_account_code,
                               deptCode = g.Key.department_code,
                               funcCode = g.Key.fk_function_code,
                               projCode = g.Key.fk_project_code,
                               fd1Code = g.Key.free_dim_1,
                               fd2Code = g.Key.free_dim_2,
                               fd3Code = g.Key.free_dim_3,
                               fd4Code = g.Key.free_dim_4,
                               amt = g.Sum(x => x.amount),
                               desc = g.Key.description,
                               adjustCode = g.Key.fk_adjustment_code,
                               period = g.Key.period
                           }).ToList();

                var finalData = GetMonthData(dataset, monthData, 0);
                finalData.AddRange(GetMonthData(dataset, monthData, 1));
                finalData.AddRange(GetMonthData(dataset, monthData, 2));
                finalData.AddRange(GetMonthData(dataset, monthData, 3));
                finalData.AddRange(GetMonthData(dataset, monthData, 4));
                finalData.AddRange(GetMonthData(dataset, monthData, 5));
                finalData.AddRange(GetMonthData(dataset, monthData, 6));
                finalData.AddRange(GetMonthData(dataset, monthData, 7));
                finalData.AddRange(GetMonthData(dataset, monthData, 8));
                finalData.AddRange(GetMonthData(dataset, monthData, 9));
                finalData.AddRange(GetMonthData(dataset, monthData, 10));
                finalData.AddRange(GetMonthData(dataset, monthData, 11));

                var Data = (from a in finalData
                            group a by new { a.accCode, a.deptCode, a.funcCode, a.projCode, a.fd1Code, a.fd2Code, a.fd3Code, a.fd4Code, a.adjustCode, a.desc } into g
                            select new BudgetIntegrationsBatchBudgetChanges
                            {
                                accCode = g.Key.accCode,
                                deptCode = g.Key.deptCode,
                                funcCode = g.Key.funcCode,
                                projCode = g.Key.projCode,
                                fd1Code = g.Key.fd1Code,
                                fd2Code = g.Key.fd2Code,
                                fd3Code = g.Key.fd3Code,
                                fd4Code = g.Key.fd4Code,
                                desc = g.Key.desc,
                                adjustCode = g.Key.adjustCode,
                                amt = g.Sum(x => x.jan) + g.Sum(x => x.feb) + g.Sum(x => x.mar) + g.Sum(x => x.apr) + g.Sum(x => x.may) + g.Sum(x => x.jun) + g.Sum(x => x.jul) + g.Sum(x => x.aug) + g.Sum(x => x.sep) + g.Sum(x => x.oct) + g.Sum(x => x.nov) + g.Sum(x => x.dec),
                                jan = g.Sum(x => x.jan),
                                feb = g.Sum(x => x.feb),
                                mar = g.Sum(x => x.mar),
                                apr = g.Sum(x => x.apr),
                                may = g.Sum(x => x.may),
                                jun = g.Sum(x => x.jun),
                                jul = g.Sum(x => x.jul),
                                aug = g.Sum(x => x.aug),
                                sep = g.Sum(x => x.sep),
                                oct = g.Sum(x => x.oct),
                                nov = g.Sum(x => x.nov),
                                dec = g.Sum(x => x.dec),
                            }).ToList();
                dataset = Data.Distinct().ToList();

                sb.AppendLine($"Grouped data from GetBatchBudgetChangesByIdAgresso data - {DateTime.UtcNow}");
            }
            else
            {
                List<integnew_export_visma_posted_bud> lstVisma = await _unitOfWork.BudgetIntegrationRepository.GetBatchBudgetChangesByIdVisma(userDetails.tenant_id, batchId);

                sb.AppendLine($"Fetched GetBatchBudgetChangesByIdVisma data - {DateTime.UtcNow}");

                dataset = (from a in lstVisma
                           select new BudgetIntegrationsBatchBudgetChanges
                           {
                               pkId = a.pk_id,
                               accCode = a.fk_account_code,
                               deptCode = a.fk_department_code,
                               funcCode = a.fk_function_code,
                               projCode = a.fk_project_code,
                               fd1Code = a.free_dim_1,
                               fd2Code = a.free_dim_2,
                               fd3Code = a.free_dim_3,
                               fd4Code = a.free_dim_4,
                               desc = a.description,
                               adjustCode = a.fk_adjustment_code,
                               amt = (a.jan == null ? 0 : a.jan.Value) +
                                      (a.feb == null ? 0 : a.feb.Value) +
                                      (a.mar == null ? 0 : a.mar.Value) +
                                      (a.apr == null ? 0 : a.apr.Value) +
                                      (a.may == null ? 0 : a.may.Value) +
                                      (a.jun == null ? 0 : a.jun.Value) +
                                      (a.jul == null ? 0 : a.jul.Value) +
                                      (a.aug == null ? 0 : a.aug.Value) +
                                      (a.sep == null ? 0 : a.sep.Value) +
                                      (a.oct == null ? 0 : a.oct.Value) +
                                      (a.nov == null ? 0 : a.nov.Value) +
                                      (a.dec == null ? 0 : a.dec.Value),
                               jan = a.jan == null ? 0 : a.jan.Value,
                               feb = a.feb == null ? 0 : a.feb.Value,
                               mar = a.mar == null ? 0 : a.mar.Value,
                               apr = a.apr == null ? 0 : a.apr.Value,
                               may = a.may == null ? 0 : a.may.Value,
                               jun = a.jun == null ? 0 : a.jun.Value,
                               jul = a.jul == null ? 0 : a.jul.Value,
                               aug = a.aug == null ? 0 : a.aug.Value,
                               sep = a.sep == null ? 0 : a.sep.Value,
                               oct = a.oct == null ? 0 : a.oct.Value,
                               nov = a.nov == null ? 0 : a.nov.Value,
                               dec = a.dec == null ? 0 : a.dec.Value
                           }).ToList();

                sb.AppendLine($"Grouped data from GetBatchBudgetChangesByIdVisma data - {DateTime.UtcNow}");
            }

            sb.AppendLine($"Before searching and sorting - {DateTime.UtcNow}");

            dataset = SearchBatchBudgetChangesById(dataset, searchParams);

            dataset = SortBatchBudgetChangesById(dataset, (searchParams == null || string.IsNullOrEmpty(searchParams.sortKey)) ? "" : searchParams.sortKey,
                                                          (searchParams == null || string.IsNullOrEmpty(searchParams.sortOrder)) ? "" : searchParams.sortOrder);

            sb.AppendLine($"After searching and sorting - {DateTime.UtcNow}");

            int count = dataset.Count;
            if (!(skip == 0 && take == 0))
            {
                dataset = dataset.Skip(skip).Take(take).ToList();
            }
            dynamic arr = new JArray();
            foreach (var row in dataset)
            {
                dynamic rowObj = new JObject();
                rowObj.id = row.pkId;

                dynamic accountData = new JObject();
                accountData.key = row.accCode;
                accountData.value = row.accCode;
                rowObj.accountData = accountData;

                dynamic departmentData = new JObject();
                departmentData.key = row.deptCode;
                departmentData.value = row.deptCode;
                rowObj.departmentData = departmentData;

                dynamic functionData = new JObject();
                functionData.key = row.funcCode;
                functionData.value = row.funcCode;
                rowObj.functionData = functionData;

                dynamic projectData = new JObject();
                projectData.key = row.projCode;
                projectData.value = row.projCode;
                rowObj.projectData = projectData;

                dynamic freeDim1 = new JObject();
                freeDim1.key = row.fd1Code;
                freeDim1.value = row.fd1Code;
                rowObj.freeDim1 = freeDim1;

                dynamic freeDim2 = new JObject();
                freeDim2.key = row.fd2Code;
                freeDim2.value = row.fd2Code;
                rowObj.freeDim2 = freeDim2;

                dynamic freeDim3 = new JObject();
                freeDim3.key = row.fd3Code;
                freeDim3.value = row.fd3Code;
                rowObj.freeDim3 = freeDim3;

                dynamic freeDim4 = new JObject();
                freeDim4.key = row.fd4Code;
                freeDim4.value = row.fd4Code;
                rowObj.freeDim4 = freeDim4;

                rowObj.budget = row.amt;
                rowObj.description = row.desc;
                rowObj.adjustCode = row.adjustCode;
                rowObj.jan = row.jan;
                rowObj.feb = row.feb;
                rowObj.mar = row.mar;
                rowObj.apr = row.apr;
                rowObj.may = row.may;
                rowObj.jun = row.jun;
                rowObj.jul = row.jul;
                rowObj.aug = row.aug;
                rowObj.sep = row.sep;
                rowObj.oct = row.oct;
                rowObj.nov = row.nov;
                rowObj.dec = row.dec;
                arr.Add(rowObj);
            }

            dynamic obj = new JObject();
            obj.Add("data", arr);
            obj.Add("totalRows", count);

            sb.AppendLine($"Returning the data - {DateTime.UtcNow}");

            string path = $"tenantId-{userDetails.tenant_id}/{userDetails.pk_id}/{budgetYear}/GetBatchBudgetChangesById/performancelog_{DateTime.UtcNow}.json";
            await _azureBlobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.YearlyBudget, path, sb.ToString());

            return obj;
        }

        public static List<BudgetIntegrationsBatchBudgetChanges> SearchBatchBudgetChangesById(List<BudgetIntegrationsBatchBudgetChanges> lst, BudgetIntegrationsBatchBudgetChanges searchParams)
        {
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.accCode))
            {
                lst = lst.Where(x => x.accCode.ToLower().Contains(searchParams.accCode.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.deptCode))
            {
                lst = lst.Where(x => x.deptCode.ToLower().Contains(searchParams.deptCode.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.funcCode))
            {
                lst = lst.Where(x => x.funcCode.ToLower().Contains(searchParams.funcCode.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.projCode))
            {
                lst = lst.Where(x => x.projCode.ToLower().Contains(searchParams.projCode.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.fd1Code))
            {
                lst = lst.Where(x => x.fd1Code.ToLower().Contains(searchParams.fd1Code.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.fd2Code))
            {
                lst = lst.Where(x => x.fd2Code.ToLower().Contains(searchParams.fd2Code.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.fd3Code))
            {
                lst = lst.Where(x => x.fd3Code.ToLower().Contains(searchParams.fd3Code.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.fd4Code))
            {
                lst = lst.Where(x => x.fd4Code.ToLower().Contains(searchParams.fd4Code.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.desc))
            {
                lst = lst.Where(x => x.desc.ToLower().Contains(searchParams.desc.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.adjustCode))
            {
                lst = lst.Where(x => x.adjustCode.ToLower().Contains(searchParams.adjustCode.ToLower())).ToList();
            }
            return lst;
        }

        public static List<BudgetIntegrationsBatchBudgetChanges> SortBatchBudgetChangesById(List<BudgetIntegrationsBatchBudgetChanges> lst, string sortKey, string sortOrder)
        {
            switch (sortKey)
            {
                case "accountData.key":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.accCode).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.accCode).ToList();
                            break;
                    }
                    break;

                case "departmentData.key":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.deptCode).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.deptCode).ToList();
                            break;
                    }
                    break;

                case "functionData.key":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.funcCode).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.funcCode).ToList();
                            break;
                    }
                    break;

                case "projectData.key":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.projCode).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.projCode).ToList();
                            break;
                    }
                    break;

                case "budget":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.amt).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.amt).ToList();
                            break;
                    }
                    break;

                case "description":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.desc).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.desc).ToList();
                            break;
                    }
                    break;

                case "jan":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.jan).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.jan).ToList();
                            break;
                    }
                    break;

                case "feb":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.feb).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.feb).ToList();
                            break;
                    }
                    break;

                case "mar":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.mar).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.mar).ToList();
                            break;
                    }
                    break;

                case "apr":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.apr).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.apr).ToList();
                            break;
                    }
                    break;

                case "may":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.may).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.may).ToList();
                            break;
                    }
                    break;

                case "jun":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.jun).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.jun).ToList();
                            break;
                    }
                    break;

                case "jul":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.jul).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.jul).ToList();
                            break;
                    }
                    break;

                case "aug":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.aug).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.aug).ToList();
                            break;
                    }
                    break;

                case "sep":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.sep).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.sep).ToList();
                            break;
                    }
                    break;

                case "oct":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.oct).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.oct).ToList();
                            break;
                    }
                    break;

                case "nov":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.nov).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.nov).ToList();
                            break;
                    }
                    break;

                case "dec":
                    switch (sortOrder)
                    {
                        case "asc":
                            lst = lst.OrderBy(x => x.dec).ToList();
                            break;

                        case "desc":
                            lst = lst.OrderByDescending(x => x.dec).ToList();
                            break;
                    }
                    break;
            }

            return lst;
        }

        public async Task<JObject> GetAccountingDropdownValues(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var accounts = await _unitOfWork.BudgetIntegrationRepository.GetAccounts(userDetails.tenant_id, budgetYear);
            var departments = await _unitOfWork.BudgetIntegrationRepository.GetDepartments(userDetails.tenant_id, budgetYear);
            var functions = await _unitOfWork.BudgetIntegrationRepository.GetFuctions(userDetails.tenant_id, budgetYear);
            var projects = await _unitOfWork.BudgetIntegrationRepository.GetProjects(userDetails.tenant_id, budgetYear);
            var freedim1 = await _unitOfWork.BudgetIntegrationRepository.GetFreeDimValues(userDetails.tenant_id, budgetYear, "free_dim_1");
            var freedim2 = await _unitOfWork.BudgetIntegrationRepository.GetFreeDimValues(userDetails.tenant_id, budgetYear, "free_dim_2");
            var freedim3 = await _unitOfWork.BudgetIntegrationRepository.GetFreeDimValues(userDetails.tenant_id, budgetYear, "free_dim_3");
            var freedim4 = await _unitOfWork.BudgetIntegrationRepository.GetFreeDimValues(userDetails.tenant_id, budgetYear, "free_dim_4");

            dynamic arrAccounts = new JArray();
            foreach (var row in accounts)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.pk_account_code;
                rowObj.value = $"{row.pk_account_code}-{row.description}";
                arrAccounts.Add(rowObj);
            }

            dynamic arrDepartments = new JArray();
            foreach (var row in departments)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.pk_department_code;
                rowObj.value = $"{row.pk_department_code}-{row.department_name}";
                arrDepartments.Add(rowObj);
            }

            dynamic arrFunctions = new JArray();
            foreach (var row in functions)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.pk_Function_code;
                rowObj.value = $"{row.pk_Function_code}-{row.display_name}";
                arrFunctions.Add(rowObj);
            }

            dynamic arrProjects = new JArray();
            foreach (var row in projects)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.pk_project_code;
                rowObj.value = $"{row.pk_project_code}-{row.project_name}";
                arrProjects.Add(rowObj);
            }

            dynamic arrfreedim1 = new JArray();
            foreach (var row in freedim1)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.free_dim_code;
                rowObj.value = $"{row.free_dim_code}-{row.description}";
                arrfreedim1.Add(rowObj);
            }

            dynamic arrfreedim2 = new JArray();
            foreach (var row in freedim2)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.free_dim_code;
                rowObj.value = $"{row.free_dim_code}-{row.description}";
                arrfreedim2.Add(rowObj);
            }

            dynamic arrfreedim3 = new JArray();
            foreach (var row in freedim3)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.free_dim_code;
                rowObj.value = $"{row.free_dim_code}-{row.description}";
                arrfreedim3.Add(rowObj);
            }

            dynamic arrfreedim4 = new JArray();
            foreach (var row in freedim4)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.free_dim_code;
                rowObj.value = $"{row.free_dim_code}-{row.description}";
                arrfreedim4.Add(rowObj);
            }

            dynamic obj = new JObject();
            obj.Add("accounts", arrAccounts);
            obj.Add("departments", arrDepartments);
            obj.Add("functions", arrFunctions);
            obj.Add("projects", arrProjects);
            obj.Add("freedim1", arrfreedim1);
            obj.Add("freedim2", arrfreedim2);
            obj.Add("freedim3", arrfreedim3);
            obj.Add("freedim4", arrfreedim4);

            return obj;
        }

        public JArray GetBatchBudgetChangesColumns(string userId)
        {
            return GetBatchBudgetChangesColumnsAsync(userId).GetAwaiter().GetResult();
        }

        public async Task<JArray> GetBatchBudgetChangesColumnsAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetInteg");
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

            dynamic columns = new JArray();
            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_acccount")).Value).LangText, "accountData.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=accountData.value#'>#=accountData.key#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_dept")).Value).LangText, "departmentData.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=departmentData.value#'>#=departmentData.key#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_function")).Value).LangText, "functionData.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=functionData.value#'>#=functionData.key#</span>",
                                    null, null, null, false, null, 0, true, false));

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_project")).Value).LangText, "projectData.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=projectData.value#'>#=projectData.key#</span>",
                                    null, null, null, false, null, 0, true, false));


            var fridim = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "budget_integration");
            List<freedimDefinition> freeDimColumns = fridim.ToList();

            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                columns.Add(ColumnData("FreeDim1", "freeDim1.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=freeDim1.value#'>#=freeDim1.key#</span>",
                                    null, null, null, false, null, 0, true, false));
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                columns.Add(ColumnData("FreeDim2", "freeDim2.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=freeDim2.value#'>#=freeDim2.key#</span>",
                                    null, null, null, false, null, 0, true, false));
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                columns.Add(ColumnData("FreeDim3", "freeDim3.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=freeDim3.value#'>#=freeDim3.key#</span>",
                                    null, null, null, false, null, 0, true, false));
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                columns.Add(ColumnData("FreeDim4", "freeDim4.key", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#=freeDim4.value#'>#=freeDim4.key#</span>",
                                    null, null, null, false, null, 0, true, false));
            }

            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_Budget")).Value).LangText, "budget", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(budget),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_january_short3text")).Value).LangText, "jan", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(jan),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_february_short3text")).Value).LangText, "feb", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(feb),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_march_short3text")).Value).LangText, "mar", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(mar),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_april_short3text")).Value).LangText, "apr", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(apr),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_may_short3text")).Value).LangText, "may", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(may),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_june_short3text")).Value).LangText, "jun", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(jun),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_july_short3text")).Value).LangText, "jul", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(jul),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_august_short3text")).Value).LangText, "aug", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(aug),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_september_short3text")).Value).LangText, "sep", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(sep),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_october_short3text")).Value).LangText, "oct", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(oct),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_november_short3text")).Value).LangText, "nov", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(nov),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_december_short3text")).Value).LangText, "dec", false, false, "{0:n0}", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span>#{# #:kendo.toString(parseInt(dec),'n0')# #}#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_desc")).Value).LangText, "description", false, false, "", false, 110,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "#let desc=MODULES.stripHtmlTags(description); if(desc.length<100){# #=description# #}else{# #:MODULES.stripHtmlTags(description).substring(0, 100)#...<a id='description_#:accountData.key#' ng-click = 'vm.showDescFullContent(\"#:description#\", \"#:accountData.key#\", \"#:departmentData.key#\", \"#:functionData.key#\")'><span class='hand-pointer'>readMore</span></a> #}#",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(((langStringValue.FirstOrDefault(v => v.Key == "bt_corr_adjust_code")).Value).LangText, "adjustCode", false, false, "", false, 150,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left: none;",
                                    null, "<span title='#adjustCode#'>#=adjustCode#</span>",
                                    null, null, null, false, null, 0, true, false));
            columns.Add(ColumnData(" ", "search", false, false, "", false, 100,
                                    "white-space:nowrap;text-align:left;width:100px;border-left: none;vertical-align:top;",
                                    "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;border-left:none;",
                                    null, null,
                                    null, null, null, false, null, 0, true, false));

            return columns;
        }

        private static JObject ColumnData(string title, string field, bool encoded, bool hidden, string format, bool expandable, int width,
                                   string attributeStyle, string headerAttributeStyle, string footerAttributeStyle, string template,
                                   string footerTemplate, string filterableCell, string headerTemplate, bool locked, string command,
                                   int colGrpSet, bool sortable, bool totalRow)
        {
            dynamic obj = new JObject();
            obj.title = title;
            obj.field = field;
            obj.encoded = encoded;
            obj.hidden = hidden;
            obj.format = format;
            obj.expandable = expandable;
            obj.width = width;

            dynamic attributes = new JObject();
            attributes.style = attributeStyle;
            obj.attributes = attributes;

            dynamic headerAttributes = new JObject();
            headerAttributes.style = headerAttributeStyle;
            obj.headerAttributes = headerAttributes;

            dynamic footerAttributes = new JObject();
            footerAttributes.style = footerAttributeStyle;
            obj.footerAttributes = footerAttributes;

            obj.template = template;

            obj.footerTemplate = footerTemplate;

            dynamic filterable = new JObject();
            filterable.cell = filterableCell;
            obj.filterable = filterable;

            obj.headerTemplate = headerTemplate;

            obj.locked = locked;

            obj.command = command;

            obj.colGrpSet = colGrpSet;

            obj.sortable = sortable;

            obj.totalRow = totalRow;

            return obj;
        }

        public async Task<string> SaveCorrections(string userId, string batchId, List<BudIntegCorrection> lstBudIntegCorrection)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            foreach (var row in lstBudIntegCorrection)
            {
                var batchDetailsById = await _unitOfWork.BudgetIntegrationRepository.GetBatchCorrectionsByIdAsync(userDetails.tenant_id, batchId, row.pkId);
                if (batchDetailsById != null)
                {
                    batchDetailsById.id_new = row.keepCheck ? string.Empty : row.newId;
                    batchDetailsById.keep = row.keepCheck;
                }
            }

            await _unitOfWork.CompleteAsync();

            return "success";
        }

        public async Task<string> ExportBatchToFix(string userId, string batchId, int budgetYear, bool isOriginalBudget)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();

            List<BudgetIntegrationValidationHelper> procBudOriginalControlValues = ValidateChangeDimensionGridValues(userId, budgetYear, batchId);
            List<int> errors = procBudOriginalControlValues.Where(x => x.error_count > 0).Select(x => x.error_count).ToList();
            var flagValue = tenantDBContext.tco_application_flag.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.flag_name.ToLower() == "LOCK_ORIGINAL_BUDGET".ToLower() && x.flag_status == 1);
            bool isLockedBudget = flagValue != null;
            if (isLockedBudget)
            {
                tco_application_flag cannot_unlock_budget_Flag = new tco_application_flag()
                {
                    flag_name = "Cannot_Unlock_Budget_Integ",
                    flag_key_id = budgetYear.ToString(),
                    flag_status = 1,
                    fk_tenant_id = userDetails.tenant_id,
                    budget_year = budgetYear,
                    period = null,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    flag_guid = null,
                    fk_org_version = null
                };
                _unitOfWork.GenericRepo.Add(cannot_unlock_budget_Flag);
                await _unitOfWork.CompleteAsync();
            }
            if ((!errors.Any() && isOriginalBudget) || !isOriginalBudget)
            {
                tco_application_flag application_Flag = new tco_application_flag()
                {
                    flag_name = "Bud_Integ_Send_Batch",
                    flag_key_id = "1",
                    flag_status = 1,
                    fk_tenant_id = userDetails.tenant_id,
                    budget_year = budgetYear,
                    period = null,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    flag_guid = null,
                    fk_org_version = null
                };

                _unitOfWork.GenericRepo.Add(application_Flag);

                await _unitOfWork.CompleteAsync();
                dynamic queueRequestObj = new JObject();
                queueRequestObj.Add("tenantId", userDetails.tenant_id);
                queueRequestObj.Add("budgetYear", budgetYear);
                List<string> tags = new List<string> { $"tenant-{userDetails.tenant_id}", "Framsikt - trigger" };
                queueRequestObj.Add("tags", JArray.FromObject(tags));
                if (isOriginalBudget)
                {
                    if (tenantData.erp_type.ToLower() == "Agresso".ToString().ToLower())
                    {
                        queueRequestObj.Add("IntegTypeId", 133);
                    }
                    else
                    {
                        queueRequestObj.Add("IntegTypeId", 130);
                    }
                }
                else
                {
                    if (tenantData.erp_type.ToLower() == "Agresso".ToString().ToLower())
                    {
                        queueRequestObj.Add("IntegTypeId", 134);
                    }
                    else
                    {
                        queueRequestObj.Add("IntegTypeId", 131);
                    }
                    queueRequestObj.Add("onlyFailedBatches", true);
                    List<string> batchIdList = new List<string> { batchId };
                    queueRequestObj.Add("retryFailedBatches", JArray.FromObject(batchIdList));                    
                }
                string strQueueRequestObj = JsonConvert.SerializeObject(queueRequestObj);
                _backendJob.QueueMessageForIntegration(strQueueRequestObj, 0);                
                return "success";
            }
            else
            {
                return "not success";
            }
        }

        public async Task<bool> isOriginalBudgetLocked(string userId, int budgetYear, string batchId, bool isOriginalBudget)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            bool isLocked = await _unitOfWork.BudgetIntegrationRepository.getLockOriginalBudgetDetails(userDetails.pk_id, userDetails.tenant_id, budgetYear);
            await _unitOfWork.CompleteAsync();
            if (isOriginalBudget)
            {
                bool isControlBatch = true;
                if (batchId != null)
                {
                    isControlBatch = await _unitOfWork.BudgetIntegrationRepository.getControlBatchDetails(userDetails.pk_id, userDetails.tenant_id, budgetYear, batchId, tenantData.erp_type.ToLower());
                }
                return isLocked && isControlBatch;
            }
            else
            {
                return isLocked;
            }
        }

        public bool ControlOriginalBudget(string userId, int budgetYear, string batchId)
        {
            return ControlOriginalBudgetAsync(userId, budgetYear, batchId).GetAwaiter().GetResult();
        }

        public async Task<bool> ControlOriginalBudgetAsync(string userId, int budgetYear, string batchId)
        {
            List<BudgetIntegrationValidationHelper> procBudOriginalControlValues;
            if (batchId == "")
                procBudOriginalControlValues = await ValidateChangeDimensionGridValuesAsync(userId, budgetYear, "");
            else
                procBudOriginalControlValues = await ValidateChangeDimensionGridValuesAsync(userId, budgetYear, batchId);
            List<int> errors = procBudOriginalControlValues.Where(x => x.error_count > 0).Select(x => x.error_count).ToList();
            return errors.Any();
        }

        private List<BudgetIntegrationValidationHelper> ValidateChangeDimensionGridValues(string userId, int budgetYear, string batchId)
        {
            return ValidateChangeDimensionGridValuesAsync(userId, budgetYear, batchId).GetAwaiter().GetResult();
        }

        private async Task<List<BudgetIntegrationValidationHelper>> ValidateChangeDimensionGridValuesAsync(string userId, int budgetYear, string batchId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(7200);
            if (batchId == null)
                batchId = "";
            var spValidate = new procBudOriginalControl
            {
                TenantId = userDetails.tenant_id,
                BudgetYear = budgetYear,
                BatchId = batchId
            };

            List<BudgetIntegrationValidationHelper> LstErrorValues = (await tenantDbContext.Database.ExecuteStoredProcedureAsync<BudgetIntegrationValidationHelper>(spValidate)).ToList();
            return LstErrorValues;
        }

        public string RunProcedureToUpdateTablesInBatch(string userId, string batchId, int budgetYear, bool isOriginalBudget)
        {
            return RunProcedureToUpdateTablesInBatchAsync(userId, batchId, budgetYear, isOriginalBudget).GetAwaiter().GetResult();
        }

        public async Task<string> RunProcedureToUpdateTablesInBatchAsync(string userId, string batchId, int budgetYear, bool isOriginalBudget)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetInteg");

            dynamic queueRequestObj = new JObject();
            queueRequestObj.Add("UserId", userId);
            queueRequestObj.Add("BudgetYear", budgetYear);
            queueRequestObj.Add("BatchId", batchId);
            if (isOriginalBudget)
            {
                queueRequestObj.Add("isOriginalBudget", isOriginalBudget);
            }
            else
            {
                queueRequestObj.Add("isOriginalBudget", isOriginalBudget);
            }
            string strQueueRequestObj = JsonConvert.SerializeObject(queueRequestObj);
            _backendJob.QueueMessage(userDetails, null, QueueName.budgetintegrationsbatchupdate, strQueueRequestObj);

            return ((langStringValue.FirstOrDefault(v => v.Key == "bt_notify_start_proc")).Value).LangText;
        }

        public async Task<JObject> BudgetIntegrationErrors(string userId, int budgetYear, bool isoriginalBudget)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            List<string> lstBatches;
            if (tenantData.erp_type.ToLower() == "Agresso".ToString().ToLower())
            {
                List<integ_agrpostback_batches> lstAgresso = await _unitOfWork.BudgetIntegrationRepository.GetBatchesAgresso(userDetails.tenant_id, budgetYear, isoriginalBudget);
                lstBatches = lstAgresso.Select(x => x.Batchid).ToList();
            }
            else
            {
                List<integnew_export_visma_postbatches> lstVisma = await _unitOfWork.BudgetIntegrationRepository.GetBatchesVisma(userDetails.tenant_id, budgetYear, isoriginalBudget);
                lstBatches = lstVisma.Select(x => x.batch_id).ToList();
            }
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetIntegration");
            var result = await _unitOfWork.BudgetIntegrationRepository.GetBudgetIntegrationErrors(userDetails.tenant_id, lstBatches);
            dynamic dataSet = new JObject();
            dynamic errorList = new JArray();
            var errorMessage = langString.FirstOrDefault(x => x.Key.Equals("budget_integration_error_title")).Value.LangText;
            foreach (var i in result)
            {
                errorList.Add(i.error_name + " - " + i.batch_id + " " + errorMessage);
            }
            dataSet.Add("errors", errorList);
            return dataSet;
        }

        private static string ReturnString(Worksheet ws, int rowNum, int colNum)
        {
            if (ws.Cells[rowNum, colNum].Value == null)
            {
                return string.Empty;
            }
            else
            {
                if (ws.Cells[rowNum, colNum].DisplayStringValue.Trim() == string.Empty)
                    return string.Empty;

                return ws.Cells[rowNum, colNum].DisplayStringValue;
            }
        }

        public async Task<JArray> GetExportsData(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            List<TfpErpExports> lstExportData = new List<TfpErpExports>();
            var dataset = (from a in lstExportData
                           select new
                           {
                               key = string.Empty,
                               value = string.Empty
                           }).ToList();

            dynamic arr = new JArray();
            lstExportData = await _unitOfWork.BudgetIntegrationRepository.GetExportIds(userDetails.tenant_id, budgetYear, tenantData.erp_type.ToLower());
            dataset = (from a in lstExportData
                       select new
                       {
                           key = a.Id.ToString(),
                           value = a.Id.ToString() + " - " + a.Date.ToString()
                       }).Distinct().ToList();

            foreach (var row in dataset)
            {
                dynamic rowObj = new JObject();
                rowObj.key = row.key;
                rowObj.value = row.value;
                arr.Add(rowObj);
            }
            return arr;
        }

        private static Dictionary<string, string> GetColumnNamesForExportIdAgresso(List<freedimDefinition> freeDimColumns)
        {
            var columnNames = new Dictionary<string, string>();
            columnNames.Add("account", "string");
            columnNames.Add("department", "string");
            columnNames.Add("function", "string");
            columnNames.Add("project", "string");

            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                columnNames.Add("freeDim1", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                columnNames.Add("freeDim2", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                columnNames.Add("freeDim3", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                columnNames.Add("freeDim4", "string");
            }
            columnNames.Add("period", "string");
            columnNames.Add("amount", "numeric");
            return columnNames;
        }

        private static Dictionary<string, string> GetColumnNamesForExportIdVisma( List<freedimDefinition> freeDimColumns)
        {
            var columnNames = new Dictionary<string, string>();
            columnNames.Add("account", "string");
            columnNames.Add("department", "string");
            columnNames.Add("function", "string");
            columnNames.Add("project", "string");
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                columnNames.Add("freeDim1", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                columnNames.Add("freeDim2", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                columnNames.Add("freeDim3", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                columnNames.Add("freeDim4", "string");
            }
            columnNames.Add("amount", "numeric");
            columnNames.Add("jan", "numeric");
            columnNames.Add("feb", "numeric");
            columnNames.Add("mar", "numeric");
            columnNames.Add("apr", "numeric");
            columnNames.Add("may", "numeric");
            columnNames.Add("jun", "numeric");
            columnNames.Add("jul", "numeric");
            columnNames.Add("aug", "numeric");
            columnNames.Add("sep", "numeric");
            columnNames.Add("oct", "numeric");
            columnNames.Add("nov", "numeric");
            columnNames.Add("dec", "numeric");
            return columnNames;
        }

        public async Task<ColumnInfo> GetErpExportColumnInfo(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetIntegration");
            Dictionary<string, string> colNames;

            var fridim = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "budget_integration");
            List<freedimDefinition> freeDimColumns = fridim.ToList();
            if (tenantData.erp_type.ToLower() == "Agresso".ToLower())
            {
                colNames = GetColumnNamesForExportIdAgresso(freeDimColumns);
            }
            else
            {
                colNames = GetColumnNamesForExportIdVisma(freeDimColumns);
            }
            string textToPrepend = "budget_integration_";
            ColumnInfo colInfo = new ColumnInfo();
            foreach (var colName in colNames)
            {
                string key = string.Concat(textToPrepend, colName.Key);
                colInfo.Fields.Add(colName.Key);
                colInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                colInfo.DataTypes.Add(colName.Value);
            }
            await _unitOfWork.CompleteAsync();
            return colInfo;
        }

        private static Dictionary<string, string> GetColumnNamesForErrorValues(List<freedimDefinition> freeDimColumns)
        {
            var columnNames = new Dictionary<string, string>();
            columnNames.Add("fk_account_code", "string");
            columnNames.Add("fk_department_code", "string");
            columnNames.Add("fk_function_code", "string");
            columnNames.Add("fk_project_code", "string");
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                columnNames.Add("free_dim_1", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                columnNames.Add("free_dim_2", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                columnNames.Add("free_dim_3", "string");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                columnNames.Add("free_dim_4", "string");
            }
            columnNames.Add("period", "string");
            columnNames.Add("erp_log_amt", "numeric");
            columnNames.Add("customer_control_amt", "numeric");
            columnNames.Add("diff", "numeric");
            return columnNames;
        }

        public async Task<ColumnInfo> GetValidationErrorColumnInfo(string userId, List<freedimDefinition> freeDimColumns)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetIntegration");
            Dictionary<string, string> colNames = GetColumnNamesForErrorValues(freeDimColumns);
            string textToPrepend = "budget_integration_";
            ColumnInfo colInfo = new ColumnInfo();
            foreach (var colName in colNames)
            {
                string key = string.Concat(textToPrepend, colName.Key);
                colInfo.Fields.Add(colName.Key);
                colInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                colInfo.DataTypes.Add(colName.Value);
            }
            await _unitOfWork.CompleteAsync();
            return colInfo;
        }

        private static List<string> GetColNames(string erpType, List<freedimDefinition> freeDimColumns)
        {
            List<string> colNames = new List<string>();
            colNames.Add("fk_account_code");
            colNames.Add("fk_department_code");
            colNames.Add("fk_function_code");
            colNames.Add("fk_project_code");
            if (erpType.ToLower() == "Agresso".ToLower())
            {
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                {
                    colNames.Add("free_dim_1");
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                {
                    colNames.Add("free_dim_2");
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                {
                    colNames.Add("free_dim_3");
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                {
                    colNames.Add("free_dim_4");
                }
                colNames.Add("period");
                colNames.Add("amount");
            }
            else
            {
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                {
                    colNames.Add("free_dim_1");
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                {
                    colNames.Add("free_dim_2");
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                {
                    colNames.Add("free_dim_3");
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                {
                    colNames.Add("free_dim_4");
                }
                colNames.Add("amount");
                colNames.Add("jan");
                colNames.Add("feb");
                colNames.Add("mar");
                colNames.Add("apr");
                colNames.Add("may");
                colNames.Add("jun");
                colNames.Add("jul");
                colNames.Add("aug");
                colNames.Add("sep");
                colNames.Add("oct");
                colNames.Add("nov");
                colNames.Add("dec");
            }

            return colNames;
        }

        public DataTable validateImportedBudgetIntegrationData(int TenantId, int exportId, int budgetYear, int pkId, string userId, List<freedimDefinition> freeDimColumns)
        {
            return validateImportedBudgetIntegrationDataAsync(TenantId, exportId, budgetYear, pkId, userId, freeDimColumns).GetAwaiter().GetResult();
        }

        public async Task<DataTable> validateImportedBudgetIntegrationDataAsync(int TenantId, int exportId, int budgetYear, int pkId, string userId, List<freedimDefinition> freeDimColumns)
        {
            DataTable Data = new DataTable();
            UserData userInfo = await _utility.GetUserDetailsAsync(userId);
            using (SqlConnection _con = await _utility.GetTenantDbConnectionAsync(userInfo.tenant_id))
            using (SqlCommand _cmd = new SqlCommand("proc_bud_integ_compare", _con))
            {
                _cmd.CommandType = CommandType.StoredProcedure;

                _cmd.Parameters.Add(new SqlParameter("@fk_tenant_id", SqlDbType.Int));
                _cmd.Parameters["@fk_tenant_id"].Value = TenantId;
                _cmd.Parameters.Add(new SqlParameter("@export_id", SqlDbType.Int));
                _cmd.Parameters["@export_id"].Value = exportId;
                _cmd.Parameters.Add(new SqlParameter("@budget_year", SqlDbType.Int));
                _cmd.Parameters["@budget_year"].Value = budgetYear;
                _cmd.Parameters.Add(new SqlParameter("@user_id", SqlDbType.Int));
                _cmd.Parameters["@user_id"].Value = pkId;
                SqlDataAdapter _dap = new SqlDataAdapter(_cmd);
                _dap.Fill(Data);
            }
            if (freeDimColumns.Count == 0 || freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") == null)
            {
                Data.Columns.Remove("free_dim_1");
            }
            if (freeDimColumns.Count == 0 || freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") == null)
            {
                Data.Columns.Remove("free_dim_2");
            }
            if (freeDimColumns.Count == 0 || freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") == null)
            {
                Data.Columns.Remove("free_dim_3");
            }
            if (freeDimColumns.Count == 0 || freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") == null)
            {
                Data.Columns.Remove("free_dim_4");
            }
            return Data;
        }

        public void ImportExcelToStaging(string userId,int budgetYear,int exportId, Workbook wb, long jobId)
        {
            ImportExcelToStagingAsync(userId, budgetYear, exportId, wb, jobId).GetAwaiter().GetResult();
        }

        public async Task ImportExcelToStagingAsync(string userId, int budgetYear, int exportId, Workbook wb, long jobId)
        {
            StringBuilder sb = new StringBuilder();
            Worksheet ws = wb.Worksheets[0];
            //Reset the workflow state by deleting any completed jobs
            await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.BudgetIntegration);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(500);
            CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            wb.Settings.CultureInfo = ci;
            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetIntegration");

            sb.AppendLine("excecutng line 1818");
            await tenantDbContext.BulkDeleteAsync(tenantDbContext.tbu_stage_budget_integrations.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear));
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            var erpType = tenantData.erp_type;
            var fridim = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "budget_integration");
            int fridimCount = fridim.Count;
            List<freedimDefinition> freeDimColumns = fridim.ToList();
            List<string> colNames = GetColNames(erpType, freeDimColumns);
            int columCount = ws.Cells.MaxColumn;
            if (columCount != colNames.Count - 1)// check if the number of colum excel is matching with columns defined
            {
                throw new InvalidDataException(langStringValue.FirstOrDefault(v => v.Key == "budget_integration_incorrect_columns").Value.LangText);
            }
            int i;
            for (i = 0; i < colNames.Count; i++)
            {
                ws.Cells[0, i].Value = colNames[i];
            }
            List<string> invalid_data_columns = new List<string>();
            i = 0;
            int originalRowCount = ws.Cells.MaxDataRow;
            int periodColNo = columCount - 12;
            int k = originalRowCount, month = 1;
            int rowsToImport = ws.Cells.MaxDataRow;
            var table = ws.Cells.ExportDataTableAsString(0, 0, rowsToImport + 1, ws.Cells.MaxDataColumn + 1, true);
            table.AcceptChanges();
            table.Columns.Add("pkId", typeof(System.Int32));
            int j = 1;
            foreach (DataRow row in table.Rows)
            {
                row["pkId"] = j++;
            }
            int period;
            decimal amount;

            string langStringRow = langStringValue.FirstOrDefault(v => v.Key == "budget_integration_row").Value.LangText;
            string langStringColumn = langStringValue.FirstOrDefault(v => v.Key == "budget_integration_column").Value.LangText;
            var accNullValues = table.AsEnumerable().Where(x => x.Field<string>("fk_account_code") == null || x.Field<string>("fk_account_code").Trim() == string.Empty).Select(r => r.Field<int>("pkId")).ToList();
            var deptNullValues = table.AsEnumerable().Where(x => x.Field<string>("fk_department_code") == null || x.Field<string>("fk_department_code").Trim() == string.Empty).Select(r => r.Field<int>("pkId")).ToList();
            var funcNullValues = table.AsEnumerable().Where(x => x.Field<string>("fk_function_code") == null || x.Field<string>("fk_function_code").Trim() == string.Empty).Select(r => r.Field<int>("pkId")).ToList();
            var amountNotDecimalValues = table.AsEnumerable().Where(x => x.Field<string>("amount") != null && x.Field<string>("amount").Trim() != string.Empty && !decimal.TryParse(x.Field<string>("amount"), out amount)).Select(r => r.Field<int>("pkId")).ToList();

            var monthsList = new[] { "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec" };

            accNullValues.ForEach(l =>
            {
                invalid_data_columns.Add($"{ReturnString(ws, 0, 0)} {langStringRow} {l} {langStringColumn} {(1)} {langStringValue.FirstOrDefault(v => v.Key == "budget_integration_not_null").Value.LangText}");
            });
            deptNullValues.ForEach(l =>
            {
                invalid_data_columns.Add($"{ReturnString(ws, 0, 1)} {langStringRow} {l} {langStringColumn} {(2)} {langStringValue.FirstOrDefault(v => v.Key == "budget_integration_not_null").Value.LangText}");
            });
            funcNullValues.ForEach(l =>
            {
                invalid_data_columns.Add($"{ReturnString(ws, 0, 2)} {langStringRow} {l} {langStringColumn} {(3)} {langStringValue.FirstOrDefault(v => v.Key == "budget_integration_not_null").Value.LangText}");
            });

            int colNo = 4 + fridimCount;
            if (erpType.ToLower() == "Agresso".ToLower())
            {
                colNo++;
            }
            var amountColName = ReturnString(ws, 0, colNo);
            amountNotDecimalValues.ForEach(l =>
            {
                invalid_data_columns.Add($"{langStringValue.FirstOrDefault(v => v.Key == "budget_integration_ValidDecimal").Value.LangText} {amountColName} {langStringRow} {l} {langStringColumn} {(colNo + 1)}");
            });
            if (erpType.ToLower() == "Agresso".ToLower())
            {
                int periodColIndex = 5 + fridimCount;
                var periodColName = ReturnString(ws, 0, periodColIndex - 1);
                var NotperiodFormatValues = table.AsEnumerable().Where(x => (int.TryParse(x.Field<string>("period"), out period) &&
                                                                                        x.Field<string>("period").Length == 6 &&
                                                                                        int.Parse(x.Field<string>("period").Substring(0, 4)) >= 1900 &&
                                                                                        int.Parse(x.Field<string>("period").Substring(0, 4)) <= 2099 &&
                                                                                        int.Parse(x.Field<string>("period").Substring(4, 2)) >= 1 &&
                                                                                        int.Parse(x.Field<string>("period").Substring(4, 2)) <= 12)).Select(r => r.Field<int>("pkId")).ToList();

                NotperiodFormatValues.ForEach(l =>
                {
                    invalid_data_columns.Add($"{langStringValue.FirstOrDefault(v => v.Key == "budget_integration_ValidPeriod").Value.LangText} {periodColName} {langStringRow} {l} {langStringColumn} {(periodColIndex)}");
                });
            }
            else
            {
                int monthColNo = 6 + fridimCount;
                for (int q = 0; q < 12; q++)
                {
                    var monthAmountNotDecimalValues = table.AsEnumerable().Where(x => x.Field<string>(monthsList[q]) != null && x.Field<string>(monthsList[q]).Trim() != string.Empty && !decimal.TryParse(x.Field<string>(monthsList[q]), out amount)).Select(r => r.Field<int>("pkId")).ToList();
                    monthAmountNotDecimalValues.ForEach(l =>
                    {
                        invalid_data_columns.Add($"{langStringValue.FirstOrDefault(v => v.Key == "budget_integration_ValidDecimal").Value.LangText} {monthsList[q]} {langStringRow} {l} {langStringColumn} {(monthColNo + q)}");
                    });
                }
            }

            sb.AppendLine("excecutng line 1930");

            UserData userInfo = await _utility.GetUserDetailsAsync(userId);

            if (invalid_data_columns.Count != 0)
            {
                throw new InvalidDataException(string.Join("<br/>", invalid_data_columns));
            }
            if (erpType.ToLower() == "Visma".ToLower())
            {
                ws.Cells.InsertColumns(columCount - 12, 1);
                colNames.Insert(columCount - 12, "period");
                for (int p = 0; p < colNames.Count; p++)
                {
                    ws.Cells[i, p].Value = ReturnString(ws, i, p);
                    if (colNames[p] == "period")
                        ws.Cells[i, p].Value = "period";
                }
                int c = colNames.IndexOf("jan");
                for (i = 1; i < originalRowCount + 1; i++)
                {
                    ws.Cells.InsertRows(ws.Cells.MaxDataRow + 1, 12);
                    for (k = originalRowCount + 12 * (i - 1) + 1, month = 1; k < originalRowCount + (12 * i) + 1; k++, month++)
                    {
                        for (int col = 0; col < colNames.Count - 12; col++)
                        {
                            ws.Cells[k, col].Value = ReturnString(ws, i, col);
                        }
                        ws.Cells[k, periodColNo].Value = ((budgetYear * 100) + month).ToString();
                        ws.Cells[k, periodColNo + 1].Value = (ReturnString(ws, i, c + month - 1) == string.Empty) ? 0.ToString() : ReturnString(ws, i, c + month - 1);
                    }
                }
                ws.Cells.DeleteRows(1, originalRowCount);
                ws.Cells.DeleteColumns(colNames.Count - 12, 12, true);
            }
            else
            {
                for (i = 1; i < originalRowCount + 1; i++)
                {
                    ws.Cells[i, colNo].Value = (ReturnString(ws, i, colNo) == string.Empty) ? 0.ToString() : ReturnString(ws, i, colNo);
                }
            }
            if (erpType.ToLower() == "Visma".ToLower())
            {
                colNames.RemoveAll(t => monthsList.Contains(t));
            }
            rowsToImport = ws.Cells.MaxDataRow;
            table = ws.Cells.ExportDataTableAsString(0, 0, rowsToImport + 1, ws.Cells.MaxDataColumn + 1, true);
            table.AcceptChanges();
            try
            {
                List<DataTable> chunks = SplitTable(table, 30000);

                foreach (var chunk in chunks)
                {
                    List<DataTable> splitTables = SplitTable(chunk, 10000);
                    await Parallel.ForEachAsync(splitTables, async (tbl, cancellationToken) =>
                    {
                        DataSet result = new DataSet();
                        result.Tables.Add(tbl);

                        DataColumn tenantIdCol = new DataColumn("TenantId")
                        {
                            DataType = typeof(int),
                            DefaultValue = userInfo.tenant_id
                        };
                        tbl.Columns.Add(tenantIdCol);

                        DataColumn budgetYearCol = new DataColumn("budget_year")
                        {
                            DataType = typeof(int),
                            DefaultValue = budgetYear
                        };
                        tbl.Columns.Add(budgetYearCol);

                        DataColumn userIdCol = new DataColumn("UserId")
                        {
                            DataType = typeof(int),
                            DefaultValue = userInfo.pk_id
                        };
                        tbl.Columns.Add(userIdCol);

                        DataColumn updated = new DataColumn("updated")
                        {
                            DataType = typeof(DateTime),
                            DefaultValue = DateTime.UtcNow
                        };
                        tbl.Columns.Add(updated);

                        DataColumn updated_by = new DataColumn("updated_by")
                        {
                            DataType = typeof(int),
                            DefaultValue = userInfo.pk_id
                        };
                        tbl.Columns.Add(updated_by);

                        using (SqlBulkCopy bulkCopy = new SqlBulkCopy(await _utility.GetTenantDbConnectionAsync(userDetails.tenant_id)) { DestinationTableName = "dbo.tbu_stage_budget_integrations" })
                        {
                            foreach (string colName in colNames)
                            {
                                bulkCopy.ColumnMappings.Add(colName, columnMapping[colName]);
                            }

                            bulkCopy.ColumnMappings.Add("TenantId", columnMapping["TenantId"]);
                            bulkCopy.ColumnMappings.Add("UserId", columnMapping["UserId"]);
                            bulkCopy.ColumnMappings.Add("budget_year", columnMapping["budget_year"]);
                            bulkCopy.ColumnMappings.Add("updated", columnMapping["updated"]);
                            bulkCopy.ColumnMappings.Add("updated_by", columnMapping["updated_by"]);

                            await bulkCopy.WriteToServerAsync(tbl);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(ex.Message);
                await _staffPlanning.InsertErrorLogAsync(userId, budgetYear, sb.ToString(), "BudgIntegrationImportException");
            }
        }

        private static List<DataTable> SplitTable(DataTable originalTable, int batchSize)
        {
            List<DataTable> tables = new List<DataTable>();
            int i = 0;
            int j = 1;
            DataTable newDt = originalTable.Clone();
            newDt.TableName = "Table_" + j;
            newDt.Clear();
            foreach (DataRow row in originalTable.Rows)
            {
                DataRow newRow = newDt.NewRow();
                newRow.ItemArray = row.ItemArray;
                newDt.Rows.Add(newRow);
                i++;
                if (i == batchSize)
                {
                    tables.Add(newDt);
                    j++;
                    newDt = originalTable.Clone();
                    newDt.TableName = "Table_" + j;
                    newDt.Clear();
                    i = 0;
                }
            }

            //The last table is likely to be less than batch size, Add the final table
            tables.Add(newDt);
            return tables;
        }
        public async Task<BudgetIntegrationValidationErrorData> GetValidationForBatchExport(string userId, string batchId, int budgetYear, int TenantId)
        {
            DataSet Data = new DataSet();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetIntegration");
            BudgetIntegrationValidationErrorData dataSet = new BudgetIntegrationValidationErrorData();           
            using (SqlConnection _con = await _utility.GetTenantDbConnectionAsync(userDetails.tenant_id))
            using (SqlCommand _cmd = new SqlCommand("proc_bud_original_control_V2", _con))
            {
                _cmd.CommandType = CommandType.StoredProcedure;
                _cmd.Parameters.Add(new SqlParameter("@tenant_id", SqlDbType.Int));
                _cmd.Parameters["@tenant_id"].Value = TenantId;
                _cmd.Parameters.Add(new SqlParameter("@batch_id", SqlDbType.NVarChar));
                _cmd.Parameters["@batch_id"].Value = batchId;
                _cmd.Parameters.Add(new SqlParameter("@budget_year", SqlDbType.Int));
                _cmd.Parameters["@budget_year"].Value = budgetYear;
                SqlDataAdapter _dap = new SqlDataAdapter(_cmd);
                _dap.Fill(Data);                
            }
            

            if (Data.Tables.Count >= 2)
            {
                DataTable ErrorData = Data.Tables[1];
                dataSet.errorTypeList = (from d in ErrorData.AsEnumerable() select d["error_type"].ToString()).ToList();

                if (dataSet.errorTypeList != null)
                {
                    if (dataSet.errorTypeList.Any(x => x == "DIM_TEMPORARY_CONTROL_SP") && dataSet.errorTypeList.Any(x => x == "BUDGET_UNBALANCED_CONTROL_SP"))
                    {
                        dataSet.mainProjectList = (from d in ErrorData.AsEnumerable()
                                                   where d["error_type"].ToString() == "DIM_TEMPORARY_CONTROL_SP"
                                                   select d["dim_1_id"].ToString()).ToList();
                        dataSet.errorType1 = "DIM_TEMPORARY_CONTROL_SP";
                        dataSet.errorType2 = "BUDGET_UNBALANCED_CONTROL_SP";
                        dataSet.errorMessage1 = langStringValue.FirstOrDefault(v => v.Key == "budget_integration_validation_mp").Value.LangText;
                        dataSet.errorMessage2 = langStringValue.FirstOrDefault(v => v.Key == "budget_integration_validation_sum").Value.LangText;

                    }
                    else if (dataSet.errorTypeList.Any(x => x == "DIM_TEMPORARY_CONTROL_SP"))
                    {

                        dataSet.mainProjectList = (from d in ErrorData.AsEnumerable()
                                                   where d["error_type"].ToString() == "DIM_TEMPORARY_CONTROL_SP"
                                                   select d["dim_1_id"].ToString()).ToList();
                        dataSet.errorType1 = "DIM_TEMPORARY_CONTROL_SP";
                        dataSet.errorMessage1 = langStringValue.FirstOrDefault(v => v.Key == "budget_integration_validation_mp").Value.LangText;

                    }
                    else if (dataSet.errorTypeList.Any(x => x == "BUDGET_UNBALANCED_CONTROL_SP"))
                    {

                        dataSet.errorType2 = "BUDGET_UNBALANCED_CONTROL_SP";
                        dataSet.errorMessage2 = langStringValue.FirstOrDefault(v => v.Key == "budget_integration_validation_sum").Value.LangText;
                    }
                }                          
            }
            return dataSet;
        }
    }
}