#pragma warning disable CS8629
#pragma warning disable CS8601
#pragma warning disable CS8604 
#pragma warning disable CS8600
#pragma warning disable CS8602

using Aspose.Cells;
using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;

namespace Framsikt.BL
{
    public class OrgStructureImport : IOrgStructureImport
    {
        private readonly IUtility _utility;
        private readonly IBackendRequest _backendJob;
        private readonly DateTime defaultDateTime = new DateTime(1900, 1, 1, 0, 0, 0);
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAdminOrgStructure _adminOrgStructure;

        private readonly Dictionary<string, string> columnMapping = new Dictionary<string, string>()
        {
            { "TenantId", "fk_tenant_id" },
            { "budget_year", "budget_year" },
            { "UserId", "user_id" },
            { "fk_org_version", "fk_org_version" },
            { "org_id_1", "org_id_1" },
            { "org_name_1", "org_name_1" },
            { "org_id_2", "org_id_2" },
            { "org_name_2", "org_name_2" },
            { "org_id_3", "org_id_3" },
            { "org_name_3", "org_name_3" },
            { "org_id_4", "org_id_4" },
            { "org_name_4", "org_name_4" },
            { "org_id_5", "org_id_5" },
            { "org_name_5", "org_name_5" },
            { "org_id_6", "org_id_6" },
            { "org_name_6", "org_name_6" },
            { "org_id_7", "org_id_7" },
            { "org_name_7", "org_name_7" },
            { "org_id_8", "org_id_8" },
            { "org_name_8", "org_name_8" },
            { "fk_department_code", "fk_department_code" },
            { "updated", "updated" },
            { "updated_by", "updated_by" },
            { "job_id", "job_Id" }
        };

        private List<string> GetColNames()
        {
            List<string> colNames = new List<string>();

            colNames.Add("fk_org_version");
            colNames.Add("org_id_1");
            colNames.Add("org_name_1");

            colNames.Add("org_id_2");
            colNames.Add("org_name_2");

            colNames.Add("org_id_3");
            colNames.Add("org_name_3");

            colNames.Add("org_id_4");
            colNames.Add("org_name_4");

            colNames.Add("org_id_5");
            colNames.Add("org_name_5");

            colNames.Add("org_id_6");
            colNames.Add("org_name_6");

            colNames.Add("org_id_7");
            colNames.Add("org_name_7");

            colNames.Add("org_id_8");
            colNames.Add("org_name_8");
            colNames.Add("fk_department_code");
            return colNames;
        }

        public async Task<ColumnInfo> GetOrgStructureColumnInfo(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "OrgStructureImport");
            Dictionary<string, string> colNames = GetColumnNamesForOrgImport();
            string textToPrepend = "OrgStructureImp_";
            ColumnInfo colInfo = new ColumnInfo();
            foreach (var colName in colNames)
            {
                string key = string.Concat(textToPrepend, colName.Key);
                colInfo.Fields.Add(colName.Key);
                colInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                colInfo.DataTypes.Add(colName.Value);
            }
            return colInfo;
        }

        private string ReturnString(Worksheet ws, object o, int rowNum, int colNum)
        {
            if (ws.Cells[rowNum, colNum].Value == null)
            {
                return string.Empty;
            }
            else
            {
                if (ws.Cells[rowNum, colNum].DisplayStringValue.Trim() == string.Empty)
                    return string.Empty;

                return ws.Cells[rowNum, colNum].DisplayStringValue;
            }
        }

        private Dictionary<string, string> GetColumnNamesForOrgImport()
        {
            var columnNames = new Dictionary<string, string>();
            columnNames.Add("OrgVersion", "string");
            columnNames.Add("OrgId_1", "string");
            columnNames.Add("OrgName_1", "string");
            columnNames.Add("OrgId_2", "string");
            columnNames.Add("OrgName_2", "string");
            columnNames.Add("OrgId_3", "string");
            columnNames.Add("OrgName_3", "string");
            columnNames.Add("OrgId_4", "string");
            columnNames.Add("OrgName_4", "string");
            columnNames.Add("OrgId_5", "string");
            columnNames.Add("OrgName_5", "string");
            columnNames.Add("OrgId_6", "string");
            columnNames.Add("OrgName_6", "string");
            columnNames.Add("OrgId_7", "string");
            columnNames.Add("OrgName_7", "string");
            columnNames.Add("OrgId_8", "string");
            columnNames.Add("OrgName_8", "string");
            columnNames.Add("DepartmentCode", "string");
            return columnNames;
        }

        public OrgStructureImport(IUnitOfWork uow, IUtility util, IBackendRequest bejob, IAdminOrgStructure adminOrgStructure)
        {
            _utility = util;
            _backendJob = bejob;
            _unitOfWork = uow;
            _adminOrgStructure = adminOrgStructure;
        }

        public async Task WriteToOrgStructureImportQueue(string userId, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            dynamic orgStructureImportRequest = new JObject();

            List<TcoJobStatus> jobs;
            long jobIdToconsider = 0;
            bool isRequestByJobId = false;

            if (jobId == -1)// if from export create new job status
            {
                jobs = await _unitOfWork.OrgStructureImportRepository.GetJobStatusBasedOnJobType(userDetails.pk_id, userDetails.tenant_id, UserTrackedJobs.OrgStructureImport.ToString());

                if (jobs.Count > 0)
                {
                    //clean up any existing rows. There can be only one job at a time
                    await _unitOfWork.OrgStructureImportRepository.RemoveJobs(jobs);
                }

                TcoJobStatus jobStatus = new TcoJobStatus
                {
                    JobType = UserTrackedJobs.OrgStructureImport.ToString(),
                    StartTime = DateTime.UtcNow,
                    TotalSteps = 100, //setting this to a high default value
                    StepsCompleted = 0,
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id
                };

                await _unitOfWork.OrgStructureImportRepository.AddJob(jobStatus);
                jobIdToconsider = jobStatus.PkId;
                isRequestByJobId = false;
            }
            else
            {
                jobIdToconsider = jobId;
                isRequestByJobId = true;

                var jobStatToUpdate = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobIdToconsider && x.TenantId == userDetails.tenant_id);

                if (jobStatToUpdate != null)
                {
                    jobStatToUpdate.StartTime = DateTime.UtcNow;
                    jobStatToUpdate.TotalSteps = 100;
                    jobStatToUpdate.StepsCompleted = 0;
                    jobStatToUpdate.jobStatus = StatusEnumData.InProgress.ToString();

                    tenantDbContext.Entry(jobStatToUpdate).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }
            }

            orgStructureImportRequest.Add("UserId", userId);
            orgStructureImportRequest.Add("TenantId", userDetails.tenant_id);
            orgStructureImportRequest.Add("JobId", jobIdToconsider);
            orgStructureImportRequest.Add("isRequestByJobId", isRequestByJobId);

            string strorgStructureImportRequest = JsonConvert.SerializeObject(orgStructureImportRequest);
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>() { };
            _backendJob.QueueMessage(userDetails, modBudgetYears, QueueName.orgstructureimportqueue, strorgStructureImportRequest);
        }

        public async Task ImportExcelToStaging(string userId, Workbook wb, long jobId)
        {
            Worksheet ws = wb.Worksheets[0];

            //Reset the workflow state by deleting any completed jobs
            await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.OrgStructureImport);

            ExportTableOptions opt = new ExportTableOptions
            {
                ExportColumnName = true,
                FormatStrategy = CellValueFormatStrategy.CellStyle,
                ExportAsString = false
            };

            List<string> colNames = GetColNames();
            for (int i = 0; i < colNames.Count; i++)
            {
                ws.Cells[0, i].Value = colNames[i];
            }

            int orgVersionColNo = colNames.IndexOf("fk_org_version");
            int orgId_1ColNo = colNames.IndexOf("org_id_1");
            int orgName_1ColNo = colNames.IndexOf("org_name_1");

            int orgId_2ColNo = colNames.IndexOf("org_id_2");
            int orgName_2ColNo = colNames.IndexOf("org_name_2");

            int orgId_3ColNo = colNames.IndexOf("org_id_3");
            int orgName_3ColNo = colNames.IndexOf("org_name_3");

            int orgId_4ColNo = colNames.IndexOf("org_id_4");
            int orgName_4ColNo = colNames.IndexOf("org_name_4");

            int orgId_5ColNo = colNames.IndexOf("org_id_5");
            int orgName_5ColNo = colNames.IndexOf("org_name_5");

            int orgId_6ColNo = colNames.IndexOf("org_id_6");
            int orgName_6ColNo = colNames.IndexOf("org_name_6");

            int orgId_7ColNo = colNames.IndexOf("org_id_7");
            int orgName_7ColNo = colNames.IndexOf("org_name_7");

            int orgId_8ColNo = colNames.IndexOf("org_id_8");
            int orgName_8ColNo = colNames.IndexOf("org_name_8");
            int fk_department_code = colNames.IndexOf("fk_department_code");

            for (int i = 0; i < ws.Cells.MaxDataRow + 1; i++)
            {
                ws.Cells[i, orgVersionColNo].Value = ReturnString(ws, ws.Cells[i, orgVersionColNo].Value, i, orgVersionColNo);
                ws.Cells[i, orgId_1ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_1ColNo].Value, i, orgId_1ColNo);
                ws.Cells[i, orgName_1ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_1ColNo].Value, i, orgName_1ColNo);
                ws.Cells[i, orgId_2ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_2ColNo].Value, i, orgId_2ColNo);
                ws.Cells[i, orgName_2ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_2ColNo].Value, i, orgName_2ColNo);
                ws.Cells[i, orgId_3ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_3ColNo].Value, i, orgId_3ColNo);
                ws.Cells[i, orgName_3ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_3ColNo].Value, i, orgName_3ColNo);
                ws.Cells[i, orgId_4ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_4ColNo].Value, i, orgId_4ColNo);
                ws.Cells[i, orgName_4ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_4ColNo].Value, i, orgName_4ColNo);
                ws.Cells[i, orgId_5ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_5ColNo].Value, i, orgId_5ColNo);
                ws.Cells[i, orgName_5ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_5ColNo].Value, i, orgName_5ColNo);
                ws.Cells[i, orgId_6ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_6ColNo].Value, i, orgId_6ColNo);
                ws.Cells[i, orgName_6ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_6ColNo].Value, i, orgName_6ColNo);
                ws.Cells[i, orgId_7ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_7ColNo].Value, i, orgId_7ColNo);
                ws.Cells[i, orgName_7ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_7ColNo].Value, i, orgName_7ColNo);
                ws.Cells[i, orgId_8ColNo].Value = ReturnString(ws, ws.Cells[i, orgId_8ColNo].Value, i, orgId_8ColNo);
                ws.Cells[i, orgName_8ColNo].Value = ReturnString(ws, ws.Cells[i, orgName_8ColNo].Value, i, orgName_8ColNo);
                ws.Cells[i, fk_department_code].Value = ReturnString(ws, ws.Cells[i, fk_department_code].Value, i, fk_department_code);
            }

            int rowsToImport = ws.Cells.MaxDataRow;
            var table = ws.Cells.ExportDataTable(0, 0, rowsToImport + 1, ws.Cells.MaxDataColumn + 1, opt);
            table.AcceptChanges();
            UserData userInfo = await _utility.GetUserDetailsAsync(userId);

            //Format the data set for import
            DataColumn tenantIdCol = new DataColumn("TenantId")
            {
                DataType = typeof(int),
                DefaultValue = userInfo.tenant_id
            };
            table.Columns.Add(tenantIdCol);

            DataColumn userIdCol = new DataColumn("UserId")
            {
                DataType = typeof(int),
                DefaultValue = userInfo.pk_id
            };
            table.Columns.Add(userIdCol);

            DataColumn updated = new DataColumn("updated")
            {
                DataType = typeof(DateTime),
                DefaultValue = DateTime.UtcNow
            };
            table.Columns.Add(updated);

            DataColumn updated_by = new DataColumn("updated_by")
            {
                DataType = typeof(int),
                DefaultValue = userInfo.pk_id
            };
            table.Columns.Add(updated_by);

            DataColumn job_id = new DataColumn("job_id")
            {
                DataType = typeof(int),
                DefaultValue = -1
            };
            table.Columns.Add(job_id);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            using (SqlBulkCopy bulkCopy = new SqlBulkCopy(await _utility.GetTenantDbConnectionAsync(userDetails.tenant_id)) { DestinationTableName = "dbo.tbu_stage_org_structure_import" })
            {
                foreach (string colName in colNames)
                {
                    bulkCopy.ColumnMappings.Add(colName, columnMapping[colName]);
                }

                bulkCopy.ColumnMappings.Add("TenantId", columnMapping["TenantId"]);
                bulkCopy.ColumnMappings.Add("UserId", columnMapping["UserId"]);
                bulkCopy.ColumnMappings.Add("updated", columnMapping["updated"]);
                bulkCopy.ColumnMappings.Add("updated_by", columnMapping["updated_by"]);
                bulkCopy.ColumnMappings.Add("job_id", columnMapping["job_id"]);

                await bulkCopy.WriteToServerAsync(table);
            }

            await ValidateImportedOrgStructureData(userId, jobId);
        }

        public async Task<OrgImportHelper> OrgStructureImportGridData(string userId, int skip, int take, long jobId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            //jobId = jobId == -1 ? 0 : jobId;
            OrgImportHelper result = await _unitOfWork.OrgStructureImportRepository.GetStageGridData(userDetails.pk_id,userDetails.tenant_id, skip, take, jobId);
            //result.data = result.data.OrderByDescending(x => x.error_count).ToList();//124433
            return result;
        }

        public async Task<ImportInfo> GetOrgStructureImportInfo(string userId, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            int failedCount = 0, totalRows = 0;
            failedCount = (int)(await _unitOfWork.OrgStructureImportRepository.GetOrgImportStagedData(userData.pk_id, userData.tenant_id, true, jobId)).Sum(y => (int?)y.error_count);
            totalRows = (await _unitOfWork.OrgStructureImportRepository.GetOrgImportStagedData(userData.pk_id, userData.tenant_id, false, jobId)).Count;
            ImportInfo info = new ImportInfo
            {
                FailedCount = failedCount,
                TotalRows = totalRows
            };
            return info;
        }

        public async Task UpdateImportedOrgStructureData(string userId, IEnumerable<tbu_stage_org_structure_import> data, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            List<tbu_stage_org_structure_import> orgStructureDataSet;
            orgStructureDataSet = await _unitOfWork.OrgStructureImportRepository.GetOrgStageSavedData(userData.pk_id, userData.tenant_id, jobId);
            foreach (var orgInfo in data)
            {
                tbu_stage_org_structure_import dborgStructure = orgStructureDataSet.FirstOrDefault(x => x.pk_id == orgInfo.pk_id);
                if (dborgStructure != null)
                {
                    orgInfo.updated = DateTime.UtcNow;
                    orgInfo.job_Id = jobId;
                    tenantDbContext.Entry(dborgStructure).CurrentValues.SetValues(orgInfo);
                }
            }
            await tenantDbContext.SaveChangesAsync();
            await ValidateImportedOrgStructureData(userId, jobId);
        }

        public async Task DeleteStaged(string userId, long jobId)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            List<tbu_stage_org_structure_import> orgStructureDataSet = await _unitOfWork.OrgStructureImportRepository.GetOrgImportStagedData(userData.pk_id, userData.tenant_id, false, jobId);
            await _unitOfWork.OrgStructureImportRepository.DeleteOrgStageTable(orgStructureDataSet);
            await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.OrgStructureImport);
        }

        public async Task<ColumnInfo> GetColumnInfo(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "OrgStructureImport");

            List<string> colNames = GetColNames();
            string mandatorySuffix = "*";
            ColumnInfo columnInfo = new ColumnInfo();

            foreach (string colName in colNames)
            {
                switch (colName)
                {
                    case "fk_org_version":
                        columnInfo.Fields.Add("fk_org_version");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgVersion").Value.LangText + mandatorySuffix);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_1":
                        columnInfo.Fields.Add("org_id_1");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_1").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_1":
                        columnInfo.Fields.Add("org_name_1");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_1").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_2":
                        columnInfo.Fields.Add("org_id_2");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_2").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_2":
                        columnInfo.Fields.Add("org_name_2");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_2").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_3":
                        columnInfo.Fields.Add("org_id_3");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_3").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_3":
                        columnInfo.Fields.Add("org_name_3");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_3").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_4":
                        columnInfo.Fields.Add("org_id_4");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_4").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_4":
                        columnInfo.Fields.Add("org_name_4");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_4").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_5":
                        columnInfo.Fields.Add("org_id_5");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_5").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_5":
                        columnInfo.Fields.Add("org_name_5");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_5").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_6":
                        columnInfo.Fields.Add("org_id_6");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_6").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_6":
                        columnInfo.Fields.Add("org_name_6");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_6").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_7":
                        columnInfo.Fields.Add("org_id_7");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_7").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_7":
                        columnInfo.Fields.Add("org_name_7");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_7").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id_8":
                        columnInfo.Fields.Add("org_id_8");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgId_8").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_name_8":
                        columnInfo.Fields.Add("org_name_8");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_OrgName_8").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "fk_department_code":
                        columnInfo.Fields.Add("fk_department_code");
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "OrgStructureImp_DepartmentCode").Value.LangText + mandatorySuffix);
                        columnInfo.DataTypes.Add("string");
                        break;
                }
            }

            return columnInfo;
        }

        public async Task<AiWorkflowState> GetWorkflowState(string userId, long jobId = -1)
        {
            ImportInfo info = await GetOrgStructureImportInfo(userId, jobId);
            TcoJobStatus status;
            AiWorkflowState state;
            if (jobId == -1)
            {
                status = await _utility.GetJobProgressAsync(userId, UserTrackedJobs.OrgStructureImport);
                if (info.TotalRows == 0 && status == null)
                {
                    //Initial state
                    state = AiWorkflowState.Initial;
                }
                else if ((info.TotalRows > 0 && status == null))
                {
                    //Staged state
                    state = AiWorkflowState.Staged;
                }
                else if (status?.EndTime == null)
                {
                    //Importing state
                    //Job is in progress if EndTime is not set
                    state = AiWorkflowState.Importing;
                }
                else if (status.TotalSteps > status.StepsCompleted && status.EndTime != null)
                {
                    //if EndTime is set
                    state = AiWorkflowState.Failed;
                }
                else
                {
                    state = AiWorkflowState.Finished;
                }
            }
            else
            {  // get the status for system user created jobs
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                status = await _unitOfWork.OrgStructureImportRepository.GetJobStatus(userDetails.tenant_id, jobId);
                if (info.TotalRows == 0)
                {
                    //Initial state
                    state = AiWorkflowState.Initial;
                }
                else if ((status != null && (status.jobStatus == StatusEnumData.CompletedWithErrors.ToString() || status.jobStatus == StatusEnumData.StagingValidationError.ToString() || status.jobStatus == StatusEnumData.NotStarted.ToString())))
                {
                    //Staged state
                    state = AiWorkflowState.Staged;
                }
                else if (status?.EndTime == null)
                {
                    //Importing state
                    //Job is in progress if EndTime is not set
                    state = AiWorkflowState.Importing;
                }
                else if (status.TotalSteps > status.StepsCompleted && status.EndTime != null)
                {
                    //if EndTime is set
                    state = AiWorkflowState.Failed;
                }
                else
                {
                    state = AiWorkflowState.Finished;
                }
            }
            return state;
        }

        private async Task ValidateImportedOrgStructureData(string userId, long jobId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

                var spValidate = new prcValidateOrgStructureImport
                {
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id,
                    JobId = jobId
                };

                tenantDbContext.Database.ExecuteStoredProcedure(spValidate);
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<DataTable> GetOrgStructureImportData(string userId)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            DataTable table = _utility.ConvertToDataTable(await _unitOfWork.OrgStructureImportRepository.GetMainTableData(userData.tenant_id));
            return table;
        }

        public async Task ImportOrgStructureDataFromStaging(string userId, int tenantId, long jobId, bool isRequestByJobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(600);

            UserData userData = await _utility.GetUserDetailsAsync(userId);
            CultureInfo cultIn = CultureInfoFactory.CreateCulture(userData.language_preference);

            try
            {
                List<tbu_stage_org_structure_import> stagedOrgData = !(isRequestByJobId) ? await tenantDbContext.tbu_stage_org_structure_import.Where(x => x.user_id == userData.pk_id && x.fk_tenant_id == tenantId).ToListAsync()
                                                                                      : await tenantDbContext.tbu_stage_org_structure_import.Where(x => x.job_Id == jobId && x.fk_tenant_id == tenantId).ToListAsync();
                //make it repository later

                //update job status start time
                TcoJobStatus jobStatus = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                if (jobStatus != null)
                {
                    jobStatus.StartTime = DateTime.UtcNow;
                    jobStatus.jobStatus = StatusEnumData.InProgress.ToString();
                    jobStatus.EndTime = null;
                    jobStatus.TotalSteps = stagedOrgData.Count;
                    jobStatus.StepsCompleted = 0;

                    tenantDbContext.Entry(jobStatus).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }

                List<tco_org_hierarchy> newOrgStrList = new List<tco_org_hierarchy>();
                List<tbu_stage_org_structure_import> delStagedList = new List<tbu_stage_org_structure_import>();

                List<string> orgVersions = new List<string>();

                if (stagedOrgData.Any())
                {
                    orgVersions = stagedOrgData.Select(x => x.fk_org_version).Distinct().ToList();
                    var dataToRemove = await tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == tenantId && orgVersions.Contains(x.fk_org_version)).ToListAsync();

                    var departments = stagedOrgData.Select(x => x.fk_department_code).ToList();

                    dataToRemove = dataToRemove.Where(x => departments.Contains(x.fk_department_code)).ToList();

                    await tenantDbContext.BulkDeleteAsync(dataToRemove);
                    await tenantDbContext.SaveChangesAsync();
                }

                //adding records into org specific data level tables
                await AddIntoOrgSpecificTable(userId, stagedOrgData);
                foreach (var stageRow in stagedOrgData)
                {                  
                    tco_org_hierarchy newRec = new tco_org_hierarchy()
                    {
                        fk_tenant_id = tenantId,
                        org_id_1 = stageRow.org_id_1.Trim(),
                        org_name_1 = stageRow.org_name_1.Trim(),
                        org_id_2 = stageRow.org_id_2.Trim(),
                        org_name_2 = stageRow.org_name_2.Trim(),
                        org_id_3 = stageRow.org_id_3.Trim(),
                        org_name_3 = stageRow.org_name_3.Trim(),
                        org_id_4 = stageRow.org_id_4.Trim(),
                        org_name_4 = stageRow.org_name_4.Trim(),
                        org_id_5 = stageRow.org_id_5.Trim(),
                        org_name_5 = stageRow.org_name_5.Trim(),
                        org_id_6 = stageRow.org_id_6.Trim(),
                        org_name_6 = stageRow.org_name_6.Trim(),
                        org_id_7 = stageRow.org_id_7.Trim(),
                        org_name_7 = stageRow.org_name_7.Trim(),
                        org_id_8 = stageRow.org_id_8.Trim(),
                        org_name_8 = stageRow.org_name_8.Trim(),
                        fk_org_version = stageRow.fk_org_version.Trim(),
                        fk_department_code = stageRow.fk_department_code.Trim(),
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        org_shortname_1 = string.Empty,
                        org_shortname_2 = string.Empty,
                        org_shortname_3 = string.Empty,
                        org_shortname_4 = string.Empty,
                        org_shortname_5 = string.Empty,
                        org_shortname_6 = string.Empty,
                        org_shortname_7 = string.Empty,
                        org_shortname_8 = string.Empty
                    };

                    newOrgStrList.Add(newRec);
                }

                await tenantDbContext.BulkInsertAsync(newOrgStrList);
                await tenantDbContext.SaveChangesAsync();

                List<OrgParentLevelHelper> parentLevelValues = new();
                //Bus : 108418, here
                var userOrgInfo = new UserOrgRoleInfoHelper
                {
                        UserId = userId,
                        OrgVersion = "",
                        ImmediateLevelAboveDept = 0,
                        ImmediateLevelValueAboveDept = new List<string?>(),
                        Department = new List<OrgIdVersionHelper>(),
                        DepartmentLevel = 0,
                        ParentLevelData = new List<OrgParentLevelHelper>()
                };
                foreach (var orgVersion in orgVersions)
                {
                    var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                    var deptLevel = orgVersionContent.lstOrgLevel.Max(y => y.org_level);
                    var stagedData = stagedOrgData.Where(x => x.fk_org_version == orgVersion).ToList();
                    userOrgInfo.OrgVersion = orgVersion;
                    
                    for (int orgLevel = 2; orgLevel <= deptLevel; orgLevel++)
                    {
                        switch (orgLevel)
                        {
                            case 8:
                                var orgId8Values = stagedData.ToList().GroupBy(x => new { x.org_id_8, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_8, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_8, x.org_id_7 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_7, CurrentOrgId = x.Key.org_id_8 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 7;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_7).Distinct().ToList();
                                userOrgInfo.Department = orgId8Values;
                                userOrgInfo.DepartmentLevel = 8;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            case 7:
                                var orgId7Values = stagedData.ToList().GroupBy(x => new { x.org_id_7, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_7, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_7, x.org_id_6 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_6, CurrentOrgId = x.Key.org_id_7 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 6;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_6).Distinct().ToList();
                                userOrgInfo.Department = orgId7Values;
                                userOrgInfo.DepartmentLevel = 7;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            case 6:
                                var orgId6Values = stagedData.ToList().GroupBy(x => new { x.org_id_6, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_6, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_5, x.org_id_6 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_5, CurrentOrgId = x.Key.org_id_6 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 5;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_5).Distinct().ToList();
                                userOrgInfo.Department = orgId6Values;
                                userOrgInfo.DepartmentLevel = 6;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            case 5:
                                var orgId5Values = stagedData.ToList().GroupBy(x => new { x.org_id_5, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_5, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_4, x.org_id_5 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_4, CurrentOrgId = x.Key.org_id_5 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 4;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_4).Distinct().ToList();
                                userOrgInfo.Department = orgId5Values;
                                userOrgInfo.DepartmentLevel = 5;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            case 4:
                                var orgId4Values = stagedData.ToList().GroupBy(x => new { x.org_id_4, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_4, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_3, x.org_id_4 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_3, CurrentOrgId = x.Key.org_id_4 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 3;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_3).Distinct().ToList();
                                userOrgInfo.Department = orgId4Values;
                                userOrgInfo.DepartmentLevel = 4;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            case 3:
                                var orgId3Values = stagedData.ToList().GroupBy(x => new { x.org_id_3, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_3, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_2, x.org_id_3 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_2, CurrentOrgId = x.Key.org_id_3 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 2;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_2).Distinct().ToList();
                                userOrgInfo.Department = orgId3Values;
                                userOrgInfo.DepartmentLevel = 3;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            case 2:
                                var orgId2Values = stagedData.ToList().GroupBy(x => new { x.org_id_2, x.fk_org_version }).Select(x => new OrgIdVersionHelper { fk_org_id = x.Key.org_id_2, fk_org_version = x.Key.fk_org_version }).ToList();
                                parentLevelValues = stagedData.ToList().GroupBy(x => new { x.org_id_1, x.org_id_2 }).Select(x => new OrgParentLevelHelper { ParentOrgId = x.Key.org_id_1, CurrentOrgId = x.Key.org_id_2 }).ToList();
                                userOrgInfo.ImmediateLevelAboveDept = 1;
                                userOrgInfo.ImmediateLevelValueAboveDept = stagedData.Select(x => x.org_id_1).Distinct().ToList();
                                userOrgInfo.Department = orgId2Values;
                                userOrgInfo.DepartmentLevel = 2;
                                userOrgInfo.ParentLevelData = parentLevelValues;
                                await _adminOrgStructure.AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(userOrgInfo);
                                break;

                            default:
                                break;
                        }
                    }

                    await tenantDbContext.BulkDeleteAsync(stagedData);
                    await tenantDbContext.BulkSaveChangesAsync();

                    await DeleteTenantCache(userId, orgVersion);
                }

                //updating tcoJobStatus steps & jobStatus
                jobStatus = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                if (jobStatus != null)
                {
                    jobStatus.EndTime = DateTime.UtcNow;
                    jobStatus.StepsCompleted = jobStatus.TotalSteps;
                    jobStatus.jobStatus = StatusEnumData.CompletedWithoutErrors.ToString();
                    tenantDbContext.Entry(jobStatus).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            catch (Exception e)
            {
                TcoJobStatus jobStatus = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                if (jobStatus != null)
                {
                    jobStatus.message = e.Message;
                    jobStatus.EndTime = DateTime.UtcNow;
                    jobStatus.jobStatus = StatusEnumData.CompletedWithErrors.ToString();
                    tenantDbContext.Entry(jobStatus).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
        }

        public async Task<DataTable> GetImportStagedData(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            DataTable result = _utility.ConvertToDataTable(await _unitOfWork.OrgStructureImportRepository.GetStagedData(userDetails.tenant_id));
            return result;
        }

        public async Task UpdateOrgHierarchy(UserData userData, tbu_stage_org_structure_import stageRow)
        {
            TenantDBContext _tenantDBContext = await _utility.GetTenantDBContextAsync();
            var orgData = await _tenantDBContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_1 == stageRow.org_id_1 && x.fk_org_version == stageRow.fk_org_version).ToListAsync();

            var orgName = orgData != null ? orgData.FirstOrDefault(x => x.org_id_1 == stageRow.org_id_1).org_name_1 : string.Empty;
            if (orgData != null && orgData.Any() && !string.IsNullOrEmpty(stageRow.org_id_1) && orgName != stageRow.org_name_1)
            {
                foreach(var data in orgData)
                {
                    data.org_name_1 = stageRow.org_name_1;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level2Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_2 == stageRow.org_id_2 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level2Data != null && level2Data.Any() ? level2Data.FirstOrDefault().org_name_2 : string.Empty;

            if (level2Data != null && level2Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_2) && orgName != stageRow.org_name_2)
            {
                foreach (var data in level2Data)
                {
                    data.org_name_2 = stageRow.org_name_2;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level3Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_3 == stageRow.org_id_3 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level3Data != null && level3Data.Any() ? level3Data.FirstOrDefault().org_name_3 : string.Empty;

            if (level3Data != null && level3Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_3) && orgName != stageRow.org_name_3)
            {
                foreach (var data in level3Data)
                {
                    data.org_name_3 = stageRow.org_name_3;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level4Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_4 == stageRow.org_id_4 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level4Data != null && level4Data.Any() ? level4Data.FirstOrDefault().org_name_4 : string.Empty;

            if (level4Data != null && level4Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_4) && orgName != stageRow.org_name_4)
            {
                foreach (var data in level4Data)
                {
                    data.org_name_4 = stageRow.org_name_4;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level5Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_5 == stageRow.org_id_5 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level5Data != null && level5Data.Any() ? level5Data.FirstOrDefault().org_name_5 : string.Empty;

            if (level5Data != null && level5Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_5) && orgName != stageRow.org_name_5)
            {
                foreach (var data in level5Data)
                {
                    data.org_name_5 = stageRow.org_name_5;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level6Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_6 == stageRow.org_id_6 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level6Data != null && level6Data.Any() ? level6Data.FirstOrDefault().org_name_6 : string.Empty;

            if (level6Data != null && level6Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_6) && orgName != stageRow.org_name_6)
            {
                foreach (var data in level6Data)
                {
                    data.org_name_6 = stageRow.org_name_6;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level7Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_7 == stageRow.org_id_7 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level7Data != null && level7Data.Any() ? level7Data.FirstOrDefault().org_name_7 : string.Empty;

            if (level7Data != null && level7Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_7) && orgName != stageRow.org_name_7)
            {
                foreach (var data in level7Data)
                {
                    data.org_name_7 = stageRow.org_name_7;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }

            var level8Data = orgData.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_8 == stageRow.org_id_8 && x.fk_org_version == stageRow.fk_org_version).ToList();
            orgName = level8Data != null && level8Data.Any() ? level8Data.FirstOrDefault().org_name_8 : string.Empty;

            if (level8Data != null && level8Data.Any() && !string.IsNullOrEmpty(stageRow.org_id_8) && orgName != stageRow.org_name_8)
            {
                foreach (var data in level8Data)
                {
                    data.org_name_8 = stageRow.org_name_8;
                    data.updated = DateTime.UtcNow;
                    data.updated_by = userData.pk_id;
                }
                await _tenantDBContext.SaveChangesAsync();
            }
        }

        public async Task AddIntoOrgSpecificTable(string userId, List<tbu_stage_org_structure_import> stagedData)
        {
            TenantDBContext _tenantDBContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            var orgVersion = stagedData.FirstOrDefault().fk_org_version;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
            List<tco_org_data_level_1> orgDataLvl1 = new List<tco_org_data_level_1>();
            List<tco_org_data_level_2> orgDataLvl2 = new List<tco_org_data_level_2>();
            List<tco_org_data_level_3> orgDataLvl3 = new List<tco_org_data_level_3>();
            List<tco_org_data_level_4> orgDataLvl4 = new List<tco_org_data_level_4>();
            List<tco_org_data_level_5> orgDataLvl5 = new List<tco_org_data_level_5>();
            List<tco_org_data_level_6> orgDataLvl6 = new List<tco_org_data_level_6>();
            List<tco_org_data_level_7> orgDataLvl7 = new List<tco_org_data_level_7>();
            List<tco_org_data_level_8> orgDataLvl8 = new List<tco_org_data_level_8>();
            foreach(var stageRow in stagedData)
            {
                var checkOrgLevel_1 = orgVersionContent.lstOrgDataLevel1.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_1 == stageRow.org_id_1 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_1.Any() && !string.IsNullOrEmpty(stageRow.org_id_1) && !(orgDataLvl1.Any(x=>x.org_id_1==stageRow.org_id_1)))
                {
                    tco_org_data_level_1 newEntry = new tco_org_data_level_1()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_1 = stageRow.org_id_1.Trim(),
                        org_name_1 = stageRow.org_name_1.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_1 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl1.Add(newEntry);

                }
                else
                {
                    if (checkOrgLevel_1.Any())
                    {
                        checkOrgLevel_1[0].org_name_1 = stageRow.org_name_1.Trim();
                        checkOrgLevel_1[0].updated = DateTime.UtcNow;
                        checkOrgLevel_1[0].updated_by = userData.pk_id;
                    }

                }

                var checkOrgLevel_2 = orgVersionContent.lstOrgDataLevel2.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_2 == stageRow.org_id_2 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_2.Any() && !string.IsNullOrEmpty(stageRow.org_id_2) && !(orgDataLvl2.Any(x => x.org_id_2 == stageRow.org_id_2)))
                {
                    tco_org_data_level_2 newEntry = new tco_org_data_level_2()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_2 = stageRow.org_id_2.Trim(),
                        org_name_2 = stageRow.org_name_2.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_2 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl2.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_2.Any())
                    {
                        checkOrgLevel_2[0].org_name_2 = stageRow.org_name_2.Trim();
                        checkOrgLevel_2[0].updated = DateTime.UtcNow;
                        checkOrgLevel_2[0].updated_by = userData.pk_id;
                    }

                }
                var checkOrgLevel_3 = orgVersionContent.lstOrgDataLevel3.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_3 == stageRow.org_id_3 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_3.Any() && !string.IsNullOrEmpty(stageRow.org_id_3) && !(orgDataLvl3.Any(x => x.org_id_3 == stageRow.org_id_3)))
                {
                    tco_org_data_level_3 newEntry = new tco_org_data_level_3()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_3 = stageRow.org_id_3.Trim(),
                        org_name_3 = stageRow.org_name_3.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_3 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl3.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_3.Any())
                    {
                        checkOrgLevel_3[0].org_name_3 = stageRow.org_name_3.Trim();
                        checkOrgLevel_3[0].updated = DateTime.UtcNow;
                        checkOrgLevel_3[0].updated_by = userData.pk_id;
                    }

                }
                var checkOrgLevel_4 = orgVersionContent.lstOrgDataLevel4.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_4 == stageRow.org_id_4 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_4.Any() && !string.IsNullOrEmpty(stageRow.org_id_4) && !(orgDataLvl4.Any(x => x.org_id_4 == stageRow.org_id_4)))
                {
                    tco_org_data_level_4 newEntry = new tco_org_data_level_4()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_4 = stageRow.org_id_4.Trim(),
                        org_name_4 = stageRow.org_name_4.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_4 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl4.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_4.Any())
                    {
                        checkOrgLevel_4[0].org_name_4 = stageRow.org_name_4.Trim();
                        checkOrgLevel_4[0].updated = DateTime.UtcNow;
                        checkOrgLevel_4[0].updated_by = userData.pk_id;
                    }

                }
                var checkOrgLevel_5 = orgVersionContent.lstOrgDataLevel5.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_5 == stageRow.org_id_5 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_5.Any() && !string.IsNullOrEmpty(stageRow.org_id_5) && !(orgDataLvl5.Any(x => x.org_id_5 == stageRow.org_id_5)))
                {
                    tco_org_data_level_5 newEntry = new tco_org_data_level_5()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_5 = stageRow.org_id_5.Trim(),
                        org_name_5 = stageRow.org_name_5.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_5 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl5.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_5.Any())
                    {
                        checkOrgLevel_5[0].org_name_5 = stageRow.org_name_5.Trim();
                        checkOrgLevel_5[0].updated = DateTime.UtcNow;
                        checkOrgLevel_5[0].updated_by = userData.pk_id;
                    }

                }
                var checkOrgLevel_6 = orgVersionContent.lstOrgDataLevel6.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_6 == stageRow.org_id_6 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_6.Any() && !string.IsNullOrEmpty(stageRow.org_id_6) && !(orgDataLvl6.Any(x => x.org_id_6 == stageRow.org_id_6)))
                {
                    tco_org_data_level_6 newEntry = new tco_org_data_level_6()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_6 = stageRow.org_id_6.Trim(),
                        org_name_6 = stageRow.org_name_6.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_6 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl6.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_6.Any())
                    {
                        checkOrgLevel_6[0].org_name_6 = stageRow.org_name_6.Trim();
                        checkOrgLevel_6[0].updated = DateTime.UtcNow;
                        checkOrgLevel_6[0].updated_by = userData.pk_id;
                    }

                }
                var checkOrgLevel_7 = orgVersionContent.lstOrgDataLevel7.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_7 == stageRow.org_id_7 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_7.Any() && !string.IsNullOrEmpty(stageRow.org_id_7) && !(orgDataLvl7.Any(x => x.org_id_7 == stageRow.org_id_7)))
                {
                    tco_org_data_level_7 newEntry = new tco_org_data_level_7()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_7 = stageRow.org_id_7.Trim(),
                        org_name_7 = stageRow.org_name_7.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_7 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl7.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_7.Any())
                    {
                        checkOrgLevel_7[0].org_name_7 = stageRow.org_name_7.Trim();
                        checkOrgLevel_7[0].updated = DateTime.UtcNow;
                        checkOrgLevel_7[0].updated_by = userData.pk_id;
                    }

                }
                var checkOrgLevel_8 =  orgVersionContent.lstOrgDataLevel8.Where(x => x.fk_tenant_id == userData.tenant_id && x.org_id_8 == stageRow.org_id_8 && x.fk_org_version == stageRow.fk_org_version).ToList();

                if (!checkOrgLevel_8.Any() && !string.IsNullOrEmpty(stageRow.org_id_8) && !(orgDataLvl8.Any(x => x.org_id_8 == stageRow.org_id_8)))
                {
                    tco_org_data_level_8 newEntry = new tco_org_data_level_8()
                    {
                        fk_tenant_id = userData.tenant_id,
                        org_id_8 = stageRow.org_id_8.Trim(),
                        org_name_8 = stageRow.org_name_8.Trim(),
                        fk_org_version = stageRow.fk_org_version,
                        org_short_name_8 = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userData.pk_id,
                        status = 1
                    };
                    orgDataLvl8.Add(newEntry);
                }
                else
                {
                    if (checkOrgLevel_8.Any())
                    {
                        checkOrgLevel_8[0].org_name_8 = stageRow.org_name_8.Trim();
                        checkOrgLevel_8[0].updated = DateTime.UtcNow;
                        checkOrgLevel_8[0].updated_by = userData.pk_id;
                    }

                }
            }
            await _tenantDBContext.BulkInsertAsync(orgDataLvl1);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl2);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl3);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl4);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl5);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl6);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl7);
            await _tenantDBContext.BulkInsertAsync(orgDataLvl8);
            await _tenantDBContext.BulkSaveChangesAsync();
            //await _tenantDBContext.SaveChangesAsync();
        }

        public async Task<List<TcoJobsHelper>> GetTcoJobStatusTransactions(string userId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            List<TcoJobsHelper> transactionData = new List<TcoJobsHelper>();
            List<string> jobStatusList = new List<string>() { StatusEnumData.NotStarted.ToString(), StatusEnumData.StagingValidationError.ToString(), StatusEnumData.CompletedWithErrors.ToString() };
            List<string> jobTypeList = new List<string>() { "OrgStructureImport" };
            Dictionary<string, clsLanguageString> langStringValuesAcctimport = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "OrgStructureImport");
            string orgStructureImportLangString = langStringValuesAcctimport["OrgStructureImp_JobId_DropDown_Text"].LangText;
            var tempData = await (from a in tenantDbContext.TcoJobStatus
                                  join b in tenantDbContext.tbu_stage_org_structure_import on a.PkId equals b.job_Id
                                  where a.TenantId == userDetails.tenant_id
                                  orderby a.PkId descending
                                  select new
                                  {
                                      PkId = a.PkId.ToString(),
                                      JobType = a.JobType,
                                      StartTime = a.StartTime
                                  }).Distinct().ToListAsync();

            var norwayTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");

            tempData.ForEach(a =>
            {
                transactionData.Add(new TcoJobsHelper()
                {
                    Key = a.PkId,
                    Value = (a.PkId).ToString() + "-" + orgStructureImportLangString + "-" + (TimeZoneInfo.ConvertTimeFromUtc(a.StartTime, norwayTimeZone).ToString("g", ci)),
                });
            });
            return transactionData;
        }

        public async Task<TcoJobStatus> GetImportOrgStructureJobProgressByJobId(string userId, long jobId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var jobStatus = await _unitOfWork.OrgStructureImportRepository.GetImportJobStatusWithJobId(userDetails.tenant_id, jobId);
            return jobStatus;
        }

        private async Task DeleteTenantCache(string userId, string orgVersion)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            string prefix = AppConfiguration.GetConfigurationSetting("frontendCacheKeyPrefix");

            var key = $"GetOrgVersionSpecificContentBasedOnOrgVersion_OrgStruct_{orgVersion}";
            using (AppDataCache cache = new AppDataCache(true, prefix, new CacheClientFactory()))
            {
                cache.DeleteStringForTenant(clientId, userDetails.tenant_id, key);
            }
        }
    }
}