#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8625

using Framsikt.BL.Core.BudgetProposalPartial;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Framsikt.BL
{
    public class BudgetProposalStrategy : IBudgetProposalStrategy
    {
        private readonly IUtility _utility;
        private readonly IConsequenceAdjustedBudget _consAdjBudget;
        private readonly IBudPropUtility _bpUtility;
        private readonly IBudgetProposal _budgetProposal;
        private readonly IBusinessPlan _businessPlan;
        private readonly IFinUtility _finUtility;

        public BudgetProposalStrategy(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _consAdjBudget = container.GetRequiredService<IConsequenceAdjustedBudget>();
            _bpUtility = container.GetRequiredService<IBudPropUtility>();
            _budgetProposal = container.GetRequiredService<IBudgetProposal>();
            _businessPlan = container.GetRequiredService<IBusinessPlan>();
            _finUtility = container.GetRequiredService<IFinUtility>();
        }

        public async Task<dynamic> GetStrategyGridAsync(string userName, string orgId, int orgLevel, string serviceId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposal");

            dynamic bpStratGrid = new JObject();

            bpStratGrid.Title = ((langStrings.FirstOrDefault(v => v.Key == "bpStratGridTitle")).Value).LangText;
            bpStratGrid.Description = ((langStrings.FirstOrDefault(v => v.Key == "bpStratGridTooltip")).Value).LangText;

            dynamic columnsArray = GetGridColumns(langStrings);
            bpStratGrid.Add("columns", columnsArray);

            var gridData = new JArray();
            List<BudPropStratxtHelper> straTxtData = await _budgetProposal.FetchStraTxtDataAsync(userName, orgId, orgLevel, serviceId, budgetYear, false, string.Empty);

            List<KeyValueData> lstActionTags = await _utility.GetActionTagsAsync(userName);
            TargetFormttingHelper data = new TargetFormttingHelper();

            foreach (var std in straTxtData)
            {
                dynamic row = new JObject();
                row.id = std.straTxtId;
                row.strategy = std.strategyName;
                row.strategyDesc = std.strategyDesc;
                row.strategyDescHistoryId = std.stratDescHistoryId;

                StringBuilder sb = new StringBuilder();

                foreach (int ati in std.actionTagIds)
                {
                    var lat = lstActionTags.FirstOrDefault(x => x.KeyId == ati);
                    if (lat != null)
                    {
                        sb.Append("<span class='bp-grey-tag white-space-normal'>" + lat.ValueString + "</span>&nbsp;");
                    }
                }
                std.orgName = string.IsNullOrEmpty(sb.ToString()) ? std.orgName : "</br>" + std.orgName;
                std.orgName = string.IsNullOrEmpty(std.planShortName) ? std.orgName : std.orgName + "</br>";
                row.tags = sb.ToString() + std.orgName + std.planShortName;

                data.goalsLinked = std.goalsLinked;
                data.targetsLinked = std.targetsLinked;
                data.isBusinessPlanGoal = false;
                if (std.goalsLinked.Count > 0 || std.targetsLinked.Count > 0)
                {
                    row.target = await _businessPlan.FormatTargetDataForAssignmentOrStrategyAsync(userName, budgetYear, data);
                }
                else
                {
                    row.target = string.Empty;
                }
                row.isPlanStrategy = std.isPlanStrategy;
                gridData.Add(row);
            }
            bpStratGrid.Add("data", gridData);
            return bpStratGrid;
        }

        private dynamic GetGridColumns(Dictionary<string, clsLanguageString> langStrings)
        {
            List<string> columnFields = new List<string> { "target", "strategy", "strategyDesc" };
            List<string> columnTitles = new List<string>();
            foreach (var cf in columnFields)
            {
                columnTitles.Add(((langStrings.FirstOrDefault(v => v.Key == "bpStrategyGrid_" + cf)).Value).LangText);
            }

            List<int> colCounts = new List<int> { 1, 2, 3 };
            List<bool> encodes = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();
            List<bool> hiddens = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();
            List<bool> expands = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();
            List<int> widths = new List<int> { 175, 175, 400 };
            List<string> attrStyles = new List<string> { "text-align:left; border-left:none; padding-bottom:2%;vertical-align: top;width:175px;", "text-align:left; padding-bottom:2%;vertical-align: top;width:175px;", "text-align:left; vertical-align: top;width:400px;" };
            List<string> hdrAttrStyles = new List<string> { "text-align:left; border-left:none;width:175px;", "text-align:left;width:175px;", "text-align:left; border-left:none; white-space:nowrap;width:400px;" };
            List<string> ftrAddrStyles = Enumerable.Repeat<string>(null, columnFields.Count).ToList();
            List<string> templates = new List<string> { null, "<span class='strategy-tag-template'></span>",
                                                        "# if(strategyDesc.length < 150) {#<div class='strategydesc-ckeditor' style='height:103px;border-radius:5px;'></div>#}else{#<div class='strategydesc-ckeditor padding5' style='height:103px;border-radius:5px;'></div>#}#"
            };
            List<string> hdrTemplates = Enumerable.Repeat<string>(null, columnFields.Count).ToList();
            List<string> ftrTemplates = Enumerable.Repeat<string>(null, columnFields.Count).ToList();
            List<bool> lockeds = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();

            dynamic columnsArray = new JArray();

            for (int i = 0; i < columnFields.Count; i++)
            {
                columnsArray.Add(_utility.GenerateColumnObject(columnTitles.ElementAt(i),
                                                                columnFields.ElementAt(i),
                                                                colCounts.ElementAt(i),
                                                                encodes.ElementAt(i),
                                                                hiddens.ElementAt(i),
                                                                expands.ElementAt(i),
                                                                widths.ElementAt(i),
                                                                attrStyles.ElementAt(i),
                                                                hdrAttrStyles.ElementAt(i),
                                                                ftrAddrStyles.ElementAt(i),
                                                                templates.ElementAt(i),
                                                                hdrTemplates.ElementAt(i),
                                                                ftrTemplates.ElementAt(i),
                                                                lockeds.ElementAt(i)));
            }
            return columnsArray;
        }

        public dynamic GetStrategyDetById(string userName, string id, int budgetYear, string orgId, string serviceId, int orgLevel)
        {
            return GetStrategyDetByIdAsync(userName, id, budgetYear, orgId, serviceId, orgLevel).GetAwaiter().GetResult();
        }

        public async Task<dynamic> GetStrategyDetByIdAsync(string userName, string id, int budgetYear, string orgId, string serviceId, int orgLevel)
        {
            UserData userdata = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<Guid> goalIdsList = new List<Guid>();
            List<string> goalsLinked = new List<string>();
            List<string> targetsLinked = new List<string>();
            List<int> stratAcTagIds = new List<int>();
            List<string> assignmentLinked = new List<string>();
            string unsDef = string.Empty;
            string faDef = string.Empty;
            string unTarget = string.Empty;

            dynamic retData = new JObject();
            if (string.IsNullOrEmpty(id))
            {
                retData.strategy = string.Empty;
                retData.strategyDesc = string.Empty;
                retData.strategyDescHistoryId = Guid.NewGuid();
                retData.goalIds = JToken.FromObject(goalsLinked);
                retData.targetIds = JToken.FromObject(targetsLinked);
                retData.actionTagIds = JToken.FromObject(stratAcTagIds);
                retData.assignmentIds = JToken.FromObject(assignmentLinked);
            }
            else
            {
                //Show targets/goals only if dilegated strategy level has goal/goal linked to the target(when user selects only target)
                string goalIdsInfo = await _budgetProposal.GetCityGoalsForTargetAsync(userName, orgId, orgLevel, serviceId, budgetYear, orgLevel == 1);
                dynamic dataToManipulate = JsonConvert.DeserializeObject(goalIdsInfo);
                var datalist = (JArray)dataToManipulate["Data"];
                var goalsKeyList = (from s in datalist
                                    select new
                                    {
                                        Key = (string)s["key"]
                                    }).ToList();
                goalIdsList = goalsKeyList.Select(x => Guid.Parse(x.Key)).Distinct().ToList();

                int strategyId = int.Parse(id);
                tfp_strategy_text tst = await dbContext.tfp_strategy_text.Where(x => x.pk_strategy_id == strategyId && x.fk_tenant_id == userdata.tenant_id && !x.is_busplan).FirstOrDefaultAsync();
                if (tst != null)
                {
                    retData.strategy = tst.strategy_name;
                    retData.strategyDesc = tst.strategy_desc;
                    retData.strategyDescHistoryId = tst.strategy_desc_history == Guid.Empty ? Guid.NewGuid() : tst.strategy_desc_history;

                    if (!string.IsNullOrEmpty(tst.tags))
                    {
                        stratAcTagIds = ((string)tst.tags).Split(',').Select(int.Parse).ToList();
                    }

                    retData.actionTagIds = JToken.FromObject(stratAcTagIds);

                    List<tfp_strategy_goal> tsgList = await dbContext.tfp_strategy_goal.Where(x => x.fk_tenant_id == userdata.tenant_id && x.fk_strategy_id == tst.pk_strategy_id && goalIdsList.Contains(x.fk_goal_id)).ToListAsync();
                    goalsLinked = tsgList != null ? tsgList.Select(x => x.fk_goal_id.ToString()).ToList() : goalsLinked;

                    List<tfp_strategy_target> targetsList = await dbContext.tfp_strategy_target.Where(x => x.fk_tenant_id == userdata.tenant_id && x.fk_strategy_id == tst.pk_strategy_id).ToListAsync();
                    targetsLinked = targetsList != null ? targetsList.Select(x => x.fk_target_id.ToString()).ToList() : targetsLinked;
                    targetsLinked = await dbContext.tco_targets.Where(x => x.fk_tenant_id == userdata.tenant_id && targetsLinked.Contains(x.pk_target_id.ToString()) && goalIdsList.Contains(x.fk_goal_id)).Select(x => x.pk_target_id.ToString()).ToListAsync();
                    List<TbiAssignmentStrategy> assignmentsList = await dbContext.TbiAssignmentStrategy.Where(x => x.fk_tenant_id == userdata.tenant_id && x.fk_strategy_id == tst.pk_strategy_id).ToListAsync();
                    assignmentLinked = assignmentsList.Select(x => x.fk_assignment_id.ToString()).ToList();
                    retData.assignmentIds = JToken.FromObject(assignmentLinked);
                    retData.targetIds = JToken.FromObject(targetsLinked);
                    retData.goalIds = JToken.FromObject(goalsLinked);
                }
                else
                {
                    retData.strategy = string.Empty;
                    retData.strategyDesc = string.Empty;
                    retData.strategyDescHistoryId = Guid.NewGuid();
                    retData.assignmentIds = JToken.FromObject(assignmentLinked);
                    retData.goalIds = JToken.FromObject(goalsLinked);
                    retData.targetIds = JToken.FromObject(targetsLinked);
                    retData.actionTagIds = JToken.FromObject(stratAcTagIds);
                }

                //To display focus area and UNS goals tagged to goal/target
                if (goalsLinked.Count > 0 || targetsLinked.Count > 0)
                {
                    List<TargetDropdownHelper> faOrUnsgoalIdsData = new List<TargetDropdownHelper>();
                    goalsLinked = goalsLinked.Count > 0 ? goalsLinked : await dbContext.tco_targets.Where(x => x.fk_tenant_id == userdata.tenant_id && targetsLinked.Contains(x.pk_target_id.ToString())).Select(x => x.fk_goal_id.ToString()).ToListAsync();

                    faOrUnsgoalIdsData = await (from a in dbContext.tco_goals
                                                where a.fk_tenant_id == userdata.tenant_id && goalsLinked.Contains(a.pk_goal_id.ToString())
                                                select new TargetDropdownHelper
                                                {
                                                    unsdGoals = a.unsd_goals,
                                                    focusAreaIds = a.focus_area
                                                }).Distinct().ToListAsync();

                    var UnTargetIdsData = await (from a in dbContext.tco_targets
                                                 where a.fk_tenant_id == userdata.tenant_id && targetsLinked.Contains(a.pk_target_id.ToString())
                                                 select new
                                                 {
                                                     UnTarget = a.unsd_target
                                                 }).ToListAsync();

                    List<string> unTargetFromData = new List<string>();
                    List<string> unsListFromData = new List<string>();
                    List<string> focusAreaListFromData = new List<string>();

                    foreach (var u in faOrUnsgoalIdsData)
                    {
                        List<string> unsIds = new List<string>();
                        unsIds = u.unsdGoals.Split(',').Distinct().ToList();
                        unsListFromData.AddRange(unsIds);

                        List<string> faIds = new List<string>();
                        faIds = u.focusAreaIds.Split(',').Distinct().ToList();
                        focusAreaListFromData.AddRange(faIds);
                    }

                    foreach (var item in UnTargetIdsData)
                    {
                        List<string> unIds = new List<string>();
                        unIds = item.UnTarget.Split(',').Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();
                        unTargetFromData.AddRange(unIds);
                    }
                    var focusAreaInfo = await dbContext.tco_focusarea.Where(x => x.fk_tenant_id == userdata.tenant_id).ToListAsync();
                    var unsgoalsInfo = await dbContext.gco_un_susdev_goals.Select(x => new { x.pk_goal_id, x.goal_short_name }).ToListAsync();
                    var unsdTargetInfo = await dbContext.gco_un_susdev_targets.Select(x => new { x.pk_target_id, x.target_name }).ToListAsync();
                    foreach (var uns in unsListFromData.Distinct())
                    {
                        if (!string.IsNullOrEmpty(uns))
                        {
                            unsDef = unsDef + "<span class='bp-blue-tag cursor'>" + unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_short_name).FirstOrDefault() + "</span>";
                        }
                    }
                    List<BudgetProposalPlanFocusAreaHelper> planproposalFocusArea = await _budgetProposal.GetPlanBudgetProposalFocusAreaDataAsync(userName, budgetYear);
                    foreach (var fa in focusAreaListFromData.Distinct())
                    {
                        string finFocusArea = fa;
                        if (planproposalFocusArea.FirstOrDefault(x => x.PlanFocusAreaId.ToString() == fa) != null)
                        {
                            finFocusArea = planproposalFocusArea.FirstOrDefault(x => x.PlanFocusAreaId.ToString() == fa).FinPlanFocusAreaId.ToString();
                        }
                        if (!string.IsNullOrEmpty(finFocusArea))
                        {
                            faDef = faDef + "<span class=\"bp-red-tag\">" + focusAreaInfo.Where(x => x.pk_id.ToString() == finFocusArea).Select(x => x.focusarea_description).FirstOrDefault() + "</span>";
                        }
                    }
                    foreach (var item in unTargetFromData)
                    {
                        unTarget = unTarget + "<span class='bp-blue-tag cursor'>" + unsdTargetInfo.Where(x => x.pk_target_id == item).Select(x => x.target_name).FirstOrDefault() + "</span>";
                    }
                }
            }
            retData.unsGoalsDescription = unsDef;
            retData.focusAreaDescription = faDef;
            retData.unTargetsLinked = unTarget;
            return retData;
        }

        public string AddUpdateStrategyText(string userName, string orgId, int orgLevel, string serviceId, int budgetYear, BudPropStraTxtSave data)
        {
            return AddUpdateStrategyTextAsync(userName, orgId, orgLevel, serviceId, budgetYear, data).GetAwaiter().GetResult();
        }

        public async Task<string> AddUpdateStrategyTextAsync(string userName, string orgId, int orgLevel, string serviceId, int budgetYear, BudPropStraTxtSave data)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            int strategyId = 0;
            serviceId = string.IsNullOrEmpty(serviceId) ? string.Empty : serviceId;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, _utility.GetForecastPeriod(budgetYear, 1));
            List<Guid> targetIdsForSelectedTargets = new List<Guid>();
            List<Guid> goalIdsForSelectedTargets = new List<Guid>();
            List<KeyValue> goalKeyValData = new List<KeyValue>();
            Guid strategyDescHistoryId = string.IsNullOrEmpty(data.strategyDescHistoryId) ? Guid.NewGuid() : Guid.Parse(data.strategyDescHistoryId);
            var cls = await _bpUtility.GetOrgIdOrgLevelServiceIdServiceLevelAsync(userName, orgId, orgLevel, serviceId, budgetYear);

            if (orgLevel == 1)
            {
                orgId = orgVersionContent.lstOrgHierarchy.Select(y => y.org_id_1).FirstOrDefault();
            }

            //Add New
            if (string.IsNullOrEmpty(data.id))
            {
                tfp_strategy_text tstNew = new tfp_strategy_text();
                tstNew.fk_tenant_id = userDetails.tenant_id;
                tstNew.budget_year = budgetYear;
                tstNew.strategy_name = data.strategy;
                tstNew.strategy_desc = data.strategyDesc;
                Guid id = Guid.NewGuid();
                tstNew.strategy_desc_history = id;
                tstNew.org_id = orgId;
                tstNew.org_level = orgLevel;
                tstNew.service_id = serviceId;
                tstNew.service_level = cls.serviceLevel;
                tstNew.updated = DateTime.UtcNow;
                tstNew.updated_by = userDetails.pk_id;
                tstNew.strategy_desc_history = strategyDescHistoryId;
                tstNew.is_busplan = false;
                //insert new tags and get the list to tag ids
                List<string> lstTags = null;
                if (data.tagsData != null && data.tagsData.Any())
                {
                    lstTags = await _consAdjBudget.InsertActionTagsAsync(userName, data.tagsData);
                }
                tstNew.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                await dbContext.tfp_strategy_text.AddAsync(tstNew);
                await dbContext.SaveChangesAsync();
                strategyId = tstNew.pk_strategy_id;

                //Update targets
                if (data.targetTags != null && data.targetTags.Count > 0)
                {
                    targetIdsForSelectedTargets = data.targetTags;
                    foreach (var item in data.targetTags)
                    {
                        tfp_strategy_target tsgNew = new tfp_strategy_target();
                        tsgNew.fk_strategy_id = strategyId;
                        tsgNew.fk_tenant_id = userDetails.tenant_id;
                        tsgNew.budget_year = budgetYear;
                        tsgNew.fk_target_id = item;
                        tsgNew.updated = DateTime.UtcNow;
                        tsgNew.updated_by = userDetails.pk_id;
                        await dbContext.tfp_strategy_target.AddAsync(tsgNew);
                    }
                    await dbContext.SaveChangesAsync();
                }

                //Update goals
                //Save linked goals for selected target when user selects only target
                if ((data.goalTags == null || data.goalTags.Count == 0) && targetIdsForSelectedTargets.Count > 0)
                {
                    goalIdsForSelectedTargets = await dbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && targetIdsForSelectedTargets.Contains(x.pk_target_id)).Select(x => x.fk_goal_id).Distinct().ToListAsync();
                    foreach (var g in goalIdsForSelectedTargets)
                    {
                        KeyValue keyval = new KeyValue();
                        keyval.Key = g.ToString();
                        goalKeyValData.Add(keyval);
                    }
                    data.goalTags = goalKeyValData;
                }

                if (data.goalTags.Count > 0)
                {
                    foreach (KeyValue kItem in data.goalTags)
                    {
                        tfp_strategy_goal tsgNew = new tfp_strategy_goal();
                        tsgNew.fk_strategy_id = strategyId;
                        tsgNew.fk_tenant_id = userDetails.tenant_id;
                        tsgNew.fk_goal_id = Guid.Parse(kItem.Key);
                        tsgNew.updated = DateTime.UtcNow;
                        tsgNew.updated_by = userDetails.pk_id;
                        await dbContext.tfp_strategy_goal.AddAsync(tsgNew);
                    }
                    await dbContext.SaveChangesAsync();
                }
                if (!string.IsNullOrEmpty(data.strategyDesc))
                {
                    await _utility.SaveTextLogAsync(userName, id, data.strategyDesc, null, "", data.ConnectedUserName);
                }
            }
            else //Update
            {
                strategyId = int.Parse(data.id);
                tfp_strategy_text tst = await dbContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                            && x.pk_strategy_id == strategyId && !x.is_busplan).FirstOrDefaultAsync();
                if (tst != null)
                {
                    tst.strategy_name = data.strategy;
                    tst.strategy_desc = data.strategyDesc;
                    tst.updated = DateTime.UtcNow;
                    tst.updated_by = userDetails.pk_id;
                    tst.strategy_desc_history = strategyDescHistoryId;
                    tst.is_busplan = false;
                    List<string> lstTags = null;
                    if (data.tagsData != null && data.tagsData.Any())
                    {
                        lstTags = await _consAdjBudget.InsertActionTagsAsync(userName, data.tagsData);
                    }
                    tst.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                    await dbContext.SaveChangesAsync();

                    //Get Previously saved text
                    string historyData = await _utility.GetHistoryDataAsync(userName, strategyDescHistoryId);
                    if (!string.IsNullOrEmpty(historyData) || !string.IsNullOrEmpty(data.strategyDesc))
                    {
                        await _utility.SaveTextLogAsync(userName, strategyDescHistoryId, data.strategyDesc, null, "", data.ConnectedUserName);
                    }
                }

                //Save targets
                if (data.targetTags != null && data.targetTags.Count > 0)
                {
                    List<Guid> taggedTargetIds = data.targetTags;
                    targetIdsForSelectedTargets = data.targetTags;

                    List<tfp_strategy_target> tgsDel = await dbContext.tfp_strategy_target.Where(x => x.fk_strategy_id == strategyId && x.fk_tenant_id == userDetails.tenant_id && !taggedTargetIds.Contains(x.fk_target_id)).ToListAsync();
                    if (tgsDel.Count > 0)
                    {
                        dbContext.tfp_strategy_target.RemoveRange(tgsDel);
                    }

                    foreach (var item in data.targetTags)
                    {
                        tfp_strategy_target tsg = await dbContext.tfp_strategy_target.Where(x => x.fk_strategy_id == strategyId && x.fk_target_id == item && x.fk_tenant_id == userDetails.tenant_id).FirstOrDefaultAsync();
                        if (tsg != null)
                        {
                            tsg.updated = DateTime.UtcNow;
                            tsg.updated_by = userDetails.pk_id;
                        }
                        else
                        {
                            tfp_strategy_target tsgNew = new tfp_strategy_target();
                            tsgNew.fk_strategy_id = strategyId;
                            tsgNew.fk_tenant_id = userDetails.tenant_id;
                            tsgNew.budget_year = budgetYear;
                            tsgNew.fk_target_id = item;
                            tsgNew.updated = DateTime.UtcNow;
                            tsgNew.updated_by = userDetails.pk_id;
                            await dbContext.tfp_strategy_target.AddAsync(tsgNew);
                        }
                    }
                    await dbContext.SaveChangesAsync();
                }
                else
                {
                    //Delete existing targets tagged
                    List<tfp_strategy_target> targetsExisting = await dbContext.tfp_strategy_target.Where(x => x.fk_strategy_id == strategyId
                                                                                              && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                    if (targetsExisting != null && targetsExisting.Count > 0)
                    {
                        dbContext.tfp_strategy_target.RemoveRange(targetsExisting);
                        await dbContext.SaveChangesAsync();
                    }
                }

                //Save goals
                //Save linked goals for selected target when user selects only target
                if ((data.goalTags == null || data.goalTags.Count == 0) && targetIdsForSelectedTargets.Count > 0)
                {
                    goalIdsForSelectedTargets = await dbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && targetIdsForSelectedTargets.Contains(x.pk_target_id)).Select(x => x.fk_goal_id).Distinct().ToListAsync();
                    foreach (var g in goalIdsForSelectedTargets)
                    {
                        KeyValue keyval = new KeyValue();
                        keyval.Key = g.ToString();
                        goalKeyValData.Add(keyval);
                    }
                    data.goalTags = goalKeyValData;
                }

                if (data.goalTags.Count > 0)
                {
                    List<string> taggedGoalIds = data.goalTags.Select(x => x.Key).ToList();
                    List<tfp_strategy_goal> tgsDel = await dbContext.tfp_strategy_goal.Where(x => x.fk_strategy_id == strategyId && x.fk_tenant_id == userDetails.tenant_id && !taggedGoalIds.Contains(x.fk_goal_id.ToString())).ToListAsync();
                    if (tgsDel.Count > 0)
                    {
                        dbContext.tfp_strategy_goal.RemoveRange(tgsDel);
                    }

                    foreach (KeyValue kItem in data.goalTags)
                    {
                        tfp_strategy_goal tsg = await dbContext.tfp_strategy_goal.Where(x => x.fk_strategy_id == strategyId && x.fk_goal_id.ToString() == kItem.Key && x.fk_tenant_id == userDetails.tenant_id).FirstOrDefaultAsync();
                        if (tsg != null)
                        {
                            tsg.updated = DateTime.UtcNow;
                            tsg.updated_by = userDetails.pk_id;
                        }
                        else
                        {
                            tfp_strategy_goal tsgNew = new tfp_strategy_goal();
                            tsgNew.fk_strategy_id = strategyId;
                            tsgNew.fk_tenant_id = userDetails.tenant_id;
                            tsgNew.fk_goal_id = Guid.Parse(kItem.Key);
                            tsgNew.updated = DateTime.UtcNow;
                            tsgNew.updated_by = userDetails.pk_id;
                            await dbContext.tfp_strategy_goal.AddAsync(tsgNew);
                        }
                    }
                    await dbContext.SaveChangesAsync();
                }
                else
                {
                    //Delete existing goals tagged
                    List<tfp_strategy_goal> tgsExisting = await dbContext.tfp_strategy_goal.Where(x => x.fk_strategy_id == strategyId
                                                                                              && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                    if (tgsExisting.Count > 0)
                    {
                        dbContext.tfp_strategy_goal.RemoveRange(tgsExisting);
                        await dbContext.SaveChangesAsync();
                    }
                }
                if (tst != null)
                {
                    strategyId = tst.master_strategy_id ?? strategyId;
                }
            }

            //Distribute the strategy to the orgs in the org-distribution/selection tree
            if (data.orgList != null && data.orgList.Count > 0)
            {
                List<OrgTreeItemGoalTgtNew> orgItems = data.orgList.ToObject<List<OrgTreeItemGoalTgtNew>>();
                await _bpUtility.DistributeStrategyOrgListAsync(userName, budgetYear, strategyId, orgItems, data);
            }

            return strategyId != 0 ? strategyId.ToString() : string.Empty;
        }

        public string DeleteStrategyGoalinks(string userName, int strategyId)
        {
            return DeleteStrategyGoalinksAsync(userName, strategyId).GetAwaiter().GetResult();
        }

        public async Task<string> DeleteStrategyGoalinksAsync(string userName, int strategyId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();

            List<tfp_strategy_goal> tsgLinks = await dbContext.tfp_strategy_goal.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_strategy_id == strategyId).ToListAsync();

            int delGoaLinkCount = tsgLinks.Count;
            if (delGoaLinkCount > 0)
            {
                dbContext.tfp_strategy_goal.RemoveRange(tsgLinks);
            }

            //delete targets linked to strategy
            List<tfp_strategy_target> targetLinks = await dbContext.tfp_strategy_target.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_strategy_id == strategyId).ToListAsync();
            int delTargetLinkCount = targetLinks.Count;
            if (delTargetLinkCount > 0)
            {
                dbContext.tfp_strategy_target.RemoveRange(targetLinks);
            }
            //Updating the mapping table for only master strategies coming from plan
            tfp_strategy_text tstRec = await dbContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_strategy_id == strategyId).FirstOrDefaultAsync();
            if (tstRec != null && tstRec.master_strategy_id == null)
            {
                tpl_tfp_strategy_mapping data = await dbContext.tpl_tfp_strategy_mapping.FirstOrDefaultAsync(x => x.fk_strategy_id == strategyId);
                if (data != null)
                {
                    data.is_transferred_tofinplan = false;
                }
            }
            if (tstRec != null)
            {
                dbContext.tfp_strategy_text.Remove(tstRec);
            }
            //list of delegated strategies
            List<tfp_strategy_text> delStrategy = await dbContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.master_strategy_id == strategyId).ToListAsync();
            foreach (tfp_strategy_text strategy in delStrategy)
            {
                //delete goal linked to delegated strategy
                tsgLinks = await dbContext.tfp_strategy_goal.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_strategy_id == strategy.pk_strategy_id).ToListAsync();

                if (tsgLinks.Count > 0)
                {
                    dbContext.tfp_strategy_goal.RemoveRange(tsgLinks);
                }

                //delete targets linked to delegated strategy
                targetLinks = await dbContext.tfp_strategy_target.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_strategy_id == strategy.pk_strategy_id).ToListAsync();

                if (targetLinks.Count > 0)
                {
                    dbContext.tfp_strategy_target.RemoveRange(targetLinks);
                }
            }
            if (delStrategy.Count > 0)
            {
                dbContext.tfp_strategy_text.RemoveRange(delStrategy);
            }

            await dbContext.SaveChangesAsync();

            return delGoaLinkCount.ToString();
        }

        public List<KeyValueHelper> GetGoalStrategyData(string userName, string orgId, int orgLevel, string serviceId, int budgetYear, string goalId)
        {
            return GetGoalStrategyDataAsync(userName, orgId, orgLevel, serviceId, budgetYear, goalId).GetAwaiter().GetResult();
        }

        public async Task<List<KeyValueHelper>> GetGoalStrategyDataAsync(string userName, string orgId, int orgLevel, string serviceId, int budgetYear, string goalId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dBContext = await _utility.GetTenantDBContextAsync();
            Guid.TryParse(goalId, out var goalGuid);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, _utility.GetForecastPeriod(budgetYear, 1));

            if (orgLevel == 1)
            {
                orgId = orgVersionContent.lstOrgHierarchy.Select(y => y.org_id_1).FirstOrDefault();
            }

            List<KeyValueHelper> goalStrategyData = string.IsNullOrEmpty(serviceId) || serviceId.ToUpper() == "ALL" ?
                                                    await (from tst in dBContext.tfp_strategy_text
                                                           where tst.fk_tenant_id == userDetails.tenant_id
                                                               && tst.budget_year == budgetYear
                                                               && tst.org_id == orgId
                                                               && tst.org_level == orgLevel
                                                           select new KeyValueHelper
                                                           {
                                                               KeyId = tst.pk_strategy_id,
                                                               KeyString = tst.strategy_name,
                                                               ValueString = tst.strategy_desc
                                                           }).ToListAsync() :
                                                    await (from tst in dBContext.tfp_strategy_text
                                                           where tst.fk_tenant_id == userDetails.tenant_id
                                                               && tst.budget_year == budgetYear
                                                               && tst.org_id == orgId
                                                               && tst.org_level == orgLevel
                                                               && tst.service_id == serviceId
                                                           select new KeyValueHelper
                                                           {
                                                               KeyId = tst.pk_strategy_id,
                                                               KeyString = tst.strategy_name,
                                                               ValueString = tst.strategy_desc
                                                           }).ToListAsync();

            //96671
            var(multiSelectData, active) = await _utility.GetParameterValueAndActiveStatusAsync(userName, "IS_MULTI_SELECT_GOALS");
            if (!(multiSelectData != null && active == 1 && multiSelectData.ToLower() == "true"))
            {
                var goalStratList = await dBContext.tfp_strategy_goal.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_goal_id != goalGuid).Select(s => s.fk_strategy_id).ToListAsync();
                var targetStratList = await (from tgt in dBContext.tco_targets
                                             join tst in dBContext.tfp_strategy_target on tgt.pk_target_id equals tst.fk_target_id
                                             where tgt.fk_goal_id != goalGuid
                                             select tst.fk_strategy_id).ToListAsync();

                goalStrategyData = goalStrategyData.Where(x => !goalStratList.Contains(x.KeyId) && !targetStratList.Contains(x.KeyId)).ToList();
            }

            return goalStrategyData;
        }

        public List<KeyValueHelper> GetGoalsPerStrategyId(string userName, int strategyId)
        {
            return GetGoalsPerStrategyIdAsync(userName, strategyId).GetAwaiter().GetResult();
        }

        public async Task<List<KeyValueHelper>> GetGoalsPerStrategyIdAsync(string userName, int strategyId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dBContext = await _utility.GetTenantDBContextAsync();

            List<KeyValueHelper> goalsLinkedData = await (from tst in dBContext.tfp_strategy_goal
                                                          join tcg in dBContext.tco_goals on new { a = tst.fk_tenant_id, b = tst.fk_goal_id }
                                                                                      equals new { a = tcg.fk_tenant_id, b = tcg.pk_goal_id }
                                                          where tst.fk_tenant_id == userDetails.tenant_id
                                                             && tst.fk_strategy_id == strategyId
                                                          select new KeyValueHelper
                                                          {
                                                              KeyString = tst.fk_goal_id.ToString(),
                                                              ValueString = tcg.goal_name
                                                          }).ToListAsync();

            return goalsLinkedData;
        }

        public dynamic GetBPStrategyHierarchy(string userId, int budgetYear, string id, int orgLevel, string orgId, bool isAngular = false)
        {
            return GetBPStrategyHierarchyAsync(userId, budgetYear, id, orgLevel, orgId, string.Empty, isAngular).GetAwaiter().GetResult();
        }

        public async Task<dynamic> GetBPStrategyHierarchyAsync(string userId, int budgetYear, string id, int orgLevel, string orgId, string chapterId = "", bool isAngular = false)
        {
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            dynamic hierarchyData = new JArray();
            int strategyId = Int32.Parse(id);
            var fpLevel1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            var fpLevel2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            int fLv2OrgLvl = 0;

            if (!string.IsNullOrEmpty(fpLevel2))
            {
                fLv2OrgLvl = int.Parse(fpLevel2.Substring(fpLevel2.Length - 1, 1));
            }
            if ((!string.IsNullOrEmpty(fpLevel1) && fpLevel1.Substring(0, 3) != "ser") && (string.IsNullOrEmpty(fpLevel2) || (!string.IsNullOrEmpty(fpLevel2))))
            {
                List<clsOrgIdsAndServiceIds> lstResult = await _consAdjBudget.GetOrgIdsAndServiceIdsAsync(userId, false, budgetYear, true);
                var orgIds = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).ToList();
                int level2 = (!string.IsNullOrEmpty(fpLevel2)) ? int.Parse(fpLevel2.Substring(fpLevel2.Length - 1)) : 0;
                int level1 = int.Parse(fpLevel1.Substring(fpLevel1.Length - 1));
                if (level2 != 0 && orgLevel >= level2 && !string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) != "ser")
                    return hierarchyData;
                else if (orgLevel == 1)
                {
                    var cityLevel = await tenantDBContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefaultAsync();

                    var itemlvl0 = new JObject();
                    itemlvl0.Add("id", cityLevel.org_id_1);
                    itemlvl0.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                    itemlvl0.Add("expanded", false);
                    itemlvl0.Add("checked", true);
                    itemlvl0.Add("fpLevel", 0);
                    itemlvl0.Add("isUseInDelegSave", false);
                    itemlvl0.Add("parentId", null);
                    itemlvl0.Add("uniq_id", cityLevel.org_id_1);
                    itemlvl0.Add("isNodeDisabled", false);

                    if (string.IsNullOrEmpty(id) || id == "0")
                    {
                        if (orgIds.Count > 0)
                        {
                            dynamic tenantLevelData = new JArray();
                            List<string> filteredList = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Distinct().ToList();
                            foreach (var fp1item in filteredList)
                            {
                                var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                                var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                var itemlvl1 = new JObject();
                                itemlvl1.Add("id", level1Detail.orgId);
                                itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                itemlvl1.Add("expanded", false);
                                itemlvl1.Add("checked", false);
                                itemlvl1.Add("fpLevel", 1);
                                itemlvl1.Add("isUseInDelegSave", !secondlevelItems.Any());
                                itemlvl1.Add("parentId", cityLevel.org_id_1);
                                itemlvl1.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId);
                                itemlvl1.Add("isNodeDisabled", false);

                                if (secondlevelItems.Count > 0)
                                {
                                    dynamic secondLevelData = new JArray();
                                    List<string> secondLevelItemsList = new List<string>();
                                    var itemlvl2 = new JObject();
                                    if ((string.IsNullOrEmpty(fpLevel2)) || (!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) != "ser"))
                                    {
                                        itemlvl2.Add("id", level1Detail.orgId);
                                        itemlvl2.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                        itemlvl2.Add("expanded", false);
                                        itemlvl2.Add("checked", false);
                                        itemlvl2.Add("fpLevel", 1);
                                        itemlvl2.Add("isUseInDelegSave", true);
                                        itemlvl2.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                        itemlvl2.Add("parentId", cityLevel.org_id_1);
                                        itemlvl2.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId + "_" + level1Detail.orgId);
                                        itemlvl2.Add("items", new JArray());
                                        itemlvl2.Add("isNodeDisabled", false);
                                        secondLevelData.Add(itemlvl2);
                                    }

                                    foreach (var fp2items in secondlevelItems)
                                    {
                                        if ((fpLevel1.Substring(0, 3) == "org" && fpLevel2.Substring(0, 3) == "ser"))
                                        {
                                            if (!secondLevelItemsList.Contains(fp2items.orgId))
                                            {
                                                itemlvl2 = new JObject();
                                                itemlvl2.Add("id", fp2items.orgId);
                                                itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                itemlvl2.Add("expanded", false);
                                                itemlvl2.Add("checked", false);
                                                itemlvl2.Add("fpLevel", 2);
                                                itemlvl2.Add("isUseInDelegSave", true);
                                                itemlvl2.Add("parentId", fp1item);
                                                itemlvl2.Add("items", new JArray());
                                                itemlvl2.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId + "_" + fp2items.orgId);
                                                itemlvl2.Add("isNodeDisabled", false);
                                                secondLevelData.Add(itemlvl2);
                                                secondLevelItemsList.Add(fp2items.orgId);
                                            }
                                        }
                                        else
                                        {
                                            itemlvl2 = new JObject();
                                            itemlvl2.Add("id", fp2items.orgId);
                                            itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                            itemlvl2.Add("expanded", false);
                                            itemlvl2.Add("checked", false);
                                            itemlvl2.Add("fpLevel", 2);
                                            itemlvl1.Add("isUseInDelegSave", true);
                                            itemlvl2.Add("parentId", fp1item);
                                            itemlvl2.Add("items", new JArray());
                                            itemlvl2.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId + "_" + fp2items.orgId);
                                            itemlvl2.Add("isNodeDisabled", false);
                                            secondLevelData.Add(itemlvl2);
                                        }
                                    }
                                    itemlvl1.Add("items", secondLevelData);
                                }
                                else
                                {
                                    itemlvl1.Add("items", new JArray());
                                }
                                if (fpLevel1 == "org_id_1")
                                {
                                    hierarchyData.Add(itemlvl1);
                                }
                                else
                                {
                                    tenantLevelData.Add(itemlvl1);
                                }
                            }
                            itemlvl0.Add("items", tenantLevelData);
                        }
                        if (fpLevel1 != "org_id_1")
                        {
                            hierarchyData.Add(itemlvl0);
                        }
                    }
                    else
                    {
                        var masterStrategyDetails = await tenantDBContext.tfp_strategy_text.Where(x => x.pk_strategy_id == (!string.IsNullOrEmpty(id.Trim()) ? int.Parse(id) : 0)).Select(x => x.master_strategy_id).FirstAsync();
                        int masterStratId = masterStrategyDetails == null ? !string.IsNullOrEmpty(id.Trim()) ? int.Parse(id) : 0 : masterStrategyDetails.Value;
                        var masterStrategyDataObj = await tenantDBContext.tfp_strategy_text.Where(x => x.pk_strategy_id == masterStratId).ToListAsync();
                        var strategyDetails = await (from p in tenantDBContext.tfp_strategy_text
                                                        where p.fk_tenant_id == userDetails.tenant_id && p.master_strategy_id == masterStratId && !p.is_busplan
                                                        select new { p.org_id, p.service_id }).Distinct().ToListAsync();
                        if (orgIds.Count > 0)
                        {
                            dynamic tenantLevelData = new JArray();
                            var itemlvl0_1 = new JObject();
                            itemlvl0_1.Add("id", cityLevel.org_id_1);
                            itemlvl0_1.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                            itemlvl0_1.Add("expanded", false);
                            itemlvl0_1.Add("checked", true);
                            itemlvl0_1.Add("fpLevel", 0);
                            itemlvl0_1.Add("isUseInDelegSave", false);
                            itemlvl0_1.Add("parentId", cityLevel.org_id_1);
                            itemlvl0_1.Add("uniq_id", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
                            itemlvl0_1.Add("isNodeDisabled", true);
                            tenantLevelData.Add(itemlvl0_1);
                            itemlvl0.Add("unchecked", orgIds.Any());
                            List<string> level1Ids = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Distinct().ToList();
                            foreach (string fp1item in level1Ids)
                            {
                                var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                                var itemlvl1 = new JObject();
                                itemlvl1.Add("id", level1Detail.orgId);
                                itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                itemlvl1.Add("expanded", false);
                                itemlvl1.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null);
                                itemlvl1.Add("fpLevel", 1);
                                itemlvl1.Add("isUseInDelegSave", !secondlevelItems.Any());
                                itemlvl1.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId);
                                itemlvl1.Add("parentId", cityLevel.org_id_1);
                                itemlvl1.Add("isNodeDisabled", false);
                                itemlvl1.Add("unchecked", true);

                                //var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                if (secondlevelItems.Count > 0)
                                {
                                    List<string> secondLevelItemsList = new List<string>();
                                    dynamic secondLevelData = new JArray();

                                    var itemlvl2 = new JObject();
                                    itemlvl2.Add("id", level1Detail.orgId);
                                    itemlvl2.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                    itemlvl2.Add("expanded", false);
                                    itemlvl2.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null || fpLevel1 == "org_id_1");
                                    itemlvl2.Add("fpLevel", 1);
                                    itemlvl2.Add("isUseInDelegSave", fpLevel1 != "org_id_1");
                                    itemlvl2.Add("parentId", cityLevel.org_id_1);
                                    itemlvl2.Add("items", new JArray());
                                    itemlvl2.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId + "_" + level1Detail.orgId);
                                    itemlvl2.Add("isNodeDisabled", fpLevel1 == "org_id_1");
                                    itemlvl2.Add("unchecked", secondlevelItems.Any());
                                    secondLevelData.Add(itemlvl2);

                                    foreach (var fp2items in secondlevelItems)
                                    {
                                        if ((fpLevel1.Substring(0, 3) == "org" && fpLevel2.Substring(0, 3) == "ser"))
                                        {
                                            if (!secondLevelItemsList.Contains(fp2items.orgId))
                                            {
                                                itemlvl2 = new JObject();
                                                itemlvl2.Add("id", fp2items.orgId);
                                                itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                itemlvl2.Add("expanded", false);
                                                if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                {
                                                    itemlvl2.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null);
                                                }
                                                else
                                                {
                                                    itemlvl2.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) != null || masterStrategyDataObj.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) != null);
                                                }
                                                itemlvl2.Add("fpLevel", 2);
                                                itemlvl2.Add("isUseInDelegSave", true);
                                                itemlvl2.Add("parentId", fp1item);
                                                itemlvl2.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId + "_" + fp2items.orgId);
                                                itemlvl2.Add("isNodeDisabled", masterStrategyDataObj.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) != null?true:false);
                                                itemlvl2.Add("items", new JArray());
                                                itemlvl2.Add("unchecked", false);
                                                secondLevelData.Add(itemlvl2);
                                                secondLevelItemsList.Add(fp2items.orgId);
                                            }
                                        }
                                        else
                                        {
                                            itemlvl2 = new JObject();
                                            itemlvl2.Add("id", fp2items.orgId);
                                            itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                            itemlvl2.Add("expanded", false);
                                            if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                            {
                                                itemlvl2.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null);
                                            }
                                            else
                                            {
                                                itemlvl2.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) != null);
                                            }
                                            itemlvl2.Add("fpLevel", 2);
                                            itemlvl2.Add("isUseInDelegSave", true);
                                            itemlvl2.Add("parentId", fp1item);
                                            itemlvl2.Add("uniq_id", cityLevel.org_id_1 + "_" + level1Detail.orgId + "_" + fp2items.orgId);
                                            itemlvl2.Add("isNodeDisabled", false);
                                            itemlvl2.Add("items", new JArray());
                                            itemlvl2.Add("unchecked", false);
                                            secondLevelData.Add(itemlvl2);
                                        }
                                    }
                                    itemlvl1.Add("items", secondLevelData);
                                }
                                else
                                {
                                    itemlvl1.Add("items", new JArray());
                                }
                                if (fpLevel1 == "org_id_1")
                                {
                                    hierarchyData.Add(itemlvl1);
                                }
                                else
                                {
                                    tenantLevelData.Add(itemlvl1);
                                }
                            }
                            itemlvl0.Add("items", tenantLevelData);
                        }
                        if (fpLevel1 != "org_id_1")
                        {
                            hierarchyData.Add(itemlvl0);
                        }
                    }
                }
                else if (orgLevel == level1)
                {
                    dynamic tenantLevelData = new JArray();
                    if (string.IsNullOrEmpty(id) || id == "0")
                    {
                        var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
                        var itemlvl0 = new JObject();
                        itemlvl0.Add("id", level1Detail.orgId);
                        itemlvl0.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                        itemlvl0.Add("expanded", false);
                        itemlvl0.Add("checked", true);
                        itemlvl0.Add("fpLevel", 0);
                        itemlvl0.Add("isUseInDelegSave", false);
                        itemlvl0.Add("parentId", string.Empty);
                        itemlvl0.Add("uniq_id", level1Detail.orgId);
                        itemlvl0.Add("isNodeDisabled", true);

                        if (orgIds.Count > 0)
                        {
                            var secondlevelItems = lstResult.Where(x => x.parentId == orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                            if (secondlevelItems.Count > 0)
                            {
                                dynamic secondLevelData = new JArray();
                                List<string> secondLevelItemsList = new List<string>();

                                var itemlvl1 = new JObject();
                                if ((string.IsNullOrEmpty(fpLevel2)) || (!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) != "ser"))
                                {
                                    itemlvl1.Add("id", level1Detail.orgId);
                                    itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                    itemlvl1.Add("expanded", false);
                                    itemlvl1.Add("checked", true);
                                    itemlvl1.Add("fpLevel", 0);
                                    itemlvl1.Add("isUseInDelegSave", true);
                                    itemlvl1.Add("parentId", string.Empty);
                                    itemlvl1.Add("unchecked", secondlevelItems.Any());
                                    itemlvl1.Add("uniq_id", level1Detail.orgId + "_" + level1Detail.orgId);
                                    itemlvl1.Add("isNodeDisabled", true);
                                    // this to avoid deleting of deligated target for two level tree
                                    itemlvl1.Add("items", new JArray());
                                    secondLevelData.Add(itemlvl1);
                                }

                                foreach (var fp2items in secondlevelItems)
                                {
                                    if ((fpLevel1.Substring(0, 3) == "org" && fpLevel2.Substring(0, 3) == "ser"))
                                    {
                                        if (!secondLevelItemsList.Contains(fp2items.orgId))
                                        {
                                            itemlvl1 = new JObject();
                                            itemlvl1.Add("id", fp2items.orgId);
                                            itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                            itemlvl1.Add("expanded", false);
                                            itemlvl1.Add("checked", false);
                                            itemlvl1.Add("fpLevel", 2);
                                            itemlvl1.Add("isUseInDelegSave", true);
                                            itemlvl1.Add("parentId", orgId);
                                            itemlvl1.Add("uniq_id", level1Detail.orgId + "_" + fp2items.orgId);
                                            itemlvl1.Add("isNodeDisabled", false);
                                            itemlvl1.Add("items", new JArray());
                                            secondLevelData.Add(itemlvl1);
                                            secondLevelItemsList.Add(fp2items.orgId);
                                        }
                                    }
                                    else
                                    {
                                        itemlvl1 = new JObject();
                                        itemlvl1.Add("id", fp2items.orgId);
                                        itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                        itemlvl1.Add("expanded", false);
                                        itemlvl1.Add("checked", false);
                                        itemlvl1.Add("fpLevel", 2);
                                        itemlvl1.Add("isUseInDelegSave", true);
                                        itemlvl1.Add("parentId", orgId);
                                        itemlvl1.Add("uniq_id", level1Detail.orgId + "_" + fp2items.orgId);
                                        itemlvl1.Add("isNodeDisabled", false);
                                        itemlvl1.Add("items", new JArray());
                                        secondLevelData.Add(itemlvl1);
                                    }
                                }
                                itemlvl0.Add("items", secondLevelData);
                            }
                            else
                            {
                                itemlvl0.Add("items", new JArray());
                            }
                            if (fpLevel1 == "org_id_1")
                            {
                                hierarchyData.Add(itemlvl0);
                            }
                            else
                            {
                                tenantLevelData.Add(itemlvl0);
                            }
                        }
                        if (fpLevel1 != "org_id_1")
                        {
                            hierarchyData.Add(itemlvl0);
                        }
                    }
                    else
                    {
                        int stratId = !string.IsNullOrEmpty(id.Trim()) ? int.Parse(id) : 0;
                        var masterId = await tenantDBContext.tfp_strategy_text.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_strategy_id == stratId && !x.is_busplan);
                        int masterStratId = masterId != null ? (masterId.master_strategy_id ?? stratId) : 0;
                        var strategyDetails = await (from p in tenantDBContext.tfp_strategy_text
                                                        where p.fk_tenant_id == userDetails.tenant_id && !p.is_busplan && p.master_strategy_id == masterStratId || p.pk_strategy_id == masterStratId
                                                        select new { p.org_id, p.service_id, p.org_level }).Distinct().ToListAsync();

                        var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
                        var secondlevelItems = lstResult.Where(x => x.parentId == orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();

                        var itemlvl0 = new JObject();
                        itemlvl0.Add("id", level1Detail.orgId);
                        itemlvl0.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                        itemlvl0.Add("expanded", false);
                        itemlvl0.Add("checked", true);
                        itemlvl0.Add("unchecked", secondlevelItems.Any());
                        itemlvl0.Add("fpLevel", 1);
                        itemlvl0.Add("isUseInDelegSave", false);
                        itemlvl0.Add("uniq_id", level1Detail.orgId);
                        itemlvl0.Add("parentId", string.Empty);
                        itemlvl0.Add("isNodeDisabled", false);

                        if (orgIds.Any())
                        {
                            tenantLevelData = new JArray();
                            if (secondlevelItems.Count > 0)
                            {
                                List<string> secondLevelItemsList = new List<string>();

                                var itemlvl1 = new JObject();
                                itemlvl1.Add("id", level1Detail.orgId);
                                itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                itemlvl1.Add("expanded", false);
                                itemlvl1.Add("checked", true);
                                itemlvl1.Add("fpLevel", 1);
                                itemlvl1.Add("isUseInDelegSave", false);
                                itemlvl1.Add("uniq_id", level1Detail.orgId + "_" + level1Detail.orgId);
                                itemlvl1.Add("parentId", string.Empty);
                                itemlvl1.Add("isNodeDisabled", true);
                                dynamic secondLevelData = new JArray();
                                secondLevelData.Add(itemlvl1);
                                foreach (var fp2items in secondlevelItems)
                                {
                                    if ((fpLevel1.Substring(0, 3) == "org" && fpLevel2.Substring(0, 3) == "ser"))
                                    {
                                        if (!secondLevelItemsList.Contains(fp2items.orgId))
                                        {
                                            itemlvl1 = new JObject();
                                            itemlvl1.Add("id", fp2items.orgId);
                                            itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                            itemlvl1.Add("expanded", false);
                                            if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                            {
                                                itemlvl1.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null);
                                            }
                                            else
                                            {
                                                itemlvl1.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == orgId && x.service_id == fp2items.orgId) != null);
                                            }
                                            itemlvl1.Add("fpLevel", 2);
                                            itemlvl1.Add("isUseInDelegSave", true);
                                            itemlvl1.Add("parentId", orgId);
                                            itemlvl1.Add("unchecked", false);
                                            itemlvl1.Add("uniq_id", level1Detail.orgId + "_" + fp2items.orgId);
                                            itemlvl1.Add("isNodeDisabled", false);
                                            itemlvl1.Add("items", new JArray());
                                            secondLevelData.Add(itemlvl1);
                                            secondLevelItemsList.Add(fp2items.orgId);
                                        }
                                    }
                                    else
                                    {
                                        itemlvl1 = new JObject();
                                        itemlvl1.Add("id", fp2items.orgId);
                                        itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                        itemlvl1.Add("expanded", false);
                                        if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                        {
                                            itemlvl1.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null);
                                        }
                                        else
                                        {
                                            itemlvl1.Add("checked", masterStratId == 0 || strategyDetails.FirstOrDefault(x => x.org_id == orgId && x.service_id == fp2items.orgId) != null);
                                        }
                                        itemlvl1.Add("fpLevel", 2);
                                        itemlvl1.Add("isUseInDelegSave", true);
                                        itemlvl1.Add("parentId", orgId);
                                        itemlvl1.Add("unchecked", false);
                                        itemlvl1.Add("uniq_id", level1Detail.orgId + "_" + fp2items.orgId);
                                        itemlvl1.Add("isNodeDisabled", false);
                                        itemlvl1.Add("items", new JArray());
                                        secondLevelData.Add(itemlvl1);
                                    }
                                }
                                itemlvl0.Add("items", secondLevelData);
                            }
                            else
                            {
                                itemlvl0["checked"] = strategyDetails.FirstOrDefault(x => x.org_id == orgId) != null;
                                itemlvl0.Add("items", new JArray());
                            }
                            if (fpLevel1 == "org_id_1")
                            {
                                hierarchyData.Add(itemlvl0);
                            }
                            else
                            {
                                tenantLevelData.Add(itemlvl0);
                            }
                        }
                        if (fpLevel1 != "org_id_1")
                        {
                            hierarchyData.Add(itemlvl0);
                        }
                    }
                }
            }
            return hierarchyData;
        }
    }
}