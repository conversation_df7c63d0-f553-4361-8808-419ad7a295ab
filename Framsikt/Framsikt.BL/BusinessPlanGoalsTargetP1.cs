#pragma warning disable CS8629

#pragma warning disable CS8073
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8625

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Framsikt.BL
{
    public partial class BusinessPlanGoalsTarget : IBusinessPlanGoalsTarget
    {
        private async Task<List<BusinessPlanGoalsTgtHelper>> FetchBpGoalsTargetGridData(string userId,
                                                                            int orgLevel,
                                                                            string orgId,
                                                                            string serviceId,
                                                                            int budgetYear,
                                                                            int reportedPeriod,
                                                                            string useUnGoalsForTarget)

        {
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            UserData userdata = await _utility.GetUserDetailsAsync(userId);
            List<string> serviceidwithall = new List<string>();
            List<TmrEffectTargetStatus> tetsList = new List<TmrEffectTargetStatus>();
            Dictionary<string, clsLanguageString> langStringValuesFormat = await _utility.GetLanguageStringsAsync(userdata.language_preference, userdata.user_name, "NumberFormats");
            Dictionary<string, clsLanguageString> langStr = await _utility.GetLanguageStringsAsync(userdata.language_preference, userdata.user_name, "BusinessPlan");
            string fpLevelOne = await _bpUtility.GetFinPlanLevelSetOrTypeAsync(userId, "FINPLAN_LEVEL_1", false);
            int fpLevel1 = !string.IsNullOrEmpty(fpLevelOne) ? int.Parse(fpLevelOne) : 0;

            string fpLevelTwo = await _bpUtility.GetFinPlanLevelSetOrTypeAsync(userId, "FINPLAN_LEVEL_2", false);
            int fpLevel2 = !string.IsNullOrEmpty(fpLevelTwo) ? int.Parse(fpLevelTwo) : 0;
            bool isDataFiltered = false;
            List<tco_org_hierarchy> orgHierachy = orgVersionContent.lstOrgHierarchy;
            var actionTagsAsync = GetKeyValueActionTagsDatas(userdata.tenant_id);
            var indicListAsync = GetTfpEffectTargetDetails(userdata.tenant_id);
            var stratGoalAsync = GetTfpStrategyGoals(userdata.tenant_id);
            var stratTargetAsync = GetTfpStrategyTargets(userdata.tenant_id);
            var stratTextAsync = GetTfpStrategyTexts(userdata.tenant_id);
            var goalDistListAsync = _budPropUtil.FetchGoalDistrPerfAsync(userdata.tenant_id, budgetYear, serviceId);
            var tgtDistListAsync = _budPropUtil.FetchTargetDistrPerfAsync(userdata.tenant_id, budgetYear, serviceId);
            var tfpEffectTargetAsync = GetTcoTargetsList(userdata.tenant_id, budgetYear);
            var tcgGoalsAsync = GetTcoGoalsList(userdata.tenant_id, budgetYear);
            var tcoFocusAreaAsync = GetTcoFocusareas(userdata.tenant_id, budgetYear);
            var plansConnectedToGoalAsync = PlanConnectedToGoal(userdata.tenant_id, budgetYear);
            var plansConnectedToTargetAsync = PlanConnectedToTarget(userdata.tenant_id, budgetYear);
            var tcoIndicatorsAsync = GetTcoIndicators(userdata.tenant_id);
            var statusDataAsync = GetProgressStatusList(userdata.tenant_id);
            var indicatorResultsPrevYearAsync = GetIndicatorResults(userdata.tenant_id, budgetYear - 1, orgId, orgLevel);
            var indicatorResultsPrevtoPrevYearAsync = GetIndicatorResults(userdata.tenant_id, budgetYear - 2, orgId, orgLevel);
            var unsGoalsAsync = _budgetProposal.GetUnSuDevGoalsAsync();
            var tServsAsync = GetTcoServiceValuesList(userdata.tenant_id);
            var unSuDevTargetsAsync = GetGcoUnSusdevTargetsList();

            await Task.WhenAll(actionTagsAsync, indicListAsync, stratGoalAsync, stratTargetAsync, stratTextAsync, goalDistListAsync,
                tgtDistListAsync, tfpEffectTargetAsync, tcgGoalsAsync, tcoFocusAreaAsync, plansConnectedToGoalAsync, plansConnectedToTargetAsync,
                 tcoIndicatorsAsync, statusDataAsync, indicatorResultsPrevYearAsync, indicatorResultsPrevtoPrevYearAsync, unsGoalsAsync, tServsAsync,
                unSuDevTargetsAsync);

            JArray orgStructure = await _utility.GetOrgStructureAsync(orgVersionContent, userId);
            List<ClsOrgStructureContent> lstOrgStructureContent = orgStructure.ToObject<List<ClsOrgStructureContent>>();
            List<ClsOrgStructureFlat> lstResult = _utility.GetFlatOrgStructure(lstOrgStructureContent, new List<ClsOrgStructureFlat>(), null);

            List<string> subLevelOrgIds = new List<string>();
            if (orgLevel == 1)
            {
                lstResult = lstResult.Where(x => x.level1Id == orgId).ToList();
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level2Id).Distinct().ToList());
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level3Id).Distinct().ToList());
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level4Id).Distinct().ToList());
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level5Id).Distinct().ToList());
            }
            else if (orgLevel == 2)
            {
                lstResult = lstResult.Where(x => x.level2Id == orgId).ToList();
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level3Id).Distinct().ToList());
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level4Id).Distinct().ToList());
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level5Id).Distinct().ToList());
            }
            else if (orgLevel == 3)
            {
                lstResult = lstResult.Where(x => x.level3Id == orgId).ToList();
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level4Id).Distinct().ToList());
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level5Id).Distinct().ToList());
            }
            else if (orgLevel == 4)
            {
                lstResult = lstResult.Where(x => x.level4Id == orgId).ToList();
                subLevelOrgIds.AddRange(lstResult.Select(x => x.level5Id).Distinct().ToList());
            }
            subLevelOrgIds.Remove(null);

            List<KeyValueData> actionTags = actionTagsAsync.Result;
            List<tfp_effect_target_detail> indicList = indicListAsync.Result;
            List<tfp_strategy_goal> stratGoal = stratGoalAsync.Result;
            List<tfp_strategy_target> stratTarget = stratTargetAsync.Result;
            List<tfp_strategy_text> stratText = stratTextAsync.Result;
            List<GoalnDistrFth> goalDistList = goalDistListAsync.Result;
            List<TargetnDistrFth> tgtDistList = tgtDistListAsync.Result;
            List<tco_indicator_setup> tcoIndicators = tcoIndicatorsAsync.Result;

            var stratGoalList = (from st in stratText
                                 join sg in stratGoal on st.pk_strategy_id equals sg.fk_strategy_id
                                 select new
                                 {
                                     st.strategy_name,
                                     st.pk_strategy_id,
                                     sg.fk_goal_id
                                 }).ToList();

            var stratTargetList = (from st in stratText
                                   join tst in stratTarget on st.pk_strategy_id equals tst.fk_strategy_id
                                   select new
                                   {
                                       st.pk_strategy_id,
                                       st.strategy_name,
                                       tst.fk_target_id
                                   }).ToList();

            var strategyTargetSet = (from at in tgtDistList
                                     join b in stratTargetList on at.targetId equals b.fk_target_id
                                     select new
                                     {
                                         at.targetId,
                                         b.strategy_name
                                     }).Distinct().ToList();

            var strategyTargetFormattedSet = (from a in strategyTargetSet
                                              group a by a.targetId into g1
                                              select new
                                              {
                                                  targetId = g1.Key,
                                                  strategy_name = string.Join("<br><br>", g1.Select(x => x.strategy_name).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList())
                                              }).ToList();

            List<BusinessPlanGoalsTgtHelper> bpGoalsTargetData = (from at in tgtDistList
                                                                  join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                                                                                      equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                                                                  join d in indicList on new { t = at.targetId, d = at.targetDistrId }
                                                                                  equals new { t = d.fk_target_id, d = d.fk_target_distribution_id } into gj
                                                                  from res in gj.DefaultIfEmpty()
                                                                  join ind in tcoIndicators on res == null ? Guid.Empty : res.fk_indicator_code equals ind.pk_indicator_code into gind
                                                                  from indRes in gind.DefaultIfEmpty()

                                                                  join strategy in strategyTargetFormattedSet on at.targetId equals strategy.targetId into gj1
                                                                  from res1 in gj1.DefaultIfEmpty()

                                                                  select new BusinessPlanGoalsTgtHelper
                                                                  {
                                                                      targetId = at.targetId,
                                                                      targetDistrId = at.targetDistrId,
                                                                      goalId = gb.goalId,
                                                                      goalDistrId = gb.goalDistrId,
                                                                      masterGoalId = gb.goalId != gb.goalDistrId ? gb.goalId : Guid.Empty,
                                                                      masterTargetId = at.targetId != at.targetDistrId ? at.targetId : Guid.Empty,
                                                                      targetDetailId = res == null ? 0 : res.pk_id,
                                                                      bpGoal = gb.goal_name,
                                                                      bpTarget = at.target_name,
                                                                      unGoals = gb.unsd_goals,
                                                                      unTargets = at.unsd_target,
                                                                      unGoalsForTargets = at.unsd_goal,
                                                                      bpIndicator = indRes == null ? string.Empty : indRes.measurment_criteria,
                                                                      year1Result = res == null ? string.Empty : res.year_1_value,
                                                                      numberFormat = indRes == null ? "text" : indRes.value_type,
                                                                      orgId = at.org_id,
                                                                      orgLevel = at.org_level,
                                                                      serviceId = at.service_id,
                                                                      serviceLevel = at.service_level,
                                                                      bpGoalsTags = gb.tags,
                                                                      targetDescription = at.plan_target_text,
                                                                      isReported = res == null ? false : res.is_reported,
                                                                      rptdCheckDisabled = res == null ? true : false,
                                                                      isBusPlanIndicator = res == null ? false : res.is_bplan_indicator,
                                                                      isDelegatedIndicator = res == null ? false : res.is_delegated_indicator,
                                                                      isBusPlanTarget = at.is_busplan_target,
                                                                      isBusPlanGoal = gb.is_busplan_goal,
                                                                      fk_indicator_code = indRes == null ? Guid.Empty : indRes.pk_indicator_code,
                                                                      indicatorDescription = res == null ? string.Empty : res.indicator_strategy,
                                                                      acceptable_from = res == null ? 0 : res.acceptable_from,
                                                                      acceptable_To = res == null ? 0 : res.acceptable_to,
                                                                      wished_value = res == null ? string.Empty : res.wished_value,
                                                                      sort_order = at.sort_order,
                                                                      indicator_sort = res == null ? 0 : res.sort_order,
                                                                      isDelegatedGoal = gb.org_level_created < orgLevel,
                                                                      strategy = res1 == null ? string.Empty : res1.strategy_name,
                                                                      statusGoal = gb.statusGoal,
                                                                      statusGoalId = gb.statusGoalId,
                                                                      statusGoalDesc = gb.statusGoalDescription,
                                                                      statusTarget = at.statusTarget,
                                                                      statusTargetId = at.statusTargetId,
                                                                      statusTargetDesc = at.statusTargetDesc
                                                                  }).ToList();

            List<BusinessPlanGoalsTgtHelper> bpGoalsTargetDataCopy = bpGoalsTargetData;

            List<BusinessPlanGoalsTgtHelper> bpGoalsData = (from gb in goalDistList
                                                            select new BusinessPlanGoalsTgtHelper
                                                            {
                                                                goalId = gb.goalId,
                                                                goalDistrId = gb.goalDistrId,
                                                                masterGoalId = gb.goalId != gb.goalDistrId ? gb.goalId : Guid.Empty,
                                                                bpGoal = gb.goal_name,
                                                                unGoals = gb.unsd_goals,
                                                                orgId = gb.org_id,
                                                                orgLevel = gb.org_level,
                                                                serviceId = gb.service_id,
                                                                serviceLevel = gb.service_level,
                                                                isBusPlanGoal = gb.is_busplan_goal,
                                                                isDelegatedGoal = gb.org_level_created < orgLevel
                                                            }).ToList();

            List<BusinessPlanGoalsTgtHelper> bpGoalSubLevelOrgId;
            List<BusinessPlanGoalsTgtHelper> bpGtDataSubLevelOrgId;
            List<tco_goals> bpGtDataGoals = new List<tco_goals>();
            if (orgLevel > 1)
            {
                if (string.IsNullOrEmpty(serviceId) || (serviceId.ToUpper() != "ALL" && serviceId != "-1" && serviceId != "-2"))
                {
                    if (serviceId != null)
                    {
                        if (serviceId.Trim() == string.Empty)
                        {
                            serviceidwithall.Add(string.Empty);
                            serviceidwithall.Add("-1");
                        }
                        else
                        {
                            serviceidwithall.Add(serviceId);
                        }
                    }
                    if (serviceId == null && orgId != null)
                    {
                        bpGtDataSubLevelOrgId = bpGoalsTargetData.Where(x => subLevelOrgIds.Contains(x.orgId) && (string.IsNullOrEmpty(x.serviceId) || x.serviceId == "-1")).ToList();
                        bpGoalSubLevelOrgId = bpGoalsData.Where(x => subLevelOrgIds.Contains(x.orgId) && (string.IsNullOrEmpty(x.serviceId) || x.serviceId == "-1")).ToList();
                        bpGoalsTargetData = bpGoalsTargetData.Where(x => x.orgId == orgId && x.orgLevel == orgLevel && (string.IsNullOrEmpty(x.serviceId) || x.serviceId == "-1")).ToList();
                        isDataFiltered = true;
                    }
                    else if (orgId == null && serviceId != null)
                    {
                        bpGtDataSubLevelOrgId = bpGoalsTargetData.Where(x => serviceidwithall.Contains(x.serviceId)).ToList();
                        bpGoalSubLevelOrgId = bpGoalsData.Where(x => serviceidwithall.Contains(x.serviceId)).ToList();
                        bpGoalsTargetData = bpGoalsTargetData.Where(x => serviceidwithall.Contains(x.serviceId)).ToList();
                        isDataFiltered = true;
                    }
                    else
                    {
                        bpGtDataSubLevelOrgId = bpGoalsTargetData.Where(x => serviceidwithall.Contains(x.serviceId) && subLevelOrgIds.Contains(x.orgId)).ToList();
                        bpGoalSubLevelOrgId = bpGoalsData.Where(x => serviceidwithall.Contains(x.serviceId) && subLevelOrgIds.Contains(x.orgId)).ToList();
                        bpGoalsTargetData = bpGoalsTargetData.Where(x => serviceidwithall.Contains(x.serviceId) && x.orgId == orgId && x.orgLevel == orgLevel).ToList();
                        isDataFiltered = true;
                    }
                }
                else
                {
                    if (serviceId != null)
                    {
                        if (serviceId.Trim() == string.Empty)
                            serviceidwithall.Add(string.Empty);
                        else if (serviceId == "ALL" || serviceId == "-1")
                        {
                            serviceidwithall.Add("ALL");
                            serviceidwithall.Add("-1");
                        }
                    }

                    bpGtDataSubLevelOrgId = bpGoalsTargetData.Where(x => subLevelOrgIds.Contains(x.orgId)).ToList();
                    bpGoalSubLevelOrgId = bpGoalsData.Where(x => subLevelOrgIds.Contains(x.orgId)).ToList();
                    if (serviceId == "-2")
                        bpGoalsTargetData = bpGoalsTargetData.Where(x => x.orgId == orgId && x.orgLevel == orgLevel).ToList();
                    else
                        bpGoalsTargetData = bpGoalsTargetData.Where(x => x.orgId == orgId && x.orgLevel == orgLevel && serviceidwithall.Contains(x.serviceId)).ToList();
                    isDataFiltered = true;
                }
            }
            else
            {
                if (serviceId != null)
                {
                    if (serviceId.Trim() == string.Empty)
                        serviceidwithall.Add(string.Empty);
                    else if (serviceId == "ALL" || serviceId == "-1")
                    {
                        serviceidwithall.Add("ALL");
                        serviceidwithall.Add("-1");
                    }
                    else
                        serviceidwithall.Add(serviceId);
                }

                if (!string.IsNullOrEmpty(serviceId) && serviceId != "-2")
                {
                    bpGtDataSubLevelOrgId = bpGoalsTargetData.Where(x => subLevelOrgIds.Contains(x.orgId)).ToList();
                    bpGoalSubLevelOrgId = bpGoalsData.Where(x => subLevelOrgIds.Contains(x.orgId)).ToList();
                    bpGoalsTargetData = bpGoalsTargetData.Where(x => x.orgLevel == 1 && serviceidwithall.Contains(x.serviceId)).ToList();
                }
                else
                {
                    bpGtDataSubLevelOrgId = bpGoalsTargetData.Where(x => subLevelOrgIds.Contains(x.orgId)).ToList();
                    bpGoalSubLevelOrgId = bpGoalsData.Where(x => subLevelOrgIds.Contains(x.orgId)).ToList();
                    bpGoalsTargetData = bpGoalsTargetData.Where(x => x.orgLevel == 1).ToList();
                }

                isDataFiltered = true;
            }

            List<Guid> tgtGoalsList = bpGoalsTargetData.Select(x => x.goalId).ToList();
            List<GoalnDistrFth> cityGoalList = await _budPropUtil.ApplyOrgServFltrGoDistrAsync(userId, goalDistList, orgId, orgLevel, serviceId, budgetYear);

            if (serviceId == "-2")
            {
                List<string> tgtGoalServIdList = bpGoalsTargetData.Select(x => x.goalId.ToString() + "_" + x.serviceId).ToList();
                foreach (GoalnDistrFth cg in cityGoalList.OrderBy(x => x.service_id).ToList())
                {
                    string servIdAllOrEmpty = cg.service_id == "" || cg.service_id == "-1" || cg.service_id == "ALL" ? "-1" : cg.service_id;
                    if (!tgtGoalServIdList.Contains(cg.goalId.ToString() + "_" + servIdAllOrEmpty))
                    {
                        bpGoalsTargetData.Add(new BusinessPlanGoalsTgtHelper
                        {
                            targetId = Guid.Empty,
                            targetDistrId = Guid.Empty,
                            goalId = cg.goalId,
                            masterGoalId = cg.goalDistrId != cg.goalId ? cg.goalId : Guid.Empty,
                            masterTargetId = Guid.Empty,
                            targetDetailId = 0,
                            bpGoal = cg.goal_name,
                            bpTarget = string.Empty,
                            unGoals = cg.unsd_goals,
                            unTargets = string.Empty,
                            unGoalsForTargets = string.Empty,
                            bpIndicator = string.Empty,
                            year1Result = string.Empty,
                            numberFormat = string.Empty,
                            orgId = orgId,
                            orgLevel = orgLevel,
                            serviceId = !string.IsNullOrEmpty(cg.service_id) ? cg.service_id : "-1",
                            serviceLevel = cg.service_level,
                            bpGoalsTags = cg.tags,
                            targetDescription = string.Empty,
                            recType = string.Empty,
                            isReported = false,
                            rptdCheckDisabled = true,
                            isBusPlanIndicator = false,
                            isBusPlanTarget = false,
                            isBusPlanGoal = cg.is_busplan_goal,
                            fk_indicator_code = Guid.Empty,
                            indicatorDescription = string.Empty,
                            sort_order = 0,
                            indicator_sort = 0,
                            isDelegatedGoal = cg.org_level_created < orgLevel,
                            strategy = GetGoalStrategy(stratGoalList, cg.goalId),
                            statusGoal = "",
                            statusGoalId = 0,
                            statusGoalDesc = string.Empty,
                            statusTarget = "",
                            statusTargetId = 0,
                            statusTargetDesc = string.Empty
                        });
                    }
                    tgtGoalServIdList.Add(cg.goalId.ToString() + "_" + servIdAllOrEmpty);
                }
            }
            else
            {
                foreach (GoalnDistrFth cg in cityGoalList)
                {
                    if (!tgtGoalsList.Contains(cg.goalId))
                    {
                        bpGoalsTargetData.Add(new BusinessPlanGoalsTgtHelper
                        {
                            targetId = Guid.Empty,
                            targetDistrId = Guid.Empty,
                            goalId = cg.goalId,
                            masterGoalId = cg.goalDistrId != cg.goalId ? cg.goalId : Guid.Empty,
                            masterTargetId = Guid.Empty,
                            targetDetailId = 0,
                            bpGoal = cg.goal_name,
                            bpTarget = string.Empty,
                            unGoals = cg.unsd_goals,
                            unTargets = string.Empty,
                            unGoalsForTargets = string.Empty,
                            bpIndicator = string.Empty,
                            year1Result = string.Empty,
                            numberFormat = string.Empty,
                            orgId = orgId,
                            orgLevel = orgLevel,
                            serviceId = !string.IsNullOrEmpty(cg.service_id) ? cg.service_id : "-1",
                            serviceLevel = cg.service_level,
                            bpGoalsTags = cg.tags,
                            targetDescription = string.Empty,
                            recType = string.Empty,
                            isReported = false,
                            rptdCheckDisabled = true,
                            isBusPlanIndicator = false,
                            isBusPlanTarget = false,
                            isBusPlanGoal = cg.is_busplan_goal,
                            fk_indicator_code = Guid.Empty,
                            indicatorDescription = string.Empty,
                            sort_order = 0,
                            indicator_sort = 0,
                            isDelegatedGoal = cg.org_level_created < orgLevel,
                            strategy = GetGoalStrategy(stratGoalList, cg.goalId),
                            statusGoal = "",
                            statusGoalId = 0,
                            statusGoalDesc = string.Empty,
                            statusTarget = "",
                            statusTargetId = 0,
                            statusTargetDesc = string.Empty
                        });
                    }
                }
            }

            List<Guid> tcGoalsList = bpGoalsTargetData.Select(x => x.goalId).ToList();

            if (!isDataFiltered)
            {
                bpGoalsTargetData.Clear();
            }

            var indicatorResultsPrevYear = indicatorResultsPrevYearAsync.Result;
            var indicatorResultsPrevtoPrevYear = indicatorResultsPrevtoPrevYearAsync.Result;

            List<tmd_indicator_results> indResYr = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? indicatorResultsPrevYear : indicatorResultsPrevYear.Where(x => serviceidwithall.Contains(x.service_id)).ToList();
            List<tmd_indicator_results> indResPrvYr = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? indicatorResultsPrevtoPrevYear : indicatorResultsPrevtoPrevYear.Where(x => serviceidwithall.Contains(x.service_id)).ToList();

            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            if (reportedPeriod != 0)
            {
                tetsList = await tenantDbContext.tmr_effect_target_status.Where(x => x.FkTenantId == userdata.tenant_id && x.ForecastPeriod == reportedPeriod && x.fk_target_distribution_id != null).ToListAsync();
            }

            bpGoalsTargetData.ForEach(x =>
            {
                if (reportedPeriod != 0)
                {
                    var tets = tetsList.FirstOrDefault(y => (x.targetDetailId != 0 && y.FkTargetDetailId == x.targetDetailId));
                    if (tets != null)
                        x.lastReportedMonRes = tets.IndicatorValue != null ? tets.IndicatorValue.Replace(',', '.') : string.Empty;
                    else
                        x.lastReportedMonRes = string.Empty;

                    x.reportedMonDesc = tets != null ? tets.StatusDesc : string.Empty;
                    x.reportedmon_ind_value = tets != null ? tets.IndicatorValue : string.Empty;
                    x.reportedmon_indval_format = tets != null ? tets.ValueType : string.Empty;
                }

                var iry = indResYr.FirstOrDefault(y => y.fk_indicator_id == x.fk_indicator_code);
                x.lastYrReportMonRes = iry != null ? iry.total_year : string.Empty;

                var irpy = indResPrvYr.FirstOrDefault(y => y.fk_indicator_id == x.fk_indicator_code);
                x.prvlastYrRptMonRes = irpy != null ? irpy.total_year : string.Empty;
            });

            List<clsOrgIdsAndServiceIds> orgIdSidList = await _conseqAdjBudget.GetOrgIdsAndServiceIdsAsync(userId, false, budgetYear, true);

            List<tco_targets> tfpEffectTarget = tfpEffectTargetAsync.Result;
            List<tco_goals> tcgGoals = tcgGoalsAsync.Result;
            List<tco_focusarea> tcoFocusArea = tcoFocusAreaAsync.Result;
            var plansConnectedToGoal = plansConnectedToGoalAsync.Result;
            var plansConnectedToTarget = plansConnectedToTargetAsync.Result;
            List<KeyValueHelper> unsGoals = unsGoalsAsync.Result;
            List<tco_service_values> tServs = tServsAsync.Result;
            var unSuDevTargets = unSuDevTargetsAsync.Result;

            List<tco_goals_distribution> tcgdGoals = await tenantDbContext.tco_goals_distribution.Where(x => x.fk_tenant_id == userdata.tenant_id && tcGoalsList.Contains(x.fk_goal_id)).ToListAsync();

            List<int> tagIds;
            List<Guid> targetIdList = new List<Guid>();
            List<string> unGoalIdList = new List<string>();

            string invalidTargetForBudgetYear = "InvalidTarget_BudgetYearMismatch";
            string invalidGoalForBudgetYear = "InvalidGoal_BudgetYearMismatch";
            Guid invalidGoalIdForBudgetYear = Guid.NewGuid();

            List<KeyValueHelper> unsgTargets;

            bpGoalsTargetData = bpGoalsTargetData.OrderBy(x => x.bpGoal).ThenBy(y => y.bpTarget).ThenBy(z => z.sort_order).ThenBy(z => z.indicator_sort).ThenBy(z => z.bpIndicator).ThenBy(z => z.serviceId).ToList();

            bpGoalsTargetData = (from a in bpGoalsTargetData
                                 join b in tfpEffectTarget on new { a = a.targetId } equals new { a = b.pk_target_id }
                                 into grp
                                 select new BusinessPlanGoalsTgtHelper
                                 {
                                     goalId = a.targetId != Guid.Empty ? grp != null ? grp.FirstOrDefault().fk_goal_id : invalidGoalIdForBudgetYear : a.goalId,
                                     bpTarget = a.targetId != Guid.Empty ? grp != null ? grp.FirstOrDefault().target_name : invalidTargetForBudgetYear : string.Empty,
                                     targetId = a.targetId,
                                     targetDistrId = a.targetDistrId,
                                     goalDistrId = a.goalDistrId,
                                     masterGoalId = a.masterGoalId,
                                     masterTargetId = a.masterTargetId,
                                     targetDetailId = a.targetDetailId,
                                     bpGoal = a.bpGoal,
                                     unGoals = a.unGoals,
                                     unTargets = a.unTargets,
                                     unGoalsForTargets = a.unGoalsForTargets,
                                     bpIndicator = a.bpIndicator,
                                     year1Result = a.year1Result,
                                     numberFormat = a.numberFormat,
                                     orgId = a.orgId,
                                     orgLevel = a.orgLevel,
                                     serviceId = a.serviceId,
                                     serviceLevel = a.serviceLevel,
                                     bpGoalsTags = a.bpGoalsTags,
                                     targetDescription = a.targetDescription,
                                     isReported = a.isReported,
                                     rptdCheckDisabled = a.rptdCheckDisabled,
                                     isBusPlanIndicator = a.isBusPlanIndicator,
                                     isDelegatedIndicator = a.isDelegatedIndicator,
                                     isBusPlanTarget = a.isBusPlanTarget,
                                     isBusPlanGoal = a.isBusPlanGoal,
                                     fk_indicator_code = a.fk_indicator_code,
                                     indicatorDescription = a.indicatorDescription,
                                     acceptable_from = a.acceptable_from,
                                     acceptable_To = a.acceptable_To,
                                     wished_value = a.wished_value,
                                     sort_order = a.sort_order,
                                     indicator_sort = a.indicator_sort,
                                     isDelegatedGoal = a.isDelegatedGoal,
                                     strategy = a.strategy,
                                     achievedresults = a.lastReportedMonRes,
                                     statusdescription = a.reportedMonDesc,
                                     prvlastYrRptMonRes = a.prvlastYrRptMonRes,
                                     lastYrReportMonRes = a.lastYrReportMonRes,
                                     statusGoal = a.statusGoal,
                                     statusGoalId = a.statusGoalId,
                                     statusTarget = a.statusTarget,
                                     statusTargetId = a.statusTargetId,
                                     statusGoalDesc = a.statusGoalDesc,
                                     statusTargetDesc = a.statusTargetDesc
                                 }).ToList();

            List<ProgressStatus> statusData = statusDataAsync.Result;

            foreach (var item in bpGoalsTargetData)
            {
                List<string> unTargetIdList = new List<string>();

                //UN Goals
                StringBuilder sungb = new StringBuilder();
                StringBuilder un = new StringBuilder();
                if (!string.IsNullOrEmpty((item.unGoals).Trim()))
                {
                    item.unGoalIds = unGoalIdList;
                }
                else
                {
                    if (unGoalIdList.Count > 0)
                        unGoalIdList.RemoveRange(0, unGoalIdList.Count);

                    item.unGoalIds = new List<string>();
                }

                var unGoalData = string.Empty;
                var unTargetData = string.Empty;
                List<string> unGoalList = new List<string>();
                List<string> unGoalIds = new List<string>();
                if (!string.IsNullOrEmpty(item.unGoals))
                {
                    unGoalIds = ((string)item.unGoals).Split(',').ToList();
                    unGoalIds = unGoalIds.OrderBy(x => Int32.Parse(x)).ToList();
                }

                foreach (var gTag in unGoalIds)
                {
                    var gt = unsGoals.FirstOrDefault(x => x.KeyString == gTag);
                    var gtg = gt != null ? gt.ValueString : string.Empty;
                    var gtt = gt != null ? gt.longDesc : string.Empty;
                    unGoalData = gtt != string.Empty ? gtt.Substring(0, 5) + gtg : string.Empty;
                    sungb.Append(unGoalData + "\n").ToString();
                    // unGoalList.Add(unGoalData + "<br>");
                }
                //unGoalList = unGoalList.OrderBy(x => x).ToList();
                item.goalsTagsHtml = sungb.ToString();
                item.unGoalTag = sungb.ToString();

                unsgTargets = (from ust in unSuDevTargets
                               join gls in unGoalIds on new { a = ust.fk_goal_id } equals new { a = gls.Trim() }
                               orderby ust.target_name
                               select new KeyValueHelper
                               {
                                   KeyString = ust.pk_target_id,
                                   ValueString = ust.target_short_name,
                                   longDesc = ust.target_name
                               }).ToList();

                StringBuilder suntb = new StringBuilder();
                if (item.bpTarget.Trim() != String.Empty)
                {
                    if (useUnGoalsForTarget.ToLower() == "true")
                    {
                        if (!string.IsNullOrEmpty(((string)item.unGoalsForTargets).Trim()))
                        {
                            unTargetIdList = ((string)item.unGoalsForTargets).Split(',').ToList();
                            item.unTargetIds = (unTargetIdList.OrderBy(x => x).ToList());
                        }
                        else
                        {
                            item.unTargetIds = new List<string>();
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty((item.unTargets).Trim()))
                        {
                            unTargetIdList = (item.unTargets).Split(',').ToList();
                            item.unTargetIds = (unTargetIdList.OrderBy(x => x).ToList());
                        }
                        else
                        {
                            item.unTargetIds = new List<string>();
                        }
                    }
                    foreach (var tTag in unTargetIdList)
                    {
                        var tt = useUnGoalsForTarget.ToLower() == "true" ? unsGoals.FirstOrDefault(x => x.KeyString == tTag) : unsgTargets.FirstOrDefault(x => x.KeyString == tTag);
                        var ttg = tt != null ? tt.ValueString : string.Empty;
                        var ttt = tt != null ? tt.longDesc : string.Empty;
                        unTargetData = ttt != string.Empty ?  ttg : string.Empty;

                        suntb.Append(unTargetData + "\n").ToString();
                    }
                }

                item.targetTagsHtml = suntb.ToString();
                item.unTargetTag = suntb.ToString();

                var tcg = tcgGoals.FirstOrDefault(x => x.pk_goal_id == item.goalId);
                var tcgd = tcgdGoals.FirstOrDefault(z => z.pk_goal_distribution_id == item.goalId);
                item.bpGoal = tcg != null ? tcg.goal_name : invalidGoalForBudgetYear;
                int focusArea = tcg != null && !string.IsNullOrEmpty(tcg.focus_area) ? int.Parse(tcg.focus_area) : -1;
                var tfa = tcoFocusArea.FirstOrDefault(x => x.pk_id == focusArea);
                if (tfa != null)
                {
                    string shortfadesc = "";
                    if (tfa.focusarea_description.Length > 24)
                    {
                        shortfadesc = tfa.focusarea_description.Substring(0, 24);
                        shortfadesc += "...";
                        item.focusAreaTag = "<span class='su-bp-tag-saddlebrown' title='" + tfa.focusarea_description + "'>" + tfa.focusarea_description + "</span>&nbsp;";
                        item.focusArea = "<span class='su-bp-tag-saddlebrown' title='" + tfa.focusarea_description + "'>" + tfa.focusarea_description + "</span>&nbsp;";
                    }
                    else
                    {
                        item.focusAreaTag = "<span class='su-bp-tag-saddlebrown' title='" + tfa.focusarea_description + "'>" + tfa.focusarea_description + "</span>&nbsp;";
                        item.focusArea = "<span class='su-bp-tag-saddlebrown' title='" + tfa.focusarea_description + "'>" + tfa.focusarea_description + "</span>&nbsp;";
                    }
                }
                else
                {
                    item.focusAreaTag = string.Empty;
                    item.focusArea = string.Empty;
                }

                if (tcgd != null)
                {
                    StringBuilder dsb = new StringBuilder();
                    string goalTgtTxt =  await _utility.GetAzureTableDescription(userId, tcgd.target_blob_id.ToString());
                    goalTgtTxt = goalTgtTxt.Trim();
                    if (goalTgtTxt.Length > 0)
                        dsb.Append(goalTgtTxt + "<br>");

                    string goalFocTxt = await _utility.GetAzureTableDescription(userId, tcgd.focus_blob_id.ToString());
                    if (goalFocTxt.Length > 0)
                        dsb.Append(goalFocTxt);
                    string delgoTxt = "";
                    delgoTxt = tcgd.delgo_blob_id != null && tcgd.delgo_blob_id != Guid.Empty ? await _utility.GetAzureTableDescription(userId, tcgd.delgo_blob_id.ToString()) : "";
                        if (delgoTxt.Length > 0)
                        dsb.Append(delgoTxt);
                    
                    item.goalDescription = dsb.ToString();
                }

                item.bpTargetInternalSort = item.bpTarget;
                item.bpGoalInternalSort = item.bpGoal;
                if (item.statusGoalId != -1)
                {
                    ProgressStatus gStatus = statusData.FirstOrDefault(x => x.Key == item.statusGoalId);
                    if (gStatus != null)
                    {
                        item.goalIconDesc = gStatus.Value;
                        item.goalIconId = gStatus.Key;
                    }
                    else
                    {
                        item.goalIconDesc = "";
                        item.goalIconId = 0;
                    }
                }
                else
                {
                    item.goalIconDesc = string.Empty;
                    item.goalIconId = 0;
                }
                if (item.statusTargetId != -1)
                {
                    ProgressStatus tStatus = statusData.FirstOrDefault(x => x.Key == item.statusTargetId);
                    if (tStatus != null)
                    {
                        item.targetIconDesc = tStatus.Value;
                        item.targetIconId = tStatus.Key;
                    }
                    else
                    {
                        item.targetIconDesc = "";
                        item.targetIconId = 0;
                    }
                }
                else
                {
                    item.targetIconDesc = string.Empty;
                    item.targetIconId = 0;
                }
                tagIds = new List<int>();
                if (!string.IsNullOrEmpty((string)item.bpGoalsTags))
                {
                    tagIds = ((string)item.bpGoalsTags).Split(',').Select(int.Parse).ToList();
                }

                StringBuilder sb = new StringBuilder();
                sb.Append(item.tagsDesc + "<br>");
                foreach (var tags in tagIds)
                {
                    var tagDes = actionTags.FirstOrDefault(x => x.KeyId == tags);
                    if (tagDes != null)
                    {
                        sb.Append("<span class='bp-tag-selected'>" + tagDes.ValueString + "</span>&nbsp;");
                    }
                }

                List<string> goalPlanName = plansConnectedToGoal.Where(x => x.key == item.goalId).Select(x => x.value).Distinct().ToList();
                List<string> targetPlanName = plansConnectedToTarget.Where(x => x.key == item.targetId).Select(x => x.value).Distinct().ToList();
                StringBuilder pg = new StringBuilder();
                StringBuilder pt = new StringBuilder();
                if (goalPlanName.Any())
                {
                    foreach (string mpn in goalPlanName)
                    {
                        pg.Append("<span class ='theme-plan-tags'>&nbsp;" + mpn + "</span><br>");
                    }
                }
                item.planGoal = pg.ToString();
                if (targetPlanName.Any())
                {
                    foreach (string mpn in targetPlanName)
                    {
                        if (goalPlanName.Any())
                        {
                            pt.Append("<span class ='theme-plan-tags'>&nbsp;" + mpn + "</span><br>");
                        }
                    }
                }
                item.planTarget = pt.ToString();
                item.tagsDesc = sb.ToString();

                string orgName = string.Empty;
                tco_goals masterGoal = tcgGoals.FirstOrDefault(x => x.pk_goal_id == item.goalId);

                if (string.IsNullOrEmpty(item.orgId) && item.serviceId == null && !string.IsNullOrEmpty(item.year1Result))
                {
                    orgName = orgHierachy.FirstOrDefault().org_name_1;
                }
                else
                {
                    if (masterGoal != null && !string.IsNullOrEmpty(masterGoal.org_created))
                    {
                        switch (masterGoal.org_level_created)
                        {
                            case 1:
                                orgName = orgHierachy.FirstOrDefault().org_name_1;
                                break;

                            case 2:
                                orgName = orgHierachy.FirstOrDefault(x => x.org_id_2 == masterGoal.org_created).org_name_2;
                                break;

                            case 3:
                                orgName = orgHierachy.FirstOrDefault(x => x.org_id_3 == masterGoal.org_created).org_name_3;
                                break;

                            case 4:
                                orgName = orgHierachy.FirstOrDefault(x => x.org_id_4 == masterGoal.org_created).org_name_4;
                                break;

                            case 5:
                                orgName = orgHierachy.FirstOrDefault(x => x.org_id_5 == masterGoal.org_created).org_name_5;
                                break;
                        }
                    }
                    else
                    {
                        orgName = orgHierachy.FirstOrDefault().org_name_1;
                    }
                }

                if (string.IsNullOrEmpty(orgName))
                {
                    orgName = orgHierachy.FirstOrDefault().org_name_1;
                }

                item.tagOrg = "<span class='su-bp-tag su-bp-tag-dark'>" + orgName + "</span>&nbsp;";

                if (!string.IsNullOrEmpty(item.serviceId) && tServs.Count > 0)
                {
                    if (item.serviceId == "-1" || item.serviceId.ToLower() == "all")
                    {
                        item.serviceName = ((langStr.FirstOrDefault(v => v.Key == "GT_Grid_col_serviceall")).Value).LangText;
                    }
                    else
                    {
                        tco_service_values tsv = tServs.FirstOrDefault(x => x.service_id_1 == item.serviceId);
                        if (tsv != null)
                            item.serviceName = item.serviceId + " " + tsv.service_name_1;
                        else
                        {
                            tsv = tServs.FirstOrDefault(x => x.service_id_2 == item.serviceId);

                            if (tsv != null)
                                item.serviceName = item.serviceId + " " + tsv.service_name_2;
                            else
                            {
                                tsv = tServs.FirstOrDefault(x => x.service_id_3 == item.serviceId);
                                if (tsv != null)
                                    item.serviceName = item.serviceId + " " + tsv.service_name_3;
                                else
                                {
                                    tsv = tServs.FirstOrDefault(x => x.service_id_4 == item.serviceId);
                                    if (tsv != null)
                                        item.serviceName = item.serviceId + " " + tsv.service_name_4;
                                    else
                                    {
                                        tsv = tServs.FirstOrDefault(x => x.service_id_5 == item.serviceId);
                                        if (tsv != null)
                                            item.serviceName = item.serviceId + " " + tsv.service_name_5;
                                    }
                                }
                            }
                        }
                    }
                }
                else
                    item.serviceName = string.Empty;

                item.isGoalDelgLower = ((bpGoalSubLevelOrgId.FirstOrDefault(x => (x.masterGoalId == item.goalId ||
                                                                                  (item.masterGoalId != Guid.Empty && x.masterGoalId == item.masterGoalId)) &&
                                                                                  x.orgLevel > item.orgLevel) == null) &&
                                        (bpGoalSubLevelOrgId.FirstOrDefault(x => (x.goalId == item.goalId ||
                                                                                  (item.masterGoalId != Guid.Empty && x.masterGoalId == item.masterGoalId)) &&
                                                                                  x.orgLevel > item.orgLevel)) == null) ? false : true;

                item.isAssignedToLowerLevel = ((bpGtDataSubLevelOrgId.FirstOrDefault(x => (x.masterTargetId == item.targetId ||
                                                                                           (item.masterTargetId != Guid.Empty && x.masterTargetId == item.masterTargetId)) &&
                                                                                           x.orgLevel > item.orgLevel) == null) &&
                                               (bpGtDataSubLevelOrgId.FirstOrDefault(x => (x.targetId == item.targetId ||
                                                                                           (item.masterTargetId != Guid.Empty && x.masterTargetId == item.masterTargetId)) &&
                                                                                           x.orgLevel > item.orgLevel)) == null) ? false : true;

                //Logic to determine if indicator has been delegated to lower level
                if (item.isAssignedToLowerLevel && item.fk_indicator_code != Guid.Empty)
                {
                    if (orgLevel > 1)
                        item.isIndicatorDelgLower = bpGtDataSubLevelOrgId.FirstOrDefault(x => x.fk_indicator_code == item.fk_indicator_code &&
                                                                                             (x.masterTargetId == item.masterTargetId || x.masterTargetId == item.targetId) &&
                                                                                             x.orgLevel > item.orgLevel) == null ? false : true;
                    else
                        item.isIndicatorDelgLower = bpGtDataSubLevelOrgId.FirstOrDefault(x => x.fk_indicator_code == item.fk_indicator_code &&
                                                                                             (x.masterTargetId == item.targetId || x.masterTargetId == item.masterTargetId) &&
                                                                                             x.orgLevel > item.orgLevel) == null ? false : true;
                }
                else
                    item.isIndicatorDelgLower = false;

                if (item.isIndicatorDelgLower)
                {
                    List<int> targetIds = bpGoalsTargetDataCopy.Where(x => x.masterGoalId == item.goalId && x.fk_indicator_code == item.fk_indicator_code && x.orgLevel > item.orgLevel).Select(y => y.targetDetailId).ToList();
                    foreach (var target in targetIds)
                    {
                        var tets = tetsList.FirstOrDefault(y => y.FkTargetDetailId == target);
                        string acheievedResults = (tets != null && tets.IndicatorValue != null) ? tets.IndicatorValue.Replace(',', '.') : string.Empty;
                        if (!string.IsNullOrEmpty(acheievedResults))
                        {
                            item.isAmountDelg = true;
                        }
                    }
                }

                if (item.targetId != Guid.Empty)
                {
                    if (targetIdList.Contains(item.targetId))
                    {
                        item.bpGoal = string.Empty;
                        item.bpTarget = string.Empty;
                        item.tagsDesc = string.Empty;
                        item.tagOrg = string.Empty;
                    }
                    else
                        targetIdList.Add(item.targetId);
                }
            }

            bpGoalsTargetData = bpGoalsTargetData.Where(x => x.bpTarget != invalidTargetForBudgetYear
                                                          && x.goalId != invalidGoalIdForBudgetYear
                                                          && x.bpGoal != invalidGoalForBudgetYear).ToList();

            bpGoalsTargetData = bpGoalsTargetData.OrderBy(x => x.bpGoalInternalSort).ThenBy(z => z.sort_order).ThenBy(y => y.bpTargetInternalSort).ThenBy(z => z.indicator_sort).ThenBy(z => z.bpIndicator).ToList();

            BusinessPlanGoalsTgtHelper prevItem = bpGoalsTargetData.FirstOrDefault();
            bool invalidPrevItem = true;
            if (prevItem != null)
            {
                int indiCounter = 0;
                int tgtCounter = 0;
                List<string> runGoalIds = new List<string>();
                List<string> runTgtIds = new List<string>();
                List<string> runGtIndics = new List<string>();

                foreach (BusinessPlanGoalsTgtHelper item in bpGoalsTargetData)
                {
                    if (runGoalIds.Contains(item.goalId.ToString()))
                    {
                        item.goalDescription = " ";
                        item.bpGoal = string.Empty;
                        item.isLastTarget = item.targetId == Guid.Empty;
                        item.isLastIndicator = item.targetId != Guid.Empty;
                        item.focusArea = string.Empty;
                        item.unGoalTag = string.Empty;
                        item.planGoal = string.Empty;
                        item.focusAreaTag = string.Empty;
                        item.statusGoal = string.Empty;
                        item.statusGoalDesc = string.Empty;
                        item.unGoals = string.Empty;
                        item.goalIconDesc = string.Empty;
                        item.goalIconId = 0;
                    }
                    else
                    {
                        item.isDeleteGoal = item.targetId == Guid.Empty && tgtCounter == 0 && item.isBusPlanGoal == true && item.masterGoalId == Guid.Empty;
                        prevItem.isLastTarget = true && !invalidPrevItem;
                        tgtCounter = 0;
                    }

                    if (item.targetId != Guid.Empty && runTgtIds.Contains(item.targetId.ToString()))
                    {
                        item.targetDescription = " ";
                        item.bpTarget = string.Empty;
                        item.tagsDesc = " ";
                        item.strategy = "";
                        item.unTargetTag = string.Empty;
                        item.planTarget = string.Empty;
                        item.statusTarget = string.Empty;
                        item.statusTargetDesc = string.Empty;
                        item.unTargets = string.Empty;
                        item.targetIconDesc = string.Empty;
                        item.targetIconId = 0;
                        if (item.fk_indicator_code != Guid.Empty)
                        {
                            indiCounter++;
                        }

                        tgtCounter++;
                        item.isLastTarget = item.targetId == Guid.Empty;
                        item.isLastIndicator = item.fk_indicator_code == Guid.Empty;
                    }
                    else
                    {
                        prevItem.isLastIndicator = prevItem.targetId != Guid.Empty && !invalidPrevItem;
                        prevItem.isDeleteTarget = indiCounter == 0 && prevItem.fk_indicator_code == Guid.Empty && prevItem.targetId != Guid.Empty && prevItem.masterTargetId == Guid.Empty && prevItem.isBusPlanTarget == true;
                        prevItem.isDeleteGoal = prevItem.targetId == Guid.Empty && tgtCounter == 0 && prevItem.isBusPlanGoal == true && prevItem.masterGoalId == Guid.Empty;
                        indiCounter = 0;
                        tgtCounter = 0;
                    }

                    if (serviceId == "-2" && item.fk_indicator_code != Guid.Empty && runGtIndics.Contains(item.goalId.ToString() + item.targetId.ToString() + item.fk_indicator_code.ToString()))
                    {
                        item.bpIndicator = " ";
                    }

                    item.isDeleteIndicator = !item.isDelegatedIndicator;
                    runGoalIds.Add(item.goalId.ToString());
                    runTgtIds.Add(item.targetId.ToString());
                    runGtIndics.Add(item.goalId.ToString() + item.targetId.ToString() + item.fk_indicator_code.ToString());
                    prevItem = item;
                    invalidPrevItem = false;
                }

                prevItem.isDeleteGoal = prevItem.targetId == Guid.Empty && tgtCounter == 0 && prevItem.isBusPlanGoal == true && prevItem.masterGoalId == Guid.Empty;
                prevItem.isDeleteTarget = indiCounter == 0 && prevItem.fk_indicator_code == Guid.Empty && prevItem.targetId != Guid.Empty && prevItem.masterTargetId == Guid.Empty && prevItem.isBusPlanTarget == true;
                prevItem.isLastTarget = true;
                prevItem.isLastIndicator = prevItem.targetId != Guid.Empty;
            }

            return bpGoalsTargetData;
        }

        private async Task<List<gco_un_susdev_targets>> GetGcoUnSusdevTargetsList()
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.gco_un_susdev_targets.ToListAsync();
        }

        private async Task<List<tco_service_values>> GetTcoServiceValuesList(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tco_service_values.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        private async Task<List<ProgressStatus>> GetProgressStatusList(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await (from s in tenantDbContext.tco_progress_status
                          where s.fk_tenant_id == tenantId && s.type == "sunit_bplan" && s.active == 1
                          select new ProgressStatus
                          {
                              Key = s.status_id,
                              Value = s.status_description,
                              negativeFlag = s.negative_flag
                          }).ToListAsync();
        }

        private async Task<List<KeyValueData>> GetKeyValueActionTagsDatas(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await (from atg in tenantDbContext.tcoActionTags
                          where atg.FkTenantId == tenantId
                          select new KeyValueData
                          {
                              KeyId = atg.PkId,
                              ValueString = atg.TagDescription
                          }).ToListAsync();
        }

        private async Task<List<tco_indicator_setup>> GetTcoIndicators(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tco_indicator_setup.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        private async Task<List<tco_focusarea>> GetTcoFocusareas(int tenantId, int budgetYear)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tco_focusarea.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        private async Task<List<tco_goals>> GetTcoGoalsList(int tenantId, int budgetYear)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tco_goals.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        private async Task<List<tco_targets>> GetTcoTargetsList(int tenantId, int budgetYear)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tco_targets.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        private async Task<List<tfp_strategy_text>> GetTfpStrategyTexts(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tfp_strategy_text.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        private async Task<List<tfp_strategy_target>> GetTfpStrategyTargets(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tfp_strategy_target.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        private async Task<List<tfp_strategy_goal>> GetTfpStrategyGoals(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tfp_strategy_goal.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        private async Task<List<tfp_effect_target_detail>> GetTfpEffectTargetDetails(int tenantId)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tfp_effect_target_detail.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        private async Task<List<tmd_indicator_results>> GetIndicatorResults(int tenantId, int budgetYear, string orgId, int orgLevel)
        {
            var tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            return await tenantDbContext.tmd_indicator_results.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.org_id == orgId && x.org_level == orgLevel).ToListAsync();
        }
    }
}