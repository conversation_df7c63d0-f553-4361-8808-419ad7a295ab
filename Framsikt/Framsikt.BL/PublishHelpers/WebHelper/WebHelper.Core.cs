#pragma warning disable CS8625
#pragma warning disable SYSLIB0014

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8618

using Framsikt.BL.Constants;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL.PublishHelpers;

public partial class WebHelper : IPublishHelper
{
    protected internal string userId;
    protected internal readonly List<string> errors;
    protected internal IUtility utility;
    protected internal IBudgetManagement budgetManagement;
    protected internal IFootnotes footnotes;
    protected internal PubContentHandler contentInsert;
    protected internal IHtmlImageRenderer renderer;
    protected internal IAzureBlobHelper azureBlobHelper;
    protected internal PublishTreeHelper publishTree;
    protected internal DocWidgetExport docWidgetExp;
    protected internal int configId;
    protected internal PublishConfig? publishConfig;
    protected internal int templateId;
    protected internal int processedChapterCount;
    protected internal Chapter? currentChapter;
    protected internal string baseUrl;
    protected internal List<EmbeddedUrl> EmbeddedUrls { get; set; }
    protected internal readonly Stack<Chapter> nestedChapters = new Stack<Chapter>();
    protected internal PublishTreeType publishTreeType;
    protected internal BlobContainers containerForPublish;
    protected internal bool collapsibleListStarted = false;
    protected internal int collapsibleItemId = 0;
    protected internal int budgetYear;
    protected internal int forecastPeriod;
    protected internal IMediaEmbed mediaEmbed;

    private int _preferredTableWidth = 0;
    protected internal Dictionary<string, clsLanguageString> docExportStrings = null;
    protected internal Dictionary<string, string> frontPageAbstracts = null;
    protected internal Dictionary<string, string> frontPageVideoUrls = null;
    protected internal const string classLeftAlign = "left";
    protected internal const string classRightAlign = "right";
    protected internal const string classCenterAlign = "center";
    protected internal const string classJustifyAlign = "justify";
    protected internal const string classWhiteSpaceNormal = "white-space-normal";
    protected internal const string classWhiteSpaceNoWrap = "white-space-nowrap";
    protected internal const string classBoxShadow = "th-box-shadow";
    protected internal const string classBoldFont = "subheading1";
    protected internal const string classSemiFont = "semi";
    protected internal const string classItalicFont = "italic";
    protected internal const string classDetailSumBorders = "detail-sum-borders";
    protected internal const string classBottomBorder = "th-border-bottom";
    protected internal const string classNoWrap = "nowrap";
    private string _captionText = string.Empty;
    protected internal const string classGreyTopBorder = "grey-border-top";
    protected internal const string showColorPalette = "SHOW_COLOR_PALETTE";
    protected internal const string pictureDesignSquare = "WEB_PICTURE_DESIGN_SQUARE";
    protected internal const string mainBannerAlignment = "Is_Main_Banner_Center_Aligned";
    protected internal bool isBookmarkIndentationEnabled = false;
    private bool expandable = false;

    private const string logHeader = "UniqueId,NodeId,NodeType,NodeName,OrgId,ServiceId,EntryType,TimeStamp\n";
    public int HeaderSize { get; private set; } = 1;
    public int NodeLevel { get; private set; }

    private readonly Stack<int> _headerSizeForBranch = new Stack<int>();
    protected IEnumerable<string> placeHolderNodes = null;
    protected internal IServiceProvider container;
    private string currentNodeType = string.Empty;
    private string currentNodeUid = string.Empty;
    private string currentNodeId = string.Empty;
    protected Dictionary<string, string> _globalPublishVariables;
    private List<ApprovalBookmarkDetails> _approvalBookmarkDetails;
    protected internal bool isPlanPreview = false;
    protected string groupByDropDownTableName = string.Empty;
    private const string reportingWidget = "reportingWidget";

    private Stack<int> _nodeLevelForBranch = new Stack<int>();
    protected internal const string finplanLevel = "FINPLAN_LEVEL_2";
    protected internal const string monthrepLevel = "MONTHREP_LEVEL_2";
    private const string HotJarPattern = @"<script>.*?hotjar\.com\/.*?<\/script>";
    private const string GoogleAnalyticsPattern = @"<script[^>]*\bsrc=['""][^'""]*googletagmanager\.com[^'""]*['""][^>]*>\s*<\/script>|<script[^>]*>[^<]*googletagmanager[^<]*<\/script>";
    private const string AnalyticsTrackingPlaceHolder = "<!--{analytics-tracking-code}-->";
    private const string HotjarTrackingPlaceholder = "<!--{hotjar-tracking-code}-->";
    protected internal List<FootnoteHelper> lstFootnotes { get; set; }
    private int footnoteCount;

    public bool HasDynamicTable { get; private set; } = false;
    public bool HasDynamicPivotTable { get; private set; } = false;
    public bool HasDynamicGraphElement { get; private set; } = false;
    public bool HasKostraAnalyseElement { get; private set; } = false;
    public bool HasKostraPopulationStatisticsElement { get; private set; } = false;
    private ICKEditorExtensions _editorExtensions;
    private string _contentHtmlForSinglePage  = string.Empty;
    private int _currentHeadingLevelForSinglePage  = 1;
    private List<string> _headingsForSinglePage = new();
    private const string SinglePageUrl  = "/generic/summary/11111111-1111-1111-1111-111111111111";
    private readonly List<ChapterBookmark> _bookmarksForSinglePage = new();
    private int headingIdItrForSinglePage = 0;

    private enum ErrorTypes
    {
        IndexNotConfigured,
        NoResultsFound
    }

    private enum EntryType
    {
        Start,
        End
    }

    public WebHelper()
    {
        errors = new List<string>();
        _headerSizeForBranch = new Stack<int>();
        _nodeLevelForBranch = new Stack<int>();
        Initialized = false;
    }

    public bool Initialized { get; internal set; }

    public string Version { get; protected internal set; }
    public string PublishId { get; protected internal set; }
    public string BaseStoragePath { get; protected internal set; }
    public string Title1Font { get; protected internal set; }
    public string Title2Font { get; protected internal set; }
    public string Title3Font { get; protected internal set; }

    //public void InsertBookmarks(JObject array, string path)
    //{
    //    if (string.IsNullOrEmpty(path)) path = currentChapter.Path;
    //    TenantData tenantData = utility.GetTenantData(userId);
    //    string bookmarkData = JsonConvert.SerializeObject(array);

    //    contentInsert.InsertBookmarks(tenantData.publish_Id, Version, path, bookmarkData, containerForPublish);

    //}
}

public enum LinkType
{
    Internal,
    external
};

public static class tabFilterType
{
    public const string accountGrid = "account";
    public const string functionGrid = "function";
}