using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using HtmlAgilityPack;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Framsikt.BL.PublishHelpers;

public partial class WebHelper
{
    private void ProcessDynamicWidgetSearch(string processedHtml, PublishTreeHelper tree, string type)
    {
        Dictionary<string, string> gridIdWithJsonPath = new();
        Dictionary<string, string> graphIdWithJsonPath = new();
        HtmlDocument processedDocument = new();
        processedDocument.LoadHtml(processedHtml);
        GetJsonInfoForDynamicWidget(processedDocument, gridIdWithJsonPath, graphIdWithJsonPath);
        int i = 0;
        foreach (var graphId in graphIdWithJsonPath)
        {
            i++;
            PublishText tableText = new()
            {
                abstractText = string.Empty,
                title = string.Empty,
                description = graphId.Value,
                type = PublishText.TextType.SearchTable,
                pageUrl = $"/{currentChapter.ControllerName.ToString().ToLower()}{currentChapter.Path}?scrollTo={graphId.Key}",
                pagePath = contentInsert.getPath(tree, type, currentChapter.Path).ToString(),
                heading = currentChapter.CurrentHeading
            };
            InsertDataIntoBlob(JsonConvert.SerializeObject(tableText), $"{currentChapter.Path}{graphId.Key}-{i}", string.Empty,
                PubContentHandler.BlobType.SearchText);
        }

        foreach (var gridId in gridIdWithJsonPath)
        {
            var jsonInfo = azureBlobHelper.GetTextBlobAsync(StorageAccount.PublishStage,
                containerForPublish, $"{BaseStoragePath.TrimEnd('/')}/content/data{gridId.Value}.json").GetAwaiter().GetResult();

            var data = JObject.Parse(jsonInfo);
            List<string> textInfo = new();
            if (data.ContainsKey("data"))
            {
                var newData = JToken.Parse(JsonConvert.SerializeObject(data["data"]));
                ExtractStringFromJson(newData, textInfo);

                if (textInfo.Any())
                {
                    currentChapter.TableSearchText.Clear();
                    currentChapter.TableSearchText.AddRange(textInfo.Select(x => x).Distinct());
                    i = 0;

                    foreach (var text in currentChapter.TableSearchText)
                    {
                        i++;
                        PublishText tableText = new()
                        {
                            abstractText = string.Empty,
                            title = string.Empty,
                            description = text,
                            type = PublishText.TextType.SearchTable,
                            pageUrl = $"/{currentChapter.ControllerName.ToString().ToLower()}{currentChapter.Path}?scrollTo={gridId.Key}",
                            pagePath = contentInsert.getPath(tree, type, currentChapter.Path).ToString(),
                            heading = currentChapter.CurrentHeading
                        };
                        InsertDataIntoBlob(JsonConvert.SerializeObject(tableText), $"{currentChapter.Path}{gridId.Key}-{i}", string.Empty,
                            PubContentHandler.BlobType.SearchText);
                    }
                }
            }
        }
    }

    private void ExtractStringFromJson(JToken data, List<string> textInfo)
    {
        foreach (var item in data)
        {
            if (item is JObject)
            {
                ExtractStringFromJson(item, textInfo);
            }
            else
            {
                var textContent = ((string)item.FirstOrDefault()) ?? string.Empty;
                if (!string.IsNullOrEmpty(textContent.Trim()) && IsString(textContent.Trim()))
                {
                    textInfo.Add(RemoveHTMLContentFromSearch(textContent));
                }
            }
        }
    }

    private bool IsString(string value)
    {
        if (value == null)
            return false;
        if (Guid.TryParse(value, out Guid x))
            return false;
        if (int.TryParse(value, out int y))
            return false;
        if (float.TryParse(value, out float x1))
            return false;
        if (decimal.TryParse(value, out decimal y1))
            return false;
        if (bool.TryParse(value, out bool z))
            return false;

        return true;
    }

    private void GetJsonInfoForDynamicWidget(HtmlDocument rawDocument, Dictionary<string, string> gridIdWithJsonPath,
        Dictionary<string, string> graphIdWithJsonPath)
    {
        if (rawDocument.DocumentNode.ChildNodes.Any())
        {
            foreach (var item in rawDocument.DocumentNode.ChildNodes)
            {
                if (item.Name == "div" && item.Attributes != null && item.Attributes.Any())
                {
                    var dataIdAttr = item.Attributes.FirstOrDefault(x => x.Name.ToLower() == "dataId".ToLower());
                    var idAttr = item.Attributes.FirstOrDefault(x => x.Name.ToLower() == "id");
                    var typeAttr = item.Attributes.FirstOrDefault(x => x.Name.ToLower() == "type");
                    var titleAttr = item.Attributes.FirstOrDefault(x => x.Name.ToLower() == "title");
                    if (idAttr != null
                        && dataIdAttr != null
                        && typeAttr != null
                        && (dataIdAttr.Value.Contains("docwidget") ||
                            typeAttr.Value.ToLower() == "widgetGrph" ||
                            typeAttr.Value.ToLower() == "populationgraph"))
                    {
                        switch (typeAttr.Value.ToLower())
                        {
                            case "table":
                            case "kostra_table":
                            case "bmtree_table":
                            case "dashboard_table":
                            case "mrtree_table":
                            case "pivottable":
                            case "bmtree_pivottable":
                            case "mrtree_pivottable":
                            case "dashboard_pivottable":
                                var hasRecord = gridIdWithJsonPath.FirstOrDefault(x => x.Key == idAttr.Value);
                                if (hasRecord.Key == null)
                                    gridIdWithJsonPath.Add(idAttr.Value, dataIdAttr.Value);
                                break;

                            case "widgetgrph":
                            case "populationgraph":
                                var hasgrpRecord = graphIdWithJsonPath.FirstOrDefault(x => x.Key == idAttr.Value);
                                if (hasgrpRecord.Key == null && titleAttr != null)
                                    graphIdWithJsonPath.Add(idAttr.Value, titleAttr.Value);
                                break;
                        }
                    }
                }
                if (item.ChildNodes.Any())
                {
                    HtmlDocument childDoc = new HtmlDocument();
                    childDoc.LoadHtml(item.InnerHtml);
                    GetJsonInfoForDynamicWidget(childDoc, gridIdWithJsonPath, graphIdWithJsonPath);
                }
            }
        }
    }



    private void SetFootnotes(HtmlNode node)
    {
        if (node.HasClass("textnote") && node.Attributes["class"].Value == "textnote")
        {
            node.Attributes.Remove("onclick");
            node.Attributes.Remove("target");
            var footnoteId = node.HasAttributes && node.Attributes["id"] != null ? node.Attributes["id"].Value.ToLower() : string.Empty;
            node.Attributes.Append("ng-click", $"scrollToFootNote('{footnoteId}');");
            node.Attributes.Append("href", "javascript:void(0)");
            footnoteCount++;

            lstFootnotes.Add(new FootnoteHelper()
            {
                footnoteId = footnoteId,
                footnoteText = string.Empty,
                historyId = node.HasAttributes && node.Attributes["historyid"] != null ? Guid.Parse(node.Attributes["historyid"].Value.ToLower()) : Guid.Empty,
                footnoteCount = footnoteCount
            });

            node.FirstChild.InnerHtml = footnoteCount.ToString();
        }
    }



    public string ProcessEmbeddedMedia(string html)
    {
        string modifiedHtml = html;

        HtmlDocument doc = new HtmlDocument();
        doc.LoadHtml(modifiedHtml);
        var embedTags = doc.DocumentNode.SelectNodes("//oembed");

        if (embedTags == null) return modifiedHtml;

        foreach (var embedTag in embedTags)
        {
            var urlAttr = embedTag.Attributes.FirstOrDefault(x =>
                x.Name.Equals("url", StringComparison.InvariantCultureIgnoreCase));
            if (urlAttr == null) continue;

            var mediaHtml = mediaEmbed.GetEmbedHtml(urlAttr.Value);

            var mediaTag = HtmlNode.CreateNode(mediaHtml);
            embedTag.ParentNode.ReplaceChild(mediaTag, embedTag);
        }

        modifiedHtml = doc.DocumentNode.OuterHtml;

        return modifiedHtml;
    }



    private string GetPageText(PublishTreeHelper tree, string type)
    {
        return contentInsert.getPath(tree, type, ((currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
            currentChapter.Path : "")).ToString();
    }



    public void InsertTextForSearch(string title, string description, string userId)
    {
        if (description == null)
        {
            return;
        }

        if (currentChapter != null)
        {
            string tagId = currentChapter.GetNextTagId();
            currentChapter.SearchTextId++;
            PublishTreeHelper tree = GetPublishTree();
            string path = $"{GetCurrentChapterPath()}searchtext{currentChapter.SearchTextId}";
            TenantData tenantData = utility.GetTenantData(this.userId);
            string type = (currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
                (currentChapter.Path.Split('/').Any() ? currentChapter.Path.Split('/')[1] : "") : "";
            if (currentChapter.CurrentHeading == null)
            {
                currentChapter.CurrentHeading = title;
            }
            PublishText content = new PublishText
            {
                title = string.IsNullOrEmpty(title) ? string.Empty : title,
                abstractText = string.Empty,
                description = description,
                type = PublishText.TextType.SearchText,
                pageUrl = $"/{currentChapter.ControllerName.ToString().ToLower()}{currentChapter.Path}?scrollTo={tagId}",
                //pageUrl = $"/{BaseStoragePath}/#/" +
                //    (type == "summary" ? "generic" : "budsadetail") + ((currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
                //    currentChapter.Path : "") + "?scrollTo=" + tagId,
                pagePath = contentInsert.getPath(tree, type, ((currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
                    currentChapter.Path : "")).ToString(),
                heading = currentChapter.CurrentHeading
            };
            InsertDataIntoBlob(JsonConvert.SerializeObject(content), path, string.Empty,
                PubContentHandler.BlobType.SearchText);
        }
    }



    public void InsertAbstract(string abstractText)
    {
        if (string.IsNullOrEmpty(abstractText))
        {
            return;
        }

        string path = currentChapter.Path;
        PublishTreeHelper tree = GetPublishTree();

        string type = (currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
            (currentChapter.Path.Split('/').Any() ? currentChapter.Path.Split('/')[1] : "") : "";
        if (currentChapter != null)
        {
            PublishText content = new PublishText
            {
                title = string.Empty,
                abstractText = abstractText,
                description = string.Empty,
                type = PublishText.TextType.Abstract,
                pageUrl = $"/{currentChapter.ControllerName.ToString().ToLower()}{currentChapter.Path}",
                //pageUrl = $"/{BaseStoragePath}/#/" +
                //    (type == "summary" ? "generic" : "budsadetail") + ((currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
                //    currentChapter.Path : ""),
                pagePath = contentInsert.getPath(tree, type, ((currentChapter != null && !string.IsNullOrEmpty(currentChapter.Path)) ?
                    currentChapter.Path : "")).ToString(),
                heading = currentChapter.CurrentHeading
            };

            InsertDataIntoBlob(JsonConvert.SerializeObject(content), path, string.Empty,
                PubContentHandler.BlobType.AbstractText, new Dictionary<string, string>());
        }
    }



    public string ProcessTable(string nodeContent)
    {
        //Clean out the table tags to remove border, cellpadding and cellspacing
        nodeContent = Regex.Replace(nodeContent, "border\\s*=\\s*\"\\d*\"", "");
        nodeContent = Regex.Replace(nodeContent, "cellspacing\\s*=\\s*\"\\d*\"", "");
        nodeContent = Regex.Replace(nodeContent, "cellpadding\\s*=\\s*\"\\d*\"", "");
        nodeContent = ProcessJson(nodeContent);
        nodeContent = Regex.Replace(nodeContent, @"<table.*?>", "<table class=\"ck-custom-table-export\">");

        /* change td widh for table web*/
        nodeContent = UpdateTableWidth(nodeContent);
        /*end*/
        nodeContent = RemoveSpanColorforHeaderTags(nodeContent);
        return nodeContent;
    }



    private string UpdateTableWidth(string nodeContent)
    {
        try
        {
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(nodeContent);
            nodeContent = GetFormattedString(nodeContent, false);

            //to identify the case that the table has only a thead and no tbody
            bool onlyTHead = false;

            //Find all tables
            //Loop through tables
            //Find the last <tr>. Use this to calculate the total width of the table

            var htmlTables = htmlDoc.DocumentNode.SelectNodes("//table");
            if (htmlTables != null)
            {
                foreach (var htmlTable in htmlTables)
                {
                    bool tableHasWWidth = false; //This will be true if it is a table that the user has edited to set column widths
                    var rows = htmlTable.SelectNodes("./tbody/tr");
                    //In case table contains rows only in thead - Bug 46168
                    if (rows == null)
                    {
                        rows = htmlTable.SelectNodes("./thead/tr");
                        onlyTHead = true;
                    }
                    if (rows == null)
                    {
                        continue;
                    }
                    var lastRow = rows.Last();
                    if (lastRow != null)
                    {
                        var firstTdNode = lastRow.SelectSingleNode("./td|./th");
                        if (firstTdNode != null && firstTdNode.Attributes.Contains("wwidth"))
                        {
                            tableHasWWidth = true;
                        }
                        else
                        {
                            //Table width has not been edited by the user. WYSIWYG applies
                            continue;
                        }
                    }
                    if (rows.Any())
                    {
                        int rowCount = 0;

                        foreach (var row in rows)
                        {
                            var tdNodes = row.SelectNodes("./td|./th");
                            if (tdNodes != null)
                            {
                                int tableWidth = 0;
                                if (tableHasWWidth)
                                {
                                    if (rowCount == (rows.Count - 1))
                                    {
                                        //Calculate table width
                                        foreach (var tdNode in tdNodes)
                                        {
                                            int colWidth = 0;
                                            bool parseColWidth = false;
                                            if (tdNode.Attributes.Contains(@"wwidth"))
                                            {
                                                parseColWidth = int.TryParse(tdNode.Attributes.FirstOrDefault(x => x.Name == "wwidth").Value, out colWidth);
                                            }
                                            else
                                            {
                                                //if it does not exist use the width attribute, which will always be there
                                                if (tdNode.Attributes.FirstOrDefault(x => x.Name == "width") != null)
                                                {
                                                    parseColWidth = int.TryParse(tdNode.Attributes.FirstOrDefault(x => x.Name == "width").Value, out colWidth);
                                                }
                                            }
                                            if (parseColWidth)
                                            {
                                                tableWidth = tableWidth + colWidth;
                                            }
                                        }
                                        //Recalculate the column width. Convert from absolute to width relative to table width
                                        foreach (var tdNode in tdNodes)
                                        {
                                            int colWidth = 0;
                                            if (tdNode.Attributes.Contains(@"wwidth"))
                                            {
                                                bool parseColWidth = int.TryParse(tdNode.Attributes.FirstOrDefault(x => x.Name == "wwidth").Value, out colWidth);
                                                if (parseColWidth)
                                                {
                                                    var relativeWidth = (colWidth * 100) / tableWidth;
                                                    SetStyleValue(tdNode, "width", $"{relativeWidth}%");
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        foreach (var tdNode in tdNodes)
                                        {
                                            RemoveStyleValue(tdNode, "width");
                                        }
                                    }
                                }
                                //Set the table width
                                if (tableHasWWidth) //The value was calculated and is last row
                                {
                                    SetStyleValue(htmlTable, "width", $"{tableWidth}%");

                                    if (rowCount == (rows.Count - 1))
                                    {
                                        //If the table element has figure as a parent element, then update figure element width
                                        if (htmlTable.ParentNode.Name.ToLower() == "figure")
                                            SetStyleValue(htmlTable.ParentNode, "width", $"{tableWidth}%");
                                    }
                                }
                            }
                            rowCount++;
                        }
                    }
                    if (tableHasWWidth)
                    {
                        //Set the table width

                        //Clean up unwanted attributes
                        var allTdNodes = htmlTable.SelectNodes("./tbody/tr/td|./tbody/tr/th");
                        if (onlyTHead)
                        {
                            allTdNodes = htmlTable.SelectNodes("./thead/tr/td|./tbody/tr/th");
                        }
                        if (allTdNodes != null)
                        {
                            foreach (var tdNode in allTdNodes)
                            {
                                tdNode.Attributes.Remove("wwidth");
                                tdNode.Attributes.Remove("dwidth");
                                if (tdNode.Attributes.FirstOrDefault(x => x.Name == "width") != null)
                                {
                                    tdNode.Attributes.Remove("width");
                                }
                            }
                        }
                    }
                }
                StringWriter sw = new StringWriter();
                htmlDoc.Save(sw);
                return sw.ToString();
            }
            else
            {
                return nodeContent;
            }
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }



    /// <summary>
    /// Method to remove width related properties property from style attribute for tables that has wwidth attribute
    /// Bug 103463
    /// </summary>
    /// <param name="nodeContent"></param>
    private string VaildateTableandRemoveWidthAttribute(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(nodeContent);
        var htmlTables = htmlDoc.DocumentNode.SelectNodes("//table");

        if (htmlTables != null)
        {
            foreach (var item in htmlTables)
            {
                var rows = item.SelectNodes("./tbody/tr");
                if (rows == null) //  bug #115264
                {
                    continue;
                }
                else
                {
                    var lastRow = rows.Last();
                    var firstTdNode = lastRow.SelectSingleNode("./td");
                    if (firstTdNode != null && firstTdNode.Attributes.Contains("wwidth"))
                    {
                        RemoveWidthAttribute(item);
                    }
                }
            }
        }

        return htmlDoc.DocumentNode.OuterHtml;
    }



    private void RemoveWidthAttribute(HtmlNode item)
    {
        if (item.Attributes.Contains("style"))
        {
            RemoveStyleValue(item, "width");
        }
        if (item.ChildNodes.Any())
        {
            foreach (var child in item.ChildNodes)
            {
                RemoveWidthAttribute(child);
            }
        }
    }



    private void RemoveStyleValue(HtmlNode tdNode, string styleAttrName)
    {
        if (tdNode.Attributes.FirstOrDefault(x => x.Name == "style") != null)
        {
            List<string> newSet = new List<string>();
            var styleAttribute = tdNode.Attributes.FirstOrDefault(x => x.Name == "style").Value.Split(';').ToList();

            if (styleAttribute.Any())
            {
                tdNode.Attributes.Remove("style");
                foreach (var item in styleAttribute)
                {
                    var widthAttr = item.Split(':')[0];
                    if (!widthAttr.Contains(styleAttrName))
                    {
                        newSet.Add(item);
                    }
                }
            }
            tdNode.SetAttributeValue("style", newSet.Any() ? string.Join(";", newSet) : string.Empty);
        }
    }



    private void SetStyleValue(HtmlNode tdNode, string styleAttrName, string value)
    {
        StringBuilder sb = new StringBuilder();
        if (tdNode.Attributes.FirstOrDefault(x => x.Name == "style") != null)
        {
            var styleAttribute = tdNode.Attributes.FirstOrDefault(x => x.Name == "style").Value.Split(';').ToList();

            if (styleAttribute.Any())
            {
                tdNode.Attributes.Remove("style");
                if (styleAttribute.FirstOrDefault(x => x.Contains(styleAttrName)) != null)
                {
                    styleAttribute.Remove(styleAttribute.FirstOrDefault(x => x.Contains(styleAttrName)));
                }
            }

            foreach (var item in styleAttribute.Where(x => !string.IsNullOrEmpty(x)))
            {
                sb.Append(item);
                sb.Append(";");
            }
        }
        sb.Append($"{styleAttrName}:{value}");
        tdNode.SetAttributeValue("style", sb.ToString());
    }



    //Framsikt style tables use Kendo grids for rendering. This method parses the embedded table
    //and generates a json file that can be bound with a kendo grid
    //Below method process only tables drawn in ck editor using framsikt table 1 and table 2 styles i.e., cke-table-custom2 & cke-table-custom
    //Using html agility pack for preparing json
    //Implemented as part of story 21864
    private string ProcessJson(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        nodeContent = ProcessFramsikt3Styles(nodeContent);
        htmlDoc.LoadHtml(nodeContent);
        var htmltables = htmlDoc.DocumentNode.SelectNodes("//table");
        if (htmltables == null)
        {
            return nodeContent;
        }

        foreach (var table in htmltables)
        {
            try
            {
                string tablepath = "/ckeditorTables";
                //If this is a framsikt style table
                if (table.Attributes.Contains("class") && table.Attributes["class"].Value.Contains("cke-table-custom"))
                {
                    string captionText = string.Empty;
                    string gridId = string.Concat("grid-", Guid.NewGuid().ToString());
                    tablepath += "/" + gridId + "/";
                    Guid ckTable = Guid.NewGuid();

                    var htmlrows = table.SelectNodes(".//tr");
                    HtmlNodeCollection caption = table.SelectNodes(".//caption");
                    if (caption != null)
                    {
                        captionText = Regex.Replace(caption.FirstOrDefault().OuterHtml, "<.*?>", string.Empty);
                    }
                    List<int> widthList = new List<int>();
                    if (htmlrows == null)
                    {
                        continue;
                    }
                    var lastRow = htmlrows.LastOrDefault();
                    foreach (var r in lastRow.ChildNodes)
                    {
                        if (r.Name == "td")
                        {
                            if (r.Attributes.Contains("wwidth"))
                            {
                                widthList.Add(int.Parse(r.Attributes.FirstOrDefault(x => x.Name == "wwidth").Value));
                            }
                            else
                            {
                                widthList.Add(0);
                            }
                        }
                    }

                    var finalJsonObject = new JObject();
                    var dataArray = new JArray();
                    Dictionary<string, Dictionary<string, string>> columns = new Dictionary<string, Dictionary<string, string>>();
                    bool columnsInitiated = false;
                    bool isFramsikt3Styles = (table.Attributes.Contains("class") && table.Attributes["class"].Value.Contains("cke-table-custom3"));
                    bool isFramsikt4Style = (table.Attributes.Contains("class") && table.Attributes["class"].Value.Contains("cke-table-custom4"));
                    if (isFramsikt3Styles)
                    {
                        GetColumnsRightAlignmentStatus(table.OuterHtml);
                    }
                    for (int i = 0; i < htmlrows.Count; i++)
                    {
                        var objData = new JObject();
                        for (int j = 0; j < htmlrows[i].ChildNodes.Count; j++)
                        {
                            var htmlNode = htmlrows[i].ChildNodes[j];
                            if (htmlNode.Name == "td" || htmlNode.Name == "th")
                            {
                                if (i == 0)
                                {
                                    if (!columnsInitiated)
                                    {
                                        var styleAttr = htmlNode.Attributes.FirstOrDefault(x => x.Name.ToLower() == "style".ToLower());

                                        columns.Add(
                                            htmlrows[i].ChildNodes[0].Name != "td" ? "column" + (j - 1).ToString() : "column" + j.ToString(),
                                            new Dictionary<string, string> { { $"<span class='heading2 th-border-bottom'>{htmlNode.InnerText}</span>", styleAttr == null ? string.Empty :
                                                GetAlignmentClassName(styleAttr.Value) } }
                                        );
                                    }
                                }
                                else if (i == (htmlrows.Count - 1) && !isFramsikt4Style)
                                {
                                    //if this is the last row and the table is not of framsikt style 4, we should apply total formatting
                                    var styleAttr = htmlNode.Attributes.FirstOrDefault(x => x.Name.ToLower() == "style".ToLower());
                                    string cellContent = htmlNode.InnerHtml;
                                    string htmlTag = $"<span class='total1'>{cellContent}</span>";
                                    if (styleAttr != null)
                                    {
                                        string styles = GetAlignmentClassName(styleAttr.Value);
                                        if (!string.IsNullOrEmpty(styles))
                                        {
                                            htmlTag = $"<div class='total1 {styles}'>{cellContent}</div>";
                                        }
                                    }

                                    objData.Add(htmlrows[i].ChildNodes[0].Name != "td" ? "column" + (j - 1).ToString() : "column" + j.ToString(), htmlTag);
                                }
                                else
                                {
                                    var applyBorder = false;
                                    if (htmlNode.ParentNode.Attributes.Any() &&
                                        htmlNode.ParentNode.Attributes.FirstOrDefault(x => x.Value == "ck-table-total"
                                            || x.Value == "ck-table-line" || x.Value == "ck-table-line-top" || x.Value == "ck-table-line-bottom") != null)
                                    {
                                        applyBorder = true;
                                    }
                                    if (applyBorder)
                                    {
                                        var styleAttr = htmlNode.Attributes.FirstOrDefault(x => x.Name.ToLower() == "style".ToLower());
                                        string cellContent = htmlNode.InnerHtml;

                                        if (htmlNode.ParentNode.Attributes.FirstOrDefault(x => x.Value == "ck-table-line-top") != null)
                                        {
                                            string htmlTag = $"<span class='content2'>{cellContent}</span>";
                                            if (styleAttr != null)
                                            {
                                                htmlTag = $"<div class='content2 {GetAlignmentClassName(styleAttr.Value)}'>{cellContent}</div>";
                                            }

                                            objData.Add(htmlrows[i].ChildNodes[0].Name != "td" ? "column" + (j - 1).ToString() : "column" + j.ToString(), htmlTag);
                                        }
                                        else if (htmlNode.ParentNode.Attributes.FirstOrDefault(x => x.Value == "ck-table-total") != null)
                                        {
                                            string className = "subtotal2";
                                            if (i == (htmlrows.Count - 1))
                                            {
                                                //If it is the last row, we want to apply the grandtotal style
                                                className = "grandtotal";
                                            }
                                            string htmlTag = $"<span class='{className}'>{cellContent}</span>";
                                            if (styleAttr != null)
                                            {
                                                htmlTag = $"<div class='{className} {GetAlignmentClassName(styleAttr.Value)}'>{cellContent}</div>";
                                            }

                                            objData.Add(htmlrows[i].ChildNodes[0].Name != "td" ? "column" + (j - 1).ToString() : "column" + j.ToString(), htmlTag);
                                        }
                                        else
                                        {
                                            string htmlTag = $"<span class='content1'>{cellContent}</span>";
                                            if (styleAttr != null)
                                            {
                                                htmlTag = $"<div class='content1 {GetAlignmentClassName(styleAttr.Value)}'>{cellContent}</div>";
                                            }
                                            objData.Add(htmlrows[i].ChildNodes[0].Name != "td" ? "column" + (j - 1).ToString() : "column" + j.ToString(), htmlTag);
                                        }
                                    }
                                    else
                                    {
                                        var styleAttr = htmlNode.Attributes.FirstOrDefault(x => x.Name.ToLower() == "style".ToLower());
                                        if (styleAttr == null)
                                        {
                                            objData.Add(htmlrows[i].ChildNodes[0].Name != "td" ? $"column{(j - 1).ToString()}" : $"column{j.ToString()}",
                                                $"<span class='content1'>{htmlNode.InnerHtml}</span>");
                                        }
                                        else
                                        {
                                            objData.Add(htmlrows[i].ChildNodes[0].Name != "td" ? $"column{(j - 1).ToString()}" : $"column{j.ToString()}",
                                                $"<span class='content1 {GetAlignmentClassName(styleAttr.Value)}'>{htmlNode.InnerHtml}</span>");
                                        }
                                    }
                                }
                            }
                        }
                        if (objData.Count > 0)
                        {
                            dataArray.Add(objData);
                        }
                        columnsInitiated = true;
                    }

                    var columnsArray = new JArray();
                    bool isFirstRowFirstColumn = false; int colIndex = 0;

                    foreach (var col in columns)
                    {
                        dynamic row;
                        string colContent = string.IsNullOrEmpty(col.Value.FirstOrDefault().Key.ToString()) ? " " : col.Value.FirstOrDefault().Key.ToString();
                        if (!isFirstRowFirstColumn)
                        {
                            row = new JObject() {
                                new JProperty("title", colContent),
                                new JProperty("field", col.Key.ToString()),
                                new JProperty("colCount", 0),
                                new JProperty("encoded", false),
                                new JProperty("attributes", new JObject { new JProperty("style", $"white-space: normal; text-align:left;{GetWebWidth(widthList,colIndex)}") }),
                                new JProperty("headerAttributes", new JObject { new JProperty("style", $"white-space: normal; text-align:left;{GetWebWidth(widthList, colIndex)}") })
                            };
                        }
                        else
                        {
                            row = new JObject() {
                                new JProperty("title", colContent),
                                new JProperty("field", col.Key.ToString()),
                                new JProperty("colCount", 0),
                                new JProperty("encoded", false),
                                new JProperty("attributes", string.IsNullOrEmpty(col.Value.FirstOrDefault().Value.ToLower()) ?
                                    new JObject { new JProperty("style", $"white-space: normal;{GetWebWidth(widthList, colIndex)}") } :
                                    new JObject { new JProperty("style", $"text-align:{col.Value.FirstOrDefault().Value.ToLower()};white-space: normal;{GetWebWidth(widthList, colIndex)}")}),
                                new JProperty("headerAttributes", string.IsNullOrEmpty(col.Value.FirstOrDefault().Value.ToLower()) ?
                                    new JObject { new JProperty("style", $"white-space: normal;{GetWebWidth(widthList, colIndex)}") } :
                                    new JObject { new JProperty("style", $"text-align:{col.Value.FirstOrDefault().Value.ToLower()};white-space: normal;{GetWebWidth(widthList, colIndex)}")})
                            };
                        }
                        colIndex++;
                        isFirstRowFirstColumn = true;
                        columnsArray.Add(row);
                    }

                    finalJsonObject.Add("data", dataArray);
                    finalJsonObject.Add("column", columnsArray);
                    finalJsonObject.Add("caption", captionText);

                    Console.WriteLine(finalJsonObject);
                    string content = JsonConvert.SerializeObject(finalJsonObject);
                    InsertDataIntoBlob(content, tablepath, ckTable.ToString(), PubContentHandler.BlobType.Json);

                    string fixedWidth = $"width: {widthList.Sum()}%;";
                    string tableTag = $"<div type=\"table\" id=\"{gridId}\" dataId = \"{string.Concat(tablepath, ckTable.ToString())}\" class=\"k-grid k-widget\" data-role=\"grid\" style=\"{fixedWidth}\"></div>";

                    var divWrapper = HtmlNode.CreateNode(tableTag);
                    table.ParentNode.InsertBefore(divWrapper, table);
                    table.Remove();
                }
                else
                {
                    //This is not a framsikt style table.
                    //We have to put the table under a div to control its appearance
                    //We have to set the column widths if the wwidth has been specified
                    var tableClone = table.Clone();
                    var divWrapper = HtmlNode.CreateNode("<div class='k-grid k-widget k-display-block kendo-custom-table'></div>");
                    divWrapper.ChildNodes.Add(tableClone);
                    table.ParentNode.InsertBefore(divWrapper, table);
                    table.Remove();
                }
            }
            catch
            {
                throw new InvalidDataException("Failed to create html version of table. Possible cause - table format not supported.");
            }
        }
        nodeContent = htmlDoc.DocumentNode.OuterHtml.ToString();
        nodeContent = Regex.Replace(nodeContent, "<caption>", "<caption class='sr-only'>");
        return nodeContent;
    }



    public string ProcessTableCkEditor5(string nodeContent)
    {
        //Clean out the table tags to remove border, cellpadding and cellspacing
        nodeContent = Regex.Replace(nodeContent, "border\\s*=\\s*\"\\d*\"", "");
        nodeContent = Regex.Replace(nodeContent, "cellspacing\\s*=\\s*\"\\d*\"", "");
        nodeContent = Regex.Replace(nodeContent, "cellpadding\\s*=\\s*\"\\d*\"", "");
        nodeContent = VaildateTableandRemoveWidthAttribute(nodeContent);
        nodeContent = UpdateTableWidth(nodeContent);
        nodeContent = AppendFigureTableclass(nodeContent);
        nodeContent = processJsonCkEditor5(nodeContent);
        // identify the widget and process
        SetUpAccessibilityInfoOnDynamicandKostraElement(nodeContent);
        if (!isPlanPreview)
        {
            this.AddPublishNodeDetails();
            var processedData = docWidgetExp.IdentifyUpdateWidget(userId, nodeContent, _globalPublishVariables, false, currentNodeType == reportingWidget ? currentNodeId : string.Empty).Result;
            nodeContent = processedData.descContent;
        }
        return nodeContent;
    }



    private void SetUpAccessibilityInfoOnDynamicandKostraElement(string desc)
    {
        if ((desc.Contains("displaytype=\"table\"") || desc.Contains("displaytype =\"BMTree_Table\"")) && !HasDynamicTable)
            HasDynamicTable = true;

        if ((desc.Contains("displaytype=\"pivottable\"") || desc.Contains("displaytype=\"BMTree_PivotTable\"")) && !HasDynamicPivotTable)
            HasDynamicPivotTable = true;

        if ((desc.Contains("displaytype=\"graph\"") || desc.Contains("displaytype=\"BMTree_Graph\"")) && !HasDynamicGraphElement)
            HasDynamicGraphElement = true;

        if (desc.Contains("widgettype=\"kostra\"") && !HasKostraAnalyseElement)
            HasKostraAnalyseElement = true;

        if (desc.Contains("widgettype=\"populationStatistics\"") && !HasKostraPopulationStatisticsElement)
            HasKostraPopulationStatisticsElement = true;
    }



    private string AppendFigureTableclass(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(nodeContent);
        var figureNodes = htmlDoc.DocumentNode.SelectNodes("//figure");
        if (figureNodes == null) return nodeContent;
        foreach (var item in figureNodes)
        {
            var attributetoreplace = item.Attributes.FirstOrDefault(x => x.Name == "class" && x.Value == "table");
            if (attributetoreplace != null)
            {
                attributetoreplace.Value = "table plan-publish-figure-table";
            }
        }
        nodeContent = htmlDoc.DocumentNode.OuterHtml.ToString();
        return nodeContent;
    }



    private string processJsonCkEditor5(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(nodeContent);
        var htmltables = htmlDoc.DocumentNode.SelectNodes("//table");
        if (htmltables == null) return nodeContent;
        foreach (var table in htmltables)
        {
            List<HtmlNode> headRowslist = GetTableRows(table, "thead");
            List<HtmlNode> bodyRowslist = GetTableRows(table, "tbody");
            var divWrapper = HtmlNode.CreateNode("<div class='k-grid k-widget k-display-block kendo-custom-table'></div>");
            var newTable = ApplyPlanCustomTableClass(bodyRowslist) ? HtmlNode.CreateNode("<table class=\"ck-custom-table-export plan-publish plan-custom-table\"></table>")
                : HtmlNode.CreateNode("<table class=\"ck-custom-table-export plan-publish\"></table>");
            var captionDesc = table.Attributes.FirstOrDefault(x => x.Name == "caption");
            if (captionDesc != null)
            {
                var captionNode = HtmlNode.CreateNode($"<caption class=\"sr-only\">{captionDesc.Value} </caption> ");
                newTable.ChildNodes.Add(captionNode);
            }

            foreach (var item in table.Attributes)
            {
                newTable.Attributes.Add(item.Name, item.Value);
            }

            if (headRowslist.Any())
            {
                var newTableHead = HtmlNode.CreateNode("<thead></thead>");
                foreach (var item in headRowslist)
                {
                    newTableHead.ChildNodes.Add(item);
                }
                newTable.ChildNodes.Add(newTableHead);
            }

            var newTableBody = HtmlNode.CreateNode("<tbody></tbody>");
            foreach (var item in bodyRowslist)
            {
                newTableBody.ChildNodes.Add(item);
            }
            newTable.ChildNodes.Add(newTableBody);
            divWrapper.ChildNodes.Add(newTable);
            table.ParentNode.InsertBefore(divWrapper, table);
            table.Remove();
        }
        nodeContent = htmlDoc.DocumentNode.OuterHtml.ToString();
        return nodeContent;
    }



    private bool ApplyPlanCustomTableClass(List<HtmlNode> bodyRowslist)
    {
        bool applyPlanCustomTable = false;
        foreach (var item in bodyRowslist)
        {
            var classAtttibute = item.Attributes.FirstOrDefault(x => x.Name == "class");
            if (classAtttibute != null && classAtttibute.Value.Contains("ck-tablestyle-"))
            {
                applyPlanCustomTable = true;
                break;
            }
        }
        return applyPlanCustomTable;
    }



    private List<HtmlNode> GetTableRows(HtmlNode table, string type)
    {
        List<HtmlNode> nodesList = new List<HtmlNode>();
        if (table.ChildNodes.Any(x => x.Name == type))
        {
            var tableheadNodes = table.ChildNodes.Where(x => x.Name == type);
            foreach (var item in tableheadNodes)
            {
                var rowNodes = item.ChildNodes.Where(x => x.Name == "tr");
                foreach (var row in rowNodes)
                {
                    HtmlNode newRow = HtmlNode.CreateNode("<tr></tr>");
                    if (row.Attributes.Any()) newRow.Attributes.AddRange(row.Attributes);
                    var tableHeads = row.ChildNodes;
                    foreach (var ths in tableHeads)
                    {
                        var dataNode = GetInnerHtml(ths);
                        foreach (var attr in ths.Attributes)
                        {
                            dataNode.Attributes.Add(attr.Name, attr.Value);
                        }
                        newRow.ChildNodes.Add(dataNode);
                    }
                    if (newRow.ChildNodes.Any(x => x.Name == "td") || newRow.ChildNodes.Any(x => x.Name == "th"))
                    {
                        nodesList.Add(newRow);
                    }
                }
            }
        }
        return nodesList;
    }



    private HtmlNode GetInnerHtml(HtmlNode node)
    {
        return node.Name == "th" ? HtmlNode.CreateNode($"<th>{node.InnerHtml}</th>") : HtmlNode.CreateNode($"<td>{node.InnerHtml}</td>");
    }



    private string GetAlignmentClassName(string style)
    {
        if (style.Contains("text-align:right"))
        {
            return classRightAlign;
        }
        else if (style.Contains("text-align:left"))
        {
            return classLeftAlign;
        }
        else if (style.Contains("text-align:center"))
        {
            return classCenterAlign;
        }
        else if (style.Contains("text-align:justify"))
        {
            return classJustifyAlign;
        }
        else
        {
            return string.Empty;
        }
    }



    private string GetWebWidth(List<int> widthList, int position)
    {
        if (widthList.Any())
        {
            if (widthList[position] == 0)
            {
                return string.Empty;
            }
            else
            {
                return $"width:{widthList[position]}%;";
            }
        }
        else
        {
            return string.Empty;
        }
    }



    private static List<bool> GetColumnsRightAlignmentStatus(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(nodeContent);
        var html = htmlDoc.DocumentNode.ChildNodes;
        var resultNodeContent = string.Empty;
        var keyValuePairs = new List<bool>();
        string innerText = string.Empty;
        foreach (var item in html)
        {
            string tableElement = item.OuterHtml.ToString();
            var htmlDoc1 = new HtmlDocument();
            htmlDoc1.LoadHtml(tableElement);
            if (item.Name.ToLower() == "table".ToLower())
            {
                if (item.Attributes.Contains("class") && item.Attributes["class"].Value.Contains("cke-table-custom3"))
                {
                    var htmlrows = htmlDoc1.DocumentNode.SelectNodes("//tr");
                    var count = htmlrows.Count;
                    for (int i = 0; i < htmlrows.Count; i++)
                    {
                        if (i != 0)
                        {
                            for (int j = 0; j < htmlrows[i].ChildNodes.Count; j++)
                            {
                                Int64 number = 0;
                                if (htmlrows[i].ChildNodes[j].Name == "td")
                                {
                                    List<string> attList = new List<string>();
                                    if (htmlrows[i].ChildNodes[j].Attributes.Count == 0)
                                    {
                                        if (i == count - 1)
                                        {
                                            if (count == 2)
                                            {
                                                htmlrows[i].ChildNodes[j].SetAttributeValue("style", "text-align:right");
                                            }
                                            else
                                            {
                                                innerText = htmlrows[i - 1].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[i - 1].ChildNodes[j].InnerText.Replace(",", "");
                                                if (Int64.TryParse(htmlrows[i - 1].ChildNodes[j].InnerText, out number))
                                                {
                                                    htmlrows[i].ChildNodes[j].SetAttributeValue("style", "text-align:right");
                                                }
                                            }
                                            keyValuePairs.Add(true);
                                        }
                                        else
                                        {
                                            var rowCount = htmlrows.Count;
                                            List<bool> collection = new List<bool>(); Int64 z1 = 0;

                                            for (int a = 1; a < htmlrows.Count; a++)
                                            {
                                                innerText = htmlrows[a].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[a].ChildNodes[j].InnerText.Replace(",", "");
                                                if (Int64.TryParse(innerText, out z1))
                                                {
                                                    collection.Add(true);
                                                }
                                                else
                                                {
                                                    collection.Add(false);
                                                }
                                            }
                                            keyValuePairs.Add((collection.Where(x => x == true).Count() > collection.Where(x => x == false).Count()));
                                        }
                                    }
                                    else
                                    {
                                        bool isCollectionInitiated = false;
                                        foreach (var attr in htmlrows[i].ChildNodes[j].Attributes)
                                        {
                                            if (!isCollectionInitiated)
                                            {
                                                if (i == count - 1)
                                                {
                                                    keyValuePairs.Add(true);
                                                }
                                                else
                                                {
                                                    var rowCount = htmlrows.Count;
                                                    List<bool> collection = new List<bool>(); Int64 z1 = 0;

                                                    for (int a = 1; a < htmlrows.Count; a++)
                                                    {
                                                        if (htmlrows[a].ChildNodes.Count > j)// double check the array
                                                        {
                                                            innerText = htmlrows[a].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[a].ChildNodes[j].InnerText.Replace(",", "");
                                                            if (Int64.TryParse(innerText, out z1))
                                                            {
                                                                collection.Add(true);
                                                            }
                                                            else
                                                            {
                                                                collection.Add(false);
                                                            }
                                                        }
                                                    }
                                                    keyValuePairs.Add((collection.Where(x => x == true).Count() > collection.Where(x => x == false).Count()));
                                                }
                                            }
                                            isCollectionInitiated = true;
                                        }
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
        return keyValuePairs;
    }



    private string RemoveSpanColorforHeaderTags(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(nodeContent);
        List<string> headerTags = new List<string>() { "h1", "h2", "h3", "h4", "h5", "h6" };
        foreach (var item in headerTags)
        {
            var headersTagsSectionList = htmlDoc.DocumentNode.SelectNodes(item);
            if (headersTagsSectionList != null)
            {
                foreach (var htag in headersTagsSectionList)
                {
                    foreach (var sp in htag.ChildNodes)
                    {
                        if (sp.Name.ToLower() == "span".ToLower())
                        {
                            bool removeColorStyle = false;
                            List<string> attList = new List<string>();
                            foreach (var attr in sp.Attributes)
                            {
                                attList = attr.Value.Split(';').ToList();
                                foreach (var a in attList)
                                {
                                    if (a.ToLower().Contains("color"))
                                    {
                                        attList.Remove(a);
                                        removeColorStyle = true;
                                        break;
                                    }
                                }
                            }
                            if (removeColorStyle)
                            {
                                sp.SetAttributeValue("style", string.Join(";", attList));
                            }
                        }
                    }
                }
            }
        }
        nodeContent = htmlDoc.DocumentNode.OuterHtml.ToString();
        return nodeContent;
    }



    private static string ProcessFramsikt3Styles(string nodeContent)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(nodeContent);
        var html = htmlDoc.DocumentNode.ChildNodes;
        var resultNodeContent = string.Empty;
        var innerText = string.Empty;
        foreach (var item in html)
        {
            string tableElement = item.OuterHtml.ToString();
            var htmlDoc1 = new HtmlDocument();
            htmlDoc1.LoadHtml(tableElement);
            if (item.Name.ToLower() == "table".ToLower())
            {
                if (item.Attributes.Contains("class") && item.Attributes["class"].Value.Contains("cke-table-custom3"))
                {
                    var htmlrows = htmlDoc1.DocumentNode.SelectNodes("//tr");
                    var count = htmlrows.Count;
                    for (int i = 0; i < htmlrows.Count; i++)
                    {
                        if (i != 0)
                        {
                            for (int j = 0; j < htmlrows[i].ChildNodes.Count; j++)
                            {
                                if (htmlrows[i].ChildNodes[j].Name == "td")
                                {
                                    Int64 number = 0;
                                    var text = htmlrows[i].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[i].ChildNodes[j].InnerText.Replace(",", "");
                                    if (Int64.TryParse(text, out number))
                                    {
                                        List<string> attList = new List<string>();
                                        if (htmlrows[i].ChildNodes[j].Attributes.Count == 0)
                                        {
                                            if (i == count - 1)
                                            {
                                                if (count == 2)
                                                {
                                                    htmlrows[i].ChildNodes[j].SetAttributeValue("style", "text-align:right !important");
                                                }
                                                else
                                                {
                                                    innerText = htmlrows[i - 1].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[i - 1].ChildNodes[j].InnerText.Replace(",", "");
                                                    if (Int64.TryParse(innerText, out number))
                                                    {
                                                        htmlrows[i].ChildNodes[j].SetAttributeValue("style", "text-align:right !important");
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                var rowCount = htmlrows.Count;
                                                List<bool> collection = new List<bool>(); Int64 z1 = 0;
                                                for (int a = 1; a < htmlrows.Count; a++)
                                                {
                                                    innerText = htmlrows[a].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[a].ChildNodes[j].InnerText.Replace(",", "");
                                                    if (Int64.TryParse(innerText, out z1))
                                                    {
                                                        collection.Add(true);
                                                    }
                                                    else
                                                    {
                                                        collection.Add(false);
                                                    }
                                                }

                                                if (collection.Where(x => x == true).Count() > collection.Where(x => x == false).Count())
                                                {
                                                    htmlrows[i].ChildNodes[j].SetAttributeValue("style", "text-align:right !important");
                                                }
                                            }
                                        }
                                        else
                                        {
                                            foreach (var attr in htmlrows[i].ChildNodes[j].Attributes)
                                            {
                                                if (attr.Name.ToLower() == "style".ToLower())
                                                {
                                                    if (i == count - 1)
                                                    {
                                                        if (count == 2)
                                                        {
                                                            attr.Value = attr.Value.Replace("; text-align:right", "").Replace("; text-align:left", "");
                                                            htmlrows[i].ChildNodes[j].SetAttributeValue("style", string.Join(" ", attr.Value, ";text-align:right !important"));
                                                        }
                                                        else
                                                        {
                                                            innerText = htmlrows[i - 1].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[i - 1].ChildNodes[j].InnerText.Replace(",", "");
                                                            if (Int64.TryParse(innerText, out number))
                                                            {
                                                                attr.Value = attr.Value.Replace("; text-align:right", "").Replace("; text-align:left", "");
                                                                htmlrows[i].ChildNodes[j].SetAttributeValue("style", string.Join(" ", attr.Value, ";text-align:right !important"));
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        var rowCount = htmlrows.Count;
                                                        List<bool> collection = new List<bool>(); Int64 z1 = 0;
                                                        for (int a = 1; a < htmlrows.Count; a++)
                                                        {
                                                            innerText = htmlrows[a].ChildNodes[j].InnerText.ToLower() == "&nbsp;".ToLower() ? "0" : htmlrows[a].ChildNodes[j].InnerText.Replace(",", "");
                                                            if (Int64.TryParse(innerText, out z1))
                                                            {
                                                                collection.Add(true);
                                                            }
                                                            else
                                                            {
                                                                collection.Add(false);
                                                            }
                                                        }

                                                        if (collection.Where(x => x == true).Count() > collection.Where(x => x == false).Count())
                                                        {
                                                            attr.Value = attr.Value.Replace("; text-align:right", "").Replace("; text-align:left", "");
                                                            htmlrows[i].ChildNodes[j].SetAttributeValue("style", string.Join(" ", attr.Value, ";text-align:right !important"));
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    resultNodeContent = string.IsNullOrEmpty(resultNodeContent) ? htmlDoc1.DocumentNode.OuterHtml.ToString() : string.Concat("", resultNodeContent, htmlDoc1.DocumentNode.OuterHtml.ToString());
                }
                else
                {
                    resultNodeContent = string.IsNullOrEmpty(resultNodeContent) ? htmlDoc1.DocumentNode.OuterHtml.ToString() : string.Concat("", resultNodeContent, htmlDoc1.DocumentNode.OuterHtml.ToString());
                }
            }
            else
            {
                resultNodeContent = string.IsNullOrEmpty(resultNodeContent) ? htmlDoc1.DocumentNode.OuterHtml.ToString() : string.Concat("", resultNodeContent, htmlDoc1.DocumentNode.OuterHtml.ToString());
            }
        }
        nodeContent = resultNodeContent;
        return nodeContent;
    }

}