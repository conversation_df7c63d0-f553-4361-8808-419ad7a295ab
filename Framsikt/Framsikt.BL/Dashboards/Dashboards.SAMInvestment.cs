using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Globalization;

namespace Framsikt.BL;

public partial class Dashboard
{


    public async Task<JObject> GetSAMStatusTableAsync(string userId, SAMStatusInput input)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        JObject result = new JObject();
        int maxForecastPeriod = await GetMaxPeriodForSAMStatusAsync(input.forecastPeriod, input.budgetYear, userId);
        input.forecastPeriod = maxForecastPeriod;

        result.Add("data", JArray.FromObject(await GetSAMStatusTableDataAsync(userId, input)));
        result.Add("column", await GetSAMStatusTableColumns(userId));
        if ((maxForecastPeriod % 100) != 0)
        {
            result.Add("period", getFullName(DateTime.ParseExact(maxForecastPeriod.ToString(), "yyyyMM", CultureInfo.InvariantCulture).Month, userDetails.language_preference));
        }
        else
        {
            result.Add("period", string.Empty);
        }
        return result;
    }



    public async Task<dynamic> GetSAMStatusGraphAsync(string userId, SAMStatusInput input)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        var userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "DashBoard");

        int maxForecastPeriod = await GetMaxPeriodForSAMStatusAsync(input.forecastPeriod, input.budgetYear, userId);
        input.forecastPeriod = maxForecastPeriod;

        var SAMStatusData = await GetSAMStatusTableDataAsync(userId, input);

        dynamic resultObj = new JObject();

        dynamic plotArea = new JObject();
        plotArea.width = 300;
        plotArea.height = 300;
        resultObj.plotArea = plotArea;

        dynamic title = new JObject();
        title.visible = false;
        resultObj.title = title;

        dynamic legend = new JObject();
        legend.visible = false;
        resultObj.legend = legend;

        dynamic seriesDefaults = new JObject();
        seriesDefaults.type = "pie";

        dynamic overlay = new JObject();
        overlay.gradient = "none";
        seriesDefaults.overlay = overlay;

        dynamic labels = new JObject();
        labels.font = "14px semiFont, sans-serif";
        labels.position = "center";
        labels.color = "#000";
        labels.visible = true;
        labels.background = "transparent";
        labels.template = "#= category #: \n #= kendo.format('{0:n0}', value) #";
        seriesDefaults.labels = labels;
        resultObj.seriesDefaults = seriesDefaults;

        dynamic series = new JArray();

        dynamic seriesObj = new JObject();
        dynamic seriesObjLabels = new JObject();
        seriesObjLabels.align = "circle";
        seriesObjLabels.distance = 20;
        seriesObj.labels = seriesObjLabels;
        seriesObj.visual = "";

        dynamic data = new JArray();

        int count = 0;
        foreach (var s in SAMStatusData.Where(x => x.parentId == null && x.id != 1 && x.id != -1))
        {
            dynamic obj = new JObject();

            obj.category = s.statusType;

            obj.value = s.statusAmount;

            obj.explode = false;
            obj.color = _utility.GetSAMStatusColors().FirstOrDefault(x => x.Key == s.id).Value;
            data.Add(obj);
            count++;
        }

        seriesObj.data = data;
        series.Add(seriesObj);
        resultObj.series = series;

        dynamic tooltip = new JObject();
        tooltip.visible = true;
        tooltip.template = "#= category #: \n #= kendo.format('{0:n0}', value) #";
        resultObj.tooltip = tooltip;
        if ((maxForecastPeriod % 100) != 0)
        {
            resultObj.period = getFullName(DateTime.ParseExact(maxForecastPeriod.ToString(), "yyyyMM", CultureInfo.InvariantCulture).Month, userDetails.language_preference);
        }
        else
        {
            resultObj.period = string.Empty;
        }
        return resultObj;
    }



    public async Task<JObject> YBInvestment(string userId, YBInvDashBoardGridInput input)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        JObject result = new JObject();
        if (!input.isHeaderCall)
        {
            result.Add("data", JArray.FromObject(await YBInvestmentData(userId, input)));
        }
        result.Add("column", await GetYBInvestmentTableColumns(userId));
        return result;
    }



    public async Task<List<YBInvestmentStatus>> YBInvestmentData(string userId, YBInvDashBoardGridInput input)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var statusData = (from di in dbContext.vw_dash_investments
            where di.fk_tenant_id == userDetails.tenant_id && di.budget_year == input.budgetYear && di.period == input.forecastPeriod
            select new YBInvestmentStatus
            {
                mainProjCode = di.pk_main_project_code,
                mainProjName = di.pk_main_project_code + "-" + di.main_project_name,
                projectCode = di.fk_project_code,
                projectName = di.fk_project_code + "-" + di.project_name,
                levelDescription = di.level_1_description,
                levelId = di.level_1_id,
                accountingYTD = di.accounting_ytd,
                originalBudget = di.original_budget,
                revisedBudget = di.rev_budget,
                orgId_1 = di.org_id_1,
                orgId_2 = di.org_id_2,
                orgId_3 = di.org_id_3,
                orgId_4 = di.org_id_4,
                orgId_5 = di.org_id_5,
                serviceId = di.service_id_2,
                isExpence = di.is_expence
            });
        switch (input.orgLevel)
        {
            case 1:
                statusData = statusData.Where(x => x.orgId_1 == input.orgId);
                break;

            case 2:
                statusData = statusData.Where(x => x.orgId_2 == input.orgId);

                break;

            case 3:
                statusData = statusData.Where(x => x.orgId_3 == input.orgId);

                break;

            case 4:
                statusData = statusData.Where(x => x.orgId_4 == input.orgId);

                break;

            case 5:
                statusData = statusData.Where(x => x.orgId_5 == input.orgId);

                break;
        }
        if (!string.IsNullOrEmpty(input.serviceId))
        {
            statusData = statusData.Where(x => x.serviceId == input.serviceId);
        }

        if (input.showOnlyInvExp)
        {
            statusData = statusData.Where(x => x.isExpence == 1);
        }

        var filteredData = await statusData.ToListAsync();

        var finalResult = (from sd in filteredData
            group sd by new { sd.mainProjCode, sd.mainProjName, sd.projectCode, sd.projectName, sd.levelId, sd.levelDescription } into grp
            select new YBInvestmentStatus
            {
                mainProjCode = grp.Key.mainProjCode,
                mainProjName = grp.Key.mainProjName,
                projectCode = grp.Key.projectCode,
                projectName = grp.Key.projectName,
                levelDescription = grp.Key.levelDescription,
                levelId = grp.Key.levelId,
                originalBudget = grp.Sum(x => x.originalBudget),
                revisedBudget = grp.Sum(x => x.revisedBudget),
                accountingYTD = grp.Sum(x => x.accountingYTD),
                residualbudget = grp.Sum(x => x.revisedBudget) - grp.Sum(x => x.accountingYTD),
                consumption = grp.Sum(x => x.revisedBudget) == 0 ? 0 : (grp.Sum(x => x.accountingYTD) / grp.Sum(x => x.revisedBudget)),
                numberFormat = "n0"
            }).ToList().Where(x => x.originalBudget != 0 || x.revisedBudget != 0 || x.accountingYTD != 0 || x.residualbudget != 0 || x.consumption != 0).ToList();

        List<YBInvestmentStatus> formattedData = new List<YBInvestmentStatus>();
        //1st level data
        if (!input.isChildExpandData)
        {
            formattedData.AddRange(finalResult.GroupBy(x => new { x.mainProjCode, x.mainProjName }).Select(y => new YBInvestmentStatus
            {
                id = $"{y.Key.mainProjCode}_",
                parentId = null,
                projectList = y.Key.mainProjName,
                originalBudget = y.Sum(z => z.originalBudget),
                revisedBudget = y.Sum(z => z.revisedBudget),
                accountingYTD = y.Sum(z => z.accountingYTD),
                residualbudget = y.Sum(x => x.revisedBudget) - y.Sum(x => x.accountingYTD),
                consumption = y.Sum(x => x.revisedBudget) == 0 ? 0 : (y.Sum(x => x.accountingYTD) / y.Sum(x => x.revisedBudget)) * 100,
                hasChildren = true,
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList());
        }
        else //2nd and 3rd level data
        {
            var parentIds = input.parentId.Split('_');
            if (parentIds.Length > 1 && parentIds[1] == string.Empty)
            {
                formattedData.AddRange(finalResult.Where(x => x.mainProjCode == parentIds[0]).GroupBy(x => new { x.mainProjCode, x.projectCode, x.projectName }).Select(y => new YBInvestmentStatus
                {
                    id = $"{y.Key.mainProjCode}_{y.Key.projectCode}",
                    parentId = $"{y.Key.mainProjCode}_",
                    projectList = y.Key.projectName,
                    originalBudget = y.Sum(z => z.originalBudget),
                    revisedBudget = y.Sum(z => z.revisedBudget),
                    accountingYTD = y.Sum(z => z.accountingYTD),
                    residualbudget = y.Sum(x => x.revisedBudget) - y.Sum(x => x.accountingYTD),
                    consumption = y.Sum(x => x.revisedBudget) == 0 ? 0 : (y.Sum(x => x.accountingYTD) / y.Sum(x => x.revisedBudget)) * 100,
                    hasChildren = true,
                    numberFormat = "n0",
                    columnClass = "dashTableNormal"
                }).ToList());
            }
            else if (parentIds.Length > 1 && parentIds[1] != string.Empty)
            {
                formattedData.AddRange(finalResult.Where(x => x.mainProjCode == parentIds[0] && x.projectCode == parentIds[1]).GroupBy(x => new { x.mainProjCode, x.projectCode, x.projectName, x.levelDescription, x.levelId }).Select(y => new YBInvestmentStatus
                {
                    id = $"{y.Key.mainProjCode}_{y.Key.projectCode}_{y.Key.levelId}",
                    parentId = $"{y.Key.mainProjCode}_{y.Key.projectCode}",
                    projectList = y.Key.levelDescription,
                    originalBudget = y.Sum(z => z.originalBudget),
                    revisedBudget = y.Sum(z => z.revisedBudget),
                    accountingYTD = y.Sum(z => z.accountingYTD),
                    residualbudget = y.Sum(x => x.revisedBudget) - y.Sum(x => x.accountingYTD),
                    consumption = y.Sum(x => x.revisedBudget) == 0 ? 0 : (y.Sum(x => x.accountingYTD) / y.Sum(x => x.revisedBudget)) * 100,
                    hasChildren = false,
                    numberFormat = "n0",
                    columnClass = "dashTableNormal"
                }).ToList());
            }
        }
        if (!input.isChildExpandData) //Summary
        {
            var langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "DashBoard");
            formattedData.Add(new YBInvestmentStatus
            {
                id = "-1",
                parentId = null,
                projectList = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_Total".ToLower()).Value.LangText,
                originalBudget = formattedData.Where(x => x.parentId == null).Sum(z => z.originalBudget),
                revisedBudget = formattedData.Where(x => x.parentId == null).Sum(z => z.revisedBudget),
                accountingYTD = formattedData.Where(x => x.parentId == null).Sum(z => z.accountingYTD),
                residualbudget = formattedData.Where(x => x.parentId == null).Sum(x => x.revisedBudget) - formattedData.Where(x => x.parentId == null).Sum(x => x.accountingYTD),
                consumption = formattedData.Where(x => x.parentId == null).Sum(x => x.revisedBudget) == 0 ? 0 : (formattedData.Where(x => x.parentId == null).Sum(x => x.accountingYTD) / formattedData.Where(x => x.parentId == null).Sum(x => x.revisedBudget)) * 100,
                hasChildren = false,
                numberFormat = "n0",
                columnClass = "dashTableSemiBorder"
            });
        }
        return formattedData;
    }



    private async Task<JArray> GetSAMStatusTableColumns(string userId)
    {
        List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
        var userdetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "DashBoard");

        GridColumnHelper columnInfo = new GridColumnHelper
        {
            field = "statusType",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusType".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:left; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:left;" },
            template = null,
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        columnInfo = new GridColumnHelper
        {
            field = "statusAmount",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusAmount".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:right; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:right;" },
            template = "# if(statusAmount != null) {# #=kendo.toString(statusAmount, numberFormat)# #} #",
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        return JArray.FromObject(formattedColumns);
    }



    private async Task<JArray> GetYBInvestmentTableColumns(string userId)
    {
        List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
        var userdetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "DashBoard");

        GridColumnHelper columnInfo = new GridColumnHelper
        {
            field = "projectList",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_Project".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:left; white-space: normal;vertical-align:top; border-left:none;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:left;" },
            template = null,
            filterable = null,
            sortable = true,
            headerTemplate = null
        };
        formattedColumns.Add(columnInfo);
        columnInfo = new GridColumnHelper
        {
            field = "originalBudget",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_OriginalBudget".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:right; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:right;" },
            template = "# if(originalBudget != null) {# #=kendo.toString(originalBudget, numberFormat)# #} #",
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        columnInfo = new GridColumnHelper
        {
            field = "revisedBudget",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_RevBudget".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:right; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:right;" },
            template = "# if(revisedBudget != null) {# #=kendo.toString(revisedBudget, numberFormat)# #} #",
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        columnInfo = new GridColumnHelper
        {
            field = "accountingYTD",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_accountingYTD".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:right; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:right;" },
            template = "# if(accountingYTD != null) {# #=kendo.toString(accountingYTD, numberFormat)# #} #",
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        columnInfo = new GridColumnHelper
        {
            field = "residualbudget",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_residualbudget".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = null,
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:right; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:right;" },
            template = "# if(residualbudget != null) {# #=kendo.toString(residualbudget, numberFormat)# #} #",
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        columnInfo = new GridColumnHelper
        {
            field = "consumption",
            title = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_YBInv_consumption".ToLower()).Value.LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 100,
            format = "{0:n1}%",
            expandable = false,
            attributes = new ColumnStyleHelper() { style = "text-align:right; white-space: normal;vertical-align:top;" },
            headerAttributes = new ColumnStyleHelper() { style = "text-align:right;" },
            template = null,
            filterable = null,
            sortable = true,
            headerTemplate = null,
        };
        formattedColumns.Add(columnInfo);
        return JArray.FromObject(formattedColumns);
    }



    private async Task<List<SAMStatusData>> GetSAMStatusTableDataAsync(string userId, SAMStatusInput input)
    {
        var userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "DashBoard");

        List<SAMStatusRowData> row_data = await GetSAMStatusRowDataAsync(userId, input);

        List<SAMStatusData> finalData = new List<SAMStatusData>();
        int counter = 10;

        var data_final_amt = (from rd in row_data
            group rd by new { rd.action_name } into grp
            select new SAMStatusData
            {
                parentId = 1,
                id = counter + 1,
                statusType = grp.Key.action_name,
                statusAmount = grp.Sum(x => x.final_amt),
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList();

        if (data_final_amt.Any(x => x.statusAmount != 0))
        {
            finalData.Add(new SAMStatusData
            {
                parentId = null,
                id = 1,
                statusType = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusFinalAmount".ToLower()).Value.LangText,
                statusAmount = data_final_amt.Where(x => x.parentId == 1).Sum(x => x.statusAmount),
                numberFormat = "n0",
                columnClass = "dashTableSemi"
            });
            finalData.AddRange(data_final_amt.Where(x => x.statusAmount != 0).ToList());
        }
        counter = finalData.Any() ? finalData.Max(x => x.id) + 1 : counter;
        var data_prev_reversal = (from rd in row_data
            group rd by new { rd.action_name } into grp
            select new SAMStatusData
            {
                parentId = 2,
                id = counter + 1,
                statusType = grp.Key.action_name,
                statusAmount = grp.Sum(x => x.prev_reversal),
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList();
        if (data_prev_reversal.Any(x => x.statusAmount != 0))
        {
            finalData.Add(new SAMStatusData
            {
                parentId = null,
                id = 2,
                statusType = input.isGraph ? langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusPreRev_graph".ToLower()).Value.LangText : langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusPreRev".ToLower()).Value.LangText,
                statusAmount = data_prev_reversal.Where(x => x.parentId == 2).Sum(x => x.statusAmount),
                numberFormat = "n0",
                columnClass = "dashTableSemi"
            });
            finalData.AddRange(data_prev_reversal.Where(x => x.statusAmount != 0).ToList());
        }
        counter = finalData.Any() ? finalData.Max(x => x.id) + 1 : counter;
        var data_fund_reversal_amt_f = (from rd in row_data
            group rd by new { rd.action_name } into grp
            select new SAMStatusData
            {
                parentId = 3,
                id = counter + 1,
                statusType = grp.Key.action_name,
                statusAmount = grp.Sum(x => x.fund_reversal_amt_f),
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList();
        if (data_fund_reversal_amt_f.Any(x => x.statusAmount != 0))
        {
            finalData.Add(new SAMStatusData
            {
                parentId = null,
                id = 3,
                statusType = input.isGraph ? langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusFundRevAmtF_graph".ToLower()).Value.LangText : langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusFundRevAmtF".ToLower()).Value.LangText,
                statusAmount = data_fund_reversal_amt_f.Where(x => x.parentId == 3).Sum(x => x.statusAmount),
                numberFormat = "n0",
                columnClass = "dashTableSemi"
            });
            finalData.AddRange(data_fund_reversal_amt_f.Where(x => x.statusAmount != 0).ToList());
        }
        counter = finalData.Any() ? finalData.Max(x => x.id) + 1 : counter;
        var data_prev_withdrawal_amt = (from rd in row_data
            group rd by new { rd.action_name } into grp
            select new SAMStatusData
            {
                parentId = 4,
                id = counter + 1,
                statusType = grp.Key.action_name,
                statusAmount = grp.Sum(x => x.prev_withdrawal_amt),
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList();
        if (data_prev_withdrawal_amt.Any(x => x.statusAmount != 0))
        {
            finalData.Add(new SAMStatusData
            {
                parentId = null,
                id = 4,
                statusType = input.isGraph ? langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_prev_withdrawal_amt_graph".ToLower()).Value.LangText : langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_prev_withdrawal_amt".ToLower()).Value.LangText,
                statusAmount = data_prev_withdrawal_amt.Where(x => x.parentId == 4).Sum(x => x.statusAmount),
                numberFormat = "n0",
                columnClass = "dashTableSemi"
            });
            finalData.AddRange(data_prev_withdrawal_amt.Where(x => x.statusAmount != 0).ToList());
        };

        counter = finalData.Any() ? finalData.Max(x => x.id) + 1 : counter;
        var data_partial_reversal_amt_f = (from rd in row_data
            group rd by new { rd.action_name } into grp
            select new SAMStatusData
            {
                parentId = 5,
                id = counter + 1,
                statusType = grp.Key.action_name,
                statusAmount = grp.Sum(x => x.partial_reversal_amt_f),
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList();
        if (data_partial_reversal_amt_f.Any(x => x.statusAmount != 0))
        {
            finalData.Add(new SAMStatusData
            {
                parentId = null,
                id = 5,
                statusType = input.isGraph ? langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusPartialRevAmtF_graph".ToLower()).Value.LangText : langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusPartialRevAmtF".ToLower()).Value.LangText,
                statusAmount = data_partial_reversal_amt_f.Where(x => x.parentId == 5).Sum(x => x.statusAmount),
                numberFormat = "n0",
                columnClass = "dashTableSemi"
            });
            finalData.AddRange(data_partial_reversal_amt_f.Where(x => x.statusAmount != 0).ToList());
        }

        counter = finalData.Any() ? finalData.Max(x => x.id) + 1 : counter;
        var data_partial_reversal_2_amt_f = (from rd in row_data
            group rd by new { rd.action_name } into grp
            select new SAMStatusData
            {
                parentId = 6,
                id = counter + 1,
                statusType = grp.Key.action_name,
                statusAmount = grp.Sum(x => x.partial_reversal_2_amt_f),
                numberFormat = "n0",
                columnClass = "dashTableNormal"
            }).ToList();
        if (data_partial_reversal_2_amt_f.Any(x => x.statusAmount != 0))
        {
            finalData.Add(new SAMStatusData
            {
                parentId = null,
                id = 6,
                statusType = input.isGraph ? langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusParRev2AmtF_Graph".ToLower()).Value.LangText : langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_StatusParRev2AmtF".ToLower()).Value.LangText,
                statusAmount = data_partial_reversal_2_amt_f.Where(x => x.parentId == 6).Sum(x => x.statusAmount),
                numberFormat = "n0",
                columnClass = "dashTableSemi"
            });
            finalData.AddRange(data_partial_reversal_2_amt_f.Where(x => x.statusAmount != 0).ToList());
        }

        finalData.Add(new SAMStatusData
        {
            parentId = null,
            id = -1,
            statusType = langString.FirstOrDefault(x => x.Key.ToLower() == "DB_SAMSatus_grandTotal".ToLower()).Value.LangText,
            statusAmount = finalData.Where(x => x.parentId == null && x.id != -1).Sum(x => x.statusAmount),
            numberFormat = "n0",
            columnClass = "dashTableSemiBorder"
        });
        return finalData;
    }



    private int GetMaxPeriodForSAMStatus(int forecastPeriod, int budgetYear, string userId)
    {
        return GetMaxPeriodForSAMStatusAsync(forecastPeriod, budgetYear, userId).GetAwaiter().GetResult();
    }



    private async Task<int> GetMaxPeriodForSAMStatusAsync(int forecastPeriod, int budgetYear, string userId)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        var userDetails = await _utility.GetUserDetailsAsync(userId);
        int startPeriod = Convert.ToInt32(budgetYear.ToString() + "00");

        if (await dbContext.tsam_period_status.AnyAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period >= startPeriod && x.forecast_period <= forecastPeriod && x.status == 3))
        {
            return await dbContext.tsam_period_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period >= budgetYear && x.forecast_period <= forecastPeriod && x.status == 3).MaxAsync(y => y.forecast_period);
        }
        else
        {
            return forecastPeriod;
        }
    }



    private List<SAMStatusRowData> GetSAMStatusRowData(string userId, SAMStatusInput input)
    {
        return GetSAMStatusRowDataAsync(userId, input).GetAwaiter().GetResult();
    }



    private async Task<List<SAMStatusRowData>> GetSAMStatusRowDataAsync(string userId, SAMStatusInput input)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        var userDetails = await _utility.GetUserDetailsAsync(userId);

        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(input.budgetYear, 1));
        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
        List<List<string>> DepartmentsAndFunctions =
            _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, input.orgId, input.serviceId,
                tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);

        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

        List<string> lstAllDepartments = await _utility.GetDepartmentsAsync(orgVersionContent, userId, input.orgId, input.orgLevel, input.serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);

        var row_data = (from tsp in dbContext.tsam_period_transactions
            join th in dbContext.tfp_trans_header on new { a = tsp.fk_tenant_id, b = tsp.fk_action_id }
                equals new { a = th.fk_tenant_id, b = th.pk_action_id }
            where th.fk_tenant_id == userDetails.tenant_id && tsp.forecast_period == input.forecastPeriod
            group tsp by new { th.description, tsp.department_code, tsp.fk_function_code } into grp
            select new SAMStatusRowData
            {
                action_name = grp.Key.description,
                department_code = grp.Key.department_code,
                fk_function_code = grp.Key.fk_function_code,
                final_amt = grp.Sum(x => x.final_amt) * (-1),
                prev_reversal = grp.Sum(x => x.Prev_reversal) + grp.Sum(x => x.reversal_amt_f),
                prev_withdrawal_amt = grp.Sum(x => x.prev_withdrawal_amt) + grp.Sum(x => x.withdrawal_amt_f),
                fund_reversal_amt_f = grp.Sum(x => x.prev_fund_reversal_amt) + grp.Sum(x => x.fund_reversal_amt_f),
                partial_reversal_amt_f = grp.Sum(x => x.partial_reversal_amt_f),
                partial_reversal_2_amt_f = grp.Sum(x => x.partial_reversal_2_amt_f),
            });

        if (lstAllDepartments.Any(x => !string.IsNullOrEmpty(x)) && lstAllFunctions.Any(x => !string.IsNullOrEmpty(x)))
        {
            row_data = row_data.Where(y => lstAllDepartments.Contains(y.department_code) && lstAllFunctions.Contains(y.fk_function_code));
        }
        else if (lstAllFunctions.Any(x => !string.IsNullOrEmpty(x)))
        {
            row_data = row_data.Where(y => lstAllFunctions.Contains(y.fk_function_code));
        }
        else if (lstAllDepartments.Any(x => !string.IsNullOrEmpty(x)))
        {
            row_data = row_data.Where(y => lstAllDepartments.Contains(y.department_code));
        }

        return await row_data.ToListAsync();
    }

}