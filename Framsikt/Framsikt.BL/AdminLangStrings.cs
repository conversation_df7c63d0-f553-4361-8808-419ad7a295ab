#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL
{
    public class AdminLangStrings : IAdminLangStrings
    {
        private readonly IUtility _utility;
        private readonly IAppDataCache _cache;
        private const string languageId = "nb-NO";
        private const string ngPrefix = "ng_";

        public AdminLangStrings(IUtility util, IAppDataCache cache)
        {
            _utility = util;
            _cache = cache;
        }

        public async Task<JObject> GetLangStringsWithOverrides(string userId, int tenantId, string context,
            string defaultDesc, string tenantDesc, string id, int take, int skip)
        {
            JObject ret;
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            var userRoles = await _utility.GetUserRolesAsync(userId);
            string languagePref = userDetails.language_preference;
            if (tenantId != userDetails.tenant_id && !userRoles.Contains("Admin"))
            {
                throw new InvalidOperationException("Not authorized");
            }
            //Get data only if one of the filter parameters are provided
            if (!(string.IsNullOrEmpty(context) && string.IsNullOrEmpty(defaultDesc) &&
                  string.IsNullOrEmpty(tenantDesc) && string.IsNullOrEmpty(id)))
            {
                //var dataQuery = (from ls in dbContext.gco_language_strings
                //                 where ls.Language == languagePref
                //                 join or in (from o in dbContext.gco_language_string_overrides_tenant
                //                             where o.fk_tenant_id == tenantId && o.Language == languagePref
                //                             select new
                //                             {
                //                                 o.ID,
                //                                 o.Language,
                //                                 o.Description,
                //                                 o.fk_tenant_id,
                //                                 o.context
                //                             })
                //                     on new { a = ls.ID, b = ls.Language } equals new { a = or.ID, b = or.Language }
                //                     into x
                //                 from y in x.DefaultIfEmpty()
                //                 select new LangStringOverride
                //                 {
                //                     Id = ls.ID,
                //                     DefaultDescription = ls.Description,
                //                     TenantDescription = (y == null ? string.Empty : y.Description),
                //                     Context = ls.context,
                //                     TenantId = tenantId
                //                 });

                var dataQuery = (from ls in dbContext.gco_language_strings
                                 join or in dbContext.gco_language_string_overrides_tenant.Where(x => x.fk_tenant_id == tenantId)
                                     on new { a = ls.ID, b = ls.Language } equals new { a = or.ID, b = or.Language }
                                     into x
                                 from y in x.DefaultIfEmpty()
                                 where ls.Language == languagePref
                                 select new LangStringOverride
                                 {
                                     Id = ls.ID,
                                     DefaultDescription = ls.Description,
                                     TenantDescription = (y == null ? string.Empty : y.Description),
                                     Context = ls.context,
                                     TenantId = tenantId
                                 });
                if (!string.IsNullOrEmpty(id))
                {
                    dataQuery = dataQuery.Where(x => x.Id.Contains(id.Trim()));
                }

                if (!string.IsNullOrEmpty(context))
                {
                    dataQuery = dataQuery.Where(x => x.Context.Contains(context.Trim()));
                }

                if (!string.IsNullOrEmpty(defaultDesc))
                {
                    dataQuery = dataQuery.Where(x => x.DefaultDescription.Contains(defaultDesc.Trim()));
                }
                if (!string.IsNullOrEmpty(tenantDesc))
                {
                    dataQuery = dataQuery.Where(x => x.TenantDescription.Contains(tenantDesc.Trim()));
                }
                var data = await dataQuery.OrderBy(x => x.Id).Skip(skip).Take(take).ToListAsync();
                string strData = JsonConvert.SerializeObject(data);
                JArray dataJObject = JArray.Parse(strData);
                ret = new JObject { { "resultData", dataJObject }, { "totalRows", dataQuery.Count() } };
            }
            else
            {
                ret = new JObject { { "resultData", "" }, { "totalRows", 0 } };
            }
            return ret;
        }

        public async Task UpdateLangString(string userId, List<LangStringOverride> stringsToUpate)
        {
            //This service is hard coded to work only for nb-NO
            try
            {
                var dbContext = await _utility.GetTenantDBContextAsync();
                IEnumerable<string> userRoles = await _utility.GetUserRolesAsync(userId);
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                int clientId = userDetails.client_id;
                string languagePref = userDetails.language_preference;
                if (userDetails.tenant_id != stringsToUpate.Select(x => x.TenantId).FirstOrDefault() &&
                    !userRoles.Contains("Admin"))
                {
                    //Only users with Admin role can update other tenant language strings
                    throw new InvalidOperationException("Not allowed. User does not have Admin role");
                }

                foreach (var d in stringsToUpate)
                {
                    gco_language_string_overrides_tenant ls = dbContext.gco_language_string_overrides_tenant.FirstOrDefault(
                        x => x.ID == d.Id &&
                        x.Language == languagePref &&
                        x.fk_tenant_id == d.TenantId);

                    //If the string exists update it
                    if (ls != null)
                    {
                        if (string.IsNullOrEmpty(d.TenantDescription))
                        {
                            //delete the override
                            dbContext.gco_language_string_overrides_tenant.Remove(ls);
                        }
                        else
                        {
                            ls.Description = d.TenantDescription;
                        }
                    }
                    else //Add a new string
                    {
                        //Find the default row
                        gco_language_strings defaultLs = dbContext.gco_language_strings.FirstOrDefault(
                            x => x.ID == d.Id &&
                                 x.Language == languagePref
                            );
                        if (defaultLs == null)
                        {
                            throw new InvalidOperationException("Not found");
                        }

                        gco_language_string_overrides_tenant newOverride = new gco_language_string_overrides_tenant
                        {
                            Description = d.TenantDescription,
                            ID = d.Id,
                            fk_tenant_id = d.TenantId,
                            Language = defaultLs.Language,
                            context = defaultLs.context
                        };
                        dbContext.gco_language_string_overrides_tenant.Add(newOverride);
                    }
                    await dbContext.BulkSaveChangesAsync();
                }
                //Clear the language strings in the cache for affected tenants
                List<int> affectedTenants = stringsToUpate.Select(x => x.TenantId).Distinct().ToList();
                foreach (var tenant in affectedTenants)
                {
                    //_cache.DeleteStringsForTenant(clientId, tenant, "langStr");
                    _cache.ClearClientCache(clientId);
                }
            }
            catch (Exception)
            {
                throw new InvalidOperationException("Not found");
            }
        }

        public async Task<JArray> GetTenants(string userId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            IEnumerable<string> userRoles = await _utility.GetUserRolesAsync(userId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            JArray ret;
            if (userRoles.Contains("Admin"))
            {
                var tenants = await (from t in dbContext.gco_tenants
                                     select new
                                     {
                                         TenantId = t.pk_id,
                                         TenantName = t.tenant_name
                                     }).OrderBy(x => x.TenantId).ToListAsync();
                ret = JArray.FromObject(tenants);
                //Move the object for the logged in tenant to the first
                JToken currentTenant = ret.FirstOrDefault(x => int.Parse(x["TenantId"].ToString()) == userDetails.tenant_id);
                ret.Remove(currentTenant);
                ret.AddFirst(currentTenant);
            }
            else
            {
                var tenants = await (from t in dbContext.gco_tenants
                                     where t.pk_id == userDetails.tenant_id
                                     select new
                                     {
                                         TenantId = t.pk_id,
                                         TenantName = t.tenant_name
                                     }).ToListAsync();
                ret = JArray.FromObject(tenants);
            }
            return ret;
        }

        public async Task<Dictionary<string, string>> GetLanguageStringsByContextAsync(string userId, string context)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            var lstContext = context.Split(',').Select(x => ngPrefix + x).ToList();
            Dictionary<string, clsLanguageString> allContextData = new();

            foreach (string ctx in lstContext)
            {
                string cacheKey = $"langStrByContext_{ctx}_{userDetails.language_preference}";
                string cachedLanguageString = await _cache.GetStringForTenantAsync(userDetails.client_id, userDetails.tenant_id, cacheKey);

                if (!string.IsNullOrEmpty(cachedLanguageString))
                {
                    var cachedResult = JsonConvert.DeserializeObject<Dictionary<string, clsLanguageString>>(cachedLanguageString);
                    AddToDictionary(allContextData, cachedResult);
                }
                else
                {
                    var contextData = await FetchLanguageStringsForContextAsync(userDetails.tenant_id, ctx, userDetails.language_preference);
                    AddToDictionary(allContextData, contextData);
                    await CacheUpdatedStrings(contextData, userDetails.client_id, userDetails.tenant_id, cacheKey);
                }
            }

            return allContextData.ToDictionary(x => x.Key, x => x.Value.LangText);
        }

        private static void AddToDictionary(Dictionary<string, clsLanguageString> target, Dictionary<string, clsLanguageString> source)
        {
            foreach (var kvp in source)
            {
                if (!target.ContainsKey(kvp.Key))
                {
                    target[kvp.Key] = kvp.Value;
                }
            }
        }

        private async Task<Dictionary<string, clsLanguageString>> FetchLanguageStringsForContextAsync(int tenantId, string context, string languageId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            return await (from lg in dbContext.gco_language_strings.AsNoTracking()
                          join lgo in dbContext.gco_language_string_overrides_tenant.Where(x => x.fk_tenant_id == tenantId).AsNoTracking()
                          on lg.ID equals lgo.ID into res
                          from lang in res.DefaultIfEmpty()
                          where lg.context.Equals(context) && lg.Language == languageId
                          select new clsLanguageString
                          {
                              id = lg.ID,
                              LangText = lang != null ? lang.Description : lg.Description,
                              Language = lg.context
                          }).ToDictionaryAsync(t => t.id, t => t);
        }

        private async Task CacheUpdatedStrings(Dictionary<string, clsLanguageString> data, int clientId, int tenantId, string cacheKey)
        {
            TimeSpan cacheTimeOut = TimeSpan.FromHours(24);
            await _cache.SetStringForTenantAsync(clientId, tenantId, cacheKey, JsonConvert.SerializeObject(data), cacheTimeOut);
        }
    }
}