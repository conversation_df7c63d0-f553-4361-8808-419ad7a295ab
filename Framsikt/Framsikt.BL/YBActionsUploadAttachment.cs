using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;

namespace Framsikt.BL
{
    public class YBActionsUploadAttachment : IYBActionsUploadAttachment
    {
        private readonly IActionUploadFileUtility _actionUploadFileUtility;

        public YBActionsUploadAttachment(IActionUploadFileUtility actionUploadFileUtility)
        {
            _actionUploadFileUtility = actionUploadFileUtility;
        }

        public async Task UploadAttachment(AttachmentInput input)
        {
            await _actionUploadFileUtility.UploadAttachmentAsync(input);
        }

        public async Task DeleteAttachment(string userId, Guid attachmentId)
        {
            await _actionUploadFileUtility.DeleteAttachment(userId, attachmentId);
        }
        public Dictionary<string, string> GetAttachmentURL(string userId, Guid attachmentId)
        {
            return GetAttachmentURLAsync(userId, attachmentId).GetAwaiter().GetResult();
        }
        public async Task<Dictionary<string, string>> GetAttachmentURLAsync(string userId, Guid attachmentId)
        {
            return await _actionUploadFileUtility.GetAttachmentURLAsync(userId, attachmentId);
        }

        public List<Attachments> GetAttachments(AttachmentInput input, string userId)
        {
            return GetAttachmentsAsync(input, userId).GetAwaiter().GetResult();
        }

        public async Task<List<Attachments>> GetAttachmentsAsync(AttachmentInput input, string userId)
        {
            return await _actionUploadFileUtility.GetAttachmentsAsync(input, userId);
        }
        public List<YBAttachments> GetAdjustmentCodeAttachments(string adjustmentCode, string userId)
        {
            return GetAdjustmentCodeAttachmentsAsync(adjustmentCode, userId).GetAwaiter().GetResult();
        }
        public async Task<List<YBAttachments>> GetAdjustmentCodeAttachmentsAsync(string adjustmentCode, string userId)
        {
            return await _actionUploadFileUtility.GetAdjustmentCodeAttachmentsAsync(adjustmentCode, userId);
        }
    }
}
