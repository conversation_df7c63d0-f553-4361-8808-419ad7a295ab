<?xml version="1.0" encoding="utf-8"?>
<LoadTest Name="100-Users-MR-Load" Description="" Owner="" storage="d:\src\framsikt product development\framsikt\framsikt.loadtest\100-users-mr-load.loadtest" Priority="2147483647" Enabled="true" CssProjectStructure="" CssIteration="" DeploymentItemsEditable="" WorkItemIds="" TraceLevel="None" CurrentRunConfig="Run Settings1" Id="89e32176-7763-40ae-a7b5-9f997a46bb8a" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Scenarios>
    <Scenario Name="Scenario1" DelayBetweenIterations="0" PercentNewUsers="0" IPSwitching="false" TestMixType="PercentageOfTestsStarted" ApplyDistributionToPacingDelay="true" MaxTestIterations="0" DisableDuringWarmup="false" DelayStartTime="0" AllowedAgents="">
      <ThinkProfile Value="0.2" Pattern="NormalDistribution" />
      <LoadProfile Pattern="Constant" InitialUsers="100" />
      <TestMix>
        <TestProfile Name="LoginToMR" Path="logintomr.webtest" Id="7a95fd94-f4fe-4f73-a590-14a52a591222" Percentage="100" Type="Microsoft.VisualStudio.TestTools.WebStress.DeclarativeWebTestElement, Microsoft.VisualStudio.QualityTools.LoadTest, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      </TestMix>
      <BrowserMix>
        <BrowserProfile Percentage="50">
          <Browser Name="Chrome 2" MaxConnections="6">
            <Headers>
              <Header Name="User-Agent" Value="Mozilla/5.0 (Windows; U; Windows NT 5.1;) AppleWebKit/530.1 (KHTML, like Gecko) Chrome/********* Safari/530.1" />
              <Header Name="Accept" Value="*/*" />
              <Header Name="Accept-Language" Value="{{$IEAcceptLanguage}}" />
              <Header Name="Accept-Encoding" Value="gzip,deflate" />
              <Header Name="Accept-Charset" Value="ISO-8859-1,*,utf-8" />
            </Headers>
          </Browser>
        </BrowserProfile>
        <BrowserProfile Percentage="50">
          <Browser Name="Firefox 2.0" MaxConnections="2">
            <Headers>
              <Header Name="User-Agent" Value="Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1) Gecko/20061010 Firefox/2.0" />
              <Header Name="Accept" Value="*/*" />
              <Header Name="Accept-Language" Value="{{$IEAcceptLanguage}}" />
              <Header Name="Accept-Encoding" Value="GZIP" />
              <Header Name="Accept-Charset" Value="ISO-8859-1,utf-8;q=0.7,*;q=0.7" />
            </Headers>
          </Browser>
        </BrowserProfile>
      </BrowserMix>
      <NetworkMix>
        <NetworkProfile Percentage="100">
          <Network Name="LAN" BandwidthInKbps="1000000" NetworkProfileConfigurationXml="&lt;Emulation&gt;&lt;VirtualChannel name=&quot;defaultChannel&quot;&gt;&lt;FilterList/&gt;&lt;VirtualLink instances=&quot;1&quot; name=&quot;defaultLink&quot;&gt;&lt;LinkRule dir=&quot;upstream&quot;&gt;&lt;Bandwidth&gt;&lt;Speed unit=&quot;kbps&quot;&gt;1000000&lt;/Speed&gt;&lt;/Bandwidth&gt;&lt;/LinkRule&gt;&lt;LinkRule dir=&quot;downstream&quot;&gt;&lt;Bandwidth&gt;&lt;Speed unit=&quot;kbps&quot;&gt;1000000&lt;/Speed&gt;&lt;/Bandwidth&gt;&lt;/LinkRule&gt;&lt;/VirtualLink&gt;&lt;/VirtualChannel&gt;&lt;/Emulation&gt;" />
        </NetworkProfile>
      </NetworkMix>
    </Scenario>
  </Scenarios>
  <CounterSets>
    <CounterSet Name="LoadTest" CounterSetType="LoadTest" LocId="">
      <CounterCategories>
        <CounterCategory Name="LoadTest:Scenario">
          <Counters>
            <Counter Name="User Load" HigherIsBetter="true" />
            <Counter Name="Tests Running" HigherIsBetter="true" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="LoadTest:Test">
          <Counters>
            <Counter Name="Total Tests" HigherIsBetter="true" />
            <Counter Name="Passed Tests" HigherIsBetter="true" />
            <Counter Name="Failed Tests" />
            <Counter Name="Tests/Sec" HigherIsBetter="true" />
            <Counter Name="Passed Tests/Sec" HigherIsBetter="true" />
            <Counter Name="Failed Tests/Sec" />
            <Counter Name="Avg. Requests/Test" HigherIsBetter="true" />
            <Counter Name="Avg. Test Time" />
            <Counter Name="% Time in LoadTestPlugin" />
            <Counter Name="% Time in WebTest code" />
            <Counter Name="% Time in Rules" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="LoadTest:Transaction">
          <Counters>
            <Counter Name="Total Transactions" HigherIsBetter="true" />
            <Counter Name="Avg. Transaction Time" />
            <Counter Name="Avg. Response Time" />
            <Counter Name="Transactions/Sec" HigherIsBetter="true" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="LoadTest:Errors">
          <Counters>
            <Counter Name="Http Errors" />
            <Counter Name="Validation Rule Errors" />
            <Counter Name="Extraction Rule Errors" />
            <Counter Name="Requests Timed Out" />
            <Counter Name="Exceptions" />
            <Counter Name="Total Errors" />
            <Counter Name="Errors/Sec" />
            <Counter Name="Threshold Violations/Sec" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="LoadTest:Page">
          <Counters>
            <Counter Name="Total Pages" HigherIsBetter="true" />
            <Counter Name="Avg. Page Time" />
            <Counter Name="Page Response Time Goal" HigherIsBetter="true" />
            <Counter Name="% Pages Meeting Goal" HigherIsBetter="true" />
            <Counter Name="Pages/Sec" HigherIsBetter="true" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="LoadTest:Request">
          <Counters>
            <Counter Name="Total Requests" HigherIsBetter="true" />
            <Counter Name="Passed Requests" HigherIsBetter="true" />
            <Counter Name="Failed Requests" />
            <Counter Name="Cached Requests" HigherIsBetter="true" />
            <Counter Name="Requests/Sec" HigherIsBetter="true" />
            <Counter Name="Passed Requests/Sec" HigherIsBetter="true" />
            <Counter Name="Failed Requests/Sec" />
            <Counter Name="Avg. First Byte Time" />
            <Counter Name="Avg. Response Time" />
            <Counter Name="Avg. Connection Wait Time">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareCounters, Microsoft.VisualStudio.QualityTools.LoadTest, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
                  <RuleParameters>
                    <RuleParameter Name="DependentCategory" Value="LoadTest:Page" />
                    <RuleParameter Name="DependentCounter" Value="Avg. Page Time" />
                    <RuleParameter Name="DependentInstance" Value="_Total" />
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="0.25" />
                    <RuleParameter Name="CriticalThreshold" Value="0.5" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Avg. Content Length" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="LoadTest:LogEntries">
          <Counters>
            <Counter Name="Total Log Entries" />
            <Counter Name="Log Entries/Sec" />
          </Counters>
        </CounterCategory>
      </CounterCategories>
    </CounterSet>
    <CounterSet Name="Controller" CounterSetType="Controller" LocId="CounterSet_Controller">
      <CounterCategories>
        <CounterCategory Name="Memory">
          <Counters>
            <Counter Name="% Committed Bytes In Use" Range="100" />
            <Counter Name="Available MBytes" RangeGroup="Memory Bytes" HigherIsBetter="true">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="False" />
                    <RuleParameter Name="WarningThreshold" Value="100" />
                    <RuleParameter Name="CriticalThreshold" Value="50" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Page Faults/sec" />
            <Counter Name="Pages/sec" />
            <Counter Name="Pool Paged Bytes" RangeGroup="Memory Bytes" />
            <Counter Name="Pool Nonpaged bytes" RangeGroup="Memory Bytes" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="Network Interface">
          <Counters>
            <Counter Name="Bytes Received/sec" RangeGroup="Network Bytes" />
            <Counter Name="Bytes Sent/sec" RangeGroup="Network Bytes" />
            <Counter Name="Output Queue Length">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="1.5" />
                    <RuleParameter Name="CriticalThreshold" Value="2" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Packets Received/sec" RangeGroup="Network Packets" />
            <Counter Name="Packets Sent/sec" RangeGroup="Network Packets" />
            <Counter Name="Current Bandwidth" RangeGroup="Network Bytes" />
            <Counter Name="Bytes Total/sec" RangeGroup="Network Bytes">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareCounters, Microsoft.VisualStudio.QualityTools.LoadTest, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
                  <RuleParameters>
                    <RuleParameter Name="DependentCategory" Value="Network Interface" />
                    <RuleParameter Name="DependentCounter" Value="Current Bandwidth" />
                    <RuleParameter Name="DependentInstance" Value="" />
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="0.6" />
                    <RuleParameter Name="CriticalThreshold" Value="0.7" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
          </Counters>
          <Instances>
            <Instance Name="*" />
          </Instances>
        </CounterCategory>
        <CounterCategory Name="PhysicalDisk">
          <Counters>
            <Counter Name="% Disk Read Time" Range="100" />
            <Counter Name="% Disk Time" Range="100" />
            <Counter Name="% Disk Write Time" Range="100" />
            <Counter Name="% Idle Time" Range="100" HigherIsBetter="true">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="False" />
                    <RuleParameter Name="WarningThreshold" Value="40" />
                    <RuleParameter Name="CriticalThreshold" Value="20" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Avg. Disk Bytes/Read" RangeGroup="DiskBytesRate" />
            <Counter Name="Avg. Disk Bytes/Transfer" RangeGroup="DiskBytesRate" />
            <Counter Name="Avg. Disk Bytes/Write" RangeGroup="DiskBytesRate" />
            <Counter Name="Avg. Disk Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Avg. Disk Read Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Avg. Disk Write Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Current Disk Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Avg. Disk sec/Read" RangeGroup="Disk sec" />
            <Counter Name="Avg. Disk sec/Transfer" RangeGroup="Disk sec" />
            <Counter Name="Avg. Disk sec/Write" RangeGroup="Disk sec" />
            <Counter Name="Disk Bytes/sec" RangeGroup="Disk Bytes sec" />
            <Counter Name="Disk Read Bytes/sec" RangeGroup="Disk Bytes sec" />
            <Counter Name="Disk Reads/sec" RangeGroup="Disk Transfers sec" />
            <Counter Name="Disk Transfers/sec" RangeGroup="Disk Transfers sec" />
            <Counter Name="Disk Write Bytes/sec" RangeGroup="Disk Bytes sec" />
            <Counter Name="Disk Writes/sec" RangeGroup="Disk Transfers sec" />
            <Counter Name="Split IO/Sec" RangeGroup="Disk Transfers sec" />
          </Counters>
          <Instances>
            <Instance Name="*" />
          </Instances>
        </CounterCategory>
        <CounterCategory Name="Processor">
          <Counters>
            <Counter Name="% Processor Time" Range="100">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="75" />
                    <RuleParameter Name="CriticalThreshold" Value="90" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="% Privileged Time" Range="100" />
            <Counter Name="% User Time" Range="100" />
          </Counters>
          <Instances>
            <Instance Name="_Total" />
          </Instances>
        </CounterCategory>
        <CounterCategory Name="System">
          <Counters>
            <Counter Name="Context Switches/sec" />
            <Counter Name="Processes" />
            <Counter Name="Processor Queue Length" />
            <Counter Name="Threads" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="Process">
          <Counters>
            <Counter Name="% Processor Time" RangeGroup="Processor Time" />
            <Counter Name="% Privileged Time" RangeGroup="Processor Time" />
            <Counter Name="% User Time" RangeGroup="Processor Time" />
            <Counter Name="Handle Count" />
            <Counter Name="Thread Count" />
            <Counter Name="Private Bytes" RangeGroup="Memory Bytes" />
            <Counter Name="Virtual Bytes" RangeGroup="Memory Bytes" />
            <Counter Name="Working Set" RangeGroup="Memory Bytes" />
          </Counters>
          <Instances>
            <Instance Name="QTController" />
            <Instance Name="QTController64" />
          </Instances>
        </CounterCategory>
      </CounterCategories>
      <DefaultCountersForAutomaticGraphs>
        <DefaultCounter CategoryName="Processor" CounterName="% Processor Time" InstanceName="_Total" GraphName="" />
        <DefaultCounter CategoryName="Memory" CounterName="Available MBytes" InstanceName="" GraphName="" />
      </DefaultCountersForAutomaticGraphs>
    </CounterSet>
    <CounterSet Name="Agent" CounterSetType="Agent" LocId="CounterSet_Agent">
      <CounterCategories>
        <CounterCategory Name="Memory">
          <Counters>
            <Counter Name="% Committed Bytes In Use" Range="100" />
            <Counter Name="Available MBytes" RangeGroup="Memory Bytes" HigherIsBetter="true">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="False" />
                    <RuleParameter Name="WarningThreshold" Value="100" />
                    <RuleParameter Name="CriticalThreshold" Value="50" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Page Faults/sec" />
            <Counter Name="Pages/sec" />
            <Counter Name="Pool Paged Bytes" RangeGroup="Memory Bytes" />
            <Counter Name="Pool Nonpaged bytes" RangeGroup="Memory Bytes" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="Network Interface">
          <Counters>
            <Counter Name="Bytes Received/sec" RangeGroup="Network Bytes" />
            <Counter Name="Bytes Sent/sec" RangeGroup="Network Bytes" />
            <Counter Name="Output Queue Length">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="1.5" />
                    <RuleParameter Name="CriticalThreshold" Value="2" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Packets Received/sec" RangeGroup="Network Packets" />
            <Counter Name="Packets Sent/sec" RangeGroup="Network Packets" />
            <Counter Name="Current Bandwidth" RangeGroup="Network Bytes" />
            <Counter Name="Bytes Total/sec" RangeGroup="Network Bytes">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareCounters, Microsoft.VisualStudio.QualityTools.LoadTest, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
                  <RuleParameters>
                    <RuleParameter Name="DependentCategory" Value="Network Interface" />
                    <RuleParameter Name="DependentCounter" Value="Current Bandwidth" />
                    <RuleParameter Name="DependentInstance" Value="" />
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="0.6" />
                    <RuleParameter Name="CriticalThreshold" Value="0.7" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
          </Counters>
          <Instances>
            <Instance Name="*" />
          </Instances>
        </CounterCategory>
        <CounterCategory Name="PhysicalDisk">
          <Counters>
            <Counter Name="% Disk Read Time" Range="100" />
            <Counter Name="% Disk Time" Range="100" />
            <Counter Name="% Disk Write Time" Range="100" />
            <Counter Name="% Idle Time" Range="100" HigherIsBetter="true">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="False" />
                    <RuleParameter Name="WarningThreshold" Value="40" />
                    <RuleParameter Name="CriticalThreshold" Value="20" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="Avg. Disk Bytes/Read" RangeGroup="DiskBytesRate" />
            <Counter Name="Avg. Disk Bytes/Transfer" RangeGroup="DiskBytesRate" />
            <Counter Name="Avg. Disk Bytes/Write" RangeGroup="DiskBytesRate" />
            <Counter Name="Avg. Disk Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Avg. Disk Read Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Avg. Disk Write Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Current Disk Queue Length" RangeGroup="Disk Queue Length" />
            <Counter Name="Avg. Disk sec/Read" RangeGroup="Disk sec" />
            <Counter Name="Avg. Disk sec/Transfer" RangeGroup="Disk sec" />
            <Counter Name="Avg. Disk sec/Write" RangeGroup="Disk sec" />
            <Counter Name="Disk Bytes/sec" RangeGroup="Disk Bytes sec" />
            <Counter Name="Disk Read Bytes/sec" RangeGroup="Disk Bytes sec" />
            <Counter Name="Disk Reads/sec" RangeGroup="Disk Transfers sec" />
            <Counter Name="Disk Transfers/sec" RangeGroup="Disk Transfers sec" />
            <Counter Name="Disk Write Bytes/sec" RangeGroup="Disk Bytes sec" />
            <Counter Name="Disk Writes/sec" RangeGroup="Disk Transfers sec" />
            <Counter Name="Split IO/Sec" RangeGroup="Disk Transfers sec" />
          </Counters>
          <Instances>
            <Instance Name="*" />
          </Instances>
        </CounterCategory>
        <CounterCategory Name="Processor">
          <Counters>
            <Counter Name="% Processor Time" Range="100">
              <ThresholdRules>
                <ThresholdRule Classname="Microsoft.VisualStudio.TestTools.WebStress.Rules.ThresholdRuleCompareConstant, Microsoft.VisualStudio.QualityTools.LoadTest">
                  <RuleParameters>
                    <RuleParameter Name="AlertIfOver" Value="True" />
                    <RuleParameter Name="WarningThreshold" Value="75" />
                    <RuleParameter Name="CriticalThreshold" Value="90" />
                  </RuleParameters>
                </ThresholdRule>
              </ThresholdRules>
            </Counter>
            <Counter Name="% Privileged Time" Range="100" />
            <Counter Name="% User Time" Range="100" />
          </Counters>
          <Instances>
            <Instance Name="0" />
            <Instance Name="_Total" />
          </Instances>
        </CounterCategory>
        <CounterCategory Name="System">
          <Counters>
            <Counter Name="Context Switches/sec" />
            <Counter Name="Processes" />
            <Counter Name="Processor Queue Length" />
            <Counter Name="Threads" />
          </Counters>
        </CounterCategory>
        <CounterCategory Name="Process">
          <Counters>
            <Counter Name="% Processor Time" RangeGroup="Processor Time" />
            <Counter Name="% Privileged Time" RangeGroup="Processor Time" />
            <Counter Name="% User Time" RangeGroup="Processor Time" />
            <Counter Name="Handle Count" />
            <Counter Name="Thread Count" />
            <Counter Name="Private Bytes" RangeGroup="Memory Bytes" />
            <Counter Name="Virtual Bytes" RangeGroup="Memory Bytes" />
            <Counter Name="Working Set" RangeGroup="Memory Bytes" />
          </Counters>
          <Instances>
            <Instance Name="devenv" />
            <Instance Name="QTAgentService" />
            <Instance Name="QTAgent" />
            <Instance Name="QTAgent32" />
            <Instance Name="QTDCAgent" />
            <Instance Name="QTDCAgent32" />
            <Instance Name="QTAgent_35" />
            <Instance Name="QTAgent_40" />
            <Instance Name="QTAgent32_35" />
            <Instance Name="QTAgent32_40" />
          </Instances>
        </CounterCategory>
      </CounterCategories>
      <DefaultCountersForAutomaticGraphs>
        <DefaultCounter CategoryName="Processor" CounterName="% Processor Time" InstanceName="0" GraphName="" RunType="Local" />
        <DefaultCounter CategoryName="Processor" CounterName="% Processor Time" InstanceName="_Total" GraphName="" RunType="Remote" />
        <DefaultCounter CategoryName="Memory" CounterName="Available MBytes" InstanceName="" GraphName="" />
      </DefaultCountersForAutomaticGraphs>
    </CounterSet>
  </CounterSets>
  <RunConfigurations>
    <RunConfiguration Name="Run Settings1" Description="" ResultsStoreType="Database" TimingDetailsStorage="AllIndividualDetails" SaveTestLogsOnError="true" SaveTestLogsFrequency="0" MaxErrorDetails="200" MaxErrorsPerType="1000" MaxThresholdViolations="1000" MaxRequestUrlsReported="1000" UseTestIterations="false" RunDuration="300" WarmupTime="0" CoolDownTime="0" TestIterations="100" WebTestConnectionModel="ConnectionPerUser" WebTestConnectionPoolSize="50" SampleRate="15" ValidationLevel="High" SqlTracingConnectString="" SqlTracingConnectStringDisplayValue="" SqlTracingDirectory="" SqlTracingEnabled="false" SqlTracingFileCount="2" SqlTracingRolloverEnabled="true" SqlTracingMinimumDuration="500" RunUnitTestsInAppDomain="true" CoreCount="0" ResourcesRetentionTimeInMinutes="0" AgentDiagnosticsLevel="Warning">
      <CounterSetMappings>
        <CounterSetMapping ComputerName="[CONTROLLER MACHINE]">
          <CounterSetReferences>
            <CounterSetReference CounterSetName="LoadTest" />
            <CounterSetReference CounterSetName="Controller" />
          </CounterSetReferences>
        </CounterSetMapping>
        <CounterSetMapping ComputerName="[AGENT MACHINES]">
          <CounterSetReferences>
            <CounterSetReference CounterSetName="Agent" />
          </CounterSetReferences>
        </CounterSetMapping>
      </CounterSetMappings>
      <LoadGeneratorLocations>
        <GeoLocation Location="Default" Percentage="100" />
      </LoadGeneratorLocations>
    </RunConfiguration>
  </RunConfigurations>
</LoadTest>