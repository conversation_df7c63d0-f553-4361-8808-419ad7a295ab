DELETE FROM [gmd_auth_activity_role_mapping];
DELETE FROM gmd_auth_activities;


INSERT INTO gmd_auth_activities
(	[pk_id],
	[method_name],
	[controller_name],
	[parameters],
	[isStatic],
	[isView])
SELECT 
	[pk_id],
	[method_name],
	[controller_name],
	[parameters],
	[isStatic],
	[isView]
FROM [TEMP_gmd_auth_activities]


INSERT INTO  [gmd_auth_activity_role_mapping](
	[fk_activity_id],
	[fk_role_id] ,
	[access])
SELECT 
	[fk_activity_id],
	[fk_role_id] ,
	[access] [bit]
FROM TEMP_gmd_auth_activity_role_mapping



--DROP TABLE IF EXISTS TEMP_gmd_auth_activities;
--DROP TABLE IF EXISTS TEMP_gmd_auth_activity_role_mapping;