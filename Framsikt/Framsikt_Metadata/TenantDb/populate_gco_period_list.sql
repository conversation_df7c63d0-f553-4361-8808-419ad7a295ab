DECLARE @budget_year TABLE (budget_year INT)
DECLARE @period TABLE (period INT)

DECLARE @current_year INT = DATEPART(YEAR,getdate())


insert @budget_year
SELECT ones.n + 10*tens.n + 100*hundreds.n + 1000*thousands.n as budget_year
FROM (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) ones(n),
     (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) tens(n),
     (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) hundreds(n),
     (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) thousands(n)
WHERE (ones.n + 10*tens.n + 100*hundreds.n + 1000*thousands.n) BETWEEN 2015 and @current_year+2
ORDER BY 1

insert @period
SELECT ones.n + 10*tens.n + 100*hundreds.n + 1000*thousands.n as period
FROM (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) ones(n),
     (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) tens(n),
     (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) hundreds(n),
     (VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9)) thousands(n)
WHERE (ones.n + 10*tens.n + 100*hundreds.n + 1000*thousands.n) BETWEEN 1 and 12
ORDER BY 1


DELETE FROM gco_period_list

INSERT gco_period_list
select 
a.budget_year
,period = a.budget_year*100+b.period 
from @budget_year a
JOIN @period b ON 1=1
ORDER BY budget_year,period


