delete from tco_fonts_definition
GO

/*Oslo sans starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(2, 'semiFont', 'url(../fonts/OsloSans-Medium.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(2, 'regularFont', 'url(../fonts/OsloSans-Regular.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(2, 'lightFont', 'url(../fonts/OsloSans-Light.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(2, 'boldFont', 'url(../fonts/OsloSans-Bold.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(2, 'italicFont', 'url(../fonts/OsloSans-RegularItalic.woff)')
/* Oslo sans ends */

/*Voss Apercu starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(3, 'semiFont', 'url(../fonts/Apercu-Medium-Pro.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(3, 'regularFont', 'url(../fonts/Apercu-Regular-Pro.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(3, 'lightFont', 'url(../fonts/Apercu-Light-Pro.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(3, 'boldFont', 'url(../fonts/Apercu-Bold-Pro.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(3, 'italicFont', 'url(../fonts/Apercu-Italic-Pro.woff)')
/*Voss Apercu ends */

/*Asker starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(4, 'semiFont', 'url(../fonts/AskerSans-Bold.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(4, 'regularFont', 'url(../fonts/AskerSans-Regular.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(4, 'lightFont', 'url(../fonts/AskerSans-Regular.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(4, 'boldFont', 'url(../fonts/AskerSans-Bold.woff)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(4, 'italicFont', 'url(../fonts/AskerSans-RegularItalic.woff)')
/*Asker ends */

/*sans pro starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(5, 'semiFont', 'url(../fonts/SourceSansPro-SemiBold.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(5, 'regularFont', 'url(../fonts/SourceSansPro-Regular.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(5, 'lightFont', 'url(../fonts/SourceSansPro-Light.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(5, 'boldFont', 'url(../fonts/SourceSansPro-Bold.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(5, 'italicFont', 'url(../fonts/SourceSansPro-Italic.ttf)')
/*sans pro ends */

/*museo-sans starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(6, 'semiFont', 'museo-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(6, 'regularFont', 'museo-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(6, 'lightFont', 'museo-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(6, 'boldFont', 'museo-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(6, 'italicFont', 'museo-sans')
/*museo-sans ends */


/*roboto starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(7, 'semiFont', 'roboto')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(7, 'regularFont', 'roboto')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(7, 'lightFont', 'roboto')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(7, 'boldFont', 'roboto')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(7, 'italicFont', 'roboto')
/*roboto ends */

/*open-sans starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(8, 'semiFont', 'open-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(8, 'regularFont', 'open-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(8, 'lightFont', 'open-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(8, 'boldFont', 'open-sans')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(8, 'italicFont', 'open-sans')
/*open-sans ends */

/*NotoSans starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(9, 'semiFont', 'url(../fonts/notosans-semibold.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(9, 'regularFont', 'url(../fonts/notosans-regular.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(9, 'boldFont', 'url(../fonts/notosans-bold.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(9, 'lightFont', 'url(../fonts/notosans-light.ttf)')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(9, 'italicFont', 'url(../fonts/notosans-italic.ttf)')
/*NotoSans ends */

/*source-sans-pro starts*/
INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(10, 'semiFont', 'source-sans-pro')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(10, 'regularFont', 'source-sans-pro')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(10, 'lightFont', 'source-sans-pro')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(10, 'boldFont', 'source-sans-pro')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(10, 'italicFont', 'source-sans-pro')
/*source-sans-pro ends */

/*Muli font starts*/

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(11, 'semiFont', 'muli')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(11, 'regularFont', 'muli')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(11, 'lightFont', 'muli')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(11, 'boldFont', 'muli')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
VALUES(11, 'italicFont', 'muli')

/*Muli font ends */

/*Arial font starts*/

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url])
VALUES(12, 'semiFont', 'arial')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url])
VALUES(12, 'regularFont', 'arial')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url])
VALUES(12, 'lightFont', 'arial')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url])
VALUES(12, 'boldFont', 'arial')

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url])
VALUES(12, 'italicFont', 'arial') 

/*Arial font ends */
