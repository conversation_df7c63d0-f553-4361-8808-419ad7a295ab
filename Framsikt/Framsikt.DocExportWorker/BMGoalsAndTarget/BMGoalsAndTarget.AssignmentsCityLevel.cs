using Aspose.Words;
using Aspose.Words.Tables;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.DocExportWorker.Helpers;
using Framsikt.Entities;

namespace Framsikt.DocExportWorker
{
    public partial class BMGoalsAndTarget
    {

        public void InsertAssigmentsCityLvl(string userId, DocumentBuilder builder, int budgetYear, PublishTreeNode current, PublishTreeNode parent, bool showOnlyModified, string budgetPhaseId, List<string> treePath)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
            UserData userdata = pUtility.GetUserDetails(userId);

            TenantDBContext dbContext = pUtility.GetTenantDBContext();
            IQueryable<tco_goals> goalsQuery = null;
            string focusId = string.Empty;
            Dictionary<string, clsLanguageString> bmStrings = pUtility.GetLanguageStrings(userdata.language_preference,
                userdata.user_name, "BusinessPlan");

            if (parent != null && parent.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
            {
                goalsQuery = (from g in dbContext.tco_goals
                              where g.fk_tenant_id == userdata.tenant_id
                              && g.budget_year == budgetYear
                              && g.focus_area == parent.id
                              && g.is_busplan_goal != true
                              orderby g.goal_name
                              select g);
                focusId = parent.id;
            }
            else
            {
                goalsQuery = (from g in dbContext.tco_goals
                              where g.fk_tenant_id == userdata.tenant_id
                              && g.budget_year == budgetYear
                              && string.IsNullOrEmpty(g.focus_area)
                              && g.is_busplan_goal != true
                              orderby g.goal_name
                              select g);
            }

            var goals = goalsQuery.ToList();
            var goalIds = goals.Select(x => x.pk_goal_id).ToList();
            var allowedCatgories = dbContext.tco_category.Where(x => x.fk_tenant_id == userdata.tenant_id && x.status == 1 && x.type.ToLower() == "SUNIT_BPLAN".ToLower()).Select(x => x.pk_cat_id).ToList();

            //Get assignments
            List<tbiassignments> assignmentsSet = new List<tbiassignments>();

            var assignmentGoalData = dbContext.TbiAssignmentGoal.Where(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear).ToList();

            List<tbiassignments> assignmentsInitial = (from a in dbContext.tbiassignments
                                                       join b in dbContext.TbiAssignmentGoal on new { a = a.tenantId, b = a.assignmentId } equals new { a = b.fk_tenant_id, b = b.fk_assignment_id }
                                                       where a.tenantId == userdata.tenant_id
                                                       && a.budgetYear == budgetYear
                                                       && goalIds.Contains(b.fk_goal_id)
                                                       && a.isBudgetProposal.HasValue && a.isBudgetProposal.Value
                                                       select a).Distinct().ToList();

            foreach (var item in allowedCatgories)
            {
                assignmentsSet.AddRange(assignmentsInitial.Where(x => x.category.Split(',').ToList().Contains(item.ToString())));
            }

            var assignments = (from a in assignmentsSet
                               select a).Distinct().ToList();

            if (assignments.Any())
            {
                if (parent != null && parent.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                {
                    publishHelper.InsertHeading(userId, current.text, _context, true);
                }
                else
                {
                    publishHelper.InsertHeading(userId, current.text, _context);
                }

                if (!current.type.Equals("customNode", StringComparison.InvariantCultureIgnoreCase) && current.isEditableNode)
                {
                    _customNode.InsertNodeDescription(builder, userId, current, treePath, budgetYear,
                        showOnlyModified ? budgetPhaseId : string.Empty, true, false);
                }
                string tableName;

                List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                List<string> lstColumnDataDetails = new List<string>();
                ColumnDetails cellTemplate;

                if (parent != null && parent.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                {
                    TableDefinition tableDef = _docTableConfig.GetTableDef(userId, "BMAssignmentPerFocusArea");
                    tableDef.AmountFormat = AmountFormats.NotApplicable;
                    publishHelper.StartTableOrientation(tableDef);
                    tableName = $"{PublishSection.GetSection.BDGTAssignments}-{focusId}";
                    publishHelper.StartTable(tableName, null, null, tableDef);
                    foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        lstColumnDetails.Add(cellTemplate);
                        lstColumnDataDetails.Add(cdf.ColumnName);
                    }
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
                    lstColumnDataDetails.Clear();
                }
                else
                {
                    tableName = $"{PublishSection.GetSection.BDGTAssignments}";
                    publishHelper.StartTable(tableName, string.Empty, string.Empty);
                    List<string> data = new List<string>
                    {
                        bmStrings["SUBP_Pub_GoalTitle"].LangText,
                        bmStrings["SUBP_Pub_Assignment"].LangText,
                        bmStrings["SUBP_Pub_AssignmentDesc"].LangText
                    };

                    cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                    cellTemplate.Fontsize = 9;
                    cellTemplate.BottomBorder = 1;
                    cellTemplate.PreferredWidth = 30;
                    cellTemplate.showBottomBorder = true;
                    cellTemplate.Alignment = ParagraphAlignment.Left;
                    cellTemplate.WrapText = true;
                    cellTemplate.underlineText = true;
                    lstColumnDetails.Add(cellTemplate);
                    lstColumnDetails.Add(cellTemplate);
                    lstColumnDetails.Add(cellTemplate);

                    publishHelper.CreateRow1(lstColumnDetails, data, true, RowType.Header);
                }

                Guid previousGoalId = Guid.Empty;
                List<Guid> assignmentIds = assignments.Select(x => x.assignmentId).ToList();
                foreach (var goal in goals)
                {
                    List<Guid> uniqueassignmentIds = assignmentGoalData.Where(x => x.fk_goal_id == goal.pk_goal_id && assignmentIds.Contains(x.fk_assignment_id)).Select(x => x.fk_unique_assignment_id).Distinct().ToList();
                    List<Guid> assignmentsData = new List<Guid>();
                    foreach (Guid unqId in uniqueassignmentIds)
                    {
                        TbiAssignmentGoal assignmentsDataUnq = assignmentGoalData.FirstOrDefault(x => x.fk_unique_assignment_id == unqId);
                        assignmentsData.Add(assignmentsDataUnq.fk_assignment_id);
                    }
                    var assignmentList = assignments.Where(x => assignmentsData.Contains(x.assignmentId)).ToList();
                    if (assignmentList.Count > 0)
                    {
                        foreach (var assignment in assignmentList)
                        {
                            List<string> content = new List<string>();
                            List<ColumnDetails> contentTemplate = new List<ColumnDetails>();
                            var assignmentDescription = (from d in dbContext.tco_assignments_descriptions
                                                         where d.fk_tenant_id == userdata.tenant_id &&
                                                         d.fk_assignment_id == assignment.assignmentId
                                                         select d).FirstOrDefault();

                            if (Guid.Equals(goal.pk_goal_id, previousGoalId))
                            {
                                content.Add(string.Empty);
                            }
                            else
                            {
                                content.Add(goal.goal_name);
                                previousGoalId = goal.pk_goal_id;
                            }
                            content.Add(string.IsNullOrEmpty(assignment.assignmentName) ? string.Empty : assignment.assignmentName);
                            content.Add(string.IsNullOrEmpty(assignmentDescription?.assignment_description) ? string.Empty : assignmentDescription.assignment_description);

                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            cellTemplate.BottomBorder = 1;
                            cellTemplate.ContentType = ContentType.Html;
                            contentTemplate.Add(cellTemplate);
                            contentTemplate.Add(cellTemplate);

                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            cellTemplate.BottomBorder = 1;
                            cellTemplate.ContentType = ContentType.Html;
                            contentTemplate.Add(cellTemplate);
                            if (parent != null && parent.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                            {
                                publishHelper.CreateRow2(contentTemplate, content, RowType2.Content2);
                            }
                            else
                            {
                                publishHelper.CreateRow1(contentTemplate, content, false, RowType.Detail);
                            }
                        }
                    }
                }

                publishHelper.EndTable();
                if (parent != null && parent.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                {
                    publishHelper.EndTableOrientation();
                }
            }
            else
            {
                if (!current.type.Equals("customNode", StringComparison.InvariantCultureIgnoreCase) && current.isEditableNode)
                {
                    _customNode.InsertNodeDescription(builder, userId, current, treePath, budgetYear,
                        showOnlyModified ? budgetPhaseId : string.Empty, true, false);
                }
            }
        }

    }
}