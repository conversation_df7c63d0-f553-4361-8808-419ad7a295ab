#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8618

using Azure.Identity;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Hangfire;
using Hangfire.Storage;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;

namespace Framsikt.DocExportWorker.BackgroundProcessors
{
    public class QueueScheduler
    {
        private int _defaultQueueMessageCount;
        private int _criticalQueueMessageCount;
        private int _docExportMessageCount;
        private JobStorage _jobStorage;
        private List<string> deadLockHandleQueues = new() { QueueName.importactivityindicatorqueue.ToString(), QueueName.mrabsenceimportqueue.ToString() };

        [JobDisplayName("Recurring Queue Scheduler")]
        [AutomaticRetry(Attempts = 0)]
        public void ScheduleQueueMessage()
        {
            try
            {
                _jobStorage = JobStorage.Current;
                SetMessagesCount();

                var queueDetails = BackgroundJobConstants.GetQueueDetails();
                foreach (var queueInfo in queueDetails)
                {
                    QueueServiceClient queueServiceClient;

                    var isLocal = Environment.GetEnvironmentVariable("IsLocal") ?? "false";

                    if (!isLocal.Equals("true", StringComparison.OrdinalIgnoreCase))
                    {
                        queueServiceClient = new QueueServiceClient(new Uri(AppConfiguration.GetConfigurationSetting("azureQueueStorageAccountUri").Replace("{Service}", "queue")), new DefaultAzureCredential());
                    }
                    else 
                    {
                        queueServiceClient = new QueueServiceClient("UseDevelopmentStorage=true");
                    }

                    var queueClient = queueServiceClient.GetQueueClient(queueInfo.QueueName.ToLower());

                    queueClient.CreateIfNotExists();

                    int messageCount = BackgroundJobConstants.criticalQueues.Contains(queueInfo.QueueName) ? GetInProgressCriticalQueueCount(queueInfo.QueueName) : _defaultQueueMessageCount;
                    if (messageCount <= 0) continue;

                    QueueMessage[] messages = queueClient.ReceiveMessages(messageCount);
                    foreach (var message in messages)
                    {
                        HFQueueData objMessageData = new HFQueueData
                        {
                            MessageId = message.MessageId,
                            QueueName = queueInfo.QueueName,
                            QueueBody = message.Body.ToString(),
                            IsLoggingEnabled = queueInfo.IsLoggingEnabled
                        };
                        //handle deadlock issue for specific queues to process single job at a time for a given tenantId and budgetYear
                        #region handle deadlock
                        bool activeJobForBudgetYear = ActiveProcessingJobForYear(queueInfo, objMessageData);
                        if (activeJobForBudgetYear)
                        {
                            continue;
                        }
                        #endregion handle deadlock
                        string jobId = HangfireExtensions.GetHangfireQueueDetails(objMessageData.MessageId, objMessageData.QueueName);
                        SetBackgroundJobForProcessingQueues(objMessageData, jobId);
                        queueClient.DeleteMessage(message.MessageId, message.PopReceipt);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in QueueScheduler");
                throw;
            }
        }

        private void SetBackgroundJobForProcessingQueues(HFQueueData objMessageData, string jobId)
        {
            if (jobId == null)
            {
                if (BackgroundJobConstants.criticalQueues.Contains(objMessageData.QueueName))
                    BackgroundJob.Enqueue<BackGroundProcessor>(x => x.PushToCriticalQueue(objMessageData.QueueName, objMessageData.MessageId, objMessageData));
                else if (BackgroundJobConstants.retryJobs.Contains(objMessageData.QueueName))
                    BackgroundJob.Enqueue<BackGroundProcessor>(x => x.PushToPlanProcessingDefaultQueue(objMessageData.QueueName, objMessageData.MessageId, objMessageData));
                else
                    BackgroundJob.Enqueue<BackGroundProcessor>(x => x.PushToDefaultQueue(objMessageData.QueueName, objMessageData.MessageId, objMessageData));
            }
        }

        /// <summary>
        /// This Method sets how many messages needs to be picked for each iteration i.e., Recurring Job Execution
        /// Hangfire Critical Queues processes the heavy exports/publishes. Please refer variable _criticalQueues, Refer GetInProgressCriticalQueueCount methods for more details
        /// </summary>
        private void SetMessagesCount()
        {
            BackGroundJobConfig bcJobConfig = JsonConvert.DeserializeObject<BackGroundJobConfig>(AppConfiguration.GetConfigurationSetting("backgroundJobConfig"));
            _defaultQueueMessageCount = bcJobConfig.DefaultQueueMessageCount;
            _criticalQueueMessageCount = bcJobConfig.CriticalQueueMessageCount;
            _docExportMessageCount = bcJobConfig.DocExportMessageCount;
        }

        /// <summary>
        /// We are controlling the behaviour on how many message can be processed inside hangfire critical queue,
        /// This has been set up in the App configuration named "backgroundJobConfig" and the queue key is criticalQueueMessageCount
        /// For Doc Exports, we are referrring to key docExportMessageCount for avoiding load on render service
        /// </summary>
        /// <param name="queueName"></param>
        /// <returns></returns>
        private int GetInProgressCriticalQueueCount(string queueName)
        {
            IMonitoringApi monitoringApi = _jobStorage.GetMonitoringApi();
            var procesingCount = monitoringApi.ProcessingCount();
            var count = monitoringApi.ProcessingJobs(0, (int)procesingCount).Where(x => x.Value.Job.Args != null && x.Value.Job.Args.Any() && x.Value.Job.Args.LastOrDefault().GetType() == typeof(HFQueueData))
                                                                            .Count(x => ((HFQueueData)x.Value.Job.Args.LastOrDefault()).QueueName == queueName);
            if (BackgroundJobConstants.docExports.Contains(queueName))
                return _docExportMessageCount - count;
            else
                return _criticalQueueMessageCount - count;
        }

        private bool ActiveProcessingJobForYear(QueueInfo queueInfo, HFQueueData objMessageData)
        {
            bool activeJobForBudgetYear = false;
            if (deadLockHandleQueues.Any(x => x == queueInfo.QueueName))
            {
                int currentBudgetYear = 0, currentTenantId = 0;
                var currentJobInfo = JsonConvert.DeserializeObject<QueueJobEntity>(objMessageData.QueueBody);
                var currentMsg = currentJobInfo?.Data ?? string.Empty;
                if (!string.IsNullOrEmpty(currentMsg))
                {
                    var jsonReq = JToken.Parse(currentMsg);
                    currentBudgetYear = jsonReq["BudgetYear"]?.ToObject<int>() ?? 0;
                    currentTenantId = jsonReq["TenantId"]?.ToObject<int>() ?? 0;
                }
                //process messages details from hangfire
                IMonitoringApi monitoringApi = _jobStorage.GetMonitoringApi();
                var procesingCount = monitoringApi.ProcessingCount();
                var data = monitoringApi.ProcessingJobs(0, (int)procesingCount).Where(x => x.Value.Job.Args != null && x.Value.Job.Args.Any() && x.Value.Job.Args.LastOrDefault().GetType() == typeof(HFQueueData) && ((HFQueueData)x.Value.Job.Args.LastOrDefault()).QueueName == queueInfo.QueueName).ToList();
                foreach (var job in data)
                {
                    var jobArgs = job.Value.Job.Args;
                    var messageData = ((HFQueueData)jobArgs.LastOrDefault()) ?? new HFQueueData();
                    var jobInfo = JsonConvert.DeserializeObject<QueueJobEntity>(messageData.QueueBody);
                    var msg = jobInfo?.Data ?? string.Empty;
                    int budgetYear = 0, tenantId = 0;
                    if (!string.IsNullOrEmpty(msg))
                    {
                        var jsonReq = JToken.Parse(msg);
                        budgetYear = jsonReq["BudgetYear"]?.ToObject<int>() ?? 0;
                        tenantId = jsonReq["TenantId"]?.ToObject<int>() ?? 0;
                    }
                    if (currentTenantId == tenantId && currentBudgetYear == budgetYear) /*to check if there is already a message processing for given tenantId and budgetYear*/
                    {
                        activeJobForBudgetYear = true;
                        break;
                    }
                }
            }
            return activeJobForBudgetYear;
        }

    }
}