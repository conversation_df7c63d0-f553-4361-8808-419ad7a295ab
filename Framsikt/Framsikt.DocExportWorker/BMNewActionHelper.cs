#pragma warning disable CS8625

#pragma warning disable CS8600
#pragma warning disable CS8602

using Aspose.Words;
using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Core.PublishHelpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker.Helpers;
using Framsikt.Entities;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Drawing;
using System.Text;
using System.Text.RegularExpressions;
using static Framsikt.BL.DocWidget;

namespace Framsikt.DocExportWorker
{
    public class BMNewActionHelper
    {
        private readonly IUtility pUtility = null;
        private readonly IUnitOfWork _unitOfWork;
        private Dictionary<string, clsLanguageString> LanguageStringsBudgetMgt = null;
        private Dictionary<string, clsLanguageString> LanguageStringsDocExport = null;
        private Dictionary<string, clsLanguageString> numberFormats = null;
        private string expLang = "";
        private readonly IBudgetManagement pBudMan = null;
        private readonly IDocumentStyle docStyle = null;
        private readonly string Context = "BMExport";
        private readonly double[] bListDeletedActionsYearAmounts;
        private readonly IServiceProvider _container;
        private readonly IConsequenceAdjustedBudget pConseq = null;
        private readonly IInvestments pInvestments = null;
        private readonly IDocTableConfig _docTableConfig;
        private readonly IBudgetProposals _budgetProposals;
        private readonly DocWidgetExport _docWidExport;
        private const string actionList = "ActionListWidget";
        private const string investmentList = "InvestmentListWidget";
        private readonly IAzureBlobHelper _blobHelper;

        public BMNewActionHelper(IServiceProvider container)
        {
            pUtility = container.GetRequiredService<IUtility>();
            pBudMan = container.GetRequiredService<IBudgetManagement>();
            pConseq = container.GetRequiredService<IConsequenceAdjustedBudget>();
            pInvestments = container.GetRequiredService<IInvestments>();
            _budgetProposals = container.GetRequiredService<IBudgetProposals>();
            _blobHelper = container.GetRequiredService<IAzureBlobHelper>();
            docStyle = PublishHelperFactory.CreateDocumentStyle(container);
            bListDeletedActionsYearAmounts = new double[4];
            _container = container;
            _docTableConfig = new DocTableConfig(pUtility);
            _unitOfWork = container.GetRequiredService<IUnitOfWork>();
            _docWidExport = new DocWidgetExport(container);
        }

        public bool InsertActionDescriptions(int budgetYear, DocumentBuilder builder, String user, IEnumerable<PublishTreeNode> SubTreeNodes, bool incIntDesc,
            string actionType, string budgetPhaseId, bool showOnlyModified, string orgId, bool includeBlistIntDesc, string serviceId = "ALL", bool cabGridSelected = false,
            List<string> lstLimitCodes = null, bool insertBookmark = false, string bookmarkTitle = "", string levelSetUp = "ServiceArea")
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, docStyle, Context, _container);
            bool addSectionId = false;
            bool isBlist = (actionType == clsConstants.BM_Tree_Types.BlistActionText.ToString() || actionType == clsConstants.BM_Tree_Types.BlistActionTextLevel1.ToString());
            try
            {
                bool divideByMillions = pUtility.CheckOnDivideByMillions(user, "Million_doc_actions");
                expLang = pUtility.GetUserDetails(user).language_preference;
                if (LanguageStringsBudgetMgt == null)
                {
                    LanguageStringsBudgetMgt = pUtility.GetLanguageStrings(expLang, user, "BudgetManagement");
                }

                if (LanguageStringsDocExport == null)
                {
                    LanguageStringsDocExport = pUtility.GetLanguageStrings(expLang, user, "docexport");
                }

                if (numberFormats == null)
                {
                    numberFormats = pUtility.GetLanguageStrings(expLang, user, "NumberFormats");
                }

                bool actionHeaderInserted = false;
                foreach (var limits in SubTreeNodes)
                {
                    if (cabGridSelected && lstLimitCodes != null && lstLimitCodes.Any() && lstLimitCodes.Contains(limits.text))
                    {
                        continue;
                    }

                    bool limitHeader = true;
                    string limitHeaderText = limits.text;
                    foreach (var Actions in limits.items.OrderBy(x => x.text).ToList())
                    {
                        if (Actions.@checked)
                        {
                            dynamic ActionData = null;
                            if (actionType == clsConstants.BM_Tree_Types.NewAction.ToString())
                            {
                                if (serviceId == "ALL")
                                {
                                    ActionData = pBudMan.GetActionDetailsForBmDocument(user, Convert.ToInt32(Actions.id), divideByMillions);
                                }
                                else
                                {
                                    ActionData = pBudMan.GetActionDetailsForBmDocumentMultiLevel(user, Convert.ToInt32(Actions.id), orgId, serviceId, divideByMillions, budgetYear);
                                }
                            }
                            else if (actionType == clsConstants.BM_Tree_Types.BlistActionText.ToString() ||
                                actionType == clsConstants.BM_Tree_Types.BlistActionTextLevel1.ToString())
                            {
                                ActionData = pBudMan.GetBListDetailsForBmDocument(user, Convert.ToInt32(Actions.id), orgId, serviceId, budgetYear, levelSetUp: levelSetUp);
                            }
                            if (ActionData.jsonData.Count > 0)
                            {
                                if (showOnlyModified && actionType == clsConstants.BM_Tree_Types.NewAction.ToString())
                                {
                                    addSectionId = true;
                                    //int budgetYear = pUtility.GetActiveBudgetYear(user, clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR.ToString());
                                    List<int> budgetPhaseChangeId = pInvestments.GetChangeDataUsingBudgetPhase(budgetPhaseId, user, showOnlyModified, budgetYear);
                                    int changeIdToGetDescription = budgetPhaseChangeId.Any() ? budgetPhaseChangeId.FirstOrDefault() : -1;
                                    if (changeIdToGetDescription != -1)
                                    {
                                        string activeChangeIdDesc = pConseq.GetBudgetPhaseIdDescriptions(user, budgetYear, Convert.ToInt32(Actions.id),
                                            changeIdToGetDescription);
                                        if (!string.IsNullOrWhiteSpace(activeChangeIdDesc) && Convert.ToBoolean(ActionData.jsonData[0].isFPDisplayed))
                                        {
                                            if (limitHeader)
                                            {
                                                publishHelper.InsertHeading(user, limitHeaderText, Context, insertBookmark, bookmarkTitle);

                                                limitHeader = false;
                                            }

                                            publishHelper.InsertText(Actions.text, 11, true, false);
                                            publishHelper.InsertDescription(activeChangeIdDesc, user, 0);
                                        }
                                    }
                                }
                                else
                                {
                                    string fpd = Convert.ToString(ActionData.jsonData[0].financial_plan_description);
                                    string longDesc = Convert.ToString(ActionData.jsonData[0].longDescription);
                                    string conseq = Convert.ToString(ActionData.jsonData[0].consequence);
                                    actionHeaderInserted = false;

                                    var isFPDisplayedColumnsExist = (string)ActionData.jsonData[0].isFPDisplayed;
                                    if (string.IsNullOrEmpty(isFPDisplayedColumnsExist))
                                    {
                                        if (!string.IsNullOrWhiteSpace(longDesc))
                                        {
                                            if (limitHeader)
                                            {
                                                publishHelper.InsertHeading(user, limitHeaderText, Context, insertBookmark);

                                                limitHeader = false;
                                            }
                                            //102679
                                            publishHelper.InsertText(Actions.text, 11, true, false);
                                            actionHeaderInserted = true;
                                            publishHelper.InsertDescription(longDesc, user, 0);
                                        }
                                    }
                                    else
                                    {
                                        if (Convert.ToBoolean(ActionData.jsonData[0].isFPDisplayed))
                                        {
                                            if (!string.IsNullOrWhiteSpace(longDesc))
                                            {
                                                if (limitHeader)
                                                {
                                                    publishHelper.InsertHeading(user, limitHeaderText, Context, insertBookmark);

                                                    limitHeader = false;
                                                }
                                                //102679
                                                publishHelper.InsertText(Actions.text, 11, true, false);
                                                actionHeaderInserted = true;
                                                publishHelper.InsertDescription(longDesc, user, 0);
                                            }
                                        }
                                    }
                                    if (incIntDesc && !isBlist)
                                    {
                                        if (!string.IsNullOrWhiteSpace(fpd))
                                        {
                                            if (limitHeader)
                                            {
                                                publishHelper.InsertHeading(user, limitHeaderText,
                                                    Context, insertBookmark, bookmarkTitle);

                                                limitHeader = false;
                                            }
                                            if (!actionHeaderInserted) //if header has not already been inserted
                                            {
                                                publishHelper.InsertText(Actions.text, 11, true, false);
                                            }
                                            publishHelper.InsertNormalHeading(LanguageStringsBudgetMgt["doc_internal_desc"].LangText, user, 0, true);
                                            publishHelper.InsertDescription(fpd, user, 0);
                                        }

                                        if (!string.IsNullOrWhiteSpace(conseq))
                                        {
                                            if (limitHeader)
                                            {
                                                publishHelper.InsertHeading(user, limitHeaderText, Context);

                                                limitHeader = false;
                                            }
                                            if (!actionHeaderInserted) //if header has not already been inserted
                                            {
                                                publishHelper.InsertText(Actions.text, 11, true, false);
                                            }

                                            publishHelper.InsertDescription(conseq, user, 0);
                                        }
                                    }
                                    if (includeBlistIntDesc && isBlist)
                                    {
                                        if (!string.IsNullOrWhiteSpace(fpd))
                                        {
                                            if (limitHeader)
                                            {
                                                publishHelper.InsertHeading(user, limitHeaderText,
                                                    Context, insertBookmark, bookmarkTitle);

                                                limitHeader = false;
                                            }
                                            if (!actionHeaderInserted) //if header has not already been inserted
                                            {
                                                publishHelper.InsertText(Actions.text, 11, true, false);
                                            }
                                            publishHelper.InsertNormalHeading(LanguageStringsBudgetMgt["doc_internal_desc"].LangText, user, 0, true);
                                            publishHelper.InsertDescription(fpd, user, 0);
                                        }

                                        if (!string.IsNullOrWhiteSpace(conseq))
                                        {
                                            if (limitHeader)
                                            {
                                                publishHelper.InsertHeading(user, limitHeaderText, Context);

                                                limitHeader = false;
                                            }
                                            if (!actionHeaderInserted) //if header has not already been inserted
                                            {
                                                publishHelper.InsertText(Actions.text, 11, true, false);
                                            }

                                            publishHelper.InsertDescription(conseq, user, 0);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                return addSectionId;
            }
            catch (Exception ex)
            {
                publishHelper.InsertError("InsertActionDescriptions Failed." + ex.Message);
                return false;
            }
        }

        public int InsertBListDeletedActionSetTable(int budgetYearBudMan, DocumentBuilder builder, String user, IEnumerable<PublishTreeNode> SubTreeNodes,
            bool incIntDesc, string actionType, string orgId, string headerText, string serviceId = "ALL", string levelSetUp = "ServiceArea", List<string> relationDepartments = null)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, docStyle, Context, _container);
            string numberTypeFormat = "";
            int rowCount = 0;
            int colCount = 0;
            bool actionSetTableHeaderRowInserted = false;
            bool divideByMillions = false;
            bool bListDelActionHeaderInserted = false;

            try
            {
                expLang = pUtility.GetUserDetails(user).language_preference;
                TableDefinition tableDefBList = _docTableConfig.GetTableDef(user, "BlistAction");
                List<string> defaultColumnBList = tableDefBList.ColumnDefinitions.Where(x => x.IsActive).Select(x => x.ColumnId).ToList();
                //Table Starts
                //check amount format for the divideByMillions , not from parameter
                if (tableDefBList.AmountFormat == AmountFormats.Millions)
                {
                    divideByMillions = true;
                }
                bool anyNodeChecked = false;
                foreach (var item in SubTreeNodes)
                {
                    foreach (var action in item.items)
                    {
                        if (pUtility.AreAnyChildrenChecked(action))
                        {
                            anyNodeChecked = true;
                            break;
                        }
                    }
                }
                if (anyNodeChecked)
                {
                    if (LanguageStringsBudgetMgt == null)
                    {
                        LanguageStringsBudgetMgt = pUtility.GetLanguageStrings(expLang, user, "BudgetManagement");
                    }

                    if (LanguageStringsDocExport == null)
                    {
                        LanguageStringsDocExport = pUtility.GetLanguageStrings(expLang, user, "docexport");
                    }

                    if (numberFormats == null)
                    {
                        numberFormats = pUtility.GetLanguageStrings(expLang, user, "NumberFormats");
                    }

                    Color rowFill = DocumentStyle.GetColorsRows["row1"]; 
                    Dictionary<string, string> actionDescriptions = new Dictionary<string, string>();
                    Dictionary<string, string> actionInternalDescriptions = new Dictionary<string, string>();
                    foreach (var limits in SubTreeNodes)
                    {
                        //bool limitHeader = true;
                        string limitHeaderText = limits.text;
                        foreach (var Actions in limits.items)
                        {
                            if (Actions.@checked)
                            {
                                dynamic ActionData = null;
                                string tableName = PublishSection.GetSection.BDGTBLACTN.ToString();

                                if (actionType == clsConstants.BM_Tree_Types.BlistActionText.ToString() || actionType == clsConstants.BM_Tree_Types.BlistActionTextLevel1.ToString())
                                {
                                    ActionData = pBudMan.GetBListDetailsForBmDocument(user, Convert.ToInt32(Actions.id), orgId, serviceId, budgetYearBudMan, divideByMillions, levelSetUp, relationDepartments);
                                }

                                if (ActionData != null && ActionData.jsonData.Count > 0)
                                {
                                    if (bListDelActionHeaderInserted == false)
                                    {
                                        string bListOrDelActionsHeader = ((LanguageStringsBudgetMgt.FirstOrDefault(d => d.Key == headerText)).Value).LangText;
                                        publishHelper.InsertHeading(user, bListOrDelActionsHeader, Context, true);
                                        publishHelper.StartTableOrientation(tableDefBList);
                                        publishHelper.StartTable(tableName, null, null, tableDefBList);
                                        bListDelActionHeaderInserted = true;
                                    }

                                    colCount = ActionData.Titles.Count;
                                    numberTypeFormat = divideByMillions == true ? numberFormats["dec1"].LangText : numberFormats["amount"].LangText;

                                    if (actionSetTableHeaderRowInserted == false)
                                    {
                                        InsertHeaderRow(user, publishHelper, tableDefBList);
                                        TrackCumulativeTotalYearAmount(ActionData, 0);
                                        actionSetTableHeaderRowInserted = true;
                                    }

                                    //Insert Table Rows
                                    rowCount = 0;
                                    StringBuilder pubPath = new StringBuilder(publishHelper.GetCurrentChapterPath());
                                    pubPath.Append("blistactiondetails/");
                                    foreach (var item in ActionData.jsonData)
                                    {
                                        rowCount++;
                                        InsertActionTableDataRow(user, publishHelper, item, numberTypeFormat, tableDefBList, budgetYearBudMan, pubPath, incIntDesc, actionType);
                                        string internalDesc = string.Empty;
                                        if (incIntDesc && (actionType == clsConstants.BM_Tree_Types.BlistActionText.ToString() || actionType == clsConstants.BM_Tree_Types.BlistActionTextLevel1.ToString()))
                                        {                              

                                            internalDesc = string.IsNullOrEmpty(item.financial_plan_description.ToString()) ? string.Empty
                                                                    : item.financial_plan_description;
                                        }
                                        string description = Convert.ToBoolean(item.isFPDisplayed) ? item.longDescription : string.Empty;
                                        if (!actionDescriptions.Any(x=>x.Key== item.id.ToString()))
                                        {
                                            actionDescriptions.Add(item.id.ToString(), description);
                                            actionInternalDescriptions.Add(item.id.ToString(), internalDesc);
                                        }

                                    }

                                    TrackCumulativeTotalYearAmount(ActionData, rowCount);
                                }
                            }
                        }
                    }

                    if (colCount > 0)
                    {
                        //The data has already been divided by millions if required. The InsterTotalSumRow should not divide it again
                        InsertTotalSumRow(publishHelper, numberTypeFormat, tableDefBList);
                        publishHelper.EndTable();
                        publishHelper.EndTableOrientation();
                    }


                    //inserting action description in doc export
                    if (publishHelper.GetType() == typeof(WordHelper) && (actionDescriptions.Count > 0 || (actionInternalDescriptions.Count > 0 && incIntDesc)))
                    {
                        publishHelper.InsertHeading(user, LanguageStringsBudgetMgt["BM_BList_action_title"].LangText, Context);

                        foreach (var limits in SubTreeNodes)
                        {
                            foreach (var action in limits.items)
                            {
                                publishHelper.InsertText(action.text,11, true, false);

                                if (action.@checked)
                                {
                                    //external description
                                    if(actionDescriptions.GetValueOrDefault(action.id).ToString().Trim() != string.Empty)
                                        publishHelper.InsertDescriptionCk5(actionDescriptions.GetValueOrDefault(action.id).ToString().Trim(), user);
                                    //internal descriptions
                                    if (incIntDesc && actionInternalDescriptions.GetValueOrDefault(action.id).ToString().Trim() != string.Empty )
                                    {
                                        publishHelper.InsertDescriptionCk5(actionInternalDescriptions.GetValueOrDefault(action.id).ToString().Trim(), user);
                                    }
                                    publishHelper.InsertLineBreak();

                                }

                            }
                        }
                    }

                    return rowCount;
                }
                else
                {
                    return 0;
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("InsertBListDeletedActionSetTable Failed.");
                return 0;
            }
        }

        private void InsertHeaderRow(string user, IPublishHelper publishHelper, TableDefinition tableDef)
        {
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();
            ColumnDetails cellTemplate;
            int budgetYear = pUtility.GetActiveBudgetYear(user, clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR.ToString());
            //org id
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(string.Empty);
            //org name
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(string.Empty);
            //org short name
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(string.Empty);
            //amount format
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(string.Empty);
            //priority
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(string.Empty);
            //altercode
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(string.Empty);
            //merged header for 4 years
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            cellTemplate.Column = 4;
            cellTemplate.underlineText = true;
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(LanguageStringsDocExport["doc_FINPLAN_Header"].LangText);

            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading1);
            //Second row
            lstColumnDataDetails.Clear();
            lstColumnDetails.Clear();
            foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
            {
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                lstColumnDetails.Add(cellTemplate);
                if (cdf.ColumnId.ToLower() == "BlistAction_title".ToLower())
                {
                    lstColumnDataDetails.Add(string.Empty);
                }
                else if (cdf.ColumnId.ToLower() == "BlistAction_year1".ToLower())
                {
                    lstColumnDataDetails.Add(budgetYear.ToString());
                }
                else if (cdf.ColumnId.ToLower() == "BlistAction_year2".ToLower())
                {
                    lstColumnDataDetails.Add((budgetYear + 1).ToString());
                }
                else if (cdf.ColumnId.ToLower() == "BlistAction_year3".ToLower())
                {
                    lstColumnDataDetails.Add((budgetYear + 2).ToString());
                }
                else if (cdf.ColumnId.ToLower() == "BlistAction_year4".ToLower())
                {
                    lstColumnDataDetails.Add((budgetYear + 3).ToString());
                }
                else
                {
                    lstColumnDataDetails.Add(cdf.ColumnName);
                }
            }
            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
        }

        private void InsertActionTableDataRow(string user, IPublishHelper publishHelper, dynamic item, string numberTypeFormat, TableDefinition tableDef, int bugetYear, StringBuilder pubPath, bool includesc, string actionType)
        {
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();
            ColumnDetails cellTemplate;
            foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
            {
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                lstColumnDetails.Add(cellTemplate);
            }
            //print data

            pubPath.Append(int.Parse(item.id.ToString()));
            string path = pubPath.ToString();
            path = path.Remove(0, 1);
            pubPath.Append("/");
            pubPath = pUtility.getPath(pubPath.ToString().Length - 1, pubPath.ToString().Split('/')[pubPath.ToString().Split('/').Length - 2].Length + 1, pubPath);
            bool includeActionDetail = publishHelper.GetType() == typeof(WebHelperBm) || publishHelper.GetType() == typeof(WebHelperSyncBM);
            string rowtemplateWithData = string.Empty;
            string description = Convert.ToBoolean(item.isFPDisplayed) ? item.longDescription : string.Empty;
            string formattedDesc = string.IsNullOrEmpty(description) ? string.Empty : Regex.Replace(description.ToString(), "<.*?>", String.Empty);// added this to remove jhtml tags from tool tip description #89263
            rowtemplateWithData = "<a class='web-clickable-links' href='javascript:void(0)' title = \"(Sprettoppvindu) " + formattedDesc + "\" onkeypress = 'openBudgetSADetailPopup(\"" + path + "\")' onClick = 'openBudgetSADetailPopup(\"" + path + "\")'>" + item.actionDescription.ToString() + "<span class='sr-only'>Lenke åpnes i Sprettoppvindu</span></a>";
            lstColumnDataDetails.Add(Convert.ToString(item.orgId));
            lstColumnDataDetails.Add(Convert.ToString(item.orgName));
            lstColumnDataDetails.Add(Convert.ToString(item.orgShortName));
            lstColumnDataDetails.Add(includeActionDetail ? rowtemplateWithData : item.actionDescription.ToString());
            lstColumnDataDetails.Add(Convert.ToString(item.priority));
            lstColumnDataDetails.Add(Convert.ToString(item.actionTypeAlterDes));
            lstColumnDataDetails.Add(Convert.ToDouble((item.year1Ammount).ToString()).ToString(numberTypeFormat));
            lstColumnDataDetails.Add(Convert.ToDouble((item.year2Ammount).ToString()).ToString(numberTypeFormat));
            lstColumnDataDetails.Add(Convert.ToDouble((item.year3Ammount).ToString()).ToString(numberTypeFormat));
            lstColumnDataDetails.Add(Convert.ToDouble((item.year4Ammount).ToString()).ToString(numberTypeFormat));
            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
            if (includeActionDetail)
            {
                dynamic labelsArray = new JArray();
                labelsArray.Add(bugetYear);
                labelsArray.Add(bugetYear + 1);
                labelsArray.Add(bugetYear + 2);
                labelsArray.Add(bugetYear + 3);

                dynamic actionObj = new JObject();
                dynamic actionObjDataArray = new JArray();

                actionObjDataArray.Add(Convert.ToDouble((item.year1Ammount).ToString()).ToString(numberTypeFormat));
                actionObjDataArray.Add(Convert.ToDouble((item.year2Ammount).ToString()).ToString(numberTypeFormat));
                actionObjDataArray.Add(Convert.ToDouble((item.year3Ammount).ToString()).ToString(numberTypeFormat));
                actionObjDataArray.Add(Convert.ToDouble((item.year4Ammount).ToString()).ToString(numberTypeFormat));
                actionObj.Add("Data", actionObjDataArray);
                actionObj.Add("Labels", labelsArray);
                actionObj.Add("WebUrlLink", string.Empty);
                actionObj.Add("popUpTitle", item.actionDescription.ToString());

                string internalDesc = string.Empty;
                string consequenceDesc = string.Empty;
                if (includesc && (actionType == clsConstants.BM_Tree_Types.BlistActionText.ToString() || actionType == clsConstants.BM_Tree_Types.BlistActionTextLevel1.ToString()))
                {
                    consequenceDesc = string.IsNullOrEmpty(item.consequence.ToString()) ? string.Empty
                                                                              : item.consequence;

                    internalDesc = string.IsNullOrEmpty(item.financial_plan_description.ToString()) ? string.Empty
                                            : item.financial_plan_description;
                }
                description = string.IsNullOrEmpty(description) ? string.Empty : description;

                WebHelper wh;
                if (publishHelper.GetType() == typeof(WebHelperBm))
                {
                     wh = (WebHelperBm)publishHelper;
                }
                else
                {
                     wh = (WebHelperSyncBM)publishHelper;
                }
                string processedHtml = wh.TransferImagesInHtml(description);
                processedHtml = wh.ProcessTable(processedHtml);
                description = processedHtml;

                actionObj.Add("popUpDescription1", description);
                actionObj.Add("popUpDescription2", string.IsNullOrEmpty(internalDesc) ? string.Empty : internalDesc);
                actionObj.Add("popUpDescription3", consequenceDesc);
                actionObj.Add("popUpHeaderDesc1", LanguageStringsBudgetMgt["BM_action_long_description"].LangText);
                actionObj.Add("popUpHeaderDesc2", LanguageStringsBudgetMgt["doc_internal_desc"].LangText);
                actionObj.Add("popUpHeaderDesc3", string.Empty);
                actionObj.Add("showPopupDescription", true);
                publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(actionObj), pubPath.ToString(), item.id.ToString(), PubContentHandler.BlobType.Json);
                publishHelper.InsertTextForSearch(Convert.ToString(item.actionDescription).ToString(), Regex.Replace(item.longDescription.ToString(), "<.*?>", String.Empty), user);
            }
        }

        private void InsertTotalSumRow(IPublishHelper publishHelper, string numberTypeFormat, TableDefinition tableDef)
        {
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();
            ColumnDetails cellTemplate;
            foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
            {
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                lstColumnDetails.Add(cellTemplate);
            }
            //print sum row
            lstColumnDataDetails.Add(string.Empty);
            lstColumnDataDetails.Add(string.Empty);
            lstColumnDataDetails.Add(string.Empty);
            lstColumnDataDetails.Add(LanguageStringsBudgetMgt["matrix_table_row_sum_title"].LangText);
            lstColumnDataDetails.Add(string.Empty);
            lstColumnDataDetails.Add(string.Empty);
            lstColumnDataDetails.Add(bListDeletedActionsYearAmounts[0].ToString(numberTypeFormat));
            lstColumnDataDetails.Add(bListDeletedActionsYearAmounts[1].ToString(numberTypeFormat));
            lstColumnDataDetails.Add(bListDeletedActionsYearAmounts[2].ToString(numberTypeFormat));
            lstColumnDataDetails.Add(bListDeletedActionsYearAmounts[3].ToString(numberTypeFormat));
            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.SubTotal1);
        }

        private void TrackCumulativeTotalYearAmount(dynamic jsonActionTab, int rowCount)
        {
            double total = 0;
            string yearColName;
            for (int i = 1; i < 5; i++) //calculate for 4 years
            {
                if (rowCount != 0)
                {
                    yearColName = "year" + i + "Ammount";
                    total = 0;
                    for (int j = 0; j < rowCount; j++)
                    {
                        JToken yearAmount = jsonActionTab.jsonData[j][yearColName];
                        total = total + yearAmount.Value<Double>(); //(divideByMillions == true ? (yearAmount.Value<Double>() / 1000000) : (yearAmount.Value<Double>() / 1000));
                    }
                    bListDeletedActionsYearAmounts[i - 1] = bListDeletedActionsYearAmounts[i - 1] + total;
                }
                else
                {
                    bListDeletedActionsYearAmounts[i - 1] = 0;
                }
            }
        }

        public void InsertReportingWidget(string userId, string nodeTitle, string _context, DocumentBuilder builder, 
                            int budgetYear, string masterNodeId, string nodeId)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(userId, builder, docStyle, Context, _container);
            UserData userData = pUtility.GetUserDetails(userId);
            //var node = _budgetProposals.GetNodeData(Guid.Parse(nodeId), userId).GetAwaiter().GetResult();
            var widgetId = masterNodeId;
            var masterNode = _budgetProposals.GetMasterNodeData(Guid.Parse(widgetId), budgetYear, userId).GetAwaiter().GetResult();
            var nodeData = _budgetProposals.GetNodeData(Guid.Parse(nodeId), userId).GetAwaiter().GetResult();
            if (!string.IsNullOrEmpty(widgetId))
            {
                var reportType = masterNode.node_type == actionList ? ReportTypes.FinPlanReport.ToString() : ReportTypes.InvestmentReport.ToString();
                var widgetData = _unitOfWork.DocWidgetRepo.WidgetConfigurationbyContentId(Guid.Parse(widgetId), userData.tenant_id).GetAwaiter().GetResult();
                var widgetTitle = _budgetProposals.GetWidgetTitle(Guid.Parse(widgetId), userId, masterNode.fk_report_template_id, budgetYear).GetAwaiter().GetResult();
                if (widgetData.Any())
                {
                    var widgetConfigJson = JsonConvert.DeserializeObject<ReportingGridWidgetConfig>(widgetData.First().config_json);
                    publishHelper.InsertHeading(userId, nodeTitle, _context, true, false, nodeId.ToString());
                    string description = $"<section istransferred=\"false\"" +
                            $"_isdefault=\"false\" kostrayear=\"\" name=\"docwidget\"" +
                            $"setupid=\"\" templateid=\"{masterNode.fk_report_template_id}\" templatetext=\"{widgetConfigJson.tableName}\"" +
                            $"reporttext=\"{widgetConfigJson.templateName}\" widgettype=\"{reportType}\" reporttype=\"{reportType}\"" +
                            $"displaytype=\"table\" widgetid=\"{widgetId}\" " +
                            $"class=\"ck-report-template cmn-background2\">" +
                            $"<span class=\"ck-report-template-type\">{widgetTitle}\"</span>" +
                            $"<span class=\"ck-report-template-name\">{widgetConfigJson.tableName}</span></section>";
                    publishHelper.InsertDescriptionCk5(description, userId);

                    //Summary Text
                    if (!string.IsNullOrEmpty(nodeData.description))
                    {
                        if (LanguageStringsBudgetMgt == null)
                        {
                            LanguageStringsBudgetMgt = pUtility.GetLanguageStrings(userData.language_preference, userId, "BudgetManagement");
                        }
                        publishHelper.InsertHeading(userId, LanguageStringsBudgetMgt["BM_Doc_summary_text"].LangText, _context, false);
                        publishHelper.InsertDescriptionCk5(nodeData.description, userId);
                    }
                }
            }
        }
    }
}