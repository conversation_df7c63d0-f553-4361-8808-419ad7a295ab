#pragma warning disable CS8625
#pragma warning disable CS8629

#pragma warning disable CS8073
#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Aspose.Words;
using Aspose.Words.Tables;
using Framsikt.BL;
using Framsikt.BL.Core.BudgetProposalPartial;
using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Strategy;
using Framsikt.BL.Strategy.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.DocExportWorker
{
    public class BMOPPHelper
    {
        private readonly IUtility pUtility = null;
        private readonly IDocumentStyle docStyle = null;
        private readonly string Context = "BMExport";
        private readonly IServiceProvider _container;
        private readonly IOpportunityAssessments _opportunityAssessment;
        private readonly IDocTableConfig _docTableConfig;
        private readonly IClimateAction _climateActions;
        private readonly IBudgetProposal _bpProposal;
        private bool _isStrategyServiceAreaHeaderInserted = false;

        public BMOPPHelper(IServiceProvider container)
        {
            _container = container;
            pUtility = container.GetRequiredService<IUtility>();
            docStyle = PublishHelperFactory.CreateDocumentStyle(container);
            _opportunityAssessment = container.GetRequiredService<IOpportunityAssessments>();
            _docTableConfig = new DocTableConfig(pUtility);
            _climateActions = container.GetRequiredService<IClimateAction>();
            _bpProposal = container.GetRequiredService<IBudgetProposal>();
        }

        #region Public Methods

        public bool InsertStrategyAssessmentAreaNodeData(string user, IPublishHelper publishHelper, PublishTreeNode assessmentAreaNode, int budgetYear, PublishTreeNode parentNode, string strategyServiceArea, bool insertServiceAreaHeader)
        {
            var orgVersionContent = pUtility.GetOrgVersionSpecificContent(user, pUtility.GetForecastPeriod(budgetYear, 1));
            var cityLevel = orgVersionContent.lstOrgHierarchy.FirstOrDefault();

            string[] assessmentInfo = parentNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(assessmentInfo[1]);

            string[] assessmentAreaInfo = assessmentAreaNode.id.Split('_').ToArray();
            Guid assessmentAreaId = Guid.Parse(assessmentAreaInfo[1]);
            string orgOrServiceAreaId = assessmentAreaInfo[2];
            int fpLevel = int.Parse(assessmentAreaInfo[3]);

            AssessmentAreaContentHelper assessmentAreaSpecificData = new AssessmentAreaContentHelper();
            assessmentAreaSpecificData.budgetYear = budgetYear;
            assessmentAreaSpecificData.assessmentId = assessmentId;
            assessmentAreaSpecificData.assessmentAreaId = assessmentAreaId;
            assessmentAreaSpecificData.assessmentAreaName = assessmentAreaNode.text;
            assessmentAreaSpecificData.isAssessmentActive = true;
            assessmentAreaSpecificData.orgLevel = fpLevel;

            string fpLevel2 = pUtility.GetParameterValue(user, "FINPLAN_LEVEL_2");
            bool isServiceIdSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")));

            if (isServiceIdSetup && fpLevel == 2)
            {
                string fp1levelId = assessmentAreaInfo[4];
                assessmentAreaSpecificData.orgId = fp1levelId;
                assessmentAreaSpecificData.serviceId = orgOrServiceAreaId;
                assessmentAreaSpecificData.serviceLevel = fpLevel;
            }
            else
            {
                assessmentAreaSpecificData.orgId = orgOrServiceAreaId;
                if (assessmentAreaNode.type == clsConstants.BM_Tree_Types.AssessmentAreaActionsTable.ToString())
                {
                    assessmentAreaSpecificData.orgLevel = cityLevel.org_id_1 == orgOrServiceAreaId ? 1 : (fpLevel + 1);
                }
                assessmentAreaSpecificData.serviceId = string.Empty;
                assessmentAreaSpecificData.serviceLevel = -1;
            }

            //Get assessment area actions table data
            List<AssessmentAreaActionDataGrid> data = _opportunityAssessment.getAssessmentAreaData(user, assessmentAreaSpecificData).GetAwaiter().GetResult();

            //Do not display sum row if  related actions are not present
            if (data.Any(x => x.actionId != Guid.Empty))
            {
                if (data.FirstOrDefault(x => x.actionType.ToLower() == "Investering".ToLower()) == null)
                {
                    data = data.Where(x => !x.isInvestmentSum).ToList();
                }
                if (data.FirstOrDefault(x => x.actionType.ToLower() == "Drift".ToLower()) == null)
                {
                    data = data.Where(x => !x.isOperationalSum).ToList();
                }
                //if (insertServiceAreaHeader && !string.IsNullOrEmpty(strategyServiceArea))
                //{
                //    publishHelper.InsertHeading(user, strategyServiceArea, Context);
                //    insertServiceAreaHeader = false;
                //}
                publishHelper.InsertHeading(user, parentNode.text, Context);
                //Insert data into document/publish
                InsertDataForStrategyAssessment(user, budgetYear, publishHelper, data, false);
            }
            return insertServiceAreaHeader;
        }

        public void InsertStrategyAssessmentAreaNodeDataForBudgetProposal(string user, IPublishHelper publishHelper, PublishTreeNode assessmentAreaNode, int budgetYear, PublishTreeNode parentNode, bool isFromAssessmentDoc)
        {
            string fpLevel2 = pUtility.GetParameterValue(user, "FINPLAN_LEVEL_2");
            bool isServiceIdSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")));

            string[] assessmentInfo = parentNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(assessmentInfo[1]);

            string[] assessmentAreaInfo = assessmentAreaNode.id.Split('_').ToArray();
            Guid assessmentAreaId = Guid.Parse(assessmentAreaInfo[1]);
            string orgOrServiceAreaId = assessmentAreaInfo[2];
            int fpLevel = int.Parse(assessmentAreaInfo[3]);

            AssessmentAreaContentHelper assessmentAreaSpecificData = new AssessmentAreaContentHelper();
            assessmentAreaSpecificData.budgetYear = budgetYear;
            assessmentAreaSpecificData.assessmentId = assessmentId;
            assessmentAreaSpecificData.assessmentAreaId = assessmentAreaId;
            assessmentAreaSpecificData.assessmentAreaName = assessmentAreaNode.text;
            assessmentAreaSpecificData.isAssessmentActive = true;
            assessmentAreaSpecificData.orgLevel = fpLevel;
            assessmentAreaSpecificData.orgId = orgOrServiceAreaId;
            assessmentAreaSpecificData.serviceId = assessmentAreaInfo[4];
            assessmentAreaSpecificData.serviceLevel = fpLevel;
            assessmentAreaSpecificData.serviceLevel = isServiceIdSetup ? 2 : -1;

            //Get assessment area actions table data
            List<AssessmentAreaActionDataGrid> data = _opportunityAssessment.getAssessmentAreaData(user, assessmentAreaSpecificData).GetAwaiter().GetResult();

            //Do not display sum row if  related actions are not present
            if (data.Any(x => x.actionId != Guid.Empty))
            {
                if (data.FirstOrDefault(x => x.actionType.ToLower() == "Investering".ToLower()) == null)
                {
                    data = data.Where(x => !x.isInvestmentSum).ToList();
                }
                if (data.FirstOrDefault(x => x.actionType.ToLower() == "Drift".ToLower()) == null)
                {
                    data = data.Where(x => !x.isOperationalSum).ToList();
                }
                //Insert data into document/publish
                InsertDataForStrategyAssessment(user, budgetYear, publishHelper, data, isFromAssessmentDoc);
            }
        }

        public void InsertClimateActionCategoryData(string userId, DocumentBuilder builder, int budgetYear, PublishTreeNode categoryNode, bool filterBlistActions)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(userId, builder, docStyle, Context, _container);
            UserData userDetails = pUtility.GetUserDetails(userId);
            try
            {
                Dictionary<string, clsLanguageString> bmLangStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
                Dictionary<string, clsLanguageString> langStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "ClimateAction");
                Dictionary<string, clsLanguageString> numberFormats = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                StringBuilder pubPath = new StringBuilder(publishHelper.GetCurrentChapterPath());
                List<KeyValuePair> userDefinedCols = _climateActions.GetClimateActionColumns(userId, budgetYear, Guid.Parse(categoryNode.id), true, true).GetAwaiter().GetResult();
                List<string> costColumns = new List<string>() { "cost", "totalCost", "longTermEconomicImpact" };
                List<string> reductionColumns = new List<string>() { "reductionPrevYear2", "reductionPrevYear1", "reductionYear1", "reductionYear2", "reductionYear3", "reductionYear4", "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8", "reductionYear9", "reductionYear10", "longTermReduction" };
                KeyValuePair actionName = new KeyValuePair { key = "climateActionName", value = langStrings["FP_climateActionName"].LangText, isChecked = true };
                userDefinedCols.Insert(0, actionName);
                userDefinedCols = userDefinedCols.Where(x => x.key != "actionToAssignment" && x.isChecked).ToList();
                List<string> columnFields = userDefinedCols.Select(x => x.key).ToList();
                //amount format
                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypeDecimalAmount = numberFormats["dec1"].LangText;
                bool divideByMillions = false;
                bool docExport = publishHelper.GetType() != typeof(WebHelperBm);

                var dataset = _climateActions.GetClimateActionDataForDocument(userId, budgetYear, Guid.Parse(categoryNode.id), filterBlistActions, columnFields);
                JArray dataArray = JArray.FromObject(dataset.SelectToken("data"));
                if (dataArray.Any())
                {
                    Guid uniqueId = Guid.NewGuid();
                    int fontSize = docExport ? 8 : 14;

                    if (userDefinedCols.Any(x => reductionColumns.Contains(x.key)))
                    {
                        publishHelper.InsertText((bmLangStrings["ClimateAction_explanationText"].LangText), fontSize, false, false);
                    }
                    if (userDefinedCols.Any(x => costColumns.Contains(x.key)))
                    {
                        publishHelper.InsertText((bmLangStrings["InfoGraphics_divedBy_thousand"].LangText), fontSize, false, false);
                    }

                    publishHelper.StartTable("climateActionsGrid" + uniqueId, null, null);
                    List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                    List<string> lstColumnDataDetails = new List<string>();
                    int colCount = userDefinedCols.Count;
                    foreach (KeyValuePair column in userDefinedCols)
                    {
                        ColumnDetails cellTemplate;
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Fontsize = 10;
                        cellTemplate.BottomBorder = 1;
                        cellTemplate.showBottomBorder = true;
                        cellTemplate.FontColor = FramsiktColors.TotalColor;
                        cellTemplate.IsBold = true;
                        cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                        if (column.key == "climateActionName" || column.key == "description" || column.key == "financeDescription" || column.key == "sector" || column.key == "source" ||
                            column.key == "goal" || column.key == "unsGoal" || column.key == "securityEstimate" || column.key == "feasibility" || column.key == "referenceUrl" || column.key == "finplanAction" || column.key == "investment" ||
                            column.key == "planActionSpecInfo" || column.key == "statusInPlan" || column.key == "reductionQuantityName")
                        {
                            switch (column.key)
                            {
                                case "climateActionName":
                                    cellTemplate.PreferredWidth = colCount > 10 ? 12 : 15;
                                    break;

                                case "description":
                                case "financeDescription":
                                    if (colCount > 10)
                                    {
                                        cellTemplate.PreferredWidth = docExport ? 4 : 9;
                                    }
                                    else
                                    {
                                        cellTemplate.PreferredWidth = docExport ? 4 : 15;
                                    }
                                    break;

                                case "sector":
                                case "source":
                                case "planActionSpecInfo":
                                case "statusInPlan":
                                case "reductionQuantityName":
                                    cellTemplate.PreferredWidth = 6;
                                    break;

                                default:
                                    cellTemplate.PreferredWidth = 10;
                                    break;
                            }
                            cellTemplate.Alignment = ParagraphAlignment.Left;
                        }
                        else
                        {
                            cellTemplate.Alignment = ParagraphAlignment.Right;
                            cellTemplate.PreferredWidth = column.key == "cost" ? 4 : 6;
                        }
                        lstColumnDataDetails.Add(column.value);
                        lstColumnDetails.Add(cellTemplate);
                    }
                    publishHelper.CreateRow1(lstColumnDetails, lstColumnDataDetails, true, RowType.Header2);
                    pubPath.Append("climateactiondesc/");
                    string popupText = "(Sprettoppvindu) ";
                    int dataLength = dataArray.Count;
                    int itemCount = 0;
                    List<string> columns = userDefinedCols.Select(x => x.key).ToList();
                    List<PlanObjectsDescriptionHelper> descriptionHelper = new List<PlanObjectsDescriptionHelper>();
                    List<ClimateActionInvestmentHelper> fpActionOrInvestmentInfo = new List<ClimateActionInvestmentHelper>();

                    //insert data
                    foreach (var item in dataArray)
                    {
                        ClimateActionInvestmentHelper actionInvestmentInfo = JsonConvert.DeserializeObject<ClimateActionInvestmentHelper>(item.SelectToken("fpActionOrInvestment").ToString());
                        if (actionInvestmentInfo != null)
                        {
                            fpActionOrInvestmentInfo.Add(actionInvestmentInfo);
                        }
                        itemCount++;
                        pubPath.Append(int.Parse(item.SelectToken("climateActionId").ToString()));
                        string path = pubPath.ToString();
                        path = path.Remove(0, 1);
                        pubPath.Append("/");
                        pubPath = pUtility.getPath(pubPath.ToString().Length - 1, pubPath.ToString().Split('/')[pubPath.ToString().Split('/').Length - 2].Length + 1, pubPath);
                        lstColumnDataDetails = new List<string>();
                        string rowtemplateWithData = "<a class='web-clickable-links' href='javascript:void(0)' title = \"" + popupText + item.SelectToken("climateActionName").ToString() + "\" onkeypress = 'openBudgetSAPopup(\"" + path + "\")' onClick = 'openBudgetSAPopup(\"" + path + "\")'>" + item.SelectToken("climateActionName").ToString() + "<span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";

                        string costVal = item.SelectToken("cost").ToString();
                        decimal cost = string.IsNullOrEmpty(costVal) ? 0 : decimal.Parse(item.SelectToken("cost").ToString());
                        decimal roundedCostValue = Math.Round(cost);

                        //Total cost
                        string totalCostVal = item.SelectToken("totalCost").ToString();
                        decimal totalCost = string.IsNullOrEmpty(costVal) ? 0 : decimal.Parse(item.SelectToken("totalCost").ToString());
                        decimal roundedTotalCostValue = Math.Round(totalCost);

                        //Total ling term economic impact
                        string totalEconomicImpactVal = item.SelectToken("longTermEconomicImpact").ToString();
                        decimal totalEconomicImpact = string.IsNullOrEmpty(totalEconomicImpactVal) ? 0 : decimal.Parse(item.SelectToken("longTermEconomicImpact").ToString());
                        decimal roundedEconomicImpactValue = Math.Round(totalEconomicImpact);

                        lstColumnDataDetails.Clear();
                        lstColumnDetails.Clear();

                        if (bool.Parse(item.SelectToken("isParent").ToString()) && int.Parse(item.SelectToken("climateActionId").ToString()) != -1)
                        {
                            UpdateClimateDescriptionObject(ref descriptionHelper, item, item.SelectToken("climateActionName").ToString());
                        }

                        foreach (string column in columns)
                        {
                            ColumnDetails cellTemplate;

                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailData);
                            cellTemplate.Fontsize = 8;
                            cellTemplate.TopBorder = 1;
                            cellTemplate.WrapText = true;
                            cellTemplate.IsBold = false;
                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            if (int.Parse(item.SelectToken("climateActionId").ToString()) == -1)
                            {
                                cellTemplate.IsBold = true;
                                cellTemplate.BottomBorder = 1;
                            }
                            if (string.IsNullOrEmpty(item.SelectToken("climateActionName").ToString()))
                            {
                                cellTemplate.TopBorder = 0;
                            }
                            cellTemplate.PreferredWidth = 10;
                            switch (column)
                            {
                                case "climateActionName":
                                    cellTemplate.PreferredWidth = colCount > 10 ? 12 : 15;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(((publishHelper.GetType() == typeof(WebHelperBm)) && (itemCount != dataLength)) ? GetClimateActionName(publishHelper, path, int.Parse(item.SelectToken("climateActionId").ToString()), item.SelectToken("climateActionName").ToString()) :
                                                                                                                                                item.SelectToken("climateActionName").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "description":
                                    if (colCount > 10)
                                    {
                                        cellTemplate.PreferredWidth = docExport ? 4 : 9;
                                    }
                                    else
                                    {
                                        cellTemplate.PreferredWidth = docExport ? 4 : 15;
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                    cellTemplate.ContentType = ContentType.Html;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("description").ToString());
                                    break;

                                case "financeDescription":
                                    if (colCount > 10)
                                    {
                                        cellTemplate.PreferredWidth = docExport ? 4 : 9;
                                    }
                                    else
                                    {
                                        cellTemplate.PreferredWidth = docExport ? 4 : 15;
                                    }
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    cellTemplate.ContentType = ContentType.Html;
                                    lstColumnDataDetails.Add(item.SelectToken("financeDescription").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "cost":
                                    cellTemplate.PreferredWidth = 4;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    if (string.IsNullOrEmpty(costVal) || roundedCostValue == 0)
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperBm))
                                        {
                                            lstColumnDataDetails.Add(string.Empty);
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(divideByMillions ? (roundedCostValue / 1000000).ToString(numberTypeAmount) : (roundedCostValue / 1000).ToString(numberTypeAmount));
                                        }
                                    }
                                    else
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperBm))  //send empty if value is 0 (only for web publish)
                                        {
                                            roundedCostValue = divideByMillions ? (cost / 1000000) : (cost / 1000);
                                            lstColumnDataDetails.Add(Math.Round(roundedCostValue) == 0 ? string.Empty : roundedCostValue.ToString(numberTypeAmount));
                                        }
                                        else //for document
                                        {
                                            lstColumnDataDetails.Add(divideByMillions ? (cost / 1000000).ToString(numberTypeAmount) : (cost / 1000).ToString(numberTypeAmount));
                                        }
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "totalCost":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    if (string.IsNullOrEmpty(totalCostVal) || roundedTotalCostValue == 0)
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperBm))
                                        {
                                            lstColumnDataDetails.Add(string.Empty);
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(divideByMillions ? (roundedTotalCostValue / 1000000).ToString(numberTypeAmount) : (roundedTotalCostValue / 1000).ToString(numberTypeAmount));
                                        }
                                    }
                                    else
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperBm))  //send empty if value is 0 (only for web publish)
                                        {
                                            roundedTotalCostValue = divideByMillions ? (totalCost / 1000000) : (totalCost / 1000);
                                            lstColumnDataDetails.Add(Math.Round(roundedTotalCostValue) == 0 ? string.Empty : roundedTotalCostValue.ToString(numberTypeAmount));
                                        }
                                        else //for document
                                        {
                                            lstColumnDataDetails.Add(divideByMillions ? (totalCost / 1000000).ToString(numberTypeAmount) : (totalCost / 1000).ToString(numberTypeAmount));
                                        }
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "sector":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("sector").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "source":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("source").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionQuantityName":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("reductionQuantityName").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "longTermEconomicImpact":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    if (string.IsNullOrEmpty(totalEconomicImpactVal) || roundedEconomicImpactValue == 0)
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperBm))
                                        {
                                            lstColumnDataDetails.Add(string.Empty);
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(divideByMillions ? (roundedEconomicImpactValue / 1000000).ToString(numberTypeAmount) : (roundedEconomicImpactValue / 1000).ToString(numberTypeAmount));
                                        }
                                    }
                                    else
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperBm))  //send empty if value is 0 (only for web publish)
                                        {
                                            roundedEconomicImpactValue = divideByMillions ? (totalEconomicImpact / 1000000) : (totalEconomicImpact / 1000);
                                            lstColumnDataDetails.Add(Math.Round(roundedEconomicImpactValue) == 0 ? string.Empty : roundedEconomicImpactValue.ToString(numberTypeAmount));
                                        }
                                        else //for document
                                        {
                                            lstColumnDataDetails.Add(divideByMillions ? (totalEconomicImpact / 1000000).ToString(numberTypeAmount) : (totalEconomicImpact / 1000).ToString(numberTypeAmount));
                                        }
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear1":
                                    //Show 0's instaed blank in web
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear1 = decimal.Parse(item.SelectToken("reductionYear1").ToString());
                                    lstColumnDataDetails.Add(reductionYear1.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionPrevYear2":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionPrevYear2 = decimal.Parse(item.SelectToken("reductionPrevYear2").ToString());
                                    lstColumnDataDetails.Add(reductionPrevYear2.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionPrevYear1":
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionPrevYear1 = decimal.Parse(item.SelectToken("reductionPrevYear1").ToString());
                                    lstColumnDataDetails.Add(reductionPrevYear1.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear2":
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    cellTemplate.PreferredWidth = 6;
                                    decimal reductionYear2 = decimal.Parse(item.SelectToken("reductionYear2").ToString());
                                    lstColumnDataDetails.Add(reductionYear2.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear3":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear3 = decimal.Parse(item.SelectToken("reductionYear3").ToString());
                                    lstColumnDataDetails.Add(reductionYear3.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear4":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear4 = decimal.Parse(item.SelectToken("reductionYear4").ToString());
                                    lstColumnDataDetails.Add(reductionYear4.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear5":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear5 = decimal.Parse(item.SelectToken("reductionYear5").ToString());
                                    lstColumnDataDetails.Add(reductionYear5.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear6":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear6 = decimal.Parse(item.SelectToken("reductionYear6").ToString());
                                    lstColumnDataDetails.Add(reductionYear6.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear7":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear7 = decimal.Parse(item.SelectToken("reductionYear7").ToString());
                                    lstColumnDataDetails.Add(reductionYear7.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear8":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear8 = decimal.Parse(item.SelectToken("reductionYear8").ToString());
                                    lstColumnDataDetails.Add(reductionYear8.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear9":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear9 = decimal.Parse(item.SelectToken("reductionYear9").ToString());
                                    lstColumnDataDetails.Add(reductionYear9.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "reductionYear10":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal reductionYear10 = decimal.Parse(item.SelectToken("reductionYear10").ToString());
                                    lstColumnDataDetails.Add(reductionYear10.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "longTermReduction":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal longTermReduction = decimal.Parse(item.SelectToken("longTermReduction").ToString());
                                    lstColumnDataDetails.Add(longTermReduction.ToString(numberTypeDecimalAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "goal":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("goalDescription").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "unsGoal":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    if (publishHelper.GetType() == typeof(WebHelperBm))
                                    {
                                        lstColumnDataDetails.Add(item.SelectToken("unsGoalForWeb").ToString());
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add(item.SelectToken("unsGoalForDoc").ToString());
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "securityEstimate":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("securityEstimate").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "feasibility":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("feasibility").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "referenceUrl":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    if (publishHelper.GetType() == typeof(WebHelperBm))
                                    {
                                        if (!string.IsNullOrEmpty(item.SelectToken("referenceUrl").ToString()))
                                        {
                                            lstColumnDataDetails.Add("<a href=" + item.SelectToken("referenceUrl").ToString() + " target=_blank>" + item.SelectToken("referenceUrl").ToString() + "</a>");
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(item.SelectToken("referenceUrl").ToString());
                                        }
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add(item.SelectToken("referenceUrl").ToString());
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "finplanAction":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("finplanAction").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "investment":
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("investment").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "planActionSpecInfo":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("planActionSpecInfo").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "statusInPlan":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.ContentType = ContentType.Html;
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    lstColumnDataDetails.Add(item.SelectToken("statusInPlan").ToString());
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "financeSumYear1":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal financeSumYear1 = decimal.Parse(item.SelectToken("financeSumYear1").ToString());
                                    lstColumnDataDetails.Add(financeSumYear1.ToString(numberTypeAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "financeSumYear4":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal financeSumYear4 = decimal.Parse(item.SelectToken("financeSumYear4").ToString());
                                    lstColumnDataDetails.Add(financeSumYear4.ToString(numberTypeAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;

                                case "financeSumYear10":
                                    cellTemplate.PreferredWidth = 6;
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    decimal financeSumYear10 = decimal.Parse(item.SelectToken("financeSumYear10").ToString());
                                    lstColumnDataDetails.Add(financeSumYear10.ToString(numberTypeAmount));
                                    lstColumnDetails.Add(cellTemplate);
                                    break;
                            }
                        }

                        if (itemCount == dataLength)
                        {
                            lstColumnDetails.ForEach(x => x.ContentType = ContentType.Html);
                            publishHelper.CreateRow1(lstColumnDetails, lstColumnDataDetails, false, RowType.SubTotal);
                        }
                        else
                        {
                            publishHelper.CreateRow1(lstColumnDetails, lstColumnDataDetails, false, RowType.Detail);
                        }
                    }

                    //Data formatting for popup(in publish)
                    if (publishHelper.GetType() != typeof(WordHelper))
                    {
                        PublishClimateActionNodepupupData(userId, publishHelper, descriptionHelper, budgetYear, pubPath.ToString(), fpActionOrInvestmentInfo, Guid.Parse(categoryNode.id), columns);
                    }
                    publishHelper.EndTable();
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        publishHelper.InsertLineBreak();
                    }
                }
            }
            catch (Exception e)
            {
                publishHelper.InsertError("Failed to insert climate actions: " + e);
            }
        }

        public string InsertClimateActionEmissionGraphData(string user, int budgetYear, bool isDocExport)
        {
            var result = _climateActions.GetClimateActionsEmissionGraph(user, budgetYear, isDocExport).GetAwaiter().GetResult();

            return JsonConvert.SerializeObject(result);
        }

        public bool InsertAssessmentAreaDescription(string user, int budgetYear, IPublishHelper publishHelper, PublishTreeNode descNode, PublishTreeNode parentNode, string strategyServiceArea, bool insertServiceAreaHeader)
        {
            UserData userDetails = pUtility.GetUserDetails(user);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();
            KeyValueStringData assessmentAreaDescInfo;

            string[] assessmentInfo = parentNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(assessmentInfo[1]);
            string[] descNodeInfo = descNode.id.Split('_').ToArray();
            Guid assessmentAreaId = Guid.Parse(descNodeInfo[1]);
            string assessmentServiceAreaId = descNodeInfo[2];
            int fpLevel = int.Parse(descNodeInfo[3]);

            string fpLevel2 = pUtility.GetParameterValue(user, "FINPLAN_LEVEL_2");
            bool isServiceIdSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")));

            if (isServiceIdSetup && fpLevel == 2)
            {
                string fpLevel1Id = descNodeInfo[4];
                assessmentAreaDescInfo = (from a in tenantDbContext.tsa_assessment_area_descriptions
                                          where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear
                                                                                        && a.fk_assessment_id == assessmentId && a.fk_area_id == assessmentAreaId
                                                                                        && a.org_id == fpLevel1Id
                                                                                        && a.service_id == assessmentServiceAreaId && !string.IsNullOrEmpty(a.description)
                                          select new KeyValueStringData
                                          {
                                              Key = a.service_id,
                                              Value = a.description
                                          }).AsNoTracking().Distinct().FirstOrDefault();
            }
            else
            {
                assessmentAreaDescInfo = (from a in tenantDbContext.tsa_assessment_area_descriptions
                                          where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear
                                                                                        && a.fk_assessment_id == assessmentId && a.fk_area_id == assessmentAreaId
                                                                                        && a.org_id == assessmentServiceAreaId && !string.IsNullOrEmpty(a.description)
                                          select new KeyValueStringData
                                          {
                                              Key = a.org_id,
                                              Value = a.description
                                          }).AsNoTracking().Distinct().FirstOrDefault();
            }

            var assessmentAreaActionInfo = tenantDbContext.tsa_assessment_area_actions.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.fk_area_id == assessmentAreaId);
            bool isTableNodeChecked = parentNode.items.FirstOrDefault(x => x.type == clsConstants.BM_Tree_Types.AssessmentAreaActionsTable.ToString() || x.type == clsConstants.BM_Tree_Types.BPAssessmentAreaActionsTable.ToString()).@checked;
            if (assessmentAreaDescInfo != null && !string.IsNullOrEmpty(assessmentAreaDescInfo.Value))
            {
                if ((isTableNodeChecked && assessmentAreaActionInfo == null) || !isTableNodeChecked)
                {
                    //if (!insertServiceAreaHeader && !string.IsNullOrEmpty(strategyServiceArea))
                    //{
                    //    publishHelper.InsertHeading(user, strategyServiceArea, Context);
                    //    insertServiceAreaHeader = false;
                    //}
                    publishHelper.InsertHeading(user, parentNode.text, Context);
                }
                //Insert description text
                if (publishHelper.GetType() == typeof(WebHelperBm))
                {
                    publishHelper.InsertDescription(assessmentAreaDescInfo.Value, user);
                }
                else
                {
                    publishHelper.InsertText(assessmentAreaDescInfo.Value, 10, false, false);
                }
            }
            return insertServiceAreaHeader;
        }

        public void InsertAssessmentAreaDescriptionForDoc(string user, int budgetYear, IPublishHelper publishHelper, PublishTreeNode descNode, PublishTreeNode parentNode)
        {
            UserData userDetails = pUtility.GetUserDetails(user);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();
            KeyValueStringData assessmentAreaDescInfo;

            string[] assessmentInfo = parentNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(assessmentInfo[1]);
            string[] descNodeInfo = descNode.id.Split('_').ToArray();
            Guid assessmentAreaId = Guid.Parse(descNodeInfo[1]);
            string orgId = descNodeInfo[2];
            string serviceId = descNodeInfo[4];

            string fpLevel2 = pUtility.GetParameterValue(user, "FINPLAN_LEVEL_2");
            bool isServiceIdSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")));

            if (isServiceIdSetup)
            {
                assessmentAreaDescInfo = (from a in tenantDbContext.tsa_assessment_area_descriptions
                                          where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear
                                                                                        && a.fk_assessment_id == assessmentId && a.fk_area_id == assessmentAreaId
                                                                                        && a.org_id == orgId
                                                                                        && a.service_id == serviceId && !string.IsNullOrEmpty(a.description)
                                          select new KeyValueStringData
                                          {
                                              Key = a.service_id,
                                              Value = a.description
                                          }).AsNoTracking().Distinct().FirstOrDefault();
            }
            else
            {
                assessmentAreaDescInfo = (from a in tenantDbContext.tsa_assessment_area_descriptions
                                          where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear
                                                                                        && a.fk_assessment_id == assessmentId && a.fk_area_id == assessmentAreaId
                                                                                        && a.org_id == orgId && !string.IsNullOrEmpty(a.description)
                                          select new KeyValueStringData
                                          {
                                              Key = a.org_id,
                                              Value = a.description
                                          }).AsNoTracking().Distinct().FirstOrDefault();
            }

            var assessmentAreaActionInfo = tenantDbContext.tsa_assessment_area_actions.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.fk_area_id == assessmentAreaId);
            //bool isTableNodeChecked = parentNode.items.FirstOrDefault(x => x.type == clsConstants.BM_Tree_Types.AssessmentAreaActionsTable.ToString() || x.type == clsConstants.BM_Tree_Types.BPAssessmentAreaActionsTable.ToString()).@checked;
            if (assessmentAreaDescInfo != null && !string.IsNullOrEmpty(assessmentAreaDescInfo.Value))
            {
                //if ((isTableNodeChecked && assessmentAreaActionInfo == null) || !isTableNodeChecked)
                //{
                //    publishHelper.InsertHeading(user, parentNode.text, Context);
                //}
                publishHelper.InsertHeading(user, descNode.text, Context);
                //Insert description text
                if (publishHelper.GetType() == typeof(WebHelperBm))
                {
                    publishHelper.InsertDescription(assessmentAreaDescInfo.Value, user);
                }
                else
                {
                    publishHelper.InsertText(assessmentAreaDescInfo.Value, 10, false, false);
                }
            }
        }

        public bool InsertAssessmentDescription(string user, IPublishHelper publishHelper, PublishTreeNode descNode, bool isConclusionDesc, string strategyServiceArea, bool insertServiceAreaHeader, int budgetYear)
        {
            var orgVersionContent = pUtility.GetOrgVersionSpecificContent(user, pUtility.GetForecastPeriod(budgetYear, 1));
            var cityLevel = orgVersionContent.lstOrgHierarchy.FirstOrDefault();

            string[] descNodeInfo = descNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(descNodeInfo[1]);
            string orgOrServiceId = descNodeInfo[2];
            int fpLevel = int.Parse(descNodeInfo[3]);

            string fpLevel2 = pUtility.GetParameterValue(user, "FINPLAN_LEVEL_2");
            bool isServiceIdSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")));

            OrgDetailHelper orgInput = new OrgDetailHelper()
            {
                orgId = orgOrServiceId,
                orgLevel = fpLevel,
                serviceId = string.Empty,
            };
            if (isServiceIdSetup && fpLevel == 2)
            {
                orgInput.orgId = descNodeInfo[4];
                orgInput.serviceId = orgOrServiceId;
            }
            else
            {
                orgInput.orgLevel = cityLevel.org_id_1 == orgOrServiceId ? fpLevel : fpLevel + 1;
            }
            tsa_assessment_delegation assessmentDelegationInfo = GetAssessmentDescription(user, assessmentId, isConclusionDesc, orgInput);

            if (assessmentDelegationInfo != null)
            {
                string descriptionText = isConclusionDesc ? assessmentDelegationInfo.assessment_owner_desc : assessmentDelegationInfo.assessment_desc;
                if (!string.IsNullOrEmpty(descriptionText))
                {
                    //if (insertServiceAreaHeader && !string.IsNullOrEmpty(strategyServiceArea))
                    //{
                    //    publishHelper.InsertHeading(user, strategyServiceArea, Context);
                    //    insertServiceAreaHeader = false;
                    //}
                    publishHelper.StartNewLevel(descNode);
                    publishHelper.InsertHeading(user, descNode.text, Context);
                    publishHelper.EndCurrentLevel();
                    publishHelper.InsertDescription(descriptionText, user);
                }
            }
            return insertServiceAreaHeader;
        }

        public void InsertAssessmentDescriptionForBPDoc(string user, IPublishHelper publishHelper, PublishTreeNode descNode, bool isConclusionDesc)
        {
            string[] descNodeInfo = descNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(descNodeInfo[1]);
            string orgId = descNodeInfo[2];
            int fpLevel = int.Parse(descNodeInfo[3]);
            string serviceId = descNodeInfo[4];

            OrgDetailHelper orgInput = new OrgDetailHelper()
            {
                orgId = orgId,
                orgLevel = fpLevel,
                serviceId = serviceId,
            };
            tsa_assessment_delegation assessmentDelegationInfo = GetAssessmentDescription(user, assessmentId, isConclusionDesc, orgInput);
            if (assessmentDelegationInfo != null)
            {
                string descriptionText = isConclusionDesc ? assessmentDelegationInfo.assessment_owner_desc : assessmentDelegationInfo.assessment_desc;
                if (!string.IsNullOrEmpty(descriptionText))
                {
                    publishHelper.InsertHeading(user, descNode.text, Context);
                    publishHelper.InsertDescriptionCk5(descriptionText, user);
                }
            }
        }

        public bool InsertAssessmentNetResultEffectSummery(string userId, int budgetYear, IPublishHelper publishHelper, PublishTreeNode summeryNode, string serviceAreaText, bool isBMDoc, bool insertServiceAreaHeader)
        {
            try
            {
                UserData userDetails = pUtility.GetUserDetails(userId);

                string[] assessmentInfo = summeryNode.id.Split('_').ToArray();
                Guid assessmentId = Guid.Parse(assessmentInfo[1]);

                AssessmentSummeryGridHelper data = _opportunityAssessment.GetAssessmentActionSummeryData(userId, budgetYear, assessmentId).GetAwaiter().GetResult();

                Dictionary<string, clsLanguageString> bmLangStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
                Dictionary<string, clsLanguageString> numberFormats = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                Dictionary<string, clsLanguageString> docExportStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

                List<string> columnFields = new List<string> { "sumDescription", "fpYear3", "fpYear2", "year1", "year2", "year3", "year4" };

                if (data != null && data.summeryMainGridData.Any(x => x.fpYear2Amount != 0 || x.fpYear3Amount != 0 || x.year1Amount != 0 || x.year2Amount != 0 || x.year3Amount != 0 || x.year4Amount != 0))
                {
                    if (isBMDoc)
                    {
                        //insertServiceAreaHeader = InsertServiceAreaHeader(userId, insertServiceAreaHeader, serviceAreaText, publishHelper, isBMDoc);
                        publishHelper.StartNewLevel(summeryNode);
                        publishHelper.InsertHeading(userId, summeryNode.text, Context);
                        publishHelper.EndCurrentLevel();
                    }
                    else
                    {
                        publishHelper.InsertHeading(userId, summeryNode.text, Context);
                    }

                    publishHelper.InsertText((docExportStrings["BM_doc_title_thousands"].LangText), 8, false, true);

                    publishHelper.StartTable("netResultEffectSummery", null, null);

                    InsertAssessmentSummeryTableColumns(userDetails, columnFields, budgetYear, publishHelper);

                    //insert data
                    int itemCount = 0;
                    foreach (var item in data.summeryMainGridData)
                    {
                        InsertAssessmentSummeryActionsDataRows(columnFields, itemCount, item, publishHelper, numberFormats);
                        itemCount++;
                    }
                    publishHelper.EndTable();
                }
                return insertServiceAreaHeader;
            }
            catch (Exception e)
            {
                publishHelper.InsertError("Failed to insert assessment summery table: " + e);
                return false;
            }
        }

        public bool InsertAssessmentOperationActionSummery(string userId, int budgetYear, IPublishHelper publishHelper, PublishTreeNode summeryNode, string serviceAreaText, bool isBMDoc, bool insertServiceAreaHeader)
        {
            try
            {
                UserData userDetails = pUtility.GetUserDetails(userId);

                string[] assessmentInfo = summeryNode.id.Split('_').ToArray();
                Guid assessmentId = Guid.Parse(assessmentInfo[1]);

                AssessmentSummeryGridHelper data = _opportunityAssessment.GetAssessmentSummeryData(userId, budgetYear, assessmentId);
                List<AssessmentSummeryGridDataHelper> summeryData = new List<AssessmentSummeryGridDataHelper>();
                clsConstants.BM_Tree_Types nodeType = (clsConstants.BM_Tree_Types)Enum.Parse(typeof(clsConstants.BM_Tree_Types), summeryNode.type);

                switch (nodeType)
                {
                    case clsConstants.BM_Tree_Types.OperationActionSummery:
                    case clsConstants.BM_Tree_Types.BPOperationActionSummery:
                        summeryData = data.totalAmountGridData;
                        break;

                    case clsConstants.BM_Tree_Types.OperationActionsReductionSummery:
                    case clsConstants.BM_Tree_Types.BPOperationActionsReductionSummery:
                        summeryData = data.negativeAmountGridData;
                        break;

                    case clsConstants.BM_Tree_Types.OperationActionsIncrementSummery:
                    case clsConstants.BM_Tree_Types.BPOperationActionsIncrementSummery:
                        summeryData = data.positiveAmountGridData;
                        break;
                }

                Dictionary<string, clsLanguageString> bmLangStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
                Dictionary<string, clsLanguageString> numberFormats = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                Dictionary<string, clsLanguageString> docExportStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

                List<string> columnFields = new List<string> { "orgName", "year1", "year2", "year3", "year4" };

                if (data != null && summeryData.Any(x => x.year1Amount != 0 || x.year2Amount != 0 || x.year3Amount != 0 || x.year4Amount != 0))
                {
                    if (isBMDoc)
                    {
                        //insertServiceAreaHeader = InsertServiceAreaHeader(userId, insertServiceAreaHeader, serviceAreaText, publishHelper, isBMDoc);
                        publishHelper.StartNewLevel(summeryNode);
                        publishHelper.InsertHeading(userId, summeryNode.text, Context);
                        publishHelper.EndCurrentLevel();
                    }
                    else
                    {
                        publishHelper.InsertHeading(userId, summeryNode.text, Context);
                    }

                    publishHelper.InsertText((docExportStrings["BM_doc_title_thousands"].LangText), 8, false, true);

                    publishHelper.StartTable("operationActionSummeryTable_" + Guid.NewGuid().ToString(), null, null);

                    InsertAssessmentSummeryTableColumns(userDetails, columnFields, budgetYear, publishHelper);
                    int itemCount = 0;

                    foreach (var item in summeryData)
                    {
                        InsertAssessmentSummeryActionsDataRows(columnFields, itemCount, item, publishHelper, numberFormats);
                        itemCount++;
                    }
                    publishHelper.EndTable();
                }
                return insertServiceAreaHeader;
            }
            catch (Exception e)
            {
                publishHelper.InsertError("Failed to insert assessment summery table: " + e);
                return false;
            }
        }

        public void InsertAssessmentChildNodesBudgetProposalDocument(string userId, int budgetYear, IPublishHelper publishHelper, PublishTreeNode child, bool isFromAssessmentDoc = false)
        {
            clsConstants.BM_Tree_Types nodeType = (clsConstants.BM_Tree_Types)Enum.Parse(typeof(clsConstants.BM_Tree_Types), child.type);
            switch (nodeType)
            {
                case clsConstants.BM_Tree_Types.BPAssessmentDescription:
                    if (child.@checked)
                    {
                        InsertAssessmentDescriptionForBPDoc(userId, publishHelper, child, false);
                    }
                    break;

                case clsConstants.BM_Tree_Types.BPAssessmentConclusionDescription:
                    if (child.@checked)
                    {
                        InsertAssessmentDescriptionForBPDoc(userId, publishHelper, child, true);
                    }
                    break;

                case clsConstants.BM_Tree_Types.BPNetResultEffectSummery:
                    if (child.@checked)
                    {
                        InsertAssessmentNetResultEffectSummery(userId, budgetYear, publishHelper, child, string.Empty, false, false);
                    }
                    break;

                case clsConstants.BM_Tree_Types.BPOperationActionSummery:
                case clsConstants.BM_Tree_Types.BPOperationActionsReductionSummery:
                case clsConstants.BM_Tree_Types.BPOperationActionsIncrementSummery:
                    if (child.@checked)
                    {
                        InsertAssessmentOperationActionSummery(userId, budgetYear, publishHelper, child, string.Empty, false, false);
                    }
                    break;

                case clsConstants.BM_Tree_Types.BPAssessmentGuidelineDescription:
                    if (child.@checked)
                    {
                        InsertBPGuideLineAssessmentDescription(userId, publishHelper, child);
                    }
                    break;

                case clsConstants.BM_Tree_Types.BPAssessmentArea:
                    if (child.@checked || child.items.Any(x => x.@checked))
                    {
                        publishHelper.InsertHeading(userId, child.text, Context);
                        try
                        {
                            publishHelper.StartNewLevel(child);
                            InsertBudgetProposalAssessmentAreaInfo(userId, budgetYear, publishHelper, child, isFromAssessmentDoc);
                            publishHelper.EndCurrentLevel();
                        }
                        catch (Exception)
                        {
                            publishHelper.EndCurrentLevel();
                            throw;
                        }
                    }
                    break;
            }
        }

        public void InsertBudgetProposalAssessmentAreaInfo(string userId, int budgetYear, IPublishHelper publishHelper, PublishTreeNode parentNode, bool isFromAssessmentDoc = false)
        {
            foreach (var child in parentNode.items.Where(x => x.@checked))
            {
                clsConstants.BM_Tree_Types nodeType = (clsConstants.BM_Tree_Types)Enum.Parse(typeof(clsConstants.BM_Tree_Types), child.type);
                switch (nodeType)
                {
                    case clsConstants.BM_Tree_Types.BPAssessmentAreaActionsTable:
                        InsertStrategyAssessmentAreaNodeDataForBudgetProposal(userId, publishHelper, child, budgetYear, parentNode, isFromAssessmentDoc);
                        break;

                    case clsConstants.BM_Tree_Types.BPAssessmentAreaDescription:
                        InsertAssessmentAreaDescriptionForDoc(userId, budgetYear, publishHelper, child, parentNode);
                        break;
                }
            }
        }

        public void InsertStrategyDataForBudgetProposalDocument(string userId, int budgetYear, IPublishHelper publishHelper, List<PublishTreeNode> strategyMainNodes, bool isFromAssessmentDoc = false)
        {
            bool isBookmark = isFromAssessmentDoc;
            foreach (var subNode in strategyMainNodes)
            {
                try
                {
                    if (!isFromAssessmentDoc)
                    {
                        publishHelper.StartNewLevel(subNode);
                    }
                    if (subNode.@checked || pUtility.AreAnyChildrenChecked(subNode))
                    {
                        publishHelper.InsertHeading(userId, subNode.text, Context, isBookmark);
                    }
                    foreach (var childNode in subNode.items)
                    {
                        try
                        {
                            publishHelper.StartNewLevel(subNode);
                            clsConstants.BM_Tree_Types nodeType = (clsConstants.BM_Tree_Types)Enum.Parse(typeof(clsConstants.BM_Tree_Types), childNode.type);
                            if (isFromAssessmentDoc && (nodeType == clsConstants.BM_Tree_Types.BPAssessmentAreaActionsTable || nodeType == clsConstants.BM_Tree_Types.BPAssessmentAreaDescription))
                            {
                                if (nodeType == clsConstants.BM_Tree_Types.BPAssessmentAreaActionsTable) InsertStrategyAssessmentAreaNodeDataForBudgetProposal(userId, publishHelper, childNode, budgetYear, subNode, isFromAssessmentDoc);
                                else InsertAssessmentAreaDescriptionForDoc(userId, budgetYear, publishHelper, childNode, subNode);

                            }
                            else
                            {
                                InsertAssessmentChildNodesBudgetProposalDocument(userId, budgetYear, publishHelper, childNode, isFromAssessmentDoc);
                            }
                            publishHelper.EndCurrentLevel();
                        }
                        catch (Exception)
                        {
                            publishHelper.EndCurrentLevel();
                            throw;
                        }
                    }
                    if (!isFromAssessmentDoc)
                    {
                        publishHelper.EndCurrentLevel();
                    }
                }
                catch (Exception)
                {
                    if (!isFromAssessmentDoc)
                    {
                        publishHelper.EndCurrentLevel();
                    }
                    throw;
                }
            }
        }

        public bool InsertGuidlineAssessmentDescription(string user, IPublishHelper publishHelper, PublishTreeNode descNode, string strategyServiceArea, bool insertServiceAreaHeader)
        {
            UserData userDetails = pUtility.GetUserDetails(user);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();
            string[] descNodeInfo = descNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(descNodeInfo[1]);

            tsa_assessments tsa_AssessmentsInfo = null;
            if (assessmentId != null && Guid.Empty != assessmentId)
            {
                tsa_AssessmentsInfo = tenantDbContext.tsa_assessments.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_assessment_id == assessmentId);
            }
            if (tsa_AssessmentsInfo != null && !String.IsNullOrEmpty(tsa_AssessmentsInfo.backround_objective))
            {
                string descriptionText = tsa_AssessmentsInfo.backround_objective;
                //if (insertServiceAreaHeader && !string.IsNullOrEmpty(strategyServiceArea))
                //{
                //    publishHelper.InsertHeading(user, strategyServiceArea, Context);
                //    insertServiceAreaHeader = false;
                //}
                publishHelper.StartNewLevel(descNode);
                publishHelper.InsertHeading(user, descNode.text, Context);
                publishHelper.EndCurrentLevel();
                publishHelper.InsertDescriptionCk5(descriptionText, user);
            }
            return insertServiceAreaHeader;
        }

        public void InsertBudgetProposalStrategyData(string userId, string orgId, string serviceId, int budgetYear, IPublishHelper publishHelper, PublishTreeNode node, string chapterId = null, bool? isSyncExport = false)
        {
            UserData userDetails = pUtility.GetUserDetails(userId);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();
            List<BudPropStratxtHelper> strategyData = new List<BudPropStratxtHelper>();
            var targetUseUNGoals = pUtility.GetParameterValue(userId, "TARGET_USE_UNGOALS").ToLower() == "true" ? true : false;
            TableDefinition tableDef;
            tableDef = _docTableConfig.GetTableDef(userId, "StrategyTableServiceArea");

            var orgLevelInfo = tenantDbContext.vw_tco_parameters.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name.ToUpper() == "BMDOC_USE_AS_SA");
            bool isServiceSetup = orgLevelInfo != null && orgLevelInfo.param_value.ToLower().StartsWith("service");

            if (!isServiceSetup)
            {
                int orgLevel = orgLevelInfo != null ? int.Parse(orgLevelInfo.param_value.Last().ToString()) : 1;
                if (isSyncExport.HasValue && isSyncExport.Value)
                {
                    orgLevel = 1;
                }
                strategyData = _bpProposal.FetchStraTxtData(userId, orgId, orgLevel, serviceId, budgetYear, false, chapterId, isSyncExport.HasValue ? isSyncExport.Value : false);
            }
            else
            {
                strategyData = GetBudgetProposalStrategyFilterByServiceId(userId, budgetYear, serviceId, chapterId);
            }

            List<BudgetProposalStrategyDocHelper> strategySet = new List<BudgetProposalStrategyDocHelper>();
            List<BudgetProposalStrategyDocHelper> goalSet = new List<BudgetProposalStrategyDocHelper>();
            List<BudgetProposalStrategyDocHelper> targetSet = new List<BudgetProposalStrategyDocHelper>();
            List<BudgetProposalStrategyDocHelper> focusAreaSet = new List<BudgetProposalStrategyDocHelper>();
            List<BudgetProposalStrategyDocHelper> unsGoalSet = new List<BudgetProposalStrategyDocHelper>();
            List<BudgetProposalStrategyDocHelper> unsTargetSet = new List<BudgetProposalStrategyDocHelper>();
            List<BudgetProposalStrategyDocHelper> targetUNGoalSet = new List<BudgetProposalStrategyDocHelper>();


            var goalBaseData = tenantDbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).ToList();
            var targetBaseData = tenantDbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).ToList();
            var focusAreaBaseData = tenantDbContext.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).ToList();
            var unsGoalBaseData = tenantDbContext.gco_un_susdev_goals.Select(x => x).ToList();
            var unsTargetBaseData = tenantDbContext.gco_un_susdev_targets.Select(x => x).ToList();
            foreach (var item in strategyData)
            {
                BudgetProposalStrategyDocHelper row = new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = item.strategyName,
                    StrategyDesc = item.strategyDesc,
                    StrategyId = item.straTxtId,
                    GoalName = string.Empty,
                    TargetName = string.Empty,
                    FocusAreaName = string.Empty,
                    UNSGoalName = string.Empty,
                    UNSTargetName = string.Empty,
                    GoalId = string.Empty,
                    TargetId = string.Empty,
                    FocusAreaId = string.Empty,
                    UNSGoalId = string.Empty,
                    UNSTargetId = string.Empty,
                    TargetUNGoalId = string.Empty,
                    TargetUNGoalName = string.Empty
                };
                strategySet.Add(row);

                var filteredTargetData = targetBaseData.Where(x => item.targetsLinked.Contains(x.pk_target_id)).OrderBy(x => x.target_name).ToList();

                //Get goalids if in case target is selected and goal is not selected
                item.goalsLinked.AddRange(filteredTargetData.Select(x => x.fk_goal_id).ToList());
                var filteredGoalBaseData = goalBaseData.Where(x => item.goalsLinked.Contains(x.pk_goal_id)).ToList();

                GetGoalSetConnectedToStrategy(filteredGoalBaseData, item.straTxtId, ref goalSet, ref unsGoalSet, ref focusAreaSet, focusAreaBaseData, unsGoalBaseData);

                GetTargetSetConnectedToStrategy(filteredTargetData, item.straTxtId, ref targetSet, ref unsTargetSet, ref targetUNGoalSet, unsTargetBaseData, unsGoalBaseData, targetUseUNGoals);
            }
            var finalSet = new List<BudgetProposalStrategyDocHelper>();
            if (targetUseUNGoals)
            {
                finalSet = (from a in strategySet
                            join b in goalSet on a.StrategyId equals b.StrategyId into g1
                            from grp1 in g1.DefaultIfEmpty()
                            join c in targetSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId } equals new { a = c.StrategyId, b = c.GoalId } into g2
                            from grp2 in g2.DefaultIfEmpty()
                            join d in focusAreaSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId } equals new { a = d.StrategyId, b = d.GoalId } into g3
                            from grp3 in g3.DefaultIfEmpty()
                            join e in unsGoalSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId } equals new { a = e.StrategyId, b = e.GoalId } into g4
                            from grp4 in g4.DefaultIfEmpty()
                            join f in unsTargetSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId, c = grp2 == null ? string.Empty : grp2.TargetId } equals new { a = f.StrategyId, b = f.GoalId, c = f.TargetId } into g5
                            from grp5 in g5.DefaultIfEmpty()
                            join g in targetUNGoalSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId, c = grp2 == null ? string.Empty : grp2.TargetId } equals new { a = g.StrategyId, b = g.GoalId, c = g.TargetId } into g6
                            from grp6 in g6.DefaultIfEmpty()
                            group new { a } by new
                            {
                                a.StrategyId,
                                a.StrategyDesc,
                                a.StrategyName,
                                goalName = grp1 == null ? string.Empty : grp1.GoalName,
                                goalId = grp1 == null ? string.Empty : grp1.GoalId,
                                targetName = grp2 == null ? string.Empty : grp2.TargetName,
                                targetId = grp2 == null ? string.Empty : grp2.TargetId,
                                foucAreaName = grp3 == null ? string.Empty : grp3.FocusAreaName,
                                focusAreaId = grp3 == null ? string.Empty : grp3.FocusAreaId,
                                unsGoalName = grp4 == null ? string.Empty : grp4.UNSGoalName,
                                unsGoalId = grp4 == null ? string.Empty : grp4.UNSGoalId,
                                targetUNGoalName = grp6 == null ? string.Empty : grp6.TargetUNGoalName,
                                targetUNGoalId = grp6 == null ? string.Empty : grp6.TargetUNGoalId
                            } into grp
                            select new BudgetProposalStrategyDocHelper()
                            {
                                StrategyName = grp.Key.StrategyName,
                                StrategyDesc = grp.Key.StrategyDesc,
                                StrategyId = grp.Key.StrategyId,
                                GoalName = grp.Key.goalName,
                                TargetName = grp.Key.targetName,
                                FocusAreaName = grp.Key.foucAreaName,
                                UNSGoalName = grp.Key.unsGoalName,
                                UNSTargetName = string.Empty,
                                GoalId = grp.Key.goalId,
                                TargetId = grp.Key.targetId,
                                FocusAreaId = grp.Key.focusAreaId,
                                UNSGoalId = grp.Key.unsGoalId,
                                UNSTargetId = string.Empty,
                                TargetUNGoalName = grp.Key.targetUNGoalName,
                                TargetUNGoalId = grp.Key.targetUNGoalName
                            }).OrderBy(x => x.StrategyName).ThenBy(x => x.GoalName).ThenBy(x => x.TargetName).ToList();
            }
            else
            {
                finalSet = (from a in strategySet
                            join b in goalSet on a.StrategyId equals b.StrategyId into g1
                            from grp1 in g1.DefaultIfEmpty()
                            join c in targetSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId } equals new { a = c.StrategyId, b = c.GoalId } into g2
                            from grp2 in g2.DefaultIfEmpty()
                            join d in focusAreaSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId } equals new { a = d.StrategyId, b = d.GoalId } into g3
                            from grp3 in g3.DefaultIfEmpty()
                            join e in unsGoalSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId } equals new { a = e.StrategyId, b = e.GoalId } into g4
                            from grp4 in g4.DefaultIfEmpty()
                            join f in unsTargetSet on new { a = a.StrategyId, b = grp1 == null ? string.Empty : grp1.GoalId, c = grp2 == null ? string.Empty : grp2.TargetId } equals new { a = f.StrategyId, b = f.GoalId, c = f.TargetId } into g5
                            from grp5 in g5.DefaultIfEmpty()
                            group new { a } by new
                            {
                                a.StrategyId,
                                a.StrategyDesc,
                                a.StrategyName,
                                goalName = grp1 == null ? string.Empty : grp1.GoalName,
                                goalId = grp1 == null ? string.Empty : grp1.GoalId,
                                targetName = grp2 == null ? string.Empty : grp2.TargetName,
                                targetId = grp2 == null ? string.Empty : grp2.TargetId,
                                foucAreaName = grp3 == null ? string.Empty : grp3.FocusAreaName,
                                focusAreaId = grp3 == null ? string.Empty : grp3.FocusAreaId,
                                unsGoalName = grp4 == null ? string.Empty : grp4.UNSGoalName,
                                unsGoalId = grp4 == null ? string.Empty : grp4.UNSGoalId,
                                unsTargetName = grp5 == null ? string.Empty : grp5.UNSTargetName,
                                unsTargetId = grp5 == null ? string.Empty : grp1.UNSTargetId,

                            } into grp
                            select new BudgetProposalStrategyDocHelper()
                            {
                                StrategyName = grp.Key.StrategyName,
                                StrategyDesc = grp.Key.StrategyDesc,
                                StrategyId = grp.Key.StrategyId,
                                GoalName = grp.Key.goalName,
                                TargetName = grp.Key.targetName,
                                FocusAreaName = grp.Key.foucAreaName,
                                UNSGoalName = grp.Key.unsGoalName,
                                UNSTargetName = grp.Key.unsTargetName,
                                GoalId = grp.Key.goalId,
                                TargetId = grp.Key.targetId,
                                FocusAreaId = grp.Key.focusAreaId,
                                UNSGoalId = grp.Key.unsGoalId,
                                UNSTargetId = grp.Key.unsTargetId,
                                TargetUNGoalId = string.Empty,
                                TargetUNGoalName = string.Empty
                            }).OrderBy(x => x.StrategyName).ThenBy(x => x.GoalName).ThenBy(x => x.TargetName).ToList();
            }


            finalSet = GroupBPStrategyDataBasedOnColumns(finalSet, tableDef, targetUseUNGoals);

            //Insert data into doc/publish
            if (finalSet.Any())
            {
                InsertBPStrategyTable(userId, finalSet, publishHelper, node, tableDef, chapterId, targetUseUNGoals);
            }
        }

        #endregion Public Methods

        #region Private Methods

        private List<BudgetProposalStrategyDocHelper> GroupBPStrategyDataBasedOnColumns(List<BudgetProposalStrategyDocHelper> data, TableDefinition tableDef, bool targetUseUNGoals = false)
        {
            //Group the data based on columns selected in setup
            var activeColumns = tableDef.ColumnDefinitions.Where(x => x.IsActive).ToList();
            bool isGoalColActive = activeColumns.Any(x => x.ColumnId == "goal");
            bool isUNSGoalActive = activeColumns.Any(x => x.ColumnId == "unsgoal");
            bool isTargetActive = activeColumns.Any(x => x.ColumnId == "target");
            bool isUNSTargetActive = activeColumns.Any(x => x.ColumnId == "untarget");
            bool isFocusAreaActive = activeColumns.Any(x => x.ColumnId == "focusarea");
            bool isTargetUNGoalActive = activeColumns.Any(x => x.ColumnId == "unGoalsForTarget");
            string firstSelectedColumn = tableDef.ColumnDefinitions.Where(x => x.IsActive).OrderBy(x => x.sortOrder).First().ColumnId;

            data = (from a in data
                    group new { a } by new
                    {
                        a.StrategyId,
                        a.StrategyName,
                        a.StrategyDesc,
                        targetName = isTargetActive ? a.TargetName : string.Empty,
                        targetId = isTargetActive ? a.TargetId : string.Empty,
                        unGoalName = isUNSGoalActive ? a.UNSGoalName : string.Empty,
                        unGoalId = isUNSGoalActive ? a.UNSGoalId : string.Empty,
                        unsTargetName = isUNSTargetActive ? a.UNSTargetName : string.Empty,
                        unsTargetId = isUNSTargetActive ? a.UNSTargetId : string.Empty,
                        focusAreaName = isFocusAreaActive ? a.FocusAreaName : string.Empty,
                        focusAreaId = isFocusAreaActive ? a.FocusAreaId : string.Empty,
                        goalId = isGoalColActive ? a.GoalId : string.Empty,
                        goalName = isGoalColActive ? a.GoalName : string.Empty,
                        targetUNGoalName = isTargetUNGoalActive ? a.TargetUNGoalName : string.Empty,
                        targetUNGoalId = isTargetUNGoalActive ? a.TargetUNGoalId : string.Empty,
                    } into g
                    select new BudgetProposalStrategyDocHelper()
                    {
                        StrategyName = g.Key.StrategyName,
                        StrategyDesc = g.Key.StrategyDesc,
                        StrategyId = g.Key.StrategyId,
                        GoalName = g.Key.goalName,
                        TargetName = g.Key.targetName,
                        FocusAreaName = g.Key.focusAreaName,
                        UNSGoalName = g.Key.unGoalName,
                        UNSTargetName = g.Key.unsTargetName,
                        GoalId = g.Key.goalId,
                        TargetId = g.Key.targetId,
                        FocusAreaId = g.Key.focusAreaId,
                        UNSGoalId = g.Key.unGoalId,
                        UNSTargetId = g.Key.unsTargetId,
                        TargetUNGoalId = g.Key.targetUNGoalId,
                        TargetUNGoalName = g.Key.targetUNGoalName,
                    }).OrderBy(x => string.IsNullOrWhiteSpace(x.FocusAreaName)).ThenBy(x => x.FocusAreaName)
                    .OrderBy(x => string.IsNullOrWhiteSpace(x.GoalName)).ThenBy(x => x.GoalName)
                    .OrderBy(x => string.IsNullOrWhiteSpace(x.UNSGoalName)).ThenBy(x => x.UNSGoalName)
                    .OrderBy(x => string.IsNullOrWhiteSpace(x.TargetName)).ThenBy(x => x.TargetName)
                    .OrderBy(x => string.IsNullOrWhiteSpace(x.UNSTargetName)).ThenBy(x => x.UNSTargetName)
                    .OrderBy(x => string.IsNullOrWhiteSpace(x.TargetUNGoalName)).ThenBy(x => x.TargetUNGoalName)
                    .OrderBy(x => string.IsNullOrWhiteSpace(x.StrategyName)).ThenBy(x => x.StrategyName).ToList();

            //Order data based on first selected column #88216
            switch (firstSelectedColumn)
            {
                case "focusarea":
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.FocusAreaName)).ThenBy(x => x.FocusAreaName).ToList();
                    break;

                case "goal":
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.GoalName)).ThenBy(x => x.TargetName).ToList();
                    break;

                case "unsgoal":
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.UNSGoalName)).ThenBy(x => x.UNSGoalName).ToList();
                    break;

                case "target":
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.TargetName)).ThenBy(x => x.TargetName).ToList();
                    break;

                case "untarget":
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.UNSTargetName)).ThenBy(x => x.UNSTargetName).ToList();
                    break;
                case "unGoalsForTarget":
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.TargetUNGoalName)).ThenBy(x => x.TargetUNGoalName).ToList();
                    break;
                default:
                    data = data.OrderBy(x => string.IsNullOrWhiteSpace(x.StrategyName)).ThenBy(x => x.StrategyName).ToList();
                    break;
            }

            return data;
        }

        private List<BudPropStratxtHelper> GetBudgetProposalStrategyFilterByServiceId(string userId, int budgetYear, string serviceId, string chapterId)
        {
            UserData userDetails = pUtility.GetUserDetails(userId);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();
            List<BudPropStratxtHelper> result = new List<BudPropStratxtHelper>();

            var strategyData = (from a in tenantDbContext.tfp_strategy_text
                                join b in tenantDbContext.tfp_strategy_goal on new { a = a.fk_tenant_id, b = a.pk_strategy_id } equals
                                                                               new { a = b.fk_tenant_id, b = b.fk_strategy_id } into g1
                                from grp1 in g1.DefaultIfEmpty()
                                join c in tenantDbContext.tfp_strategy_target on new { a = a.fk_tenant_id, b = a.pk_strategy_id } equals
                                                                               new { a = c.fk_tenant_id, b = c.fk_strategy_id } into g2
                                from grp2 in g2.DefaultIfEmpty()
                                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear && a.service_id == serviceId
                                select new
                                {
                                    strategyId = a.pk_strategy_id,
                                    strategyName = a.strategy_name,
                                    strategyDesc = a.strategy_desc,
                                    goalId = grp1 == null ? Guid.Empty : grp1.fk_goal_id,
                                    targetd = grp2 == null ? Guid.Empty : grp2.fk_target_id,
                                    fkAttributeId = a.fk_attribute_id
                                }).ToList();
            if (!string.IsNullOrEmpty(chapterId))
            {
                strategyData = strategyData.Where(x => x.fkAttributeId == chapterId).ToList();
            }
            var groupedData = (from a in strategyData
                               group a by new { a.strategyDesc, a.strategyId, a.strategyName } into g
                               select new
                               {
                                   strategyName = g.Key.strategyName,
                                   strategyId = g.Key.strategyId,
                                   strategyDesc = g.Key.strategyDesc
                               }).ToList();
            foreach (var item in groupedData)
            {
                BudPropStratxtHelper row = new BudPropStratxtHelper();
                row.straTxtId = item.strategyId.ToString();
                row.strategyName = item.strategyName;
                row.strategyDesc = item.strategyDesc;
                row.goalsLinked = strategyData.Where(x => x.strategyId == item.strategyId && x.goalId != Guid.Empty).Select(y => y.goalId).ToList();
                row.targetsLinked = strategyData.Where(x => x.strategyId == item.strategyId && x.targetd != Guid.Empty).Select(y => y.targetd).ToList();
                result.Add(row);
            }
            return result;
        }

        private void GetGoalSetConnectedToStrategy(List<tco_goals> goalBaseData, string strategyId, ref List<BudgetProposalStrategyDocHelper> goalSet,
                                                    ref List<BudgetProposalStrategyDocHelper> unsGoalSet, ref List<BudgetProposalStrategyDocHelper> focusAreaSet,
                                                    List<tco_focusarea> focusAreaBaseData, List<gco_un_susdev_goals> unsGoalBaseData)
        {
            foreach (var item in goalBaseData)
            {
                List<int> focusAreaIds = new List<int>();
                List<string> unsGoalIds = new List<string>();
                BudgetProposalStrategyDocHelper row = new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = string.Empty,
                    StrategyDesc = string.Empty,
                    StrategyId = strategyId,
                    GoalName = item.goal_name,
                    GoalId = item.pk_goal_id.ToString(),
                    TargetName = string.Empty,
                    FocusAreaName = string.Empty,
                    UNSGoalName = string.Empty,
                    UNSTargetName = string.Empty,
                    TargetId = string.Empty,
                    FocusAreaId = string.Empty,
                    UNSGoalId = string.Empty,
                    UNSTargetId = string.Empty
                };
                goalSet.Add(row);

                if (!string.IsNullOrEmpty(item.focus_area))
                {
                    focusAreaIds.AddRange(Array.ConvertAll(item.focus_area.Split(','), int.Parse).ToList());
                    focusAreaSet.AddRange(GetFocusAreaConnectedToStrategy(focusAreaBaseData.Where(x => focusAreaIds.Contains(x.pk_id)).ToList(), strategyId, item.pk_goal_id.ToString()));
                }

                if (!string.IsNullOrEmpty(item.unsd_goals))
                {
                    unsGoalIds.AddRange(item.unsd_goals.Split(',').ToList());
                    unsGoalSet.AddRange(GetUnsGoalConnectedToStrategy(unsGoalBaseData.Where(x => unsGoalIds.Contains(x.pk_goal_id)).ToList(), strategyId, item.pk_goal_id.ToString()));
                }
            }
        }

        private List<BudgetProposalStrategyDocHelper> GetFocusAreaConnectedToStrategy(List<tco_focusarea> focusAreaBaseData, string strategyId, string goalId)
        {
            List<BudgetProposalStrategyDocHelper> focusAreaSet = new List<BudgetProposalStrategyDocHelper>();
            foreach (var item in focusAreaBaseData)
            {
                focusAreaSet.Add(new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = string.Empty,
                    StrategyDesc = string.Empty,
                    StrategyId = strategyId,
                    GoalName = string.Empty,
                    GoalId = goalId,
                    TargetName = string.Empty,
                    FocusAreaName = item.focusarea_description,
                    UNSGoalName = string.Empty,
                    UNSTargetName = string.Empty,
                    TargetId = string.Empty,
                    FocusAreaId = item.pk_id.ToString(),
                    UNSGoalId = string.Empty,
                    UNSTargetId = string.Empty
                });
            }
            return focusAreaSet;
        }

        private List<BudgetProposalStrategyDocHelper> GetUnsGoalConnectedToStrategy(List<gco_un_susdev_goals> unsGoalBaseData, string strategyId, string goalId)
        {
            List<BudgetProposalStrategyDocHelper> unsGoalSet = new List<BudgetProposalStrategyDocHelper>();
            foreach (var item in unsGoalBaseData)
            {
                unsGoalSet.Add(new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = string.Empty,
                    StrategyDesc = string.Empty,
                    StrategyId = strategyId,
                    GoalName = string.Empty,
                    GoalId = goalId,
                    TargetName = string.Empty,
                    FocusAreaName = string.Empty,
                    UNSGoalName = item.goal_short_name,
                    UNSTargetName = string.Empty,
                    TargetId = string.Empty,
                    FocusAreaId = string.Empty,
                    UNSGoalId = item.pk_goal_id,
                    UNSTargetId = string.Empty
                });
            }
            return unsGoalSet;
        }
        private List<BudgetProposalStrategyDocHelper> GetUnsGoalConnectedToTarget(List<gco_un_susdev_goals> unsGoalBaseData, string strategyId, string goalId, string targetId)
        {
            List<BudgetProposalStrategyDocHelper> unsGoalSet = new List<BudgetProposalStrategyDocHelper>();
            foreach (var item in unsGoalBaseData)
            {
                unsGoalSet.Add(new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = string.Empty,
                    StrategyDesc = string.Empty,
                    StrategyId = strategyId,
                    GoalName = string.Empty,
                    GoalId = goalId,
                    TargetName = string.Empty,
                    FocusAreaName = string.Empty,
                    UNSGoalName = string.Empty,
                    UNSTargetName = string.Empty,
                    TargetId = targetId,
                    FocusAreaId = string.Empty,
                    UNSGoalId = string.Empty,
                    UNSTargetId = string.Empty,
                    TargetUNGoalId = item.pk_goal_id,
                    TargetUNGoalName = item.goal_short_name,
                });
            }
            return unsGoalSet;
        }
        private void GetTargetSetConnectedToStrategy(List<tco_targets> targetBaseData, string strategyId, ref List<BudgetProposalStrategyDocHelper> targetSet,
            ref List<BudgetProposalStrategyDocHelper> unsTargetSet, ref List<BudgetProposalStrategyDocHelper> targetUNGoalSet, List<gco_un_susdev_targets> unsTargetBaseData, List<gco_un_susdev_goals> unsGoalBaseData, bool targetUseUNGoals = false)
        {
            foreach (var item in targetBaseData)
            {
                List<string> unsTargetIds = new List<string>();
                List<string> targetUNGoalIds = new List<string>();
                BudgetProposalStrategyDocHelper row = new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = string.Empty,
                    StrategyDesc = string.Empty,
                    StrategyId = strategyId,
                    GoalName = string.Empty,
                    GoalId = item.fk_goal_id.ToString(),
                    TargetName = item.target_name,
                    FocusAreaName = string.Empty,
                    UNSGoalName = string.Empty,
                    UNSTargetName = string.Empty,
                    TargetId = item.pk_target_id.ToString(),
                    FocusAreaId = string.Empty,
                    UNSGoalId = string.Empty,
                    UNSTargetId = string.Empty,
                    TargetUNGoalId = string.Empty,
                    TargetUNGoalName = string.Empty
                };
                targetSet.Add(row);
                if (!targetUseUNGoals)
                {
                    if (!string.IsNullOrEmpty(item.unsd_target))
                    {
                        unsTargetIds.AddRange(item.unsd_target.Split(',').ToList());
                        unsTargetSet.AddRange(GetUnsTargetConnectedToStrategy(unsTargetBaseData.Where(x => unsTargetIds.Contains(x.pk_target_id)).ToList(), strategyId, item.pk_target_id.ToString(), item.fk_goal_id.ToString()));
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(item.unsd_goal))
                    {
                        targetUNGoalIds.AddRange(item.unsd_goal.Split(',').ToList());
                        targetUNGoalSet.AddRange(GetUnsGoalConnectedToTarget(unsGoalBaseData.Where(x => targetUNGoalIds.Contains(x.pk_goal_id)).ToList(), strategyId, item.fk_goal_id.ToString(), item.pk_target_id.ToString()));
                    }
                }

            }
        }

        private List<BudgetProposalStrategyDocHelper> GetUnsTargetConnectedToStrategy(List<gco_un_susdev_targets> unsTargetBaseData, string strategyId, string targetId, string goalId)
        {
            List<BudgetProposalStrategyDocHelper> unsTargetSet = new List<BudgetProposalStrategyDocHelper>();
            foreach (var item in unsTargetBaseData)
            {
                unsTargetSet.Add(new BudgetProposalStrategyDocHelper()
                {
                    StrategyName = string.Empty,
                    StrategyDesc = string.Empty,
                    StrategyId = strategyId,
                    GoalName = string.Empty,
                    GoalId = goalId,
                    TargetName = string.Empty,
                    FocusAreaName = string.Empty,
                    UNSGoalName = string.Empty,
                    UNSTargetName = item.target_short_name,
                    TargetId = targetId,
                    FocusAreaId = string.Empty,
                    UNSGoalId = string.Empty,
                    UNSTargetId = item.pk_target_id
                });
            }
            return unsTargetSet;
        }

        private void InsertBPStrategyTable(string userId, List<BudgetProposalStrategyDocHelper> result, IPublishHelper publishHelper, PublishTreeNode node, TableDefinition tableDef, string chapterId, bool targetUseUNGoals = false)
        {
            ColumnDetails cellTemplate;
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();
            UserData userDetails = pUtility.GetUserDetails(userId);

            Dictionary<string, clsLanguageString> languageStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "DocConfig");

            publishHelper.InsertHeading(userId, node.text, Context, true);

            publishHelper.StartTableOrientation(tableDef);
            publishHelper.StartTable("budpropstrategysa", null, null, tableDef);

            foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
            {
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Left;
                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                lstColumnDetails.Add(cellTemplate);

                lstColumnDataDetails.Add(cdf.ColumnName);
            }
            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
            //Insert data
            int count = 0;
            string notReleventText = languageStrings["BudProp_Strategy_not_relevent"].LangText;
            foreach (var item in result)
            {
                lstColumnDataDetails = new List<string>();
                lstColumnDetails.ForEach(x => x.ContentType = ((publishHelper.GetType() == typeof(WordHelper)) ? ContentType.Text : ContentType.Html));
                bool isParent = count == 0 || result[count].StrategyId != result[count - 1].StrategyId;
                string focusAreaName = isParent && string.IsNullOrEmpty(item.FocusAreaName) ? notReleventText : item.FocusAreaName;
                string goalName = isParent && string.IsNullOrEmpty(item.GoalName) ? notReleventText : item.GoalName;
                string unsGoalName = isParent && string.IsNullOrEmpty(item.UNSGoalName) ? notReleventText : item.UNSGoalName;
                string targetName = isParent && string.IsNullOrEmpty(item.TargetName) ? notReleventText : item.TargetName;


                lstColumnDataDetails.Add(focusAreaName);
                lstColumnDataDetails.Add(goalName);
                lstColumnDataDetails.Add(unsGoalName);
                lstColumnDataDetails.Add(targetName);
                if (targetUseUNGoals)
                {
                    string unGoalsForTarget = isParent && string.IsNullOrEmpty(item.TargetUNGoalName) ? notReleventText : item.TargetUNGoalName;
                    lstColumnDataDetails.Add(unGoalsForTarget);
                }
                else
                {
                    string unsTargetName = isParent && string.IsNullOrEmpty(item.UNSTargetName) ? notReleventText : item.UNSTargetName;
                    lstColumnDataDetails.Add(unsTargetName);
                }
                lstColumnDataDetails.Add(item.StrategyName);
                lstColumnDataDetails.Add(item.StrategyDesc);

                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content2);
                count++;
            }
            publishHelper.EndTable();
            publishHelper.EndTableOrientation();
        }

        private void InsertAssessmentSummeryTableColumns(UserData userDetails, List<string> columnFields, int budgetYear, IPublishHelper publishHelper)
        {
            Dictionary<string, clsLanguageString> languageStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "OpportunityAssessment");
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();

            foreach (string column in columnFields)
            {
                ColumnDetails cellTemplate;
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Fontsize = 10;
                cellTemplate.BottomBorder = 1;
                cellTemplate.showBottomBorder = true;
                cellTemplate.FontColor = FramsiktColors.TotalColor;
                cellTemplate.IsBold = true;
                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.PreferredWidth = 10;
                string columnName = string.Empty;

                switch (column)
                {
                    case "sumDescription":
                        cellTemplate.PreferredWidth = 20;
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        columnName = languageStrings["opp_assessments_sum_desc"].LangText;
                        break;

                    case "orgName":
                        cellTemplate.PreferredWidth = 40;
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        columnName = languageStrings["opp_assessments_org_name"].LangText;
                        break;

                    case "fpYear3":
                        columnName = languageStrings["opp_summary_col1_account"].LangText + " " + (budgetYear - 2).ToString();
                        break;

                    case "fpYear2":
                        columnName = languageStrings["opp_summary_col2_account"].LangText + " " + (budgetYear - 1).ToString();
                        break;

                    case "year1":
                        columnName = languageStrings["opp_summary_col_plan"].LangText + " " + (budgetYear).ToString();
                        break;

                    case "year2":
                        columnName = languageStrings["opp_summary_col_plan"].LangText + " " + (budgetYear + 1).ToString();
                        break;

                    case "year3":
                        columnName = languageStrings["opp_summary_col_plan"].LangText + " " + (budgetYear + 2).ToString();
                        break;

                    case "year4":
                        columnName = languageStrings["opp_summary_col_plan"].LangText + " " + (budgetYear + 3).ToString();
                        break;
                }
                lstColumnDataDetails.Add(columnName);
                lstColumnDetails.Add(cellTemplate);
            }
            publishHelper.CreateRow1(lstColumnDetails, lstColumnDataDetails, true, RowType.Header2);
        }

        private tsa_assessment_delegation GetAssessmentDescription(string user, Guid assessmentId, bool isConclusionDesc, OrgDetailHelper orgInput)
        {
            UserData userDetails = pUtility.GetUserDetails(user);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();

            string fpLevel2 = pUtility.GetParameterValue(user, "FINPLAN_LEVEL_2");
            bool isServiceIdSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")));
            tsa_assessment_delegation assessmentDelegationInfo;

            //For conclusion text -> on every delegated level should fetch the text from the conclusion text on owning level
            if (isConclusionDesc)
            {
                return tenantDbContext.tsa_assessment_delegation.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_assessment_id == assessmentId && x.is_owner.Value);
            }

            //For description
            if (isServiceIdSetup)
            {
                assessmentDelegationInfo = tenantDbContext.tsa_assessment_delegation.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_assessment_id == assessmentId && x.org_id == orgInput.orgId && x.service_id == orgInput.serviceId && x.org_level == orgInput.orgLevel);
            }
            else
            {
                assessmentDelegationInfo = tenantDbContext.tsa_assessment_delegation.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_assessment_id == assessmentId && x.org_id == orgInput.orgId && x.org_level == orgInput.orgLevel);
            }
            return assessmentDelegationInfo;
        }

        private bool InsertServiceAreaHeader(string userId, bool _insertServiceAreaHeader, string strategyServiceArea, IPublishHelper publishHelper, bool isBMDoc)
        {
            if (!isBMDoc && !_isStrategyServiceAreaHeaderInserted)
            {
                publishHelper.InsertHeading(userId, strategyServiceArea, Context);
                _isStrategyServiceAreaHeaderInserted = true;
            }
            else if (isBMDoc && _insertServiceAreaHeader)
            {
                publishHelper.InsertHeading(userId, strategyServiceArea, Context);
                _insertServiceAreaHeader = false;
            }
            return _insertServiceAreaHeader;
        }

        private void InsertDataForStrategyAssessment(string userId, int budgetYear, IPublishHelper publishHelper, List<AssessmentAreaActionDataGrid> data, bool isFromAssesmnentDoc)
        {
            TableDefinition tableDef;
            UserData userDetails = pUtility.GetUserDetails(userId);
            tableDef = isFromAssesmnentDoc ? _docTableConfig.GetTableDef(userId, "OppAssStrategyTable") : _docTableConfig.GetTableDef(userId, "StrategyAssessmentsTable");

            Dictionary<string, clsLanguageString> numberFormats = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            //amount format
            bool divideByMillions = tableDef.AmountFormat == AmountFormats.Millions;
            string numberTypeAmount = divideByMillions ? ((numberFormats.FirstOrDefault(v => v.Key == "dec1")).Value).LangText : ((numberFormats.FirstOrDefault(v => v.Key == "amount")).Value).LangText;

            ColumnDetails cellTemplate;

            publishHelper.StartTableOrientation(tableDef);

            publishHelper.StartTable(("strategyAssessmentAreaTable" + Guid.NewGuid()), null, null, tableDef);
            List<KeyValuesDescriptionHelper> actionDescriptionList = new List<KeyValuesDescriptionHelper>();
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();
            //Insert header row
            foreach (ColumnDefinition column in tableDef.ColumnDefinitions)
            {
                switch (column.ColumnId)
                {
                    case "year1":
                        lstColumnDataDetails.Add(budgetYear.ToString());
                        break;

                    case "year2":
                        lstColumnDataDetails.Add((budgetYear + 1).ToString());
                        break;

                    case "year3":
                        lstColumnDataDetails.Add((budgetYear + 2).ToString());
                        break;

                    case "year4":
                        lstColumnDataDetails.Add((budgetYear + 3).ToString());
                        break;

                    case "year5":
                        lstColumnDataDetails.Add((budgetYear + 4).ToString());
                        break;

                    case "year6":
                        lstColumnDataDetails.Add((budgetYear + 5).ToString());
                        break;

                    case "year7":
                        lstColumnDataDetails.Add((budgetYear + 6).ToString());
                        break;

                    case "year8":
                        lstColumnDataDetails.Add((budgetYear + 7).ToString());
                        break;

                    case "year9":
                        lstColumnDataDetails.Add((budgetYear + 8).ToString());
                        break;

                    case "year10":
                        lstColumnDataDetails.Add((budgetYear + 9).ToString());
                        break;

                    default:
                        lstColumnDataDetails.Add(column.ColumnName);
                        break;
                }
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                lstColumnDetails.Add(cellTemplate);
            }
            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

            //insert data
            foreach (var item in data)
            {
                lstColumnDataDetails = new List<string>();

                lstColumnDataDetails.Add(item.actionName);

                if (!string.IsNullOrEmpty(item.alterCodeDescription))
                {
                    lstColumnDataDetails.Add(item.alterCodeDescription);
                }
                else
                {
                    lstColumnDataDetails.Add(item.alterCode);
                }

                lstColumnDataDetails.Add(item.actionType);

                string[] dateTimePart = item.startDate.ToString().Split(' ');
                lstColumnDataDetails.Add(dateTimePart[0]);

                //Insert year amounts
                List<string> years = new List<string> { "year1", "year2", "year3", "year4", "year5", "year6", "year7", "year8", "year9", "year10" };

                foreach (var year in years)
                {
                    decimal yearAmount = 0;
                    switch (year)
                    {
                        case "year1":
                            yearAmount = Math.Round(item.year1);
                            break;

                        case "year2":
                            yearAmount = Math.Round(item.year2);
                            break;

                        case "year3":
                            yearAmount = Math.Round(item.year3);
                            break;

                        case "year4":
                            yearAmount = Math.Round(item.year4);
                            break;

                        case "year5":
                            yearAmount = Math.Round(item.year5);
                            break;

                        case "year6":
                            yearAmount = Math.Round(item.year6);
                            break;

                        case "year7":
                            yearAmount = Math.Round(item.year7);
                            break;

                        case "year8":
                            yearAmount = Math.Round(item.year8);
                            break;

                        case "year9":
                            yearAmount = Math.Round(item.year9);
                            break;

                        case "year10":
                            yearAmount = Math.Round(item.year10);
                            break;
                    }
                    if (publishHelper.GetType() == typeof(WebHelperBm))
                    {
                        lstColumnDataDetails.Add(yearAmount == 0 ? string.Empty : yearAmount.ToString(numberTypeAmount));
                    }
                    else
                    {
                        lstColumnDataDetails.Add(yearAmount.ToString(numberTypeAmount));
                    }
                }
                //Create a row
                if (item.isSumRow)
                {
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.SubTotal1);
                }
                else
                {
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.ContentDynamic1);

                    KeyValuesDescriptionHelper actionDescriptionInfo = new KeyValuesDescriptionHelper();
                    actionDescriptionInfo.Value = item.actionName;
                    actionDescriptionInfo.LongDesc = item.externalDescription;
                    actionDescriptionInfo.ShortDesc = item.internalDescription;
                    actionDescriptionList.Add(actionDescriptionInfo);
                }
            }
            publishHelper.EndTable();
            publishHelper.EndTableOrientation();

            //Insert descriptions for actions
            int actionCount = 0;
            foreach (var ad in actionDescriptionList.GroupBy(x => new { x.Value, x.LongDesc, x.ShortDesc }).ToList())
            {
                if (!string.IsNullOrEmpty(ad.Key.LongDesc) || !string.IsNullOrEmpty(ad.Key.ShortDesc))
                {
                    if (actionCount != 0 && publishHelper.GetType() == typeof(WebHelperBm))
                    {
                        publishHelper.InsertLineBreak();
                    }
                    publishHelper.InsertText(ad.Key.Value, 11, true, false);

                    if (publishHelper.GetType() == typeof(WebHelperBm))
                    {
                        if (!string.IsNullOrEmpty(ad.Key.LongDesc))
                        {
                            publishHelper.InsertDescription(ad.Key.LongDesc, userId);
                        }

                        if (!string.IsNullOrEmpty(ad.Key.LongDesc) && !string.IsNullOrEmpty(ad.Key.ShortDesc))
                        {
                            publishHelper.InsertLineBreak();
                        }

                        if (!string.IsNullOrEmpty(ad.Key.ShortDesc))
                        {
                            publishHelper.InsertDescription(ad.Key.ShortDesc, userId);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(ad.Key.LongDesc))
                        {
                            publishHelper.InsertText(ad.Key.LongDesc, 10, false, false);
                        }

                        if (!string.IsNullOrEmpty(ad.Key.ShortDesc))
                        {
                            publishHelper.InsertText(ad.Key.ShortDesc, 10, false, false);
                        }
                    }
                    actionCount++;
                }
            }
            publishHelper.InsertLineBreak();
        }

        private string GetSummeryGridColumnValue(decimal amount, bool isDocExport, string amountType)
        {
            decimal yearAmount = Math.Round(amount);

            if (!isDocExport && yearAmount == 0)
            {
                return string.Empty;
            }
            return amount.ToString(amountType);
        }

        private void InsertAssessmentSummeryActionsDataRows(List<string> columnFields, int itemCount, AssessmentSummeryGridDataHelper rowItem, IPublishHelper publishHelper, Dictionary<string, clsLanguageString> numberFormats)
        {
            //insert data
            bool isDocExport = publishHelper.GetType() != typeof(WebHelperBm);
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();
            //amount format
            string numberTypeAmount = numberFormats["amount"].LangText;
            string decimalAmountType = numberFormats["dec1"].LangText;

            foreach (var column in columnFields)
            {
                ColumnDetails cellTemplate;
                cellTemplate = publishHelper.GetTemplate(CellTypes.DetailData);
                cellTemplate.Fontsize = 8;
                if (itemCount == 0)
                {
                    cellTemplate.TopBorder = 1;
                }
                cellTemplate.BottomBorder = 1;
                cellTemplate.WrapText = true;
                cellTemplate.IsBold = false;
                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                cellTemplate.PreferredWidth = 10;
                cellTemplate.Alignment = ParagraphAlignment.Right;
                if (rowItem.isSumRow)
                {
                    cellTemplate.IsBold = true;
                }
                string columnValue = string.Empty;
                switch (column)
                {
                    case "orgName":
                        cellTemplate.PreferredWidth = 40;
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        lstColumnDataDetails.Add(rowItem.orgName);
                        break;

                    case "sumDescription":
                        cellTemplate.PreferredWidth = 20;
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        lstColumnDataDetails.Add(rowItem.sumDescription);
                        break;

                    case "fpYear3":
                        columnValue = rowItem.isRevenueSumRow ? rowItem.fpYear3Amount.ToString(decimalAmountType) + "%" : GetSummeryGridColumnValue(rowItem.fpYear3Amount, isDocExport, numberTypeAmount);
                        lstColumnDataDetails.Add(columnValue);
                        break;

                    case "fpYear2":
                        columnValue = rowItem.isRevenueSumRow ? rowItem.fpYear2Amount.ToString(decimalAmountType) + "%" : GetSummeryGridColumnValue(rowItem.fpYear2Amount, isDocExport, numberTypeAmount);
                        lstColumnDataDetails.Add(columnValue);
                        break;

                    case "year1":
                        columnValue = rowItem.isRevenueSumRow ? rowItem.year1Amount.ToString(decimalAmountType) + "%" : GetSummeryGridColumnValue(rowItem.year1Amount, isDocExport, numberTypeAmount);
                        lstColumnDataDetails.Add(columnValue);
                        break;

                    case "year2":
                        columnValue = rowItem.isRevenueSumRow ? rowItem.year2Amount.ToString(decimalAmountType) + "%" : GetSummeryGridColumnValue(rowItem.year2Amount, isDocExport, numberTypeAmount);
                        lstColumnDataDetails.Add(columnValue);
                        break;

                    case "year3":
                        columnValue = rowItem.isRevenueSumRow ? rowItem.year3Amount.ToString(decimalAmountType) + "%" : GetSummeryGridColumnValue(rowItem.year3Amount, isDocExport, numberTypeAmount);
                        lstColumnDataDetails.Add(columnValue);
                        break;

                    case "year4":
                        columnValue = rowItem.isRevenueSumRow ? rowItem.year4Amount.ToString(decimalAmountType) + "%" : GetSummeryGridColumnValue(rowItem.year4Amount, isDocExport, numberTypeAmount);
                        lstColumnDataDetails.Add(columnValue);
                        break;
                }
                lstColumnDetails.Add(cellTemplate);
            }
            if (rowItem.isSumRow)
            {
                publishHelper.CreateRow1(lstColumnDetails, lstColumnDataDetails, false, RowType.SubTotal);
            }
            else
            {
                publishHelper.CreateRow1(lstColumnDetails, lstColumnDataDetails, false, RowType.Detail);
            }
        }

        private void InsertBPGuideLineAssessmentDescription(string user, IPublishHelper publishHelper, PublishTreeNode descNode)
        {
            UserData userDetails = pUtility.GetUserDetails(user);
            TenantDBContext tenantDbContext = pUtility.GetTenantDBContext();
            string[] descNodeInfo = descNode.id.Split('_').ToArray();
            Guid assessmentId = Guid.Parse(descNodeInfo[1]);

            tsa_assessments tsa_AssessmentsInfo = tenantDbContext.tsa_assessments.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_assessment_id == assessmentId);
            if (tsa_AssessmentsInfo != null && !string.IsNullOrEmpty(tsa_AssessmentsInfo.backround_objective))
            {
                publishHelper.InsertHeading(user, descNode.text, Context);
                publishHelper.InsertDescriptionCk5(tsa_AssessmentsInfo.backround_objective, user);
            }
        }

        private static void UpdateClimateDescriptionObject(ref List<PlanObjectsDescriptionHelper> descriptionHelper, JToken climateAction, string name)
        {
            if (!string.IsNullOrEmpty(name))
            {
                string externalDescription = climateAction.SelectToken("description").ToString();
                string financeDescription = climateAction.SelectToken("financeDescription").ToString();
                descriptionHelper.Add(new PlanObjectsDescriptionHelper
                {
                    Title = name,
                    Description = externalDescription.Trim(),
                    ShowDescription = !string.IsNullOrEmpty(externalDescription.Trim()),
                    UniqueId = climateAction.SelectToken("climateActionId").ToString(),
                    Description1 = financeDescription.Trim()
                });
            }
        }

        private void PublishClimateActionNodepupupData(string userId, IPublishHelper publishHelper, List<PlanObjectsDescriptionHelper> descriptionHelpers, int budgetYear,
                                                        string currentChapPath, List<ClimateActionInvestmentHelper> fpActionOrInvestmentInfo, Guid categoryId, List<string> columns)
        {
            var userDetails = pUtility.GetUserDetails(userId);
            TenantDBContext tenantdbContext = pUtility.GetTenantDBContext();
            Dictionary<string, clsLanguageString> langStrings = pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "ClimateAction");
            List<string> finCols = new List<string>() { "financeName", "financeType", "Year1", "Year2", "Year3", "Year4", "Year5", "Year6", "Year7", "Year8", "Year9", "Year10", "FourYearSum", "TenYearSum" };
            List<string> climateFinanceColumns = GetClimateFinanceColumns(userId, budgetYear, finCols);
            climateFinanceColumns.Remove("FourYearSum");
            climateFinanceColumns.Remove("TenYearSum");
            climateFinanceColumns.Add("sumYear4");
            climateFinanceColumns.Add("sumYear10");
            var orgDetails = JsonConvert.DeserializeObject<JObject>(userDetails.defaultOrg);
            var finInput = new ClimateFinanceInputHelper()
            {
                orgLevel = int.Parse((string)orgDetails["level"]),
                orgId = orgDetails["orgId"].ToString(),
                serviceId = "ALL",
                serviceLevel = 0,
                selectedColumns = new ColumnSelector(),
                isPublish = true
            };
            List<string> emissionGridHeader = GetEmissionGridHeader(userId, columns, budgetYear, langStrings);
            foreach (var item in descriptionHelpers)
            {
                int climateActionId = int.Parse(item.UniqueId);

                if (!string.IsNullOrEmpty(item.Description))
                {
                    item.Description = FormatDescription(item.Description, publishHelper);
                }
                if (!string.IsNullOrEmpty(item.Description1))
                {
                    item.Description1 = FormatDescription(item.Description1, publishHelper);
                }
                item.StratYear = budgetYear;
                item.EndYear = budgetYear + 3;

                var climateFinanceData = _climateActions.GetClimateFinanceDetails(userId, climateActionId, budgetYear, finInput).GetAwaiter().GetResult();
                var cliamateDetailData = _climateActions.GetClimateActionDetailData(userDetails, tenantdbContext, budgetYear, climateActionId).GetAwaiter().GetResult();

                var climateData = GetPlanClimateActionPopupPublishData(userId, climateFinanceData, langStrings, item, budgetYear, emissionGridHeader, cliamateDetailData, columns, climateFinanceColumns);
                climateData.StaticColumnCount1 = GetEmissionGridTextColumnCount(columns, emissionGridHeader);
                publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(climateData), currentChapPath, item.UniqueId, PubContentHandler.BlobType.Json);
            }
        }

        private int GetEmissionGridTextColumnCount(List<string> userDefinedColumns, List<string> emissionGridHeader)
        {
            List<string> amountColumns = new List<string>() { "reductionPrevYear2", "reductionPrevYear1", "reductionYear1", "reductionYear2", "reductionYear3", "reductionYear4",
                                                            "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8","reductionYear9","reductionYear10", "longTermReduction" };
            int amountColumnsCount = userDefinedColumns.Count(x => amountColumns.Contains(x));
            return emissionGridHeader.Count - amountColumnsCount;
        }

        private List<string> GetEmissionGridHeader(string userId, List<string> userDefinedColumns, int budgetYear, Dictionary<string, clsLanguageString> langStrings)
        {
            List<string> headerData = new List<string>();

            foreach (var column in userDefinedColumns)
            {
                switch (column)
                {
                    case "sector":
                        headerData.Add(langStrings["FP_sector"].LangText);
                        break;

                    case "source":
                        headerData.Add(langStrings["FP_source"].LangText);
                        break;

                    case "reductionQuantityName":
                        headerData.Add(langStrings["CA_Reduction_Quantity"].LangText);
                        break;

                    case "reductionPrevYear1":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear - 1));
                        break;

                    case "reductionPrevYear2":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear - 2));
                        break;

                    case "reductionYear1":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear));
                        break;

                    case "reductionYear2":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 1));
                        break;

                    case "reductionYear3":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 2));
                        break;

                    case "reductionYear4":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 3));
                        break;

                    case "reductionYear5":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 4));
                        break;

                    case "reductionYear6":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 5));
                        break;

                    case "reductionYear7":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 6));
                        break;

                    case "reductionYear8":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 7));
                        break;

                    case "reductionYear9":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 8));
                        break;

                    case "reductionYear10":
                        headerData.Add(langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 9));
                        break;

                    case "longTermReduction":
                        headerData.Add(langStrings["FP_longTermReduction"].LangText);
                        break;

                    default: break;
                }
            }
            return headerData;
        }

        private string GetClimateActionName(IPublishHelper publishHelper, string currentChapPath, int climateId, string name)
        {
            if (publishHelper.GetType() == typeof(WordHelper))
            {
                return name;
            }
            if (string.IsNullOrEmpty(name)) return string.Empty;

            string popupText = "(Sprettoppvindu) ";

            return "<a class='web-clickable-links' href='javascript:void(0)' title = \"" + popupText + climateId.ToString() + "\" onkeypress = 'openBudgetSAPopup(\"" + currentChapPath + "\",\"finplanClimateAction\")' onClick = 'openBudgetSAPopup(\"" + currentChapPath + "\",\"finplanClimateAction\")'>" + name + "<span class='sr-only'>Lenke åpnes i Sprettoppvindu</span></a>";
        }

        private string FormatDescription(string description, IPublishHelper publishHelper)
        {
            WebHelperBm wh = (WebHelperBm)publishHelper;
            string processedHtml = wh.TransferImagesInHtml(description);
            processedHtml = wh.ProcessTableCkEditor5(processedHtml);
            description = processedHtml.Replace("</p>", "</p></br>");
            return description;
        }

        private ClimateFinancePublishHelper GetPlanClimateActionPopupPublishData(string userId, climateActionFinanceData climateFinanceData,
                                                                  Dictionary<string, clsLanguageString> langStrings,
                                                                  PlanObjectsDescriptionHelper descInfo,
                                                                  int budgetYear,
                                                                  List<string> emissionGridHeader,
                                                                  List<ClimateActionFilterHelper> climateDetailData,
                                                                  List<string> emissiongGridColumns,
                                                                  List<string> climateFinanceColumns)
        {
            var popupData = new ClimateFinancePublishHelper
            {
                ActionName = descInfo.Title,
                StartYear = descInfo.StratYear,
                EndYear = descInfo.EndYear,
                GridHeader = GetFormattedFinanceColumns(climateFinanceColumns,budgetYear, langStrings),
                FinanceData = GetFormattedFinanceData(climateFinanceData.climateFinanceData),
                Description = descInfo.Description,
                FinanceGridTitle = langStrings["ca_finance_grid_title"].LangText,
                DescHeading1 = langStrings["ClimateAction_FP_description"].LangText,
                DescHeading2 = langStrings["FP_financeDescription"].LangText,
                EmissionGridTitle = langStrings["CA_web_Emission_grid_header"].LangText,
                Description1 = descInfo.Description1,
                EconomyRecords = GetFormattedEmissionGridData(climateDetailData, langStrings, emissiongGridColumns),
                GridHeader1 = emissionGridHeader,
                StaticColumnCount = 1,
                StaticColumnCount1 = 5
            };
            popupData.FinanceGridTitle = climateFinanceData.climateFinanceData.Count == 1? "": popupData.FinanceGridTitle;
            popupData.FinanceData = climateFinanceData.climateFinanceData.Count == 1 ? new List<climateActionFinanceDataHelper>(): climateFinanceData.climateFinanceData;
            return popupData;
        }

        private static List<climateActionFinanceDataHelper> GetFormattedFinanceData(List<climateActionFinanceDataHelper> financeData)
        {
            if (financeData == null)
            {
                return new List<climateActionFinanceDataHelper>();
            }
            else
            {
                return financeData.Select(x => new climateActionFinanceDataHelper
                {
                    parentId = x.parentId,
                    uniqId = x.uniqId,
                    financeType = x.parentId != null || x.uniqId == "SumRow" ? "-" : x.financeType,
                    financeName = x.financeName,
                    Year1 = x.Year1,
                    Year2 = x.Year2,
                    Year3 = x.Year3,
                    Year4 = x.Year4,
                    Year5 = x.Year5,
                    Year6 = x.Year6,
                    Year7 = x.Year7,
                    Year8 = x.Year8,
                    Year9 = x.Year9,
                    Year10 = x.Year10,
                    sumYear4 = x.sumYear4,
                    sumYear10 = x.sumYear10
                }).ToList();
            }
        }

        private static List<KeyValuePairString> GetFormattedFinanceColumns(List<string> climateFinanceColumns, int budgetYear, Dictionary<string, clsLanguageString> climateActionLangStrings)
        {
            var financeColumns = new List<KeyValuePairString>();
            foreach (string col in climateFinanceColumns)
            {
                var column = new KeyValuePairString()
                {
                    Key = col,
                    Value = string.Empty
                };

                switch (col)
                {
                    case "financeName":
                        column.Value = climateActionLangStrings["ca_finance_name"].LangText;
                        break;

                    case "financeType":
                        column.Value = climateActionLangStrings["ca_finance_type"].LangText;
                        break;

                    case "Year1":
                        column.Value = budgetYear.ToString();
                        break;

                    case "Year2":
                        column.Value = (budgetYear + 1).ToString();
                        break;

                    case "Year3":
                        column.Value = (budgetYear + 2).ToString();
                        break;

                    case "Year4":
                        column.Value = (budgetYear + 3).ToString();
                        break;

                    case "Year5":
                        column.Value = (budgetYear + 4).ToString();
                        break;

                    case "Year6":
                        column.Value = (budgetYear + 5).ToString();
                        break;

                    case "Year7":
                        column.Value = (budgetYear + 6).ToString();
                        break;

                    case "Year8":
                        column.Value = (budgetYear + 7).ToString();
                        break;

                    case "Year9":
                        column.Value = (budgetYear + 8).ToString();
                        break;

                    case "Year10":
                        column.Value = (budgetYear + 9).ToString();
                        break;

                    case "sumYear4":
                        column.Value = climateActionLangStrings["ca_sum_four_year"].LangText;
                        break;

                    case "sumYear10":
                        column.Value = climateActionLangStrings["ca_sum_ten_year"].LangText;
                        break;
                };

                financeColumns.Add(column);

            }
            return financeColumns;
        }
        private string GetActionConnectionName(TcoClimateActionHeader data, string connectionName, Dictionary<string, clsLanguageString> langStrings)
        {
            if (!string.IsNullOrEmpty(connectionName))
            {
                string connectionHeader = langStrings["CA_web_action_connection_header"].LangText;
                if (!string.IsNullOrEmpty(data.fk_main_project_code))
                {
                    connectionHeader = langStrings["CA_web_INV_connection_header"].LangText;
                }

                return "<h4>" + connectionHeader + "</h4>" + connectionName;
            }

            return string.Empty;
        }

        private List<List<string>> GetFormattedEmissionGridData(List<ClimateActionFilterHelper> climateDetailData, Dictionary<string, clsLanguageString> langStrings,
                                                                List<string> emissiongGridColumns)
        {
            //For a total row
            List<List<string>> finalSet = new List<List<string>>();
            bool includeSource = emissiongGridColumns.Contains("source");
            bool includeSector = emissiongGridColumns.Contains("sector");
            bool includeReductionQuantity = emissiongGridColumns.Contains("reductionQuantityName");

            var totalData = new ClimateActionFilterHelper()
            {
                sector = string.Empty,
                source = string.Empty,
                reductionQuantityName = string.Empty,
                reductionPrevYear1Value = climateDetailData.Sum(x => x.reductionPrevYear1Value * x.co2Factor),
                reductionPrevYear2Value = climateDetailData.Sum(x => x.reductionPrevYear2Value * x.co2Factor),
                reductionYear1 = climateDetailData.Sum(x => x.reductionYear1 * x.co2Factor),
                reductionYear2 = climateDetailData.Sum(x => x.reductionYear2 * x.co2Factor),
                reductionYear3 = climateDetailData.Sum(x => x.reductionYear3 * x.co2Factor),
                reductionYear4 = climateDetailData.Sum(x => x.reductionYear4 * x.co2Factor),
                reductionYear5 = climateDetailData.Sum(x => x.reductionYear5 * x.co2Factor),
                reductionYear6 = climateDetailData.Sum(x => x.reductionYear6 * x.co2Factor),
                reductionYear7 = climateDetailData.Sum(x => x.reductionYear7 * x.co2Factor),
                reductionYear8 = climateDetailData.Sum(x => x.reductionYear8 * x.co2Factor),
                reductionYear9 = climateDetailData.Sum(x => x.reductionYear9 * x.co2Factor),
                reductionYear10 = climateDetailData.Sum(x => x.reductionYear10 * x.co2Factor),
                longTermReduction = climateDetailData.Sum(x => x.longTermReduction * x.co2Factor)
            };

            climateDetailData = (from a in climateDetailData
                                 group new { a } by new
                                 {
                                     sector = includeSector ? a.sector : string.Empty,
                                     source = includeSource ? a.source : string.Empty,
                                     reductionQuantity = includeReductionQuantity ? a.reductionQuantityName : string.Empty,
                                     sectorId = includeSector ? a.sectorId : 0,
                                     sourceId = includeSource ? a.sourceId : 0,
                                     redictionQuantityId = includeReductionQuantity ? a.reductionQuantity : 0
                                 } into g
                                 select new ClimateActionFilterHelper()
                                 {
                                     sector = g.Key.sector,
                                     source = g.Key.source,
                                     reductionQuantityName = g.Key.reductionQuantity,
                                     reductionPrevYear1Value = g.Sum(x => x.a.reductionPrevYear1Value),
                                     reductionPrevYear2Value = g.Sum(x => x.a.reductionPrevYear2Value),
                                     reductionYear1 = g.Sum(x => x.a.reductionYear1),
                                     reductionYear2 = g.Sum(x => x.a.reductionYear2),
                                     reductionYear3 = g.Sum(x => x.a.reductionYear3),
                                     reductionYear4 = g.Sum(x => x.a.reductionYear4),
                                     reductionYear5 = g.Sum(x => x.a.reductionYear5),
                                     reductionYear6 = g.Sum(x => x.a.reductionYear6),
                                     reductionYear7 = g.Sum(x => x.a.reductionYear7),
                                     reductionYear8 = g.Sum(x => x.a.reductionYear8),
                                     reductionYear9 = g.Sum(x => x.a.reductionYear9),
                                     reductionYear10 = g.Sum(x => x.a.reductionYear10),
                                     longTermReduction = g.Sum(x => x.a.longTermReduction)
                                 }).ToList();

            climateDetailData.Add(totalData);

            int itemCount = 1;
            foreach (var item in climateDetailData)
            {
                bool isTotalRow = (climateDetailData.Count) == itemCount;
                List<string> row = new List<string>();

                foreach (var optionalColumn in emissiongGridColumns)
                {
                    switch (optionalColumn)
                    {
                        case "source":
                            row.Add(isTotalRow && !includeSector ? "<span class='bold'>" + langStrings["total_co2equivalent"].LangText + "</span>" : item.source);
                            break;

                        case "sector":
                            row.Add(isTotalRow ? "<span class='bold'>" + langStrings["total_co2equivalent"].LangText + "</span>" : item.sector);
                            break;

                        case "reductionQuantityName":
                            row.Add(isTotalRow && !includeSector && !includeSource ? "<span class='bold'>" + langStrings["total_co2equivalent"].LangText + "</span>" : item.reductionQuantityName);
                            break;

                        case "reductionPrevYear1":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionPrevYear1Value.ToString("n1"), true));
                            break;

                        case "reductionPrevYear2":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionPrevYear2Value.ToString("n1"), true));
                            break;

                        case "reductionYear1":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear1.ToString("n1"), true));
                            break;

                        case "reductionYear2":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear2.ToString("n1"), true));
                            break;

                        case "reductionYear3":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear3.ToString("n1"), true));
                            break;

                        case "reductionYear4":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear4.ToString("n1"), true));
                            break;

                        case "reductionYear5":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear5.ToString("n1"), true));
                            break;

                        case "reductionYear6":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear6.ToString("n1"), true));
                            break;

                        case "reductionYear7":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear7.ToString("n1"), true));
                            break;

                        case "reductionYear8":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear8.ToString("n1"), true));
                            break;

                        case "reductionYear9":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear9.ToString("n1"), true));
                            break;

                        case "reductionYear10":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.reductionYear10.ToString("n1"), true));
                            break;

                        case "longTermReduction":
                            row.Add(GetClimatePopupGridRowText(isTotalRow, item.longTermReduction.ToString("n1"), true));
                            break;

                        default: break;
                    }
                }

                finalSet.Add(row);
                itemCount++;
            }
            return finalSet;
        }

        private string GetClimatePopupGridRowText(bool isTotalRow, string item, bool isAmountRow)
        {
            if (isTotalRow)
            {
                if (isAmountRow)
                {
                    return "<span class='bold'>" + item + "</span>";
                }
                else
                {
                    return string.Empty;
                }
            }
            return item;
        }

        private List<string> GetClimateFinanceColumns(string userId, int budgetYear, List<string> columns)
        {
            var colselObj = _climateActions.GetClimateFinanceColumnsAsync(userId, columns, budgetYear).GetAwaiter().GetResult();
            var selectedColumns = new List<string>();
            if (colselObj != null)
            {
                selectedColumns = colselObj.ColumnSelectorSection
                   .SelectMany((ColumnSelectorSection section) => section.columns)
                   .Where((ColumnSelectorColumn column) => column.isChecked)
                   .Select((ColumnSelectorColumn column) => column.key.ToString())
                   .ToList();
                return selectedColumns;
            }
            return columns;
        }
        #endregion Private Methods
    }
}