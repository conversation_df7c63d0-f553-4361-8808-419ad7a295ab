using Autofac.Extensions.DependencyInjection;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Core;
using Framsikt.BL.Repository;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker.Core.ServiceBusQueueMessageHandlers
{
    public class PlanDiscAssignmentHandler : IQueueHandler
    {
        private readonly IServiceProvider container;

        public string QueueName { get; set; }

        public PlanDiscAssignmentHandler(IServiceProvider serviceProvider, string queueName)
        {
            QueueName = queueName;
            container = serviceProvider;
        }

        public async Task ProcessQueueMessage(string message, string sessionId, AutofacServiceProvider container)
        {
            IAzureBlobHelper _azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
            IPlanDiscRingsImport _planDiscRingsImport = container.GetRequiredService<IPlanDiscRingsImport>();
            IUtility _utility = container.GetRequiredService<IUtility>();
            INotification _notification = container.GetRequiredService<INotification>();

            dynamic jsonReq = JToken.Parse(message);
            int tenantId = jsonReq.tenantId;
            int clientId = jsonReq.clientId;
            string userId = jsonReq.userId;
            string planDiscId = jsonReq.planDiscId;
            string domainUrl = jsonReq.domainUrl ?? string.Empty;
            long jobId = jsonReq.jobId;
            AssignmentSectionHelper input = new();
            input.selectedYear = jsonReq.budgetYear;
            input.startDate = (PDdateTypesDD)Enum.Parse(typeof(PDdateTypesDD), jsonReq.startDate.ToString());
            input.endDate = (PDdateTypesDD)Enum.Parse(typeof(PDdateTypesDD), jsonReq.endDate.ToString());
            input.planDiscId = jsonReq.planDiscSetupId;
            await _planDiscRingsImport.UpdateAssignmentsToPlanDiscAsync(userId, planDiscId, domainUrl, input, jobId);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "ng_plandiscadmin");
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            Guid notId = Guid.NewGuid();
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["PD_Meetings_Job_Success"].LangText, redirectUrl, 24);
        }

        public void OnError(Exception e)
        {
            // Log error here
        }
    }
}