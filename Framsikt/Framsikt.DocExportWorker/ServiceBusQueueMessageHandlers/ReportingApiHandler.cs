using Autofac.Extensions.DependencyInjection;
using Framsikt.BL.Core.Repository.Contracts;
using Framsikt.BL.Repository;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Framsikt.DocExportWorker.Core.ServiceBusQueueMessageHandlers
{
    public class ReportingApiHandler : IQueueHandler
    {
        private readonly IServiceProvider container;

        public string QueueName { get; set; }

        public ReportingApiHandler(IServiceProvider serviceProvider, string queueName)
        {
            QueueName = queueName;
            container = serviceProvider;
        }

        public async Task ProcessQueueMessage(string message, string sessionId, AutofacServiceProvider container)
        {
            ReporingAPIEntry _processor = new ReporingAPIEntry();
            messageInput? input = JsonConvert.DeserializeObject<messageInput>(message);
            if (input != null)
            {
                var report = await _processor.GenerateReportingData(input.templateId, input.tenantId, input.budgetYear, input.clientId);
                IServiceBusProvider serviceBusProvider = container.GetRequiredService<IServiceBusProvider>();
                await serviceBusProvider.SendMessageWithSessionToQueueAsync(report, sessionId, ServiceBusQueues.reporting_response.ToString());
            }
        }

        public void OnError(Exception e)
        {
            throw new Exception(e.Message);
        }
    }
}