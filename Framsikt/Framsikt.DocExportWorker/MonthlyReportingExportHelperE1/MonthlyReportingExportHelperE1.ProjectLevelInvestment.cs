using Aspose.Words;
using Aspose.Words.Tables;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Drawing;
using System.Reflection;
using System.Text.RegularExpressions;
using static Framsikt.BL.Helpers.clsConstants;

namespace Framsikt.DocExportWorker
{
    public partial class MonthlyReportingExportHelper
    {

        private void InsertMRInvestmentProjectData(string user, DocumentBuilder builder, int forecastPeriod, PublishTreeNode node)
        {
                InsertMRInvestmentProjectLevelData(user, builder, Convert.ToInt32(node.parameters["ProjStructureNodeLevel"]), node.id, Convert.ToInt32(node.parameters["ProjectLevel"]), (forecastPeriod / 100), forecastPeriod, node.text, node.parentId, node.parameters["ParentId"], node.parameters["ProjectLevelId"], node.items).GetAwaiter().GetResult();


        }



        private async Task InsertMRInvestmentProjectLevelData(string user, DocumentBuilder builder, int level, string lvlId, int projectLevel, int budgetYear, int forecastPeriod,string nodeName, string parentId, string parentLevelId, string projectLevelId, List<PublishTreeNode> nodeItems)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            try
            {
                var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
                
                Initialize(user);
                var data = nodeItems.Any() ? nodeItems.FirstOrDefault(x => x.id == lvlId && Convert.ToInt32(x.parameters["ProjStructureNodeLevel"]) == level) : null;
                if (parentId == DocExportTreeMonthlyReportingConstants.MRInvestmentProjectLevelCtrl.ToString())
                {
                    if (nodeItems.Any() && data != null)
                    {
                        return;
                    }
                    publishHelper.InsertHeading(user, nodeName, _context, true);
                    return;
                }
                UserData userDetails = _utility.GetUserDetails(user);
                var isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                string showInvestmentWithZero = _utility.GetParameterValue(user, "MR_SHOW_INV_ZERO");
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                TableDefinition tabledef = null;
                if (isYearlySetupEnabled)
                {
                    tabledef = _docTableConfig.GetTableDef(user, "MrYearlyInvestmentProject");
                }
                else
                {
                    tabledef = _docTableConfig.GetTableDef(user, "MrInvestmentProject");
                }
                bool divideByMillions = _utility.CheckOnDivideByMillions(user, "Million_MonthlyReport_EcoStatusGrid");
                int divideBy = divideByMillions ? 1000000 : 1;
                List<MonthlyReportInvestmentHelperNew> resultSet1 = await _monthlyReportInvestment.GetInvestmentDataforDynamicProjectSetup(user, forecastPeriod, level, lvlId, parentLevelId, projectLevelId);
                resultSet1 = resultSet1.Where(x => !x.allZeroRow).ToList();
                if (resultSet1.Count == 0)
                    return;

                var monthlyReportDBContext = _utility.GetTenantDBContext();
                //drop the inv having accounting and budget both equals to 0 in yearly setup
                if (showInvestmentWithZero.ToLower() != "true")
                {
                    resultSet1.RemoveAll(x => x.Accounting == 0 && x.AdjustedBudget == 0);
                }
               
                var groupLevels = _docTableConfig.GetInvTableGrp(user, "MRProgGrpInvLevel" + level); 
               var resultSet = await _monthlyReportInvestment.FormatInvestmentDataforDynamicProjectSetupLevel1(user, resultSet1, level, lvlId, divideByMillions, tabledef, forecastPeriod, groupLevels);

                var gr1Ids = resultSet.Select(x => x.level1).Distinct().ToList();
                if (groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupName != string.Empty) == null)
                {
                    publishHelper.InsertError("Setup is Inproper");
                    return;
                }
                if ((groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "2" && x.groupName != string.Empty) == null) && (groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupId == "main_project") == null))
                {
                    string tableGroupId = groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1").groupId;
                    string groupId = tableGroupId.Split("_")[2];
                    resultSet = await FormatInvData(userDetails.tenant_id, forecastPeriod, resultSet, groupId);
                }
                builder.InsertBreak(BreakType.SectionBreakNewPage);
                publishHelper.InsertHeading(user, nodeName, _context, true);
                
                List<MonthlyReportProjectLevelHelperNew> Level2Data = new List<MonthlyReportProjectLevelHelperNew>();

                if(groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupId == "main_project") != null)// when level one is main proj
                {
                    var level1data = resultSet.ToList();
                    publishHelper.StartTableOrientation(tabledef);
                    publishHelper.StartTable("InvestmentProjLevelGridLevel_"+ level + "_"+lvlId, "main_project", null, tabledef);
                    level1data.Add(InsertMRProjectLevelTotalRowLine(userDetails, level1data));
                    InsertMRInvestmentTableHeader(tabledef, publishHelper, langStringValuesMonthlyReport, builder);
                    InsertMRProjectLevelTableData(user, publishHelper, tabledef, level1data, 1, groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupId == "main_project") != null, budgetYear, forecastPeriod);
                    publishHelper.EndTable();
                    publishHelper.EndTableOrientation();

                } else if (groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupId == "main_project") == null && groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "2" && x.groupName != string.Empty) == null)
                {
                    var level1data = resultSet.ToList();
                    publishHelper.StartTableOrientation(tabledef);
                    publishHelper.StartTable("InvestmentProjLevelGridLevel_" + level.ToString() + "_" + lvlId, level.ToString(), null, tabledef);
                    level1data.Add(InsertMRProjectLevelTotalRowLine(userDetails, level1data));
                    InsertMRInvestmentTableHeader(tabledef, publishHelper, langStringValuesMonthlyReport, builder);
                    InsertMRProjectLevelTableData(user, publishHelper, tabledef, level1data, 1, groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupId == "main_project") != null, budgetYear, forecastPeriod);
                    publishHelper.EndTable();
                    publishHelper.EndTableOrientation();
                }
                else
                {
                    foreach (var level1 in gr1Ids)
                    {
                        var level1data = resultSet.Where(x => x.level1 == level1).ToList();
                        if (level1data.Count == 0)
                            continue;
                        // publishHelper.InsertHeading(user, level1data.FirstOrDefault().level1Name, _context, HeaderSizeOptions.OneLevelLower);

                        if (groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "2" && x.groupName != string.Empty) == null)// when only level one is set
                        {
                            publishHelper.StartTableOrientation(tabledef);
                            publishHelper.StartTable("InvestmentProjLevelGridLevel_" + level.ToString() + "_" + lvlId, level1, null, tabledef);
                            level1data.Add(InsertMRProjectLevelTotalRowLine(userDetails, level1data));
                            InsertMRInvestmentTableHeader(tabledef, publishHelper, langStringValuesMonthlyReport, builder);
                            InsertMRProjectLevelTableData(user, publishHelper, tabledef, level1data, 1, groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "1" && x.groupId == "main_project") != null, budgetYear, forecastPeriod);
                            publishHelper.EndTable();
                            publishHelper.EndTableOrientation();
                        }
                        else
                        {
                            var gr2Ids = resultSet.Where(x => x.level1 == level1).Select(x => x.level2).Distinct().ToList();
                            var totalSum = InsertMRProjectLevelTotalRowLine(userDetails, level1data, level1);
                           
                            level1data.Add(totalSum);
                            Level2Data.AddRange(level1data);


                        }

                    }
                    if (groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "2" && x.groupName != string.Empty) != null)// when two level are test
                    {
                        var totalSum = InsertMRProjectLevelTotalRowLine(userDetails, resultSet, "999");
                        Level2Data.Add(totalSum);
                        publishHelper.StartTableOrientation(tabledef);
                        publishHelper.StartTable("InvestmentProjLevelGrid_"+ level.ToString() + "_" + lvlId, lvlId.ToString(), null, tabledef);
                        InsertMRInvestmentTableHeader(tabledef, publishHelper, langStringValuesMonthlyReport, builder);
                        InsertMRProjectLevellvl2TableData(user, publishHelper, tabledef, Level2Data, groupLevels.tableGroupRows.FirstOrDefault(x => x.levelId == "2" && x.groupId == "main_project") != null, budgetYear, forecastPeriod);

                        publishHelper.EndTable();
                        publishHelper.EndTableOrientation();
                    } 
                }
                string statusDesc = GetStatusDesc(userDetails.tenant_id, forecastPeriod, projectLevel, lvlId);
                if (!string.IsNullOrEmpty(statusDesc))
                {
                    publishHelper.InsertDescriptionCk5(statusDesc, user);
                }

                builder.InsertBreak(BreakType.SectionBreakContinuous);
            }
            catch (Exception ex)
            {

                publishHelper.InsertError("Failed to insert project level : " + ex.Message);
            }
            
          
        }



        private void InsertMRProjectLevelTableData(string user, IPublishHelper publishHelper, TableDefinition tabledef, List<MonthlyReportProjectLevelHelperNew> resultSet, int level, bool isMainProjectSelected, int budgetYear, int forecastPeriod)
        {
            UserData userDetails = _utility.GetUserDetails(user);

            Dictionary<string, clsLanguageString> langStringValuesMonthlyReportInv = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport_Investment");
            Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            Dictionary<string, clsLanguageString> invStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "Investment");
            JArray dataArray = JArray.FromObject(resultSet);
            int outParam = 0;
            var monthlyReportDBContext = _utility.GetTenantDBContext();
            var finishedFlagSet = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user).FirstOrDefault(x => x.finishedFlag);
            var finishedStatus = finishedFlagSet != null ? finishedFlagSet.Key : 0;

            string numberTypeAmount = numberFormats["amount"].LangText;
            string numberTypePercentage = numberFormats["percentage"].LangText;
            string numberTypeDecimalAmount = numberFormats["dec1"].LangText;
            bool divideByMillions = tabledef.AmountFormat == AmountFormats.Millions;
            string invId = string.Empty;
            string invName = string.Empty;
            string invDetailPath = string.Empty;

            //check to hide running investment
            string param_value = _utility.GetParameterValue(user, "BMDOC_INV_HIDE_RUNNING");

            bool hideRunningInv = string.IsNullOrEmpty(param_value) ? false : bool.Parse(param_value.ToLower());
            //get risk list
            var risklist = _monthlyReportInvestment.GetRiskList(user);

            //get risk list
            var statuslist = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user);

            //quater data
            var quarterData = _utility.GetEstimatedFinishQuarter(budgetYear, 1, user);

            List<string> defaultColumn = new List<string>();
            dynamic labels = new JArray();
            int statusreviewColIndex = -1;
            foreach (var item in tabledef.ColumnDefinitions.ToList())
            {
                defaultColumn.Add(item.ColumnId);
            }

            int index = 0;
            var columns = publishHelper.GetCurrentChapterColumns();
            if (publishHelper.GetType() == typeof(WebHelperMr))
            {
                foreach (var col in columns)
                {
                    if (col.columns.Count() > 0)
                    {
                        foreach (var colChild in col.columns)
                        {
                            if (colChild.columns.Count() > 0)
                            {
                                foreach (var colChildSub in colChild.columns)
                                {
                                    if (colChildSub.columns.Count() > 0)
                                    {
                                        foreach (var colChildSubSuper in colChildSub.columns)
                                        {
                                            if (colChildSubSuper.columns.Count() > 0)
                                            {
                                                if (!string.IsNullOrEmpty(colChildSubSuper.title.Trim()))
                                                {
                                                    labels.Add(colChildSubSuper.title);
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (!string.IsNullOrEmpty(colChildSub.title.Trim()))
                                        {
                                            labels.Add(colChildSub.title);
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if (!string.IsNullOrEmpty(colChild.title.Trim()))
                                {
                                    labels.Add(colChild.title);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(col.title.Trim()) && tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == "statusreview".ToLower()).ColumnName != col.title.Trim())// get titel except komment column
                        {
                            labels.Add(col.title);
                        }
                        if (tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == "statusreview".ToLower()).ColumnName == col.title.Trim())
                        {
                            statusreviewColIndex = index;// get the index of the commetcolumn
                        }
                        index++;
                    }
                }
            }

            PropertyInfo[] myPropInfo = typeof(MonthlyReportProjectLevelHelperNew).GetProperties();
            // insert data for the document

            foreach (var d in dataArray)
            {
                dynamic invData = new JArray();
                var lstColumnDetails = new List<ColumnDetails>();
                var lstColumnDataDetails = new List<string>();

                //for each column to display insert record
                int count = 0;
                foreach (var col in defaultColumn)
                {
                    count++;
                    var cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.WrapText = true;
                    cellTemplate.IsBold = false;
                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        if (count == 1)
                        {
                            cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        else
                        {
                            cellTemplate.FontColor = FramsiktColors.DefaultTextColor;
                        }
                    }
                    if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1)
                    {
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            cellTemplate.FontColor = FramsiktColors.MonthlyReportTotalColor;
                            cellTemplate.IsTotalRow = true;
                            cellTemplate.Fontsize = 17;
                            cellTemplate.IsBold = true;
                        }
                        else
                        {
                            cellTemplate.FontColor = FramsiktColors.TotalColor;
                        }
                    }

                    if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "accountingAmountDeviation".ToLower() || col.ToLower() == "deviation" || col.ToLower() == "totalorgbudget" || col.ToLower() == "costapproval")
                    {
                        if (publishHelper.GetType() != typeof(WebHelperMr))
                        {
                            cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                        }
                    }

                    cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                    cellTemplate.TopBorder = 0;
                    cellTemplate.BottomBorder = 1;

                    lstColumnDetails.Add(cellTemplate);

                    string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                    //apply number formatting of for number column
                    if (name.ToLower() != "investment" && name.ToLower() != "grouping" && name.ToLower() != "investmentPhase".ToLower() && name.ToLower() != "oe_flag".ToLower() && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter" && name.ToLower() != "approvalreference" && name.ToLower() != "qualitystatusdesc" && name.ToLower() != "finstatusdesc")
                    {
                        if (name.ToLower() != "consumepercentage")
                        {
                            decimal ammount = 0;

                            bool success = decimal.TryParse(d[name].ToString(), out ammount);
                            if (name.ToLower() == "totalamount".ToLower() || name.ToLower() == "changetotalamount".ToLower() || name.ToLower() == "accountingAmountDeviation".ToLower() || name.ToLower() == "deviation".ToLower() || name.ToLower() == "totalorgbudget" || name.ToLower() == "accountingtotal")
                            {
                                if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && (Convert.ToString(d["PkId"]) == "-1" || Convert.ToString(d["PkId"]) == "-2"))
                                {
                                    if (hideRunningInv)
                                    {
                                        lstColumnDataDetails.Add(string.Empty);
                                    }
                                    else
                                    {
                                        if (!divideByMillions)
                                        {
                                            lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                        }
                                    }
                                }
                                else if (!string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                {
                                    lstColumnDataDetails.Add("-");
                                }
                                else
                                {
                                    if (!divideByMillions)
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                    }
                                }
                            }
                            else
                            {
                                if (name.ToLower() == "costapproval")
                                {
                                    if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && (Convert.ToString(d["PkId"]) == "-1" || Convert.ToString(d["PkId"]) == "-2"))
                                    {
                                        if (hideRunningInv)
                                        {
                                            lstColumnDataDetails.Add(string.Empty);
                                        }
                                        else
                                        {
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                    }
                                    else if (ammount == 0 || !string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                    {
                                        if (!string.IsNullOrEmpty(((string)d["invStatusDescription"]))) lstColumnDataDetails.Add("-");
                                        else lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_approvalCostText"].LangText);
                                    }
                                    else
                                    {
                                        if (!divideByMillions)
                                        {
                                            lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                        }
                                    }
                                }
                                else if (name.ToLower() == "adjustedbudget")
                                {
                                    // neglect the 0 values for only these two cols.
                                    if (!divideByMillions)
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                    }
                                }
                                else if (name.ToLower() == "accounting")
                                {
                                    // neglect the 0 values for only these two cols.
                                    if (!divideByMillions)
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                    }
                                }
                                else if (name.ToLower() == "forecastamount")
                                {
                                    // neglect the 0 values for forecast col.
                                    if (!divideByMillions)
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                    }
                                }
                                else
                                {
                                    if (!divideByMillions)
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                    }
                                }
                            }
                        }
                        else
                        {
                            lstColumnDataDetails.Add(Convert.ToString(d["PkId"]) != "-1" ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                        }
                    }
                    else
                    {
                        if (name.ToLower() == "statusreview")
                        {
                            lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                        }
                        else if (name.ToLower() == "status")
                        {
                            var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                            lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                        }
                        else if (name.ToLower() == "investmentPhase".ToLower() || name.ToLower() == "oe_flag".ToLower())
                        {
                            lstColumnDataDetails.Add((string)d[name]);
                        }
                        else if (name.ToLower() == "risk")
                        {
                            var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                            lstColumnDataDetails.Add(risk != null ? (string)risk.Value : "");
                            if (risk != null)
                            {
                                switch (risk.Key)
                                {
                                    case "1":
                                        lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel1Color;
                                        break;

                                    case "2":
                                        lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel2Color;
                                        break;

                                    case "3":
                                        lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel3Color;
                                        break;
                                }
                            }
                        }
                        else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == 0) || (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1))) || ((name.ToLower() == "finishedyear") && (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1)))
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else if (name.ToLower() == "finishedyear")
                        {
                            if (((string)d["invStatusDescription"] != ""))
                            {
                                lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                            }
                            else
                            {
                                lstColumnDataDetails.Add(((string)d[name]));
                            }
                        }
                        else if (name.ToLower() == "estimatedquarter" && ((int)d[name] != 0))
                        {
                            if (((string)d["invStatusDescription"] != ""))
                            {
                                lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                            }
                            else
                            {
                                if ((int)d["Status"] == finishedStatus)
                                {
                                    var status = statuslist.FirstOrDefault(x => x.Key == (int)d["Status"]);
                                    lstColumnDataDetails.Add(status.Value.ToString());
                                }
                                else
                                {
                                    var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                    lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : "");
                                }
                            }
                        }
                        else if (name.ToLower() == "approvalreference")
                        {
                            if (!string.IsNullOrEmpty((string)d["ApprovalReferenceUrl"]) && publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDataDetails.Add("<a href=" + (string)d["ApprovalReferenceUrl"] + " target=_blank>" + (string)d["ApprovalReference"] + "</a>");
                            }
                            else
                            {
                                lstColumnDataDetails.Add((string)d[name]);
                            }
                        }
                        else if (name.ToLower() == "qualitystatusdesc".ToLower() || name.ToLower() == "finstatusdesc".ToLower())
                        {
                            lstColumnDataDetails.Add((string)d[name]);
                        }
                        else
                        {
                            if (publishHelper.GetType() == typeof(WebHelperMr) && tabledef.TableId != "MrInvestmentPerOrgId" && tabledef.TableId != "MrYearlyInvestmentPerOrgId")
                            {
                                if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && isMainProjectSelected)
                                {
                                    string rowtemplateWithData = string.Empty;
                                    rowtemplateWithData = $"<a href='javascript:void(0)' title = \"(Sprettoppvindu) {(string)d["level1Name"]}\" onkeypress = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString(d["level1"])}\")' onClick = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString(d["level1"])}\")'>{(string)d["level1Name"]} <span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                                    lstColumnDataDetails.Add(rowtemplateWithData);

                                    invId = Convert.ToString(d["sa_lvl1_gr_" + level + "_id"]);
                                    invName = ((string)d["level1Name"]);
                                    invDetailPath = publishHelper.GetCurrentChapterPath() + "invdetails/";
                                }
                                else
                                    lstColumnDataDetails.Add(((string)d["level1Name"]));
                            }
                            else
                            {
                                lstColumnDataDetails.Add(((string)d["level1Name"]));
                            }
                        }
                    }
                }
                bool allValueZeroes = true;
                int c = 0;
                lstColumnDataDetails.ForEach(x =>
                {
                    decimal valdecimal;
                    decimal.TryParse(x, out valdecimal);
                    if (count > 0 && c != (count - 1) && valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                    {
                        allValueZeroes = false;
                    }
                    else
                    {
                        if (valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                        {
                            allValueZeroes = false;
                        }
                    }
                    c++;
                });
                //added allvalueZero check bug id- #66842
                if ((!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1"))
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                else if (!allValueZeroes)
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);

                if (publishHelper.GetType() == typeof(WebHelperMr) && isMainProjectSelected)
                {
                    int dcount = 0;
                    var drow = publishHelper.GetCurrentRowData();
                    drow.ForEach(x =>
                    {
                        if (dcount == 0)
                        {
                            invData.Add(Regex.Replace(x, "<.*?>", String.Empty));
                        }
                        else if (dcount != statusreviewColIndex) //skip komment column
                        {
                            invData.Add(x);
                        }

                        dcount++;
                    });
                }
                //web popup

                if (publishHelper.GetType() == typeof(WebHelperMr) && invData.Count > 0 && isMainProjectSelected)
                {
                    dynamic invObject = new JObject();
                    invData[0] = invName;
                    for (int i = 0; i < invData.Count; i++)
                    {
                        if (invData[i] == null)
                        {
                            invData[i] = string.Empty;
                        }
                    }
                    invObject.Add("Data", invData);
                    invObject.Add("Labels", labels);
                    invObject.WebUrlLink = string.Empty;
                    invObject.popUpTitle = invName;

                    string description = string.Empty;
                    invObject.popUpDescription1 = string.IsNullOrEmpty(description) ? "" : description;

                    string description2 = string.Empty;
                    if (invId != "0")
                    {
                        List<MonthlyReportProjectLevelHelperNew> descriptiondata = resultSet;
                        string desc2 = descriptiondata.FirstOrDefault(x => x.level1 == invId) != null ? descriptiondata.FirstOrDefault(x => x.level1 == invId).Statusreview : string.Empty;
                        description2 = desc2;
                      
                    }
                    invObject.popUpDescription2 = description2;

                    invObject.popUpHeaderDesc1 = string.IsNullOrEmpty(description) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc1_heading"].LangText;
                    invObject.popUpHeaderDesc2 = string.IsNullOrEmpty(description2) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc2_heading"].LangText;

                    invObject.showPopupDescription = true;
                    publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(invObject), invDetailPath, invId.ToString(), PubContentHandler.BlobType.Json);

                }
            }
        }



        private MonthlyReportProjectLevelHelperNew InsertMRProjectLevelTotalRowLine(UserData userDetails, List<MonthlyReportProjectLevelHelperNew> data, string level1id = null)
        {
            var totalRow = new MonthlyReportProjectLevelHelperNew();
            Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport_Investment");
            var invSpecificData = data.Where(x => string.IsNullOrEmpty(x.invStatusDescription)).ToList() ?? new List<MonthlyReportProjectLevelHelperNew>();
            totalRow = new MonthlyReportProjectLevelHelperNew()
            {
                Investment = langStringValuesMonthlyReport["MRI_investmentGrid_sum"].LangText,
                level1Name = langStringValuesMonthlyReport["MRI_investmentGrid_sum"].LangText,
                level2Name = langStringValuesMonthlyReport["MRI_investmentGrid_sum"].LangText,
                //sa_lvl1_gr_3_name = langStringValuesMonthlyReport["MRI_investmentGrid_sum"].LangText,
                //OriginalPlannedamount = totaldata.Select(x => x.OriginalPlannedamount).Sum(),
                Regulation = data.Select(x => x.Regulation).Sum(),
                TotalAmount = invSpecificData.Select(x => x.TotalAmount).Sum(),
                TotUsagePercentage = data.Select(x => x.TotUsagePercentage).Sum(),
                ChangeTotalAmount = invSpecificData.Select(x => x.ChangeTotalAmount).Sum(),
                PlannedAmount = data.Select(x => x.PlannedAmount).Sum(),
                TotalOrgBudget = invSpecificData.Select(x => x.TotalOrgBudget).Sum(),
                Accounting = data.Select(x => x.Accounting).Sum(),
                AccountingTotal = invSpecificData.Select(x => x.AccountingTotal).Sum(),
                Deviation = invSpecificData.Select(x => x.Deviation).Sum(),
                AccountingPreviousYear = data.Select(x => x.AccountingPreviousYear).Sum(),
                ForecastAmount = data.Select(x => x.ForecastAmount).Sum(),
                ForecastDeviation = data.Select(x => x.ForecastDeviation).Sum(),
                ConsumePercentage = data.Select(x => x.ConsumePercentage).Sum(),
                Status = -1,
                Risk = -1,
                ProjectCode = "",
                PkId = level1id == "999" ? "9999" : "-1",
                level1 =  level1id,
                level2 =  "-1",
                PkIdServId = "-1",
                EstimatedQuarter = -1,
                FinishedYear = null,
                //DepartmentCode="",
                Statusreview = string.Empty,
                investmentPhase = string.Empty,
                IsReported = false,
                AdjustedBudget = data.Select(x => x.AdjustedBudget).Sum(),
                ProposedAdjustment = data.Select(x => x.ProposedAdjustment).Sum(),
                ActualAmtLastYear = data.Select(x => x.ActualAmtLastYear).Sum(),
                CostApproval = data.Select(x => x.CostApproval).Sum(),
                BudgetAcctdeviation = data.Select(x => x.BudgetAcctdeviation).Sum(),
                budChangeYear1 = data.Select(x => x.budChangeYear1).Sum(),
                budChangeYear2 = data.Select(x => x.budChangeYear2).Sum(),
                budChangeYear3 = data.Select(x => x.budChangeYear3).Sum(),
                budChangeYear4 = data.Select(x => x.budChangeYear4).Sum(),
                budChangeYearTotal = data.Select(x => x.budChangeYearTotal).Sum(),
                //sa_lvl1_gr_2_id = level2id ?? "-1",
                qualityStatusDesc = string.Empty,
                finStatusDesc = string.Empty,
                UnApprvBudChange = data.Select(x => x.UnApprvBudChange).Sum(),
                TotalYbUbcBudget = data.Select(x => x.TotalYbUbcBudget).Sum(),
                forecastdeviationCalculation = data.Select(x => x.forecastdeviationCalculation).Sum(),
                accountingAmountDeviation = invSpecificData.Select(x => x.accountingAmountDeviation).Sum()
            };
            return totalRow;
        }



        private void InsertMRProjectLevellvl2TableData(string user, IPublishHelper publishHelper, TableDefinition tabledef, List<MonthlyReportProjectLevelHelperNew> resultSet, bool isMainProjectSelected, int budgetYear, int forecastPeriod)
        {
            UserData userDetails = _utility.GetUserDetails(user);

            Dictionary<string, clsLanguageString> langStringValuesMonthlyReportInv = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport_Investment");
            Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            Dictionary<string, clsLanguageString> invStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "Investment");

            var monthlyReportDBContext = _utility.GetTenantDBContext();
            var finishedFlagSet = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user).FirstOrDefault(x => x.finishedFlag);
            var finishedStatus = finishedFlagSet != null ? finishedFlagSet.Key : 0;
            string numberTypeAmount = numberFormats["amount"].LangText;
            string numberTypePercentage = numberFormats["percentage"].LangText;
            string numberTypeDecimalAmount = numberFormats["dec1"].LangText;
            bool divideByMillions = tabledef.AmountFormat == AmountFormats.Millions;
            string invId = string.Empty;
            string invName = string.Empty;
            string invDetailPath = string.Empty;
            dynamic invData = new JArray();

            //check to hide running investment
            string param_value = _utility.GetParameterValue(user, "BMDOC_INV_HIDE_RUNNING");
            string showInvestmentWithZero = _utility.GetParameterValue(user, "MR_SHOW_INV_ZERO");
            bool hideRunningInv = string.IsNullOrEmpty(param_value) ? false : bool.Parse(param_value.ToLower());

            //get risk list
            var risklist = _monthlyReportInvestment.GetRiskList(user);

            //get risk list
            var statuslist = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user);

            //quater data
            var quarterData = _utility.GetEstimatedFinishQuarter(budgetYear, 1, user);

            List<string> defaultColumn = new List<string>();
            dynamic labels = new JArray();
            int statusreviewColIndex = -1;
            foreach (var item in tabledef.ColumnDefinitions.ToList())
            {
                defaultColumn.Add(item.ColumnId);
            }
            int count = 0;
            var gr2Ids = resultSet.Select(x => x.level1).Distinct().ToList();

            PropertyInfo[] myPropInfo = typeof(MonthlyReportProjectLevelHelperNew).GetProperties();

            int index = 0;
            var columns = publishHelper.GetCurrentChapterColumns();
            if (publishHelper.GetType() == typeof(WebHelperMr))
            {
                foreach (var col in columns)
                {
                    if (col.columns.Count() > 0)
                    {
                        foreach (var colChild in col.columns)
                        {
                            if (colChild.columns.Count() > 0)
                            {
                                foreach (var colChildSub in colChild.columns)
                                {
                                    if (colChildSub.columns.Count() > 0)
                                    {
                                        foreach (var colChildSubSuper in colChildSub.columns)
                                        {
                                            if (colChildSubSuper.columns.Count() > 0)
                                            {
                                                if (!string.IsNullOrEmpty(colChildSubSuper.title.Trim()))
                                                {
                                                    labels.Add(colChildSubSuper.title);
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (!string.IsNullOrEmpty(colChildSub.title.Trim()))
                                        {
                                            labels.Add(colChildSub.title);
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if (!string.IsNullOrEmpty(colChild.title.Trim()))
                                {
                                    labels.Add(colChild.title);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(col.title.Trim()) && tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == "statusreview".ToLower()).ColumnName != col.title.Trim())// get titel except komment column
                        {
                            labels.Add(col.title);
                        }
                        if (tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == "statusreview".ToLower()).ColumnName == col.title.Trim())
                        {
                            statusreviewColIndex = index;// get the index of the commetcolumn
                        }
                        index++;
                    }
                }
            }

            foreach (var id in gr2Ids)
            {
                if (id == "999")
                    continue;
                var dataSet = resultSet.Where(x => x.level1 == id);
                //convert it to JArray
                JArray dataArray = JArray.FromObject(dataSet);
                if (dataArray.Any())
                {
                    var lstColumnDetails = new List<ColumnDetails>();
                    var lstColumnDataDetails = new List<string>();
                    var cellTemplate = new ColumnDetails();
                    var subHeading = dataSet.FirstOrDefault().level1Name;
                    foreach (var c in defaultColumn)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.WrapText = true;
                        cellTemplate.BottomBorder = 0;
                        cellTemplate.Fontsize = 9;

                        if (c.ToLower() == "investment".ToLower())
                        {
                            lstColumnDataDetails.Add(dataSet.FirstOrDefault().level1Name);
                            cellTemplate.Alignment = ParagraphAlignment.Left;
                        }
                        else
                        {
                            lstColumnDataDetails.Add(string.Empty);
                            cellTemplate.Alignment = ParagraphAlignment.Right;
                        }
                        lstColumnDetails.Add(cellTemplate);
                    }

                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.MonthlyReportSubHeaderColor);
                        lstColumnDetails.ForEach(p => p.Fontsize = 17);
                        lstColumnDetails.ForEach(p => p.IsSemiBold = true);
                    }

                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.SubHeading1);
                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                    }
                    // insert data for the document

                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.DefaultTextColor);
                        lstColumnDetails.ForEach(p => p.Fontsize = 9);
                        lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                    }

                    foreach (var d in dataArray)
                    {
                        invData = new JArray();
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();
                        cellTemplate = new ColumnDetails();
                        count = 0;
                        if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "9999")
                        {
                            continue;
                        }
                        foreach (var col in defaultColumn)
                        {
                            count++;
                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                            cellTemplate.Padding = (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1") ? 0 : 5;
                            cellTemplate.Fontsize = 9;
                            cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                            cellTemplate.WrapText = true;
                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                if ((!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1"))
                                {
                                    cellTemplate.FontColor = FramsiktColors.DefaultTextColor;
                                    cellTemplate.Fontsize = 17;
                                    cellTemplate.IsSemiBold = true;
                                    cellTemplate.IsTotalRow = true;
                                }
                                else
                                {
                                    if (count == 1)
                                    {
                                        cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                                    }
                                    else
                                    {
                                        cellTemplate.FontColor = FramsiktColors.DefaultTextColor;
                                    }
                                }
                            }
                            else
                            {
                                cellTemplate.IsBold = (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1");
                            }

                            if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "deviation" || col.ToLower() == "accountingAmountDeviation".ToLower() || col.ToLower() == "finishedyear" || col.ToLower() == "EstimatedQuarter".ToLower() || col.ToLower() == "totalorgbudget".ToLower() || col.ToLower() == "costapproval".ToLower())
                            {
                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                            }
                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            cellTemplate.BottomBorder = (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1") ? 1 : 0;
                            if (col == "risk" && !string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) != "-1")
                            {
                                cellTemplate.ContentType = ContentType.Image;
                            }

                            string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                            //apply number formatting of for number column
                            if (name.ToLower() != "investment" && name.ToLower() != "investmentPhase".ToLower() && name.ToLower() != "oe_flag".ToLower() && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter" && name.ToLower() != "approvalreference" && name.ToLower() != "qualitystatusdesc" && name.ToLower() != "finstatusdesc")
                            {
                                if (name.ToLower() != "consumepercentage")
                                {
                                    decimal ammount = 0;

                                    bool success = decimal.TryParse(d[name].ToString(), out ammount);
                                    if (name.ToLower() == "totalamount".ToLower() || name.ToLower() == "changetotalamount".ToLower() || name.ToLower() == "deviation".ToLower() || name.ToLower() == "accountingAmountDeviation".ToLower() || name.ToLower() == "totalorgbudget" || name.ToLower() == "accountingtotal".ToLower())
                                    {
                                        if (!string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                        {
                                            lstColumnDataDetails.Add("-");
                                        }
                                        else if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1")
                                        {
                                            if (hideRunningInv)
                                            {
                                                lstColumnDataDetails.Add(string.Empty);
                                            }
                                            else
                                            {
                                                if (!divideByMillions)
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                }
                                                else
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (name.ToLower() == "costapproval")
                                        {
                                            if (ammount == 0 || !string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                            {
                                                if (!string.IsNullOrEmpty(((string)d["invStatusDescription"]))) lstColumnDataDetails.Add("-");
                                                else lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_approvalCostText"].LangText);
                                            }
                                            else if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1")
                                            {
                                                if (hideRunningInv)
                                                {
                                                    lstColumnDataDetails.Add(string.Empty);
                                                }
                                                else
                                                {
                                                    if (!divideByMillions)
                                                    {
                                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                    }
                                                    else
                                                    {
                                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if (!divideByMillions)
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                }
                                                else
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                }
                                            }
                                        }
                                        else if (name.ToLower() == "adjustedbudget")
                                        {
                                            // neglect the 0 values for only these two cols.
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                if (ammount / 1000000 == 0)
                                                {
                                                }
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                        else if (name.ToLower() == "accounting")
                                        {
                                            // neglect the 0 values for only these two cols.
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                        else if (name.ToLower() == "forecastamount")
                                        {
                                            // neglect the 0 values for forecast col.
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                        else
                                        {
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    lstColumnDataDetails.Add(Convert.ToString(d["PkId"]) != "-1" ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                                }
                            }
                            else
                            {
                                if (name.ToLower() == "statusreview")
                                {
                                    lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                                }
                                else if (name.ToLower() == "status")
                                {
                                    var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                                    lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                                }
                                else if (name.ToLower() == "investmentPhase".ToLower() || name.ToLower() == "oe_flag".ToLower())
                                {
                                    lstColumnDataDetails.Add((string)d[name]);
                                }
                                else if (name.ToLower() == "risk")
                                {
                                    var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                                    if (risk != null)
                                    {
                                        string image = risk.Value.ToString().ToLower() == langStringValuesMonthlyReportInv["MRI_investmentGrid_risk_low"].LangText.ToLower() ? "trafficlights-01.svg" :
                                         risk.Value.ToString().ToLower() == langStringValuesMonthlyReportInv["MRI_investmentGrid_risk_medium"].LangText.ToLower() ? "trafficlights-02.svg" :
                                         risk.Value.ToString().ToLower() == langStringValuesMonthlyReportInv["MRI_investmentGrid_risk_high"].LangText.ToLower() ? "trafficlights-03.svg" : string.Empty;
                                        lstColumnDataDetails.Add(image);
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add(string.Empty);
                                    }
                                }
                                else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == -1) && (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1"))) || ((name.ToLower() == "finishedyear") && (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1")))
                                {
                                    lstColumnDataDetails.Add(string.Empty);
                                }
                                else if (name.ToLower() == "finishedyear")
                                {
                                    if (!string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                    {
                                        lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add(!string.IsNullOrEmpty(((string)d[name])) ? ((string)d[name]) : string.Empty);
                                    }
                                }
                                else if (name.ToLower() == "approvalreference")
                                {
                                    if (!string.IsNullOrEmpty((string)d["ApprovalReferenceUrl"]))
                                    {
                                        cellTemplate.ContentType = ContentType.Url;
                                        lstColumnDataDetails.Add((string)d["ApprovalReferenceUrl"] + "_endurl_" + (string)d[name]);
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((string)d[name]);
                                    }
                                }
                                else if (name.ToLower() == "qualitystatusdesc".ToLower() || name.ToLower() == "finstatusdesc".ToLower())
                                {
                                    lstColumnDataDetails.Add((string)d[name]);
                                }
                                else if (name.ToLower() == "estimatedquarter")
                                {
                                    if (((string)d["invStatusDescription"] != ""))
                                    {
                                        lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                    }
                                    else
                                    {
                                        if ((int)d["Status"] == finishedStatus)
                                        {
                                            var status = statuslist.FirstOrDefault(x => x.Key == (int)d["Status"]);
                                            lstColumnDataDetails.Add(status.Value.ToString());
                                        }
                                        else
                                        {
                                            var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                            lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : langStringValuesMonthlyReportInv["PM_investment_done_status"].LangText);
                                        }
                                    }
                                }
                                else
                                {
                                    invId = (!string.IsNullOrEmpty(Convert.ToString(d["PkId"]))) ? Convert.ToString(d["PkId"]) : "0";
                                    if (publishHelper.GetType() == typeof(WebHelperMr) && (invId != "-1" && invId != "-2") && isMainProjectSelected)
                                    {
                                        if (!string.IsNullOrEmpty(Convert.ToString(d["level2"])))
                                        {
                                            string rowtemplateWithData = string.Empty;
                                            rowtemplateWithData = $"<a href='javascript:void(0)' title = \"(Sprettoppvindu) {(string)d["level2Name"]}\" onkeypress = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString(d["level2"])}\")' onClick = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString(d["level2"])}\")'>{(string)d["level2Name"]} <span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                                            lstColumnDataDetails.Add(rowtemplateWithData);

                                            invId = Convert.ToString(d["level2"]);
                                            invName = ((string)d["level2Name"]);
                                            invDetailPath = publishHelper.GetCurrentChapterPath() + "invdetails/";
                                        }
                                    }
                                    else
                                    {
                                        if (invId == "-1")
                                        {
                                            lstColumnDataDetails.Add((string)d["level2Name"] + " - " + subHeading);
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(((string)d["level2Name"]));
                                        }
                                    }
                                }
                            }
                            lstColumnDetails.Add(cellTemplate);
                        }

                        decimal totalSum = 0;
                        if (lstColumnDataDetails.Contains(langStringValuesMonthlyReportInv["MRI_investmentGrid_sumotherProjects"].LangText))
                        {
                            foreach (var item in lstColumnDataDetails)
                            {
                                decimal z = 0;
                                bool isDecimal = decimal.TryParse(string.IsNullOrEmpty(item) ? null : item.ToString(), out z);
                                if (isDecimal)
                                {
                                    totalSum += Convert.ToDecimal(z);
                                }
                            }

                            if (totalSum != 0)
                            {
                                for (int i = 0; i < defaultColumn.Count; i++)
                                {
                                    if (defaultColumn[i].ToLower() == "totalamount".ToLower() || defaultColumn[i].ToLower() == "changetotalamount".ToLower() || defaultColumn[i].ToLower() == "deviation".ToLower()
                                        || defaultColumn[i].ToLower() == "EstimatedQuarter".ToLower() || defaultColumn[i].ToLower() == "FinishedYear".ToLower() || defaultColumn[i].ToLower() == "totalorgbudget".ToLower() || defaultColumn[i].ToLower() == "costapproval".ToLower())
                                    {
                                        lstColumnDataDetails[i] = string.Empty;
                                    }
                                }

                                // if all amount col values for an investement is 0, dont show that investment in doc.
                                bool allValueZeroes = true;
                                int c = 0;
                                lstColumnDataDetails.ForEach(x =>
                                {
                                    decimal valdecimal;
                                    decimal.TryParse(x, out valdecimal);
                                    if (count > 0 && c != (count - 1) && valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                                    {
                                        allValueZeroes = false;
                                    }
                                    else
                                    {
                                        if (valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                                        {
                                            allValueZeroes = false;
                                        }
                                    }
                                    c++;
                                });
                                if (showInvestmentWithZero.ToLower() == "TRUE".ToLower() ||
                                    ((string.IsNullOrEmpty(showInvestmentWithZero) || (showInvestmentWithZero.ToLower() == "FALSE".ToLower())) && !allValueZeroes))
                                {
                                    publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                    }
                                }

                                int dcount = 0;
                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    var drow = publishHelper.GetCurrentRowData();
                                    drow.ForEach(x =>
                                    {
                                        if (dcount == 0 && !string.IsNullOrEmpty(x))
                                        {
                                            invData.Add(Regex.Replace(x, "<.*?>", String.Empty));
                                        }
                                        else
                                        {
                                            if (!string.IsNullOrEmpty(x) && x.Contains("_endurl_"))
                                            {
                                                x = "<a href=" + (string)d["ApprovalReferenceUrl"] + " target = _blank>" + (string)d["ApprovalReference"] + "</a>";
                                                invData.Add(x);
                                            }
                                            else if (!string.IsNullOrEmpty(x) && x.Contains(".svg"))
                                            {
                                                x = $"<img src='content/appimages/{x.ToString().Trim()}' onerror=\"this.style.display='none'\" title='' alt=''/>";
                                                invData.Add(x);
                                            }
                                            else
                                            {
                                                invData.Add(x);
                                            }
                                        }

                                        dcount++;
                                    });
                                }
                            }
                        }
                        else
                        {
                            // if all amount col values for an investement is 0, dont show that investment in doc.
                            bool allValueZeroes = true;
                            int c = 0;
                            lstColumnDataDetails.ForEach(x =>
                            {
                                decimal valdecimal;
                                decimal.TryParse(x, out valdecimal);
                                if (count > 0 && c != (count - 1) && valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                                {
                                    allValueZeroes = false;
                                }
                                else
                                {
                                    if (valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                                    {
                                        allValueZeroes = false;
                                    }
                                }
                                c++;
                            });
                            if (showInvestmentWithZero.ToLower() == "TRUE".ToLower() ||
                                ((string.IsNullOrEmpty(showInvestmentWithZero) || (showInvestmentWithZero.ToLower() == "FALSE".ToLower())) && !allValueZeroes))
                            {
                                if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) == "-1")
                                {
                                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                                }
                                else
                                {
                                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                                }
                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                }
                            }

                            int dcount = 0;
                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                var drow = publishHelper.GetCurrentRowData();
                                drow.ForEach(x =>
                                {
                                    if (dcount == 0 && !string.IsNullOrEmpty(x))
                                    {
                                        invData.Add(Regex.Replace(x, "<.*?>", String.Empty));
                                    }
                                    else
                                    {
                                        if (!string.IsNullOrEmpty(x) && x.Contains("_endurl_"))
                                        {
                                            x = "<a href=" + (string)d["ApprovalReferenceUrl"] + " target = _blank>" + (string)d["ApprovalReference"] + "</a>";
                                            invData.Add(x);
                                        }
                                        else if (!string.IsNullOrEmpty(x) && x.Contains(".svg"))
                                        {
                                            x = $"<img src='content/appimages/{x.ToString().Trim()}' onerror=\"this.style.display='none'\" title='' alt=''/>";
                                            invData.Add(x);
                                        }
                                        else
                                        {
                                            invData.Add(x);
                                        }
                                    }

                                    dcount++;
                                });
                            }
                        }
                        // web popup
                        if (publishHelper.GetType() == typeof(WebHelperMr) && invData.Count > 0 && isMainProjectSelected)
                        {
                            dynamic invObject = new JObject();
                            invData[0] = invName;
                            invObject.Add("Data", invData);
                            invObject.Add("Labels", labels);
                            invObject.WebUrlLink = string.Empty;
                            invObject.popUpTitle = invName;

                            string description = string.Empty;
                            invObject.popUpDescription1 = string.IsNullOrEmpty(description) ? "" : description;

                            string description2 = string.Empty;
                            if (invId != "0")
                            {
                                List<MonthlyReportProjectLevelHelperNew> descriptiondata = resultSet;
                                var descData = descriptiondata.ToList().FirstOrDefault(x => x.level2 == invId);
                                string desc2 = descData != null ? descData.Statusreview : string.Empty;
                                description2 = desc2;
                            }

                            invObject.popUpDescription2 = description2;

                            invObject.popUpHeaderDesc1 = string.IsNullOrEmpty(description) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc1_heading"].LangText;
                            invObject.popUpHeaderDesc2 = string.IsNullOrEmpty(description2) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc2_heading"].LangText;

                            invObject.showPopupDescription = true;
                            publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(invObject), invDetailPath, invId.ToString(), PubContentHandler.BlobType.Json);

                        }
                    }
                }
            }

            var finaldataSet = resultSet.Where(x => x.PkId == "9999");
            JArray finalSet = JArray.FromObject(finaldataSet);

            foreach (var d in finalSet)
            {
                var lstColumnDetails = new List<ColumnDetails>();
                var lstColumnDataDetails = new List<string>();
                var cellTemplate = new ColumnDetails();
                count = 0;
                foreach (var col in defaultColumn)
                {
                    count++;
                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Padding = 0;
                    cellTemplate.Fontsize = 9;
                    cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    cellTemplate.IsBold = true;
                    if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "deviation" || col.ToLower() == "finishedyear" || col.ToLower() == "EstimatedQuarter".ToLower() || col.ToLower() == "totalorgbudget".ToLower() || col.ToLower() == "costapproval".ToLower())
                    {
                        cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                    }
                    cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                    cellTemplate.BottomBorder = 1;
                    if (col == "risk" && Convert.ToString(d["PkId"]) != "-1")
                    {
                        cellTemplate.ContentType = ContentType.Image;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                    //apply number formatting of for number column
                    if (name.ToLower() != "investment" && name.ToLower() != "investmentPhase".ToLower() && name.ToLower() != "oe_flag".ToLower() && name.ToLower() != "deviation" && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter" && name.ToLower() != "approvalreference" && name.ToLower() != "qualitystatusdesc" && name.ToLower() != "finstatusdesc")
                    {
                        if (name.ToLower() != "consumepercentage")
                        {
                            decimal ammount = 0;

                            bool success = decimal.TryParse(d[name].ToString(), out ammount);
                            if (name.ToLower() == "costapproval".ToLower() || name.ToLower() == "changetotalamount".ToLower() || name.ToLower() == "totalorgbudget")
                            {
                                if (hideRunningInv)
                                {
                                    lstColumnDataDetails.Add(string.Empty);
                                }
                                else
                                {
                                    if (!divideByMillions)
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                    }
                                }
                            }
                            else
                            {
                                if (!divideByMillions)
                                {
                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                }
                                else
                                {
                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                }
                            }
                        }
                        else
                        {
                            lstColumnDataDetails.Add(!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && Convert.ToString(d["PkId"]) != "-1" ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                        }
                    }
                    else
                    {
                        if (name.ToLower() == "statusreview")
                        {
                            lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                        }
                        else if (name.ToLower() == "investmentPhase".ToLower())
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else if (name.ToLower() == "oe_flag".ToLower())
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else if (name.ToLower() == "deviation")
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else if (name.ToLower() == "status")
                        {
                            var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                            lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                        }
                        else if (name.ToLower() == "risk")
                        {
                            var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                            if (risk != null)
                            {
                                string image = risk.Value.ToString().ToLower() == langStringValuesMonthlyReportInv["MRI_investmentGrid_risk_low"].LangText.ToLower() ? "trafficlights-03.svg" :
                                 risk.Value.ToString().ToLower() == langStringValuesMonthlyReportInv["MRI_investmentGrid_risk_medium"].LangText.ToLower() ? "trafficlights-02.svg" :
                                 risk.Value.ToString().ToLower() == langStringValuesMonthlyReportInv["MRI_investmentGrid_risk_high"].LangText.ToLower() ? "trafficlights-01.svg" : string.Empty;
                                lstColumnDataDetails.Add(image);
                            }
                            else
                            {
                                lstColumnDataDetails.Add(string.Empty);
                            };
                        }
                        else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == 0) || Convert.ToString(d["PkId"]) == "-1")) || ((name.ToLower() == "finishedyear") && Convert.ToString(d["PkId"]) == "-1"))
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else if (name.ToLower() == "finishedyear")
                        {
                            if (!string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                            {
                                lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                            }
                            else
                            {
                                lstColumnDataDetails.Add(!string.IsNullOrEmpty(((string)d[name])) ? ((string)d[name]) : string.Empty);
                            }
                        }
                        else if (name.ToLower() == "estimatedquarter" && ((int)d[name] != 0))
                        {
                            if (((string)d["invStatusDescription"] != ""))
                            {
                                lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                            }
                            else
                            {
                                var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : "");
                            }
                        }
                        else if (name.ToLower() == "qualitystatusdesc".ToLower())
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else if (name.ToLower() == "finstatusdesc".ToLower())
                        {
                            lstColumnDataDetails.Add(string.Empty);
                        }
                        else
                        {
                            lstColumnDataDetails.Add(((string)d[name]));
                        }
                    }
                }

                if (publishHelper.GetType() == typeof(WebHelperMr))
                {
                    lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.MonthlyReportTotalColor);
                    lstColumnDetails.ForEach(p => p.Fontsize = 17);
                    lstColumnDetails.ForEach(p => p.IsBold = true);
                    lstColumnDetails.ForEach(p => p.IsTotalRow = true);
                }

                decimal totalSum = 0;
                if (lstColumnDataDetails.Contains(langStringValuesMonthlyReportInv["MRI_investmentGrid_sumotherProjects"].LangText))
                {
                    foreach (var item in lstColumnDataDetails)
                    {
                        decimal z = 0;
                        bool isDecimal = decimal.TryParse(string.IsNullOrEmpty(item) ? null : item.ToString(), out z);
                        if (isDecimal)
                        {
                            totalSum += Convert.ToDecimal(z);
                        }
                    }

                    if (totalSum != 0)
                    {
                        publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                        }
                    }
                }
                else
                {
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                    }
                }
            }
        }


        private string GetStatusDesc(int tenantId, int forecastPeriod, int level, string levelId)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            tmr_proj_status data = tenantDbContext.tmr_proj_status.FirstOrDefault(x => x.fk_tenant_id == tenantId &&
                                                        x.forecast_period == forecastPeriod &&
                                                        x.level == level.ToString() &&
                                                        x.level_id == levelId);

            return data != null ? data.status_desc : string.Empty;

        }


        private async Task<List<MonthlyReportProjectLevelHelperNew>>  FormatInvData(int tenantId, int forecastPeriod, List<MonthlyReportProjectLevelHelperNew> resultSet, string groupId)
        {
            TenantDBContext dBContext = await _utility.GetTenantDBContextAsync();
            List<tmr_proj_status> projStatusData = await dBContext.tmr_proj_status.Where(x => x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod && x.level == groupId).ToListAsync();
            var sumSet = resultSet.FirstOrDefault(x => x.PkId == "-2");
            resultSet.Remove(sumSet);
            List<MonthlyReportProjectLevelHelperNew> finalData = (from a in resultSet
                                                                   join b in projStatusData on a.level1 equals b.level_id into grp
                                                                   from res in grp.DefaultIfEmpty()
                                                                 //  where res.level == groupId
                                                                   select new MonthlyReportProjectLevelHelperNew()
                                                                   {
                                                                       level1 = a.level1,
                                                                       level1Name = a.level1Name,
                                                                       level2 = a.PkId,
                                                                       level2Name = a.Investment,
                                                                       PkId = a.PkId,
                                                                       Regulation = a.Regulation,
                                                                       TotalAmount = a.TotalAmount,
                                                                       ChangeTotalAmount = a.ChangeTotalAmount,
                                                                       PlannedAmount = a.PlannedAmount,
                                                                       TotalOrgBudget = a.TotalOrgBudget,
                                                                       Accounting = a.Accounting,
                                                                       AccountingTotal = a.AccountingTotal,
                                                                       TotalYearlyBudget = a.TotalYearlyBudget,
                                                                       UnApprvBudChange = a.UnApprvBudChange,
                                                                       TotUsagePercentage = a.TotUsagePercentage,
                                                                       Deviation = a.Deviation,
                                                                       ForecastAmount = a.ForecastAmount,
                                                                       FinancingForecast = a.FinancingForecast,
                                                                       ForecastDeviation = a.ForecastDeviation,
                                                                       ConsumePercentage = a.ConsumePercentage,
                                                                       Status = res != null ? res.status : 0,
                                                                       CostApproval = a.CostApproval,
                                                                       Risk = res != null ? res.risk.Value : 0,
                                                                       EstimatedQuarter = res != null ? res.est_finish_quarter : 0,
                                                                       FinishedYear = a.FinishedYear,
                                                                       AdjustedBudget = (a.AdjustedBudget),
                                                                       ProposedAdjustment =a.ProposedAdjustment,
                                                                       Statusreview = res != null ? res.status_desc : "",
                                                                       UpdatedBy = "",
                                                                       invStatusDescription = a.invStatusDescription,
                                                                       BudgetAcctdeviation = (a.BudgetAcctdeviation),
                                                                       ResponsibleName = "",
                                                                       ApproverName = "",
                                                                       ApprovalReference = a.ApprovalReference,
                                                                       ApprovalReferenceUrl = a.ApprovalReferenceUrl,
                                                                       RemainingBudget = a.RemainingBudget,
                                                                       BudgetRegulation = 0,
                                                                       budChangeYear1 = a.budChangeYear1,
                                                                       budChangeYear2 = a.budChangeYear2,
                                                                       budChangeYear3 = a.budChangeYear3,
                                                                       budChangeYear4 = a.budChangeYear4,
                                                                       budChangeYearTotal = a.budChangeYearTotal,
                                                                       investmentPhase = a.investmentPhase,
                                                                       oe_flag = a.oe_flag,
                                                                       qualityStatusDesc = a.qualityStatusDesc,
                                                                       finStatusDesc = a.finStatusDesc,
                                                                       accountingAmountDeviation = a.accountingAmountDeviation,
                                                                       costestimate_p50 = a.costestimate_p50,
                                                                       adjustedbudChangeYear1 = a.adjustedbudChangeYear1,
                                                                       adjustedbudChangeYear2 = a.adjustedbudChangeYear2,
                                                                       adjustedbudChangeYear3 = a.adjustedbudChangeYear3,
                                                                       adjustedbudChangeYear4 = a.adjustedbudChangeYear4,
                                                                       adjustedbudChangeTotal = a.adjustedbudChangeTotal

                                                                   }).ToList();

            var groupedData = (from d in finalData                               
                                 group d by new
                                 {
                                     d.FinishedYear,
                                     d.invStatusDescription,
                                     d.Statusreview,
                                     d.Status,
                                     d.Risk,
                                     d.EstimatedQuarter,
                                     d.ApprovalReference,
                                     d.ApprovalReferenceUrl,
                                     d.investmentPhase,
                                     d.level1,
                                     d.level1Name,
                                     d.oe_flag,
                                     d.qualityStatusDesc,
                                     d.finStatusDesc,
                                     d.PkId,
                                     d.level2Name
                                 } into g
                                 select new MonthlyReportProjectLevelHelperNew()
                                 {
                                     level1 = g.Key.level1,
                                     level1Name = g.Key.level1Name,
                                     level2 = g.Key.PkId,
                                     level2Name = g.Key.level2Name,
                                     PkId = g.Key.PkId,
                                     Regulation = g.Sum(x => x.Regulation),
                                     TotalAmount = g.Sum(x => x.TotalAmount),
                                     ChangeTotalAmount = g.Sum(x => x.ChangeTotalAmount),
                                     PlannedAmount = g.Sum(x => x.PlannedAmount),
                                     TotalOrgBudget = g.Sum(x => x.TotalOrgBudget),
                                     Accounting = g.Sum(x => x.Accounting),
                                     AccountingTotal = g.Sum(x => x.AccountingTotal),
                                     TotalYearlyBudget = g.Sum(x => x.TotalYearlyBudget),
                                     UnApprvBudChange = g.Sum(x => x.UnApprvBudChange),
                                     TotUsagePercentage = g.Sum(x => x.TotUsagePercentage),
                                     Deviation = g.Sum(x => x.Deviation),
                                     ForecastAmount = g.Sum(x => x.ForecastAmount),
                                     FinancingForecast = g.Sum(x => x.FinancingForecast),
                                     ForecastDeviation = g.Sum(x => x.ForecastDeviation),
                                     ConsumePercentage = g.Sum(x => x.ConsumePercentage),
                                     Status = g.Key.Status,
                                     CostApproval = g.Sum(x => x.CostApproval),
                                     Risk = g.Key.Risk,
                                     EstimatedQuarter = g.Key.EstimatedQuarter,
                                     FinishedYear = g.Key.FinishedYear,
                                     AdjustedBudget = g.Sum(x => x.AdjustedBudget),
                                     ProposedAdjustment = (g.Sum(x => x.ProposedAdjustment)),
                                     Statusreview = g.Key.Statusreview,
                                     UpdatedBy = "",
                                     invStatusDescription = g.Key.invStatusDescription,
                                     //ActualAmtLastYear = t.ActualAmtLastYear,
                                     BudgetAcctdeviation = g.Sum(x => x.BudgetAcctdeviation),
                                     ResponsibleName = "",
                                     ApproverName = "",
                                     ApprovalReference = g.Key.ApprovalReference,
                                     ApprovalReferenceUrl = g.Key.ApprovalReferenceUrl,
                                     RemainingBudget = g.Sum(x => x.RemainingBudget),
                                     BudgetRegulation = 0,
                                     budChangeYear1 = g.Sum(x => x.budChangeYear1),
                                     budChangeYear2 = g.Sum(x => x.budChangeYear2),
                                     budChangeYear3 = g.Sum(x => x.budChangeYear3),
                                     budChangeYear4 = g.Sum(x => x.budChangeYear4),
                                     budChangeYearTotal = g.Sum(x => x.budChangeYearTotal),
                                     investmentPhase = g.Key.investmentPhase,
                                     oe_flag = g.Key.oe_flag,
                                     qualityStatusDesc = g.Key.qualityStatusDesc,
                                     finStatusDesc = g.Key.finStatusDesc,
                                     accountingAmountDeviation = g.Sum(x => x.accountingAmountDeviation),
                                     costestimate_p50 = g.Sum(x => x.costestimate_p50),
                                     adjustedbudChangeYear1 = g.Sum(x => x.adjustedbudChangeYear1),
                                     adjustedbudChangeYear2 = g.Sum(x => x.adjustedbudChangeYear2),
                                     adjustedbudChangeYear3 = g.Sum(x => x.adjustedbudChangeYear3),
                                     adjustedbudChangeYear4 = g.Sum(x => x.adjustedbudChangeYear4),
                                     adjustedbudChangeTotal = g.Sum(x => x.adjustedbudChangeTotal)
                                 }).ToList();

            if (sumSet != null) groupedData.Add(sumSet);
            return groupedData;
        }

    }
}