#pragma warning disable CS8625

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Extensions.DependencyInjection;

namespace Framsikt.DocExportWorker
{
    public class PublishManager
    {
        private readonly IUtility _utility;
        private readonly INotification _notification;
        private readonly IAzureBlobHelper _blobHelper;
        private readonly ISearch _search;

        public PublishManager(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();
            _blobHelper = container.GetRequiredService<IAzureBlobHelper>();
            _search = container.GetRequiredService<ISearch>();
        }

        public async Task DeleteProdPublishAsync(string publishPath, string shortName, string userId, int tenantId, BlobContainers container)
        {
            bool errorsOccurred = false;
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            TenantData tenantDetails = await _utility.GetTenantDataAsync(userId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userId, "Notifications");

            try
            {
                await dbContext.SaveChangesAsync();

                await _blobHelper.DeleteFolderAsync(StorageAccount.PublishProd, container, publishPath, true);

                string prodIndexName = AppConfiguration.GetConfigurationSetting("azurePublishProdIndexName");

                //Delete the objects in the search index
                //Prod search indexes have the same name as the publish id
                if (container == BlobContainers.BmContent)
                {
                    _search.DeleteFromIndex(prodIndexName, Target.Production, shortName,
                        tenantDetails.publish_Id, PublishTreeType.BudgetManagement);
                }
                else if (container == BlobContainers.PsContent)
                {
                    // index not configured yet for Political Simulation
                }
                else if (container == BlobContainers.PlanContent)
                {
                    _search.DeleteFromIndex(prodIndexName, Target.Production, shortName,
                        tenantDetails.publish_Id, PublishTreeType.PlanModule);
                }
                else
                {
                    _search.DeleteFromIndex(prodIndexName, Target.Production, shortName,
                        tenantDetails.publish_Id, PublishTreeType.MonthlyReport);
                }
                errorsOccurred = false;
            }
            catch
            {
                errorsOccurred = true;
            }
            Guid notId = Guid.NewGuid();
            string notificationText = errorsOccurred ? notificationStrings["ntf_mr_del_prod_website_with_errors"].LangText
                    : notificationStrings["ntf_mr_del_prod_website_completed"].LangText;

            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationText, null, 24);
        }

        public async Task DeleteStagPublishAsync(string publishPath, string shortName, string userId, int tenantId, BlobContainers container)
        {
            bool errorsOccurred = false;
            TenantData tenantDetails = _utility.GetTenantData(userId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userId, "Notifications");

            try
            {
                await _blobHelper.DeleteFolderAsync(StorageAccount.PublishStage, container, publishPath, true);
                //Delete the objects in the search index
                //Prod search indexes have the same name as the publish id
                string stgIndexName = AppConfiguration.GetConfigurationSetting("azurePublishStageIndexName");
                if (container == BlobContainers.BmContent)
                {
                    _search.DeleteFromIndex(stgIndexName, Target.Staging, shortName,
                        tenantDetails.publish_Id, PublishTreeType.BudgetManagement);
                }
                else if (container == BlobContainers.PsContent)
                {
                    // index not configured yet for Political Simulation
                }
                else if (container == BlobContainers.PlanContent)
                {
                    _search.DeleteFromIndex(stgIndexName, Target.Staging, shortName,
                        tenantDetails.publish_Id, PublishTreeType.PlanModule);
                }
                else
                {
                    _search.DeleteFromIndex(stgIndexName, Target.Staging, shortName,
                        tenantDetails.publish_Id, PublishTreeType.MonthlyReport);
                }
                errorsOccurred = false;
            }
            catch (Exception)
            {
                errorsOccurred = true;
            }
            Guid notId = Guid.NewGuid();
            string notificationText = errorsOccurred ? notificationStrings["ntf_mr_del_stag_website_with_errors"].LangText
                    : notificationStrings["ntf_mr_del_stag_website_completed"].LangText;

            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationText, null, 24);
        }

        public async Task DeleteProdPublishMultiplePathsAsync(List<string> publishPath, string shortName, string userId, int tenantId, BlobContainers container)
        {
            bool errorsOccurred = false;
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userId, "Notifications");

            try
            {
                await dbContext.SaveChangesAsync();

                foreach (var path in publishPath)
                {
                    await _blobHelper.DeleteFolderAsync(StorageAccount.PublishProd, container, path, true);
                }
                errorsOccurred = false;
            }
            catch
            {
                errorsOccurred = true;
            }
            Guid notId = Guid.NewGuid();
            string notificationText = errorsOccurred ? notificationStrings["ntf_mr_del_prod_website_with_errors"].LangText
                    : notificationStrings["ntf_mr_del_prod_website_completed"].LangText;

            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationText, null, 24);
        }

        public async Task DeleteStagPublishMultiplePathsAsync(List<string> publishPath, string shortName, string userId, int tenantId, BlobContainers container)
        {
            bool errorsOccurred = false;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userId, "Notifications");

            try
            {
                foreach (var path in publishPath)
                {
                    await _blobHelper.DeleteFolderAsync(StorageAccount.PublishStage, container, path, true);
                }
                errorsOccurred = false;
            }
            catch
            {
                errorsOccurred = true;
            }
            Guid notId = Guid.NewGuid();
            string notificationText = errorsOccurred ? notificationStrings["ntf_mr_del_stag_website_with_errors"].LangText
                    : notificationStrings["ntf_mr_del_stag_website_completed"].LangText;

            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationText, null, 24);
        }
    }
}