#pragma warning disable CS8600
#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Framsikt.DocExportWorker
{
    public class DocExDbContextManager : IDbContextManager, IDisposable
    {
        private readonly List<TenantDBContext> _parallelContexts;
        private readonly IAppDataCache _appDataCache;

        private AuthenticationDBContext? _authDbContext;
        private HelpCenterDBContext? _helpCenterDbContext;
        private AuditLogDBContext? _auditLogDbContext;
        private DataWareHouseDbContext? _dataWareHouseDbContext;
        private IntegrationDBContext? _integrationDbContext; 
        private TenantDBContext? _tenantDbContext;
        private readonly int _tenantId;
        private readonly string _userId;
        private readonly int _clientId;
        private StatDbContext? _statDbContext;
        public DocExDbContextManager(IAppDataCache cache, string userId, int tenantId, int clientId)
        {
            _parallelContexts = new List<TenantDBContext>();
            _tenantId = tenantId;
            _appDataCache = cache;
            _userId = userId;
            _clientId = clientId;
        }

        public TenantDBContext GetTenantDbContext()
        {
            var dbContext = GetTenantDbContextAsync().GetAwaiter().GetResult();
            return dbContext;
        }

        public async Task<TenantDBContext> GetTenantDbContextAsync()
        {
            if (_tenantDbContext == null)
            {
                _tenantDbContext = await CreateTenantDbContextAsync(_clientId, _tenantId);
            }
            return _tenantDbContext;
        }

        public TenantDBContext GetTenantDbContextForParallelRead()
        {
            var dbContext = GetTenantDbContextForParallelReadAsync().GetAwaiter().GetResult();
            return dbContext;
        }

        public async Task<TenantDBContext> GetTenantDbContextForParallelReadAsync()
        {
            TenantDBContext dbContext = await CreateTenantDbContextAsync(_clientId, _tenantId);
            _parallelContexts.Add(dbContext);
            return dbContext;
        }

        public SqlConnection GetAuthDbConnection()
        {
            string connectionString = AppConfiguration.GetConfigurationSetting("FramsiktDBConnectionString");
            SqlConnection cn = new SqlConnection(connectionString);
            cn.Open();
            return cn;
        }

        public SqlConnection GetDataWarehouseConnection()
        {
            string connectionString = AppConfiguration.GetConfigurationSetting("datawarehouseConnectionString");
            SqlConnection cn = new SqlConnection(connectionString);
            cn.Open();
            return cn;
        }

        public SqlConnection GetTenantDbConnection()
        {
            var connection = GetTenantDbConnectionAsync().GetAwaiter().GetResult();
            return connection;
        }

        public async Task<SqlConnection> GetTenantDbConnectionAsync()
        {
            string connectionString = await GetConnectionStringAsync();
            SqlConnection cn = new SqlConnection(connectionString);
            await cn.OpenAsync();
            return cn;
        }

        public SqlConnection GetTenantReplicaDbConnection()
        {
            string connectionString = GetConnectionString();
            if (!connectionString.EndsWith(';'))
            {
                connectionString += ";";
            }
            connectionString += "ApplicationIntent=ReadOnly;";
            SqlConnection cn = new SqlConnection(connectionString);
            cn.Open();
            return cn;
        }

        private string GetConnectionString()
        {
            var result = GetConnectionStringAsync().GetAwaiter().GetResult();
            return result;
        }

        private async Task<string> GetConnectionStringAsync()
        {
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            IAppDataCache cache = (IAppDataCache)_appDataCache;
            AuthenticationDBContext authenticationDbContext = GetAuthenticationContext();
            string cacheKey = $"connection_str";
            string connString = await cache.GetStringForTenantAsync(_clientId, _tenantId, cacheKey);

            if (!string.IsNullOrEmpty(connString))
            {
                return connString;
            }

            client_config clientConfig =
                await authenticationDbContext.client_config.FirstOrDefaultAsync(x => x.client_id == _clientId);

            if (clientConfig != null)
            {
                connString = clientConfig.connection_string;
            }

            connString ??= string.Empty;
            await cache.SetStringForTenantAsync(_clientId, _tenantId, "connection_str", connString, cacheTimeOut);
            return connString;
        }

        public void Dispose()
        {
            _authDbContext?.Dispose();
            _tenantDbContext?.Dispose();
            _helpCenterDbContext?.Dispose();
            _auditLogDbContext?.Dispose();
            _dataWareHouseDbContext?.Dispose();
            _integrationDbContext?.Dispose();
            _statDbContext.Dispose(); 
            foreach (var dbContext in _parallelContexts)
            {
                dbContext?.Dispose();
            }
        }

        public AuditLogDBContext GetAuditLogContext()
        {
            if (_auditLogDbContext != null) return _auditLogDbContext;
            string connectionString = AppConfiguration.GetConfigurationSetting("AuditLogInfoDbConnectionString");
            var optionsBuilder = new DbContextOptionsBuilder<AuditLogDBContext>();
            optionsBuilder.UseSqlServer(connectionString, options => options.EnableRetryOnFailure());
            _auditLogDbContext = new AuditLogDBContext(optionsBuilder.Options);
            return _auditLogDbContext;
        }

        public AuthenticationDBContext GetAuthenticationContext()
        {
            if (_authDbContext != null) return _authDbContext;
            
            string connectionString = AppConfiguration.GetConfigurationSetting("FramsiktDBConnectionString");
            var optionsBuilder = new DbContextOptionsBuilder<AuthenticationDBContext>();
            optionsBuilder.UseSqlServer(connectionString, options => options.EnableRetryOnFailure());
            _authDbContext = new AuthenticationDBContext(optionsBuilder.Options);
            return _authDbContext;
        }

        public DataWareHouseDbContext GetDataWareHouseContext()
        {
            if (_dataWareHouseDbContext != null) return _dataWareHouseDbContext;
            string connectionString = AppConfiguration.GetConfigurationSetting("datawarehouseConnectionString");
            var optionsBuilder = new DbContextOptionsBuilder<DataWareHouseDbContext>();
            optionsBuilder.UseSqlServer(connectionString, options => options.EnableRetryOnFailure());
            _dataWareHouseDbContext = new DataWareHouseDbContext(optionsBuilder.Options);
            return _dataWareHouseDbContext;
        }

        public HelpCenterDBContext GetHelpCenterContext()
        {
            if (_helpCenterDbContext != null) return _helpCenterDbContext;
            string connectionString = AppConfiguration.GetConfigurationSetting("helpCenterDbConnectionString");
            var optionsBuilder = new DbContextOptionsBuilder<HelpCenterDBContext>();
            optionsBuilder.UseSqlServer(connectionString, options => options.EnableRetryOnFailure());
            _helpCenterDbContext = new HelpCenterDBContext(optionsBuilder.Options);
            return _helpCenterDbContext;
        }

        public IntegrationDBContext GetIntegrationContext()
        {
            if (_integrationDbContext != null) return _integrationDbContext;
            
            string connectionString = AppConfiguration.GetConfigurationSetting("integrationDbConnectionString");
            var optionsBuilder = new DbContextOptionsBuilder<IntegrationDBContext>();
            optionsBuilder.UseSqlServer(connectionString, options => options.EnableRetryOnFailure());
            _integrationDbContext = new IntegrationDBContext(optionsBuilder.Options);
            return _integrationDbContext;
        }
        private async Task<TenantDBContext> CreateTenantDbContextAsync(int clientId, int tenantId)
        {
            AuthenticationDBContext authDbContext = GetAuthenticationContext();
            string cacheKeyConnString = "cacheConnString";
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);

            string connString = await _appDataCache.GetStringForTenantAsync(clientId, tenantId,cacheKeyConnString);
            if (string.IsNullOrEmpty(connString))
            {
                client_config clientConfig = await authDbContext.client_config.FirstOrDefaultAsync(x => x.client_id == clientId);

                if (clientConfig == null)
                {
                    throw new InvalidDataException($"Connection config does not exist for client {clientId}");
                }
                if (string.IsNullOrEmpty(clientConfig.connection_string) || string.IsNullOrWhiteSpace(clientConfig.connection_string))
                {
                    throw new InvalidDataException($"Connection string is blank for client {clientId}");
                }

                connString = clientConfig.connection_string;
                await _appDataCache.SetStringForTenantAsync(clientId, tenantId,cacheKeyConnString, connString, cacheTimeOut);
            }

            int tenantTypeId = await GetTenantTypeIdAsync(clientId, tenantId);
            var optionsBuilder = new DbContextOptionsBuilder<TenantDBContext>();
            optionsBuilder.UseSqlServer(connString, options => options.EnableRetryOnFailure());
            TenantDBContext tenantDbContext = new TenantDBContext(optionsBuilder.Options, tenantTypeId);
            return tenantDbContext;
        }

        private async Task<int> GetTenantTypeIdAsync(int clientId, int tenantId)
        {
            AuthenticationDBContext authDbContext = GetAuthenticationContext();
            int tenantTypeId = 0;

            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            string cacheKeyTenantTypeId = $"cacheTenantTypeId-{clientId}-{tenantId}";

            string? strcacheKeyTenantTypeId = await _appDataCache.GetStringForTenantAsync(clientId, tenantId, cacheKeyTenantTypeId);
            if (string.IsNullOrEmpty(strcacheKeyTenantTypeId))
            {
                vw_gco_tenants_all vgt =
                    await authDbContext.vw_gco_tenants_all.FirstOrDefaultAsync(x =>
                        x.pk_id == tenantId && x.client_id == clientId);
                if (vgt == null || vgt.tenant_type_id == null)
                {
                    throw new InvalidDataException($"Tenant Type Id does not exist for tenant {tenantId}");
                }
                else
                {
                    tenantTypeId = vgt.tenant_type_id.Value;
                }

                await _appDataCache.SetStringForTenantAsync(clientId, tenantId, cacheKeyTenantTypeId, tenantTypeId.ToString(), cacheTimeOut);
            }
            else
            {
                tenantTypeId = int.Parse(strcacheKeyTenantTypeId);
            }

            return tenantTypeId;
        }

        public StatDbContext GetStatDBContext()
        {
            if (_statDbContext != null) return _statDbContext;

            string connectionString = AppConfiguration.GetConfigurationSetting("LogDbConnectionString");
            var optionsBuilder = new DbContextOptionsBuilder<StatDbContext>();
            optionsBuilder.UseSqlServer(connectionString, options => options.EnableRetryOnFailure());

            _statDbContext = new StatDbContext(optionsBuilder.Options);
            return _statDbContext;
        }
    }
}