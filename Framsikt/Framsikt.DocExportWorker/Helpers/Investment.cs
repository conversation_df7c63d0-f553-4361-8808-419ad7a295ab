#pragma warning disable CS8618

using System;

namespace Framsikt.DocExportWorker.Helpers
{
    internal class Investment
    {
        public string OrgName { get; set; }
        public string InvestmentName { get; set; }
        public DateTime FinishDate { get; set; }
        public string ProgramCodeDesc { get; set; }
        public string ServiceName { get; set; }
        public string FunctionCode { get; set; }
        public string FunctionName { get; set; }
        public string InvStatus { get; set; }
        public string Description { get; set; }
        public string BudgetPhase { get; set; }
        public decimal Year1Amount { get; set; }
        public decimal Year2Amount { get; set; }
        public decimal Year3Amount { get; set; }
        public decimal Year4Amount { get; set; }
        public decimal TotalAmount { get; set; }

    }
}
