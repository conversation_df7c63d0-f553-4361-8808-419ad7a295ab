#pragma warning disable CS8618
using Framsikt.BL;
using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting;
using Framsikt.BL.Reporting.Core;
using Framsikt.BL.Repository;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Framsikt.BL.Helpers.clsConstants;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MpMrSyncReportTransfer : IMessageProcessor
    {
        private IUtility _utility;
        private INotification _notification;
        private IBudgetProposalSyncDocument _budgetProposalSyncDoc;
        private IMRFetchSync mrFetchSync;
        private IMRStartReportingSync MRStartreport;
        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();
            _budgetProposalSyncDoc = container.GetRequiredService<IBudgetProposalSyncDocument>();
            mrFetchSync = container.GetRequiredService<IMRFetchSync>();
            MRStartreport = container.GetRequiredService<IMRStartReportingSync>();
            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.userId;
            int budgetYear = jsonReq.budgetYear;
            int subTenantId = jsonReq.subTenantId;
            int forecastPeriod = jsonReq.forecastPeriod;
            string processStageFromQueue=jsonReq.processStage;

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData subTenantDetails= await _utility.GetTenantDataAsync(subTenantId);
            ActionTransferQueueHeler connectedElement = new ActionTransferQueueHeler();

            BudgetProcessStage processStage = (BudgetProcessStage)Enum.Parse(typeof(BudgetProcessStage), processStageFromQueue);

            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "MonthlyReportSync");

            string notificationMsg = string.Empty;

            switch (processStage)
            { 
                case BudgetProcessStage.MR_Sync_Tree: // delegation of tree template to lower level
                    DataSyncInputHelper dataTreeInput = new DataSyncInputHelper
                    {
                        budgetYear = budgetYear,
                        bugdetPhaseId = Guid.Empty,
                        orgId = jsonReq.orgId,
                        treeType = PublishTreeType.MRSyncTree,
                        forecastPeriod = forecastPeriod
                    };
                    // delegation of BPP sync tree template to lower level
                    bool isTransfferred = await _budgetProposalSyncDoc.DelegateMasterTemplateToOrgIds(userId, dataTreeInput);
                    notificationMsg = isTransfferred ? notificationStrings["MR_template_delegation_success"].LangText : notificationStrings["MR_template_delegation_failed"].LangText;
                    break;
                case BudgetProcessStage.MR_Start_Report:
                    var isValid = await MRStartreport.TransferMasterTemplateToSub(userDetails, forecastPeriod, subTenantId, PublishTreeType.MRSyncTree);
                    Guid jobId = jsonReq.jobId;
                    bool startReportInMain = jsonReq.startReportInMain;
                    if (isValid && startReportInMain)
                    {
                        isValid = await MRStartreport.StartReportingMainTenant(userDetails, userId, forecastPeriod, subTenantId, jobId);
                    }
                    if (isValid)
                    {
                        isValid =  await MRStartreport.StartReporting(userDetails, forecastPeriod, subTenantId, jobId);
                    }
                    notificationMsg = isValid ? $"{notificationStrings["MR_Sync_StartReport_success"].LangText} - {subTenantDetails.tenant_name} " : $" {notificationStrings["MR_Sync_StartReport_Error"].LangText} - {subTenantDetails.tenant_name}";
                    if (startReportInMain)
                    {
                        string orgId = jsonReq.orgId;
                        int orgLevel = jsonReq.orgLevel;
                        await MRStartreport.DeleteMRSyncTransferMainTenantFlag(userDetails, orgId, orgLevel,
                            budgetYear, forecastPeriod);
                    }
                    break;
                case BudgetProcessStage.MR_Report_Fetch:
                   MRSyncFetchtHelper fetchInput = new MRSyncFetchtHelper()
                    {
                        sourceTenantId = jsonReq.subTenantId,
                        targetTenantId= userDetails.tenant_id,
                        processStage= BudgetProcessStage.MR_Report_Fetch,
                        forecastPeriod= forecastPeriod, 
                        budgetYear= budgetYear,                       
                        userIdName= userId,
                        userId= userDetails.pk_id,
                        jobId= jsonReq.jobId,
                        orgId = jsonReq.orgId,
                        orgLevel = jsonReq.orgLevel,

                    };
                    var transferDone = await mrFetchSync.FetchMRDataASync(fetchInput);

                    notificationMsg = transferDone ? notificationStrings["MR_sync_fetch_success"].LangText : notificationStrings["MR_sync_fetch_fail"].LangText;
                    break;

            }
            
            Guid notId = Guid.NewGuid();
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, userDetails.tenant_id, notificationMsg, redirectUrl, 24);
        }
        public string GetMessageType()
        {
            return "MonthlyReportSync";
        }
        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            int tenantId = jsonReq.TenantId;
            Guid notId = Guid.NewGuid();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            bool startReportInMain = jsonReq.startReportInMain;
            if (startReportInMain)
            {
                string orgId = jsonReq.orgId;
                int orgLevel = jsonReq.orgLevel;
                int forecastPeriod = jsonReq.forecastPeriod;
                int budgetYear = jsonReq.budgetYear;
                await MRStartreport.DeleteMRSyncTransferMainTenantFlag(userDetails, orgId, orgLevel,
                    budgetYear, forecastPeriod);
            }
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "MonthlyReportSync");
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["MR_Start_Report_Failed"].LangText, redirectUrl, 24);
        }
    }
}
