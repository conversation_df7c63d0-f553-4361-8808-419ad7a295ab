#pragma warning disable CS8604
#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8625
using Framsikt.BL;
using Framsikt.BL.Core.Integrations;
using Framsikt.BL.Helpers;
using Framsikt.BL.Integrations;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class BusinessPlanReportingExport : IMessageProcessor
    {
        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            
            dynamic jsonReq = JToken.Parse(msg);
            PublishType requestType = (PublishType)Enum.Parse(typeof(PublishType), jsonReq.RequestType.Value);
            switch (requestType)
            {
                case PublishType.CreateDocument :
                    await ProcessBusinessPlanDocumentExport(msg, container);
                    break;

                case PublishType.ArchiveDocumentExt:
                    await ProcessBusinessPlanArchiveExt(msg, container);
                    break;
            }
        }

        public Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            return Task.CompletedTask;
        }

        public string GetMessageType()
        {
            return "BusinessPlanExport";
        }

        private async Task ProcessBusinessPlanDocumentExport(string msg, IServiceProvider container)
        {
            String request = msg;

            if (request.Length > 0)
            {
                BusinessPlanExportHelper expHelper = new BusinessPlanExportHelper(container);
                IUtility utility = container.GetRequiredService<IUtility>();
                INotification notification = container.GetRequiredService<INotification>();
                IAzureBlobHelper blobHelper = container.GetRequiredService<IAzureBlobHelper>();
                IKostraData kostra = container.GetRequiredService<IKostraData>();
                IAppIntegrations appInteg = container.GetRequiredService<IAppIntegrations>();

                dynamic jsonReq = JToken.Parse(request);
                string userId = jsonReq.UserId;
                int tenantId = jsonReq.TenantId;
                int budgetYear = jsonReq.BudgetYear;
                string exportTreePath = jsonReq.ExportTreePath;
                string blobName = tenantId + "-bplanexport-" + userId + "-" + DateTime.UtcNow.ToString("yyyyMMddHHmmss") + ".docx";
                string blobPath = tenantId + "-" + userId + "/" + blobName;
                string expParamsFromAzure = await blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTree, exportTreePath);
                dynamic jsonExport = JToken.Parse(expParamsFromAzure);
                string expParams = JsonConvert.SerializeObject(jsonExport.ExportParameters);

                var exp = JsonConvert.DeserializeObject<BusinessPlanMessageDataHelper>(expParams);

                using (MemoryStream ms = expHelper.CreateDocument(userId, exp, budgetYear))
                {
                    ms.Position = 0;
                    blobHelper.UploadFromStream(StorageAccount.AppStorage, BlobContainers.BusinessPlan, blobPath, ms);
                }

                //Add a row to the doc export table
                Guid? docId = kostra.InsertDocExportRow(userId, tenantId, blobPath);

                if (docId != null)
                {
                    Guid notId = Guid.NewGuid();

                    //Success, Add notification
                    Uri getDocUrl = new Uri("../api/BusinessPlanTemplateApi/ExportDocument?docId=" + docId.ToString() + "&notificationId=" + notId, UriKind.Relative);

                    UserData userDetails = await utility.GetUserDetailsAsync(userId);

                    Dictionary<String, clsLanguageString> notificationStrings = await utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Notifications");

                    await notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ntf_export_completed"].LangText, getDocUrl, 24);
                }
                else
                {
                    //Failure, TBD
                }
                await blobHelper.DeleteBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTree, exportTreePath);
            }
        }
        private async Task ProcessBusinessPlanArchiveExt(string msg, IServiceProvider container)
        {

            string request = msg;

            if (request.Length > 0)
            {
                BusinessPlanExportHelper expHelper = new BusinessPlanExportHelper(container);
                IUtility utility = container.GetRequiredService<IUtility>();
                INotification notification = container.GetRequiredService<INotification>();
                IAzureBlobHelper blobHelper = container.GetRequiredService<IAzureBlobHelper>();
                IKostraData kostra = container.GetRequiredService<IKostraData>();
                IAppIntegrations appInteg = container.GetRequiredService<IAppIntegrations>();

                dynamic jsonReq = JToken.Parse(request);
                string userId = jsonReq.UserId;
                int tenantId = jsonReq.TenantId;
                int budgetYear = jsonReq.BudgetYear;
                string exportTreePath = jsonReq.ExportTreePath;
                string expParamsFromAzure = await blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTree, exportTreePath);

                var exp = JsonConvert.DeserializeObject<BusinessPlanMessageDataHelper>(expParamsFromAzure);
                BusplanArchive extArchiveReq = JsonConvert.DeserializeObject<BusplanArchive>(expParamsFromAzure);

                var archiveInteg = appInteg.GetIntegration(userId, IntegrationType.BusplanDocumentArchive);

                

                string path = $"archive/{tenantId.ToString()}/{DateTime.UtcNow.ToString("yyyyMMddHHmmss")}-{Guid.NewGuid().ToString()}.docx";

                using (MemoryStream ms = expHelper.CreateDocument(userId, exp, budgetYear))
                {
                    ms.Position = 0;
                    blobHelper.UploadFromStream(StorageAccount.AppStorage, BlobContainers.BusinessPlan, path, ms);

                    if (archiveInteg.SystemType == SystemType.NoArk5)
                    {
                        //Acos integration
                        var noarkClient = container.GetRequiredService<INoark5Integ>();

                        //Create Journal Post
                        var journalPost = await noarkClient.CreateJournalPost(userId, extArchiveReq.CaseYear.ToString(), extArchiveReq.CaseNum.ToString(),
                            IntegrationType.BusplanDocumentArchive, extArchiveReq.UserParameters);
                        //Create New document
                        await noarkClient.CreateNewDocument(userId, IntegrationType.BusplanDocumentArchive,
                            extArchiveReq.UserParameters, journalPost.systemID, ms);
                        //Upate document status
                        await noarkClient.UpdateDocumentStatus(userId, IntegrationType.BusplanDocumentArchive,
                            extArchiveReq.UserParameters, journalPost.systemID);
                    }
                    else if (archiveInteg.SystemType == SystemType.ElementsSikri)
                    {
                        //Elements Sikri integration
                        var elementsClient = container.GetRequiredService<IElementsSikri>();

                        //Create Journal Post
                        var journalPost = await elementsClient.CreateJournalPostAsync(userId, Convert.ToString(extArchiveReq.CaseYear), Convert.ToString(extArchiveReq.CaseNum),
                        IntegrationType.BusplanDocumentArchive, extArchiveReq.UserParameters);
                        //Create New document
                        await elementsClient.CreateNewDocumentAsync(userId, IntegrationType.BusplanDocumentArchive,
                            extArchiveReq.UserParameters, <EMAIL>, ms);
                    }
                    else if (archiveInteg.SystemType == SystemType.TietoEvry)
                    {
                        //TietoEvry integration
                        var tietoEvryClient = container.GetRequiredService<ITietoEvry>();

                        //Create Journal Post
                        var journalPost = await tietoEvryClient.CreateJournalPostAsync(userId, Convert.ToString(extArchiveReq.CaseYear), Convert.ToString(extArchiveReq.CaseNum),
                        IntegrationType.BusplanDocumentArchive, extArchiveReq.UserParameters);
                        //Create New document
                        await tietoEvryClient.CreateNewDocumentAsync(userId, IntegrationType.BusplanDocumentArchive,
                            extArchiveReq.UserParameters, <EMAIL>, ms);
                    }
                }

                //Add a row to the doc export table
                Guid notId = Guid.NewGuid();
                UserData userDetails = await utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> notificationStrings = await utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Notifications");
                await notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ntf_publish_completed"].LangText, null, 24);
                await blobHelper.DeleteBlobAsync(StorageAccount.AppStorage, BlobContainers.ExportTreeData, exportTreePath);
            }
        }
    }
}
