using Framsikt.BL;
using Framsikt.BL.Helpers;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    //This class is reponsible for implementing the logic to process the content of a queue message
    //The queue handling has been centralized in the QueueHandler class

    public class MpNotification : IMessageProcessor
    {
        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            var pBudMgnt = container.GetRequiredService<IBudgetManagement>();
            var utility = container.GetRequiredService<IUtility>();

            String request = msg;
            if (request.Length > 0)
            {
                dynamic jsonReq = JToken.Parse(request);
                int sectionId = jsonReq.SectionId;
                string sectionName = jsonReq.SectionName;
                string userId = jsonReq.UserId;
                int budgetYear = await utility.GetActiveBudgetYearAsync(jsonReq.UserId, clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR.ToString());
                await pBudMgnt.NotificationAsync(sectionId, sectionName, userId, budgetYear);
            }
        }

        public Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            return Task.CompletedTask;
        }

        public string GetMessageType()
        {
            return "Notification";
        }
    }
}