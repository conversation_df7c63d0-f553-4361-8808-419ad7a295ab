#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MPMRActionImport : IMessageProcessor
    {
        private IMRActionImport _mrActionImport;
        private IUtility _utility;
        private INotification _notification;

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            _mrActionImport = container.GetRequiredService<IMRActionImport>();
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();

            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.userId;
            int jobId = jsonReq.JobId;
            int tenantId = jsonReq.tenantId;
            int forecastPeriod = jsonReq.forecastPeriod;
            int budgetYear = jsonReq.budgetYear;
            int valueFactor = jsonReq.ValueFactor;
            decimal adjustment = jsonReq.Adjustment;
            List<KeyValueData> tagsData = JsonConvert.DeserializeObject<List<KeyValueData>>(((JValue)((JToken)jsonReq.tagsData).Parent.First).Value.ToString());
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "ActionImport");

            await _mrActionImport.ImportMrActionsFromStaging(userId, budgetYear, forecastPeriod, tenantId, valueFactor, adjustment, jobId, tagsData);

            Guid notId = Guid.NewGuid();
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ai_import_success"].LangText, redirectUrl, 24);
        }

        public string GetMessageType()
        {
            return this.GetType().Name;
        }

        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            int tenantId = jsonReq.TenantId;
            Guid notId = Guid.NewGuid();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "ActionImport");
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ai_import_failed"].LangText, redirectUrl, 24);
        }
    }
}