#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MpMRInvestmentsImport : IMessageProcessor
    {
        private IMRInvestmentImport _mrInvestmentImport;
        private IUtility _utility;
        private INotification _notification;

        public MpMRInvestmentsImport()
        {
        }

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            _mrInvestmentImport = container.GetRequiredService<IMRInvestmentImport>();
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();

            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            string tenantId = jsonReq.TenantId;
            string jobId = jsonReq.JobId;
            string forecastPeriod = jsonReq.ForeCastPeriod;
            string invOrgData = jsonReq.InvOrgData;
            string orgId = jsonReq.OrgId;

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "AccountingImport");

            _mrInvestmentImport.ImporMRInvestmentsDataFromStaging(userId, int.Parse(forecastPeriod), int.Parse(tenantId), int.Parse(jobId), invOrgData, orgId);

            Guid notId = Guid.NewGuid();
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, int.Parse(tenantId), notificationStrings["MRInvestmentImport_success"].LangText, redirectUrl, 24);
        }

        public string GetMessageType()
        {
            return "MRInvestmentsImport";
        }

        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            int tenantId = jsonReq.TenantId;
            Guid notId = Guid.NewGuid();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "AccountingImport");
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["MRInvestmentImport_failed"].LangText, redirectUrl, 24);
        }
    }
}