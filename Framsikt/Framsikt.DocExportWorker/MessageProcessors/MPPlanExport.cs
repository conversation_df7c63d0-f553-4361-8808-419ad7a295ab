#pragma warning disable CS8625

#pragma warning disable CS8602
#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.BL.Plan.PublishHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker.BackgroundProcessors;
using Hangfire;
using Hangfire.Storage;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MPPlanExport : IMessageProcessor
    {
        private IUtility _utility;
        private INotification _notification;
        private IAzureBlobHelper _blobHelper;
        private IKostraData _kostraData;

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            PlanDocumentHelper documentHelper = new PlanDocumentHelper(container);
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();
            _blobHelper = container.GetRequiredService<IAzureBlobHelper>();
            _kostraData = container.GetRequiredService<IKostraData>();

            var planPublishRequest = JsonConvert.DeserializeObject<PlanPublishQueueMessageHelper>(msg);
            UserData userDetails = await _utility.GetUserDetailsAsync(planPublishRequest.UserId);
            string status;
            if (planPublishRequest != null)
            {
                switch (planPublishRequest.RequestType)
                {
                    case PublishType.CreateDocument:
                        status = await GenerateDocument(documentHelper, planPublishRequest, userDetails);
                        break;

                    default:
                        break;
                }
            }
        }

        private async Task<string> GenerateDocument(PlanDocumentHelper documentHelper, PlanPublishQueueMessageHelper planPublishRequest, UserData userDetails)
        {
            string userId = planPublishRequest.UserId;
            int tenantId = planPublishRequest.TenantId;
            string blobName = tenantId + "-" + userId + "-" + DateTime.UtcNow.ToString("yyyyMMddHHmmss") + ".docx";
            string blobPath = tenantId + "-" + userId + "/" + blobName;

            using (MemoryStream ms = await documentHelper.CreateDocument(planPublishRequest))
            {
                ms.Position = 0;
                _blobHelper.UploadFromStream(StorageAccount.AppStorage, BlobContainers.PlanExportBlobContainer, blobPath, ms);
            }

            //Add a row to the doc export table
            Guid? docId = _kostraData.InsertDocExportRow(userId, tenantId, blobPath);
            if (docId != null)
            {
                Guid notId = Guid.NewGuid();
                Uri getDocUrl = new Uri("../api/PlanPublishApi/ExportPlanDocument?docId=" + docId.ToString() + "&notificationId=" + notId, UriKind.Relative);
                Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Notifications");
                await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ntf_planexport_completed"].LangText, getDocUrl, 24);
                await _blobHelper.DeleteBlobAsync(StorageAccount.AppStorage, BlobContainers.ExportTreeData, planPublishRequest.RequestTreePath);
            }
            return "Success";
        }

        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            var jobId = HangfireExtensions.GetHangfireQueueDetails(messageId, QueueName.planexportrequestqueue.ToString());
            IMonitoringApi monitoringApi = JobStorage.Current.GetMonitoringApi();
            var failedCount = monitoringApi.JobDetails(jobId).History.Count(x => x.StateName.ToLower() == "Failed".ToLower());
            if (failedCount == 10)
            {
                var planPublishRequest = JsonConvert.DeserializeObject<PlanPublishQueueMessageHelper>(msg);
                string userId = planPublishRequest.UserId;
                int tenantId = planPublishRequest.TenantId;
                Guid notId = Guid.NewGuid();

                _utility = container.GetRequiredService<IUtility>();
                _notification = container.GetRequiredService<INotification>();
                _blobHelper = container.GetRequiredService<IAzureBlobHelper>();

                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Notifications");

                await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ntf_planexport_failed"].LangText, null, 24);
                await _blobHelper.DeleteBlobAsync(StorageAccount.AppStorage, BlobContainers.ExportTreeData, planPublishRequest.RequestTreePath);
            }
        }

        public string GetMessageType()
        {
            return "PlanExport";
        }
    }
}