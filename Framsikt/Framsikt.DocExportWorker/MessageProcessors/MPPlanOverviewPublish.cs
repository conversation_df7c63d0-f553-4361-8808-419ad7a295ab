#pragma warning disable CS8625

#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker.BackgroundProcessors;
using Hangfire;
using Hangfire.Storage;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MPPlanOverviewPublish : IMessageProcessor
    {
        private IUtility _utility;
        private INotification _notification;

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            PlanOverviewPublishHelper publishHelper = new PlanOverviewPublishHelper(container);
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();

            var request = JsonConvert.DeserializeObject<PlanOverviewPublishQueueMessageHelper>(msg);
            UserData userDetails = await _utility.GetUserDetailsAsync(request.UserId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, request.UserId, "Notifications");

            PublishStatus status = request.RequestType == PublishType.PublishWebsiteToStaging ? publishHelper.PublishWebsiteToStaging(request) : publishHelper.PublishWebsiteToProd(request);

            Guid notId = Guid.NewGuid();

            string strUrl = request.RequestType == PublishType.PublishWebsiteToStaging ? AppConfiguration.GetConfigurationSetting("publishplanStagWebSiteUrl")
                : AppConfiguration.GetConfigurationSetting("publishMrProdWebSiteUrl");
            bool urlConfigured = !string.IsNullOrEmpty(strUrl);

            if (urlConfigured)
            {
                strUrl = (strUrl + status.PublishUrl).ToLower();
                bool success = Uri.TryCreate(strUrl, UriKind.RelativeOrAbsolute, out var publishUrl);

                await _notification.CreateNotificationAsync(notId, request.UserId, request.TenantId,
                    status.ErrorsOccurred
                        ? $"{status.PublishName} {notificationStrings["ntf_plan_pub_website_with_errors"].LangText}".Trim()
                        : $"{status.PublishName} {notificationStrings["ntf_plan_pub_website_completed"].LangText}".Trim(), success ? publishUrl : null, 24);
            }
            else
            {
                await _notification.CreateNotificationAsync(notId, request.UserId, request.TenantId,
                    status.ErrorsOccurred
                        ? $"{status.PublishName} {notificationStrings["ntf_plan_pub_website_with_errors_no_url"].LangText}".Trim()
                        : $"{status.PublishName} {notificationStrings["ntf_plan_pub_website_completed_no_url"].LangText}".Trim(), null, 24);
            }
        }

        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            var jobId = HangfireExtensions.GetHangfireQueueDetails(messageId, QueueName.planoverviewpublishrequestqueue.ToString());
            IMonitoringApi monitoringApi = JobStorage.Current.GetMonitoringApi();
            var failedCount = monitoringApi.JobDetails(jobId).History.Count(x => x.StateName.ToLower() == "Failed".ToLower());
            if (failedCount == 10)
            {
                var request = JsonConvert.DeserializeObject<PlanOverviewPublishQueueMessageHelper>(msg);
                string userId = request.UserId;
                int tenantId = request.TenantId;
                Guid notId = Guid.NewGuid();
                _utility = container.GetRequiredService<IUtility>();
                _notification = container.GetRequiredService<INotification>();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Notifications");
                await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["ntf_pub_website_failed"].LangText, null, 24);
            }
        }

        public string GetMessageType()
        {
            return "PlanOverviewPublish";
        }
    }
}