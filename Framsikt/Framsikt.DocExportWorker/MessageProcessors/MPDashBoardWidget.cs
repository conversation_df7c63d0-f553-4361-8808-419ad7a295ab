#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.BL.Repository;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.Http.Headers;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MPDashBoardWidget : IMessageProcessor
    {
        private IDashBoardWidgets _dasBoadWidgetExport;
        private IUtility _utility;
        private INotification _notification;
        IKostraData kostra;

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            _dasBoadWidgetExport = container.GetRequiredService<IDashBoardWidgets>();
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();
            kostra = container.GetRequiredService<IKostraData>();
            AccountStmtExcelExportInput jsonReq = JsonConvert.DeserializeObject<AccountStmtExcelExportInput>(msg);// JToken.Parse(msg);          
            IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
            UserData userDetails = await _utility.GetUserDetailsAsync(jsonReq.userName);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, jsonReq.userName, "AccountStatementReport");

            
            try
            {

                DasboradWidgetExcelExportHelper expHelper = new DasboradWidgetExcelExportHelper(container);
                

                string filePath = jsonReq.isPerPeriodGrid ? "perPeriod" : "view_" + jsonReq.viewType;

                string path = jsonReq.tenantId + "-" + jsonReq.userName + "/" + jsonReq.tenantId + "-" + jsonReq.userName + "-" + jsonReq.forecastPeriod + "-" + filePath +"-" + DateTime.UtcNow.ToString("yyyyMMddHHmmss") + ".xlsx";
               
                Uri redirectUrl = new Uri(".", UriKind.Relative);
              
              //  await _notification.CreateNotificationAsync(notId, jsonReq.userName, jsonReq.tenantId, notificationStrings["AccStmt_ExcelExport_started"].LangText, redirectUrl, 24);


                using (MemoryStream ms = await expHelper.ExportToExcel(jsonReq, path))
                {
                    ms.Position = 0;
                  await  azureBlobHelper.UploadFromStreamAsync(StorageAccount.AppStorage, BlobContainers.DashBoardWidgetExport, path, ms);
                }

                Guid? docId = await kostra.InsertDocExportRowAsync(jsonReq.userName, jsonReq.tenantId, path);

                if (docId != null)
                {
                    Guid notId2 = Guid.NewGuid();
                    //Success, Add notification
                    Uri getDocUrl = new Uri("../api/DashBoardWidgets/DownloadAccStmExportExcel?docId=" + docId.ToString() + "&notificationId=" + notId2, UriKind.Relative);

                    //  Thread.Sleep(10000);

                    string notification = jsonReq.isPerPeriodGrid ? notificationStrings["AccStmt_period_ExcelExport_Finished"].LangText : notificationStrings["AccStmt_ExcelExport_Finished"].LangText;

                    await _notification.CreateNotificationAsync(notId2, jsonReq.userName, jsonReq.tenantId, notification, getDocUrl, 24);

                    await _dasBoadWidgetExport.UpdateExcelExportStatusToJobsTable(jsonReq,"CompletedSuccessfully","",3);

                }
                else
                {
                    //Failure, TBD
                }

            }
            catch (Exception ex)
            {
                Uri redirectUrl = new Uri(".", UriKind.Relative);
                Guid notId3 = Guid.NewGuid();
                await _notification.CreateNotificationAsync(notId3, jsonReq.userName, jsonReq.tenantId, notificationStrings["AccStmt_ExcelExport_failed"].LangText, redirectUrl, 24);
                await _dasBoadWidgetExport.UpdateExcelExportStatusToJobsTable(jsonReq, "failed", ex.Message, 0);
            }
        }

        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();

            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            int tenantId = jsonReq.TenantId;
            Guid notId = Guid.NewGuid();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "AccountStatementReport");
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["AccStmt_ExcelExport_failed"].LangText, redirectUrl, 24);
        }

        public string GetMessageType()
        {
            return "AccountStatmentExcelExport";
        }
    }
}