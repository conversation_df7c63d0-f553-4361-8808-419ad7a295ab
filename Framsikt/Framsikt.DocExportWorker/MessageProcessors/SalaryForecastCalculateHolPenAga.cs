#pragma warning disable CS8600
#pragma warning disable CS8602

using Framsikt.BL;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class SalaryForecastCalculateHolPenAga : IMessageProcessor
    {
        public string GetMessageType()
        {
            return "SalaryForecastCalculateHolPenAga";
        }

        public Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            return Task.CompletedTask;
        }

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            IStaffPlanning staffPlanning = container.GetRequiredService<IStaffPlanning>();
            INotification notification = container.GetRequiredService<INotification>();

            var request = msg;
            if (request.Length > 0)
            {
                await ProcessCalculateAgaPensionHolidayAsync(msg, staffPlanning, notification);
            }
        }

        private async Task ProcessCalculateAgaPensionHolidayAsync(string msg, IStaffPlanning staffPlanning, INotification notification)
        {
            var request = msg;
            if (request.Length > 0)
            {
                dynamic jsonReq = JsonConvert.DeserializeObject(request);
                jsonReq = jsonReq.request;
                string userId = jsonReq.UserId;
                int tenantId = jsonReq.TenantId;
                string pageId = jsonReq.PageId;
                int selectedBudgetYear = jsonReq.SelectedBudgetYear;
                int selectedPeriod = jsonReq.SelectedPeriod;

                int forecastPeriod = Convert.ToInt32(selectedBudgetYear.ToString() + (selectedPeriod.ToString().Length > 1 ? selectedPeriod.ToString() : "0" + selectedPeriod.ToString()));

                string statusMsg = string.Empty;

                statusMsg = "lønnsprognose - " +
                    staffPlanning.CalculateSalaryForecastAgaPensionHolidayOnTenantLevel(userId, forecastPeriod, selectedBudgetYear);

                Guid notId = Guid.NewGuid();
                Uri getReturnedUrl = new Uri("../StaffPlanning/Overview", UriKind.Relative);
                await notification.CreateNotificationAsync(notId, userId, tenantId, statusMsg, getReturnedUrl, 24, pageId, 2);
            }
        }
    }
}