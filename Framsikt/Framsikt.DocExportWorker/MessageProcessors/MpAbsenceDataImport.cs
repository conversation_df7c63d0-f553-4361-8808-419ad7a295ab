#pragma warning disable CS8618

using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace Framsikt.DocExportWorker.MessageProcessors
{
    public class MpAbsenceDataImport : IMessageProcessor
    {
        private IUtility _utility;
        private INotification _notification;
        private IMrAbsenceImport _absDataImport;

        public async Task ProcessMessageAsync(string msg, IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _notification = container.GetRequiredService<INotification>();
            _absDataImport = container.GetRequiredService<IMrAbsenceImport>();

            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            int jobId = jsonReq.JobId;
            int tenantId = jsonReq.TenantId;
            int budgetYear = jsonReq.BudgetYear;
            bool isDeletePrevious = jsonReq.deleteExistingRecords;
            bool isVisWsType = jsonReq.isVisWsType;
            bool isReqByJobId = jsonReq.isRequestByJobId;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "MonthlyReport");

            if(isVisWsType)
                await _absDataImport.ImportAbsVisWsDataFromStagingAsync(userId, budgetYear, jobId, isDeletePrevious, isReqByJobId);
            else
                await _absDataImport.ImportAbsenceDataFromStagingAsync(userId, budgetYear, jobId, isDeletePrevious, isReqByJobId);

            Guid notId = Guid.NewGuid();
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["MR_AbsenceImport_Success"].LangText, redirectUrl, 24);
        }

        public string GetMessageType()
        {
            return "AbsenceDataImport";
        }

        public async Task OnError(Exception e, string msg, IServiceProvider container, string messageId)
        {
            dynamic jsonReq = JToken.Parse(msg);
            string userId = jsonReq.UserId;
            int tenantId = jsonReq.TenantId;
            Guid notId = Guid.NewGuid();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<String, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "MonthlyReport");
            Uri redirectUrl = new Uri(".", UriKind.Relative);
            await _notification.CreateNotificationAsync(notId, userId, tenantId, notificationStrings["MR_AbsenceImport_Failed"].LangText, redirectUrl, 24);
        }
    }
}
