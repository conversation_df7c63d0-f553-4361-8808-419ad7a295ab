using Aspose.Words;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker
{
    public partial class MonthlyReportingExportHelper
    {


        private bool IsYearlySetup(string user, int forecastPeriod)
        {
            UserData userDetails = _utility.GetUserDetails(user);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            tmr_period_setup isYearlySetupRec = tenantDbContext.tmr_period_setup.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period == forecastPeriod);
            if (isYearlySetupRec == null)
            {
                return false;
            }

            var isYearlySetup = isYearlySetupRec.isYearlySetupForMonthlyReport;
            return isYearlySetup.HasValue ? isYearlySetup.Value : false;
        }



        private void InsertTotalInteractiveGraph(DocumentBuilder builder, string user, int forecastPeriod)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            if (publishHelper.GetType() == typeof(WebHelperMr))
            {
                TenantDBContext dbContext = _utility.GetTenantDBContext();
                dbContext.Database.SetCommandTimeout(1000);
                UserData userDetails = _utility.GetUserDetails(user);
                _graphColors = _utility.GetGraphColors(user, ColorsFor.PublishGraphColors).ToList();
                Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
                string path;
                string divideByWhat = _utility.CheckOnDivideByWhat(user, "MR_PUBLISH_TOTALS_GRAPHS_DIVIDEBY");
                int divideByNumber = (divideByWhat.ToLower() == "Million".ToLower()) ? 1000000 : (divideByWhat.ToLower() == "Thousand".ToLower() ? 1000 : 1);
                int budgetYear = int.Parse(forecastPeriod.ToString().Substring(0, 4));
                List<int> previousYears = new List<int>() { budgetYear - 1, budgetYear - 2, budgetYear - 3 };
                var mrtotalannualgraphdataSet = (from p in dbContext.vw_mr_total_annual_graph
                                                 where p.TenantId == userDetails.tenant_id && p.ForecastPeriod == forecastPeriod
                                                 group p by new
                                                 {
                                                     p.ForecastPeriod,
                                                     p.TenantId,
                                                     p.Type,
                                                     p.ServiceL1Code,
                                                     p.ServiceL1Name,
                                                     p.ServiceL2Code,
                                                     p.ServiceL2Name,
                                                     p.OrgL1Code,
                                                     p.OrgL1Name,
                                                     p.OrgL2Code,
                                                     p.OrgL2Name,
                                                     p.OrgL3Code,
                                                     p.OrgL3Name,
                                                     p.displaylevel
                                                 } into g
                                                 select new ClsMRTotalAnnualGraph
                                                 {
                                                     GlYear = budgetYear,
                                                     TenantId = g.Key.TenantId,
                                                     Type = g.Key.Type,
                                                     ServiceL1Code = g.Key.ServiceL1Code,
                                                     ServiceL1Name = g.Key.ServiceL1Name,
                                                     ServiceL2Code = g.Key.ServiceL2Code,
                                                     ServiceL2Name = g.Key.ServiceL2Name,
                                                     OrgL1Code = g.Key.OrgL1Code,
                                                     OrgL1Name = g.Key.OrgL1Name,
                                                     OrgL2Code = g.Key.OrgL2Code,
                                                     OrgL2Name = g.Key.OrgL2Name,
                                                     OrgL3Code = g.Key.OrgL3Code,
                                                     OrgL3Name = g.Key.OrgL3Name,
                                                     Amount = g.Sum(x => x.Amount),
                                                     displaylevel = g.Key.displaylevel,
                                                 }).ToList();

                var mrtotalannualgraphdataSetPrevious = (from p in dbContext.vw_mr_total_previous_graph
                                                         where p.TenantId == userDetails.tenant_id && previousYears.Contains(p.GlYear) //p.GlYear != budgetYear
                                                         group p by new
                                                         {
                                                             p.GlYear,
                                                             p.TenantId,
                                                             p.Type,
                                                             p.ServiceL1Code,
                                                             p.ServiceL1Name,
                                                             p.ServiceL2Code,
                                                             p.ServiceL2Name,
                                                             p.OrgL1Code,
                                                             p.OrgL1Name,
                                                             p.OrgL2Code,
                                                             p.OrgL2Name,
                                                             p.OrgL3Code,
                                                             p.OrgL3Name,
                                                             p.displaylevel
                                                         } into g
                                                         select new ClsMRTotalAnnualGraph
                                                         {
                                                             GlYear = g.Key.GlYear,
                                                             TenantId = g.Key.TenantId,
                                                             Type = g.Key.Type,
                                                             ServiceL1Code = g.Key.ServiceL1Code,
                                                             ServiceL1Name = g.Key.ServiceL1Name,
                                                             ServiceL2Code = g.Key.ServiceL2Code,
                                                             ServiceL2Name = g.Key.ServiceL2Name,
                                                             OrgL1Code = g.Key.OrgL1Code,
                                                             OrgL1Name = g.Key.OrgL1Name,
                                                             OrgL2Code = g.Key.OrgL2Code,
                                                             OrgL2Name = g.Key.OrgL2Name,
                                                             OrgL3Code = g.Key.OrgL3Code,
                                                             OrgL3Name = g.Key.OrgL3Name,
                                                             Amount = g.Sum(x => x.Amount),
                                                             displaylevel = g.Key.displaylevel,
                                                         }).ToList();

                mrtotalannualgraphdataSet = mrtotalannualgraphdataSet.Union(mrtotalannualgraphdataSetPrevious).ToList();
                var monthlyReportParameters = (from monthlyReportParams in dbContext.vw_tco_parameters
                                               where monthlyReportParams.fk_tenant_id == userDetails.tenant_id && (monthlyReportParams.param_name == "MONTHREP_LEVEL_1" || monthlyReportParams.param_name == "MONTHREP_LEVEL_2") && monthlyReportParams.active == 1
                                               select monthlyReportParams).OrderBy(x => x.param_value).ToList();
                var displaySortFilterParameter = (from displayparams in dbContext.vw_tco_parameters
                                                  where displayparams.fk_tenant_id == userDetails.tenant_id && displayparams.param_name.ToLower() == "BMWEB_GRAPH_HIDE_ORGVIEW".ToLower() && displayparams.active == 1
                                                  select displayparams).FirstOrDefault();
                var hideDeptSortandFilter = displaySortFilterParameter == null ? false : displaySortFilterParameter.param_value.ToLower() == "TRUE".ToLower() ? true : false;
                Dictionary<string, string> sortConditions = new Dictionary<string, string>() { { "s1", "Sort by Service" }, { "s2", "Sort by Departments" } };
                List<int> availableYears = new List<int> { budgetYear };
                availableYears.AddRange(mrtotalannualgraphdataSetPrevious.Select(x => x.GlYear).Where(y => y <= budgetYear).OrderByDescending(y => y).Distinct().Take(3).ToList());
                var isServiceSetup = false;
                foreach (var item in monthlyReportParameters)
                {
                    if (item.param_value.Contains("service_id_"))
                    {
                        isServiceSetup = true;
                        break;
                    }
                }
                if (availableYears.Count > 0)
                {
                    dynamic years = new JObject();
                    dynamic yearsArray = new JArray();
                    for (int i = 0; i < availableYears.Count; i++)
                    {
                        dynamic yearObj = new JObject();
                        yearObj.year = availableYears[i];
                        yearObj.isDefault = i == 0 ? true : false;
                        yearsArray.Add(yearObj);
                    }
                    years.Add("years", yearsArray);
                    years.Add("displayOrgHeaders", isServiceSetup);
                    years.Add("HideDeptSortandFilter", hideDeptSortandFilter);
                    string jsonData = JsonConvert.SerializeObject(years);
                    publishHelper.InsertDataIntoBlob(jsonData, "", "yearslist", PubContentHandler.BlobType.Json);
                }

                if (monthlyReportParameters.Count == 1 && monthlyReportParameters[0].param_value.Contains("service_id"))
                {
                    foreach (var year in availableYears)
                    {
                        path = "/TotalGraph/" + year.ToString() + "/";

                        DrillDownSummary summary = _drillDownData.GetSummary(user, year);
                        publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(summary), path, "summary",
                            PubContentHandler.BlobType.Json);

                        foreach (var sort in sortConditions.Keys.OrderBy(y => y))
                        {
                            path = "/TotalGraph/" + year.ToString() + "/";
                            path = path + sort.ToString() + "/";
                            if (sort.ToLower() == "s1".ToLower())
                            {
                                PublishServiceDataForLevel1(user, DrillDownDataType.Cost, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings);
                                PublishServiceDataForLevel1(user, DrillDownDataType.Revenue, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings);

                                //Applying Filters
                                var availableOrgs = mrtotalannualgraphdataSet.Select(x => new { orgID = x.OrgL1Code, orgName = x.OrgL1Name }).OrderBy(y => y.orgID).Distinct().ToList();
                                if (availableOrgs.Count > 0)
                                {
                                    dynamic orgsArray = new JArray();
                                    dynamic yearObj = new JObject();
                                    yearObj.key = "0";
                                    yearObj.value = langStrings["MR_WEB_TotalInteractiveGraph_NoFilter"].LangText;
                                    yearObj.isDefault = true;
                                    orgsArray.Add(yearObj);

                                    for (int i = 0; i < availableOrgs.Count; i++)
                                    {
                                        yearObj = new JObject();
                                        yearObj.key = availableOrgs[i].orgID;
                                        yearObj.value = availableOrgs[i].orgName;
                                        yearObj.isDefault = false;
                                        orgsArray.Add(yearObj);
                                    }
                                    string jsonData = JsonConvert.SerializeObject(orgsArray);
                                    publishHelper.InsertDataIntoBlob(jsonData, path, "orgslist", PubContentHandler.BlobType.Json);
                                    availableOrgs.Insert(0, new { orgID = "0", orgName = langStrings["MR_WEB_TotalInteractiveGraph_NoFilter"].LangText });
                                }

                                foreach (var parentOrgID in availableOrgs.Select(x => x.orgID).OrderBy(y => y).ToList())
                                {
                                    if (parentOrgID == "0")
                                    {
                                        PublishServiceDataForLevel1(user, DrillDownDataType.Cost, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings, parentOrgID);
                                        PublishServiceDataForLevel1(user, DrillDownDataType.Revenue, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings, parentOrgID);
                                    }
                                    else
                                    {
                                        PublishServiceDataForLevel1(user, DrillDownDataType.Cost, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet.Where(x => x.OrgL1Code == parentOrgID).ToList(), langStrings, parentOrgID);
                                        PublishServiceDataForLevel1(user, DrillDownDataType.Revenue, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet.Where(x => x.OrgL1Code == parentOrgID).ToList(), langStrings, parentOrgID);
                                    }
                                }
                            }
                            else
                            {
                                dynamic orgsArray = new JArray();
                                string jsonData = JsonConvert.SerializeObject(orgsArray);
                                publishHelper.InsertDataIntoBlob(jsonData, path, "orgslist", PubContentHandler.BlobType.Json);
                                PublishDataForLevel1(user, DrillDownDataType.Cost, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings);
                                PublishDataForLevel1(user, DrillDownDataType.Revenue, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings);
                            }
                        }
                    }
                }
                else
                {
                    foreach (var year in availableYears)
                    {
                        path = "/TotalGraph/" + year.ToString() + "/";
                        DrillDownSummary summary = _drillDownData.GetSummary(user, year);
                        publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(summary), path, "summary", PubContentHandler.BlobType.Json);

                        path = "/TotalGraph/" + year.ToString() + "/s1/";

                        // Publish as per service area
                        PublishDataForLevel1(user, DrillDownDataType.Cost, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings);
                        PublishDataForLevel1(user, DrillDownDataType.Revenue, divideByNumber, path, publishHelper, year, forecastPeriod, mrtotalannualgraphdataSet, langStrings);

                        //dynamic orgsArray = new JArray();
                        //string jsonData = JsonConvert.SerializeObject(orgsArray);
                        //publishHelper.InsertDataIntoBlob(jsonData, "", "orgsList", PubContentHandler.BlobType.Json);
                    }
                }
            }
        }

    }
}