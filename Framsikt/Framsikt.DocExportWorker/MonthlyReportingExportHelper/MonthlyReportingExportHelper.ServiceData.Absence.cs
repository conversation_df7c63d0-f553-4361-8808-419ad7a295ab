using Aspose.Words;
using Aspose.Words.Tables;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json.Linq;
using System.Drawing;
using System.Globalization;
using System.Reflection;
using static Framsikt.BL.Helpers.clsConstants;

namespace Framsikt.DocExportWorker
{
    public partial class MonthlyReportingExportHelper
    {


        public void InsertAbsenceGridDataForServiceArea(string user, DocumentBuilder builder, string orgId, int budgetYear, int forecastPeriod, int orgLevel, string chapterId)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tableDef;

            if (!_monthlyReportAbsence.SneakPeakAbsDataExists(user, budgetYear))
                return;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                if (isYearlySetupEnabled == true)
                {
                    tableDef = _docTableConfig.GetTableDef(user, "MrYearlyAbsenceDisplay");
                }
                else
                {
                    tableDef = _docTableConfig.GetTableDef(user, "MrAbsence");
                }

                publishHelper.StartTableOrientation(tableDef);
                publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_absence_grid"].LangText, _context, true);

                publishHelper.StartTable("AbsenceDataGrid", null, null, tableDef);

                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportDocs = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "DocConfig");
                string[] monthFields = new string[] { ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_january_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_february_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_march_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_april_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_may_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_june_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_july_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_august_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_september_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_october_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_november_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_december_short3text")).Value).LangText };
                List<clsOrgIdAndDepartments> depsInServ = new List<clsOrgIdAndDepartments>();

                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

                decimal total = 0;

                string level1Val = string.Empty, level2Val = string.Empty, level3Val = string.Empty, level4Val = string.Empty, level5Val = string.Empty;

                switch (orgLevel)
                {
                    case 1:
                        level1Val = orgId;
                        break;

                    case 2:
                        level2Val = orgId;
                        break;

                    case 3:
                        level3Val = orgId;
                        break;

                    case 4:
                        level4Val = orgId;
                        break;
                }

                int count;
                int numAggrMonths = 3;
                int setupPeriod = forecastPeriod;
                tmr_period_setup tpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                            && x.forecast_period == forecastPeriod);

                if (tpsRec != null)
                {
                    setupPeriod = tpsRec.sickleave_period;
                    budgetYear = setupPeriod / 100;

                    if ((int)tpsRec.abs_aggr_type > 0)
                        numAggrMonths = numAggrMonths + 1;
                }

                int currentMonth = setupPeriod - budgetYear * 100;
                int preYrQrtPlusQuartsInYear = 1 + ((currentMonth % numAggrMonths == 0) ? currentMonth / numAggrMonths : currentMonth / numAggrMonths + 1);
                int quater = preYrQrtPlusQuartsInYear - 1;

                MonthlyRptQuartAbsPeriodDeptHelper quater1data = new MonthlyRptQuartAbsPeriodDeptHelper();
                MonthlyRptQuartAbsPeriodDeptHelper quater2data = new MonthlyRptQuartAbsPeriodDeptHelper();
                List<clsOrgIdAndDepartments> nextleveldepts = _utility.GetNextOrgLevelIdsDepartments(orgVersionContent, user, budgetYear, level1Val, level2Val, level3Val, level4Val, level5Val);
                bool deptsInServiceUnitLevel2 = nextleveldepts.Count > 0;

                if (deptsInServiceUnitLevel2)
                {
                    foreach (var su in nextleveldepts)
                    {
                        clsOrgIdAndDepartments suItem = new clsOrgIdAndDepartments
                        {
                            orgId = su.orgId,
                            orgName = su.orgName,
                            departmentValue = su.departmentValue,
                            departmentText = su.departmentText
                        };
                        depsInServ.Add(suItem);
                    }
                }
                else
                {
                    depsInServ = _monthlyReports.GetDepartmentsInServiceAreaOrgId(user, orgId, null, setupPeriod);
                }

                // select all department ids
                List<string> deplist = (from su in depsInServ
                                        select su.departmentValue).ToList();

                OrgInpLevels orgHierLvls = new OrgInpLevels();
                orgHierLvls.level1OrgId = level1Val;
                orgHierLvls.level2OrgId = level2Val;
                orgHierLvls.level3OrgId = level3Val;
                orgHierLvls.level4OrgId = level4Val;
                orgHierLvls.level5OrgId = level5Val;
                List<string> relevantDepartmentsList = _orgUtility.GetDepartmentsForOrgIdHierLvls(orgVersionContent, user, budgetYear, orgHierLvls);
                foreach (var rd in relevantDepartmentsList)
                {
                    if (!deplist.Contains(rd))
                        deplist.Add(rd);
                }

                if (!string.IsNullOrEmpty(chapterId))
                {
                    deplist = _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(user, budgetYear, orgId, 1, chapterId).GetAwaiter().GetResult();
                }

                if (!_monthlyReportAbsence.SneakPeakServAreaAbsDataExists(user, budgetYear, deplist))
                    return;

                bool isAnnualReport = (setupPeriod % 100 == 12) && _mrfUtility.IsYearlyReportSetup(user, setupPeriod);

                List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                List<string> lstColumnDataDetails = new List<string>();
                ColumnDetails cellTemplate;
                Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypePercentage2 = numberFormats["percentage_2_decimal"].LangText;
                lstColumnDetails = new List<ColumnDetails>();
                lstColumnDataDetails = new List<string>();

                builder.CellFormat.ClearFormatting();
                builder.RowFormat.HeadingFormat = true;
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Left;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                lstColumnDetails.Add(cellTemplate);

                foreach (ColumnDefinition cDef in tableDef.ColumnDefinitions)
                    lstColumnDataDetails.Add(cDef.ColumnName);

                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

                builder.RowFormat.HeadingFormat = false;

                bool noAbsDataForPrvYr = true;
                // print data for current + 4 prvious years
                for (int i = 4; i >= 0; i--)
                {
                    int fcastPeriodToGetFullYearData = (budgetYear - i) * 100 + 12;

                    if (i == 0)
                        fcastPeriodToGetFullYearData = setupPeriod;

                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrQuarterlyAbsenceData = _monthlyReportAbsence.FetchQuarterlyAbsenceData(user, budgetYear - i, deplist, null, 3, fcastPeriodToGetFullYearData, numAggrMonths, forecastPeriod, depsInServ, 0, orgId, true, chapterId);
                    count = mrQuarterlyAbsenceData.Count;
                    if (count > 0)
                    {
                        noAbsDataForPrvYr = false;
                        if (i == 1)
                        {
                            List<MonthlyRptQuartAbsPeriodDeptHelper> mrPrvLastYrData = _monthlyReportAbsence.FetchQuarterlyAbsenceData(user, budgetYear, deplist, null, 3, setupPeriod, numAggrMonths, forecastPeriod, depsInServ, quater, orgId, true, chapterId);
                            // get quaterly data for last year (absAggrQuarter = 0)
                            quater1data = (from d in mrPrvLastYrData
                                           where d.absAggrQuarter == 0 && d.absAggrServId == "-1"
                                           select d).FirstOrDefault();
                        }
                        if (i == 0)
                        {
                            // get quaterly data for current year
                            quater2data = (from d in mrQuarterlyAbsenceData
                                           where d.absAggrQuarter == quater && d.absAggrServId == "-1"
                                           select d).FirstOrDefault();
                        }

                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        // print data for absence
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Yearly_absence_text"].LangText + " " + (budgetYear - i));

                        if (mrQuarterlyAbsenceData[count - 1].shortTermAbsencePct < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(mrQuarterlyAbsenceData[count - 1].shortTermAbsencePct.ToString(numberTypePercentage2));

                        if (mrQuarterlyAbsenceData[count - 1].longTermAbsencePct < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(mrQuarterlyAbsenceData[count - 1].longTermAbsencePct.ToString(numberTypePercentage2));

                        if (mrQuarterlyAbsenceData[count - 1].overallAbsencePct < -0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(mrQuarterlyAbsenceData[count - 1].overallAbsencePct.ToString(numberTypePercentage2));

                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                    else
                    {
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Yearly_absence_text"].LangText + " " + (budgetYear - i));
                        lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));

                        if (!noAbsDataForPrvYr)
                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                }

                string lastMonAbsParam = _utility.GetParameterValue(user, "MRDOC_SA_ABSENCE_DISP_LASTMONTH");
                bool insertLastMonAbsData = string.IsNullOrEmpty(lastMonAbsParam) ? false : lastMonAbsParam.ToLower() == "true";
                int lastReportedPeriod = _mrfUtility.GetLastReportedPeriod(user, setupPeriod);
                int lastReportedPeriodDiff = setupPeriod - lastReportedPeriod;
                MonthlyRptQuartAbsPeriodDeptHelper? lastRptMonthData = _monthlyReportAbsence.FetchMonthlyAbsenceData(user, budgetYear, deplist, null, 3, setupPeriod, lastReportedPeriodDiff, forecastPeriod);

                //Print Quarterly data for previous, current year & Monthly Data for last month. If not in AnnualReport Mode, total 3 additional rows, else 1.
                int numAdditionalRows = !isAnnualReport ? 3 : 1;
                int endRow = insertLastMonAbsData ? 0 : 1;

                for (int j = numAdditionalRows - 1; j >= endRow; j--)
                {
                    lstColumnDetails = new List<ColumnDetails>();
                    lstColumnDataDetails = new List<string>();

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Left;
                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                    }
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    if (j == 0)
                    {
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_LastMonth_absence_text"].LangText + " " + monthFields[lastReportedPeriod % 100 - 1] + " " + (budgetYear));
                    }
                    else
                    {
                        if (numAggrMonths == 3)
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Quarterly_absence_text_" + quater].LangText + " " + (budgetYear - j + 1));
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Tertially_absence_text_" + quater].LangText + " " + (budgetYear - j + 1));
                        }
                    }

                    //print previous year quater data
                    if (j == 2)
                    {
                        if (quater1data != null)
                        {
                            if (quater1data.shortTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater1data.shortTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater1data.longTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater1data.longTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater1data.overallAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater1data.overallAbsencePct.ToString(numberTypePercentage2));

                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                        }
                    }
                    // print data for current year last quater
                    if (j == 1)
                    {
                        if (quater2data != null)
                        {
                            if (quater2data.shortTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater2data.shortTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater2data.longTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater2data.longTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater2data.overallAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater2data.overallAbsencePct.ToString(numberTypePercentage2));
                        }
                        else
                        {
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        }
                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                    // print data for last month
                    if (j == 0)
                    {
                        if (lastRptMonthData != null)
                        {
                            if (lastRptMonthData.shortTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(lastRptMonthData.shortTermAbsencePct.ToString(numberTypePercentage2));

                            if (lastRptMonthData.longTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(lastRptMonthData.longTermAbsencePct.ToString(numberTypePercentage2));

                            if (lastRptMonthData.overallAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(lastRptMonthData.overallAbsencePct.ToString(numberTypePercentage2));
                        }
                        else
                        {
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        }
                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                }
                publishHelper.EndTable();
                publishHelper.EndTableOrientation();
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence data for Service Area");
            }
        }



        private void InsertAbsenceTxtForServiceArea(string user, DocumentBuilder builder, string orgId, int budgetYear, int forecastPeriod, int orgLevel, bool isStaTxt)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                bool isFeatureEnabled = _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text).GetAwaiter().GetResult();
                if (isFeatureEnabled)
                {
                    // get the absenceStatus and absenceActionPlan from blob
                    TextDescriptionHelper input = new()
                    {
                        ForecastPeriod = forecastPeriod,
                        BudgetYear = budgetYear,
                        OrgId = orgId,
                        OrgLevel = orgLevel,
                        ServiceId = null,
                        DescriptionType = MonthlyReportTextBoxType.AbsenceStatus,
                        AttributeId = string.Empty,
                        OrgName = null,
                        ServiceName = null,
                        AttributeName = string.Empty,
                    };

                    var absenceStatus = _mrfUtility.GetDescriptionText(user, true, input).GetAwaiter().GetResult();
                    input.DescriptionType = MonthlyReportTextBoxType.AbsenceActionPlan;
                    var absenceActionPlan = _mrfUtility.GetDescriptionText(user, true,input).GetAwaiter().GetResult();

                   
                    tmr_period_setup tmrpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == forecastPeriod);

                    //Adding the desriptions there.
                    if (absenceStatus != null && !string.IsNullOrEmpty(absenceStatus.Data) && isStaTxt &&
                        (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceSummery))
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(absenceStatus.Data, user);
                    }

                    if (absenceActionPlan != null && !string.IsNullOrEmpty(absenceActionPlan.Data) && !isStaTxt &&
                        (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceActionPlan))
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(absenceActionPlan.Data, user);
                    }
                }
                else
                {
                    dynamic absenceStatus = _mrfUtility.GetMrTextDescriptions(user, true, budgetYear, forecastPeriod, orgId, orgLevel, "AbsenceStatus", null, null, null);
                    dynamic absenceActionPlan = _mrfUtility.GetMrTextDescriptions(user, true, budgetYear, forecastPeriod, orgId, orgLevel, "AbsenceActionPlan", null, null, null);

                    tmr_period_setup tmrpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == forecastPeriod);

                    //Adding the desriptions there.
                    if (absenceStatus != null && !string.IsNullOrEmpty(absenceStatus.data.ToString()) && isStaTxt &&
                        (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceSummery))
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(absenceStatus.data.ToString(), user);
                    }

                    if (absenceActionPlan != null && !string.IsNullOrEmpty(absenceActionPlan.data.ToString()) && !isStaTxt &&
                        (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceActionPlan))
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(absenceActionPlan.data.ToString(), user);
                    }

                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence text for Service Area");
            }
        }



        private void InsertAbsenceGridDataPerServiceArea(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, string orgId)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tableDef;

            if (!_monthlyReportAbsence.SneakPeakAbsDataExists(user, budgetYear))
                return;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportDoc = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "DocConfig");

                List<clsOrgIdAndDepartments> depsInServ = new List<clsOrgIdAndDepartments>();
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

                string level1Val = string.Empty, level2Val = string.Empty, level3Val = string.Empty, level4Val = string.Empty, level5Val = string.Empty;
                level1Val = orgId;

                bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);

                int count;
                int numAggrMonths = 3;
                int setupPeriod = forecastPeriod;
                tmr_period_setup tpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(
                                                                                  x => x.fk_tenant_id == userDetails.tenant_id
                                                                                    && x.forecast_period == forecastPeriod);

                if (tpsRec != null)
                {
                    setupPeriod = tpsRec.sickleave_period;
                    budgetYear = setupPeriod / 100;
                    ////if ((int)tpsRec.abs_aggr_type == 1) // bugFix 96704 - Commenting out since the Publish format is fixed w.r.t 4 quarters display
                    ////    numAggrMonths = numAggrMonths + 1;
                }

                List<clsOrgIdAndDepartments> nextleveldepts = _utility.GetNextOrgLevelIdsDepartments(orgVersionContent, user, budgetYear, level1Val, level2Val, level3Val, level4Val, level5Val);
                bool deptsInServiceUnitLevel2 = nextleveldepts.Count > 0;

                if (deptsInServiceUnitLevel2)
                {
                    foreach (var su in nextleveldepts)
                    {
                        clsOrgIdAndDepartments suItem = new clsOrgIdAndDepartments
                        {
                            orgId = su.orgId,
                            orgName = su.orgName,
                            departmentValue = su.departmentValue,
                            departmentText = su.departmentText
                        };
                        depsInServ.Add(suItem);
                    }
                }
                else
                {
                    depsInServ = _monthlyReports.GetDepartmentsInServiceAreaOrgId(user, orgId, null, setupPeriod);
                }

                // select all department ids
                var deplist = (from su in depsInServ
                               select su.departmentValue).ToList();

                depsInServ = depsInServ.OrderBy(x => x.orgId).ToList<clsOrgIdAndDepartments>();
                //Add a proxy item for display of total row in the grid
                depsInServ.Add(new clsOrgIdAndDepartments
                {
                    orgId = "-1",
                    orgName = ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "MR_AbsSAAnnual_Total")).Value).LangText,
                    departmentValue = "-1",
                    departmentText = ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "MR_AbsSAAnnual_Total")).Value).LangText
                });

                if (!_monthlyReportAbsence.SneakPeakServAreaAbsDataExists(user, budgetYear, deplist))
                    return;

                //Populate Grid with one row per Service Area or Service Unit
                //Regroup depsInServ to have list of unique service area/unit orgids
                List<clsOrgIdAndDepartments> rowOrgIdNames = (from dis in depsInServ
                                                              group dis by new { dis.orgId, dis.orgName } into grp
                                                              select new clsOrgIdAndDepartments
                                                              {
                                                                  orgId = grp.Key.orgId,
                                                                  orgName = grp.Key.orgName,
                                                                  departmentValue = grp.Key.orgId,
                                                                  departmentText = grp.Key.orgName
                                                              }).ToList();

                List<MonthlyRptQuartAbsPeriodDeptHelper> mrAllDepQuartsAbsenceData = new List<MonthlyRptQuartAbsPeriodDeptHelper>();
                List<MonthlyRptQuartAbsPeriodDeptHelper> mrAllOrgsAbsenceData = new List<MonthlyRptQuartAbsPeriodDeptHelper>();

                if (isYearlySetupEnabled)
                {
                    // 0 for Previous Year Quarter, 1 through 4 for current year Quarters, 5 for Year sum
                    List<int> preQnQuartsInYearnYear = new List<int> { 5 };

                    //Mould-Datastructure for records for each ServiceArea|ServiceUnit & Quarter combination
                    mrAllDepQuartsAbsenceData = (from servUnit in rowOrgIdNames
                                                 from quart in preQnQuartsInYearnYear
                                                 group servUnit by new { servUnit.orgId, servUnit.orgName, quart } into grpServUnit
                                                 select new MonthlyRptQuartAbsPeriodDeptHelper
                                                 {
                                                     absAggrServId = grpServUnit.Key.orgId,
                                                     absAggrServName = grpServUnit.Key.orgName,
                                                     absAggrQuarter = grpServUnit.Key.quart
                                                 }).ToList();

                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrAnnualAbsenceDataCY = _monthlyReportAbsence.FetchQuarterlyAbsenceData(user, budgetYear, deplist, null, 2, setupPeriod, numAggrMonths, forecastPeriod, depsInServ, 0, string.Empty, false, string.Empty);
                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrAnnualAbsenceDataPY = _monthlyReportAbsence.FetchQuarterlyAbsenceData(user, budgetYear - 1, deplist, null, 2, (setupPeriod - 100), numAggrMonths, forecastPeriod, depsInServ, 0, string.Empty, false, string.Empty);

                    //Stuff it into the Mould
                    mrAllDepQuartsAbsenceData.ForEach(x =>
                    {
                        var qad = mrAnnualAbsenceDataCY.FirstOrDefault(y => y.departmentCode == x.absAggrServId
                                                                 && y.absAggrQuarter == x.absAggrQuarter);
                        x.shortTermAbsencePct = qad != null ? qad.shortTermAbsencePct : 0;
                        x.longTermAbsencePct = qad != null ? qad.longTermAbsencePct : 0;
                        x.overallAbsencePct = qad != null ? qad.overallAbsencePct : 0;
                        x.isValidDataRow = qad == null ? false : true;
                    });

                    //Stuff it into the Mould
                    mrAllDepQuartsAbsenceData.ForEach(x =>
                    {
                        var qad = mrAnnualAbsenceDataPY.FirstOrDefault(y => y.departmentCode == x.absAggrServId
                                                                 && y.absAggrQuarter == x.absAggrQuarter);
                        x.shortTermAbsencePctPY = qad != null ? qad.shortTermAbsencePct : 0;
                        x.longTermAbsencePctPY = qad != null ? qad.longTermAbsencePct : 0;
                        x.overallAbsencePctPY = qad != null ? qad.overallAbsencePct : 0;
                    });

                    mrAllDepQuartsAbsenceData = mrAllDepQuartsAbsenceData.Where(x => x.absAggrQuarter == 5).ToList();
                }
                else
                {
                    // 1 through 4 for current year Quarters, 5 for Overall current year, 6 for last year overall. ShortTerm 1-6, LongTerm 11-16, Overall 21-26
                    List<int> preQnQuartsInYearnYear = new List<int> { 1, 2, 3, 4, 5, 6, 11, 12, 13, 14, 15, 16, 21, 22, 23, 24, 25, 26 };
                    List<int> holderForDoc = new List<int> { 5 };

                    //Mould-Datastructure for records for each Org
                    mrAllOrgsAbsenceData = (from servUnit in rowOrgIdNames
                                            from quart in holderForDoc
                                            group servUnit by new { servUnit.orgId, servUnit.orgName, quart } into grpServUnit
                                            select new MonthlyRptQuartAbsPeriodDeptHelper
                                            {
                                                absAggrServId = grpServUnit.Key.orgId,
                                                absAggrServName = grpServUnit.Key.orgName,
                                                absAggrQuarter = grpServUnit.Key.quart
                                            }).ToList();

                    //Mould-Datastructure for records for each Org & Quarter combination
                    mrAllDepQuartsAbsenceData = (from servUnit in rowOrgIdNames
                                                 from quart in preQnQuartsInYearnYear
                                                 group servUnit by new { servUnit.orgId, servUnit.orgName, quart } into grpServUnit
                                                 select new MonthlyRptQuartAbsPeriodDeptHelper
                                                 {
                                                     absAggrServId = grpServUnit.Key.orgId,
                                                     absAggrServName = grpServUnit.Key.orgName,
                                                     absAggrQuarter = grpServUnit.Key.quart
                                                 }).ToList();

                    List<MonthlyRptQuartAbsPeriodDeptHelper> absenceDataDetail = _monthlyReportAbsence.FetchQuarterlyAbsenceDataDetail(user, budgetYear, deplist, null, 2, setupPeriod, numAggrMonths, forecastPeriod, depsInServ);

                    //Stuff it into the Bigger Mould. The mould matrix is wider to hold 3 sets of columns - ShortTerm, LongTerm, Overall. Hence % 10 on the absAggrQuarter
                    mrAllDepQuartsAbsenceData.ForEach(x =>
                    {
                        var qad = absenceDataDetail.FirstOrDefault(y => y.departmentCode == x.absAggrServId
                                                            && y.absAggrQuarter % 10 == x.absAggrQuarter);
                        x.shortTermAbsencePct = qad != null ? qad.shortTermAbsencePct : 0;
                        x.longTermAbsencePct = qad != null ? qad.longTermAbsencePct : 0;
                        x.overallAbsencePct = qad != null ? qad.overallAbsencePct : 0;
                        x.isValidDataRow = qad != null;
                    });

                    //Select data from the Bigger mould to actual Mould.
                    mrAllOrgsAbsenceData.ForEach(x =>
                    {
                        var madqad = mrAllDepQuartsAbsenceData.Where(y => y.absAggrServId == x.absAggrServId).ToList();

                        var shQtr1Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 1);
                        x.shortTermQtr1Pct = shQtr1Pct != null ? shQtr1Pct.shortTermAbsencePct : 0;
                        var shQtr2Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 2);
                        x.shortTermQtr2Pct = shQtr2Pct != null ? shQtr2Pct.shortTermAbsencePct : 0;
                        var shQtr3Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 3);
                        x.shortTermQtr3Pct = shQtr3Pct != null ? shQtr3Pct.shortTermAbsencePct : 0;
                        var shQtr4Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 4);
                        x.shortTermQtr4Pct = shQtr4Pct != null ? shQtr4Pct.shortTermAbsencePct : 0;
                        var shQtr5Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 5);
                        x.shortTermAbsencePct = shQtr5Pct != null ? shQtr5Pct.shortTermAbsencePct : 0;
                        var shQtr6Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 6);
                        x.shortTermAbsencePctPY = shQtr6Pct != null ? shQtr6Pct.shortTermAbsencePct : 0;

                        var lgQtr1Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 1);
                        x.longTermQtr1Pct = lgQtr1Pct != null ? lgQtr1Pct.longTermAbsencePct : 0;
                        var lgQtr2Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 2);
                        x.longTermQtr2Pct = lgQtr2Pct != null ? lgQtr2Pct.longTermAbsencePct : 0;
                        var lgQtr3Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 3);
                        x.longTermQtr3Pct = lgQtr3Pct != null ? lgQtr3Pct.longTermAbsencePct : 0;
                        var lgQtr4Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 4);
                        x.longTermQtr4Pct = lgQtr4Pct != null ? lgQtr4Pct.longTermAbsencePct : 0;
                        var lgQtr5Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 5);
                        x.longTermAbsencePct = lgQtr5Pct != null ? lgQtr5Pct.longTermAbsencePct : 0;
                        var lgQtr6Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 6);
                        x.longTermAbsencePctPY = lgQtr6Pct != null ? lgQtr6Pct.longTermAbsencePct : 0;

                        var ovQtr1Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 1);
                        x.overallQtr1Pct = ovQtr1Pct != null ? ovQtr1Pct.overallAbsencePct : 0;
                        var ovQtr2Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 2);
                        x.overallQtr2Pct = ovQtr2Pct != null ? ovQtr2Pct.overallAbsencePct : 0;
                        var ovQtr3Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 3);
                        x.overallQtr3Pct = ovQtr3Pct != null ? ovQtr3Pct.overallAbsencePct : 0;
                        var ovQtr4Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 4);
                        x.overallQtr4Pct = ovQtr4Pct != null ? ovQtr4Pct.overallAbsencePct : 0;
                        var ovQtr5Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 5);
                        x.overallAbsencePct = ovQtr5Pct != null ? ovQtr5Pct.overallAbsencePct : 0;
                        var ovQtr6Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 6);
                        x.overallAbsencePctPY = ovQtr6Pct != null ? ovQtr6Pct.overallAbsencePct : 0;

                        x.isValidDataRow = madqad.Count > 0;
                    });
                }
                //convert it to JArray
                JArray dataArray = isYearlySetupEnabled ? JArray.FromObject(mrAllDepQuartsAbsenceData) : JArray.FromObject(mrAllOrgsAbsenceData);

                if (dataArray.Any())
                {
                    Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                    string numberTypeAmount = numberFormats["amount"].LangText;
                    string numberTypePercentage2 = numberFormats["percentage_2_decimal"].LangText;

                    string userDefinedCol = string.Empty;
                    if (isYearlySetupEnabled)
                    {
                        tableDef = _docTableConfig.GetTableDef(user, "MrYearlyAbsSa");
                    }
                    else
                    {
                        tableDef = _docTableConfig.GetTableDef(user, "MrAbsSa");
                    }
                    publishHelper.StartTableOrientation(tableDef);
                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_absencegrid_per_SA"].LangText, _context, true);
                    publishHelper.StartTable("AbsenceDataGridPerSA", null, null, tableDef);

                    var defaultColumn = new List<string>();
                    foreach (var item in tableDef.ColumnDefinitions)
                    {
                        defaultColumn.Add(item.ColumnId);
                    }

                    List<string> colsForSubColHeads = new List<string>();
                    foreach (ColumnDefinition cDef in tableDef.ColumnDefinitions.Where(x => x.IsActive).ToList())
                    {
                        colsForSubColHeads.Add(cDef.ColumnId);
                    }

                    ColumnDetails cellTemplate;

                    int set1HdrColCount = isYearlySetupEnabled ? 3 : 6;
                    int set2HdrColCount = isYearlySetupEnabled ? 3 : 6;
                    int set3HdrColCount = isYearlySetupEnabled ? 0 : 6;

                    List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                    List<string> lstColumnDataDetails = new List<string>();
                    cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    cellTemplate.Column = 1;
                    lstColumnDetails.Add(cellTemplate);
                    lstColumnDataDetails.Add(string.Empty);

                    if (set1HdrColCount > 0)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Column = set1HdrColCount;
                        cellTemplate.underlineText = true;
                        lstColumnDetails.Add(cellTemplate);
                        if (isYearlySetupEnabled)
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_group_title"].LangText + " " + budgetYear);
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_title_shorttermabs"].LangText);
                        }
                    }

                    if (set2HdrColCount > 0)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Column = set2HdrColCount;
                        cellTemplate.underlineText = true;
                        lstColumnDetails.Add(cellTemplate);
                        if (isYearlySetupEnabled)
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_group_title"].LangText + " " + (budgetYear - 1));
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_title_longtermabs"].LangText);
                        }
                    }

                    if (set3HdrColCount > 0)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Column = set3HdrColCount;
                        cellTemplate.underlineText = true;
                        lstColumnDetails.Add(cellTemplate);
                        if (!isYearlySetupEnabled)
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_title_totalabs"].LangText);
                        }
                    }

                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading1);

                    count = 0;
                    string suffix = string.Empty;
                    lstColumnDetails = new List<ColumnDetails>();
                    lstColumnDataDetails = new List<string>();
                    builder.CellFormat.ClearFormatting();
                    builder.RowFormat.HeadingFormat = true;

                    foreach (var c in defaultColumn)
                    {
                        count++;
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;

                        if (isYearlySetupEnabled)
                        {
                            if (!string.IsNullOrEmpty(userDefinedCol))
                            {
                                cellTemplate.PreferredWidth = 17.5;
                            }
                            else if (count == 1)
                            {
                                cellTemplate.PreferredWidth = 30;
                            }
                            else
                            {
                                cellTemplate.PreferredWidth = 11.7;
                            }
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(userDefinedCol))
                            {
                                cellTemplate.PreferredWidth = 6.5;
                            }
                            else if (count == 1)
                            {
                                cellTemplate.PreferredWidth = 15;
                            }
                            else
                            {
                                cellTemplate.PreferredWidth = 4.3;
                            }
                        }

                        cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;

                        lstColumnDetails.Add(cellTemplate);

                        if (isYearlySetupEnabled)
                        {
                            if (c.ToLower() == "shortTermAbsencePct".ToLower() || c.ToLower() == "shortTermAbsencePctPY".ToLower())
                            {
                                suffix = c;
                            }
                            else if (c.ToLower() == "longTermAbsencePct".ToLower() || c.ToLower() == "longTermAbsencePctPY".ToLower())
                            {
                                suffix = c;
                            }
                            else if (c.ToLower() == "overallAbsencePct".ToLower() || c.ToLower() == "overallAbsencePctPY".ToLower())
                            {
                                suffix = c;
                            }
                            else
                            {
                                suffix = "sa";
                            }

                            if (suffix == "sa")
                            {
                                lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_title_" + suffix].LangText);
                            }
                            else
                            {
                                lstColumnDataDetails.Add(langStringValuesMonthlyReportDoc["coltexty_" + suffix].LangText);
                            }
                        }
                        else
                        {
                            if (c.ToLower() == "shortTermAbsencePctPY".ToLower() || c.ToLower() == "longTermAbsencePctPY".ToLower() || c.ToLower() == "overallAbsencePctPY".ToLower())
                            {
                                lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_" + c.ToLower()].LangText + " " + (budgetYear - 1));
                            }
                            else
                            {
                                if (c.ToLower() == "orgId".ToLower())
                                {
                                    suffix = "sa";
                                }
                                else if (c.ToLower() == "shortTermAbsencePct".ToLower())
                                {
                                    suffix = "shortTermAbsencePct";
                                }
                                else if (c.ToLower() == "longTermAbsencePct".ToLower())
                                {
                                    suffix = "longTermAbsencePct";
                                }
                                else if (c.ToLower() == "overallAbsencePct".ToLower())
                                {
                                    suffix = "overallAbsencePct";
                                }
                                else
                                {
                                    suffix = "kv";
                                }

                                if (suffix == "kv")
                                {
                                    int qtrNum = int.Parse((c.Substring(c.Length - 4)).Substring(0, 1));
                                    lstColumnDataDetails.Add(qtrNum.ToString() + "." + langStringValuesMonthlyReport["MR_Doc_absSA_col_title_" + suffix].LangText);
                                }
                                else if (suffix == "sa")
                                {
                                    lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_absSA_col_title_" + suffix].LangText);
                                }
                                else
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                    lstColumnDataDetails.Add(langStringValuesMonthlyReportDoc["coltext_" + suffix].LangText);
                                }
                            }
                        }
                    }

                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
                    builder.RowFormat.HeadingFormat = false;

                    // Get the PropertyInfo object by passing the property name.
                    PropertyInfo[] myPropInfo = typeof(MonthlyRptQuartAbsPeriodDeptHelper).GetProperties();
                    // insert data for the document

                    foreach (var d in dataArray)
                    {
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        //for each column to display insert record
                        count = 0;
                        foreach (var col in defaultColumn)
                        {
                            count++;
                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);

                            cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                            cellTemplate.WrapText = true;

                            if ((string)d["absAggrServId"] == "-1")
                            {
                                cellTemplate.FontColor = FramsiktColors.TotalColor;
                                cellTemplate.IsBold = true;
                            }

                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            lstColumnDetails.Add(cellTemplate);

                            if (isYearlySetupEnabled)
                            {
                                if (col.ToLower() == "overallAbsencePct".ToLower() || col.ToLower() == "overallAbsencePctPY".ToLower())
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                }
                            }
                            else
                            {
                                if (col.ToLower() == "shortTermAbsencePct".ToLower() || col.ToLower() == "longTermAbsencePct".ToLower() || col.ToLower() == "overallAbsencePct".ToLower())
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                }
                            }

                            string name = string.Empty;
                            if (col.ToLower() == "orgId".ToLower())
                            {
                                name = "absAggrServName";
                            }
                            else
                            {
                                name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                            }

                            //apply number formatting of for number column
                            if (name.ToLower() == "absAggrServName".ToLower())
                            {
                                var valuecy = (string)d[name];
                                lstColumnDataDetails.Add(valuecy);
                            }
                            else
                            {
                                decimal valuecy = 0;

                                bool success = decimal.TryParse(d[name].ToString(), out valuecy);
                                if (valuecy < 0)
                                    lstColumnDataDetails.Add("-");
                                else
                                    lstColumnDataDetails.Add(valuecy.ToString(numberTypePercentage2));
                            }
                        }
                        if ((bool)d["isValidDataRow"])
                        {
                            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                        }

                        builder.RowFormat.HeadingFormat = false;
                    }
                    publishHelper.EndTable();
                    publishHelper.EndTableOrientation();
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence data per Service Area");
            }
        }



        private void InsertAbsenceGridDataPerService(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, string orgId)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tableDef;

            if (!_monthlyReportAbsence.SneakPeakAbsDataExists(user, budgetYear))
                return;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);
                Dictionary<string, clsLanguageString> langStrMr = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStrMrDoc = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "DocConfig");

                List<clsOrgIdAndDepartments> depsInServ = new List<clsOrgIdAndDepartments>();
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

                string level1Val = string.Empty, level2Val = string.Empty, level3Val = string.Empty, level4Val = string.Empty, level5Val = string.Empty;
                level1Val = orgId;

                OrgInpLevels orgInput = new OrgInpLevels();
                orgInput.level1OrgId = level1Val;

                bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);

                int count;
                int numAggrMonths = 3;
                int setupPeriod = forecastPeriod;
                tmr_period_setup tpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                            && x.forecast_period == forecastPeriod);

                if (tpsRec != null)
                {
                    setupPeriod = tpsRec.sickleave_period;
                    budgetYear = setupPeriod / 100;
                }

                List<clsOrgIdAndDepartments> nextleveldepts = _utility.GetNextOrgLevelIdsDepartments(orgVersionContent, user, budgetYear, level1Val, level2Val, level3Val, level4Val, level5Val);
                bool deptsInServiceUnitLevel2 = nextleveldepts.Count > 0;

                if (deptsInServiceUnitLevel2)
                {
                    foreach (var su in nextleveldepts)
                    {
                        clsOrgIdAndDepartments suItem = new clsOrgIdAndDepartments
                        {
                            orgId = su.orgId,
                            orgName = su.orgName,
                            departmentValue = su.departmentValue,
                            departmentText = su.departmentText
                        };
                        depsInServ.Add(suItem);
                    }
                }
                else
                {
                    depsInServ = _monthlyReports.GetDepartmentsInServiceAreaOrgId(user, orgId, null, setupPeriod);
                }

                // select all department ids
                var deplist = (from su in depsInServ
                               select su.departmentValue).ToList();

                depsInServ = depsInServ.OrderBy(x => x.orgId).ToList<clsOrgIdAndDepartments>();
                //Add a proxy item for display of total row in the grid
                depsInServ.Add(new clsOrgIdAndDepartments
                {
                    orgId = "-1",
                    orgName = ((langStrMr.FirstOrDefault(v => v.Key == "MR_AbsSAAnnual_Total")).Value).LangText,
                    departmentValue = "-1",
                    departmentText = ((langStrMr.FirstOrDefault(v => v.Key == "MR_AbsSAAnnual_Total")).Value).LangText
                });

                string paramValue = _utility.GetParameterValue(user, "MONTHREP_LEVEL_1");
                int serviceIdLvl = 0;
                if (!string.IsNullOrEmpty(paramValue))
                    serviceIdLvl = int.Parse(paramValue.Last().ToString());

                List<string> funcsList = _orgUtility.GetFunctionsForOrgIdHierLvls(orgVersionContent, user, orgInput, null);
                List<clsOrgIdAndDepartments> funcsInServ = _mrfUtility.GetServiceIdForMrServLvl(user, funcsList, serviceIdLvl);

                //Populate Grid with one row per Service
                List<clsOrgIdAndDepartments> rowServIdNames = (from dis in funcsInServ
                                                               group dis by new { dis.serviceId, dis.serviceName } into grp
                                                               select new clsOrgIdAndDepartments
                                                               {
                                                                   serviceId = grp.Key.serviceId,
                                                                   serviceName = grp.Key.serviceName,
                                                                   functionValue = grp.Key.serviceId
                                                               }).ToList();

                List<MonthlyRptQuartAbsPeriodDeptHelper> mrAllDepQuartsAbsenceData = new List<MonthlyRptQuartAbsPeriodDeptHelper>();
                List<MonthlyRptQuartAbsPeriodDeptHelper> mrAllOrgsAbsenceData = new List<MonthlyRptQuartAbsPeriodDeptHelper>();

                if (isYearlySetupEnabled)
                {
                    // 0 for Previous Year Quarter, 1 through 4 for current year Quarters, 5 for Year sum
                    List<int> preQnQuartsInYearnYear = new List<int> { 5 };

                    //Mould-Datastructure for records for each ServiceArea|ServiceUnit & Quarter combination
                    mrAllDepQuartsAbsenceData = (from servUnit in rowServIdNames
                                                 from quart in preQnQuartsInYearnYear
                                                 group servUnit by new { servUnit.serviceId, servUnit.serviceName, quart } into grpServUnit
                                                 select new MonthlyRptQuartAbsPeriodDeptHelper
                                                 {
                                                     absAggrServId = grpServUnit.Key.serviceId,
                                                     absAggrServName = grpServUnit.Key.serviceName,
                                                     absAggrQuarter = grpServUnit.Key.quart
                                                 }).ToList();

                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrAnnualAbsenceDataCY = _monthlyReportAbsence.FetchQuarterlyAbsenceServ(user, budgetYear, deplist, funcsList, setupPeriod, numAggrMonths, forecastPeriod, funcsInServ, 0);
                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrAnnualAbsenceDataPY = _monthlyReportAbsence.FetchQuarterlyAbsenceServ(user, budgetYear - 1, deplist, funcsList, (setupPeriod - 100), numAggrMonths, forecastPeriod, funcsInServ, 0);

                    //Stuff it into the Mould
                    mrAllDepQuartsAbsenceData.ForEach(x =>
                    {
                        var qad = mrAnnualAbsenceDataCY.FirstOrDefault(y => y.functionCode == x.absAggrServId
                                                                 && y.absAggrQuarter == x.absAggrQuarter);
                        x.shortTermAbsencePct = qad != null ? qad.shortTermAbsencePct : 0;
                        x.longTermAbsencePct = qad != null ? qad.longTermAbsencePct : 0;
                        x.overallAbsencePct = qad != null ? qad.overallAbsencePct : 0;
                        x.isValidDataRow = qad == null ? false : true;
                    });

                    //Stuff it into the Mould
                    mrAllDepQuartsAbsenceData.ForEach(x =>
                    {
                        var qad = mrAnnualAbsenceDataPY.FirstOrDefault(y => y.functionCode == x.absAggrServId
                                                                 && y.absAggrQuarter == x.absAggrQuarter);
                        x.shortTermAbsencePctPY = qad != null ? qad.shortTermAbsencePct : 0;
                        x.longTermAbsencePctPY = qad != null ? qad.longTermAbsencePct : 0;
                        x.overallAbsencePctPY = qad != null ? qad.overallAbsencePct : 0;
                    });

                    mrAllDepQuartsAbsenceData = mrAllDepQuartsAbsenceData.Where(x => x.absAggrQuarter == 5).ToList();
                }
                else
                {
                    // 1 through 4 for current year Quarters, 5 for Overall current year, 6 for last year overall. ShortTerm 1-6, LongTerm 11-16, Overall 21-26
                    List<int> preQnQuartsInYearnYear = new List<int> { 1, 2, 3, 4, 5, 6, 11, 12, 13, 14, 15, 16, 21, 22, 23, 24, 25, 26 };
                    List<int> holderForDoc = new List<int> { 5 };

                    //Mould-Datastructure for records for each Org
                    mrAllOrgsAbsenceData = (from servUnit in rowServIdNames
                                            from quart in holderForDoc
                                            group servUnit by new { servUnit.serviceId, servUnit.serviceName, quart } into grpServUnit
                                            select new MonthlyRptQuartAbsPeriodDeptHelper
                                            {
                                                absAggrServId = grpServUnit.Key.serviceId,
                                                absAggrServName = grpServUnit.Key.serviceName,
                                                absAggrQuarter = grpServUnit.Key.quart
                                            }).ToList();

                    //Mould-Datastructure for records for each Org & Quarter combination
                    mrAllDepQuartsAbsenceData = (from servUnit in rowServIdNames
                                                 from quart in preQnQuartsInYearnYear
                                                 group servUnit by new { servUnit.serviceId, servUnit.serviceName, quart } into grpServUnit
                                                 select new MonthlyRptQuartAbsPeriodDeptHelper
                                                 {
                                                     absAggrServId = grpServUnit.Key.serviceId,
                                                     absAggrServName = grpServUnit.Key.serviceName,
                                                     absAggrQuarter = grpServUnit.Key.quart
                                                 }).ToList();

                    List<MonthlyRptQuartAbsPeriodDeptHelper> absenceDataDetail = _monthlyReportAbsence.FetchQuarterlyAbsenceFuncServ(user, budgetYear, deplist, funcsList, setupPeriod, numAggrMonths, forecastPeriod, funcsInServ);

                    //Stuff it into the Bigger Mould. The mould matrix is wider to hold 3 sets of columns - ShortTerm, LongTerm, Overall. Hence % 10 on the absAggrQuarter
                    mrAllDepQuartsAbsenceData.ForEach(x =>
                    {
                        var qad = absenceDataDetail.FirstOrDefault(y => y.functionCode == x.absAggrServId
                                                            && y.absAggrQuarter % 10 == x.absAggrQuarter);
                        x.shortTermAbsencePct = qad != null ? qad.shortTermAbsencePct : 0;
                        x.longTermAbsencePct = qad != null ? qad.longTermAbsencePct : 0;
                        x.overallAbsencePct = qad != null ? qad.overallAbsencePct : 0;
                        x.isValidDataRow = qad != null;
                    });

                    //Select data from the Bigger mould to actual Mould.
                    mrAllOrgsAbsenceData.ForEach(x =>
                    {
                        var madqad = mrAllDepQuartsAbsenceData.Where(y => y.absAggrServId == x.absAggrServId).ToList();

                        var shQtr1Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 1);
                        x.shortTermQtr1Pct = shQtr1Pct != null ? shQtr1Pct.shortTermAbsencePct : 0;
                        var shQtr2Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 2);
                        x.shortTermQtr2Pct = shQtr2Pct != null ? shQtr2Pct.shortTermAbsencePct : 0;
                        var shQtr3Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 3);
                        x.shortTermQtr3Pct = shQtr3Pct != null ? shQtr3Pct.shortTermAbsencePct : 0;
                        var shQtr4Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 4);
                        x.shortTermQtr4Pct = shQtr4Pct != null ? shQtr4Pct.shortTermAbsencePct : 0;
                        var shQtr5Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 5);
                        x.shortTermAbsencePct = shQtr5Pct != null ? shQtr5Pct.shortTermAbsencePct : 0;
                        var shQtr6Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 6);
                        x.shortTermAbsencePctPY = shQtr6Pct != null ? shQtr6Pct.shortTermAbsencePct : 0;

                        var lgQtr1Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 1);
                        x.longTermQtr1Pct = lgQtr1Pct != null ? lgQtr1Pct.longTermAbsencePct : 0;
                        var lgQtr2Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 2);
                        x.longTermQtr2Pct = lgQtr2Pct != null ? lgQtr2Pct.longTermAbsencePct : 0;
                        var lgQtr3Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 3);
                        x.longTermQtr3Pct = lgQtr3Pct != null ? lgQtr3Pct.longTermAbsencePct : 0;
                        var lgQtr4Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 4);
                        x.longTermQtr4Pct = lgQtr4Pct != null ? lgQtr4Pct.longTermAbsencePct : 0;
                        var lgQtr5Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 5);
                        x.longTermAbsencePct = lgQtr5Pct != null ? lgQtr5Pct.longTermAbsencePct : 0;
                        var lgQtr6Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 6);
                        x.longTermAbsencePctPY = lgQtr6Pct != null ? lgQtr6Pct.longTermAbsencePct : 0;

                        var ovQtr1Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 1);
                        x.overallQtr1Pct = ovQtr1Pct != null ? ovQtr1Pct.overallAbsencePct : 0;
                        var ovQtr2Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 2);
                        x.overallQtr2Pct = ovQtr2Pct != null ? ovQtr2Pct.overallAbsencePct : 0;
                        var ovQtr3Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 3);
                        x.overallQtr3Pct = ovQtr3Pct != null ? ovQtr3Pct.overallAbsencePct : 0;
                        var ovQtr4Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 4);
                        x.overallQtr4Pct = ovQtr4Pct != null ? ovQtr4Pct.overallAbsencePct : 0;
                        var ovQtr5Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 5);
                        x.overallAbsencePct = ovQtr5Pct != null ? ovQtr5Pct.overallAbsencePct : 0;
                        var ovQtr6Pct = madqad.FirstOrDefault(y => y.absAggrQuarter == 6);
                        x.overallAbsencePctPY = ovQtr6Pct != null ? ovQtr6Pct.overallAbsencePct : 0;

                        x.isValidDataRow = madqad.Count > 0;
                    });
                }
                //convert it to JArray
                JArray dataArray = isYearlySetupEnabled ? JArray.FromObject(mrAllDepQuartsAbsenceData) : JArray.FromObject(mrAllOrgsAbsenceData);

                if (dataArray.Any())
                {
                    Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                    string numberTypeAmount = numberFormats["amount"].LangText;
                    string numberTypePercentage2 = numberFormats["percentage_2_decimal"].LangText;

                    string userDefinedCol = string.Empty;
                    if (isYearlySetupEnabled)
                    {
                        tableDef = _docTableConfig.GetTableDef(user, "MrYearlyAbsSa");
                    }
                    else
                    {
                        tableDef = _docTableConfig.GetTableDef(user, "MrAbsSa");
                    }
                    publishHelper.StartTableOrientation(tableDef);
                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_absencegrid_per_SA"].LangText, _context, true);
                    publishHelper.StartTable("AbsenceDataGridPerSA", null, null, tableDef);

                    var defaultColumn = new List<string>();
                    foreach (var item in tableDef.ColumnDefinitions)
                    {
                        defaultColumn.Add(item.ColumnId);
                    }

                    List<string> colsForSubColHeads = new List<string>();
                    foreach (ColumnDefinition cDef in tableDef.ColumnDefinitions.Where(x => x.IsActive).ToList())
                    {
                        colsForSubColHeads.Add(cDef.ColumnId);
                    }

                    ColumnDetails cellTemplate;

                    int set1HdrColCount = isYearlySetupEnabled ? 3 : 6;
                    int set2HdrColCount = isYearlySetupEnabled ? 3 : 6;
                    int set3HdrColCount = isYearlySetupEnabled ? 0 : 6;

                    List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                    List<string> lstColumnDataDetails = new List<string>();
                    cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    cellTemplate.Column = 1;
                    lstColumnDetails.Add(cellTemplate);
                    lstColumnDataDetails.Add(string.Empty);

                    if (set1HdrColCount > 0)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Column = set1HdrColCount;
                        cellTemplate.underlineText = true;
                        lstColumnDetails.Add(cellTemplate);
                        if (isYearlySetupEnabled)
                        {
                            lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_group_title"].LangText + " " + budgetYear);
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_title_shorttermabs"].LangText);
                        }
                    }

                    if (set2HdrColCount > 0)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Column = set2HdrColCount;
                        cellTemplate.underlineText = true;
                        lstColumnDetails.Add(cellTemplate);
                        if (isYearlySetupEnabled)
                        {
                            lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_group_title"].LangText + " " + (budgetYear - 1));
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_title_longtermabs"].LangText);
                        }
                    }

                    if (set3HdrColCount > 0)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Column = set3HdrColCount;
                        cellTemplate.underlineText = true;
                        lstColumnDetails.Add(cellTemplate);
                        if (!isYearlySetupEnabled)
                        {
                            lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_title_totalabs"].LangText);
                        }
                    }

                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading1);

                    count = 0;
                    string suffix = string.Empty;
                    lstColumnDetails = new List<ColumnDetails>();
                    lstColumnDataDetails = new List<string>();
                    builder.CellFormat.ClearFormatting();
                    builder.RowFormat.HeadingFormat = true;

                    foreach (var c in defaultColumn)
                    {
                        count++;
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;

                        if (isYearlySetupEnabled)
                        {
                            if (!string.IsNullOrEmpty(userDefinedCol))
                            {
                                cellTemplate.PreferredWidth = 17.5;
                            }
                            else if (count == 1)
                            {
                                cellTemplate.PreferredWidth = 30;
                            }
                            else
                            {
                                cellTemplate.PreferredWidth = 11.7;
                            }
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(userDefinedCol))
                            {
                                cellTemplate.PreferredWidth = 6.5;
                            }
                            else if (count == 1)
                            {
                                cellTemplate.PreferredWidth = 15;
                            }
                            else
                            {
                                cellTemplate.PreferredWidth = 4.3;
                            }
                        }

                        cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;

                        lstColumnDetails.Add(cellTemplate);

                        if (isYearlySetupEnabled)
                        {
                            if (c.ToLower() == "shortTermAbsencePct".ToLower() || c.ToLower() == "shortTermAbsencePctPY".ToLower())
                            {
                                suffix = c;
                            }
                            else if (c.ToLower() == "longTermAbsencePct".ToLower() || c.ToLower() == "longTermAbsencePctPY".ToLower())
                            {
                                suffix = c;
                            }
                            else if (c.ToLower() == "overallAbsencePct".ToLower() || c.ToLower() == "overallAbsencePctPY".ToLower())
                            {
                                suffix = c;
                            }
                            else
                            {
                                suffix = "sa";
                            }

                            if (suffix == "sa")
                            {
                                lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_title_serv"].LangText);
                            }
                            else
                            {
                                lstColumnDataDetails.Add(langStrMrDoc["coltexty_" + suffix].LangText);
                            }
                        }
                        else
                        {
                            if (c.ToLower() == "shortTermAbsencePctPY".ToLower() || c.ToLower() == "longTermAbsencePctPY".ToLower() || c.ToLower() == "overallAbsencePctPY".ToLower())
                            {
                                lstColumnDataDetails.Add(langStrMr["MR_Doc_" + c.ToLower()].LangText + " " + (budgetYear - 1));
                            }
                            else
                            {
                                if (c.ToLower() == "orgId".ToLower())
                                {
                                    suffix = "sa";
                                }
                                else if (c.ToLower() == "shortTermAbsencePct".ToLower())
                                {
                                    suffix = "shortTermAbsencePct";
                                }
                                else if (c.ToLower() == "longTermAbsencePct".ToLower())
                                {
                                    suffix = "longTermAbsencePct";
                                }
                                else if (c.ToLower() == "overallAbsencePct".ToLower())
                                {
                                    suffix = "overallAbsencePct";
                                }
                                else
                                {
                                    suffix = "kv";
                                }

                                if (suffix == "kv")
                                {
                                    int qtrNum = int.Parse((c.Substring(c.Length - 4)).Substring(0, 1));
                                    lstColumnDataDetails.Add(qtrNum.ToString() + "." + langStrMr["MR_Doc_absSA_col_title_" + suffix].LangText);
                                }
                                else if (suffix == "sa")
                                {
                                    lstColumnDataDetails.Add(langStrMr["MR_Doc_absSA_col_title_serv"].LangText);
                                }
                                else
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                    lstColumnDataDetails.Add(langStrMrDoc["coltext_" + suffix].LangText);
                                }
                            }
                        }
                    }

                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
                    builder.RowFormat.HeadingFormat = false;

                    // Get the PropertyInfo object by passing the property name.
                    PropertyInfo[] myPropInfo = typeof(MonthlyRptQuartAbsPeriodDeptHelper).GetProperties();
                    // insert data for the document

                    foreach (var d in dataArray)
                    {
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        //for each column to display insert record
                        count = 0;
                        foreach (var col in defaultColumn)
                        {
                            count++;
                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);

                            cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                            cellTemplate.WrapText = true;

                            if ((string)d["absAggrServId"] == "-1")
                            {
                                cellTemplate.FontColor = FramsiktColors.TotalColor;
                                cellTemplate.IsBold = true;
                            }

                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            lstColumnDetails.Add(cellTemplate);

                            if (isYearlySetupEnabled)
                            {
                                if (col.ToLower() == "overallAbsencePct".ToLower() || col.ToLower() == "overallAbsencePctPY".ToLower())
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                }
                            }
                            else
                            {
                                if (col.ToLower() == "shortTermAbsencePct".ToLower() || col.ToLower() == "longTermAbsencePct".ToLower() || col.ToLower() == "overallAbsencePct".ToLower())
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                }
                            }

                            string name = string.Empty;
                            if (col.ToLower() == "orgId".ToLower())
                            {
                                name = "absAggrServName";
                            }
                            else
                            {
                                name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                            }

                            //apply number formatting of for number column
                            if (name.ToLower() == "absAggrServName".ToLower())
                            {
                                var valuecy = (string)d[name];
                                lstColumnDataDetails.Add(valuecy);
                            }
                            else
                            {
                                decimal valuecy = 0;

                                bool success = decimal.TryParse(d[name].ToString(), out valuecy);
                                if (valuecy < 0)
                                    lstColumnDataDetails.Add("-");
                                else
                                    lstColumnDataDetails.Add(valuecy.ToString(numberTypePercentage2));
                            }
                        }
                        if ((bool)d["isValidDataRow"])
                        {
                            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                        }

                        builder.RowFormat.HeadingFormat = false;
                    }
                    publishHelper.EndTable();
                    publishHelper.EndTableOrientation();
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence data per Service");
            }
        }



        private void InsertAbsenceTxtPerServiceArea(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, string orgId, bool isTxtStat)
        {
            UserData userDetails = _utility.GetUserDetails(user);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            int orgLevel = 1; // This is at tenant level.

            bool isFeatureEnabled = _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text).GetAwaiter().GetResult();
            if (isFeatureEnabled)
            {
                TextDescriptionHelper input = new()
                {
                    ForecastPeriod = forecastPeriod,
                    BudgetYear = budgetYear,
                    OrgId = orgId,
                    OrgLevel = orgLevel,
                    ServiceId = null,
                    DescriptionType = MonthlyReportTextBoxType.AbsenceStatus,
                    AttributeId = string.Empty,
                    OrgName = null,
                    ServiceName = null,
                    AttributeName = string.Empty,
                };

                var absenceStatus = _mrfUtility.GetDescriptionText(user, true, input).GetAwaiter().GetResult();
                input.DescriptionType = MonthlyReportTextBoxType.AbsenceActionPlan;
                var absenceActionPlan = _mrfUtility.GetDescriptionText(user,true, input).GetAwaiter().GetResult();

                tmr_period_setup tmrpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == forecastPeriod);

                //Adding the desriptions there.
                if (absenceStatus != null && !string.IsNullOrEmpty(absenceStatus.Data) && isTxtStat &&
                (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceSummery))
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                    publishHelper.InsertDescriptionCk5(absenceStatus.Data, user);
                }

                if (absenceActionPlan != null && !string.IsNullOrEmpty(absenceActionPlan.Data) && !isTxtStat &&
                    (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceActionPlan))
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                    publishHelper.InsertDescriptionCk5(absenceActionPlan.Data, user);
                }
            }
            else
            {
                // get the absenceStatus and absenceActionPlan from blob
                   dynamic absenceStatus = _mrfUtility.GetMrTextDescriptions(user, true, budgetYear, forecastPeriod, orgId, orgLevel, "AbsenceStatus", null, null, null);
                   dynamic absenceActionPlan = _mrfUtility.GetMrTextDescriptions(user, true, budgetYear, forecastPeriod, orgId, orgLevel, "AbsenceActionPlan", null, null, null);

                tmr_period_setup tmrpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == forecastPeriod);

                //Adding the desriptions there.
                if (absenceStatus != null && !string.IsNullOrEmpty(absenceStatus.data.ToString()) && isTxtStat &&
                (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceSummery))
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                    publishHelper.InsertDescriptionCk5(absenceStatus.data.ToString(), user);
                }

                if (absenceActionPlan != null && !string.IsNullOrEmpty(absenceActionPlan.data.ToString()) && !isTxtStat &&
                    (tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAll || tmrpsRec.absence_desc_type == (int)clsConstants.sickLeaveTextBoxType.showAbsenceActionPlan))
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                    publishHelper.InsertDescriptionCk5(absenceActionPlan.data.ToString(), user);
                }

            }

        }



        private void InsertAbsenceGridDataForService(string user, DocumentBuilder builder, string orgID, string serviceId, int budgetYear,
            int forecastPeriod, int orgLevel, int serviceLevel)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tableDef;
            int absDescForePeriod = forecastPeriod;

            if (!_monthlyReportAbsence.SneakPeakAbsDataExists(user, budgetYear))
                return;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);
                bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                if (isYearlySetupEnabled)
                {
                    tableDef = _docTableConfig.GetTableDef(user, "MrYearlyAbsenceDisplay");
                }
                else
                {
                    tableDef = _docTableConfig.GetTableDef(user, "MrAbsence");
                }

                publishHelper.StartTableOrientation(tableDef);
                publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_absence_grid"].LangText, _context, true);

                publishHelper.StartTable("AbsenceDataGrid", null, null, tableDef);

                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportDocs = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "DocConfig");

                string[] monthFields = new string[] { ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_january_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_february_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_march_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_april_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_may_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_june_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_july_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_august_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_september_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_october_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_november_short3text")).Value).LangText,
                                                      ((_languageStringsExportCommon.FirstOrDefault(v => v.Key == "month_december_short3text")).Value).LangText };

                var tenantDbContext = _utility.GetTenantDBContext();
                var data = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, orgLevel, serviceLevel).ToList();
                var functionsInServiceID = data.Where(x => x.ServiceID == serviceId).Where(y => y.FunctionCode != null).Select(z => z.FunctionCode).Distinct().ToList();
                var departmentsList = data.Where(x => x.OrgID == orgID).Where(y => y.DepartmentCode != null).Select(z => z.DepartmentCode).Distinct().ToList();

                var displayOrgLevel = _utility.GetParameterValue(user, "DOC_MONTHLY_DISPLAY_LEVEL");

                var configOrgLevel = string.IsNullOrEmpty(displayOrgLevel) ? 2 :
                                     displayOrgLevel == "org_id_1" ? 1 :
                                     displayOrgLevel == "org_id_2" ? 2 :
                                     displayOrgLevel == "org_id_3" ? 3 :
                                     displayOrgLevel == "org_id_4" ? 4 :
                                     displayOrgLevel == "org_id_5" ? 5 : 2;

                orgLevel = orgLevel == -1 ? configOrgLevel : orgLevel;
                var orgIDList = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, configOrgLevel, serviceLevel).Where(x => x.ServiceID == serviceId).Select(x => x.OrgID).Distinct().ToList();
                decimal total = 0;

                List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                List<string> lstColumnDataDetails = new List<string>();
                ColumnDetails cellTemplate;
                Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypePercentage2 = numberFormats["percentage_2_decimal"].LangText;
                lstColumnDetails = new List<ColumnDetails>();
                lstColumnDataDetails = new List<string>();

                builder.CellFormat.ClearFormatting();
                builder.RowFormat.HeadingFormat = true;
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Left;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "AbsenceTittle";
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "AbsenceShortTerm";
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "AbsenceOfLongTerm";
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "TotalAbsence";
                lstColumnDetails.Add(cellTemplate);

                foreach (ColumnDefinition cDef in tableDef.ColumnDefinitions)
                {
                    lstColumnDataDetails.Add(cDef.ColumnName);
                }

                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

                builder.RowFormat.HeadingFormat = false;

                int count;
                int numAggrMonths = 3;
                int setupPeriod = forecastPeriod;
                tmr_period_setup tpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(
                                                                                  x => x.fk_tenant_id == userDetails.tenant_id
                                                                                    && x.forecast_period == forecastPeriod);

                if (tpsRec != null)
                {
                    setupPeriod = tpsRec.sickleave_period;
                    budgetYear = setupPeriod / 100;
                    if ((int)tpsRec.abs_aggr_type > 0)
                        numAggrMonths = numAggrMonths + 1;
                }

                int currentMonth = setupPeriod - budgetYear * 100;
                int preYrQrtPlusQuartsInYear = 1 + ((currentMonth % numAggrMonths == 0) ? currentMonth / numAggrMonths : currentMonth / numAggrMonths + 1);
                int quater = preYrQrtPlusQuartsInYear - 1;

                MonthlyRptQuartAbsPeriodDeptHelper quater1data = new MonthlyRptQuartAbsPeriodDeptHelper();
                MonthlyRptQuartAbsPeriodDeptHelper quater2data = new MonthlyRptQuartAbsPeriodDeptHelper();

                bool noAbsDataForPrvYr = true;
                // print data for current + 4 prvious years
                for (int i = 4; i >= 0; i--)
                {
                    int fcastPeriodToGetFullYearData = (budgetYear - i) * 100 + 12;

                    if (i == 0)
                        fcastPeriodToGetFullYearData = setupPeriod;

                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrQuarterlyAbsenceData = _monthlyReportAbsence.FetchQuarterlyAbsenceDataforService(user, budgetYear - i, functionsInServiceID, departmentsList, fcastPeriodToGetFullYearData, numAggrMonths, forecastPeriod);
                    count = mrQuarterlyAbsenceData.Count;
                    if (count > 0)
                    {
                        noAbsDataForPrvYr = false;
                        if (i == 1)
                        {
                            // get quaterly data for last year
                            quater1data = (from d in mrQuarterlyAbsenceData
                                           where d.absAggrQuarter == quater && d.absAggrServId == "-1"
                                           select d).FirstOrDefault();
                        }

                        if (i == 0)
                        {
                            // get quaterly data for current year
                            quater2data = (from d in mrQuarterlyAbsenceData
                                           where d.absAggrQuarter == quater && d.absAggrServId == "-1"
                                           select d).FirstOrDefault();
                        }

                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }

                        lstColumnDetails.Add(cellTemplate);

                        // print data for absence
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Yearly_absence_text"].LangText + " " + (budgetYear - i));

                        if (mrQuarterlyAbsenceData[count - 1].shortTermAbsencePct < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(mrQuarterlyAbsenceData[count - 1].shortTermAbsencePct.ToString(numberTypePercentage2));

                        if (mrQuarterlyAbsenceData[count - 1].longTermAbsencePct < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(mrQuarterlyAbsenceData[count - 1].longTermAbsencePct.ToString(numberTypePercentage2));

                        if (mrQuarterlyAbsenceData[count - 1].overallAbsencePct < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(mrQuarterlyAbsenceData[count - 1].overallAbsencePct.ToString(numberTypePercentage2));

                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                    else
                    {
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Yearly_absence_text"].LangText + " " + (budgetYear - i));
                        lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));

                        if (!noAbsDataForPrvYr)
                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                }

                bool isAnnualReport = (setupPeriod % 100 == 12) && _mrfUtility.IsYearlyReportSetup(user, setupPeriod);

                string lastMonAbsParam = _utility.GetParameterValue(user, "MRDOC_SA_ABSENCE_DISP_LASTMONTH");
                bool insertLastMonAbsData = string.IsNullOrEmpty(lastMonAbsParam) ? false : lastMonAbsParam.ToLower() == "true";
                int lastReportedPeriod = _mrfUtility.GetLastReportedPeriod(user, setupPeriod);
                int lastReportedPeriodDiff = setupPeriod - lastReportedPeriod;
                MonthlyRptQuartAbsPeriodDeptHelper? lastRptMonthData = _monthlyReportAbsence.FetchMonthlyAbsenceData(user, budgetYear, departmentsList, functionsInServiceID, 3, setupPeriod, lastReportedPeriodDiff, forecastPeriod);

                //Print Quarterly data for previous, current year & Monthly Data for last month. If not in AnnualReport Mode, total 3 additional rows, else 1.
                int numAdditionalRows = !isAnnualReport ? 3 : 1;
                int endRow = insertLastMonAbsData ? 0 : 1;
                // Print Quaterly data for previous and current year if not in AnnualReport Mode
                for (int j = numAdditionalRows - 1; j >= endRow; j--)
                {
                    lstColumnDetails = new List<ColumnDetails>();
                    lstColumnDataDetails = new List<string>();

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Left;
                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                    }
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                    cellTemplate.Alignment = ParagraphAlignment.Right;
                    cellTemplate.WrapText = true;
                    if (j == 1)
                    {
                        cellTemplate.TopBorder = 0;
                        cellTemplate.BottomBorder = 1;
                    }
                    if (j == 0)
                    {
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                    }
                    lstColumnDetails.Add(cellTemplate);

                    if (j == 0)
                    {
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_LastMonth_absence_text"].LangText + " " + monthFields[lastReportedPeriod % 100 - 1] + " " + (budgetYear));
                    }
                    else
                    {
                        if (numAggrMonths == 3)
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Quarterly_absence_text_" + quater].LangText + " " + (budgetYear - j + 1));
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Tertially_absence_text_" + quater].LangText + " " + (budgetYear - j + 1));
                        }
                    }

                    //print previous year quater data
                    if (j == 2)
                    {
                        if (quater1data != null)
                        {
                            if (quater1data.shortTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater1data.shortTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater1data.longTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater1data.longTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater1data.overallAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater1data.overallAbsencePct.ToString(numberTypePercentage2));

                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                        }
                    }
                    // print data for current year last quater
                    if (j == 1)
                    {
                        if (quater2data != null)
                        {
                            if (quater2data.shortTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater2data.shortTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater2data.longTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater2data.longTermAbsencePct.ToString(numberTypePercentage2));

                            if (quater2data.overallAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(quater2data.overallAbsencePct.ToString(numberTypePercentage2));
                        }
                        else
                        {
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                        }
                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                    // print data for last month
                    if (j == 0)
                    {
                        if (lastRptMonthData != null)
                        {
                            if (lastRptMonthData.shortTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(lastRptMonthData.shortTermAbsencePct.ToString(numberTypePercentage2));

                            if (lastRptMonthData.longTermAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(lastRptMonthData.longTermAbsencePct.ToString(numberTypePercentage2));

                            if (lastRptMonthData.overallAbsencePct < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(lastRptMonthData.overallAbsencePct.ToString(numberTypePercentage2));
                        }
                        else
                        {
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                            lstColumnDataDetails.Add((Math.Round(total, 2).ToString(numberTypePercentage2)));
                        }
                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                    }
                }

                publishHelper.EndTable();
                publishHelper.EndTableOrientation();
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence Grid data");
            }
        }



        private async Task InsertAbsenceTxtForService(string user, DocumentBuilder builder, string orgID, string serviceId, int budgetYear,
            int forecastPeriod, int orgLevel, int serviceLevel, bool isStatxtAbs)
        {
            if (await _utility.IsFeatureEnabled(FeatureFlags.ck_Editor))
            {
                serviceId = await _utility.RefineServiceIdValue(serviceId, user);
            }

            if (await _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text))
            {
                InsertAbsenceTxtForServiceNewFetch(user, builder, orgID, serviceId, budgetYear, forecastPeriod, orgLevel, serviceLevel, isStatxtAbs);
            }
            else
            {
                InsertAbsenceTxtForServiceLogic(user, builder, orgID, serviceId, budgetYear, forecastPeriod, orgLevel, serviceLevel, isStatxtAbs);
            }
        }



        private void InsertAbsenceGridDataForAllServiceArea(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tableDef;

            if (!_monthlyReportAbsence.SneakPeakAbsDataExists(user, budgetYear))
                return;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);
                bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                if (isYearlySetupEnabled == true)
                {
                    tableDef = _docTableConfig.GetTableDef(user, "MrYearlyAbsenceDisplay");
                }
                else
                {
                    tableDef = _docTableConfig.GetTableDef(user, "MrAbsence");
                }

                publishHelper.StartTableOrientation(tableDef);
                publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_absence_grid"].LangText, _context, true);

                publishHelper.StartTable("AbsenceGridDataForAllServiceArea", null, null, tableDef);

                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportDocs = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "DocConfig");

                // get all service Area ids
                var serviceAreaIds = _utility.GetServiceAreaUnit(orgVersionContent, user).Select(x => x.ServiceAreaID).Distinct();

                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

                decimal yearlytotalabsHrsShort = 0;
                decimal yearlytotalwrkHrsShort = 0;
                decimal yearlytotalabsHrsLong = 0;
                decimal yearlytotalwrkHrsLong = 0;
                decimal yearlytotalabsHrsOvrl = 0;
                decimal yearlytotalawrkHrsOvrl = 0;

                decimal quarterly_perious_totalabsHrsShort = 0;
                decimal quarterly_perious_totalwrkHrsShort = 0;
                decimal quarterly_perious_totalabsHrsLong = 0;
                decimal quarterly_perious_totalwrkHrsLong = 0;
                decimal quarterly_perious_totalabsHrsOvrl = 0;
                decimal quarterly_perious_totalawrkHrsOvrl = 0;

                decimal quarterly_current_totalabsHrsShort = 0;
                decimal quarterly_current_totalwrkHrsShort = 0;
                decimal quarterly_current_totalabsHrsLong = 0;
                decimal quarterly_current_totalwrkHrsLong = 0;
                decimal quarterly_current_totalabsHrsOvrl = 0;
                decimal quarterly_current_totalawrkHrsOvrl = 0;

                List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                List<string> lstColumnDataDetails = new List<string>();
                ColumnDetails cellTemplate;
                Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypePercentage2 = numberFormats["percentage_2_decimal"].LangText;
                lstColumnDetails = new List<ColumnDetails>();
                lstColumnDataDetails = new List<string>();

                builder.CellFormat.ClearFormatting();
                builder.RowFormat.HeadingFormat = true;
                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Left;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "AbsenceTittle";
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "AbsenceShortTerm";
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "AbsenceOfLongTerm";
                lstColumnDetails.Add(cellTemplate);

                cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                cellTemplate.Alignment = ParagraphAlignment.Right;
                cellTemplate.WrapText = true;
                cellTemplate.BottomBorder = 1;
                //cellTemplate.ColumnType = "TotalAbsence";
                lstColumnDetails.Add(cellTemplate);

                foreach (ColumnDefinition cDef in tableDef.ColumnDefinitions)
                    lstColumnDataDetails.Add(cDef.ColumnName);

                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

                builder.RowFormat.HeadingFormat = false;

                int count;
                int numAggrMonths = 3;
                int setupPeriod = forecastPeriod;
                tmr_period_setup tpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                            && x.forecast_period == forecastPeriod);

                if (tpsRec != null)
                {
                    setupPeriod = tpsRec.sickleave_period;
                    budgetYear = setupPeriod / 100;
                    if ((int)tpsRec.abs_aggr_type > 0)
                    {
                        numAggrMonths = numAggrMonths + 1;
                    }
                }

                int currentMonth = setupPeriod - budgetYear * 100;
                int preYrQrtPlusQuartsInYear = 1 + ((currentMonth % numAggrMonths == 0) ? currentMonth / numAggrMonths : currentMonth / numAggrMonths + 1);
                int quater = preYrQrtPlusQuartsInYear - 1;

                MonthlyRptQuartAbsPeriodDeptHelper quater1data = new MonthlyRptQuartAbsPeriodDeptHelper();
                MonthlyRptQuartAbsPeriodDeptHelper quater2data = new MonthlyRptQuartAbsPeriodDeptHelper();

                // print data for current year + four previus year
                for (int i = 4; i >= 0; i--)
                {
                    int fcastPeriodToGetFullYearData = (budgetYear - i) * 100 + 12;

                    if (i == 0)
                        fcastPeriodToGetFullYearData = setupPeriod;

                    List<clsOrgIdAndDepartments> depsInServ = new List<clsOrgIdAndDepartments>();
                    List<string> deplist = orgVersionContent.lstOrgHierarchy.Select(x => x.fk_department_code).Distinct().ToList();

                    // get absence grid data
                    List<MonthlyRptQuartAbsPeriodDeptHelper> mrQuarterlyAbsenceData = _monthlyReportAbsence.FetchQuarterlyAbsenceData(user, budgetYear - i, deplist, null, 0, fcastPeriodToGetFullYearData, numAggrMonths, forecastPeriod, depsInServ, 0, string.Empty, false, string.Empty);

                    count = mrQuarterlyAbsenceData.Count;
                    if (count > 0)
                    {
                        if (i == 1)
                        {
                            // fectch the data for previous year last quarter data
                            quater1data = (from d in mrQuarterlyAbsenceData
                                           where d.absAggrQuarter == quater && d.absAggrServId == "-1"
                                           select d).FirstOrDefault();
                            if (quater1data != null)
                            {
                                quarterly_perious_totalabsHrsShort = quarterly_perious_totalabsHrsShort + quater1data.absHrsShort;
                                quarterly_perious_totalwrkHrsShort = quarterly_perious_totalwrkHrsShort + quater1data.wrkHrsShort;
                                quarterly_perious_totalabsHrsLong = quarterly_perious_totalabsHrsLong + quater1data.absHrsLong;
                                quarterly_perious_totalwrkHrsLong = quarterly_perious_totalwrkHrsLong + quater1data.wrkHrsLong;
                                quarterly_perious_totalabsHrsOvrl = quarterly_perious_totalabsHrsOvrl + quater1data.absHrsOvrl;
                                quarterly_perious_totalawrkHrsOvrl = quarterly_perious_totalawrkHrsOvrl + quater1data.wrkHrsOvrl;
                            }
                        }

                        if (i == 0)
                        {
                            // fectch the data for current year last quarter data
                            quater2data = (from d in mrQuarterlyAbsenceData
                                           where d.absAggrQuarter == quater && d.absAggrServId == "-1"
                                           select d).FirstOrDefault();

                            if (quater2data != null)
                            {
                                quarterly_current_totalabsHrsShort = quarterly_current_totalabsHrsShort + quater2data.absHrsShort;
                                quarterly_current_totalwrkHrsShort = quarterly_current_totalwrkHrsShort + quater2data.wrkHrsShort;
                                quarterly_current_totalabsHrsLong = quarterly_current_totalabsHrsLong + quater2data.absHrsLong;
                                quarterly_current_totalwrkHrsLong = quarterly_current_totalwrkHrsLong + quater2data.wrkHrsLong;
                                quarterly_current_totalabsHrsOvrl = quarterly_current_totalabsHrsOvrl + quater2data.absHrsOvrl;
                                quarterly_current_totalawrkHrsOvrl = quarterly_current_totalawrkHrsOvrl + quater2data.wrkHrsOvrl;
                            }
                        }

                        // add the short long and overl absence data
                        MonthlyRptQuartAbsPeriodDeptHelper yearlyAggrData = (from d in mrQuarterlyAbsenceData
                                                                             where d.absAggrQuarter == 5 && d.absAggrServId == "-1"
                                                                             select d).FirstOrDefault();

                        if (yearlyAggrData != null)
                        {
                            yearlytotalabsHrsShort = yearlytotalabsHrsShort + yearlyAggrData.absHrsShort;
                            yearlytotalwrkHrsShort = yearlytotalwrkHrsShort + yearlyAggrData.wrkHrsShort;
                            yearlytotalabsHrsLong = yearlytotalabsHrsLong + yearlyAggrData.absHrsLong;
                            yearlytotalwrkHrsLong = yearlytotalwrkHrsLong + yearlyAggrData.wrkHrsLong;
                            yearlytotalabsHrsOvrl = yearlytotalabsHrsOvrl + yearlyAggrData.absHrsOvrl;
                            yearlytotalawrkHrsOvrl = yearlytotalawrkHrsOvrl + yearlyAggrData.wrkHrsOvrl;
                        }
                        // }

                        //}

                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (i == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        //calculate percentage and display
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Yearly_absence_text"].LangText + " " + (budgetYear - i));

                        if (yearlytotalabsHrsShort < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(Math.Round((yearlytotalwrkHrsShort == 0) ? 0 : (yearlytotalabsHrsShort / yearlytotalwrkHrsShort) * 100, 2).ToString(numberTypePercentage2));

                        if (yearlytotalabsHrsLong < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(Math.Round((yearlytotalwrkHrsLong == 0) ? 0 : (yearlytotalabsHrsLong / yearlytotalwrkHrsLong) * 100, 2).ToString(numberTypePercentage2));

                        if (yearlytotalabsHrsOvrl < 0)
                            lstColumnDataDetails.Add("-");
                        else
                            lstColumnDataDetails.Add(Math.Round((yearlytotalawrkHrsOvrl == 0) ? 0 : (yearlytotalabsHrsOvrl / yearlytotalawrkHrsOvrl) * 100, 2).ToString(numberTypePercentage2));

                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);

                        //after printing data for year reset vairable for next year
                        yearlytotalabsHrsShort = 0;
                        yearlytotalwrkHrsShort = 0;
                        yearlytotalabsHrsLong = 0;
                        yearlytotalwrkHrsLong = 0;
                        yearlytotalabsHrsOvrl = 0;
                        yearlytotalawrkHrsOvrl = 0;
                    }
                }

                bool isAnnualReport = (setupPeriod % 100 == 12) && _mrfUtility.IsYearlyReportSetup(user, setupPeriod);

                // print quaterly result for current and previous year if not in AnnualReport Mode
                if (!isAnnualReport)
                {
                    for (int j = 1; j >= 0; j--)
                    {
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Left;
                        cellTemplate.WrapText = true;
                        if (j == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (j == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (j == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        if (j == 0)
                        {
                            cellTemplate.TopBorder = 0;
                            cellTemplate.BottomBorder = 1;
                        }
                        lstColumnDetails.Add(cellTemplate);

                        if (numAggrMonths == 3)
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Quarterly_absence_text_" + quater].LangText + " " + (budgetYear - j));
                        }
                        else
                        {
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_Tertially_absence_text_" + quater].LangText + " " + (budgetYear - j));
                        }

                        // print privoius year quater data
                        if (j == 1)
                        {
                            if (quarterly_perious_totalabsHrsShort < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(Math.Round((quarterly_perious_totalabsHrsShort == 0) ? 0 : (quarterly_perious_totalabsHrsShort / quarterly_perious_totalwrkHrsShort) * 100, 2).ToString(numberTypePercentage2));

                            if (quarterly_perious_totalabsHrsLong < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(Math.Round((quarterly_perious_totalwrkHrsLong == 0) ? 0 : (quarterly_perious_totalabsHrsLong / quarterly_perious_totalwrkHrsLong) * 100, 2).ToString(numberTypePercentage2));

                            if (quarterly_perious_totalabsHrsOvrl < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(Math.Round((quarterly_perious_totalawrkHrsOvrl == 0) ? 0 : (quarterly_perious_totalabsHrsOvrl / quarterly_perious_totalawrkHrsOvrl) * 100, 2).ToString(numberTypePercentage2));

                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                            }
                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                        }
                        // print current year quater data
                        if (j == 0)
                        {
                            if (quarterly_current_totalabsHrsShort < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(Math.Round((quarterly_current_totalwrkHrsShort == 0) ? 0 : (quarterly_current_totalabsHrsShort / quarterly_current_totalwrkHrsShort) * 100, 2).ToString(numberTypePercentage2));

                            if (quarterly_current_totalabsHrsLong < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(Math.Round((quarterly_current_totalwrkHrsLong == 0) ? 0 : (quarterly_current_totalabsHrsLong / quarterly_current_totalwrkHrsLong) * 100, 2).ToString(numberTypePercentage2));

                            if (quarterly_current_totalabsHrsOvrl < 0)
                                lstColumnDataDetails.Add("-");
                            else
                                lstColumnDataDetails.Add(Math.Round((quarterly_current_totalawrkHrsOvrl == 0) ? 0 : (quarterly_current_totalabsHrsOvrl / quarterly_current_totalawrkHrsOvrl) * 100, 2).ToString(numberTypePercentage2));

                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                            }
                            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, false);
                        }
                    }
                }
                publishHelper.EndTable();
                publishHelper.EndTableOrientation();
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert data for Absencedata");
            }
        }



        private async Task InsertAbsenceTxtAllServiceArea(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, bool isStatusDesc)
        {
            if(await _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text))
            {
                InsertAbsenceTxtAllServiceAreaNewFetch(user, builder, budgetYear, forecastPeriod, isStatusDesc);
            }
            else
            {
                InsertAbsenceTxtAllServiceAreaLogic(user, builder, budgetYear, forecastPeriod, isStatusDesc);
            }
        }



        private void InsertAbsenceTxtAllServiceAreaLogic(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, bool isStatusDesc)
        {
            UserData userDetails = _utility.GetUserDetails(user);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            List<string> serviceIdWithAll = new List<string> { "-1", "ALL", "all" };
            string orgIdOne = tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefault().org_id_1;

            List<string> descTypes = new List<string> { nameof(MonthlyReportTextBoxType.AbsenceActionPlan).ToLower(), nameof(MonthlyReportTextBoxType.AbsenceStatus).ToLower() };
            List<tco_monthrep_texts> absDesCityList = tenantDbContext.tco_monthrep_texts.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                            && x.org_level == "1"
                                                                            && x.org_id == orgIdOne
                                                                            && x.budget_year == budgetYear
                                                                            && x.forecast_period == forecastPeriod
                                                                            && x.acc_group_value == "NA").ToList();
            tco_monthrep_texts absDescCityLevel;

            if (absDesCityList.Count > 1)
                absDescCityLevel = absDesCityList.FirstOrDefault(x => serviceIdWithAll.Contains(x.service_id));
            else if (absDesCityList.Count == 1)
                absDescCityLevel = absDesCityList.FirstOrDefault();
            else
                absDescCityLevel = null;

            if (absDescCityLevel != null)
            {
                if (!string.IsNullOrEmpty(absDescCityLevel.absencedisply_status_desc) && isStatusDesc)
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                    publishHelper.InsertDescriptionCk5(absDescCityLevel.absencedisply_status_desc, user);
                }
                if (!string.IsNullOrEmpty(absDescCityLevel.absencedisply_actionplan_desc) && !isStatusDesc)
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                    publishHelper.InsertDescriptionCk5(absDescCityLevel.absencedisply_actionplan_desc, user);
                }
            }
        }



        private void InsertAbsenceTxtAllServiceAreaNewFetch(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, bool isStatusDesc)
        {
            UserData userDetails = _utility.GetUserDetails(user);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            List<string> serviceIdWithAll = new List<string> { "-1", "ALL", "all" };
            List<string> serviceIdWithAllNew = new();
            if (_utility.IsFeatureEnabled(FeatureFlags.ck_Editor).GetAwaiter().GetResult())
            {
                foreach (var serviceIdItem in serviceIdWithAll)
                {
                    var serviceId = _utility.RefineServiceIdValue(serviceIdItem, user).GetAwaiter().GetResult();
                    serviceIdWithAllNew.Add(serviceId);
                }
                serviceIdWithAll = serviceIdWithAllNew.ToList();
            }
               
            string orgIdOne = tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefault().org_id_1;

            string descType = isStatusDesc ? nameof(MonthlyReportTextBoxType.AbsenceStatus) : nameof(MonthlyReportTextBoxType.AbsenceActionPlan);
            List<tco_monthrep_descriptions> absDesCityList = tenantDbContext.tco_monthrep_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                            && x.org_level == "1"
                                                                            && x.org_id == orgIdOne
                                                                            && x.budget_year == budgetYear
                                                                            && x.forecast_period == forecastPeriod
                                                                            && x.acc_group_value == "NA"
                                                                            && x.description_type.ToLower() == descType.ToLower()).ToList();
            tco_monthrep_descriptions absDescCityLevel;

            if (absDesCityList.Any())
            {
                absDescCityLevel = absDesCityList.FirstOrDefault(x => serviceIdWithAll.Contains(x.service_id));
            }
            else
            {
                absDescCityLevel = null;
            }

            if (absDescCityLevel != null)
            {
                if (!string.IsNullOrEmpty(absDescCityLevel.description_text) && isStatusDesc)
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                }
                if (!string.IsNullOrEmpty(absDescCityLevel.description_text) && !isStatusDesc)
                {
                    publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                }
                publishHelper.InsertDescriptionCk5(absDescCityLevel.description_text, user);
            }
        }



        private void InsertAbsenceTxtForServiceLogic(string user, DocumentBuilder builder, string orgID, string serviceId, int budgetYear,
            int forecastPeriod, int orgLevel, int serviceLevel, bool isStatxtAbs)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            int absDescForePeriod = forecastPeriod;
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            var displayOrgLevel = _utility.GetParameterValue(user, "DOC_MONTHLY_DISPLAY_LEVEL");

            int configOrgLevel = string.IsNullOrEmpty(displayOrgLevel) ? 2 :
                                 displayOrgLevel == "org_id_1" ? 1 :
                                 displayOrgLevel == "org_id_2" ? 2 :
                                 displayOrgLevel == "org_id_3" ? 3 :
                                 displayOrgLevel == "org_id_4" ? 4 :
                                 displayOrgLevel == "org_id_5" ? 5 : 2;

            orgLevel = orgLevel == -1 ? configOrgLevel : orgLevel;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                if (_utility.IsFeatureEnabled(FeatureFlags.ck_Editor).GetAwaiter().GetResult())
                {
                    serviceId = _utility.RefineServiceIdValue(serviceId, user).GetAwaiter().GetResult();
                }

                var orgIDList = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, configOrgLevel, serviceLevel).Where(x => x.ServiceID == serviceId).Select(x => x.OrgID).Distinct().ToList();

                var absenceDetails = tenantDbContext.tco_monthrep_texts.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                && x.service_id == serviceId
                                                                                && x.org_level == orgLevel.ToString()
                                                                                && x.budget_year == budgetYear
                                                                                && x.forecast_period == absDescForePeriod
                                                                                && x.acc_group_value == "NA"
                                                                                && orgIDList.Contains(x.org_id)).ToList();

                foreach (var item in absenceDetails)
                {
                    if (!string.IsNullOrEmpty(item.absencedisply_status_desc) && isStatxtAbs)
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(item.absencedisply_status_desc, user);
                        //Add line break between the textboxes
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            publishHelper.InsertLineBreak();
                        }
                    }
                    if (!string.IsNullOrEmpty(item.absencedisply_actionplan_desc) && !isStatxtAbs)
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(item.absencedisply_actionplan_desc, user);
                    }
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence text for service");
            }
                
        }



        private void InsertAbsenceTxtForServiceNewFetch(string user, DocumentBuilder builder, string orgID, string serviceId, int budgetYear,
            int forecastPeriod, int orgLevel, int serviceLevel, bool isStatxtAbs)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            int absDescForePeriod = forecastPeriod;
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            var displayOrgLevel = _utility.GetParameterValue(user, "DOC_MONTHLY_DISPLAY_LEVEL");

            int configOrgLevel = string.IsNullOrEmpty(displayOrgLevel) ? 2 :
                                 displayOrgLevel == "org_id_1" ? 1 :
                                 displayOrgLevel == "org_id_2" ? 2 :
                                 displayOrgLevel == "org_id_3" ? 3 :
                                 displayOrgLevel == "org_id_4" ? 4 :
                                 displayOrgLevel == "org_id_5" ? 5 : 2;

            orgLevel = orgLevel == -1 ? configOrgLevel : orgLevel;

            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                if (_utility.IsFeatureEnabled(FeatureFlags.ck_Editor).GetAwaiter().GetResult())
                {
                    serviceId = _utility.RefineServiceIdValue(serviceId, user).GetAwaiter().GetResult();
                }
                

                var orgIDList = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, configOrgLevel, serviceLevel).Where(x => x.ServiceID == serviceId).Select(x => x.OrgID).Distinct().ToList();

                List<string> descTypes = new List<string> { nameof(MonthlyReportTextBoxType.AbsenceStatus).ToLower(), nameof(MonthlyReportTextBoxType.AbsenceActionPlan).ToLower() };
                var absenceDetails = tenantDbContext.tco_monthrep_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                && x.service_id == serviceId
                                                                                && x.org_level == orgLevel.ToString()
                                                                                && x.budget_year == budgetYear
                                                                                && x.forecast_period == absDescForePeriod
                                                                                && x.acc_group_value == "NA"
                                                                                && orgIDList.Contains(x.org_id)
                                                                                && descTypes.Contains(x.description_type.ToLower())).ToList();

                foreach (var item in absenceDetails)
                {
                    if (item.description_type.ToLower() == nameof(MonthlyReportTextBoxType.AbsenceStatus).ToLower() && !string.IsNullOrEmpty(item.description_text) && isStatxtAbs)
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absstat_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(item.description_text, user);
                        //Add line break between the textboxes
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            publishHelper.InsertLineBreak();
                        }
                    }
                    if (item.description_type.ToLower() == nameof(MonthlyReportTextBoxType.AbsenceActionPlan).ToLower() && !string.IsNullOrEmpty(item.description_text) && !isStatxtAbs)
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportMR["MR_Doc_Absact_node_text"].LangText, _context, true);
                        publishHelper.InsertDescriptionCk5(item.description_text, user);
                    }
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Absence text for service");
            }
        }



        private void InsertMonthlyAbsenceData(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, int orgLevel, string orgId, string serviceId)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            try
            {
                Initialize(user);
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
                UserData userDetails = _utility.GetUserDetails(user);
                TableDefinition tableDef = null;
                ColumnDetails cellTemplate;
                int serviceAreaOrgLevel = 2;
                OrgInpLevels orgInput = new OrgInpLevels(string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty);
                orgInput.level1OrgId = orgId;
                switch (orgLevel)
                {
                    case 2:
                        orgInput.level2OrgId = orgId;
                        break;

                    case 3:
                        orgInput.level3OrgId = orgId;
                        break;

                    case 4:
                        orgInput.level4OrgId = orgId;
                        break;

                    case 5:
                        orgInput.level5OrgId = orgId;
                        break;

                    case 6:
                        orgInput.level6OrgId = orgId;
                        break;

                    case 7:
                        orgInput.level7OrgId = orgId;
                        break;
                }
                int rowAggregateLevel = 0;
                if (orgInput.level2OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel;
                else if (orgInput.level3OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel + 1;
                else if (orgInput.level4OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel + 2;
                else if (orgInput.level5OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel + 3;
                else if (orgInput.level6OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel + 4;
                else if (orgInput.level7OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel + 5;
                else if (orgInput.level8OrgId == null)
                    rowAggregateLevel = serviceAreaOrgLevel + 6;

                var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
                List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.ToList();
                string orgName = string.Empty;
                switch (orgLevel)
                {
                    case -1:
                        orgId = lstOrgHierarchy.FirstOrDefault().org_id_1;
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == orgId).org_name_1;
                        orgInput.level1OrgId = orgId;
                        break;

                    case 1:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == orgId).org_name_1;
                        break;

                    case 2:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgId).org_name_2;
                        break;

                    case 3:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId).org_name_3;
                        break;

                    case 4:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_name_4;
                        break;

                    case 5:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_name_5;
                        break;
                }
                dynamic result = _monthlyReportAbsence.GetMrMonAbsenceGrid(user, budgetYear, string.Empty, orgLevel, orgName, forecastPeriod, rowAggregateLevel, serviceId, orgInput, true);
                JToken resultobj = result.SelectToken("data");
                int setupPeriod = forecastPeriod;
                tmr_period_setup tpsRec = tenantDbContext.tmr_period_setup.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period == forecastPeriod);
                if (tpsRec != null)
                {
                    setupPeriod = tpsRec.sickleave_period;
                }
                if (resultobj.Any())
                {
                    int month = setupPeriod - budgetYear * 100;
                    bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                    if (isYearlySetupEnabled)
                    {
                        tableDef = _docTableConfig.GetTableDef(user, "MrYearlyMonAbsenceData");
                    }
                    else
                    {
                        tableDef = _docTableConfig.GetTableDef(user, "MrMonAbsenceData");
                    }
                    Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");

                    var lstColumnDataDetailsList = new List<dynamic>();

                    List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                    List<string> lstColumnDataDetails = new List<string>();

                    //print column header
                    foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
                    {
                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        if (cdf.ColumnId.ToLower() == "Jan".ToLower() && month < 1)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "Feb".ToLower() && month < 2)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "March".ToLower() && month < 3)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "April".ToLower() && month < 4)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "May".ToLower() && month < 5)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "June".ToLower() && month < 6)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "July".ToLower() && month < 7)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "Aug".ToLower() && month < 8)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "Sept".ToLower() && month < 9)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "Oct".ToLower() && month < 10)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "Nov".ToLower() && month < 11)
                        {
                            cdf.IsActive = false;
                        }
                        else if (cdf.ColumnId.ToLower() == "Dec".ToLower() && month < 12)
                        {
                            cdf.IsActive = false;
                        }
                        lstColumnDetails.Add(cellTemplate);
                        lstColumnDataDetails.Add(cdf.ColumnName);
                    }

                    lstColumnDataDetailsList.Add(new { lstColumnDataDetails, rowType = RowType2.Heading2 });

                    int count = 0;
                    foreach (var pur in resultobj)
                    {
                        lstColumnDataDetails = new List<string>();
                        count++;
                        foreach (ColumnDefinition cdf in tableDef.ColumnDefinitions)
                        {
                            if (cdf.ColumnId.ToLower() == "Jan".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["jan"] != "" ? (((string)pur["jan"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "Feb".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["feb"] != "" ? (((string)pur["feb"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "March".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["mar"] != "" ? (((string)pur["mar"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "April".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["apr"] != "" ? (((string)pur["apr"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "May".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["may"] != "" ? (((string)pur["may"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "June".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["jun"] != "" ? (((string)pur["jun"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "July".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["jul"] != "" ? (((string)pur["jul"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "Aug".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["aug"] != "" ? (((string)pur["aug"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "Sept".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["sep"] != "" ? (((string)pur["sep"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "Oct".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["oct"] != "" ? (((string)pur["oct"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "Nov".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["nov"] != "" ? (((string)pur["nov"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "Dec".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["dec"] != "" ? (((string)pur["dec"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "ytd".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["ytd"] != "" ? (((string)pur["ytd"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "lastyrmon".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["lastyrmon"] != "" ? (((string)pur["lastyrmon"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "lastyrytd".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["lastyrytd"] != "" ? (((string)pur["lastyrytd"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("-1");
                            }
                            else if (cdf.ColumnId.ToLower() == "type".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["absType"] != "" ? (((string)pur["absType"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("");
                            }
                            else if (cdf.ColumnId.ToLower() == "orgName".ToLower())
                            {
                                if (cdf.IsActive)
                                    lstColumnDataDetails.Add((string)pur["namcol01"] != "" ? (((string)pur["namcol01"]).Replace(".", ",")) : "-1");
                                else
                                    lstColumnDataDetails.Add("");
                            }
                        }
                        int noDataCount = 0;
                        //remove row with all -1 value row, which is set when data doesn't exist for a given org-month
                        for (int c = 0; c < lstColumnDataDetails.Count; c++)
                        {
                            string tempData = lstColumnDataDetails[c].Replace(",", ".");
                            if (c >= 2)
                            {
                                decimal value = (Convert.ToDecimal(tempData, CultureInfo.InvariantCulture));
                                if (Math.Round(value) == -1)
                                {
                                    noDataCount++;
                                    lstColumnDataDetails[c] = "0";
                                }

                                lstColumnDataDetails[c] = lstColumnDataDetails[c] + " %";
                            }
                        }

                        if (noDataCount != 15)
                        {
                            if (count == 3)
                            {
                                lstColumnDataDetailsList.Add(new { lstColumnDataDetails, rowType = RowType2.Total1 });
                                count = 0;
                            }
                            else
                                lstColumnDataDetailsList.Add(new { lstColumnDataDetails, rowType = RowType2.Content1 });
                        }
                        else
                        {
                            if (count == 3)
                                count = 0;
                        }
                    }

                    if (lstColumnDataDetailsList.Count() > 1)
                    {
                        publishHelper.StartTableOrientation(tableDef);
                        publishHelper.InsertHeading(user, langStringValuesMonthlyReport["MR_Doc_MonAbsenceData_title"].LangText, _context, true);
                        publishHelper.StartTable("MonAbsenceData", null, null, tableDef);

                        foreach (var columnData in lstColumnDataDetailsList)
                        {
                            publishHelper.CreateRow2(lstColumnDetails, columnData.lstColumnDataDetails, columnData.rowType);
                        }

                        publishHelper.EndTable();
                        publishHelper.EndTableOrientation();
                    }
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Monthly Absence data");
            }
        }

    }
}