using Aspose.Words;
using Aspose.Words.Tables;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Drawing;
using System.Reflection;
using System.Text.RegularExpressions;
using static Framsikt.BL.Helpers.clsConstants;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.DocExportWorker
{
    public partial class MonthlyReportingExportHelper
    {


        private void InsertServiceUnitInvestmentData(string user, DocumentBuilder builder, string orgId, int budgetYear, int forecastPeriod, int orgLevel, string otherOrgId, bool isDescNode)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            try
            {
                var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");

                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportInv = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport_Investment");
                Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                var isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                var monthlyReportDBContext = _utility.GetTenantDBContext();
                int finishedStatus = (from d in monthlyReportDBContext.tco_progress_status
                                      where d.fk_tenant_id == userDetails.tenant_id && d.type == "MONTHREP_INV" && d.active == 1 && d.finished_flag == true
                                      select d).FirstOrDefault() != null ? (from d in monthlyReportDBContext.tco_progress_status
                                                                            where d.fk_tenant_id == userDetails.tenant_id && d.type == "MONTHREP_INV" && d.active == 1 && d.finished_flag == true
                                                                            select d).FirstOrDefault().pk_id : 0;
                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypePercentage = numberFormats["percentage"].LangText;
                string numberTypeDecimalAmount = numberFormats["dec1"].LangText;
                int invId = 0;
                dynamic labels = new JArray();
                string invName = string.Empty;
                string invDetailPath = string.Empty;
                string OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5;
                string showInvestmentWithZero = _utility.GetParameterValue(user, "MR_SHOW_INV_ZERO");
                //assgin service id value to corret  orglevel
                List<string> defaultColumn = new List<string>();

                switch (orgLevel.ToString())
                {
                    case "1":
                        OrgLevel1 = orgId;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        break;

                    case "2":
                        OrgLevel2 = orgId;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        OrgLevel1 = null;
                        break;

                    case "3":
                        OrgLevel3 = orgId;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        break;

                    case "4":
                        OrgLevel4 = orgId;
                        OrgLevel5 = null;
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        break;

                    case "5":
                        OrgLevel5 = orgId;
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        break;

                    default:
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        break;
                }

                //get orgstructure
                var orgStructure = _utility.GetNextOrgLevelIdsDepartments(orgVersionContent, user, budgetYear, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, false, null);

                //get List of department from orgstructure

                var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5);

                //get list of function from orgstructure
                var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, null);

                //get the data from service
                List<MonthlyReportInvestmentHelper> resultSet = _monthlyReportInvestment.GetInvestementData(user, forecastPeriod, orgId, orgLevel, null, departmentList, functionList, true, false, false);

                //get risk list
                var risklist = _monthlyReportInvestment.GetRiskList(user);

                //get risk list
                var statuslist = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user);

                //quater data
                var quarterData = _utility.GetEstimatedFinishQuarter(budgetYear, 1, user);

                //drop the inv having accounting and budget both equals to 0 in yearly setup
                if (isYearlySetupEnabled && (showInvestmentWithZero.ToLower() != "TRUE".ToLower()))
                {
                    //added pkid check due to bug #66662
                    resultSet.RemoveAll(x => x.Accounting == 0 && x.AdjustedBudget == 0 && x.PkId != -2);
                }

                //get the columns from TCO parametre
                TableDefinition tabledef = null;
                string userDefinedCol = string.Empty;

                if (isYearlySetupEnabled)
                {
                    tabledef = _docTableConfig.GetTableDef(user, "MrYearlyInvestment");
                }
                else
                {
                    tabledef = _docTableConfig.GetTableDef(user, "MrInvestment");
                }

                bool divideByMillions = tabledef.AmountFormat == AmountFormats.Millions;
                foreach (var item in tabledef.ColumnDefinitions.ToList())
                {
                    defaultColumn.Add(item.ColumnId);
                }

                //convert it to JArray
                JArray dataArray = JArray.FromObject(resultSet);
                if (dataArray.Any())
                {
                    if (!isDescNode)
                    {
                        publishHelper.StartTableOrientation(tabledef);
                        publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investment_desc"].LangText, _context, true);
                        publishHelper.StartTable("InvestmentGrid", null, null, tabledef);

                        List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                        List<string> lstColumnDataDetails = new List<string>();

                        int count = 0;

                        int set1HeaderIndex = defaultColumn.Count(x => x.ToLower() == "totalamount" ||
                            x.ToLower() == "changetotalamount" || x.ToLower() == "deviation" || x.ToLower() == "totalorgbudget" || x.ToLower() == "costapproval");
                        int set2HeaderIndex = defaultColumn.Count(x => x.ToLower() == "status" ||
                            x.ToLower() == "plannedamount" || x.ToLower() == "regulation" || x.ToLower() == "forecastamount" ||
                            x.ToLower() == "forecastdeviation" || x.ToLower() == "adjustedbudget" ||
                            x.ToLower() == "proposedadjustment" || x.ToLower() == "accounting".ToLower() ||
                            x.ToLower() == "UnApprvBudChange".ToLower() || x.ToLower() == "TotalYbUbcBudget".ToLower() || x.ToLower() == "budgetacctdeviation".ToLower());

                        // create the header row
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();
                        builder.CellFormat.ClearFormatting();
                        builder.RowFormat.HeadingFormat = true;
                        var cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        foreach (var c in defaultColumn)
                        {
                            count++;
                            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                            if (c.ToLower() == "risk" || c.ToLower() == "statusreview" || c.ToLower() == "investment")
                            {
                                cellTemplate.Alignment = ParagraphAlignment.Left;
                            }
                            else
                            {
                                cellTemplate.Alignment = ParagraphAlignment.Right;
                            }

                            cellTemplate.WrapText = true;
                            cellTemplate.BottomBorder = 1;
                            if (c.ToLower() == "totalamount" || c.ToLower() == "changetotalamount" || c.ToLower() == "deviation" || c.ToLower() == "totalorgbudget" || c.ToLower() == "costapproval")
                            {
                                if (publishHelper.GetType() != typeof(WebHelperMr))
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                }
                            }
                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            lstColumnDetails.Add(cellTemplate);
                            lstColumnDataDetails.Add(tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == c.ToLower()).ColumnName);
                        }

                        publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
                        builder.RowFormat.HeadingFormat = false;

                        var columns = publishHelper.GetCurrentChapterColumns();
                        int statusreviewColIndex = -1;
                        int index = 0;
                        labels = new JArray();
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            foreach (var col in columns)
                            {
                                if (col.columns.Count() > 0)
                                {
                                    foreach (var colChild in col.columns)
                                    {
                                        if (colChild.columns.Count() > 0)
                                        {
                                            foreach (var colChildSub in colChild.columns)
                                            {
                                                if (colChildSub.columns.Count() > 0)
                                                {
                                                    foreach (var colChildSubSuper in colChildSub.columns)
                                                    {
                                                        if (colChildSubSuper.columns.Count() > 0)
                                                        {
                                                            if (!string.IsNullOrEmpty(colChildSubSuper.title.Trim()))
                                                            {
                                                                labels.Add(colChildSubSuper.title);
                                                            }
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    if (!string.IsNullOrEmpty(colChildSub.title.Trim()))
                                                    {
                                                        labels.Add(colChildSub.title);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (!string.IsNullOrEmpty(colChild.title.Trim()))
                                            {
                                                labels.Add(colChild.title);
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    if (!string.IsNullOrEmpty(col.title.Trim()) && tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == "statusreview".ToLower()).ColumnName != col.title.Trim())// get titel except komment column
                                    {
                                        labels.Add(col.title);
                                    }
                                    if (tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == "statusreview".ToLower()).ColumnName == col.title.Trim())
                                    {
                                        statusreviewColIndex = index;// get the index of the commetcolumn
                                    }
                                    index++;
                                }
                            }
                        }

                        // Get the PropertyInfo object by passing the property name.
                        PropertyInfo[] myPropInfo = typeof(MonthlyReportInvestmentHelper).GetProperties();
                        // insert data for the document
                        foreach (var d in dataArray)
                        {
                            dynamic invData = new JArray();
                            lstColumnDetails = new List<ColumnDetails>();
                            lstColumnDataDetails = new List<string>();

                            //for each column to display insert record
                            count = 0;
                            foreach (var col in defaultColumn)
                            {
                                count++;
                                cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                                // if (j < 3)
                                if (col.ToLower() == "risk" || col.ToLower() == "statusreview" || col.ToLower() == "investment")
                                {
                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                    if (col.ToLower() == "investment")
                                    {
                                        cellTemplate.PreferredWidth = 30;
                                    }
                                }
                                else
                                {
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    cellTemplate.PreferredWidth = 10;
                                }
                                cellTemplate.WrapText = true;
                                cellTemplate.IsBold = false;
                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    if (count == 1)
                                    {
                                        cellTemplate.FontColor = FramsiktColors.MonthlyReportDetailColor;
                                    }
                                    else
                                    {
                                        cellTemplate.FontColor = FramsiktColors.DefaultTextColor;
                                    }
                                }
                                if ((int)d["PkId"] == -1)
                                {
                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        cellTemplate.FontColor = FramsiktColors.MonthlyReportTotalColor;
                                        cellTemplate.IsTotalRow = true;
                                        cellTemplate.Fontsize = 17;
                                        cellTemplate.IsBold = true;
                                    }
                                    else
                                    {
                                        cellTemplate.FontColor = FramsiktColors.TotalColor;
                                    }
                                }

                                if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "deviation" || col.ToLower() == "totalorgbudget" || col.ToLower() == "costapproval")
                                {
                                    if (publishHelper.GetType() != typeof(WebHelperMr))
                                    {
                                        cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                    }
                                }

                                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                                cellTemplate.TopBorder = 0;
                                cellTemplate.BottomBorder = 1;

                                if (col.ToLower() == "accounting")
                                {
                                    cellTemplate.ColumnType = "accounting";
                                }
                                else if (col.ToLower() == "TotalYbUbcBudget".ToLower())
                                {
                                    cellTemplate.ColumnType = "TotalYbUbcBudget".ToLower();
                                }
                                else if (col.ToLower() == "UnApprvBudChange".ToLower())
                                {
                                    cellTemplate.ColumnType = "UnApprvBudChange".ToLower();
                                }
                                else if (col.ToLower() == "accountingtotal")
                                {
                                    cellTemplate.ColumnType = "accountingtotal";
                                }
                                else if (col.ToLower() == "actualamtlastyear")
                                {
                                    cellTemplate.ColumnType = "actualamtlastyear";
                                }
                                else if (col.ToLower() == "adjustedbudget")
                                {
                                    cellTemplate.ColumnType = "adjustedbudget";
                                }
                                else if (col.ToLower() == "budgetacctdeviation")
                                {
                                    cellTemplate.ColumnType = "budgetacctdeviation";
                                }
                                else if (col.ToLower() == "changetotalamount")
                                {
                                    cellTemplate.ColumnType = "changetotalamount";
                                }
                                else if (col.ToLower() == "consumepercentage")
                                {
                                    cellTemplate.ColumnType = "consumepercentage";
                                }
                                else if (col.ToLower() == "deviation")
                                {
                                    cellTemplate.ColumnType = "deviation";
                                }
                                else if (col.ToLower() == "EstimatedQuarter".ToLower())
                                {
                                    cellTemplate.ColumnType = "EstimatedQuarter";
                                }
                                else if (col.ToLower() == "FinishedYear".ToLower())
                                {
                                    cellTemplate.ColumnType = "FinishedYear";
                                }
                                else if (col.ToLower() == "forecastamount".ToLower())
                                {
                                    cellTemplate.ColumnType = "forecastamount";
                                }
                                else if (col.ToLower() == "forecastdeviation".ToLower())
                                {
                                    cellTemplate.ColumnType = "forecastdeviation";
                                }
                                else if (col.ToLower() == "investment".ToLower())
                                {
                                    cellTemplate.ColumnType = "investment";
                                }
                                else if (col.ToLower() == "plannedamount".ToLower())
                                {
                                    cellTemplate.ColumnType = "plannedamount";
                                }
                                else if (col.ToLower() == "regulation".ToLower())
                                {
                                    cellTemplate.ColumnType = "regulation";
                                }
                                else if (col.ToLower() == "risk".ToLower())
                                {
                                    cellTemplate.ColumnType = "risk";
                                }
                                else if (col.ToLower() == "status".ToLower())
                                {
                                    cellTemplate.ColumnType = "status";
                                }
                                else if (col.ToLower() == "statusreview".ToLower())
                                {
                                    cellTemplate.ColumnType = "statusreview";
                                }
                                else if (col.ToLower() == "totalamount".ToLower())
                                {
                                    cellTemplate.ColumnType = "totalamount";
                                }

                                lstColumnDetails.Add(cellTemplate);

                                string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                                //apply number formatting of for number column
                                if (name.ToLower() != "investment" && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter" && name.ToLower() != "approvalreference")
                                {
                                    if (name.ToLower() != "consumepercentage")
                                    {
                                        decimal ammount = 0;

                                        bool success = decimal.TryParse(d[name].ToString(), out ammount);
                                        if (name.ToLower() == "costapproval")
                                        {
                                            if (ammount == 0 || !string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                            {
                                                if (!string.IsNullOrEmpty(((string)d["invStatusDescription"]))) lstColumnDataDetails.Add("-");
                                                else lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_approvalCostText"].LangText);
                                            }
                                            else
                                            {
                                                if (!divideByMillions)
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                }
                                                else
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                }
                                            }
                                        }
                                        else if (name.ToLower() == "totalamount".ToLower() || name.ToLower() == "changetotalamount".ToLower() || name.ToLower() == "deviation".ToLower() || name.ToLower() == "totalorgbudget".ToLower())
                                        {
                                            if (!string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                            {
                                                lstColumnDataDetails.Add("-");
                                            }
                                            else
                                            {
                                                if (!divideByMillions)
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                }
                                                else
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((int)d["PkId"] != -1 ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                                    }
                                }
                                else
                                {
                                    if (name.ToLower() == "statusreview")
                                    {
                                        lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                                    }
                                    else if (name.ToLower() == "status")
                                    {
                                        var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                                        lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                                    }
                                    else if (name.ToLower() == "risk")
                                    {
                                        var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                                        lstColumnDataDetails.Add(risk != null ? (string)risk.Value : "");
                                        if (risk != null)
                                        {
                                            switch (risk.Key)
                                            {
                                                case "1":
                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel1Color;
                                                    break;

                                                case "2":
                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel2Color;
                                                    break;

                                                case "3":
                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel3Color;
                                                    break;
                                            }
                                        }
                                    }
                                    else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == 0) || (int)d["PkId"] == -1)) || ((name.ToLower() == "finishedyear") && (int)d["PkId"] == -1))
                                    {
                                        lstColumnDataDetails.Add(string.Empty);
                                    }
                                    else if (name.ToLower() == "finishedyear")
                                    {
                                        if (((string)d["invStatusDescription"] != ""))
                                        {
                                            lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(((string)d[name]));
                                        }
                                    }
                                    else if (name.ToLower() == "estimatedquarter" && ((int)d[name] != 0))
                                    {
                                        if (((string)d["invStatusDescription"] != ""))
                                        {
                                            lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                        }
                                        else
                                        {
                                            if ((int)d["Status"] == finishedStatus)
                                            {
                                                var status = statuslist.FirstOrDefault(x => x.Key == (int)d["Status"]);
                                                lstColumnDataDetails.Add(status.Value.ToString());
                                            }
                                            else
                                            {
                                                var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                                lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : "");
                                            }
                                        }
                                    }
                                    else if (name.ToLower() == "approvalreference")
                                    {
                                        lstColumnDataDetails.Add((string)d[name]);
                                    }
                                    else
                                    {
                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            string rowtemplateWithData = string.Empty;
                                            rowtemplateWithData = $"<a href='javascript:void(0)' title = \"(Sprettoppvindu) {(string)d[name]}\" onkeypress = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToInt32(d["PkId"])}\")' onClick = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToInt32(d["PkId"])}\")'>{(string)d[name]}<span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                                            lstColumnDataDetails.Add(rowtemplateWithData);

                                            invId = Convert.ToInt32(d["PkId"]);
                                            invName = ((string)d[name]);
                                            invDetailPath = publishHelper.GetCurrentChapterPath() + "invdetails/";
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(((string)d[name]));
                                        }
                                    }
                                }
                            }
                            bool allValueZeroes = true;
                            int c = 0;
                            lstColumnDataDetails.ForEach(x =>
                            {
                                decimal valdecimal;
                                decimal.TryParse(x, out valdecimal);
                                if (count > 0 && c != (count - 1) && valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                                {
                                    allValueZeroes = false;
                                }
                                else
                                {
                                    if (valdecimal != 0 && tabledef.ColumnDefinitions.ElementAt(c).IsActive)
                                    {
                                        allValueZeroes = false;
                                    }
                                }
                                c++;
                            });
                            //added allvalueZero check bug id- #66842
                            if ((int)d["PkId"] == -1)
                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                            else if ((int)d["PkId"] != -2 || ((int)d["PkId"] == -2 && !allValueZeroes))
                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);

                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                int dcount = 0;
                                var drow = publishHelper.GetCurrentRowData();
                                drow.ForEach(x =>
                                {
                                    if (dcount == 0)
                                    {
                                        invData.Add(Regex.Replace(x, "<.*?>", String.Empty));
                                    }
                                    else if (dcount != statusreviewColIndex) //skip komment column
                                    {
                                        invData.Add(x);
                                    }

                                    dcount++;
                                });
                            }
                            //web popup
                            if (publishHelper.GetType() == typeof(WebHelperMr) && invData.Count > 0)
                            {
                                dynamic invObject = new JObject();
                                invData[0] = invName;
                                invObject.Add("Data", invData);
                                invObject.Add("Labels", labels);
                                invObject.WebUrlLink = string.Empty;
                                invObject.popUpTitle = invName;

                                string description = string.Empty;
                                description = _utility.GetInvestmentsDescription(user, invId, budgetYear, "external", Guid.Empty).Value;
                                invObject.popUpDescription1 = string.IsNullOrEmpty(description) ? "" : description;

                                string description2 = string.Empty;

                                List<MonthlyReportInvestmentHelper> descriptiondata = resultSet;
                                string desc2 = descriptiondata.ToList().FirstOrDefault(x => x.PkId == invId).Statusreview;
                                description2 = desc2;

                                invObject.popUpDescription2 = description2;

                                invObject.popUpHeaderDesc1 = string.IsNullOrEmpty(description) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc1_heading"].LangText;
                                invObject.popUpHeaderDesc2 = string.IsNullOrEmpty(description2) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc2_heading"].LangText;

                                string showFpDescription = _utility.GetParameterValue(user, "MR_PUBLISHDisplay_FinPlan_description_In_Popup");
                                if (!string.IsNullOrEmpty(showFpDescription) && showFpDescription.ToLower() == "true")
                                {
                                    invObject.showPopupDescription = true;
                                }
                                else
                                {
                                    invObject.showPopupDescription = false;
                                }

                                publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(invObject), invDetailPath, invId.ToString(), PubContentHandler.BlobType.Json);
                            }
                        }
                        publishHelper.EndTable();
                        publishHelper.EndTableOrientation();
                    }
                    else
                    {
                        if (publishHelper.GetType() != typeof(WebHelperMr))
                        {
                            publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                            string showStatusDescription = _utility.GetParameterValue(user, "DOC_INV_STATUSDESCRIPTION");

                            if (!string.IsNullOrEmpty(showStatusDescription) && showStatusDescription.ToUpper() == "TRUE")
                            {
                                List<MonthlyReportInvestmentHelper> descriptiondata = resultSet;
                                foreach (var item in descriptiondata.ToList().Where(x => !string.IsNullOrEmpty(x.Statusreview)))
                                {
                                    publishHelper.InsertHeading(userDetails.user_name, item.Investment, _context);
                                    publishHelper.InsertDescriptionCk5(item.Statusreview, userDetails.user_name);
                                }
                            }
                        }

                        DescToBlobHelper input = new DescToBlobHelper();
                        input.ModuleId = "MONTHREP";
                        input.BudgetYear = budgetYear;
                        input.FieldId = "MRIDESC";

                        if (string.IsNullOrEmpty(otherOrgId))
                        {
                            input.OrgLevel2 = orgId;
                        }
                        else
                        {
                            input.OrgLevel2 = otherOrgId;
                            input.OrgLevel3 = orgId;
                        }

                        dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);

                        string finPlanDeviation = result.description.ToString();
                        if (!string.IsNullOrEmpty((finPlanDeviation)))
                        {
                            publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                        }

                        input.FieldId = "MRIBUDSTAT";
                        result = _utility.GetDescriptionText(input, user, forecastPeriod);
                        string budgetStatus = result.description.ToString();
                        if (!string.IsNullOrEmpty((budgetStatus)))
                        {
                            publishHelper.InsertDescriptionCk5(budgetStatus, user);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                publishHelper.InsertError("Failed to insert service unit Investment data" + ex);
            }
        }



        private void InsertServiceUnitInvestmentDataNew(string user, DocumentBuilder builder, string orgId, int budgetYear, int forecastPeriod, int orgLevel, string otherOrgId, bool isDescNode)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            try
            {
                var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                string OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5;
                switch (orgLevel.ToString())
                {
                    case "1":
                        OrgLevel1 = orgId;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        break;

                    case "2":
                        OrgLevel2 = orgId;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        OrgLevel1 = null;
                        break;

                    case "3":
                        OrgLevel3 = orgId;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        break;

                    case "4":
                        OrgLevel4 = orgId;
                        OrgLevel5 = null;
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        break;

                    case "5":
                        OrgLevel5 = orgId;
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        break;

                    default:
                        OrgLevel1 = null;
                        OrgLevel2 = null;
                        OrgLevel3 = null;
                        OrgLevel4 = null;
                        OrgLevel5 = null;
                        break;
                }

                //get orgstructure
                var orgStructure = _utility.GetNextOrgLevelIdsDepartments(orgVersionContent, user, budgetYear, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, false, null);

                //get List of department from orgstructure

                var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5);

                //get list of function from orgstructure
                var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, null);

                //get the data from service
                List<MonthlyReportInvestmentHelperNew> resultSet = _monthlyReportInvestment.GetInvestementDataNew(user, forecastPeriod, orgId, orgLevel, otherOrgId, departmentList, functionList, true, false, false);

                //convert it to JArray
                JArray dataArray = JArray.FromObject(resultSet);
                if (dataArray.Any())
                {
                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);

                    List<MonthlyReportInvestmentHelperNew> descriptiondata = resultSet;
                    foreach (var item in descriptiondata.ToList().Where(x => !string.IsNullOrEmpty(x.Statusreview)))
                    {
                        publishHelper.InsertText(item.Investment, 11, true, false);
                        publishHelper.InsertDescriptionCk5(item.Statusreview, userDetails.user_name);
                    }

                    DescToBlobHelper input = new DescToBlobHelper();
                    input.ModuleId = "MONTHREP";
                    input.BudgetYear = budgetYear;
                    input.FieldId = "MRIDESC";
                    input.descriptionId = string.Empty;

                    if (string.IsNullOrEmpty(otherOrgId))
                    {
                        input.OrgLevel2 = orgId;
                    }
                    else
                    {
                        input.OrgLevel2 = otherOrgId;
                        input.OrgLevel3 = orgId;
                    }

                    dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);

                    string finPlanDeviation = result.description.ToString();
                    if (!string.IsNullOrEmpty((finPlanDeviation)))
                    {
                        publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                    }

                    input.FieldId = "MRIBUDSTAT";
                    result = _utility.GetDescriptionText(input, user, forecastPeriod);
                    string budgetStatus = result.description.ToString();
                    if (!string.IsNullOrEmpty((budgetStatus)))
                    {
                        publishHelper.InsertDescriptionCk5(budgetStatus, user);
                    }
                }
            }
            catch (Exception ex)
            {
                publishHelper.InsertError("Failed to insert service unit Investment data" + ex);
            }
        }



        private void InsertServiceUnitAccountingData(string user, DocumentBuilder builder, string level1Id, int budgetYear, int forecastPeriod, string level2Id)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tableDef;
            try
            {
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);

                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportAcc = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MRI_AccountingData");
                Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                var userDefinedCols = new List<string>();
                bool isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);

                if (isYearlySetupEnabled)
                    tableDef = _docTableConfig.GetTableDef(user, "MrYearlyDocAccounting");
                else
                    tableDef = _docTableConfig.GetTableDef(user, "MrDocAccounting");

                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypePercentage = numberFormats["percentage"].LangText;
                string numberTypeDecimalAmount = numberFormats["dec1"].LangText;
                bool divideByMillions = tableDef.AmountFormat == AmountFormats.Millions;
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

                foreach (var item in tableDef.ColumnDefinitions.Where(x => x.IsFixed != true))
                    userDefinedCols.Add(item.ColumnId);

                int maxNumAcColumns = userDefinedCols.Count;
                var dataset = (from a in tenantDbContext.vwUserDetails
                               where a.tenant_id == -1
                               select new
                               {
                                   lineGroupId = 0,
                                   lineGroup = string.Empty,
                                   lineItemId = 0,
                                   lineItem = string.Empty,
                                   column1 = 0.0M,
                                   column2 = 0.0M,
                                   column3 = 0.0M,
                                   column4 = 0.0M,
                                   column5 = 0.0M
                               }).ToList();

                if (!string.IsNullOrEmpty(level1Id) && !string.IsNullOrEmpty(level2Id))
                {
                    dataset = (from a in tenantDbContext.vw_accgrp_monthrep
                               where a.fk_tenant_id == userDetails.tenant_id
                               && (a.forecast_period == 0 || a.forecast_period == forecastPeriod)
                               && a.budget_year == budgetYear
                               && a.mr_level_1_value == level1Id
                               && a.mr_level_2_value == level2Id
                               group a by new { a.line_group_id, a.line_group, a.line_item_id, a.line_item } into g
                               select new
                               {
                                   lineGroupId = g.Key.line_group_id,
                                   lineGroup = g.Key.line_group,
                                   lineItemId = g.Key.line_item_id,
                                   lineItem = g.Key.line_item,
                                   column1 = g.Sum(x => x.gl_amount_1.Value),
                                   column2 = g.Sum(x => x.gl_amount_2.Value),
                                   column3 = g.Sum(x => x.gl_amount_3.Value),
                                   column4 = g.Sum(x => x.gl_amount_4.Value),
                                   column5 = g.Sum(x => x.budget_amount.Value)
                               }).ToList();
                }
                else if (!string.IsNullOrEmpty(level1Id) && string.IsNullOrEmpty(level2Id))
                {
                    dataset = (from a in tenantDbContext.vw_accgrp_monthrep
                               where a.fk_tenant_id == userDetails.tenant_id
                               && (a.forecast_period == 0 || a.forecast_period == forecastPeriod)
                               && a.budget_year == budgetYear
                               && a.mr_level_1_value == level1Id
                               group a by new { a.line_group_id, a.line_group, a.line_item_id, a.line_item } into g
                               select new
                               {
                                   lineGroupId = g.Key.line_group_id,
                                   lineGroup = g.Key.line_group,
                                   lineItemId = g.Key.line_item_id,
                                   lineItem = g.Key.line_item,
                                   column1 = g.Sum(x => x.gl_amount_1.Value),
                                   column2 = g.Sum(x => x.gl_amount_2.Value),
                                   column3 = g.Sum(x => x.gl_amount_3.Value),
                                   column4 = g.Sum(x => x.gl_amount_4.Value),
                                   column5 = g.Sum(x => x.budget_amount.Value)
                               }).ToList();
                }
                else if (string.IsNullOrEmpty(level1Id) && !string.IsNullOrEmpty(level2Id))
                {
                    dataset = (from a in tenantDbContext.vw_accgrp_monthrep
                               where a.fk_tenant_id == userDetails.tenant_id
                               && (a.forecast_period == 0 || a.forecast_period == forecastPeriod)
                               && a.budget_year == budgetYear
                               && a.mr_level_2_value == level2Id
                               group a by new { a.line_group_id, a.line_group, a.line_item_id, a.line_item } into g
                               select new
                               {
                                   lineGroupId = g.Key.line_group_id,
                                   lineGroup = g.Key.line_group,
                                   lineItemId = g.Key.line_item_id,
                                   lineItem = g.Key.line_item,
                                   column1 = g.Sum(x => x.gl_amount_1.Value),
                                   column2 = g.Sum(x => x.gl_amount_2.Value),
                                   column3 = g.Sum(x => x.gl_amount_3.Value),
                                   column4 = g.Sum(x => x.gl_amount_4.Value),
                                   column5 = g.Sum(x => x.budget_amount.Value)
                               }).ToList();
                }

                var lineGroupIds = (from d in dataset
                                    select new
                                    {
                                        d.lineGroupId,
                                        d.lineGroup
                                    }).ToList();

                //convert it to JArray
                JArray dataArray = JArray.FromObject(dataset);
                if (dataArray.Any())
                {
                    publishHelper.StartTableOrientation(tableDef);
                    publishHelper.InsertHeading(user, langStringValuesMonthlyReportAcc["MRI_AccountingData_heading_desc"].LangText, _context,
                        true);

                    publishHelper.StartTable("AccountingDataGrid", null, null, tableDef);

                    List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                    List<string> lstColumnDataDetails = new List<string>();

                    for (int i = 0; i <= maxNumAcColumns; i++)
                    {
                        var cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Alignment = i == 0 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;

                        if ((i == 0 || i == maxNumAcColumns - 1 || i == maxNumAcColumns) && publishHelper.GetType() == typeof(WordHelper))
                            cellTemplate.PreferredWidth = i == 0 ? 33 : 10;

                        lstColumnDetails.Add(cellTemplate);
                    }

                    lstColumnDataDetails.Add(string.Empty);

                    if (userDefinedCols.Contains("year1"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "year1").ColumnName + " " + (budgetYear - 3));

                    if (userDefinedCols.Contains("year2"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "year2").ColumnName + " " + (budgetYear - 2));

                    if (userDefinedCols.Contains("year3"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "year3").ColumnName + " " + (budgetYear - 1));

                    if (userDefinedCols.Contains("year4"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "year4").ColumnName + " " + (budgetYear));

                    if (userDefinedCols.Contains("budgetyear"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "budgetyear").ColumnName + " " + (budgetYear));

                    if (userDefinedCols.Contains("deviation"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "deviation").ColumnName);

                    if (userDefinedCols.Contains("deviationpc"))
                        lstColumnDataDetails.Add(tableDef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId == "deviationpc").ColumnName);

                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

                    if (publishHelper.GetType() == typeof(WebHelperMr))
                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);

                    decimal roundOffErrorPc = divideByMillions ? 0.5m : 0.005m;

                    foreach (var lg in lineGroupIds.Select(x => x.lineGroupId).Distinct().ToList())
                    {
                        var subDataset = dataset.Where(x => x.lineGroupId == lg);

                        int subDatasetCount = 0;
                        foreach (var li in subDataset)
                        {
                            lstColumnDetails = new List<ColumnDetails>();
                            lstColumnDataDetails = new List<string>();
                            subDatasetCount++;

                            for (int i = 0; i <= maxNumAcColumns; i++)
                            {
                                var cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                                cellTemplate.Alignment = i == 0 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                cellTemplate.WrapText = true;
                                if ((i == 0 || i == maxNumAcColumns - 1 || i == maxNumAcColumns) && publishHelper.GetType() == typeof(WordHelper))
                                {
                                    cellTemplate.PreferredWidth = i == 0 ? 33 : 10;
                                }
                                lstColumnDetails.Add(cellTemplate);
                            }
                            lstColumnDataDetails.Add(li.lineItem);
                            if (userDefinedCols.Contains("year1"))
                            {
                                lstColumnDataDetails.Add(divideByMillions == true ? (li.column1 / 1000000).ToString(numberTypeDecimalAmount) : (li.column1 / 1000).ToString(numberTypeAmount));
                            }

                            if (userDefinedCols.Contains("year2"))
                            {
                                lstColumnDataDetails.Add(divideByMillions == true ? (li.column2 / 1000000).ToString(numberTypeDecimalAmount) : (li.column2 / 1000).ToString(numberTypeAmount));
                            }

                            if (userDefinedCols.Contains("year3"))
                            {
                                lstColumnDataDetails.Add(divideByMillions == true ? (li.column3 / 1000000).ToString(numberTypeDecimalAmount) : (li.column3 / 1000).ToString(numberTypeAmount));
                            }

                            if (userDefinedCols.Contains("year4"))
                            {
                                lstColumnDataDetails.Add(divideByMillions == true ? (li.column4 / 1000000).ToString(numberTypeDecimalAmount) : (li.column4 / 1000).ToString(numberTypeAmount));
                            }

                            if (userDefinedCols.Contains("budgetyear"))
                            {
                                lstColumnDataDetails.Add(divideByMillions == true ? (li.column5 / 1000000).ToString(numberTypeDecimalAmount) : (li.column5 / 1000).ToString(numberTypeAmount));
                            }

                            if (userDefinedCols.Contains("deviation"))
                            {
                                lstColumnDataDetails.Add(divideByMillions == true ? ((li.column5 / 1000000) - (li.column4 / 1000000)).ToString(numberTypeDecimalAmount) : ((li.column5 / 1000) - (li.column4 / 1000)).ToString(numberTypeAmount));
                            }

                            var column5 = divideByMillions ? (li.column5 / 1000000) : (li.column5 / 1000);
                            var column4 = divideByMillions ? (li.column4 / 1000000) : (li.column4 / 1000);

                            if (userDefinedCols.Contains("deviationpc"))
                            {
                                lstColumnDataDetails.Add(Math.Abs(column5) <= roundOffErrorPc ? 0.ToString(numberTypePercentage) : (((column5 - column4) / column5) * 100).ToString(numberTypePercentage));
                            }

                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                            }
                            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                            }

                            if (subDatasetCount == subDataset.Count())
                            {
                                lstColumnDetails = new List<ColumnDetails>();
                                lstColumnDataDetails = new List<string>();
                                for (int i = 0; i <= maxNumAcColumns; i++)
                                {
                                    var cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                                    cellTemplate.Alignment = i == 0 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                    cellTemplate.WrapText = true;
                                    cellTemplate.TopBorder = 1;
                                    cellTemplate.BottomBorder = 1;
                                    cellTemplate.FontColor = FramsiktColors.TotalColor;
                                    if ((i == 0 || i == maxNumAcColumns - 1 || i == maxNumAcColumns) && publishHelper.GetType() == typeof(WordHelper))
                                    {
                                        cellTemplate.PreferredWidth = i == 0 ? 33 : 10;
                                    }
                                    lstColumnDetails.Add(cellTemplate);
                                }
                                lstColumnDataDetails.Add("Sum " + lineGroupIds.FirstOrDefault(x => x.lineGroupId == lg).lineGroup);

                                if (userDefinedCols.Contains("year1"))
                                {
                                    lstColumnDataDetails.Add(divideByMillions == true ? (subDataset.Sum(x => x.column1 / 1000000)).ToString(numberTypeDecimalAmount) : (subDataset.Sum(x => x.column1 / 1000)).ToString(numberTypeAmount));
                                }

                                if (userDefinedCols.Contains("year2"))
                                {
                                    lstColumnDataDetails.Add(divideByMillions == true ? (subDataset.Sum(x => x.column2 / 1000000)).ToString(numberTypeDecimalAmount) : (subDataset.Sum(x => x.column2 / 1000)).ToString(numberTypeAmount));
                                }

                                if (userDefinedCols.Contains("year3"))
                                {
                                    lstColumnDataDetails.Add(divideByMillions == true ? (subDataset.Sum(x => x.column3 / 1000000)).ToString(numberTypeDecimalAmount) : (subDataset.Sum(x => x.column3 / 1000)).ToString(numberTypeAmount));
                                }

                                if (userDefinedCols.Contains("year4"))
                                {
                                    lstColumnDataDetails.Add(divideByMillions == true ? (subDataset.Sum(x => x.column4 / 1000000)).ToString(numberTypeDecimalAmount) : (subDataset.Sum(x => x.column4 / 1000)).ToString(numberTypeAmount));
                                }

                                if (userDefinedCols.Contains("budgetyear"))
                                {
                                    lstColumnDataDetails.Add(divideByMillions == true ? (subDataset.Sum(x => x.column5 / 1000000)).ToString(numberTypeDecimalAmount) : (subDataset.Sum(x => x.column5 / 1000)).ToString(numberTypeAmount));
                                }

                                if (userDefinedCols.Contains("deviation"))
                                {
                                    lstColumnDataDetails.Add(divideByMillions == true ? ((subDataset.Sum(x => x.column5) / 1000000) - (subDataset.Sum(x => x.column4) / 1000000)).ToString(numberTypeDecimalAmount) : ((subDataset.Sum(x => x.column5) / 1000) - (subDataset.Sum(x => x.column4) / 1000)).ToString(numberTypeAmount));
                                }

                                column5 = divideByMillions ? (subDataset.Sum(x => x.column5) / 1000000) : (subDataset.Sum(x => x.column5) / 1000);
                                column4 = divideByMillions ? (subDataset.Sum(x => x.column4) / 1000000) : (subDataset.Sum(x => x.column4) / 1000);

                                if (userDefinedCols.Contains("deviationpc"))
                                {
                                    lstColumnDataDetails.Add(Math.Abs(column5) <= roundOffErrorPc ? 0.ToString(numberTypePercentage) : (((column5 - column4) / column5) * 100).ToString(numberTypePercentage));
                                }

                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.DefaultTextColor);
                                    lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportSubTotalColor;
                                    lstColumnDetails.ForEach(p => p.Fontsize = 17);
                                    lstColumnDetails.ForEach(p => p.IsSemiBold = true);
                                    lstColumnDetails.ForEach(p => p.IsTotalRow = true);
                                }
                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                }
                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                }
                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                {
                                    lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.DefaultTextColor);
                                    lstColumnDetails.ForEach(p => p.Fontsize = 9);
                                    lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                                }
                            }
                        }
                    }

                    lstColumnDetails = new List<ColumnDetails>();
                    lstColumnDataDetails = new List<string>();
                    for (int i = 0; i <= maxNumAcColumns; i++)
                    {
                        var cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                        cellTemplate.Alignment = i == 0 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        cellTemplate.TopBorder = 1;
                        cellTemplate.BottomBorder = 1;
                        cellTemplate.IsBold = true;
                        cellTemplate.FontColor = FramsiktColors.TotalColor;
                        if ((i == 0 || i == maxNumAcColumns - 1 || i == maxNumAcColumns) && publishHelper.GetType() == typeof(WordHelper))
                        {
                            cellTemplate.PreferredWidth = i == 0 ? 33 : 10;
                        }
                        if (i == maxNumAcColumns)
                        {
                            cellTemplate.ColumnType = "fxbudadj";
                        }
                        else
                        {
                            cellTemplate.ColumnType = userDefinedCols[i];
                        }

                        lstColumnDetails.Add(cellTemplate);
                    }

                    lstColumnDataDetails.Add(langStringValuesMonthlyReportAcc["MRI_AccountingData_total_row"].LangText);
                    if (userDefinedCols.Contains("year1"))
                    {
                        lstColumnDataDetails.Add(divideByMillions == true ? (dataset.Sum(x => x.column1) / 1000000).ToString(numberTypeDecimalAmount) : (dataset.Sum(x => x.column1) / 1000).ToString(numberTypeAmount));
                    }

                    if (userDefinedCols.Contains("year2"))
                    {
                        lstColumnDataDetails.Add(divideByMillions == true ? (dataset.Sum(x => x.column2) / 1000000).ToString(numberTypeDecimalAmount) : (dataset.Sum(x => x.column2) / 1000).ToString(numberTypeAmount));
                    }

                    if (userDefinedCols.Contains("year3"))
                    {
                        lstColumnDataDetails.Add(divideByMillions == true ? (dataset.Sum(x => x.column3) / 1000000).ToString(numberTypeDecimalAmount) : (dataset.Sum(x => x.column3) / 1000).ToString(numberTypeAmount));
                    }

                    if (userDefinedCols.Contains("year4"))
                    {
                        lstColumnDataDetails.Add(divideByMillions == true ? (dataset.Sum(x => x.column4) / 1000000).ToString(numberTypeDecimalAmount) : (dataset.Sum(x => x.column4) / 1000).ToString(numberTypeAmount));
                    }

                    if (userDefinedCols.Contains("budgetyear"))
                    {
                        lstColumnDataDetails.Add(divideByMillions == true ? (dataset.Sum(x => x.column5) / 1000000).ToString(numberTypeDecimalAmount) : (dataset.Sum(x => x.column5) / 1000).ToString(numberTypeAmount));
                    }

                    if (userDefinedCols.Contains("deviation"))
                    {
                        lstColumnDataDetails.Add(divideByMillions == true ? ((dataset.Sum(x => x.column5) / 1000000) - (dataset.Sum(x => x.column4) / 1000000)).ToString(numberTypeDecimalAmount) : ((dataset.Sum(x => x.column5) / 1000) - (dataset.Sum(x => x.column4) / 1000)).ToString(numberTypeAmount));
                    }

                    var column5Total = divideByMillions ? (dataset.Sum(x => x.column5) / 1000000) : (dataset.Sum(x => x.column5) / 1000);
                    var column4Total = divideByMillions ? (dataset.Sum(x => x.column4) / 1000000) : (dataset.Sum(x => x.column4) / 1000);

                    if (userDefinedCols.Contains("deviationpc"))
                    {
                        lstColumnDataDetails.Add(Math.Abs(column5Total) <= roundOffErrorPc ? 0.ToString(numberTypePercentage) : (((column5Total - column4Total) / column5Total) * 100).ToString(numberTypePercentage));
                    }

                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        lstColumnDetails.ForEach(x => x.FontColor = FramsiktColors.MonthlyReportTotalColor);
                        lstColumnDetails.ForEach(x => x.IsTotalRow = true);
                        lstColumnDetails.ForEach(x => x.Fontsize = 17);
                        lstColumnDetails.ForEach(x => x.IsBold = true);
                    }
                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                    if (publishHelper.GetType() == typeof(WebHelperMr))
                    {
                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                    }

                    publishHelper.EndTable();
                    publishHelper.EndTableOrientation();
                }
            }
            catch (Exception)
            {
                publishHelper.InsertError("Failed to insert Accounting data");
                throw;
            }
        }



        private void InsertInvestmentDataAtCityLevel(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, int orgLevel, string otherOrgId, bool isDescNode)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            TableDefinition tabledef = null;
            try
            {
                var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesMonthlyReportInv = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport_Investment");
                Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
                //bool divideByMillions = _utility.CheckOnDivideByMillions(user, "Million_MonthlyReport_InvestmentsGrids");
                string showInvestmentWithZero = _utility.GetParameterValue(user, "MR_SHOW_INV_ZERO");
                bool divideByMillions = false;
                var isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                string numberTypeAmount = numberFormats["amount"].LangText;
                string numberTypePercentage = numberFormats["percentage"].LangText;
                string numberTypeDecimalAmount = numberFormats["dec1"].LangText;
                int invId = 0;
                dynamic labels = new JArray();
                string invName = string.Empty;
                string invDetailPath = string.Empty;

                var monthlyReportParameters = (from monthlyReportParams in tenantDbContext.vw_tco_parameters
                                               where monthlyReportParams.fk_tenant_id == userDetails.tenant_id && (monthlyReportParams.param_name == "MONTHREP_LEVEL_1" || monthlyReportParams.param_name == "MONTHREP_LEVEL_2") && monthlyReportParams.active == 1
                                               select monthlyReportParams).OrderBy(x => x.param_value).ToList();

                var portfolioParam = tenantDbContext.vw_tco_parameters.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == "INV_USE_PORTFOLIO").FirstOrDefault();
                if (portfolioParam == null || portfolioParam.param_value.ToLower() == "false")
                {
                    if (monthlyReportParameters.Count == 1 && monthlyReportParameters[0].param_value.Contains("service_id") && !isDescNode)
                    {
                        var configuredServiceID = monthlyReportParameters[0].param_value;
                        int serviceIDLevel = 0;
                        serviceIDLevel = configuredServiceID == "service_id_1" ? 1 : configuredServiceID == "service_id_2" ? 2 : configuredServiceID == "service_id_3" ? 3 :
                                         configuredServiceID == "service_id_4" ? 4 : configuredServiceID == "service_id_5" ? 5 : 0;
                        var dataforPublishingNode = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, -1, serviceIDLevel);
                        InsertAggregatedInvestmentDataforService(user, builder, budgetYear, forecastPeriod, dataforPublishingNode.ToList(), -1, serviceIDLevel, isDescNode);
                    }
                    else if (monthlyReportParameters.Count == 1 && monthlyReportParameters[0].param_value.Contains("service_id") && isDescNode)
                    {
                        var configuredServiceID = monthlyReportParameters[0].param_value;
                        int serviceIDLevel = 0;
                        serviceIDLevel = configuredServiceID == "service_id_1" ? 1 : configuredServiceID == "service_id_2" ? 2 : configuredServiceID == "service_id_3" ? 3 :
                                         configuredServiceID == "service_id_4" ? 4 : configuredServiceID == "service_id_5" ? 5 : 0;
                        var dataforPublishingNode = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, -1, serviceIDLevel);
                        InsertAggregatedInvestmentDataDescforService(user, builder, budgetYear, forecastPeriod, dataforPublishingNode.ToList(), -1, serviceIDLevel, isDescNode);
                    }
                    else
                    {
                        if (isDescNode)
                        {
                            tco_org_hierarchy toh = tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefault();
                            string tenantCityOrgId = toh != null ? toh.org_id_1 : string.Empty;
                            string tenantCityOrgLevel = "1";
                            bool isFeatureEnabled = _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text).GetAwaiter().GetResult();
                            if (isFeatureEnabled)
                            {
                                tco_monthrep_descriptions tmt = tenantDbContext.tco_monthrep_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                && x.budget_year == budgetYear
                                                                                                && x.forecast_period == forecastPeriod
                                                                                                && x.org_level == tenantCityOrgLevel
                                                                                                && x.org_id == tenantCityOrgId
                                                                                                && x.acc_group_value == "NA"
                                                                                                && x.description_type == nameof(MonthlyReportTextBoxType.MRInvestmentsStatusText)).FirstOrDefault();
                                if (tmt != null && !string.IsNullOrEmpty(tmt.description_text))
                                {
                                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                                    publishHelper.InsertDescriptionCk5(tmt.description_text, user);
                                }
                            }
                            else
                            {
                                tco_monthrep_texts tmt = tenantDbContext.tco_monthrep_texts.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                                && x.budget_year == budgetYear
                                                                                                                                && x.forecast_period == forecastPeriod
                                                                                                                                && x.org_level == tenantCityOrgLevel
                                                                                                                                && x.org_id == tenantCityOrgId
                                                                                                                                && x.acc_group_value == "NA").FirstOrDefault();
                                if (tmt != null && !string.IsNullOrEmpty(tmt.mrinvestments_status_text))
                                {
                                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                                    publishHelper.InsertDescriptionCk5(tmt.mrinvestments_status_text, user);
                                }
                            }
                            
                        }
                        else
                        {
                            builder.InsertBreak(BreakType.SectionBreakNewPage);
                            //builder.PageSetup.Orientation = Orientation.Landscape;
                            publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investment_desc"].LangText, _context, true);
                        }

                        string showAllInvInOneTable = _utility.GetParameterValue(user, "MRDOC_SHOW_INV_IN_ONE_TABLE");
                        if (showAllInvInOneTable.ToLower() == "true")
                        {
                            // get all service Area ids
                            var serviceAreas = _utility.GetServiceAreaUnit(orgVersionContent, user);
                            var SAIds = (from d in serviceAreas
                                         select new
                                         {
                                             d.ServiceAreaName,
                                             d.ServiceAreaID
                                         }).Distinct().ToList();

                            List<MonthlyReportInvestmentHelper> finalResultSet = new List<MonthlyReportInvestmentHelper>();

                            //get risk list
                            var risklist = _monthlyReportInvestment.GetRiskList(user);
                            //get risk list
                            var statuslist = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user);

                            //quater data
                            var quarterData = _utility.GetEstimatedFinishQuarter(budgetYear, 1, user);

                            foreach (var sa in SAIds.Select(x => x.ServiceAreaID).ToList())
                            {
                                string OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5;
                                //assgin service id value to corret  orglevel
                                switch (orgLevel.ToString())
                                {
                                    case "1":
                                        OrgLevel1 = sa;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        break;

                                    case "2":
                                        OrgLevel2 = sa;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        OrgLevel1 = null;
                                        break;

                                    case "3":
                                        OrgLevel3 = sa;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        break;

                                    case "4":
                                        OrgLevel4 = sa;
                                        OrgLevel5 = null;
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        break;

                                    case "5":
                                        OrgLevel5 = sa;
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        break;

                                    default:
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        break;
                                }

                                //get List of department from orgstructure
                                var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5);

                                var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, null);

                                //get the data from service
                                List<MonthlyReportInvestmentHelper> resultSet = _monthlyReportInvestment.GetInvestementData(user, forecastPeriod, sa, orgLevel, null, departmentList, functionList, true, false, true);

                                //drop the inv having accounting and budget both equals to 0 in yearly setup
                                if (isYearlySetupEnabled && (showInvestmentWithZero.ToLower() != "TRUE".ToLower()))
                                {
                                    resultSet.RemoveAll(x => x.Accounting == 0 && x.AdjustedBudget == 0);
                                }
                                finalResultSet.AddRange(resultSet);
                                if (isDescNode)
                                {
                                    DescToBlobHelper input = new DescToBlobHelper();
                                    input.ModuleId = "MONTHREP";
                                    input.BudgetYear = budgetYear;
                                    input.FieldId = "MRIDESC";

                                    if (string.IsNullOrEmpty(otherOrgId))
                                    {
                                        input.OrgLevel2 = sa;
                                    }
                                    else
                                    {
                                        input.OrgLevel2 = otherOrgId;
                                        input.OrgLevel3 = sa;
                                    }

                                    dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);

                                    string finPlanDeviation = result.description.ToString();

                                    input.FieldId = "MRIBUDSTAT";
                                    result = _utility.GetDescriptionText(input, user, forecastPeriod);
                                    string budgetStatus = result.description.ToString();
                                    if (!string.IsNullOrEmpty((finPlanDeviation)))
                                    {
                                        publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                                    }
                                    if (!string.IsNullOrEmpty((budgetStatus)))
                                    {
                                        publishHelper.InsertDescriptionCk5(budgetStatus, user);
                                    }
                                }
                            }
                            finalResultSet = _monthlyReportInvestment.FormatInvestmentDataSet(finalResultSet);
                            //convert it to JArray
                            JArray dataArray = JArray.FromObject(finalResultSet);
                            int outParam = 0;
                            if (dataArray.Any())
                            {
                                if (!isDescNode)
                                {
                                    List<string> defaultColumn = new List<string>();

                                    if (isYearlySetupEnabled)
                                    {
                                        tabledef = _docTableConfig.GetTableDef(user, "MrYearlyInvestment");
                                    }
                                    else
                                    {
                                        tabledef = _docTableConfig.GetTableDef(user, "MrInvestment");
                                    }

                                    divideByMillions = tabledef.AmountFormat == AmountFormats.Millions;

                                    foreach (var item in tabledef.ColumnDefinitions.ToList())
                                    {
                                        defaultColumn.Add(item.ColumnId);
                                    }
                                    publishHelper.StartTableOrientation(tabledef);
                                    publishHelper.StartTable("InvestmentGrid", null, null, tabledef);

                                    List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                                    List<string> lstColumnDataDetails = new List<string>();

                                    int count = 0;

                                    int set1HeaderIndex = defaultColumn.Count(x => x.ToLower() == "totalamount" ||
                                        x.ToLower() == "changetotalamount" || x.ToLower() == "deviation" || x.ToLower() == "totalorgbudget" || x.ToLower() == "costapproval");
                                    int set2HeaderIndex = defaultColumn.Count(x => x.ToLower() == "status" ||
                                        x.ToLower() == "plannedamount" || x.ToLower() == "regulation" || x.ToLower() == "forecastamount" ||
                                        x.ToLower() == "forecastdeviation" || x.ToLower() == "adjustedbudget" || x.ToLower() == "UnApprvBudChange".ToLower() || x.ToLower() == "TotalYbUbcBudget".ToLower() ||
                                        x.ToLower() == "proposedadjustment" || x.ToLower() == "accounting".ToLower() || x.ToLower() == "budgetacctdeviation".ToLower());

                                    lstColumnDetails = new List<ColumnDetails>();
                                    lstColumnDataDetails = new List<string>();
                                    var cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                    cellTemplate.Alignment = ParagraphAlignment.Right;
                                    cellTemplate.WrapText = true;
                                    cellTemplate.Column = 1;
                                    cellTemplate.Fontsize = 9;
                                    //cellTemplate.PreferredWidth = 18;
                                    lstColumnDetails.Add(cellTemplate);

                                    cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                    cellTemplate.Alignment = ParagraphAlignment.Center;
                                    cellTemplate.WrapText = true;
                                    cellTemplate.Column = set1HeaderIndex;
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                    cellTemplate.Fontsize = 9;
                                    //cellTemplate.PreferredWidth = 17;
                                    lstColumnDetails.Add(cellTemplate);

                                    cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                    cellTemplate.Alignment = ParagraphAlignment.Center;
                                    cellTemplate.WrapText = true;
                                    cellTemplate.Column = set2HeaderIndex;
                                    cellTemplate.Fontsize = 9;
                                    //cellTemplate.PreferredWidth = 29;
                                    lstColumnDetails.Add(cellTemplate);

                                    lstColumnDataDetails.Add(string.Empty);
                                    lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_total_budget"].LangText);
                                    lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_yearly_budget"].LangText);

                                    int blankCells = defaultColumn.Count - (set1HeaderIndex + set2HeaderIndex + 1);
                                    for (int i = 0; i < blankCells; i++)
                                    {
                                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                        cellTemplate.Alignment = ParagraphAlignment.Center;
                                        cellTemplate.WrapText = true;

                                        lstColumnDetails.Add(cellTemplate);

                                        lstColumnDataDetails.Add(string.Empty);
                                    }

                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.MonthlyReportSubHeaderColor);
                                        lstColumnDetails.ForEach(p => p.Fontsize = 17);
                                        lstColumnDetails.ForEach(p => p.IsSemiBold = true);
                                    }

                                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading1);

                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                    }

                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        lstColumnDetails.ForEach(x => x.FontColor = FramsiktColors.DefaultTextColor);
                                        lstColumnDetails.ForEach(p => p.Fontsize = 9);
                                        lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                                    }

                                    count = 0;

                                    // create the header row
                                    lstColumnDetails = new List<ColumnDetails>();
                                    lstColumnDataDetails = new List<string>();
                                    builder.CellFormat.ClearFormatting();
                                    builder.RowFormat.HeadingFormat = true;
                                    foreach (var c in defaultColumn)
                                    {
                                        count++;
                                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                        //  if (i < 3)
                                        cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                        //  else
                                        //     cellTemplate.Alignment = ParagraphAlignment.Right;
                                        cellTemplate.WrapText = true;
                                        cellTemplate.BottomBorder = 1;
                                        cellTemplate.Fontsize = 9;

                                        if (c.ToLower() == "investment".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 18;
                                        }
                                        if (c.ToLower() == "statusreview".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 82 - (defaultColumn.Count * 5.5) + 15;
                                        }
                                        if (c.ToLower() == "totalamount".ToLower() || c.ToLower() == "totalorgbudget" || c.ToLower() == "costapproval")
                                        {
                                            cellTemplate.PreferredWidth = 6;
                                            cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                        }
                                        if (c.ToLower() == "changetotalamount".ToLower() || c.ToLower() == "deviation".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 6;
                                            cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                        }
                                        if (c.ToLower() == "EstimatedQuarter".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 7;
                                            cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                        }
                                        if (c.ToLower() == "status".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 5.5;
                                        }
                                        if (c.ToLower() == "regulation".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 6.5;
                                        }
                                        if (c.ToLower() == "adjustedbudget".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 6;
                                        }
                                        if (c.ToLower() == "forecastamount".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 5.5;
                                        }
                                        if (c.ToLower() == "accounting".ToLower() || c.ToLower() == "UnApprvBudChange".ToLower() || c.ToLower() == "TotalYbUbcBudget".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 6.5;
                                        }
                                        if (c.ToLower() == "forecastdeviation".ToLower() || c.ToLower() == "budgetacctdeviation".ToLower())
                                        {
                                            cellTemplate.PreferredWidth = 5.5;
                                        }
                                        if (c.ToLower() == "proposedadjustment".ToLower() || c.ToLower() == "risk")
                                        {
                                            cellTemplate.PreferredWidth = 5;
                                        }
                                        if (c.ToLower() == "finishedyear")
                                        {
                                            cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                        }
                                        if (c.ToLower() == "risk" || c.ToLower() == "statusreview" || c.ToLower() == "investment")
                                        {
                                            cellTemplate.Alignment = ParagraphAlignment.Left;
                                        }
                                        cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                                        lstColumnDetails.Add(cellTemplate);
                                        lstColumnDataDetails.Add(tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == c.ToLower()).ColumnName);
                                    }

                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                                    }

                                    publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                    }
                                    builder.RowFormat.HeadingFormat = false;

                                    var columns = publishHelper.GetCurrentChapterColumns();
                                    labels = new JArray();
                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        foreach (var col in columns)
                                        {
                                            if (col.columns.Count() > 0)
                                            {
                                                foreach (var colChild in col.columns)
                                                {
                                                    if (colChild.columns.Count() > 0)
                                                    {
                                                        foreach (var colChildSub in colChild.columns)
                                                        {
                                                            if (colChildSub.columns.Count() > 0)
                                                            {
                                                                foreach (var colChildSubSuper in colChildSub.columns)
                                                                {
                                                                    if (colChildSubSuper.columns.Count() > 0)
                                                                    {
                                                                        if (!string.IsNullOrEmpty(colChildSubSuper.title.Trim()))
                                                                        {
                                                                            labels.Add(colChildSubSuper.title);
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            else
                                                            {
                                                                if (!string.IsNullOrEmpty(colChildSub.title.Trim()))
                                                                {
                                                                    labels.Add(colChildSub.title);
                                                                }
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (!string.IsNullOrEmpty(colChild.title.Trim()))
                                                        {
                                                            labels.Add(colChild.title);
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if (!string.IsNullOrEmpty(col.title.Trim()))
                                                {
                                                    labels.Add(col.title);
                                                }
                                            }
                                        }
                                    }

                                    // Get the PropertyInfo object by passing the property name.
                                    PropertyInfo[] myPropInfo = typeof(MonthlyReportInvestmentHelper).GetProperties();
                                    // insert data for the document
                                    foreach (var d in dataArray)
                                    {
                                        dynamic invData = new JArray();
                                        lstColumnDetails = new List<ColumnDetails>();
                                        lstColumnDataDetails = new List<string>();

                                        //for each column to display insert record
                                        count = 0;
                                        foreach (var col in defaultColumn)
                                        {
                                            count++;
                                            cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                                            cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                            cellTemplate.WrapText = true;
                                            cellTemplate.IsBold = false;
                                            if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && ((int)d["PkId"]) == -1)
                                            {
                                                if (publishHelper.GetType() == typeof(WebHelperMr))
                                                {
                                                    if (count == 1)
                                                    {
                                                        cellTemplate.FontColor = FramsiktColors.MonthlyReportSubTotalColor;
                                                        cellTemplate.Fontsize = 17;
                                                        cellTemplate.IsSemiBold = true;
                                                    }
                                                    cellTemplate.IsTotalRow = true;
                                                }
                                                else
                                                {
                                                    cellTemplate.FontColor = FramsiktColors.TotalColor;
                                                }
                                            }

                                            if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "deviation" || col.ToLower() == "totalorgbudget" || col.ToLower() == "costapproval" || col.ToLower() == "EstimatedQuarter".ToLower() || col.ToLower() == "FinishedYear".ToLower())
                                            {
                                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                            }
                                            if (col.ToLower() == "risk" || col.ToLower() == "statusreview" || col.ToLower() == "investment")
                                            {
                                                cellTemplate.Alignment = ParagraphAlignment.Left;
                                            }

                                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                                            cellTemplate.TopBorder = 0;
                                            cellTemplate.BottomBorder = 1;

                                            if (col.ToLower() == "accounting")
                                            {
                                                cellTemplate.ColumnType = "accounting";
                                            }
                                            else if (col.ToLower() == "TotalYbUbcBudget".ToLower())
                                            {
                                                cellTemplate.ColumnType = "totalybubcbudget";
                                            }
                                            else if (col.ToLower() == "UnApprvBudChange".ToLower())
                                            {
                                                cellTemplate.ColumnType = "unapprvbudchange";
                                            }
                                            else if (col.ToLower() == "accountingtotal")
                                            {
                                                cellTemplate.ColumnType = "accountingtotal";
                                            }
                                            else if (col.ToLower() == "actualamtlastyear")
                                            {
                                                cellTemplate.ColumnType = "actualamtlastyear";
                                            }
                                            else if (col.ToLower() == "adjustedbudget")
                                            {
                                                cellTemplate.ColumnType = "adjustedbudget";
                                            }
                                            else if (col.ToLower() == "budgetacctdeviation")
                                            {
                                                cellTemplate.ColumnType = "budgetacctdeviation";
                                            }
                                            else if (col.ToLower() == "changetotalamount")
                                            {
                                                cellTemplate.ColumnType = "changetotalamount";
                                            }
                                            else if (col.ToLower() == "consumepercentage")
                                            {
                                                cellTemplate.ColumnType = "consumepercentage";
                                            }
                                            else if (col.ToLower() == "deviation")
                                            {
                                                cellTemplate.ColumnType = "deviation";
                                            }
                                            else if (col.ToLower() == "EstimatedQuarter".ToLower())
                                            {
                                                cellTemplate.ColumnType = "EstimatedQuarter";
                                            }
                                            else if (col.ToLower() == "FinishedYear".ToLower())
                                            {
                                                cellTemplate.ColumnType = "FinishedYear";
                                            }
                                            else if (col.ToLower() == "forecastamount".ToLower())
                                            {
                                                cellTemplate.ColumnType = "forecastamount";
                                            }
                                            else if (col.ToLower() == "forecastdeviation".ToLower())
                                            {
                                                cellTemplate.ColumnType = "forecastdeviation";
                                            }
                                            else if (col.ToLower() == "investment".ToLower())
                                            {
                                                cellTemplate.ColumnType = "investment";
                                            }
                                            else if (col.ToLower() == "plannedamount".ToLower())
                                            {
                                                cellTemplate.ColumnType = "plannedamount";
                                            }
                                            else if (col.ToLower() == "regulation".ToLower())
                                            {
                                                cellTemplate.ColumnType = "regulation";
                                            }
                                            else if (col.ToLower() == "risk".ToLower())
                                            {
                                                cellTemplate.ColumnType = "risk";
                                            }
                                            else if (col.ToLower() == "status".ToLower())
                                            {
                                                cellTemplate.ColumnType = "status";
                                            }
                                            else if (col.ToLower() == "statusreview".ToLower())
                                            {
                                                cellTemplate.ColumnType = "statusreview";
                                            }
                                            else if (col.ToLower() == "totalamount".ToLower())
                                            {
                                                cellTemplate.ColumnType = "totalamount";
                                            }

                                            string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                                            //apply number formatting of for number column
                                            if (name.ToLower() != "investment" && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter" && name.ToLower() != "approvalreference")
                                            {
                                                if (name.ToLower() != "consumepercentage")
                                                {
                                                    decimal ammount = 0;

                                                    bool success = decimal.TryParse(d[name].ToString(), out ammount);
                                                    if (name.ToLower() == "costapproval")
                                                    {
                                                        if (ammount == 0 || !string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                                        {
                                                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_approvalCostText"].LangText);
                                                        }
                                                        else
                                                        {
                                                            if (!divideByMillions)
                                                            {
                                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                            }
                                                            else
                                                            {
                                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (!divideByMillions)
                                                        {
                                                            lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                        }
                                                        else
                                                        {
                                                            lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    lstColumnDataDetails.Add((!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] != -1) ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                                                }
                                            }
                                            else
                                            {
                                                if (name.ToLower() == "statusreview")
                                                {
                                                    lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                                                }
                                                else if (name.ToLower() == "status")
                                                {
                                                    var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                                                    lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                                                }
                                                else if (name.ToLower() == "risk")
                                                {
                                                    var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                                                    lstColumnDataDetails.Add(risk != null ? (string)risk.Value : "");
                                                    if (risk != null)
                                                    {
                                                        switch (risk.Key)
                                                        {
                                                            case "1":
                                                                lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel1Color;
                                                                break;

                                                            case "2":
                                                                lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel2Color;
                                                                break;

                                                            case "3":
                                                                lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel3Color;
                                                                break;
                                                        }
                                                    }
                                                }
                                                else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == 0) || (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1))) || ((name.ToLower() == "finishedyear") && (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1)))
                                                {
                                                    lstColumnDataDetails.Add(string.Empty);
                                                }
                                                else if (name.ToLower() == "finishedyear")
                                                {
                                                    if (((string)d["invStatusDescription"] != ""))
                                                    {
                                                        lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                                    }
                                                    else
                                                    {
                                                        lstColumnDataDetails.Add(((string)d[name]));
                                                    }
                                                }
                                                else if (name.ToLower() == "estimatedquarter" && ((int)d[name] != 0))
                                                {
                                                    if (((string)d["invStatusDescription"] != ""))
                                                    {
                                                        lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                                    }
                                                    else
                                                    {
                                                        var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                                        lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : "");
                                                    }
                                                }
                                                else if (name.ToLower() == "approvalreference")
                                                {
                                                    if (!string.IsNullOrEmpty((string)d["ApprovalReferenceUrl"]))
                                                    {
                                                        cellTemplate.ContentType = ContentType.Url;
                                                        lstColumnDataDetails.Add((string)d["ApprovalReferenceUrl"] + "_endurl_" + (string)d[name]);
                                                    }
                                                    else
                                                    {
                                                        lstColumnDataDetails.Add((string)d[name]);
                                                    }
                                                }
                                                else
                                                {
                                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                                    {
                                                        if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])))
                                                        {
                                                            string rowtemplateWithData = string.Empty;
                                                            rowtemplateWithData = $"<a href='javascript:void(0)' title = \"(Sprettoppvindu) {(string)d[name]}\" onkeypress = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString((d["PkId"]))}\")' onClick = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString((d["PkId"]))}\")'>{(string)d[name]} <span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                                                            lstColumnDataDetails.Add(rowtemplateWithData);

                                                            invId = Convert.ToInt32(d["PkId"]);
                                                            invName = ((string)d[name]);
                                                            invDetailPath = publishHelper.GetCurrentChapterPath() + "invdetails/";
                                                        }
                                                    }
                                                    else
                                                    {
                                                        lstColumnDataDetails.Add(((string)d[name]));
                                                    }
                                                }
                                            }
                                            lstColumnDetails.Add(cellTemplate);
                                        }
                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                                        }
                                        if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && ((int)d["PkId"]) == -1)
                                            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                                        else
                                            publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                        }
                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.DefaultTextColor);
                                            lstColumnDetails.ForEach(p => p.Fontsize = 9);
                                            lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                                        }

                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            int dcount = 0;
                                            var drow = publishHelper.GetCurrentRowData();
                                            drow.ForEach(x =>
                                            {
                                                if (dcount == 0)
                                                {
                                                    invData.Add(Regex.Replace(x, "<.*?>", String.Empty));
                                                }
                                                else
                                                {
                                                    if (!string.IsNullOrEmpty(x) && x.Contains("_endurl_"))
                                                    {
                                                        x = "<a href=" + (string)d["ApprovalReferenceUrl"] + " target = _blank>" + (string)d["ApprovalReference"] + "</a>";
                                                        invData.Add(x);
                                                    }
                                                    else
                                                    {
                                                        invData.Add(x);
                                                    }
                                                }

                                                dcount++;
                                            });
                                        }

                                        //web popup
                                        if (publishHelper.GetType() == typeof(WebHelperMr) && invData.Count > 0)
                                        {
                                            dynamic invObject = new JObject();
                                            invData[0] = invName;
                                            invObject.Add("Data", invData);
                                            invObject.Add("Labels", labels);
                                            invObject.WebUrlLink = string.Empty;
                                            invObject.popUpTitle = invName;

                                            string description = string.Empty;
                                            //description = _utility.GetInvestmentsDescription(user, invId, budgetYear, "external", Guid.Empty).Value;
                                            invObject.popUpDescription1 = string.IsNullOrEmpty(description) ? "" : description;

                                            string description2 = string.Empty;
                                            if (invId != 0)
                                            {
                                                List<MonthlyReportInvestmentHelper> descriptiondata = finalResultSet;
                                                string desc2 = descriptiondata.ToList().FirstOrDefault(x => x.PkId == invId).Statusreview;
                                                description2 = desc2;
                                            }
                                            invObject.popUpDescription2 = description2;

                                            invObject.popUpHeaderDesc1 = string.IsNullOrEmpty(description) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc1_heading"].LangText;
                                            invObject.popUpHeaderDesc2 = string.IsNullOrEmpty(description2) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc2_heading"].LangText;

                                            string showFpDescription = _utility.GetParameterValue(user, "MR_PUBLISHDisplay_FinPlan_description_In_Popup");
                                            if (!string.IsNullOrEmpty(showFpDescription) && showFpDescription.ToLower() == "true")
                                            {
                                                invObject.showPopupDescription = true;
                                            }
                                            else
                                            {
                                                invObject.showPopupDescription = false;
                                            }

                                            publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(invObject), invDetailPath, (invId).ToString(), PubContentHandler.BlobType.Json);
                                        }
                                    }
                                    publishHelper.EndTable();
                                    publishHelper.EndTableOrientation();
                                    builder.InsertBreak(BreakType.SectionBreakContinuous);
                                }
                            }
                        }
                        else
                        {
                            // get all service Area ids
                            var serviceAreas = _utility.GetServiceAreaUnit(orgVersionContent, user);
                            var SAIds = (from d in serviceAreas
                                         select new
                                         {
                                             d.ServiceAreaName,
                                             d.ServiceAreaID
                                         }).Distinct().ToList();

                            foreach (var sa in SAIds.Select(x => x.ServiceAreaID).ToList())
                            {
                                string OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5;
                                //assgin service id value to corret  orglevel
                                switch (orgLevel.ToString())
                                {
                                    case "1":
                                        OrgLevel1 = sa;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        break;

                                    case "2":
                                        OrgLevel2 = sa;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        OrgLevel1 = null;
                                        break;

                                    case "3":
                                        OrgLevel3 = sa;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        break;

                                    case "4":
                                        OrgLevel4 = sa;
                                        OrgLevel5 = null;
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        break;

                                    case "5":
                                        OrgLevel5 = sa;
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        break;

                                    default:
                                        OrgLevel1 = null;
                                        OrgLevel2 = null;
                                        OrgLevel3 = null;
                                        OrgLevel4 = null;
                                        OrgLevel5 = null;
                                        break;
                                }

                                //get List of department from orgstructure
                                var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5);

                                var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, null);

                                //get the data from service
                                List<MonthlyReportInvestmentHelper> resultSet = _monthlyReportInvestment.GetInvestementData(user, forecastPeriod, sa, orgLevel, null, departmentList, functionList, true, false, true);

                                //get risk list
                                var risklist = _monthlyReportInvestment.GetRiskList(user);

                                //get risk list
                                var statuslist = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user);

                                //quater data
                                var quarterData = _utility.GetEstimatedFinishQuarter(budgetYear, 1, user);

                                //drop the inv having accounting and budget both equals to 0 in yearly setup
                                if (isYearlySetupEnabled && (showInvestmentWithZero.ToLower() != "TRUE".ToLower()))
                                {
                                    resultSet.RemoveAll(x => x.Accounting == 0 && x.AdjustedBudget == 0);
                                }

                                //convert it to JArray
                                JArray dataArray = JArray.FromObject(resultSet);
                                int outParam = 0;
                                if (dataArray.Any())
                                {
                                    if (!isDescNode)
                                    {
                                        List<string> defaultColumn = new List<string>();

                                        if (isYearlySetupEnabled)
                                        {
                                            tabledef = _docTableConfig.GetTableDef(user, "MrYearlyInvestment");
                                        }
                                        else
                                        {
                                            tabledef = _docTableConfig.GetTableDef(user, "MrInvestment");
                                        }

                                        divideByMillions = tabledef.AmountFormat == AmountFormats.Millions;

                                        foreach (var item in tabledef.ColumnDefinitions.ToList())
                                        {
                                            defaultColumn.Add(item.ColumnId);
                                        }
                                        publishHelper.StartTableOrientation(tabledef);
                                        publishHelper.InsertHeading(user, SAIds.FirstOrDefault(x => x.ServiceAreaID == sa).ServiceAreaName, _context);

                                        publishHelper.StartTable("InvestmentGrid", sa, null, tabledef);

                                        List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                                        List<string> lstColumnDataDetails = new List<string>();

                                        int count = 0;

                                        int set1HeaderIndex = defaultColumn.Count(x => x.ToLower() == "totalamount" ||
                                            x.ToLower() == "changetotalamount" || x.ToLower() == "deviation" || x.ToLower() == "totalorgbudget" || x.ToLower() == "costapproval");
                                        int set2HeaderIndex = defaultColumn.Count(x => x.ToLower() == "status" ||
                                            x.ToLower() == "plannedamount" || x.ToLower() == "regulation" || x.ToLower() == "forecastamount" ||
                                            x.ToLower() == "forecastdeviation" || x.ToLower() == "adjustedbudget" || x.ToLower() == "UnApprvBudChange".ToLower() || x.ToLower() == "TotalYbUbcBudget".ToLower() ||
                                            x.ToLower() == "proposedadjustment" || x.ToLower() == "accounting".ToLower() || x.ToLower() == "budgetacctdeviation".ToLower());

                                        lstColumnDetails = new List<ColumnDetails>();
                                        lstColumnDataDetails = new List<string>();
                                        var cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                        cellTemplate.Alignment = ParagraphAlignment.Right;
                                        cellTemplate.WrapText = true;
                                        cellTemplate.Column = 1;
                                        cellTemplate.Fontsize = 9;
                                        //cellTemplate.PreferredWidth = 18;
                                        lstColumnDetails.Add(cellTemplate);

                                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                        cellTemplate.Alignment = ParagraphAlignment.Center;
                                        cellTemplate.WrapText = true;
                                        cellTemplate.Column = set1HeaderIndex;
                                        cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                        cellTemplate.Fontsize = 9;
                                        //cellTemplate.PreferredWidth = 17;
                                        lstColumnDetails.Add(cellTemplate);

                                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                        cellTemplate.Alignment = ParagraphAlignment.Center;
                                        cellTemplate.WrapText = true;
                                        cellTemplate.Column = set2HeaderIndex;
                                        cellTemplate.Fontsize = 9;
                                        //cellTemplate.PreferredWidth = 29;
                                        lstColumnDetails.Add(cellTemplate);

                                        lstColumnDataDetails.Add(string.Empty);
                                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_total_budget"].LangText);
                                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_yearly_budget"].LangText);

                                        int blankCells = defaultColumn.Count - (set1HeaderIndex + set2HeaderIndex + 1);
                                        for (int i = 0; i < blankCells; i++)
                                        {
                                            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                            cellTemplate.Alignment = ParagraphAlignment.Center;
                                            cellTemplate.WrapText = true;

                                            lstColumnDetails.Add(cellTemplate);

                                            lstColumnDataDetails.Add(string.Empty);
                                        }

                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.MonthlyReportSubHeaderColor);
                                            lstColumnDetails.ForEach(p => p.Fontsize = 17);
                                            lstColumnDetails.ForEach(p => p.IsSemiBold = true);
                                        }

                                        publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading1);

                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                        }

                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails.ForEach(x => x.FontColor = FramsiktColors.DefaultTextColor);
                                            lstColumnDetails.ForEach(p => p.Fontsize = 9);
                                            lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                                        }

                                        count = 0;

                                        // create the header row
                                        lstColumnDetails = new List<ColumnDetails>();
                                        lstColumnDataDetails = new List<string>();
                                        builder.CellFormat.ClearFormatting();
                                        builder.RowFormat.HeadingFormat = true;
                                        foreach (var c in defaultColumn)
                                        {
                                            count++;
                                            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                                            //  if (i < 3)
                                            cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                            //  else
                                            //     cellTemplate.Alignment = ParagraphAlignment.Right;
                                            cellTemplate.WrapText = true;
                                            cellTemplate.BottomBorder = 1;
                                            cellTemplate.Fontsize = 9;

                                            if (c.ToLower() == "investment".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 18;
                                            }
                                            if (c.ToLower() == "statusreview".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 82 - (defaultColumn.Count * 5.5) + 15;
                                            }
                                            if (c.ToLower() == "totalamount".ToLower() || c.ToLower() == "totalorgbudget" || c.ToLower() == "costapproval")
                                            {
                                                cellTemplate.PreferredWidth = 6;
                                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                            }
                                            if (c.ToLower() == "changetotalamount".ToLower() || c.ToLower() == "deviation".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 6;
                                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                            }
                                            if (c.ToLower() == "EstimatedQuarter".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 7;
                                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                            }
                                            if (c.ToLower() == "status".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 5.5;
                                            }
                                            if (c.ToLower() == "regulation".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 6.5;
                                            }
                                            if (c.ToLower() == "adjustedbudget".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 6;
                                            }
                                            if (c.ToLower() == "forecastamount".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 5.5;
                                            }
                                            if (c.ToLower() == "accounting".ToLower() || c.ToLower() == "UnApprvBudChange".ToLower() || c.ToLower() == "TotalYbUbcBudget".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 6.5;
                                            }
                                            if (c.ToLower() == "forecastdeviation".ToLower() || c.ToLower() == "budgetacctdeviation".ToLower())
                                            {
                                                cellTemplate.PreferredWidth = 5.5;
                                            }
                                            if (c.ToLower() == "proposedadjustment".ToLower() || c.ToLower() == "risk")
                                            {
                                                cellTemplate.PreferredWidth = 5;
                                            }
                                            if (c.ToLower() == "finishedyear")
                                            {
                                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                            }
                                            if (c.ToLower() == "risk" || c.ToLower() == "statusreview" || c.ToLower() == "investment")
                                            {
                                                cellTemplate.Alignment = ParagraphAlignment.Left;
                                            }
                                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                                            lstColumnDetails.Add(cellTemplate);
                                            lstColumnDataDetails.Add(tabledef.ColumnDefinitions.FirstOrDefault(k => k.ColumnId.ToLower() == c.ToLower()).ColumnName);
                                        }

                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                                        }

                                        publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);

                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                        }
                                        builder.RowFormat.HeadingFormat = false;

                                        var columns = publishHelper.GetCurrentChapterColumns();
                                        labels = new JArray();
                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                        {
                                            foreach (var col in columns)
                                            {
                                                if (col.columns.Count() > 0)
                                                {
                                                    foreach (var colChild in col.columns)
                                                    {
                                                        if (colChild.columns.Count() > 0)
                                                        {
                                                            foreach (var colChildSub in colChild.columns)
                                                            {
                                                                if (colChildSub.columns.Count() > 0)
                                                                {
                                                                    foreach (var colChildSubSuper in colChildSub.columns)
                                                                    {
                                                                        if (colChildSubSuper.columns.Count() > 0)
                                                                        {
                                                                            if (!string.IsNullOrEmpty(colChildSubSuper.title.Trim()))
                                                                            {
                                                                                labels.Add(colChildSubSuper.title);
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    if (!string.IsNullOrEmpty(colChildSub.title.Trim()))
                                                                    {
                                                                        labels.Add(colChildSub.title);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (!string.IsNullOrEmpty(colChild.title.Trim()))
                                                            {
                                                                labels.Add(colChild.title);
                                                            }
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    if (!string.IsNullOrEmpty(col.title.Trim()))
                                                    {
                                                        labels.Add(col.title);
                                                    }
                                                }
                                            }
                                        }

                                        // Get the PropertyInfo object by passing the property name.
                                        PropertyInfo[] myPropInfo = typeof(MonthlyReportInvestmentHelper).GetProperties();
                                        // insert data for the document
                                        foreach (var d in dataArray)
                                        {
                                            dynamic invData = new JArray();
                                            lstColumnDetails = new List<ColumnDetails>();
                                            lstColumnDataDetails = new List<string>();

                                            //for each column to display insert record
                                            count = 0;
                                            foreach (var col in defaultColumn)
                                            {
                                                count++;
                                                cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                                                cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                                cellTemplate.WrapText = true;
                                                cellTemplate.IsBold = false;
                                                if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && ((int)d["PkId"]) == -1)
                                                {
                                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                                    {
                                                        if (count == 1)
                                                        {
                                                            cellTemplate.FontColor = FramsiktColors.MonthlyReportSubTotalColor;
                                                            cellTemplate.Fontsize = 17;
                                                            cellTemplate.IsSemiBold = true;
                                                        }
                                                        cellTemplate.IsTotalRow = true;
                                                    }
                                                    else
                                                    {
                                                        cellTemplate.FontColor = FramsiktColors.TotalColor;
                                                    }
                                                }

                                                if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "deviation" || col.ToLower() == "totalorgbudget" || col.ToLower() == "costapproval" || col.ToLower() == "EstimatedQuarter".ToLower() || col.ToLower() == "FinishedYear".ToLower())
                                                {
                                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                                }
                                                if (col.ToLower() == "risk" || col.ToLower() == "statusreview" || col.ToLower() == "investment")
                                                {
                                                    cellTemplate.Alignment = ParagraphAlignment.Left;
                                                }

                                                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                                                cellTemplate.TopBorder = 0;
                                                cellTemplate.BottomBorder = 1;

                                                if (col.ToLower() == "accounting")
                                                {
                                                    cellTemplate.ColumnType = "accounting";
                                                }
                                                else if (col.ToLower() == "TotalYbUbcBudget".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "totalybubcbudget";
                                                }
                                                else if (col.ToLower() == "UnApprvBudChange".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "unapprvbudchange";
                                                }
                                                else if (col.ToLower() == "accountingtotal")
                                                {
                                                    cellTemplate.ColumnType = "accountingtotal";
                                                }
                                                else if (col.ToLower() == "actualamtlastyear")
                                                {
                                                    cellTemplate.ColumnType = "actualamtlastyear";
                                                }
                                                else if (col.ToLower() == "adjustedbudget")
                                                {
                                                    cellTemplate.ColumnType = "adjustedbudget";
                                                }
                                                else if (col.ToLower() == "budgetacctdeviation")
                                                {
                                                    cellTemplate.ColumnType = "budgetacctdeviation";
                                                }
                                                else if (col.ToLower() == "changetotalamount")
                                                {
                                                    cellTemplate.ColumnType = "changetotalamount";
                                                }
                                                else if (col.ToLower() == "consumepercentage")
                                                {
                                                    cellTemplate.ColumnType = "consumepercentage";
                                                }
                                                else if (col.ToLower() == "deviation")
                                                {
                                                    cellTemplate.ColumnType = "deviation";
                                                }
                                                else if (col.ToLower() == "EstimatedQuarter".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "EstimatedQuarter";
                                                }
                                                else if (col.ToLower() == "FinishedYear".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "FinishedYear";
                                                }
                                                else if (col.ToLower() == "forecastamount".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "forecastamount";
                                                }
                                                else if (col.ToLower() == "forecastdeviation".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "forecastdeviation";
                                                }
                                                else if (col.ToLower() == "investment".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "investment";
                                                }
                                                else if (col.ToLower() == "plannedamount".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "plannedamount";
                                                }
                                                else if (col.ToLower() == "regulation".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "regulation";
                                                }
                                                else if (col.ToLower() == "risk".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "risk";
                                                }
                                                else if (col.ToLower() == "status".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "status";
                                                }
                                                else if (col.ToLower() == "statusreview".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "statusreview";
                                                }
                                                else if (col.ToLower() == "totalamount".ToLower())
                                                {
                                                    cellTemplate.ColumnType = "totalamount";
                                                }

                                                string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                                                //apply number formatting of for number column
                                                if (name.ToLower() != "investment" && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter" && name.ToLower() != "approvalreference")
                                                {
                                                    if (name.ToLower() != "consumepercentage")
                                                    {
                                                        decimal ammount = 0;

                                                        bool success = decimal.TryParse(d[name].ToString(), out ammount);
                                                        if (name.ToLower() == "costapproval")
                                                        {
                                                            if (ammount == 0 || !string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                                            {
                                                                lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_approvalCostText"].LangText);
                                                            }
                                                            else
                                                            {
                                                                if (!divideByMillions)
                                                                {
                                                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                                }
                                                                else
                                                                {
                                                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (!divideByMillions)
                                                            {
                                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                            }
                                                            else
                                                            {
                                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        lstColumnDataDetails.Add((!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] != -1) ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                                                    }
                                                }
                                                else
                                                {
                                                    if (name.ToLower() == "statusreview")
                                                    {
                                                        lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                                                    }
                                                    else if (name.ToLower() == "status")
                                                    {
                                                        var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                                                        lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                                                    }
                                                    else if (name.ToLower() == "risk")
                                                    {
                                                        var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                                                        lstColumnDataDetails.Add(risk != null ? (string)risk.Value : "");
                                                        if (risk != null)
                                                        {
                                                            switch (risk.Key)
                                                            {
                                                                case "1":
                                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel1Color;
                                                                    break;

                                                                case "2":
                                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel2Color;
                                                                    break;

                                                                case "3":
                                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel3Color;
                                                                    break;
                                                            }
                                                        }
                                                    }
                                                    else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == 0) || (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1))) || ((name.ToLower() == "finishedyear") && (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && (int)d["PkId"] == -1)))
                                                    {
                                                        lstColumnDataDetails.Add(string.Empty);
                                                    }
                                                    else if (name.ToLower() == "finishedyear")
                                                    {
                                                        if (((string)d["invStatusDescription"] != ""))
                                                        {
                                                            lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                                        }
                                                        else
                                                        {
                                                            lstColumnDataDetails.Add(((string)d[name]));
                                                        }
                                                    }
                                                    else if (name.ToLower() == "estimatedquarter" && ((int)d[name] != 0))
                                                    {
                                                        if (((string)d["invStatusDescription"] != ""))
                                                        {
                                                            lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                                        }
                                                        else
                                                        {
                                                            var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                                            lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : "");
                                                        }
                                                    }
                                                    else if (name.ToLower() == "approvalreference")
                                                    {
                                                        if (!string.IsNullOrEmpty((string)d["ApprovalReferenceUrl"]))
                                                        {
                                                            cellTemplate.ContentType = ContentType.Url;
                                                            lstColumnDataDetails.Add((string)d["ApprovalReferenceUrl"] + "_endurl_" + (string)d[name]);
                                                        }
                                                        else
                                                        {
                                                            lstColumnDataDetails.Add((string)d[name]);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (publishHelper.GetType() == typeof(WebHelperMr))
                                                        {
                                                            if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])))
                                                            {
                                                                string rowtemplateWithData = string.Empty;
                                                                rowtemplateWithData = $"<a href='javascript:void(0)' title = \"(Sprettoppvindu) {(string)d[name]}\" onkeypress = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString((d["PkId"]) + sa)}\")' onClick = 'openMonthRepInvPopup(\"{publishHelper.GetCurrentChapterPath().Substring(1)}invdetails/{Convert.ToString((d["PkId"]) + sa)}\")'>{(string)d[name]} <span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                                                                lstColumnDataDetails.Add(rowtemplateWithData);

                                                                invId = Convert.ToInt32(d["PkId"]);
                                                                invName = ((string)d[name]);
                                                                invDetailPath = publishHelper.GetCurrentChapterPath() + "invdetails/";
                                                            }
                                                        }
                                                        else
                                                        {
                                                            lstColumnDataDetails.Add(((string)d[name]));
                                                        }
                                                    }
                                                }
                                                lstColumnDetails.Add(cellTemplate);
                                            }
                                            if (publishHelper.GetType() == typeof(WebHelperMr))
                                            {
                                                lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                                            }
                                            if (!string.IsNullOrEmpty(Convert.ToString(d["PkId"])) && int.TryParse(Convert.ToString(d["PkId"]), out outParam) && ((int)d["PkId"]) == -1)
                                            {
                                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                                            }
                                            else
                                            {
                                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                                            }
                                            if (publishHelper.GetType() == typeof(WebHelperMr))
                                            {
                                                lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                                            }
                                            if (publishHelper.GetType() == typeof(WebHelperMr))
                                            {
                                                lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.DefaultTextColor);
                                                lstColumnDetails.ForEach(p => p.Fontsize = 9);
                                                lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                                            }

                                            if (publishHelper.GetType() == typeof(WebHelperMr))
                                            {
                                                int dcount = 0;
                                                var drow = publishHelper.GetCurrentRowData();
                                                drow.ForEach(x =>
                                                {
                                                    if (dcount == 0)
                                                    {
                                                        invData.Add(Regex.Replace(x, "<.*?>", String.Empty));
                                                    }
                                                    else
                                                    {
                                                        if (!string.IsNullOrEmpty(x) && x.Contains("_endurl_"))
                                                        {
                                                            x = "<a href=" + (string)d["ApprovalReferenceUrl"] + " target = _blank>" + (string)d["ApprovalReference"] + "</a>";
                                                            invData.Add(x);
                                                        }
                                                        else
                                                        {
                                                            invData.Add(x);
                                                        }
                                                    }

                                                    dcount++;
                                                });
                                            }

                                            //web popup
                                            if (publishHelper.GetType() == typeof(WebHelperMr) && invData.Count > 0)
                                            {
                                                dynamic invObject = new JObject();
                                                invData[0] = invName;
                                                invObject.Add("Data", invData);
                                                invObject.Add("Labels", labels);
                                                invObject.WebUrlLink = string.Empty;
                                                invObject.popUpTitle = invName;

                                                string description = string.Empty;
                                                invObject.popUpDescription1 = string.IsNullOrEmpty(description) ? "" : description;

                                                string description2 = string.Empty;
                                                if (invId != 0)
                                                {
                                                    List<MonthlyReportInvestmentHelper> descriptiondata = resultSet;
                                                    string desc2 = descriptiondata.ToList().FirstOrDefault(x => x.PkId == invId).Statusreview;
                                                    description2 = desc2;
                                                }
                                                invObject.popUpDescription2 = description2;

                                                invObject.popUpHeaderDesc1 = string.IsNullOrEmpty(description) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc1_heading"].LangText;
                                                invObject.popUpHeaderDesc2 = string.IsNullOrEmpty(description2) ? string.Empty : langStringValuesMonthlyReportInv["MRI_investmentGrid_popUp_desc2_heading"].LangText;

                                                string showFpDescription = _utility.GetParameterValue(user, "MR_PUBLISHDisplay_FinPlan_description_In_Popup");
                                                if (!string.IsNullOrEmpty(showFpDescription) && showFpDescription.ToLower() == "true")
                                                {
                                                    invObject.showPopupDescription = true;
                                                }
                                                else
                                                {
                                                    invObject.showPopupDescription = false;
                                                }

                                                publishHelper.InsertDataIntoBlob(JsonConvert.SerializeObject(invObject), invDetailPath, (invId + sa).ToString(), PubContentHandler.BlobType.Json);
                                            }
                                        }
                                        publishHelper.EndTable();
                                        publishHelper.EndTableOrientation();
                                        builder.InsertBreak(BreakType.SectionBreakContinuous);
                                    }
                                    else
                                    {
                                        DescToBlobHelper input = new DescToBlobHelper();
                                        input.ModuleId = "MONTHREP";
                                        input.BudgetYear = budgetYear;
                                        input.FieldId = "MRIDESC";

                                        if (string.IsNullOrEmpty(otherOrgId))
                                        {
                                            input.OrgLevel2 = sa;
                                        }
                                        else
                                        {
                                            input.OrgLevel2 = otherOrgId;
                                            input.OrgLevel3 = sa;
                                        }

                                        dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);

                                        string finPlanDeviation = result.description.ToString();

                                        input.FieldId = "MRIBUDSTAT";
                                        result = _utility.GetDescriptionText(input, user, forecastPeriod);
                                        string budgetStatus = result.description.ToString();
                                        if (!string.IsNullOrEmpty((finPlanDeviation)) || !string.IsNullOrEmpty((budgetStatus)))
                                        {
                                            publishHelper.InsertHeading(user, SAIds.FirstOrDefault(x => x.ServiceAreaID == sa).ServiceAreaName, _context);
                                        }
                                        if (!string.IsNullOrEmpty((finPlanDeviation)))
                                        {
                                            publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                                        }
                                        if (!string.IsNullOrEmpty((budgetStatus)))
                                        {
                                            publishHelper.InsertDescriptionCk5(budgetStatus, user);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    insertDataPortfoliowise(user, builder, budgetYear, forecastPeriod, orgLevel, otherOrgId, publishHelper, userDetails, tenantDbContext, langStringValuesMonthlyReport, divideByMillions, isYearlySetupEnabled, numberTypeAmount, numberTypePercentage, numberTypeDecimalAmount, isDescNode);
                }
            }
            catch (Exception ex)
            {
                publishHelper.InsertError("Failed to insert city level investment data" + ex);
            }
        }



        private void InsertInvestmentDataAtCityLevelNew(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, int orgLevel, string otherOrgId, bool isDescNode)
        {
            IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
            try
            {
                var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
                Initialize(user);
                UserData userDetails = _utility.GetUserDetails(user);
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
                bool isHeadingInserted = false;
                var monthlyReportParameters = (from monthlyReportParams in tenantDbContext.vw_tco_parameters
                                               where monthlyReportParams.fk_tenant_id == userDetails.tenant_id && (monthlyReportParams.param_name == "MONTHREP_LEVEL_1" || monthlyReportParams.param_name == "MONTHREP_LEVEL_2") && monthlyReportParams.active == 1
                                               select monthlyReportParams).OrderBy(x => x.param_value).ToList();

                var portfolioParam = tenantDbContext.vw_tco_parameters.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == "INV_USE_PORTFOLIO").FirstOrDefault();
                if (portfolioParam == null || portfolioParam.param_value.ToLower() == "false")
                {
                    if (monthlyReportParameters.Count == 1 && monthlyReportParameters[0].param_value.Contains("service_id") && isDescNode)
                    {
                        var configuredServiceID = monthlyReportParameters[0].param_value;
                        int serviceIDLevel = 0;
                        serviceIDLevel = configuredServiceID == "service_id_1" ? 1 : configuredServiceID == "service_id_2" ? 2 : configuredServiceID == "service_id_3" ? 3 :
                                         configuredServiceID == "service_id_4" ? 4 : configuredServiceID == "service_id_5" ? 5 : 0;
                        var dataforPublishingNode = _utility.GetDataForPublishingNodes(orgVersionContent, user, budgetYear, -1, serviceIDLevel);
                        InsertAggregatedInvestmentDataDescforServiceNew(user, builder, budgetYear, forecastPeriod, dataforPublishingNode.ToList(), -1, serviceIDLevel, isDescNode);
                    }
                    else
                    {
                        tco_org_hierarchy toh = tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefault();
                        string tenantCityOrgId = toh != null ? toh.org_id_1 : string.Empty;
                        string tenantCityOrgLevel = "1";
                        bool isFeatureEnabled = _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text).GetAwaiter().GetResult();
                        if (isFeatureEnabled)
                        {
                            tco_monthrep_descriptions tmt = tenantDbContext.tco_monthrep_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                            && x.budget_year == budgetYear
                                                                                            && x.forecast_period == forecastPeriod
                                                                                            && x.org_level == tenantCityOrgLevel
                                                                                            && x.org_id == tenantCityOrgId
                                                                                            && x.acc_group_value == "NA"
                                                                                            && x.description_type == nameof(MonthlyReportTextBoxType.MRInvestmentsStatusText)).FirstOrDefault();
                            if (tmt != null && !string.IsNullOrEmpty(tmt.description_text))
                            {
                                publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                                if (!string.IsNullOrEmpty(tmt.description_text))
                                    publishHelper.InsertDescriptionCk5(tmt.abstract_text, user);
                                publishHelper.InsertDescriptionCk5(tmt.description_text, user);
                            }
                        }
                        else
                        {
                            tco_monthrep_texts tmt = tenantDbContext.tco_monthrep_texts.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                            && x.budget_year == budgetYear
                                                                                            && x.forecast_period == forecastPeriod
                                                                                            && x.org_level == tenantCityOrgLevel
                                                                                            && x.org_id == tenantCityOrgId
                                                                                            && x.acc_group_value == "NA").FirstOrDefault();
                            if (tmt != null && !string.IsNullOrEmpty(tmt.mrinvestments_status_text))
                            {
                                publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                                if (!string.IsNullOrEmpty(tmt.mrinvestments_status_abstract))
                                    publishHelper.InsertDescriptionCk5(tmt.mrinvestments_status_abstract, user);
                                publishHelper.InsertDescriptionCk5(tmt.mrinvestments_status_text, user);
                            }
                        }
                        var serviceAreas = _utility.GetServiceAreaUnit(orgVersionContent, user);
                        var SAIds = (from d in serviceAreas
                                     select new
                                     {
                                         d.ServiceAreaName,
                                         d.ServiceAreaID
                                     }).Distinct().ToList();

                        foreach (var sa in SAIds.Select(x => x.ServiceAreaID).ToList())
                        {
                            string OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5;
                            //assgin service id value to corret  orglevel
                            switch (orgLevel.ToString())
                            {
                                case "1":
                                    OrgLevel1 = sa;
                                    OrgLevel2 = null;
                                    OrgLevel3 = null;
                                    OrgLevel4 = null;
                                    OrgLevel5 = null;
                                    break;

                                case "2":
                                    OrgLevel2 = sa;
                                    OrgLevel3 = null;
                                    OrgLevel4 = null;
                                    OrgLevel5 = null;
                                    OrgLevel1 = null;
                                    break;

                                case "3":
                                    OrgLevel3 = sa;
                                    OrgLevel4 = null;
                                    OrgLevel5 = null;
                                    OrgLevel1 = null;
                                    OrgLevel2 = null;
                                    break;

                                case "4":
                                    OrgLevel4 = sa;
                                    OrgLevel5 = null;
                                    OrgLevel1 = null;
                                    OrgLevel2 = null;
                                    OrgLevel3 = null;
                                    break;

                                case "5":
                                    OrgLevel5 = sa;
                                    OrgLevel1 = null;
                                    OrgLevel2 = null;
                                    OrgLevel3 = null;
                                    OrgLevel4 = null;
                                    break;

                                default:
                                    OrgLevel1 = null;
                                    OrgLevel2 = null;
                                    OrgLevel3 = null;
                                    OrgLevel4 = null;
                                    OrgLevel5 = null;
                                    break;
                            }

                            //get List of department from orgstructure
                            var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5);

                            var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, null);

                            //get the data from service
                            List<MonthlyReportInvestmentHelperNew> resultSet = _monthlyReportInvestment.GetInvestementDataNew(user, forecastPeriod, sa, orgLevel, null, departmentList, functionList, true, false, true);
                            TableDefinition tabledef = null;
                            var isYearlySetupEnabled = IsYearlySetup(user, forecastPeriod);
                            string showInvestmentWithZero = _utility.GetParameterValue(user, "MR_SHOW_INV_ZERO");

                            if (isYearlySetupEnabled)
                            {
                                tabledef = _docTableConfig.GetTableDef(user, "MrYearlyInvestmentNew");
                            }
                            else
                            {
                                tabledef = _docTableConfig.GetTableDef(user, "MrInvestmentNew");
                            }
                            resultSet = _monthlyReportInvestment.RemoveInvZeroRowDataBasedOnColumn(resultSet, tabledef);


                            //drop the inv having accounting and budget both equals to 0 in yearly setup
                            if (showInvestmentWithZero.ToLower() != "true")
                            {
                                resultSet.RemoveAll(x => x.Accounting == 0 && x.AdjustedBudget == 0);
                            }

                            JArray dataArray = JArray.FromObject(resultSet);
                            string showStatusDescription = _utility.GetParameterValue(user, "DOC_INV_STATUSDESCRIPTION");
                            if (!string.IsNullOrEmpty(showStatusDescription) && showStatusDescription.ToUpper() == "TRUE")
                            {
                                foreach (var item in resultSet.Where(x => !string.IsNullOrEmpty(x.Statusreview)))
                                {
                                    if (!isHeadingInserted)
                                    {
                                        publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                                        isHeadingInserted = true;
                                    }

                                    publishHelper.InsertText(item.Investment, 11, true, false);
                                    publishHelper.InsertDescriptionCk5(item.Statusreview, userDetails.user_name);
                                }
                            }
                            //Do not remove.
                            //if (dataArray.Any())
                            //{
                            //    DescToBlobHelper input = new DescToBlobHelper();
                            //    input.ModuleId = "MONTHREP";
                            //    input.BudgetYear = budgetYear;
                            //    input.FieldId = "MRIDESC";

                            //    if (string.IsNullOrEmpty(otherOrgId))
                            //    {
                            //        input.OrgLevel2 = sa;
                            //    }
                            //    else
                            //    {
                            //        input.OrgLevel2 = otherOrgId;
                            //        input.OrgLevel3 = sa;
                            //    }

                            //    dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);

                            //    string finPlanDeviation = result.description.ToString();

                            //    input.FieldId = "MRIBUDSTAT";
                            //    result = _utility.GetDescriptionText(input, user, forecastPeriod);
                            //    string budgetStatus = result.description.ToString();
                            //    if (!string.IsNullOrEmpty((finPlanDeviation)) || !string.IsNullOrEmpty((budgetStatus)))
                            //    {
                            //        publishHelper.InsertHeading(user, SAIds.FirstOrDefault(x => x.ServiceAreaID == sa).ServiceAreaName, _context);
                            //    }
                            //    if (!string.IsNullOrEmpty((finPlanDeviation)))
                            //    {
                            //        publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                            //    }
                            //    if (!string.IsNullOrEmpty((budgetStatus)))
                            //    {
                            //        publishHelper.InsertDescriptionCk5(budgetStatus, user);
                            //    }
                            //}
                        }
                    }
                }
                else
                {
                    InsertDataPortfoliowiseNew(user, builder, budgetYear, forecastPeriod, orgLevel, otherOrgId, publishHelper, userDetails, tenantDbContext);
                }
            }
            catch (Exception ex)
            {
                publishHelper.InsertError("Failed to insert city level investment data" + ex);
            }
        }



        private void InsertDataPortfoliowiseNew(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, int orgLevel, string otherOrgId, IPublishHelper publishHelper, UserData userDetails, TenantDBContext tenantDbContext)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            var OrgLevel1 = tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefault().org_id_1;

            //get List of department from orgstructure
            var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, null, null, null, null);

            var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, null, null, null, null, null);

            //get the data from service
            List<MonthlyReportInvestmentHelperNew> resultSetData = _monthlyReportInvestment.GetInvestementDataNew(user, forecastPeriod, OrgLevel1, 1, null, departmentList, functionList, true, false, true, false, true);
            var prtfolioList = (from d in resultSetData
                                where !string.IsNullOrEmpty(d.PortfolioId) // bug 107857
                                group d by new { d.PortfolioName, d.PortfolioId } into g
                                select new KeyValuePair
                                {
                                    key = g.Key.PortfolioId,
                                    value = g.Key.PortfolioName
                                }).Where(x => !string.IsNullOrEmpty(x.key)).ToList();

            bool isHeadingInserted = true;
            bool isFeatureEnabled = _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text).GetAwaiter().GetResult();
            if (isFeatureEnabled)
            {
                tco_monthrep_descriptions tmt = tenantDbContext.tco_monthrep_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                && x.budget_year == budgetYear
                                                                                && x.forecast_period == forecastPeriod
                                                                                && x.org_level == "1"
                                                                                && x.org_id == OrgLevel1
                                                                                && x.acc_group_value == "NA"
                                                                                && x.description_type == nameof(MonthlyReportTextBoxType.MRInvestmentsStatusText)).FirstOrDefault();
                if (tmt != null && !string.IsNullOrEmpty(tmt.description_text))
                {
                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                    isHeadingInserted = false;
                    publishHelper.InsertDescriptionCk5(tmt.description_text, user);
                }
            }
            else
            {
                tco_monthrep_texts tmt = tenantDbContext.tco_monthrep_texts.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                && x.budget_year == budgetYear
                                                                                                && x.forecast_period == forecastPeriod
                                                                                                && x.org_level == "1"
                                                                                                && x.org_id == OrgLevel1
                                                                                                && x.acc_group_value == "NA").FirstOrDefault();
                if (tmt != null && !string.IsNullOrEmpty(tmt.mrinvestments_status_text))
                {
                    publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                    isHeadingInserted = false;
                    publishHelper.InsertDescriptionCk5(tmt.mrinvestments_status_text, user);
                }
            }
            foreach (var port in prtfolioList)
            {
                DescToBlobHelper input = new DescToBlobHelper();
                input.ModuleId = "MONTHREP";
                input.BudgetYear = budgetYear;
                input.FieldId = port.key;
                input.OrgLevel1 = OrgLevel1;
                input.OrgLevel2 = string.Empty;
                input.OrgLevel3 = string.Empty;
                input.OrgLevel4 = string.Empty;
                input.OrgLevel5 = string.Empty;
                input.IsPortfolio = true;
                dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);
                string finPlanDeviation = result.description.ToString();
                if (!string.IsNullOrEmpty((finPlanDeviation)))
                {
                    if (isHeadingInserted)
                    {
                        publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                        isHeadingInserted = false;
                    }
                    publishHelper.InsertHeading(user, port.value, _context, false);
                    publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                }
            }
        }



        private void insertDataPortfoliowise(string user, DocumentBuilder builder, int budgetYear, int forecastPeriod, int orgLevel, string otherOrgId, IPublishHelper publishHelper, UserData userDetails, TenantDBContext tenantDbContext, Dictionary<string, clsLanguageString> langStringValuesMonthlyReport, bool divideByMillions, bool isYearlySetupEnabled, string numberTypeAmount, string numberTypePercentage, string numberTypeDecimalAmount, bool isDescNode)
        {
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(user, forecastPeriod);
            var prtfolioList = tenantDbContext.tco_inv_portfolio.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1).Select(x => new KeyValuePair { key = x.pk_portfolio_code, value = x.portfolio_name }).ToList();

            var OrgLevel1 = tenantDbContext.tco_org_hierarchy.Where(x => x.fk_tenant_id == userDetails.tenant_id).FirstOrDefault().org_id_1;

            //get List of department from orgstructure
            var departmentList = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, user, OrgLevel1, null, null, null, null);

            var functionList = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, user, OrgLevel1, null, null, null, null, null);

            //get the data from service
            List<MonthlyReportInvestmentHelper> resultSet = _monthlyReportInvestment.GetInvestementData(user, forecastPeriod, OrgLevel1, 1, null, departmentList, functionList, true, false, true);

            //get risk list
            var risklist = _monthlyReportInvestment.GetRiskList(user);

            //get risk list
            var statuslist = _monthlyReportInvestment.GetMonthlyReportInvestmentStatusList(user);

            //quater data
            var quarterData = _utility.GetEstimatedFinishQuarter(budgetYear, 1, user);

            //drop the inv having accounting and budget both equals to 0 in yearly setup
            if (isYearlySetupEnabled)
            {
                resultSet.RemoveAll(x => x.Accounting == 0 && x.AdjustedBudget == 0);
            }

            if (isDescNode)
            {
                bool isHeadingInserted = true;
                bool isFeatureEnabled = _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text).GetAwaiter().GetResult();
                if (isFeatureEnabled)
                {
                    tco_monthrep_descriptions tmt = tenantDbContext.tco_monthrep_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                    && x.budget_year == budgetYear
                                                                                    && x.forecast_period == forecastPeriod
                                                                                    && x.org_level == "1"
                                                                                    && x.org_id == OrgLevel1
                                                                                    && x.acc_group_value == "NA"
                                                                                    && x.description_type == nameof(MonthlyReportTextBoxType.MRInvestmentsStatusText)).FirstOrDefault();
                    if (tmt != null && !string.IsNullOrEmpty(tmt.description_text))
                    {
                        if (isHeadingInserted)
                        {
                            publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                            isHeadingInserted = false;
                        }
                        publishHelper.InsertDescriptionCk5(tmt.description_text, user);
                    }
                } 
                else
                {
                    tco_monthrep_texts tmt = tenantDbContext.tco_monthrep_texts.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                    && x.budget_year == budgetYear
                                                                                    && x.forecast_period == forecastPeriod
                                                                                    && x.org_level == "1"
                                                                                    && x.org_id == OrgLevel1
                                                                                    && x.acc_group_value == "NA").FirstOrDefault();
                    if (tmt != null && !string.IsNullOrEmpty(tmt.mrinvestments_status_text))
                    {
                        if (isHeadingInserted)
                        {
                            publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                            isHeadingInserted = false;
                        }
                        publishHelper.InsertDescriptionCk5(tmt.mrinvestments_status_text, user);
                    }
                }
                foreach (var port in prtfolioList)
                {
                    DescToBlobHelper input = new DescToBlobHelper();
                    input.ModuleId = "MONTHREP";
                    input.BudgetYear = budgetYear;
                    input.FieldId = port.key;
                    input.OrgLevel1 = OrgLevel1;
                    input.OrgLevel2 = string.Empty;
                    input.OrgLevel3 = string.Empty;
                    input.OrgLevel4 = string.Empty;
                    input.OrgLevel5 = string.Empty;
                    input.IsPortfolio = true;
                    dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);
                    string finPlanDeviation = result.description.ToString(); ;
                    if (!string.IsNullOrEmpty((finPlanDeviation)))
                    {
                        if (isHeadingInserted)
                        {
                            publishHelper.InsertHeading(user, _languageStringsExportCommon["doc_monthly_reporting_investdesc_desc"].LangText, _context, true);
                            isHeadingInserted = false;
                        }
                        publishHelper.InsertHeading(user, port.value, _context, false);
                        publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                    }
                }
            }
            else
            {
                foreach (var port in prtfolioList)
                {
                    //convert it to JArray
                    var dataArray = JArray.FromObject(resultSet.Where(x => x.PortfolioId == port.key).ToList());

                    if (dataArray.Any())
                    {
                        publishHelper.InsertHeading(user, port.value, _context);

                        publishHelper.StartTable("InvestmentGrid", port.value, null, divideByMillions ? AmountFormats.Millions : AmountFormats.Thousands);

                        List<string> defaultColumn = new List<string>();

                        //get the columns from TCO parametre
                        string userDefinedCol = string.Empty;
                        if (isYearlySetupEnabled)
                        {
                            userDefinedCol = _utility.GetParameterValue(user, "INVESTMENT_COL_SELECTION_MONTHLY_REPORT_YEARLYSETUP");
                        }
                        else
                        {
                            userDefinedCol = _utility.GetParameterValue(user, "INVESTMENT_COL_SELECTION_MONTHLY_REPORT");
                        }
                        if (!string.IsNullOrEmpty(userDefinedCol))
                        {
                            defaultColumn = userDefinedCol.Split(',').ToList();
                        }
                        else
                        {
                            defaultColumn = isYearlySetupEnabled ?
                                                 new List<string> { "investment", "totalamount", "accountingtotal", "changetotalamount", "deviation", "adjustedbudget", "accounting", "budgetacctdeviation", "FinishedYear", "EstimatedQuarter", "status", "risk", "actualamtlastyear" }
                                                : new List<string> { "investment", "totalamount", "accountingtotal", "changetotalamount", "deviation", "plannedamount", "regulation", "forecastamount", "forecastdeviation", "FinishedYear", "EstimatedQuarter", "status", "risk", "statusreview" };
                        }

                        List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
                        List<string> lstColumnDataDetails = new List<string>();

                        int count = 0;

                        int set1HeaderIndex = defaultColumn.Count(x => x.ToLower() == "totalamount" ||
                            x.ToLower() == "changetotalamount" || x.ToLower() == "deviation" || x.ToLower() == "totalorgbudget" || x.ToLower() == "costapproval");
                        int set2HeaderIndex = defaultColumn.Count(x => x.ToLower() == "status" ||
                            x.ToLower() == "plannedamount" || x.ToLower() == "regulation" || x.ToLower() == "forecastamount" ||
                            x.ToLower() == "forecastdeviation" || x.ToLower() == "adjustedbudget" ||
                            x.ToLower() == "proposedadjustment" || x.ToLower() == "accounting".ToLower() || x.ToLower() == "budgetacctdeviation".ToLower());

                        //If no matches were found, then there is only one column. No merge required.
                        if (set1HeaderIndex == 0)
                        {
                            set1HeaderIndex = 1;
                        }

                        if (set2HeaderIndex == 0)
                        {
                            set2HeaderIndex = 1;
                        }

                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();
                        var cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Alignment = ParagraphAlignment.Right;
                        cellTemplate.WrapText = true;
                        cellTemplate.Column = 1;
                        cellTemplate.Fontsize = 9;
                        cellTemplate.PreferredWidth = 26.8;
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Alignment = ParagraphAlignment.Center;
                        cellTemplate.WrapText = true;
                        cellTemplate.Column = set1HeaderIndex;
                        cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                        cellTemplate.Fontsize = 9;
                        cellTemplate.PreferredWidth = 35.3;
                        lstColumnDetails.Add(cellTemplate);

                        cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                        cellTemplate.Alignment = ParagraphAlignment.Center;
                        cellTemplate.WrapText = true;
                        cellTemplate.Column = set2HeaderIndex;
                        cellTemplate.Fontsize = 9;
                        cellTemplate.PreferredWidth = 37.9;
                        lstColumnDetails.Add(cellTemplate);

                        lstColumnDataDetails.Add(string.Empty);
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_total_budget"].LangText);
                        lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_yearly_budget"].LangText);

                        int blankCells = defaultColumn.Count - (set1HeaderIndex + set2HeaderIndex + 1);
                        for (int i = 0; i < blankCells; i++)
                        {
                            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                            cellTemplate.Alignment = ParagraphAlignment.Center;
                            cellTemplate.WrapText = true;

                            lstColumnDetails.Add(cellTemplate);

                            lstColumnDataDetails.Add(string.Empty);
                        }

                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.MonthlyReportSubHeaderColor);
                            lstColumnDetails.ForEach(p => p.Fontsize = 17);
                            lstColumnDetails.ForEach(p => p.IsSemiBold = true);
                        }

                        publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading1);
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                        }

                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails.ForEach(x => x.FontColor = FramsiktColors.DefaultTextColor);
                            lstColumnDetails.ForEach(p => p.Fontsize = 9);
                            lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                        }

                        count = 0;
                        //int i = 0;
                        // create the header row
                        lstColumnDetails = new List<ColumnDetails>();
                        lstColumnDataDetails = new List<string>();
                        builder.CellFormat.ClearFormatting();
                        builder.RowFormat.HeadingFormat = true;
                        foreach (var c in defaultColumn)
                        {
                            count++;
                            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
                            //  if (i < 3)
                            cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                            //  else
                            //     cellTemplate.Alignment = ParagraphAlignment.Right;
                            cellTemplate.WrapText = true;
                            cellTemplate.BottomBorder = 1;
                            cellTemplate.Fontsize = 9;

                            if (c.ToLower() == "investment".ToLower())
                            {
                                cellTemplate.PreferredWidth = 26.8;
                            }
                            if (c.ToLower() == "totalamount".ToLower() || c.ToLower() == "totalorgbudget" || c.ToLower() == "costapproval")
                            {
                                cellTemplate.PreferredWidth = 7.6;
                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                            }
                            if (c.ToLower() == "changetotalamount".ToLower() || c.ToLower() == "deviation".ToLower())
                            {
                                cellTemplate.PreferredWidth = 8.6;
                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                            }
                            if (c.ToLower() == "EstimatedQuarter".ToLower())
                            {
                                cellTemplate.PreferredWidth = 10.5;
                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                            }
                            if (c.ToLower() == "status".ToLower())
                            {
                                cellTemplate.PreferredWidth = 9;
                            }
                            if (c.ToLower() == "adjustedbudget".ToLower())
                            {
                                cellTemplate.PreferredWidth = 7.6;
                            }
                            if (c.ToLower() == "forecastamount".ToLower() || c.ToLower() == "accounting".ToLower())
                            {
                                cellTemplate.PreferredWidth = 7;
                            }
                            if (c.ToLower() == "forecastdeviation".ToLower() || c.ToLower() == "budgetacctdeviation".ToLower())
                            {
                                cellTemplate.PreferredWidth = 7.1;
                            }
                            if (c.ToLower() == "proposedadjustment".ToLower())
                            {
                                cellTemplate.PreferredWidth = 7.2;
                            }
                            if (c.ToLower() == "finishedyear")
                            {
                                cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                //finishedYearColPos = count;
                            }
                            cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                            lstColumnDetails.Add(cellTemplate);
                            lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_" + c].LangText);
                        }

                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                        }
                        publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Heading2);
                        if (publishHelper.GetType() == typeof(WebHelperMr))
                        {
                            lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                        }
                        builder.RowFormat.HeadingFormat = false;

                        // Get the PropertyInfo object by passing the property name.
                        PropertyInfo[] myPropInfo = typeof(MonthlyReportInvestmentHelper).GetProperties();
                        // insert data for the document
                        foreach (var d in dataArray)
                        {
                            lstColumnDetails = new List<ColumnDetails>();
                            lstColumnDataDetails = new List<string>();

                            //for each column to display insert record
                            count = 0;
                            foreach (var col in defaultColumn)
                            {
                                count++;
                                cellTemplate = publishHelper.GetTemplate(CellTypes.DetailDescription);
                                cellTemplate.Alignment = count == 1 ? ParagraphAlignment.Left : ParagraphAlignment.Right;
                                cellTemplate.WrapText = true;
                                cellTemplate.IsBold = false;
                                if ((int)d["PkId"] == -1)
                                {
                                    if (publishHelper.GetType() == typeof(WebHelperMr))
                                    {
                                        if (count == 1)
                                        {
                                            cellTemplate.FontColor = FramsiktColors.MonthlyReportSubTotalColor;
                                            cellTemplate.Fontsize = 17;
                                            cellTemplate.IsSemiBold = true;
                                        }
                                        cellTemplate.IsTotalRow = true;
                                    }
                                    else
                                    {
                                        cellTemplate.FontColor = FramsiktColors.TotalColor;
                                    }
                                }

                                if (col.ToLower() == "totalamount" || col.ToLower() == "changetotalamount" || col.ToLower() == "deviation" || col.ToLower() == "totalorgbudget" || col.ToLower() == "costapproval")
                                {
                                    cellTemplate.FillColor = Color.FromArgb(242, 242, 242);
                                }

                                cellTemplate.VerticalAlignment = CellVerticalAlignment.Top;
                                cellTemplate.TopBorder = 0;
                                cellTemplate.BottomBorder = 1;

                                lstColumnDetails.Add(cellTemplate);

                                string name = myPropInfo.FirstOrDefault(x => x.Name.ToLower() == col.ToLower()).Name;
                                //apply number formatting of for number column
                                if (name.ToLower() != "investment" && name.ToLower() != "statusreview" && name.ToLower() != "status" && name.ToLower() != "risk" && name.ToLower() != "finishedyear" && name.ToLower() != "estimatedquarter")
                                {
                                    if (name.ToLower() != "consumepercentage")
                                    {
                                        decimal ammount = 0;

                                        bool success = decimal.TryParse(d[name].ToString(), out ammount);
                                        if (name.ToLower() == "costapproval")
                                        {
                                            if (ammount == 0 || !string.IsNullOrEmpty(((string)d["invStatusDescription"])))
                                            {
                                                lstColumnDataDetails.Add(langStringValuesMonthlyReport["MR_Doc_investment_col_title_approvalCostText"].LangText);
                                            }
                                            else
                                            {
                                                if (!divideByMillions)
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                                }
                                                else
                                                {
                                                    lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (!divideByMillions)
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000).ToString(numberTypeAmount));
                                            }
                                            else
                                            {
                                                lstColumnDataDetails.Add((ammount / 1000000).ToString(numberTypeDecimalAmount));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add((int)d["PkId"] != -1 ? ((decimal)d[name]).ToString(numberTypePercentage) : "");
                                    }
                                }
                                else
                                {
                                    if (name.ToLower() == "statusreview")
                                    {
                                        lstColumnDataDetails.Add((System.Net.WebUtility.HtmlDecode(Regex.Replace(((string)d[name]), "<(.|\n)*?>", ""))));
                                    }
                                    else if (name.ToLower() == "status")
                                    {
                                        var status = statuslist.FirstOrDefault(x => x.Key == (int)d[name]);
                                        lstColumnDataDetails.Add(status != null ? (string)status.Value : "");
                                    }
                                    else if (name.ToLower() == "risk")
                                    {
                                        var risk = risklist.FirstOrDefault(x => x.Key == (string)d[name]);
                                        lstColumnDataDetails.Add(risk != null ? (string)risk.Value : "");
                                        if (risk != null)
                                        {
                                            switch (risk.Key)
                                            {
                                                case "1":
                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel1Color;
                                                    break;

                                                case "2":
                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel2Color;
                                                    break;

                                                case "3":
                                                    lstColumnDetails.ElementAt(lstColumnDetails.Count - 1).FontColor = FramsiktColors.RiskLevel3Color;
                                                    break;
                                            }
                                        }
                                    }
                                    else if (((name.ToLower() == "estimatedquarter") && (((int)d[name] == 0) || (int)d["PkId"] == -1)) || ((name.ToLower() == "finishedyear") && (int)d["PkId"] == -1))
                                    {
                                        lstColumnDataDetails.Add(string.Empty);
                                    }
                                    else if (name.ToLower() == "finishedyear")
                                    {
                                        if (((string)d["invStatusDescription"] != ""))
                                        {
                                            lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                        }
                                        else
                                        {
                                            lstColumnDataDetails.Add(((string)d[name]));
                                        }
                                    }
                                    else if (name.ToLower() == "estimatedquarter" && ((int)d[name] != 0))
                                    {
                                        if (((string)d["invStatusDescription"] != ""))
                                        {
                                            lstColumnDataDetails.Add((string)d["invStatusDescription"]);
                                        }
                                        else
                                        {
                                            var quaterValue = quarterData.FirstOrDefault(x => x.KeyId == (int)d[name]);
                                            lstColumnDataDetails.Add(quaterValue != null ? (string)quaterValue.ValueString : "");
                                        }
                                    }
                                    else
                                    {
                                        lstColumnDataDetails.Add(((string)d[name]));
                                    }
                                }
                            }
                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails[0].FontColor = FramsiktColors.MonthlyReportDetailColor;
                            }
                            if ((int)d["PkId"] == -1)
                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Total1);
                            else
                                publishHelper.CreateRow2(lstColumnDetails, lstColumnDataDetails, RowType2.Content1);
                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails.ForEach(x => x.IsTotalRow = false);
                            }
                            if (publishHelper.GetType() == typeof(WebHelperMr))
                            {
                                lstColumnDetails.ForEach(p => p.FontColor = FramsiktColors.DefaultTextColor);
                                lstColumnDetails.ForEach(p => p.Fontsize = 9);
                                lstColumnDetails.ForEach(p => p.IsSemiBold = false);
                            }
                        }

                        publishHelper.EndTable();

                        DescToBlobHelper input = new DescToBlobHelper();
                        input.ModuleId = "MONTHREP";
                        input.BudgetYear = budgetYear;
                        input.FieldId = port.key;
                        input.OrgLevel1 = OrgLevel1;
                        input.OrgLevel2 = string.Empty;
                        input.OrgLevel3 = string.Empty;
                        input.OrgLevel4 = string.Empty;
                        input.OrgLevel5 = string.Empty;
                        input.IsPortfolio = true;
                        dynamic result = _utility.GetDescriptionText(input, user, forecastPeriod);

                        string finPlanDeviation = result.description.ToString(); ;
                        if (!string.IsNullOrEmpty((finPlanDeviation)))
                        {
                            publishHelper.InsertDescriptionCk5(finPlanDeviation, user);
                        }
                    }
                }
            }
        }

    }
}