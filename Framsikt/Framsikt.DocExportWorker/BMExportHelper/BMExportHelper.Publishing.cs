using Aspose.Words;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker;

public partial class BmExportHelper
{
    //#region 19706 Story

    public void ProcessPublish(PublishProcessInputHelper publishProcessInputData)
    {
        IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(publishProcessInputData.userId,
            publishProcessInputData.builder, _docStyle, _context, _container);
        //get budgetyear for node description
        int budgetYear = publishProcessInputData.oppBudgetYear == 0
            ? _utility.GetActiveBudgetYear(publishProcessInputData.userId,
                nameof(clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR))
            : publishProcessInputData.oppBudgetYear;
        Initialize(publishProcessInputData.userId);

        List<string> treePath = new List<string>();
        //stroy #57636
        if (publishHelper.GetType() == typeof(WebHelperBm))
        {
            TcoPublishTemplate publishTemplateData =
                _budgetManagement.GetPublishTemplateData(publishProcessInputData.userId,
                    publishProcessInputData.publishTemplateID);

            if (publishProcessInputData.treeNodes.Any(x => x.type == "BudgetByServiceAreaLevel2"))
            {
                foreach (var serviceArea in publishProcessInputData.treeNodes
                             .FirstOrDefault(x => x.type == "BudgetByServiceAreaLevel2").items)
                {
                    if (publishProcessInputData.treeType == "OppAsMasterTemplate" ||
                        (publishProcessInputData.treeType == "BudgetProposal" &&
                         _utility.AreAnyChildrenChecked(serviceArea))
                        || (publishTemplateData.isDataMigrated && serviceArea.@checked)
                        || (!publishTemplateData.isDataMigrated))
                    {
                        _selectedServiceArea.Add(serviceArea.id);
                    }
                }
            }
            else if (publishProcessInputData.treeNodes.Any(x => x.type == "BudgetByServiceAreaLevel1"))
            {
                foreach (var serviceArea in publishProcessInputData.treeNodes
                             .FirstOrDefault(x => x.type == "BudgetByServiceAreaLevel1").items)
                {
                    if (publishProcessInputData.treeType == "OppAsMasterTemplate" ||
                        (publishProcessInputData.treeType == "BudgetProposal" &&
                         _utility.AreAnyChildrenChecked(serviceArea))
                        || (publishTemplateData.isDataMigrated && serviceArea.@checked)
                        || (!publishTemplateData.isDataMigrated))
                    {
                        _selectedServiceArea.Add(serviceArea.id);
                    }
                }
            }
            else if (publishProcessInputData.treeNodes.Any(x => x.type == "BudgetByServiceArea"))
            {
                foreach (var serviceArea in publishProcessInputData.treeNodes
                             .FirstOrDefault(x => x.type == "BudgetByServiceArea").items)
                {
                    if (publishProcessInputData.treeType == "OppAsMasterTemplate" ||
                        (publishProcessInputData.treeType == "BudgetProposal" &&
                         _utility.AreAnyChildrenChecked(serviceArea))
                        || (publishTemplateData.isDataMigrated && serviceArea.@checked)
                        || (!publishTemplateData.isDataMigrated))
                    {
                        _selectedServiceArea.Add(serviceArea.id);
                    }
                }
            }
        }

        foreach (var exportRoot in publishProcessInputData.treeNodes)
        {
            //UnusedNodesFolder and FrontPageLinks is processed out of the loop
            if (exportRoot.type.Equals("UnusedNodesFolder", StringComparison.InvariantCultureIgnoreCase) ||
                exportRoot.type.Equals("FrontPageLinks", StringComparison.InvariantCultureIgnoreCase))
            {
                continue;
            }

            if (!_utility.AreAnyChildrenChecked(exportRoot))
            {
                continue;
            }

            try
            {
                treePath.Clear(); //Start a new path
                if (exportRoot.type.Equals("FocusAreaSectionNewIntro", StringComparison.InvariantCultureIgnoreCase))
                {
                    treePath.Add(exportRoot.type);
                }
                else if (exportRoot.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                {
                    treePath.Add(exportRoot.type);
                }
                else if (publishProcessInputData.parentPath != null)
                {
                    treePath.AddRange(publishProcessInputData.parentPath);
                }

                treePath.Add(exportRoot.id.ToLower());

                bool isChapter = false;
                if (exportRoot.@checked || _utility.AreAnyChildrenChecked(exportRoot))
                {
                    string pathPrefix = string.Empty;
                    if (treePath[0].Equals("Budsa", StringComparison.InvariantCultureIgnoreCase))
                    {
                        pathPrefix = "orgstructuremain";
                    }
                    else
                    {
                        pathPrefix = "summary";
                    }

                    string chapterId = string.Empty;
                    bool isCustomNode = false;
                    if (treePath[0].Equals("budsa", StringComparison.InvariantCultureIgnoreCase))
                    {
                        //Use the isChapter flag to identify chapters
                        //In case of custom nodes the isChapter will not be set. Check if the custom node is within the context of a chapter
                        //if not create one for it
                        if (exportRoot.isChapter)
                        {
                            isChapter = true;
                            chapterId = exportRoot.id;
                        }
                        else if (exportRoot.type.Equals("customNode",
                                     StringComparison.InvariantCultureIgnoreCase) &&
                                 string.IsNullOrEmpty(publishHelper.GetCurrentChapter()))
                        {
                            isChapter = true;
                            isCustomNode = true;
                            chapterId = $"{exportRoot.id}-cn";
                        }
                    }
                    else if (publishHelper.NodeLevel == 0)
                    {
                        if (exportRoot.type.Equals("FocusAreaSectionNew",
                                StringComparison.InvariantCultureIgnoreCase))
                        {
                            /*This should not be a chapter*/
                        }
                        else if (exportRoot.type.Equals("IntroGoalTarget",
                                     StringComparison.InvariantCultureIgnoreCase))
                        {
                            if (exportRoot.type.Equals("IntroGoalTarget",
                                    StringComparison.InvariantCultureIgnoreCase)
                                && (exportRoot.items.Where(e =>
                                            e.type.Contains("CityStrategyAndInvestmentOnFinancialPlan") &&
                                            e.@checked)
                                        .Any()
                                    || exportRoot.items.Where(e =>
                                        e.type.Contains("VisionAmbitionCityLvl") && e.@checked).Any()
                                    || exportRoot.items
                                        .Where(e => e.type.Contains("GoalsCityLvlNoFa") && e.@checked).Any()
                                    || exportRoot.items.Where(e =>
                                        e.type.Contains("StratergyTextCityLevel") && e.@checked).Any()
                                    || exportRoot.items.Where(e =>
                                        e.type.Contains("TargetsCityLvlNoFa") && e.@checked).Any()
                                    || exportRoot.items.Where(e =>
                                        e.type.Contains("AssignmentsCityLvlNoFa") && e.@checked).Any()
                                    || exportRoot.items.Where(e => e.type.Contains("customNode") && e.@checked)
                                        .Any()))
                            {
                                //IntroGoalTarget can be a chapter only in case of above nodes are checked,
                                //focus areas are excluded here as it ia a different chapter
                                isChapter = true;
                                chapterId = exportRoot.id;

                                if (exportRoot.type.Equals("customNode",
                                        StringComparison.InvariantCultureIgnoreCase))
                                {
                                    isCustomNode = true;
                                    chapterId = $"{exportRoot.id}-cn";
                                }
                            }
                        }
                        else
                        {
                            //All nodes at level 0 are chapters
                            if (exportRoot.type.Equals("customNode", StringComparison.InvariantCultureIgnoreCase))
                            {
                                isChapter = true;
                                isCustomNode = true;
                                chapterId = $"{exportRoot.id}-cn";
                            }
                            else
                            {
                                isChapter = true;
                                chapterId = exportRoot.id;
                            }
                        }
                    }

                    if (exportRoot.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                    {
                        //Focus Area is now a chapter (Starting from sprint 111)
                        isChapter = true;
                        chapterId = exportRoot.id;
                    }

                    if (exportRoot.type.Equals("FocusAreaSectionNewIntro",
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        isChapter = true;
                        chapterId = exportRoot.id;
                    }

                    if (isChapter)
                    {
                        if (exportRoot.type.Equals("FocusArea", StringComparison.InvariantCultureIgnoreCase))
                        {
                            treeLevel1Id = treePath[0].Equals("budsa", StringComparison.InvariantCultureIgnoreCase)
                                ? pathPrefix
                                : pathPrefix + "/" + treePath[0];
                            treeLevel2Id = chapterId;
                            treeLevel3Id = string.Empty;
                        }
                        else
                        {
                            switch (publishHelper.NodeLevel)
                            {
                                case 0:
                                    treeLevel1Id =
                                        treePath[0].Equals("budsa", StringComparison.InvariantCultureIgnoreCase)
                                            ? pathPrefix
                                            : pathPrefix + "/" + chapterId;
                                    treeLevel2Id = string.Empty;
                                    treeLevel3Id = string.Empty;
                                    break;

                                case 1:
                                    treeLevel1Id =
                                        treePath[0].Equals("budsa", StringComparison.InvariantCultureIgnoreCase)
                                            ? pathPrefix
                                            : pathPrefix + "/" + treePath[0];
                                    treeLevel2Id = chapterId;
                                    treeLevel3Id = string.Empty;
                                    break;

                                case 2:
                                    treeLevel1Id =
                                        treePath[0].Equals("budsa", StringComparison.InvariantCultureIgnoreCase)
                                            ? pathPrefix
                                            : pathPrefix + "/" + treePath[0];
                                    treeLevel2Id = treePath[1];
                                    treeLevel3Id = chapterId;
                                    break;
                            }
                        }

                        publishHelper.StartChapter(treeLevel1Id, treeLevel2Id, treeLevel3Id,
                            isCustomNode ? BookmarkStrategy.UseH1Tags : BookmarkStrategy.Custom, exportRoot.text,
                            PageController.Generic, exportRoot.Uid);
                    }
                }

                PublishProcessInputHelper processNodeInputData = new PublishProcessInputHelper()
                {
                    userId = publishProcessInputData.userId,
                    builder = publishProcessInputData.builder,
                    parent = publishProcessInputData.parent,
                    node = exportRoot,
                    orgId = publishProcessInputData.orgId,
                    templateId = publishProcessInputData.templateId,
                    incIntDesc = publishProcessInputData.incIntDesc,
                    budgetYear = budgetYear,
                    budgetPhaseId = publishProcessInputData.budgetPhaseId,
                    showOnlyModified = publishProcessInputData.showOnlyModified,
                    treePath = treePath,
                    incBlistIntDesc = publishProcessInputData.incBlistIntDesc,
                    publishTemplateID = publishProcessInputData.publishTemplateID,
                    treeType = publishProcessInputData.treeType,
                    cacheKey = publishProcessInputData.cacheKey
                };
                ProcessNode(processNodeInputData);

                if (!noRecursionChapters.Contains(exportRoot.type))
                {
                    if (exportRoot.items.Any())
                    {
                        PublishProcessInputHelper publishProcessRecursionInputData = new PublishProcessInputHelper()
                        {
                            userId = publishProcessInputData.userId,
                            templateId = publishProcessInputData.templateId,
                            incIntDesc = publishProcessInputData.incIntDesc,
                            orgId = publishProcessInputData.orgId,
                            parent = exportRoot,
                            treeNodes = exportRoot.items.ToList(),
                            builder = publishProcessInputData.builder,
                            budgetPhaseId = publishProcessInputData.budgetPhaseId,
                            showOnlyModified = publishProcessInputData.showOnlyModified,
                            incBlistIntDesc = publishProcessInputData.incBlistIntDesc,
                            parentPath = treePath,
                            oppBudgetYear = 0,
                            publishTemplateID = publishProcessInputData.publishTemplateID,
                            treeType = publishProcessInputData.treeType,
                            cacheKey = processNodeInputData.cacheKey
                        };
                        publishHelper.StartNewLevel(exportRoot);
                        exportRoot.items.Where(x => x.type.ToLower() == "customNode".ToLower() && x.@checked)
                            .ToList().ForEach(y => y.chapterLevel = 1);
                        ProcessPublish(publishProcessRecursionInputData);
                        publishHelper.EndCurrentLevel();
                    }
                }

                if (isChapter)
                {
                    publishHelper.EndCurrentChapter();
                }
            }
            catch (DbUpdateException e)
            {
                publishHelper.InsertError("Process publish failed");
                publishHelper.InsertError(e.ToString());
                LogError(e, publishHelper);

                // Exception is thrown when invalid data is tried to inserted/updated in the DB. That invalid Entity is not removed from the EF Core Tracker
                // So manually needs to be removed to avoid further exception on calling SaveChanges in DBContext

                foreach (var entry in _utility.GetTenantDBContext().ChangeTracker.Entries())
                {
                    if (entry.State is EntityState.Modified or EntityState.Added)
                    {
                        entry.State = EntityState.Detached;
                    }
                }
            }
            catch (Exception e)
            {
                publishHelper.InsertError("Process publish failed");
                publishHelper.InsertError(e.ToString());
                LogError(e, publishHelper);
            }
        }
    }


    public void PublishServiceIds(string userId, IEnumerable<PublishTreeNode> exportTree, DocumentBuilder builder)
    {
        PublishTreeNode budSerArea = exportTree.FirstOrDefault(x =>
            x.type.Equals("BudgetByServiceArea", StringComparison.InvariantCultureIgnoreCase));
        if (budSerArea != null)
        {
            TenantData tenantData = _utility.GetTenantData(userId);
            IPublishHelper publishHelper =
                PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
            foreach (var lvl2 in budSerArea.items)
            {
                if (lvl2.items.Any() && !_utility.AreAnyChildrenChecked(lvl2))
                {
                    continue;
                }

                List<PubServiceId> serIds = new List<PubServiceId>();
                foreach (var lvl3 in lvl2.items.Where(x => x.type == "ServiceId" || x.type == "ServiceUnit"))
                {
                    if (lvl3.items.Any() && !_utility.AreAnyChildrenChecked(lvl3))
                    {
                        continue;
                    }

                    PubServiceId serId = new PubServiceId();
                    serId.id = lvl3.id;
                    serId.title = lvl3.text;

                    serIds.Add(serId);
                }

                string strSerIds = JsonConvert.SerializeObject(serIds);
                string pubPath = $"{publishHelper.BaseStoragePath}/content/data/serviceids/{lvl2.id}.json";
                _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.BmContent, pubPath,
                    strSerIds);
            }
        }
    }


    private void CopyUIcode(IPublishHelper publishHelper, TenantData tenantData)
    {
        string tgtBuild = string.Empty;
        try
        {
            //Will throw an error if the target build.json file does not exists
            tgtBuild = _blobHelper.GetTextBlob(StorageAccount.PublishStage, BlobContainers.BmContent,
                $"{publishHelper.BaseStoragePath}/build.json");
        }
        catch (Exception)
        {
            //Do nothing
        }

        if (!string.IsNullOrEmpty(tgtBuild))
        {
            string srcBuild =
                _blobHelper.GetTextBlob(StorageAccount.AppStorage, BlobContainers.BuildBm, "build.json");
            var tgtBuildJson = JObject.Parse(tgtBuild);
            var srcBuildJson = JObject.Parse(srcBuild);
        }

        //Copy the UI code

        //delete the code files before copying
        var tasks = new List<Task>();

        string[] foldersToDelete = new string[]
        {
            $"{publishHelper.BaseStoragePath}/images",
            $"{publishHelper.BaseStoragePath}/partials",
            $"{publishHelper.BaseStoragePath}/scripts",
            $"{publishHelper.BaseStoragePath}/styles"
        };
        string[] blobsToDelete = new string[]
        {
            $"{publishHelper.BaseStoragePath}/index.html",
            $"{publishHelper.BaseStoragePath}/index-original.html",
            $"{publishHelper.BaseStoragePath}/build.json",
            $"{publishHelper.BaseStoragePath}/login.html",
            $"{publishHelper.BaseStoragePath}/error.html"
        };

        // Add folder delete tasks
        foreach (var folder in foldersToDelete)
        {
            tasks.Add(Task.Run(() =>
                _blobHelper.DeleteFolderAsync(StorageAccount.PublishStage, BlobContainers.BmContent, folder, true)
                    .GetAwaiter().GetResult()));
        }

        // Add blob delete tasks
        foreach (var blob in blobsToDelete)
        {
            tasks.Add(Task.Run(() =>
                _blobHelper.DeleteBlobAsync(StorageAccount.PublishStage, BlobContainers.BmContent, blob)
                    .GetAwaiter().GetResult()));
        }

        // Wait for all tasks to complete
        Task.WhenAll(tasks).GetAwaiter().GetResult();

        IEnumerable<string> errors = _blobHelper.CopyFolderContentsAsync(StorageAccount.AppStorage,
                BlobContainers.BuildBm, string.Empty,
                StorageAccount.PublishStage, BlobContainers.BmContent, publishHelper.BaseStoragePath).GetAwaiter()
            .GetResult();
        if (errors.Any())
        {
            publishHelper.InsertError(JsonConvert.SerializeObject(errors));
        }
    }


    private void CreateConfig(IPublishHelper publishHelper, string searchIndexName, PublishTreeType treeType,
        tco_publish_config publishConfig, string userId)
    {
        try
        {
            PublishURLAccessControlData urlAccessControlData =
                _budgetManagement.GetPublishURLAccessibility(publishConfig, userId, "BMWEB_ACCESSCTRL_ALWAYS_ON");
            JObject config = new JObject
            {
                { "searchIndex", searchIndexName },
                { "searchVersion", 3 },
                { "publishType", treeType.ToString() },
                { "configId", publishConfig != null ? publishConfig.pk_id : -1 },
                { "tenantId", publishConfig != null ? publishConfig.fk_tenant_id : -1 },
                { "accessControlEnabled", urlAccessControlData.internalPublishaccessControlEnabled },
                { "accessControlEndTime", string.Empty },
                { "publishTime", string.Empty }
            };
            UpdatePubConfigAccessControl(publishConfig, urlAccessControlData.internalPublishaccessControlEnabled,
                false);
            string filePath = $"{publishHelper.BaseStoragePath}/content/data/config.json";
            _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.BmContent, filePath,
                config.ToString());
        }
        catch (Exception e)
        {
            publishHelper.InsertError("CreateConfig failed");
            publishHelper.InsertError(e.Message);
        }
    }


    private void UpdatePubConfigAccessControl(tco_publish_config publishConfig, bool accessControlEnabled,
        bool isPublicURL)
    {
        TenantDBContext dbContext = _utility.GetTenantDBContext();
        if (publishConfig != null)
        {
            if (isPublicURL)
            {
                publishConfig.prod_pub_url_access_enabled = accessControlEnabled;
            }
            else
            {
                publishConfig.stg_pub_url_access_enabled = accessControlEnabled;
            }

            dbContext.SaveChanges();
        }
    }


    private void ClearExistingData(int configId)
    {
        TenantDBContext dbContext = _utility.GetTenantDBContext();
        tco_publish_config pubConfig = dbContext.tco_publish_config.FirstOrDefault(x => x.pk_id == configId);

        //commented because data is processed before delete
        //StringBuilder deletePath = new StringBuilder();
        //string stgUrl = pubConfig.stg_publish_url.TrimStart('/');
        //deletePath.Append(stgUrl + "/data/budgetreductiongraph");
        //_blobHelper.DeleteFolder(StorageAccount.PublishStage, BlobContainers.BmContent, deletePath.ToString());
    }


    private void GetTotalChaptersToProcess(string userId, int configId, bool isApprovalProcessPublish)
    {
        TenantDBContext dbContext = _utility.GetTenantDBContext();
        List<string> chapterListFromDB = (from pc in dbContext.tco_publish_chapter_info
            select pc.chapter_type).ToList();
        List<string> chaptersToProcess = chapterTypes.Where(p => chapterListFromDB.Any(p2 => p2 == p)).ToList();
        PublishConfig treeSel = _utility.GetPublishConfig(userId, configId, isApprovalProcessPublish);
        treeSel.stg_pub_total_chapter = chaptersToProcess.Count();
        treeSel.stg_pub_total_processed = 0;
        dbContext.SaveChanges();
    }


    private void UpdateBookmarkIdsForRunningApprovalProcess(string userId, string processId,
        List<ApprovalBookmarkDetails> approvalBookmarkDetails)
    {
        UserData userDetails = _utility.GetUserDetails(userId);
        TenantDBContext dbContext = _utility.GetTenantDBContext();

        var dataToUpdate = dbContext.tco_publish_review_child.Where(x =>
            x.fk_tenant_id == userDetails.tenant_id && x.processId.ToString() == processId).ToList();

        dataToUpdate.ForEach(x =>
        {
            x.bookmark_id = approvalBookmarkDetails.FirstOrDefault(y => y.key == x.uid) == null
                ? string.Empty
                : approvalBookmarkDetails.FirstOrDefault(y => y.key == x.uid).value;
        });

        dbContext.SaveChanges();
    }
}