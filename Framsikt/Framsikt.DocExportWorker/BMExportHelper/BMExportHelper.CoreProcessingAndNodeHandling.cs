using Aspose.Words;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.DocExportWorker.Helpers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Drawing;

namespace Framsikt.DocExportWorker;

public partial class BmExportHelper
{
    /// <summary>
    /// Inserts a page title header if required, with optional additional condition check
    /// </summary>
    /// <param name="publishHelper">The publish helper instance</param>
    /// <param name="builder">The document builder</param>
    /// <param name="userId">The user ID</param>
    /// <param name="isHeaderRequired">Reference to the header required flag</param>
    /// <param name="isPageBreak">Reference to the page break flag</param>
    /// <param name="additionalCondition">Additional condition that must be true (default: true)</param>
    /// <returns>True if header was inserted, false otherwise</returns>
    private void InsertHeaderIfRequired(IPublishHelper publishHelper,
        DocumentBuilder builder,
        string userId,
        ref bool isHeaderRequired,
        ref bool isPageBreak,
        bool additionalCondition = true)
    {
        if (!isHeaderRequired || !additionalCondition) return;

        publishHelper.InsertPageTitle(builder, userId, _languageStringsExportCommon["doc_OpBdgt_SA_title"].LangText);
        builder.InsertBreak(BreakType.PageBreak);
        isPageBreak = true;
        isHeaderRequired = false;
    }

    /// <summary>
    /// Inserts a Service Area heading if required
    /// </summary>
    /// <param name="publishHelper">The publish helper instance</param>
    /// <param name="builder">The document builder</param>
    /// <param name="userId">The user ID</param>
    /// <param name="isSAHeadingRequired">Reference to the SA heading required flag</param>
    /// <param name="leveloneData">The level one data containing the text</param>
    /// <returns>True if SA heading was inserted, false otherwise</returns>
    private bool InsertSaHeadingIfRequired(
        IPublishHelper publishHelper,
        DocumentBuilder builder,
        string userId,
        ref bool isSAHeadingRequired,
        dynamic leveloneData)
    {
        if (!isSAHeadingRequired) return false;
        
        publishHelper.InsertPageTitle(builder, userId, Convert.ToString(leveloneData.text));
        isSAHeadingRequired = false;
        return true;
    }

    private void ProcessNode(PublishProcessInputHelper processNodeInputData)
    {
        string tableId = string.Empty;
        bool insertAbstract = true, insertDescription = true;
        if (!(processNodeInputData.node.@checked || _utility.AreAnyChildrenChecked(processNodeInputData.node)))
        {
            return;
        }

        IPublishHelper publishHelper = PublishHelperFactory.CreatePublishHelper(processNodeInputData.userId,
            processNodeInputData.builder, _docStyle, _context, _container);
        try
        {
            publishHelper.StartNode(processNodeInputData.node);
            switch (processNodeInputData.node.type.Trim())
            {
                case "BudgetByServiceArea":
                    Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(processNodeInputData.userId);
                    if (orgLevelValue.Count == 1)
                    {
                        InsertBudgetByServiceArea(processNodeInputData.userId, processNodeInputData, "ServiceArea");
                    }
                    else
                    {
                        InsertBudgetByServiceAreaForMultiLevel(processNodeInputData.userId, processNodeInputData);
                    }

                    break;

                case "BudgetByServiceAreaLevel1":
                    _bmTenantSpecificSA.InsertBudgetByServiceAreaTenantSpecific(processNodeInputData.userId,
                        processNodeInputData, "level1");
                    break;

                case "BudgetByServiceAreaLevel2":
                    _bmTenantSpecificSA.InsertBudgetByServiceAreaTenantSpecific(processNodeInputData.userId,
                        processNodeInputData, "level2");
                    break;

                case "CEOSummary":

                    InsertCeoSummary(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear);
                    break;

                case "customNode":
                    bool headingIsBookmark = true;

                    if (processNodeInputData.parent != null)
                    {
                        if (processNodeInputData.parent.type.Equals("FinplanClimateAction",
                                StringComparison.InvariantCultureIgnoreCase) ||
                            processNodeInputData.parent.type.Equals("BlistClimateAction",
                                StringComparison.InvariantCultureIgnoreCase) ||
                            processNodeInputData.parent.type.Equals("ClimateActionCategory",
                                StringComparison.InvariantCultureIgnoreCase) ||
                            processNodeInputData.parent.type.Equals("ClimateActionBlistCategory",
                                StringComparison.InvariantCultureIgnoreCase))
                        {
                            headingIsBookmark = false;
                        }
                    }

                    if (processNodeInputData.parent == null ||
                        !processNodeInputData.parent.type.Equals("InvestmentsAllSA",
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        _customNode.InsertCustomNode(processNodeInputData.builder, processNodeInputData.userId,
                            Guid.Parse(processNodeInputData.node.id), processNodeInputData.budgetPhaseId,
                            processNodeInputData.showOnlyModified, true, headingIsBookmark, false, null, null,
                            processNodeInputData.incIntDesc);
                    }

                    break;

                case "BudgetProposalText":
                    if (processNodeInputData.node.id.ToLower() == "BudPrp".ToLower())
                    {
                        InsertBudgetProposalChapterNew(processNodeInputData.userId, processNodeInputData.builder,
                            processNodeInputData.node, processNodeInputData.budgetYear,
                            processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    }

                    if (processNodeInputData.node.id.ToLower() == "BudPrpText".ToLower())
                    {
                        InsertBudgetProposalTextChild(processNodeInputData.userId, processNodeInputData.builder);
                    }

                    break;

                case "BudGetForm_Fridm_empt":
                    InsertBudgetFormChapterNew(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudGetForm_Fridm_empt_title", processNodeInputData.treePath);
                    break;

                case "BudGetForm_Fridm":
                    InsertBudgetFormChapterNew(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudGetForm_Fridm_title", processNodeInputData.treePath);
                    break;

                case "AppendixRegulationReport":
                case "AppendixRegulationReportNew":
                    if (_utility.AreAnyChildrenChecked(processNodeInputData.node))
                    {
                        UserData userData = _utility.GetUserDetails(processNodeInputData.userId);
                        Dictionary<string, clsLanguageString> langStringsBudMgmt =
                            _utility.GetLanguageStrings(userData.language_preference, processNodeInputData.userId,
                                "BudgetManagement");
                        publishHelper.InsertHeading(processNodeInputData.userId,
                            langStringsBudMgmt["Rpt_1A_heading1"].LangText, _context, true);
                    }

                    break;

                case "LongTermInvestmentPlan":
                    if (_utility.AreAnyChildrenChecked(processNodeInputData.node))
                    {
                        UserData userData = _utility.GetUserDetails(processNodeInputData.userId);
                        Dictionary<string, clsLanguageString> langStringsBudForms =
                            _utility.GetLanguageStrings(userData.language_preference, processNodeInputData.userId,
                                "BudgetManagement");
                        publishHelper.InsertHeading(processNodeInputData.userId,
                            langStringsBudForms["bm_Appendix_Long_Term_Inv_Plan"].LangText, _context,
                            true);
                    }

                    break;

                case "LongTermInvPlanAppendix2A":
                    InsertBudgetFormsLongTerm2A2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "LongTermInvPlanAppendix2B":
                    InsertBudgetFormsLongTerm2B2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudgetSheet1A":
                case "BudgetSheet1A_budget":
                    InsertBudgetSheet1A(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        processNodeInputData.treePath, processNodeInputData.node.type.Trim());
                    insertAbstract = false;
                    break;

                case "BudgetSheet1B":
                case "BudgetSheet1B_budget":
                    InsertBudgetSheet1B(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath,
                        processNodeInputData.node.type.Trim());
                    insertAbstract = false;
                    break;

                case "BudgetSheetFin1A":
                    InsertBudgetSheetFin1A(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "Framsikt1A_2020":
                    InsertFramsikt1A2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "Framsikt1B_2020":
                    InsertFramsikt1B2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "Framsikt1B_2020_New":
                    InsertFramsikt1B2020New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1A_2020":
                    InsertBudgetForms1A2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1A_SA":
                    InsertBudgetForms1APerServiceId(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, false);
                    break;

                case "BudSht1A_SA_Change":
                    InsertBudgetForms1APerServiceId(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, true);
                    break;

                case "BMDevelopmentUseOfFunds":
                    InsertBMDevelopmentUseOfFunds(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath);
                    break;

                case "BudSht1B_2020":
                    InsertBudgetForms1B2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1B_2020_New":
                    InsertBudgetForms1B2020New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1B_Consolidation":
                    InsertBudgetForms1BConsolidation(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht2A_2020":
                    InsertBudgetForms2A2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht2B_2020":
                    InsertBudgetForms2B2020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht2B_Consolidation":
                    InsertBudgetForms2BConsolidation(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht3_2020":
                    InsertBudgetForms32020(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudgetSheetFin1B":
                    InsertBudgetSheetFin1B(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "BudgetSheet3":
                case "BudgetSheet3_budget":
                    InsertBudgetSheet3(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath,
                        processNodeInputData.node.type.Trim());
                    insertAbstract = false;
                    break;

                case "BudgetSheet2A":
                case "BudgetSheet2A_budget":
                    InsertBudgetSheet2A(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath,
                        processNodeInputData.node.type.Trim());
                    insertAbstract = false;
                    break;

                case "BudgetSheet2B":
                case "BudgetSheet2B_budget":
                    InsertBudgetSheet2B(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath,
                        processNodeInputData.node.type.Trim());
                    insertAbstract = false;
                    break;

                case "BudgetSheet4":
                case "BudgetSheet4_budget":
                    InsertBudgetSheet4(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath,
                        processNodeInputData.node.type.Trim());
                    insertAbstract = false;
                    break;

                case "Framsikt1A_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "Framsikt1A_Fridm_empt");
                    break;

                case "Framsikt1B_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "Framsikt1B_Fridm_empt");
                    break;

                case "BudSht1A_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht1A_Fridm_empt");
                    break;

                case "BudSht1B_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht1B_Fridm_empt");
                    break;

                case "BudSht2A_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht2A_Fridm_empt");
                    break;

                case "BudSht2B_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht2B_Fridm_empt");
                    break;

                case "BudSht3_Fridm_empt":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht3_Fridm_empt");
                    break;

                case "Framsikt1A_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "Framsikt1A_Fridm");
                    break;

                case "Framsikt1B_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "Framsikt1B_Fridm", true);
                    break;

                case "BudSht1A_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht1A_Fridm");
                    break;

                case "BudSht1B_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified, "BudSht1B_Fridm",
                        true);
                    break;

                case "BudSht2A_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "BudSht2A_Fridm");
                    break;

                case "BudSht2B_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified, "BudSht2B_Fridm",
                        true);
                    break;

                case "BudSht3_Fridm":
                    InsertBudFormForFridData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified, "BudSht3_Fridm");
                    break;

                case "YearlyBudgetForm_Fridm_empt":
                    InsertBudgetFormChapterNew(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "YearlyBudgetForm_Fridm_empt_title", processNodeInputData.treePath);
                    break;

                case "YearlyBudgetForm_Fridm":
                    InsertBudgetFormChapterNew(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "YearlyBudgetForm_Fridm_title", processNodeInputData.treePath);
                    break;

                case "Framsikt1A_YearlyBudget_New":
                    InsertFramsikt1AYearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "Framsikt1B_YearlyBudget_New":
                    InsertFramsikt1BYearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "Framsikt1B_YB_Appendix":
                    InsertFramsikt1BYearlyBudgetNewTree(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1A_YearlyBudget_New":
                    InsertBudSht1AYearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1B_YearlyBudget_New":
                    InsertBudSht1BYearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht1B_YB_Appendix":
                    InsertBudSht1BYearlyBudgetNewTree(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht2A_YearlyBudget_New":
                    InsertBudSht2AYearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht2B_YearlyBudget_New":
                    InsertBudSht2BYearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "BudSht3_YearlyBudget_New":
                    InsertBudSht3YearlyBudget_New(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    break;

                case "Framsikt1A_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "Framsikt1A_YearlyBudget_New_Fridm_empt");
                    break;

                case "Framsikt1B_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "Framsikt1B_YearlyBudget_New_Fridm_empt");
                    break;

                case "BudSht1A_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht1A_YearlyBudget_New_Fridm_empt");
                    break;

                case "BudSht1B_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht1B_YearlyBudget_New_Fridm_empt");
                    break;

                case "BudSht2A_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht2A_YearlyBudget_New_Fridm_empt");
                    break;

                case "BudSht2B_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht2B_YearlyBudget_New_Fridm_empt");
                    break;

                case "BudSht3_YearlyBudget_Fridm_empt":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht3_YearlyBudget_New_Fridm_empt");
                    break;

                case "Framsikt1A_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "Framsikt1A_YearlyBudget_New_Fridm");
                    break;

                case "Framsikt1B_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "Framsikt1B_YearlyBudget_New_Fridm");
                    break;

                case "BudSht1A_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht1A_YearlyBudget_New_Fridm");
                    break;

                case "BudSht1B_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht1B_YearlyBudget_New_Fridm");
                    break;

                case "BudSht2A_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht2A_YearlyBudget_New_Fridm");
                    break;

                case "BudSht2B_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht2B_YearlyBudget_New_Fridm");
                    break;

                case "BudSht3_YearlyBudget_Fridm":
                    InsertBudFormForFridmData_YearlyBudget(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, "BudSht3_YearlyBudget_New_Fridm");
                    break;

                case "YearlyBudgetForm_Consolidation1B":
                    InsertYearlyBudgetForms1BConsolidation(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified);
                    break;

                case "YearlyBudgetForm_Consolidation2B":
                    InsertYearlyBudgetForms2BConsolidation(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear,
                        processNodeInputData.treePath, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified);
                    break;

                case "FeePolicy":
                    InsertFeePolicy(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "IntroGoalTarget":
                    InsertIntroGoalTargetNew(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "FocusArea":
                    if (publishHelper.GetType() == typeof(WordHelper))
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                            _context);
                    }
                    //Please don't remove this commented code
                    //if (_utility.AreAnyChildrenChecked(node))
                    //{
                    //    var textNode = node.items.FirstOrDefault(x => x.type == "FocusAreaLongDescription");
                    //    if (textNode != null && textNode.@checked)
                    //    {
                    //        var bookmarkFortextNode = textNode == null ? "" : textNode.text;
                    //        if (_bmGoalsandtarget.HasFocusAreaText(userId, budgetYear, node.id, builder))
                    //        {
                    //            publishHelper.InsertHeading(userId, StyleIdentifier.Heading3, bookmarkFortextNode, _context, true);

                    //            Dictionary<string, clsLanguageString> lsBudgetProposal = _utility.GetLanguageStrings(_expLang, userId, "BudgetProposal");
                    //            _bmGoalsandtarget.InsertFocusAreaText(userId, budgetYear, node.id, builder);
                    //        }
                    //    }
                    //}
                    break;

                case "FocusAreaLongDescription":

                    if (processNodeInputData.node.@checked)
                    {
                        string nodeId = processNodeInputData.node.id.Split('-')[1];
                        if (_bmGoalsandtarget.HasFocusAreaText(processNodeInputData.userId,
                                processNodeInputData.budgetYear, nodeId, processNodeInputData.builder))
                        {
                            publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                                _context, true);
                            _bmGoalsandtarget.InsertFocusAreaText(processNodeInputData.userId,
                                processNodeInputData.budgetYear, nodeId, processNodeInputData.builder);
                        }
                    }

                    break;

                case "VisionAmbitionCityLvl":
                    // insert Vision and ambision
                    _bmGoalsandtarget.InsertVisionAmbision(publishHelper, processNodeInputData.builder,
                        processNodeInputData.userId, string.Empty, "ALL", processNodeInputData.budgetYear);
                    break;

                case "GoalsCityLvl":
                    if (processNodeInputData.parent.type.Equals("FocusArea",
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        _bmGoalsandtarget.InsertGoalsSectionForIntro(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node, processNodeInputData.showOnlyModified,
                            processNodeInputData.budgetPhaseId, processNodeInputData.parent.id,
                            processNodeInputData.treePath);
                        insertAbstract = false;
                    }

                    break;

                case "GoalsCityLvlNoFa":
                    _bmGoalsandtarget.InsertGoalsSectionForIntro(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId, string.Empty,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "TargetsCityLvl":
                    if (processNodeInputData.parent.type.Equals("FocusArea",
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        _bmGoalsandtarget.InsertTargetSectionForIntro(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node, processNodeInputData.parent.id);
                    }

                    break;

                case "TargetsCityLvlNoFa":
                    _bmGoalsandtarget.InsertTargetSectionForIntro(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear, processNodeInputData.node,
                        string.Empty);
                    break;

                case "StratergyTextCityLevel":
                    _bmGoalsandtarget.InsertStratergyTextCityLevel(processNodeInputData.userId, publishHelper,
                        string.Empty, 1, string.Empty, processNodeInputData.budgetYear, true);
                    break;

                case "AssignmentsCityLvl":
                    if (processNodeInputData.parent.type.Equals("FocusArea",
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        _bmAssignmentHelper.InsertBMAssignmentPerFocusAreaData(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node, processNodeInputData.parent,
                            processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                            processNodeInputData.treePath);
                        //_bmGoalsandtarget.InsertAssigmentsCityLvl(userId, builder, budgetYear, node, parent, showOnlyModified, budgetPhaseId, treePath);
                        insertAbstract = false;
                    }

                    break;

                case "StrategyCityLvl":
                    if (processNodeInputData.parent.type.Equals("FocusArea",
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        _bmGoalsandtarget.InsertStrategySectionCityLevel(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node, processNodeInputData.parent);
                        insertAbstract = false;
                    }

                    break;

                case "AssignmentsCityLvlNoFa":
                    _bmGoalsandtarget.InsertAssigmentsCityLvl(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear, processNodeInputData.node,
                        null, processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "CityStrategyAndInvestmentOnFinancialPlan":
                    InsertCityStrategyAndInvestmentOnFinancialPlan(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear);
                    break;

                case "PlanStratergyCityLevel":
                    InsertCityStrategyPlan(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetYear, budgetPhaseId: string.Empty,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath);
                    break;

                case "StrategyAndObjectives":
                    InsertStrategyAndObjectivesNew(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "DocumentKeyFig":
                    KeyfigForDocument(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node.text);
                    break;

                case "MainTextNode":
                    InsertMainTextNode(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear);
                    break;

                case "BudgetOf":
                    InsertBudgetOf(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear);
                    break;

                case "CentralGovtRequirementAndGuidelines":
                    InsertCentralGovtRequirementAndGuidelines(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear);
                    break;

                case "ChallengeAndStrategyInFinancialPlan":
                    InsertChallengeAndStrategyInFinancialPlan(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear);
                    break;

                case "KostraCityChallenge":
                    InsertKostraCityChallenge(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.templateId);
                    break;

                case "StatusFinKPI":
                    InsertStatusFinKPI(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node, processNodeInputData.templateId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId);
                    break;

                case "CostReductionPotential":
                    InsertCostReductionPotential(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node, processNodeInputData.templateId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "OverallDemography":
                    if (processNodeInputData.node.id == "POVD")
                    {
                        InsertOverallDemography(processNodeInputData.builder, processNodeInputData.userId,
                            processNodeInputData.templateId, processNodeInputData.budgetYear,
                            processNodeInputData.node, processNodeInputData.showOnlyModified,
                            processNodeInputData.budgetPhaseId, processNodeInputData.treePath,
                            processNodeInputData.incIntDesc);
                        insertAbstract = false;
                    }

                    break;

                case "PopulationDevelopment":
                    if (publishHelper.GetType() == typeof(WordHelper) &&
                        _utility.AreAnyChildrenChecked(processNodeInputData.node))
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                            _context);
                    }

                    break;

                case "PopulationDevelopmentSection":
                    List<string> pdsList = new List<string>
                        { "pf0", "pf1", "pf2", "pf3", "pf4", "pf5", "pf6", "pf7" };
                    if (pdsList.Contains(processNodeInputData.node.id))
                    {
                        InsertPopulationDevelopmentSection(processNodeInputData.builder,
                            processNodeInputData.userId, processNodeInputData.templateId,
                            processNodeInputData.budgetYear, processNodeInputData.node,
                            processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                            processNodeInputData.treePath, processNodeInputData.incIntDesc);
                        insertAbstract = false;
                    }

                    break;

                case "InvestmentPlan":
                    InsertInvestmentPlanNew(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "InvestmentStrategy":
                    InsertInvestmentStrategy(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear);
                    break;

                case "InvestmentsAllSA":
                    UserData userDetails = _utility.GetUserDetails(processNodeInputData.userId);
                    Dictionary<string, clsLanguageString> langStringsBudManagement =
                        _utility.GetLanguageStrings(userDetails.language_preference, processNodeInputData.userId,
                            "BudgetManagement");
                    if (_utility.AreAnyChildrenChecked(processNodeInputData.node))
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId,
                            langStringsBudManagement["Intro_Investments_text"].LangText, _context, true);
                    }

                    InsertInvestmentProgramwise(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.budgetYear, "InvestmentsAllSA", processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.incIntDesc, processNodeInputData.incBlistIntDesc);
                    insertAbstract = false;
                    break;

                case "InvestmentFinSummary":
                    InsertInvestmentFinSummary(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear);
                    insertAbstract = false;
                    break;

                case "InvestmentFinDecriptionText":
                    InsertInvestmentFinText(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.incIntDesc,
                        processNodeInputData.budgetYear);
                    break;

                case "InvestmentFinPlanCompare":
                    InsertFinInvestmentPlanComparedData(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetYear);
                    break;

                case "ProfitRevenueGraph_position_2":
                    InsertProfitRevenueGraph_position_2(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear);
                    insertAbstract = false;
                    break;

                case "ProfitRevenueGraph_position_4":
                    InsertProfitRevenueGraph_position_4(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear, 4);
                    insertAbstract = false;
                    break;

                case "LDIPAppendixDebtPerInhabitant":
                    InsertProfitRevenueGraph_position_4(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear, 20);
                    insertAbstract = false;
                    break;

                case "BudgetSummary":
                    InsertBudgetSummary(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "RevenueData":
                    if (processNodeInputData.node.id.ToLower() == "RevDat".ToLower())
                    {
                        tableId = processNodeInputData.showOnlyModified
                            ? "FinplanOverviewTables_change"
                            : "FinplanOverviewTables";
                        InsertRevenueData(processNodeInputData.userId, processNodeInputData.builder,
                            processNodeInputData.budgetYear, processNodeInputData.node,
                            processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                            processNodeInputData.treePath, tableId);
                        insertAbstract = false;
                    }
                    else if (int.TryParse(processNodeInputData.node.id, out _))
                    {
                        bool displayOnlyTables = _globalVariabl.getDisplayOnlyTablesProp();
                        if (!displayOnlyTables)
                        {
                            InsertActionDescriptions(processNodeInputData.userId, processNodeInputData.builder,
                                processNodeInputData.node, processNodeInputData.incIntDesc,
                                processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                                processNodeInputData.budgetYear);
                        }
                    }

                    break;

                case "ExpensesData":
                    if (processNodeInputData.node.id.ToLower() == "ExpData".ToLower())
                    {
                        tableId = processNodeInputData.showOnlyModified
                            ? "FinplanOverviewTables_change"
                            : "FinplanOverviewTables";
                        InsertExpensesData(processNodeInputData.userId, processNodeInputData.builder,
                            processNodeInputData.budgetYear, processNodeInputData.node,
                            processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                            processNodeInputData.treePath, tableId);
                        insertAbstract = false;
                    }
                    else if (int.TryParse(processNodeInputData.node.id, out _))
                    {
                        bool displayOnlyTables = _globalVariabl.getDisplayOnlyTablesProp();
                        if (!displayOnlyTables)
                        {
                            InsertActionDescriptions(processNodeInputData.userId, processNodeInputData.builder,
                                processNodeInputData.node, processNodeInputData.incIntDesc,
                                processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                                processNodeInputData.budgetYear);
                        }
                    }

                    break;

                case "centralFigures1BBudgets":
                    tableId = processNodeInputData.showOnlyModified
                        ? "FinplanOverviewTables_change"
                        : "FinplanOverviewTables";
                    InsertCentralFig1BSheet(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        processNodeInputData.treePath, tableId);
                    break;

                case "FinancialExpensesAndRevenues":
                    if (processNodeInputData.node.id.ToLower() == "FExRev".ToLower())
                    {
                        tableId = processNodeInputData.showOnlyModified
                            ? "FinplanOverviewTables_change"
                            : "FinplanOverviewTables";
                        InsertFinancialExpensesAndRevenues(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node, processNodeInputData.budgetPhaseId,
                            processNodeInputData.showOnlyModified, processNodeInputData.treePath, tableId);
                        insertAbstract = false;
                    }
                    else if (int.TryParse(processNodeInputData.node.id, out _))
                    {
                        bool displayOnlyTables = _globalVariabl.getDisplayOnlyTablesProp();
                        if (!displayOnlyTables)
                        {
                            InsertActionDescriptions(processNodeInputData.userId, processNodeInputData.builder,
                                processNodeInputData.node, processNodeInputData.incIntDesc,
                                processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                                processNodeInputData.budgetYear);
                        }
                    }

                    break;

                case "FundsAndReserves":
                    if (processNodeInputData.node.id.ToLower() == "FunRev".ToLower())
                    {
                        tableId = processNodeInputData.showOnlyModified
                            ? "FinplanOverviewTables_change"
                            : "FinplanOverviewTables";
                        InsertFundsAndReserves(processNodeInputData.userId, processNodeInputData.builder,
                            processNodeInputData.budgetYear, processNodeInputData.node,
                            processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                            processNodeInputData.treePath, tableId);
                        insertAbstract = false;
                    }

                    break;

                case "FinancialProvisions":
                    if (int.TryParse(processNodeInputData.node.id, out _))
                    {
                        bool displayOnlyTables = _globalVariabl.getDisplayOnlyTablesProp();
                        if (!displayOnlyTables)
                        {
                            InsertActionDescriptions(processNodeInputData.userId, processNodeInputData.builder,
                                processNodeInputData.node, processNodeInputData.incIntDesc,
                                processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                                processNodeInputData.budgetYear);
                        }
                    }

                    break;

                case "SummaryOfAvailableFunds":
                    InsertSummaryOfAvailableFunds(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "PersonalAndHMS":
                    InsertPersonalAndHMS(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear);
                    break;

                case "FinSummaryBudgetTable":
                    InsertFinSummaryBudgetTable(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "BudgetReductionGraph":
                    InsertbudgetReductionGraph(processNodeInputData.userId, processNodeInputData.builder,
                        nameof(PublishSection.GetSection.BUDGTREDUCTION), processNodeInputData.budgetYear,
                        processNodeInputData.node,
                        processNodeInputData.parent.id, processNodeInputData.showOnlyModified,
                        processNodeInputData.budgetPhaseId, processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "TenantLevelCabOverview":
                    InsertTenantLevelCabOverview(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "BudgetByServiceAreaTenantLevel":
                    _bmBudBySaHelper.InsertBudgetByServiceAreaTenantLevel(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.node, processNodeInputData.parent.id,
                        processNodeInputData.budgetYear, processNodeInputData.showOnlyModified,
                        processNodeInputData.budgetPhaseId, processNodeInputData.cacheKey);
                    insertAbstract = false;
                    break;

                case "ServiceAreaBudget":
                    InsertServiceAreaBudget(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId);
                    insertAbstract = false;
                    break;

                case "NetBudgetByServiceArea":
                    InsertNetBudgetByServiceArea(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "OperationalFinSummary":
                    InsertOperationalFinSummary(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "ProfitRevenueChart":
                    InsertProfitRevenueChart(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "ProfitRevenueGraph_position_1":
                    InsertProfitRevenueGraph_position_1(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.budgetYear, false);
                    insertAbstract = false;
                    break;

                case "NetOperatingProfit":
                    InsertProfitRevenueGraph_position_1(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.budgetYear, true);
                    insertAbstract = false;
                    break;

                case "ProfitRevenueNetFinanceAndInstallments":
                    InsertProfitRevenueNetFinanceAndInstallments(processNodeInputData.builder,
                        processNodeInputData.userId, processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear,
                        processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "ProfitRevenueGraph_position_3":
                    InsertProfitRevenueGraph_position_3(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear);
                    insertAbstract = false;
                    break;

                case "ProfitRevenueTable":
                    InsertProfitRevenueTable(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear);
                    insertAbstract = false;
                    break;

                case "ProfitRevenueDesc":
                    InsertProfitRevenueDesc(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetYear);
                    break;

                case "FinPlan_FundDevelop_Year1":
                    InsertFPFundDevDataData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "FinPlan_FundDevelop_Year1");
                    break;

                case "FinPlan_FundDevelop_Year2":
                    InsertFPFundDevDataData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "FinPlan_FundDevelop_Year2");
                    break;

                case "FinPlan_FundDevelop_Year3":
                    InsertFPFundDevDataData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "FinPlan_FundDevelop_Year3");
                    break;

                case "FinPlan_FundDevelop_Year4":
                    InsertFPFundDevDataData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "FinPlan_FundDevelop_Year4");
                    break;

                case "FinPlan_FundDevelop_Desc":
                    InsertFPFundDevDataData(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified,
                        "FinPlan_FundDevelop_Desc");
                    break;

                case "Apendix":
                    InsertApendixNew(processNodeInputData.builder, processNodeInputData.userId);
                    break;

                case "GrantsPerServiceArea":
                    InsertGrantsPerServiceArea(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath);
                    break;

                case "FridimGrantsPerServiceArea":
                    InsertFridimGrantsPerServiceArea(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath,
                        processNodeInputData.budgetYear);
                    break;

                case "StrategyAssessment":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context, true);
                    break;

                case "Strategies":
                case "AssessmentFPLevel1":
                    //case "AssessmentArea":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context);
                    break;

                case "StrategyServiceArea":
                    if (processNodeInputData.parent.text != processNodeInputData.node.text)
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                            _context);
                    }

                    _insertServiceAreaHeader =
                        false; //parent.text != node.text; //To avoid inserting header 2 times #85623
                    _strategyServiceArea = processNodeInputData.node.text;
                    break;

                case "AssessmentAreaActionsTable":
                    _insertServiceAreaHeader = InsertStrategyAssessmentAreaActionsTable(
                        processNodeInputData.builder, processNodeInputData.userId, processNodeInputData.node,
                        processNodeInputData.budgetYear, processNodeInputData.parent, _strategyServiceArea,
                        _insertServiceAreaHeader);
                    break;

                case "AssessmentAreaDescription":
                    _insertServiceAreaHeader = _bmOppHelper.InsertAssessmentAreaDescription(
                        processNodeInputData.userId, processNodeInputData.budgetYear, publishHelper,
                        processNodeInputData.node, processNodeInputData.parent, _strategyServiceArea,
                        _insertServiceAreaHeader);
                    break;

                case "AssessmentDescription":
                case "AssessmentConclusionDescription":
                    _insertServiceAreaHeader = _bmOppHelper.InsertAssessmentDescription(processNodeInputData.userId,
                        publishHelper, processNodeInputData.node,
                        processNodeInputData.node.type == "AssessmentConclusionDescription", _strategyServiceArea,
                        _insertServiceAreaHeader, processNodeInputData.budgetYear);
                    break;

                case "AssessmentGuidlineDescription":
                    _insertServiceAreaHeader = _bmOppHelper.InsertGuidlineAssessmentDescription(
                        processNodeInputData.userId, publishHelper, processNodeInputData.node, _strategyServiceArea,
                        _insertServiceAreaHeader);
                    break;

                case "NetResultEffectSummery":
                    _insertServiceAreaHeader = _bmOppHelper.InsertAssessmentNetResultEffectSummery(
                        processNodeInputData.userId, processNodeInputData.budgetYear, publishHelper,
                        processNodeInputData.node, _strategyServiceArea, true, _insertServiceAreaHeader);
                    break;

                case "OperationActionSummery":
                case "OperationActionsReductionSummery":
                case "OperationActionsIncrementSummery":
                    _insertServiceAreaHeader = _bmOppHelper.InsertAssessmentOperationActionSummery(
                        processNodeInputData.userId, processNodeInputData.budgetYear, publishHelper,
                        processNodeInputData.node, _strategyServiceArea, true, _insertServiceAreaHeader);
                    break;

                case "ActionDetailsAppendix":
                    _bmActionDetails.GenerateActionChapter(processNodeInputData.builder,
                        processNodeInputData.userId, processNodeInputData.node, processNodeInputData.incIntDesc,
                        processNodeInputData.budgetPhaseId, processNodeInputData.budgetYear,
                        processNodeInputData.showOnlyModified);
                    break;

                case "yearlyBudgetDataSAwise":
                    bool divideByMillions =
                        _utility.CheckOnDivideByMillions(processNodeInputData.userId, "Million_BudgetSheet4");
                    InsertYBdataSAwise(processNodeInputData.userId, processNodeInputData.builder, divideByMillions,
                        processNodeInputData.node, processNodeInputData.treePath,
                        processNodeInputData.budgetPhaseId, processNodeInputData.showOnlyModified);
                    insertAbstract = false;
                    processNodeInputData.builder.InsertBreak(BreakType.PageBreak);
                    break;

                case "AppendixDetailedbudgetbyserviceunits":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context, true);
                    _bmDetailBdtbySu.CreateReport(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified);
                    break;

                case "AppendixYearlybudgetbyserviceunits":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context, true);
                    _bmYearlyBudgetServiceUnit.CreateYearlybudgetbyserviceunitsReport(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.budgetYear, processNodeInputData.showOnlyModified);
                    break;

                case "ApendixMatrixTable":
                    InsertApendixMatrixTable(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "FinancialPlanTable":
                    InsertFinancialPlanTable(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.budgetPhaseId,
                        processNodeInputData.showOnlyModified, processNodeInputData.treePath);
                    insertAbstract = false;
                    break;

                case "InvestmentProgramwise":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context, true);
                    InsertInvestmentProgramwise(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.budgetYear, "InvestmentProgramwise", processNodeInputData.node,
                        processNodeInputData.showOnlyModified, processNodeInputData.budgetPhaseId,
                        processNodeInputData.incIntDesc, processNodeInputData.incBlistIntDesc);
                    insertAbstract = false;
                    break;

                case "InvestmentProgramwiseTable":
                    insertAbstract = false;
                    break;

                case "FPAssignmentsCityLevel":
                    _bmAssignmentHelper.InsertFPAssignmentsForCommonChapter(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.node, processNodeInputData.budgetYear,
                        processNodeInputData.treePath,
                        processNodeInputData.showOnlyModified ? processNodeInputData.budgetPhaseId : string.Empty);
                    break;

                case "ClimateStatisticsAndActionSummary":
                    InsertClimateStatisticsAndActionSummary(processNodeInputData.userId,
                        processNodeInputData.builder);
                    break;

                case "ClimateDataAnalysis":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context, true);

                    _customNode.InsertNodeDescription(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.treePath, processNodeInputData.budgetYear,
                        processNodeInputData.showOnlyModified ? processNodeInputData.budgetPhaseId : string.Empty,
                        insertAbstract, insertDescription);

                    _exportClimateDataAnalysis.InserClimateDataAnalysis(processNodeInputData.userId, publishHelper,
                        processNodeInputData.builder, processNodeInputData.templateId,
                        processNodeInputData.budgetYear, _context, false);
                    break;

                case "ClimateDuckyDataGraph":
                    InsertClimateDuckyDataGraph(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node);
                    break;

                case "FinplanClimateAction":
                case "BlistClimateAction":
                case "ClimateActionCategory":
                case "ClimateActionBlistCategory":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context);
                    break;

                case "ClimateActionCategoryTable":
                    bool filterBlistActions = processNodeInputData.parent.type == "ClimateActionBlistCategory";
                    _bmOppHelper.InsertClimateActionCategoryData(processNodeInputData.userId,
                        processNodeInputData.builder, processNodeInputData.budgetYear, processNodeInputData.node,
                        filterBlistActions);
                    break;

                case "ClimateActionsDescriptionCL":
                    bool filterOnBlistActions = processNodeInputData.parent.type == "ClimateActionBlistCategory";
                    InsertClimateActionDescriptions(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node, filterOnBlistActions);
                    break;

                case "FinPlanAssignmentSummary":
                    InsertFinplanAssignmentSummary(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "POLSIMQADoc":
                    if (publishHelper.GetType() == typeof(WordHelper))
                    {
                        _BMPolsimQA.InsertPOLSIMQADocument(processNodeInputData.userId,
                            processNodeInputData.budgetYear, processNodeInputData.builder);
                    }

                    break;

                case "ClimateActionEmissionGraph":
                    InsertClimateActionEmissionGraph(processNodeInputData.userId, processNodeInputData.budgetYear,
                        processNodeInputData.node, processNodeInputData.builder);
                    break;

                case "FeesSummary":
                    if (publishHelper.GetType() == typeof(WordHelper))
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                            _context, false);
                    }

                    _customNode.InsertNodeDescription(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.treePath, processNodeInputData.budgetYear,
                        processNodeInputData.showOnlyModified ? processNodeInputData.budgetPhaseId : string.Empty,
                        insertAbstract, insertDescription);
                    InsertBudProFeeDetails(processNodeInputData.userId, processNodeInputData.budgetYear,
                        processNodeInputData.node, processNodeInputData.builder);
                    break;

                case "PlanActions":
                    if (publishHelper.GetType() == typeof(WordHelper))
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                            _context, false);
                    }

                    _customNode.InsertNodeDescription(processNodeInputData.builder, processNodeInputData.userId,
                        processNodeInputData.node, processNodeInputData.treePath, processNodeInputData.budgetYear,
                        processNodeInputData.showOnlyModified ? processNodeInputData.budgetPhaseId : string.Empty,
                        insertAbstract, insertDescription);
                    InsertPlanActionDetails(processNodeInputData.userId, processNodeInputData.budgetYear,
                        processNodeInputData.node, processNodeInputData.builder);
                    break;

                case "PopulationChartAndTable":
                    _popDev.InsertPopulationChartAndTable(processNodeInputData.userId, processNodeInputData.builder,
                        processNodeInputData.budgetYear, processNodeInputData.node);
                    break;

                case "FinancialSustainabilityKPIRadarGraph":
                    InsertFinancialSustainabilityRadarGraphAndTableForKPI(processNodeInputData.userId,
                        processNodeInputData.budgetYear, processNodeInputData.builder, processNodeInputData.node);
                    insertAbstract = false;
                    break;

                case "BM_Perosnal_and_Org":
                    publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                        _context);
                    break;

                case "BM_ManYears":
                    _bmBudBySaHelper.InsertBMManYearsTable(processNodeInputData.userId,
                        processNodeInputData.budgetYear, processNodeInputData.builder, processNodeInputData.node,
                        false, "");
                    break;

                case "FocusAreaSectionNew": //108111
                    if (publishHelper.GetType() == typeof(WordHelper))
                    {
                        publishHelper.InsertHeading(processNodeInputData.userId, processNodeInputData.node.text,
                            _context, false);
                    }

                    break;

                case "PlanInfoSection":
                    InsertPlanInformation(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "ApprovedPlans":
                    InsertApprovedPlansTable(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "IncludedPlanAndTask":
                    InsertPlanAndPlanningTaskTable(processNodeInputData.userId, processNodeInputData.builder);
                    break;

                case "AppendixFpBudgetbyFunction":
                case "BudPrpBudgetbyFunction":
                case "BudgetSummaryBudgetbyFunction":
                    _FinplanYearlyBudgetSummaryHelper.Initialize(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node)
                        .GetAwaiter().GetResult();
                    _FinplanYearlyBudgetSummaryHelper.InsertFpBudgetbyFunctionalArea().GetAwaiter().GetResult();
                    break;

                case "AppendixFpBudgetbyKostraFunction":
                case "BudPrpBudgetbyKostraFunction":
                case "BudgetSummaryBudgetbyKostraFunction":
                    _FinplanYearlyBudgetSummaryHelper.Initialize(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node)
                        .GetAwaiter().GetResult();
                    _FinplanYearlyBudgetSummaryHelper.InsertFpBudgetbyKostraFunction().GetAwaiter().GetResult();
                    break;

                case "AppendixFpBudgetbyUnit":
                case "BudPrpBudgetbyUnit":
                case "BudgetSummaryBudgetbyUnit":
                    _FinplanYearlyBudgetSummaryHelper.Initialize(processNodeInputData.userId,
                            processNodeInputData.builder, processNodeInputData.budgetYear,
                            processNodeInputData.node)
                        .GetAwaiter().GetResult();
                    _FinplanYearlyBudgetSummaryHelper.InsertFpBudgetbyUnitArea().GetAwaiter().GetResult();
                    break;
            }

            var excludedNodeTypes = new HashSet<string>(StringComparer.InvariantCultureIgnoreCase)
            {
                "customNode",
                "CEOSummary",
                "PlanActions",
                "ClimateDataAnalysis",
                "FeesSummary",
                "PlanStratergyCityLevel"
            };
            // Check if the node type is not in the excluded set and if the node is editable
            if (!excludedNodeTypes.Contains(processNodeInputData.node.type) &&
                processNodeInputData.node.isEditableNode)
            {
                _customNode.InsertNodeDescription(processNodeInputData.builder, processNodeInputData.userId,
                    processNodeInputData.node, processNodeInputData.treePath, processNodeInputData.budgetYear,
                    processNodeInputData.showOnlyModified ? processNodeInputData.budgetPhaseId : string.Empty,
                    insertAbstract, insertDescription);
            }

            publishHelper.EndNode(processNodeInputData.node);
        }
        catch (Exception e)
        {
            publishHelper.InsertError("Process publish failed at Processnode");
            publishHelper.InsertError($"path-{publishHelper.GetCurrentChapterPath()}");
            publishHelper.InsertError(e.ToString());
            LogError(e, publishHelper);
        }
    }


    private void ProcessChildNodes(string userId, DocumentBuilder builder, PublishTreeNode childNodes,
        PublishProcessInputHelper processNodeInputData, string levelSetUp = "ServiceArea")
    {
        string templateId = processNodeInputData.templateId;
        bool incIntDesc = processNodeInputData.incIntDesc;
        string budgetPhaseId = processNodeInputData.budgetPhaseId;
        bool showOnlyModified = processNodeInputData.showOnlyModified;
        int budgetYearBudMan = processNodeInputData.budgetYear;
        bool incBlistIntDesc = processNodeInputData.incBlistIntDesc;
        var orgVersionContent =
            _utility.GetOrgVersionSpecificContent(userId, _utility.GetForecastPeriod(budgetYearBudMan, 1));
        UserData userDetails = _utility.GetUserDetails(userId);
        Dictionary<string, clsLanguageString> langStringValuesDocExport =
            _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
        bool insertTarget = false;
        bool goalSectionSelected = false;
        bool tgtSectionSelected = false;
        bool nodeProcessed = false;
        bool isHeadingInserted = false;

        foreach (var subnode in childNodes.items)
        {
            publishHelper.StartNewLevel(subnode);
            publishHelper.StartNode(subnode);
            bool isNodeFromAssessment = subnode.parameters != null &&
                                        subnode.parameters.ContainsKey("isOpportunityAssessment");
            bool anyChildrenChecked = _utility.AreAnyChildrenChecked(subnode);
            
            switch (subnode.type)
            {
                case nameof(clsConstants.BM_Tree_Types.ChapterIntro) when subnode.@checked:
                    InsertDescriptionsForLevel1(builder, userId, childNodes, budgetYearBudMan);
                    break;
                case nameof(clsConstants.BM_Tree_Types.FPAssignmentServiceAreaLevel) when subnode.@checked:
                    _bmAssignmentHelper.InsertFPAssignmentsData(userId, builder, subnode,
                        budgetYearBudMan, 2, childNodes.id, "");
                    break;
                case nameof(clsConstants.BM_Tree_Types.StratergyTextServiceAreaLevel) when
                    subnode.@checked:
                    _bmGoalsandtarget.InsertStratergyTextCityLevel(userId, publishHelper, childNodes.id,
                        childNodes.orgLevel, "", budgetYearBudMan);
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaDescription) when subnode.@checked:
                    //Insert strategy text
                    _bmIntroHelper.CreateServiceAreaDescription(builder, userId, subnode,
                        Convert.ToString(childNodes.id), "");
                    break;
                //if (subnode.type == nameof(clsConstants.BM_Tree_Types.BudpropExplanation) && subnode.@checked)
                //{
                //    _bmIntroHelper.GetBudPropExplanation(builder, userId, subnode, Convert.ToString(childNodes.id), "");
                //}
                case nameof(clsConstants.BM_Tree_Types.StrategyAndChallenge) when subnode.@checked:
                    //Insert strategy text
                    _bmIntroHelper.CreateStrategyObjectives(builder, userId, subnode, Convert.ToString(childNodes.id),
                        "");
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceTable) when anyChildrenChecked:
                {
                    publishHelper.InsertHeading(userId, langStringValuesDocExport["BM_doc_budget_table_title"].LangText,
                        _context, true);

                    string type = "Type1";
                    
                    foreach (var item in subnode.items.Where(item => item.@checked))
                    {
                        publishHelper.StartNewLevel(item);
                        publishHelper.StartNode(item);
                        
                        switch (item.type)
                        {
                            case nameof(clsConstants.BM_Tree_Types.BudgetByAccountGroupTable):
                            {
                                _bmBudBySaHelper.InsertBudgetByAccountGroupTable(userId, builder, childNodes.id, "",
                                    processNodeInputData.cacheKey, budgetYearBudMan, budgetPhaseId, item, subnode.id,
                                    showOnlyModified, type);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        childNodes.id + subnode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.SaOpexIncGraphlvl1) when
                                publishHelper.GetType() == typeof(WebHelperBm):
                            {
                                publishHelper.StartCollapsibleList(item.text, true,
                                    _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                                publishHelper.StartCollapsibleListItem(
                                    _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                                string strAccGraphOne = _BMWebGraphs.GetAccountingGraphOneData(userId, budgetYearBudMan,
                                    Convert.ToString(childNodes.id), string.Empty, budgetPhaseId);
                                if (strAccGraphOne == string.Empty)
                                {
                                    return;
                                }

                                //builder.InsertImage(_graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true));
                                using (var retImage =
                                       _graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600,
                                           true))
                                {
                                    if (retImage != null)
                                    {
                                        var imageInfo = _utility.ConvertImagetoByte(retImage);
                                        builder.InsertImage(imageInfo);
                                    }
                                }

                                publishHelper.EndCollapsibleListItem();
                                publishHelper.EndCollapsibleList();
                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable):
                            {
                                _bmBudBySaHelper.InsertBudgetByServiceTable(userId, builder, childNodes.id, "", templateId,
                                    item, subnode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                                    processNodeInputData.cacheKey);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        childNodes.id + subnode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.BudgetByService):
                            {
                                bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                                string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan,
                                    childNodes.id, "", isWebHelper);
                                Dictionary<string, clsLanguageString> langStrings =
                                    _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                                        "BudgetManagement");

                                if (jsonStr == string.Empty)
                                {
                                    return;
                                }

                                publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                bool divideByMillions =
                                    _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                                if (divideByMillions)
                                {
                                    publishHelper.InsertText(langStrings["InfoGraphics_divedBy_million"].LangText, 8, false,
                                        true);
                                }
                                else
                                {
                                    publishHelper.InsertText(langStrings["InfoGraphics_divedBy_thousand"].LangText, 8,
                                        false, true);
                                }

                                dynamic json = JToken.Parse(jsonStr);
                                string graphOneName = "BudgetByService";
                                dynamic jsonRet = new JObject();
                                jsonRet.Add("infile", json);
                                jsonRet.infile.type = graphOneName;
                                jsonRet.infile.numformat = "";

                                String mergedData = JsonConvert.SerializeObject(jsonRet);

                                using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                {
                                    if (retImage != null)
                                    {
                                        //builder.InsertImage(retImage);
                                        var imageInfo = _utility.ConvertImagetoByte(retImage);
                                        builder.InsertImage(imageInfo);
                                    }
                                }

                                publishHelper.InsertParagraphBreak();
                                //line break between graph and table
                                if (publishHelper.GetType() != typeof(WebHelperBm))
                                {
                                    publishHelper.InsertLineBreak();
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.BudgetByDepartment):
                            {
                                bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                                Dictionary<string, clsLanguageString> langStrings =
                                    _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                                        "BudgetManagement");
                                string jsonStr = _budgetManagement.GetBudgetByDepartment(userId, budgetYearBudMan,
                                    childNodes.id, "", isWebHelper, budgetPhaseId);
                                if (jsonStr == string.Empty)
                                {
                                    return;
                                }

                                publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                isHeadingInserted = true;
                                bool divideByMillions =
                                    _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_organization_title");
                                if (divideByMillions)
                                {
                                    publishHelper.InsertText(langStrings["InfoGraphics_divedBy_million"].LangText, 8, false,
                                        true);
                                }
                                else
                                {
                                    publishHelper.InsertText(langStrings["InfoGraphics_divedBy_thousand"].LangText, 8,
                                        false, true);
                                }

                                dynamic json = JToken.Parse(jsonStr);
                                string graphOneName = "BudgetByDepartment";
                                dynamic jsonRet = new JObject();
                                jsonRet.Add("infile", json);
                                jsonRet.infile.type = graphOneName;
                                jsonRet.infile.numformat = "";

                                String mergedData = JsonConvert.SerializeObject(jsonRet);

                                using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                {
                                    if (retImage != null)
                                    {
                                        //builder.InsertImage(retImage);
                                        var imageInfo = _utility.ConvertImagetoByte(retImage);
                                        builder.InsertImage(imageInfo);
                                    }
                                }

                                publishHelper.InsertParagraphBreak();
                                //line break between graph and table
                                if (publishHelper.GetType() != typeof(WebHelperBm))
                                {
                                    publishHelper.InsertLineBreak();
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.BudgetByOrgTable):
                            {
                                _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, childNodes.id, "",
                                    templateId, item, subnode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                                    processNodeInputData.cacheKey, isHeadingInserted);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        childNodes.id + subnode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable_Chapter):
                            {
                                _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, childNodes.id, "",
                                    templateId, item, subnode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                                    processNodeInputData.cacheKey, false, true);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        childNodes.id + subnode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.BudgetByService_Chapter):
                            {
                                bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                                string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan,
                                    childNodes.id, "", isWebHelper, true);
                                Dictionary<string, clsLanguageString> langStrings33 =
                                    _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                                        "DocConfig");
                                publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                if (!string.IsNullOrEmpty(jsonStr))
                                {
                                    //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                                    bool divideByMillions =
                                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                                    if (divideByMillions)
                                    {
                                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8,
                                            false, true);
                                    }
                                    else
                                    {
                                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8,
                                            false, true);
                                    }

                                    dynamic json = JToken.Parse(jsonStr);
                                    string graphOneName = "BudgetByService_Chapter";
                                    dynamic jsonRet = new JObject();
                                    jsonRet.Add("infile", json);
                                    jsonRet.infile.type = graphOneName;
                                    jsonRet.infile.numformat = "";

                                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                    {
                                        if (retImage != null)
                                        {
                                            //builder.InsertImage(retImage);
                                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                                            builder.InsertImage(imageInfo);
                                        }
                                    }

                                    publishHelper.InsertParagraphBreak();
                                }

                                break;
                            }
                            case nameof(clsConstants.BM_Tree_Types.customNode):
                                _customNode.InsertNestedCustomNodes(builder, userId, item, budgetPhaseId, showOnlyModified,
                                    true, true);
                                break;
                        }

                        publishHelper.EndCurrentLevel();
                        publishHelper.EndNode(item);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.KostraAnalysis) when subnode.@checked:
                {
                    bool kostraTabGridPerReportingArea =
                        _utility.CheckOnKostraTableGridPerReportingArea(userId, "TRUE");

                    if (kostraTabGridPerReportingArea)
                    {
                        //Apply the logic based on connection between the org_id and the reporting area
                        //There should be one table grid per reporting area with the description for that reporting area made in KOSTRA module.
                        Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(userId);
                        Dictionary<string, string> reportingArea = _kostraData.GetReportingAreaDataForServiceIds(userId,
                            Convert.ToString(childNodes.id), orgLevelValue["FINPLAN_LEVEL_1"]);

                        foreach (var item in reportingArea)
                        {
                            _bmBudBySaHelper.InsertKostraAnalysis(userId, builder, subnode, item.Key, item.Value,
                                templateId, false, kostraTabGridPerReportingArea, true);
                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }
                    else
                    {
                        //Display one Kostra table per service area with a description based on a link between the service_id (level_1 / level_2 value) and the indicators needed in report
                        _bmBudBySaHelper.InsertKostraAnalysis(userId, builder, subnode, Convert.ToString(childNodes.id),
                            string.Empty,
                            templateId, false, kostraTabGridPerReportingArea);
                    }

                    if (subnode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, childNodes.id + subnode.id + subnode.type,
                            subnode.Uid, budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.KostraProfile) when subnode.@checked &&
                                                                           publishHelper.GetType() == typeof(WebHelperBm):
                {
                    bool kostraTabGridPerReportingArea =
                        _utility.CheckOnKostraTableGridPerReportingArea(userId, "TRUE");

                    if (kostraTabGridPerReportingArea)
                    {
                        //Apply the logic based on connection between the org_id and the reporting area
                        //There should be one table grid per reporting area with the description for that reporting area made in KOSTRA module.
                        Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(userId);
                        Dictionary<string, string> reportingArea = _kostraData.GetReportingAreaDataForServiceIds(userId,
                            Convert.ToString(childNodes.id), orgLevelValue["FINPLAN_LEVEL_1"]);

                        foreach (var item in reportingArea)
                        {
                            kostraProfileInput input = new kostraProfileInput
                            {
                                User = userId,
                                Builder = builder,
                                KNode = subnode,
                                OrgIdOrAreaCode = item.Key,
                                ServiceIdOrAreaName = item.Value,
                                TemplateId = templateId,
                                IsTenantSpecific = false,
                                IsUsingOrgRepArea = kostraTabGridPerReportingArea,
                                LoopingThroughMultipleReportingAreas = true
                            };

                            _bmBudBySaHelper.InsertKostraProfileGraph(input).GetAwaiter().GetResult();
                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }
                    else
                    {
                        kostraProfileInput input = new kostraProfileInput
                        {
                            User = userId,
                            Builder = builder,
                            KNode = subnode,
                            OrgIdOrAreaCode = Convert.ToString(childNodes.id),
                            ServiceIdOrAreaName = string.Empty,
                            TemplateId = templateId,
                            IsTenantSpecific = false,
                            IsUsingOrgRepArea = kostraTabGridPerReportingArea
                        };
                        //Display one Kostra table per service area with a description based on a link between the service_id (level_1 / level_2 value) and the indicators needed in report
                        _bmBudBySaHelper.InsertKostraProfileGraph(input).GetAwaiter().GetResult();
                    }

                    if (subnode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, childNodes.id + subnode.id + subnode.type,
                            subnode.Uid, budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.OperationBudgetServiceArea) when
                    subnode.@checked:
                {
                    //Insert CAB Grid
                    if (showOnlyModified)
                    {
                        _bmBudBySaHelper.InsertCabGridForSingleBudgetPhase(userId, builder,
                            Convert.ToString(childNodes.id), budgetPhaseId,
                            showOnlyModified, subnode, budgetYearBudMan, false);
                    }
                    else
                    {
                        var orgToGroup = _utility.GetParameterValue(userId, "CAB_ORG_GROUPING_L1");
                        if (!string.IsNullOrEmpty(orgToGroup))
                        {
                            orgToGroup = "org_lvl_1";
                        }

                        _bmBudBySaHelper.InsertCabGridNew(userId, builder, Convert.ToString(childNodes.id),
                            budgetPhaseId, showOnlyModified,
                            subnode, budgetYearBudMan, false, incIntDesc, orgtoGroup: orgToGroup);
                    }
                    //if (subnode.isEditableNode)
                    //{
                    //    _customNode.InsertNodeDescription(builder, userId, childNodes.id + subnode.id + subnode.type, subnode.Uid, budgetYearBudMan,
                    //        showOnlyModified ? budgetPhaseId : string.Empty);
                    //}
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.NewAction) when subnode.@checked:
                    try
                    {
                        //New Action Grid
                        List<PublishTreeNode> ServiceAreaAction = new List<PublishTreeNode>();
                        List<clsOrgLevelOneAndTwoDetails> filteredLevelAndLevel2nonFilteredAction;
                        List<string> level1 = new List<string>();
                        List<string> level2 = new List<string>();
                        List<clsOrgStructure> lstOrgStructure =
                            _utility.GetTenantOrgStructure(orgVersionContent, userId);
                        int budgetYear = _utility.GetActiveBudgetYear(userId,
                            nameof(clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR));
                        List<DocTreeActionHelper> nonFilteredActionWithChange =
                            _budgetManagement.NonFilteredActionNames(userId, new List<int> { 6, 9, 21, 31, 41, 7 },
                                budgetYear);
                        //budget phase
                        List<int> budgetPhaseChangeId =
                            _investment.GetChangeDataUsingBudgetPhase(budgetPhaseId, userId, showOnlyModified,
                                budgetYear);
                        List<DocTreeActionHelper> nonFilteredAction = nonFilteredActionWithChange
                            .Where(x => budgetPhaseChangeId.Contains(x.changeId)).ToList();

                        List<clsOrgLevelOneAndTwoDetails> lstLevel1AndTwoDetailsnonFilteredAction =
                            _utility.GetOrgIdsAndServiceIdsDetailsFromStorage(orgVersionContent, userId,
                                lstOrgStructure, budgetYear, nonFilteredAction, "nonFilteredAction");
                        filteredLevelAndLevel2nonFilteredAction = lstLevel1AndTwoDetailsnonFilteredAction
                            .Where(x => x.orgId == Convert.ToString(childNodes.id)).ToList();
                        foreach (var item in filteredLevelAndLevel2nonFilteredAction)
                        {
                            level1.AddRange(item.level1);
                            level2.AddRange(item.level2);
                        }

                        IEnumerable<DocTreeActionHelper> newActionNames =
                            _budgetManagement.GetActionNames2(userId, nonFilteredAction, level1, level2);
                        Dictionary<string, string> limitCodeData = _budgetManagement.GetLimitCode(newActionNames);
                        foreach (var limit in limitCodeData)
                        {
                            PublishTreeNode limitType = _budgetManagement.AddTreeNodeFromDoc(
                                Convert.ToString(limit.Key), Convert.ToString(limit.Value),
                                nameof(clsConstants.BM_Tree_Types.LimitCode), false, true);
                            foreach (var aa in newActionNames.Where(x => x.limitCode == limit.Key)
                                         .OrderByDescending(x => Math.Abs(x.Year1Amount)))
                            {
                                PublishTreeNode ActionName = _budgetManagement.AddTreeNodeFromDoc(
                                    Convert.ToString(aa.tempId), Convert.ToString(aa.Actions),
                                    nameof(clsConstants.BM_Tree_Types.Action), false, true);
                                limitType.items.Add(ActionName);
                            }

                            ServiceAreaAction.Add(limitType);
                        }

                        IEnumerable<PublishTreeNode> items = ServiceAreaAction;
                        _bmNewActionHelper.InsertActionDescriptions(budgetYearBudMan, builder,
                            userId, items, incIntDesc,
                            nameof(clsConstants.BM_Tree_Types.NewAction), budgetPhaseId, showOnlyModified,
                            Convert.ToString(childNodes.id), incBlistIntDesc, "", insertBookmark: true,
                            bookmarkTitle: subnode.text);
                    }
                    catch
                    {
                        publishHelper.InsertError("Insert NewAction Failed.");
                    }

                    break;
                case nameof(clsConstants.BM_Tree_Types.BlistActionText) when subnode.@checked:
                    try
                    {
                        List<PublishTreeNode> ServiceAreaAction = new List<PublishTreeNode>();
                        List<clsOrgStructure> lstOrgStructure =
                            _utility.GetTenantOrgStructure(orgVersionContent, userId);
                        int budgetYear = _utility.GetActiveBudgetYear(userId,
                            nameof(clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR));
                        List<DocTreeActionHelper> nonFilteredBlist =
                            _budgetManagement.NonFilteredBList(userId, new List<int> { 31, 41, 9, 21 }, budgetYear);
                        List<clsOrgLevelOneAndTwoDetails> lstLevel1AndTwoDetailsnonFilteredBlist =
                            _utility.GetOrgIdsAndServiceIdsDetailsFromStorage(orgVersionContent, userId,
                                lstOrgStructure, budgetYear, nonFilteredBlist, "nonFilteredBlist");
                        var filteredLevelAndLevel2nonFilteredBlist = lstLevel1AndTwoDetailsnonFilteredBlist
                            .Where(x => x.orgId == Convert.ToString(childNodes.id)).ToList();

                        Dictionary<int, string> ActionType = new Dictionary<int, string>();
                        ActionType.Add(31,
                            langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_FinPlan_CR").Value.LangText);
                        ActionType.Add(41,
                            langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_FinPlan_NP").Value.LangText);
                        ActionType.Add(9,
                            langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_FinPlan_ActionType9").Value
                                .LangText);
                        ActionType.Add(21,
                            langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_BList_21").Value.LangText);

                        foreach (var a in ActionType)
                        {
                            PublishTreeNode ActionTypes = _budgetManagement.AddTreeNodeFromDoc(Convert.ToString(a.Key),
                                Convert.ToString(a.Value),
                                nameof(clsConstants.BM_Tree_Types.ActionType), false, true);
                            dynamic ActionNames = _budgetManagement.GetBListActionsForDocTree(userId, a.Key,
                                Convert.ToString(childNodes.id), null, nonFilteredBlist, budgetYear);
                            foreach (var aa in ActionNames)
                            {
                                PublishTreeNode ActionName = _budgetManagement.AddTreeNodeFromDoc(
                                    Convert.ToString(aa.tempId), Convert.ToString(aa.Actions),
                                    nameof(clsConstants.BM_Tree_Types.Action), false, true);
                                ActionTypes.items.Add(ActionName);
                            }

                            ServiceAreaAction.Add(ActionTypes);
                        }

                        IEnumerable<PublishTreeNode> items = ServiceAreaAction;
                        _bmNewActionHelper.InsertBListDeletedActionSetTable(budgetYearBudMan, builder, userId, items,
                            incBlistIntDesc, nameof(clsConstants.BM_Tree_Types.BlistActionText),
                            Convert.ToString(childNodes.id), "NewBlistActon_text", "", levelSetUp);
                        if (publishHelper.GetType() != typeof(WebHelperBm))
                        {
                            _bmNewActionHelper.InsertActionDescriptions(budgetYearBudMan, builder, userId, items,
                                incIntDesc, nameof(clsConstants.BM_Tree_Types.BlistActionText), budgetPhaseId,
                                showOnlyModified, Convert.ToString(childNodes.id), incBlistIntDesc, "",
                                insertBookmark: true, bookmarkTitle: subnode.text, levelSetUp: levelSetUp);
                        }
                    }
                    catch
                    {
                        publishHelper.InsertError("Insert BlistActionText Failed.");
                    }

                    break;
                case nameof(clsConstants.BM_Tree_Types.BudgetServiceArea) when subnode.@checked:
                    _bmStatusHelper.InsertBudgetDescription(builder, userId, 2, Convert.ToString(childNodes.id), "");
                    break;
                case nameof(clsConstants.BM_Tree_Types.Investments):
                case nameof(clsConstants.BM_Tree_Types.InvestmentsStatus):
                {
                    IEnumerable<PublishTreeNode> items = null;
                    bool isBlistInvestment = subnode.type == nameof(clsConstants.BM_Tree_Types.InvestmentsStatus);
                    if (subnode.items.Any())
                    {
                        items = subnode.items;
                        _bmInvestHelper.InsertInvestmentTables(builder, userId, Convert.ToString(childNodes.id), items,
                            budgetPhaseId, showOnlyModified, subnode, budgetYearBudMan, incIntDesc, false,
                            isBlistInvestment, incBlistIntDesc);
                    }

                    break;
                }
                //indicator section
                case nameof(clsConstants.BM_Tree_Types.ActivityIndicatorLvl1) when subnode.@checked:
                    _bmActivityIndicator.InsertIndicatorData(userId, builder, Convert.ToString(childNodes.id),
                        string.Empty,
                        budgetYearBudMan, subnode, budgetPhaseId, showOnlyModified, subnode.orgLevel);
                    break;
                case nameof(clsConstants.BM_Tree_Types.GoalsLvl1) when subnode.@checked:
                {
                    goalSectionSelected = true;
                    var targetNode = childNodes.items.FirstOrDefault(x =>
                        x.type.Equals(nameof(clsConstants.BM_Tree_Types.TargetLvl1),
                            StringComparison.InvariantCultureIgnoreCase));

                    _bmGoalsandtarget.InsertGoalsSection(userId, builder, Convert.ToString(childNodes.id), string.Empty,
                        budgetYearBudMan,
                        subnode, targetNode, true, showOnlyModified, budgetPhaseId, true);
                    insertTarget = true;
                    tgtSectionSelected = true;
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.TargetLvl1) when subnode.@checked &&
                                                                        insertTarget == false:
                    tgtSectionSelected = true;
                    _bmGoalsandtarget.InsertTargetSection(userId, builder, Convert.ToString(childNodes.id),
                        string.Empty, budgetYearBudMan, subnode, null, true,
                        showOnlyModified, budgetPhaseId, goalSectionSelected);
                    break;
                case nameof(clsConstants.BM_Tree_Types.customNode) when
                    (subnode.@checked || _utility.AreAnyChildrenChecked(subnode)):
                    _customNode.InsertNestedCustomNodes(builder, userId, subnode, budgetPhaseId, showOnlyModified, true,
                        true); //We want the top level heading to be a bookmark
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaKeyFigures) when subnode.@checked:
                {
                    var saKeyFigures = _budgetManagement.FormatServiceAreaKeyFigures(userId,
                            publishHelper.BaseStoragePath, budgetYearBudMan, childNodes.id, string.Empty).GetAwaiter()
                        .GetResult();
                    publishHelper.InsertDataIntoBlob(saKeyFigures, publishHelper.GetCurrentChapterPath(), "keyfigures",
                        PubContentHandler.BlobType.Json);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) when subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvInFpYears) when subnode.@checked && !nodeProcessed:
                {
                    var uidPctTotalGrossExpenses =
                        childNodes.items.FirstOrDefault(x => x.type == "PctTotalGrossExpenses") == null
                            ? ""
                            : childNodes.items.FirstOrDefault(x => x.type == "PctTotalGrossExpenses").Uid;
                    var uidInvInFpYears = childNodes.items.FirstOrDefault(x => x.type == "InvInFpYears") == null
                        ? ""
                        : childNodes.items.FirstOrDefault(x => x.type == "InvInFpYears").Uid;

                    nodeProcessed = true;
                    bool casePublish = false;
                    //data fetch
                    string jsonStr = _budgetManagement.GetPctTotalGrossExpenses(userId, budgetYearBudMan,
                        childNodes.id, string.Empty, childNodes.text, string.Empty);
                    string jsonStrGrp = _bmInvestHelper.GetInvInFpYears(userId,
                        new List<int>()
                            { budgetYearBudMan, budgetYearBudMan + 1, budgetYearBudMan + 2, budgetYearBudMan + 3 },
                        childNodes.id, string.Empty, budgetPhaseId, showOnlyModified);
                    var divideByMillions =
                        _utility.GetParameterValue(userId, "BM_FINPLAN_INFOGRAPHICS_DIVIDE_BY_MILL");
                    string expLang = _utility.GetUserDetails(userId).language_preference;
                    var _languageStringsBM = _utility.GetLanguageStrings(expLang, userId, "BudgetManagement");
                    // side by side graph only for Web
                    if (publishHelper.GetType() == typeof(WebHelperBm) &&
                        subnode.type == nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) &&
                        subnode.@checked &&
                        childNodes.items.FirstOrDefault(x =>
                            x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)) != null && childNodes.items
                            .FirstOrDefault(x => x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears))
                            .@checked)
                    {
                        if (jsonStrGrp != string.Empty && jsonStr != string.Empty)
                        {
                            publishHelper.InsertLineBreak();
                            publishHelper.InsertLineBreak();
                            publishHelper.GenerateGraphSideBySide(jsonStr, jsonStrGrp,
                                _languageStringsBM["PctTotalGrossExpenses_text"].LangText,
                                _languageStringsBM["InvInFpYears_text"].LangText,
                                !string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true"
                                    ? _languageStringsBM["InfoGraphics_divedBy_million"].LangText
                                    : _languageStringsBM["InfoGraphics_divedBy_thousand"].LangText,
                                uidPctTotalGrossExpenses, uidInvInFpYears);
                            publishHelper.InsertLineBreak();
                            publishHelper.InsertLineBreak();
                            casePublish = true;
                        }
                    }

                    if (!casePublish)
                    {
                        if (jsonStr != string.Empty &&
                            subnode.type == nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) &&
                            subnode.@checked)
                        {
                            _bmTenantSpecificSA.GeneratePctTotalGrossExpensesOrInvInFpYearsGrh(userId,
                                publishHelper, builder, jsonStr, string.Empty, "PctTotalGrossExpenses", null);
                        }

                        if (jsonStrGrp != string.Empty &&
                            childNodes.items.FirstOrDefault(x =>
                                x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)) != null && childNodes
                                .items.FirstOrDefault(x =>
                                    x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)).@checked)
                        {
                            _bmTenantSpecificSA.GeneratePctTotalGrossExpensesOrInvInFpYearsGrh(userId,
                                publishHelper, builder, jsonStrGrp, string.Empty, "InvInFpYears", divideByMillions);
                        }
                    }
                    
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.SaOpexIncGraphlvl1) when subnode.@checked &&
                    publishHelper.GetType() == typeof(WebHelperBm):
                {
                    publishHelper.StartCollapsibleList(subnode.text, true,
                        _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                    publishHelper.StartCollapsibleListItem(_languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"]
                        .LangText);
                    string strAccGraphOne = _BMWebGraphs.GetAccountingGraphOneData(userId, budgetYearBudMan,
                        Convert.ToString(childNodes.id), string.Empty, budgetPhaseId);
                    if (strAccGraphOne == string.Empty)
                    {
                        return;
                    }

                    //builder.InsertImage(_graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true));
                    using (var retImage =
                           _graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true))
                    {
                        if (retImage != null)
                        {
                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                            builder.InsertImage(imageInfo);
                        }
                    }

                    publishHelper.EndCollapsibleListItem();
                    publishHelper.EndCollapsibleList();
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetProposalStrategySA) when subnode.@checked:
                    _bmOppHelper.InsertBudgetProposalStrategyData(userId, childNodes.id, string.Empty, budgetYearBudMan,
                        publishHelper, subnode);
                    break;
                case nameof(clsConstants.BM_Tree_Types.BM_ManYears) when subnode.@checked:
                    //Enter Table Data
                    _bmBudBySaHelper.InsertBMManYearsTable(userId, budgetYearBudMan, builder, subnode, true,
                        childNodes.id);
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaLevelCabOverview) when subnode.@checked:
                    //Enter Table Data
                    _bmBudBySaHelper.InsertCabGridOnTenantLevel(userId, builder, childNodes.id, budgetPhaseId,
                        showOnlyModified, null);
                    break;
                case nameof(clsConstants.BM_Tree_Types.reportingWidget) when subnode.@checked:
                {
                    if (subnode.parameters != null && subnode.parameters.ContainsKey("masterNodeId"))
                    {
                        _bmNewActionHelper.InsertReportingWidget(userId, subnode.text, _context, builder,
                            budgetYearBudMan
                            , subnode.parameters.GetValueOrDefault("masterNodeId"),
                            subnode.id);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByAccountGroupTable) when subnode.@checked:
                {
                    string type = "Type1";
                    _bmBudBySaHelper.InsertBudgetByAccountGroupTable(userId, builder, childNodes.id, "",
                        processNodeInputData.cacheKey, budgetYearBudMan, budgetPhaseId,
                        subnode, subnode.id, showOnlyModified, type);
                    if (subnode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            childNodes.id + subnode.id + subnode.id + subnode.type, subnode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable) when subnode.@checked:
                {
                    string type = "Type1";
                    _bmBudBySaHelper.InsertBudgetByServiceTable(userId, builder, childNodes.id, "", templateId, subnode,
                        subnode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                        processNodeInputData.cacheKey);
                    if (subnode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            childNodes.id + subnode.id + subnode.id + subnode.type, subnode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByService) when subnode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr =
                        _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, childNodes.id, "", isWebHelper);
                    Dictionary<string, clsLanguageString> langStrings =
                        _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                            "BudgetManagement");

                    if (jsonStr == string.Empty)
                    {
                        return;
                    }

                    publishHelper.InsertHeading(userId, subnode.text.Trim(), _context, true);
                    bool divideByMillions =
                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                    if (divideByMillions)
                    {
                        publishHelper.InsertText(langStrings["InfoGraphics_divedBy_million"].LangText, 8, false, true);
                    }
                    else
                    {
                        publishHelper.InsertText(langStrings["InfoGraphics_divedBy_thousand"].LangText, 8, false, true);
                    }

                    dynamic json = JToken.Parse(jsonStr);
                    string graphOneName = "BudgetByService";
                    dynamic jsonRet = new JObject();
                    jsonRet.Add("infile", json);
                    jsonRet.infile.type = graphOneName;
                    jsonRet.infile.numformat = "";

                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                    {
                        if (retImage != null)
                        {
                            //builder.InsertImage(retImage);
                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                            builder.InsertImage(imageInfo);
                        }
                    }

                    publishHelper.InsertParagraphBreak();
                    //line break between graph and table
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        publishHelper.InsertLineBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByDepartment) when subnode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    Dictionary<string, clsLanguageString> langStrings =
                        _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                            "BudgetManagement");
                    string jsonStr = _budgetManagement.GetBudgetByDepartment(userId, budgetYearBudMan, childNodes.id,
                        "", isWebHelper, budgetPhaseId);
                    if (jsonStr == string.Empty)
                    {
                        return;
                    }

                    publishHelper.InsertHeading(userId, subnode.text.Trim(), _context, true);
                    isHeadingInserted = true;
                    bool divideByMillions =
                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_organization_title");
                    if (divideByMillions)
                    {
                        publishHelper.InsertText(langStrings["InfoGraphics_divedBy_million"].LangText, 8, false, true);
                    }
                    else
                    {
                        publishHelper.InsertText(langStrings["InfoGraphics_divedBy_thousand"].LangText, 8, false, true);
                    }

                    dynamic json = JToken.Parse(jsonStr);
                    string graphOneName = "BudgetByDepartment";
                    dynamic jsonRet = new JObject();
                    jsonRet.Add("infile", json);
                    jsonRet.infile.type = graphOneName;
                    jsonRet.infile.numformat = "";

                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                    {
                        if (retImage != null)
                        {
                            //builder.InsertImage(retImage);
                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                            builder.InsertImage(imageInfo);
                        }
                    }

                    publishHelper.InsertParagraphBreak();
                    //line break between graph and table
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        publishHelper.InsertLineBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByOrgTable) when subnode.@checked:
                {
                    string type = "Type1";
                    _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, childNodes.id, "", templateId,
                        subnode, subnode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                        processNodeInputData.cacheKey, isHeadingInserted);
                    if (subnode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            childNodes.id + subnode.id + subnode.id + subnode.type, subnode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable_Chapter) when subnode.@checked:
                {
                    string type = "Type1";
                    _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, childNodes.id, "", templateId,
                        subnode, subnode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                        processNodeInputData.cacheKey, false, true);
                    if (subnode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            childNodes.id + subnode.id + subnode.id + subnode.type, subnode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByService_Chapter) when subnode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, childNodes.id, "",
                        isWebHelper, true);
                    Dictionary<string, clsLanguageString> langStrings33 =
                        _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                            "DocConfig");
                    publishHelper.InsertHeading(userId, subnode.text.Trim(), _context, true);
                    if (!string.IsNullOrEmpty(jsonStr))
                    {
                        //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                        bool divideByMillions =
                            _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                        if (divideByMillions)
                        {
                            publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8, false,
                                true);
                        }
                        else
                        {
                            publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false,
                                true);
                        }

                        dynamic json = JToken.Parse(jsonStr);
                        string graphOneName = "BudgetByService_Chapter";
                        dynamic jsonRet = new JObject();
                        jsonRet.Add("infile", json);
                        jsonRet.infile.type = graphOneName;
                        jsonRet.infile.numformat = "";

                        String mergedData = JsonConvert.SerializeObject(jsonRet);

                        using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                        {
                            if (retImage != null)
                            {
                                //builder.InsertImage(retImage);
                                var imageInfo = _utility.ConvertImagetoByte(retImage);
                                builder.InsertImage(imageInfo);
                            }
                        }

                        publishHelper.InsertParagraphBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl2) when subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) when
                    subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl2) when subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl1) when
                    subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl1) when
                    subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl1) when subnode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl1) when subnode.@checked:
                {
                    bool isBlistInvestment =
                        subnode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) ||
                        subnode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl2) ||
                        subnode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl1) ||
                        subnode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl1);
                    _bmInvestHelper.InsertInvestmentTablesFlat(builder, userId, Convert.ToString(childNodes.id),
                        budgetPhaseId, showOnlyModified, subnode, budgetYearBudMan, incIntDesc, false,
                        isBlistInvestment, incBlistIntDesc);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetPerFunctAreaAndAccntGrpForOrg) when
                    subnode.@checked:
                    _FinplanYearlyBudgetSummaryHelper.Initialize(userId, builder, budgetYearBudMan, subnode)
                        .GetAwaiter().GetResult();
                    _FinplanYearlyBudgetSummaryHelper.InsertFpBudgetbyFunctionalArea(orgVersionContent, childNodes.id)
                        .GetAwaiter().GetResult();
                    break;
                default:
                {
                    if (subnode is { type: nameof(clsConstants.BM_Tree_Types.TargetPlanTextLvl1), @checked: true } ||
                        tgtSectionSelected)
                    {
                        if (publishHelper.GetType() != typeof(WebHelperBm))
                        {
                            _bmGoalsandtarget.InsertPlanTargetText(userId, builder, Convert.ToString(childNodes.id),
                                string.Empty, budgetYearBudMan, subnode, null,
                                showOnlyModified, budgetPhaseId, goalSectionSelected, tgtSectionSelected);
                        }

                        tgtSectionSelected = false;
                    }

                    else if (subnode.type == nameof(clsConstants.BM_Tree_Types.BPStrategies) && anyChildrenChecked ||
                        isNodeFromAssessment)
                    {
                        if (isNodeFromAssessment)
                        {
                            _bmOppHelper.InsertStrategyDataForBudgetProposalDocument(userId, budgetYearBudMan,
                                publishHelper, subnode.items, true);
                        }
                        else
                        {
                            publishHelper.InsertHeading(userId, subnode.text, _context, false);
                            _bmOppHelper.InsertStrategyDataForBudgetProposalDocument(userId, budgetYearBudMan,
                                publishHelper, subnode.items);
                        }
                    }

                    break;
                }
            }

            publishHelper.EndCurrentLevel();

            //cab decription section
            publishHelper.EndNode(subnode);
        }
    }


    private bool ProcessChildNodesForMultiLevel(string userId, DocumentBuilder builder,
        PublishTreeNode leveloneData, PublishTreeNode cNode,
        bool isHeaderRequired, bool isSAHeadingRequired, bool isPageBreak, dynamic retVal, dynamic retValBudpropExp,
        string templateId, bool incIntDesc,
        string budgetPhaseId, bool showOnlyModified, int budgetYearBudMan, List<PublishTreeNode> levelOne,
        bool goalsSectionSelected, bool nodeProcessed, bool incBlistIntDesc, Guid cacheKey)
    {
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
        try
        {
            var orgVersionContent =
                _utility.GetOrgVersionSpecificContent(userId, _utility.GetForecastPeriod(budgetYearBudMan, 1));
            bool insertTarget = false;
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringValuesDocExport =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
            Dictionary<string, clsLanguageString> langStrings33 =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                    "BudgetManagement");
            
            switch (cNode.type)
            {
                case nameof(clsConstants.BM_Tree_Types.ChapterIntro) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    Dictionary<string, clsLanguageString> langStrings =
                        _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                            "docexport");

                    if (retVal.descBPSummarySA.description != "" && retVal.descBPSummarySA.description != null)
                    {
                        publishHelper.InsertHeading(userId,
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("ChapterIntro_title", StringComparison.InvariantCultureIgnoreCase))
                                .Value
                                .LangText,
                            _context);

                        publishHelper.InsertDescriptionCk5(Convert.ToString(retVal.descBPSummarySA.description), userId,
                            0);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.FPAssignmentServiceAreaLevel) when cNode.@checked:
                    _bmAssignmentHelper.InsertFPAssignmentsData(userId, builder, cNode,
                        budgetYearBudMan, 2, leveloneData.id, "");
                    break;
                case nameof(clsConstants.BM_Tree_Types.StratergyTextServiceAreaLevel) when cNode.@checked:
                    _bmGoalsandtarget.InsertStratergyTextCityLevel(userId, publishHelper, leveloneData.id,
                        leveloneData.orgLevel, "", budgetYearBudMan);
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceTable):
                {
                    //var subnodeSelectCount = cNode.items.Count(x => x.@checked);
                    //int childcount = 0;
                    ////foreach (var a in cNode.items)
                    //{
                    //    var subnodeSelectCountchild = a.items.Count(x => x.@checked);
                    //    if (subnodeSelectCountchild > 0)
                    //    {
                    //        childcount++;
                    //    }
                    //}
                    if (_utility.AreAnyChildrenChecked(cNode))
                    {
                        InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);

                        if (InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData))
                        {
                            bool anyChildrenChecked = _utility.AreAnyChildrenChecked(cNode);
                            if (anyChildrenChecked)
                            {
                                isPageBreak = true;
                            }
                        }

                        publishHelper.InsertHeading(userId,
                            _languageStringsExportCommon["BM_doc_budget_table_title"].LangText, _context, true);

                        string type = "Type1";
                        foreach (var item in cNode.items)
                        {
                            publishHelper.StartNewLevel(item);
                            publishHelper.StartNode(item);
                            if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByAccountGroupTable) &&
                                item.@checked)
                            {
                                _bmBudBySaHelper.InsertBudgetByAccountGroupTable(userId, builder, leveloneData.id,
                                    "All", cacheKey, budgetYearBudMan, budgetPhaseId, item, cNode.id, showOnlyModified,
                                    type);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        leveloneData.id + cNode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }
                            }

                            if (item.type == nameof(clsConstants.BM_Tree_Types.SaOpexIncGraphlvl1) && item.@checked &&
                                publishHelper.GetType() == typeof(WebHelperBm))
                            {
                                publishHelper.StartCollapsibleList(item.text, true,
                                    _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                                publishHelper.StartCollapsibleListItem(
                                    _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                                string strAccGraphOne = _BMWebGraphs.GetAccountingGraphOneData(userId, budgetYearBudMan,
                                    Convert.ToString(leveloneData.id), string.Empty, budgetPhaseId);
                                if (!string.IsNullOrEmpty(strAccGraphOne))
                                {
                                    //builder.InsertImage(_graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true));
                                    using (var retImage =
                                           _graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne,
                                               600, true))
                                    {
                                        if (retImage != null)
                                        {
                                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                                            builder.InsertImage(imageInfo);
                                        }
                                    }
                                }

                                publishHelper.EndCollapsibleListItem();
                                publishHelper.EndCollapsibleList();
                            }
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable) &&
                                     item.@checked)
                            {
                                _bmBudBySaHelper.InsertBudgetByServiceTable(userId, builder, leveloneData.id, "All",
                                    templateId, item, cNode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, type,
                                    cacheKey);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        leveloneData.id + cNode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }
                            }

                            //fetch graph data(budget by service)
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByService) && item.@checked)
                            {
                                bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                                string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan,
                                    leveloneData.id, "All", isWebHelper);
                                publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                if (!string.IsNullOrEmpty(jsonStr))
                                {
                                    //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                                    bool divideByMillions =
                                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                                    if (divideByMillions)
                                    {
                                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText,
                                            8, false, true);
                                    }
                                    else
                                    {
                                        publishHelper.InsertText(
                                            langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false, true);
                                    }

                                    dynamic json = JToken.Parse(jsonStr);
                                    string graphOneName = "BudgetByService";
                                    dynamic jsonRet = new JObject();
                                    jsonRet.Add("infile", json);
                                    jsonRet.infile.type = graphOneName;
                                    jsonRet.infile.numformat = "";

                                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                    {
                                        if (retImage != null)
                                        {
                                            //builder.InsertImage(retImage);
                                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                                            builder.InsertImage(imageInfo);
                                        }
                                    }

                                    publishHelper.InsertParagraphBreak();
                                }
                            }

                            //fetch graph data(budget by Department)
                            if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByDepartment) && item.@checked)
                            {
                                publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                bool divideByMillions = _utility.CheckOnDivideByMillions(userId,
                                    "Million_BM_doc_budget_organization_title");
                                if (divideByMillions)
                                {
                                    publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8,
                                        false, true);
                                }
                                else
                                {
                                    publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8,
                                        false, true);
                                }

                                bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                                string jsonStr = _budgetManagement.GetBudgetByDepartment(userId, budgetYearBudMan,
                                    leveloneData.id, "All", isWebHelper, budgetPhaseId);
                                if (!string.IsNullOrEmpty(jsonStr))
                                {
                                    dynamic json = JToken.Parse(jsonStr);
                                    string graphOneName = "BudgetByDepartment";
                                    dynamic jsonRet = new JObject();
                                    jsonRet.Add("infile", json);
                                    jsonRet.infile.type = graphOneName;
                                    jsonRet.infile.numformat = "";

                                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                    {
                                        if (retImage != null)
                                        {
                                            //builder.InsertImage(retImage);
                                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                                            builder.InsertImage(imageInfo);
                                        }
                                    }

                                    publishHelper.InsertParagraphBreak();
                                }
                            }
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByOrgTable) && item.@checked)
                            {
                                //publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, leveloneData.id,
                                    "All", templateId, item, cNode.id, budgetYearBudMan, showOnlyModified,
                                    budgetPhaseId, type, cacheKey, false);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        leveloneData.id + cNode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }
                            }
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.customNode) && item.@checked)
                            {
                                _customNode.InsertNestedCustomNodes(builder, userId, item, budgetPhaseId,
                                    showOnlyModified, true, true);
                            }
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.customNode) && item.@checked)
                            {
                                //if (isServiceTableHeader)
                                //{
                                //    publishHelper.InsertHeading(userId, _languageStringsExportCommon["BM_doc_budget_table_title"].LangText, _context);

                                //    isServiceTableHeader = false;
                                //}
                                InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                                _customNode.InsertNestedCustomNodes(builder, userId, item, budgetPhaseId,
                                    showOnlyModified, true, true);
                            }
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable_Chapter) &&
                                     item.@checked)
                            {
                                _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, leveloneData.id,
                                    "All", templateId, item, cNode.id, budgetYearBudMan, showOnlyModified,
                                    budgetPhaseId, type, cacheKey, false, true);
                                if (item.isEditableNode)
                                {
                                    _customNode.InsertNodeDescription(builder, userId,
                                        leveloneData.id + cNode.id + item.id + item.type, item.Uid, budgetYearBudMan,
                                        showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                                }
                            }
                            else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByService_Chapter) &&
                                     item.@checked)
                            {
                                bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                                string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan,
                                    leveloneData.id, "All", isWebHelper, true);
                                publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                                if (!string.IsNullOrEmpty(jsonStr))
                                {
                                    //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                                    bool divideByMillions =
                                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                                    if (divideByMillions)
                                    {
                                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText,
                                            8, false, true);
                                    }
                                    else
                                    {
                                        publishHelper.InsertText(
                                            langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false, true);
                                    }

                                    dynamic json = JToken.Parse(jsonStr);
                                    string graphOneName = "BudgetByService_Chapter";
                                    dynamic jsonRet = new JObject();
                                    jsonRet.Add("infile", json);
                                    jsonRet.infile.type = graphOneName;
                                    jsonRet.infile.numformat = "";

                                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                    {
                                        if (retImage != null)
                                        {
                                            //builder.InsertImage(retImage);
                                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                                            builder.InsertImage(imageInfo);
                                        }
                                    }

                                    publishHelper.InsertParagraphBreak();
                                }
                            }

                            publishHelper.EndCurrentLevel();
                            publishHelper.EndNode(item);
                        }
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaDescription) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);

                    if (InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData))
                    {
                        InsertDescriptionsForLevel1(builder, userId, leveloneData, budgetYearBudMan);
                    }

                    if (retVal.descBPCA.description != "" && retVal.descBPCA.description != null)
                    {
                        publishHelper.InsertHeading(userId,
                            _languageStringsExportCommon["doc_bm_SA_Description"].LangText, _context,
                            true, cNode.text);

                        if (retVal.descBPCA.abstractText != "" && retVal.descBPCA.abstractText != null)
                        {
                            //Color color = Color.Black;
                            //builder.ParagraphFormat.ClearFormatting();
                            //_docStyle.FormatFont(builder, false, color.ToArgb(), "Calibri", 0, true, 11);
                            //builder.Write(retVal.descBPCA["abstractText"].Value);
                            //builder.ParagraphFormat.ClearFormatting();
                            publishHelper.InsertDescriptionCk5(Convert.ToString(retVal.descBPCA.abstractText), userId,
                                true, true);
                        }

                        publishHelper.InsertDescriptionCk5(Convert.ToString(retVal.descBPCA.description), userId, 0);
                    }

                    break;
                }
                //else if (cNode.type == nameof(clsConstants.BM_Tree_Types.BudpropExplanation) && cNode.@checked)
                //{
                //    if (retValBudpropExp.userComments != "" && retValBudpropExp.userComments != null)
                //    {
                //        publishHelper.InsertHeading(userId, _languageStringsExportCommon["doc_bm_SA_Description_budprop_exp"].LangText, _context,
                //            true, cNode.text);
                //        publishHelper.InsertDescriptionCk5(Convert.ToString(retValBudpropExp.userComments), userId, 0);
                //    }
                //}
                case nameof(clsConstants.BM_Tree_Types.KostraAnalysis) when cNode.@checked:
                {
                    bool kostraTabGridPerReportingArea =
                        _utility.CheckOnKostraTableGridPerReportingArea(userId, "TRUE");
                
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    string descHeader = string.Empty;
                    var langStringValues =
                        _utility.GetLanguageStrings(_utility.GetUserDetails(userId).language_preference, userId,
                            "BudgetProposal");

                    if (kostraTabGridPerReportingArea)
                    {
                        //Apply the logic based on connection between the org_id and the reporting area
                        //There should be one table grid per reporting area with the description for that report area made in KOSTRA module.
                        Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(userId);
                        Dictionary<string, string> reportingArea = _kostraData.GetReportingAreaDataForServiceIds(userId,
                            Convert.ToString(leveloneData.id), orgLevelValue["FINPLAN_LEVEL_1"]);

                        bool checkForFirstKostraBookmark = false;

                        bool multipleReportingAreas = reportingArea.Count > 1;
                        foreach (var item in reportingArea)
                        {
                            descHeader = langStringValues["BP_KostraDescHeader"].LangText + item.Value;

                            bool hasSectionId = _bmBudBySaHelper.InsertKostraAnalysis(userId, builder, cNode, item.Key,
                                item.Value,
                                templateId, true, kostraTabGridPerReportingArea,
                                loopingThroughMultipleReportingAreas: multipleReportingAreas,
                                descriptionHeader: descHeader);
                            if (hasSectionId && checkForFirstKostraBookmark == false)
                            {
                                checkForFirstKostraBookmark = true;
                            }

                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }
                    else
                    {
                        List<clsOrgIdsAndServiceIds> orgAndServiceIds =
                            _cab.GetOrgIdsAndServiceIds(userId, false, budgetYearBudMan);

                        var areaName = orgAndServiceIds.FirstOrDefault(x => x.orgId == leveloneData.id)?.orgName;

                        if (areaName != null)
                        {
                            descHeader = langStringValues["BP_KostraDescHeader"].LangText + areaName;
                        }

                        //Display one Kostra table per service area with a description based on a link between the service_id (level_1 / level_2 value) and the indicators needed in report
                        _bmBudBySaHelper.InsertKostraAnalysis(userId, builder, cNode, leveloneData.id, "All",
                            templateId,
                            true, kostraTabGridPerReportingArea);
                    }

                    if (cNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, leveloneData.id + cNode.id + cNode.type,
                            cNode.Uid, budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.KostraProfile) when cNode.@checked &&
                                                                           publishHelper.GetType() == typeof(WebHelperBm):
                {
                    bool kostraTabGridPerReportingArea =
                        _utility.CheckOnKostraTableGridPerReportingArea(userId, "TRUE");

                    if (kostraTabGridPerReportingArea)
                    {
                        //Apply the logic based on connection between the org_id and the reporting area
                        //There should be one table grid per reporting area with the description for that report area made in KOSTRA module.
                        Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(userId);
                        Dictionary<string, string> reportingArea = _kostraData.GetReportingAreaDataForServiceIds(userId,
                            Convert.ToString(leveloneData.id), orgLevelValue["FINPLAN_LEVEL_1"]);

                        bool checkForFirstKostraBookmark = false;

                        bool multipleReportingAreas = reportingArea.Count > 1;
                        foreach (var item in reportingArea)
                        {
                            kostraProfileInput input = new kostraProfileInput
                            {
                                User = userId,
                                Builder = builder,
                                KNode = cNode,
                                OrgIdOrAreaCode = item.Key,
                                ServiceIdOrAreaName = item.Value,
                                TemplateId = templateId,
                                IsTenantSpecific = true,
                                IsUsingOrgRepArea = kostraTabGridPerReportingArea,
                                LoopingThroughMultipleReportingAreas = multipleReportingAreas
                            };

                            bool hasSectionId = _bmBudBySaHelper.InsertKostraProfileGraph(input).GetAwaiter()
                                .GetResult();
                            if (hasSectionId && checkForFirstKostraBookmark == false)
                            {
                                checkForFirstKostraBookmark = true;
                            }

                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }
                    else
                    {
                        kostraProfileInput input = new kostraProfileInput
                        {
                            User = userId,
                            Builder = builder,
                            KNode = cNode,
                            OrgIdOrAreaCode = leveloneData.id,
                            ServiceIdOrAreaName = "All",
                            TemplateId = templateId,
                            IsTenantSpecific = true,
                            IsUsingOrgRepArea = kostraTabGridPerReportingArea
                        };
                        //Display one Kostra table per service area with a description based on a link between the service_id (level_1 / level_2 value) and the indicators needed in report
                        _bmBudBySaHelper.InsertKostraProfileGraph(input).GetAwaiter().GetResult();
                    }

                    if (cNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, leveloneData.id + cNode.id + cNode.type,
                            cNode.Uid, budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.StrategyAndChallenge) when cNode.@checked:
                {
                    if (retVal.descBPSC.description != "" && retVal.descBPSC.description != null)
                    {
                        InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                        InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                        publishHelper.InsertHeading(userId,
                            _languageStringsExportCommon["doc_bm_strategy_objective"].LangText, _context,
                            true, cNode.text);

                        if (retVal.descBPSC.abstractText != null && retVal.descBPSC.abstractText != "")
                        {
                            Color color = Color.Black;
                            builder.ParagraphFormat.ClearFormatting();
                            _docStyle.FormatFont(builder, false, color.ToArgb(), "Calibri", 0, true, 11);
                            builder.Write(retVal.descBPSC["abstractText"].Value);
                            builder.ParagraphFormat.ClearFormatting();
                        }

                        publishHelper.InsertDescriptionCk5(Convert.ToString(retVal.descBPSC.description), userId, 0);
                    }

                    break;
                }
                //Activity Indicator
                case nameof(clsConstants.BM_Tree_Types.ActivityIndicatorLvl1) when cNode.@checked:
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    _bmActivityIndicator.InsertIndicatorData(userId, builder, Convert.ToString(leveloneData.id), "ALL",
                        budgetYearBudMan, cNode, budgetPhaseId, showOnlyModified, leveloneData.orgLevel);
                    insertTarget = true;
                    break;
                //goal
                case nameof(clsConstants.BM_Tree_Types.GoalsLvl1) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    var targetNode = levelOne.FirstOrDefault(x =>
                        x.type.Equals(nameof(clsConstants.BM_Tree_Types.TargetLvl1),
                            StringComparison.InvariantCultureIgnoreCase));

                    _bmGoalsandtarget.InsertGoalsSection(userId, builder, Convert.ToString(leveloneData.id), "ALL",
                        budgetYearBudMan, cNode, targetNode, true,
                        showOnlyModified, budgetPhaseId, true);
                    goalsSectionSelected = true;
                    insertTarget = true;
                    targetSectionLvl1Selected = true;
                    break;
                }
                //target
                case nameof(clsConstants.BM_Tree_Types.TargetLvl1) when cNode.@checked &&
                                                                        insertTarget == false:
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    _bmGoalsandtarget.InsertTargetSection(userId, builder, Convert.ToString(leveloneData.id), "ALL",
                        budgetYearBudMan, cNode, null, true, showOnlyModified, budgetPhaseId, goalsSectionSelected);
                    targetSectionLvl1Selected = true;
                    break;
                case nameof(clsConstants.BM_Tree_Types.TargetPlanTextLvl1) when cNode.@checked:
                {
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                        InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                        _bmGoalsandtarget.InsertPlanTargetText(userId, builder, Convert.ToString(leveloneData.id),
                            "ALL", budgetYearBudMan, cNode, null, showOnlyModified, budgetPhaseId, goalsSectionSelected,
                            targetSectionLvl1Selected);
                    }

                    targetSectionLvl1Selected = false;
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.NewActionsLvl1) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    //New Action Grid
                    List<PublishTreeNode> ServiceAreaAction = new List<PublishTreeNode>();
                    List<clsOrgLevelOneAndTwoDetails> filteredLevelAndLevel2nonFilteredAction;
                    List<string> level1 = new List<string>();
                    List<string> level2 = new List<string>();
                    List<clsOrgStructure> lstOrgStructure = _utility.GetTenantOrgStructure(orgVersionContent, userId);
                    int budgetYear =
                        _utility.GetActiveBudgetYear(userId, nameof(clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR));
                    List<DocTreeActionHelper> nonFilteredActionWithChange =
                        _budgetManagement.NonFilteredActionNames(userId, new List<int> { 6, 9, 21, 31, 41, 7 },
                            budgetYear);
                    //budget phase
                    List<int> budgetPhaseChangeId =
                        _investment.GetChangeDataUsingBudgetPhase(budgetPhaseId, userId, showOnlyModified, budgetYear);
                    List<DocTreeActionHelper> nonFilteredAction = nonFilteredActionWithChange
                        .Where(x => budgetPhaseChangeId.Contains(x.changeId)).ToList();

                    List<clsOrgLevelOneAndTwoDetails> lstLevel1AndTwoDetailsnonFilteredAction =
                        _utility.GetOrgIdsAndServiceIdsDetailsFromStorage(orgVersionContent, userId, lstOrgStructure,
                            budgetYear, nonFilteredAction, "nonFilteredAction");
                    filteredLevelAndLevel2nonFilteredAction = lstLevel1AndTwoDetailsnonFilteredAction
                        .Where(x => x.orgId == Convert.ToString(leveloneData.id)).ToList();
                    foreach (var item in filteredLevelAndLevel2nonFilteredAction)
                    {
                        level1.AddRange(item.level1);
                        level2.AddRange(item.level2);
                    }

                    IEnumerable<DocTreeActionHelper> newActionNames =
                        _budgetManagement.GetActionNames2(userId, nonFilteredAction, level1, level2);
                    Dictionary<string, string> limitCodeData = _budgetManagement.GetLimitCode(newActionNames);
                    foreach (var limit in limitCodeData)
                    {
                        PublishTreeNode limitType = _budgetManagement.AddTreeNodeFromDoc(Convert.ToString(limit.Key),
                            Convert.ToString(limit.Value), nameof(clsConstants.BM_Tree_Types.LimitCode), false, true);
                        foreach (var aa in newActionNames.Where(x => x.limitCode == limit.Key)
                                     .OrderByDescending(x => Math.Abs(x.Year1Amount)))
                        {
                            PublishTreeNode ActionName = _budgetManagement.AddTreeNodeFromDoc(
                                Convert.ToString(aa.tempId), Convert.ToString(aa.Actions),
                                nameof(clsConstants.BM_Tree_Types.Action), false, true);
                            limitType.items.Add(ActionName);
                        }

                        ServiceAreaAction.Add(limitType);
                    }

                    IEnumerable<PublishTreeNode> items = ServiceAreaAction;
                    _bmNewActionHelper.InsertActionDescriptions(budgetYearBudMan, builder, userId,
                        items, incIntDesc,
                        nameof(clsConstants.BM_Tree_Types.NewAction), budgetPhaseId, showOnlyModified,
                        Convert.ToString(leveloneData.id), incBlistIntDesc,
                        insertBookmark: true, bookmarkTitle: cNode.text);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.SaOpexIncGraphlvl1) when cNode.@checked &&
                    publishHelper.GetType() == typeof(WebHelperBm):
                {
                    publishHelper.StartCollapsibleList(cNode.text, true,
                        _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                    publishHelper.StartCollapsibleListItem(_languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"]
                        .LangText);
                    string strAccGraphOne = _BMWebGraphs.GetAccountingGraphOneData(userId, budgetYearBudMan,
                        Convert.ToString(leveloneData.id), string.Empty, budgetPhaseId);
                    if (!string.IsNullOrEmpty(strAccGraphOne))
                    {
                        //builder.InsertImage(_graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true));
                        using (var retImage =
                               _graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true))
                        {
                            if (retImage != null)
                            {
                                var imageInfo = _utility.ConvertImagetoByte(retImage);
                                builder.InsertImage(imageInfo);
                            }
                        }
                    }

                    publishHelper.EndCollapsibleListItem();
                    publishHelper.EndCollapsibleList();
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BlistActionTextLevel1) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);

                    if (InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData))
                    {
                        isPageBreak = true;
                    }

                    IEnumerable<PublishTreeNode> items = FormatBlistDataSet(userId, builder, leveloneData,
                        budgetYearBudMan, incBlistIntDesc, orgVersionContent, langStringValuesDocExport);
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        _bmNewActionHelper.InsertActionDescriptions(budgetYearBudMan, builder, userId, items,
                            incIntDesc, nameof(clsConstants.BM_Tree_Types.BlistActionTextLevel1), budgetPhaseId,
                            showOnlyModified, Convert.ToString(leveloneData.id), incBlistIntDesc, insertBookmark: true,
                            bookmarkTitle: cNode.text);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.OperationBudgetServiceAreaLvl1) when
                    cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);

                    if (InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData))
                    {
                        isPageBreak = true;
                    }

                    if (showOnlyModified)
                    {
                        _bmBudBySaHelper.InsertCabGridForSingleBudgetPhase(userId, builder, leveloneData.id,
                            budgetPhaseId, showOnlyModified,
                            cNode, budgetYearBudMan, true);
                    }
                    else
                    {
                        var orgToGroup = _utility.GetParameterValue(userId, "CAB_ORG_GROUPING_L1");
                        if (!string.IsNullOrEmpty(orgToGroup))
                        {
                            orgToGroup = "org_lvl_1";
                        }

                        _bmBudBySaHelper.InsertCabGridNew(userId, builder, leveloneData.id, budgetPhaseId,
                            showOnlyModified,
                            cNode, budgetYearBudMan, true, incIntDesc, orgtoGroup: orgToGroup);
                    }
                    //if (cNode.isEditableNode)
                    //{
                    //    _customNode.InsertNodeDescription(builder, userId, leveloneData.id + cNode.id + cNode.type, cNode.Uid, budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    //}
                    break;
                }
                //else if (cNode.type == clsConstants.BM_Tree_Types.PivotCABServiceAreaLvl1.ToString() && cNode.@checked)
                //{
                //    //Insert strategy text
                //    _pivotCab.InsertPivotCABGrid(userId, builder, budgetYearBudMan, leveloneData.id, string.Empty, cNode);
                //}
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaKeyFigures) when cNode.@checked:
                {
                    var saKeyFigures = _budgetManagement.FormatServiceAreaKeyFigures(userId,
                            publishHelper.BaseStoragePath, budgetYearBudMan, leveloneData.id, string.Empty).GetAwaiter()
                        .GetResult();
                    publishHelper.InsertDataIntoBlob(saKeyFigures, publishHelper.GetCurrentChapterPath(), "keyfigures",
                        PubContentHandler.BlobType.Json);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) when cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvInFpYears) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                    var uidPctTotalGrossExpenses =
                        leveloneData.items.FirstOrDefault(x => x.type == "PctTotalGrossExpenses") == null
                            ? ""
                            : leveloneData.items.FirstOrDefault(x => x.type == "PctTotalGrossExpenses").Uid;
                    var uidInvInFpYears = leveloneData.items.FirstOrDefault(x => x.type == "InvInFpYears") == null
                        ? ""
                        : leveloneData.items.FirstOrDefault(x => x.type == "InvInFpYears").Uid;
                    bool casePublish = false;
                    //data fetch
                    string jsonStr = _budgetManagement.GetPctTotalGrossExpenses(userId, budgetYearBudMan,
                        leveloneData.id, string.Empty, leveloneData.text, string.Empty);
                    string jsonStrGrp = _bmInvestHelper.GetInvInFpYears(userId,
                        new List<int>()
                            { budgetYearBudMan, budgetYearBudMan + 1, budgetYearBudMan + 2, budgetYearBudMan + 3 },
                        leveloneData.id, string.Empty, budgetPhaseId, showOnlyModified);
                    var divideByMillions =
                        _utility.GetParameterValue(userId, "BM_FINPLAN_INFOGRAPHICS_DIVIDE_BY_MILL");
                    string expLang = _utility.GetUserDetails(userId).language_preference;
                    var _languageStringsBM = _utility.GetLanguageStrings(expLang, userId, "BudgetManagement");
                    // side by side graph only for Web
                    if (publishHelper.GetType() == typeof(WebHelperBm) &&
                        cNode.type == nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) && cNode.@checked &&
                        levelOne.FirstOrDefault(x => x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)) !=
                        null && levelOne
                            .FirstOrDefault(x => x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears))
                            .@checked)
                    {
                        if (jsonStrGrp != string.Empty && jsonStr != string.Empty)
                        {
                            publishHelper.InsertLineBreak();
                            publishHelper.InsertLineBreak();
                            publishHelper.GenerateGraphSideBySide(jsonStr, jsonStrGrp,
                                _languageStringsBM["PctTotalGrossExpenses_text"].LangText,
                                _languageStringsBM["InvInFpYears_text"].LangText,
                                !string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true"
                                    ? _languageStringsBM["InfoGraphics_divedBy_million"].LangText
                                    : _languageStringsBM["InfoGraphics_divedBy_thousand"].LangText,
                                uidPctTotalGrossExpenses, uidInvInFpYears);
                            publishHelper.InsertLineBreak();
                            publishHelper.InsertLineBreak();
                            casePublish = true;
                        }
                    }

                    if (!casePublish)
                    {
                        if (jsonStr != string.Empty &&
                            cNode.type == nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) &&
                            cNode.@checked)
                        {
                            _bmTenantSpecificSA.GeneratePctTotalGrossExpensesOrInvInFpYearsGrh(userId,
                                publishHelper, builder, jsonStr, string.Empty, "PctTotalGrossExpenses", null);
                        }

                        if (jsonStrGrp != string.Empty &&
                            levelOne.FirstOrDefault(x =>
                                x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)) != null &&
                            levelOne.FirstOrDefault(x => x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears))
                                .@checked)
                        {
                            _bmTenantSpecificSA.GeneratePctTotalGrossExpensesOrInvInFpYearsGrh(userId,
                                publishHelper, builder, jsonStrGrp, string.Empty, "InvInFpYears", divideByMillions);
                        }
                    }
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.Investments):
                case nameof(clsConstants.BM_Tree_Types.InvestmentsStatus):
                {
                    IEnumerable<PublishTreeNode> items = null;
                    bool isBlistInvestment = cNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentsStatus);
                    if (cNode.items != null)
                    {
                        items = cNode.items;

                        foreach (var subnode in items)
                        {
                            if (subnode.@checked || _utility.AreAnyChildrenChecked(subnode))
                            {
                                InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                                InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);
                                break;
                            }
                        }

                        _bmInvestHelper.InsertInvestmentTables(builder, userId, Convert.ToString(leveloneData.id),
                            items, budgetPhaseId,
                            showOnlyModified, cNode, budgetYearBudMan, incIntDesc, true, isBlistInvestment,
                            incBlistIntDesc);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.customNode):
                {
                    bool anyChildrenChecked = _utility.AreAnyChildrenChecked(cNode);
                    if (anyChildrenChecked)
                    {
                        InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                        InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                        _customNode.InsertNestedCustomNodes(builder, userId, cNode, budgetPhaseId, showOnlyModified,
                            true, true);
                        //_customNode.InsertCustomNode(builder, userId, Guid.Parse(cNode.id), budgetPhaseId, showOnlyModified);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.ServiceId):
                {
                    bool anyChildrenChecked = _utility.AreAnyChildrenChecked(cNode);
                    if (cNode.@checked || anyChildrenChecked)
                    {
                        publishHelper.AddServiceUnitsUnderCurrentServiceArea(leveloneData.id, leveloneData.text,
                            cNode.id, cNode.text);

                        string OrgId = leveloneData.id;
                        string OrgName = leveloneData.text;
                        publishHelper.StartChapter(nameof(clsConstants.TreeId.orgstructuremain),
                            Convert.ToString(leveloneData.id),
                            Convert.ToString(cNode.id), BookmarkStrategy.Custom, cNode.text, PageController.Generic,
                            cNode.Uid);

                        InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                        InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                        if (anyChildrenChecked)
                        {
                            publishHelper.InsertHeading(userId, Convert.ToString(cNode.text), _context);
                        }

                        ProcessChildNodesForServiceId(userId, builder, cNode, templateId, OrgId, OrgName, incIntDesc,
                            budgetPhaseId, showOnlyModified, budgetYearBudMan, incBlistIntDesc, cacheKey: cacheKey);

                        publishHelper.EndCurrentChapter();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetServiceArea) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);

                    if (InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData))
                    {
                        isPageBreak = true;
                    }

                    publishHelper.InsertHeading(userId, _languageStringsBudgetManagement["BudgetDesc_title"].LangText,
                        _context, true);

                    if (retVal.descBPBD.description != "" && retVal.descBPBD.description != null)
                    {
                        if (retVal.descBPBD.abstractText != null && retVal.descBPBD.abstractText != "")
                        {
                            Color color = Color.Black;
                            builder.ParagraphFormat.ClearFormatting();
                            _docStyle.FormatFont(builder, false, color.ToArgb(), "Calibri", 0, true, 11);
                            builder.Write(retVal.descBPBD["abstractText"].Value);
                            builder.ParagraphFormat.ClearFormatting();
                        }

                        publishHelper.InsertDescriptionCk5(Convert.ToString(retVal.descBPBD.description), userId, 0);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetProposalStrategySA) when cNode.@checked:
                    _bmOppHelper.InsertBudgetProposalStrategyData(userId, leveloneData.id, string.Empty,
                        budgetYearBudMan, publishHelper, cNode);
                    break;
                case nameof(clsConstants.BM_Tree_Types.BM_ManYears) when cNode.@checked:
                    //Enter Table Data
                    _bmBudBySaHelper.InsertBMManYearsTable(userId, budgetYearBudMan, builder, cNode, true,
                        leveloneData.id);
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaLevelCabOverview) when cNode.@checked:
                    //Enter Table Data

                    _bmBudBySaHelper.InsertCabGridOnTenantLevel(userId, builder, leveloneData.id, budgetPhaseId,
                        showOnlyModified, null);
                    break;
                case nameof(clsConstants.BM_Tree_Types.reportingWidget) when cNode.@checked:
                {
                    InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak);
                    InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);
        
                    if (cNode.parameters != null && cNode.parameters.ContainsKey("masterNodeId"))
                    {
                        _bmNewActionHelper.InsertReportingWidget(userId, cNode.text, _context, builder,
                            budgetYearBudMan, cNode.parameters.GetValueOrDefault("masterNodeId"), cNode.id);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByAccountGroupTable) when cNode.@checked:
                {
                    _bmBudBySaHelper.InsertBudgetByAccountGroupTable(userId, builder, leveloneData.id, "All", cacheKey,
                        budgetYearBudMan, budgetPhaseId, cNode, cNode.id, showOnlyModified, "Type1");
                    if (cNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            leveloneData.id + cNode.id + cNode.id + cNode.type, cNode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable) when cNode.@checked:
                {
                    _bmBudBySaHelper.InsertBudgetByServiceTable(userId, builder, leveloneData.id, "All", templateId,
                        cNode, cNode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, "Type1", cacheKey);
                    if (cNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            leveloneData.id + cNode.id + cNode.id + cNode.type, cNode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByService) when cNode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, leveloneData.id,
                        "All", isWebHelper);
                    publishHelper.InsertHeading(userId, cNode.text.Trim(), _context, true);
                    if (!string.IsNullOrEmpty(jsonStr))
                    {
                        //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                        bool divideByMillions =
                            _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                        if (divideByMillions)
                        {
                            publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8, false,
                                true);
                        }
                        else
                        {
                            publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false,
                                true);
                        }

                        dynamic json = JToken.Parse(jsonStr);
                        string graphOneName = "BudgetByService";
                        dynamic jsonRet = new JObject();
                        jsonRet.Add("infile", json);
                        jsonRet.infile.type = graphOneName;
                        jsonRet.infile.numformat = "";

                        String mergedData = JsonConvert.SerializeObject(jsonRet);

                        using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                        {
                            if (retImage != null)
                            {
                                //builder.InsertImage(retImage);
                                var imageInfo = _utility.ConvertImagetoByte(retImage);
                                builder.InsertImage(imageInfo);
                            }
                        }

                        publishHelper.InsertParagraphBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByDepartment) when cNode.@checked:
                {
                    publishHelper.InsertHeading(userId, cNode.text.Trim(), _context, true);
                    bool divideByMillions =
                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_organization_title");
                    if (divideByMillions)
                    {
                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8, false,
                            true);
                    }
                    else
                    {
                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false,
                            true);
                    }

                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr = _budgetManagement.GetBudgetByDepartment(userId, budgetYearBudMan, leveloneData.id,
                        "All", isWebHelper, budgetPhaseId);
                    if (!string.IsNullOrEmpty(jsonStr))
                    {
                        dynamic json = JToken.Parse(jsonStr);
                        string graphOneName = "BudgetByDepartment";
                        dynamic jsonRet = new JObject();
                        jsonRet.Add("infile", json);
                        jsonRet.infile.type = graphOneName;
                        jsonRet.infile.numformat = "";

                        String mergedData = JsonConvert.SerializeObject(jsonRet);

                        using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                        {
                            if (retImage != null)
                            {
                                //builder.InsertImage(retImage);
                                var imageInfo = _utility.ConvertImagetoByte(retImage);
                                builder.InsertImage(imageInfo);
                            }
                        }

                        publishHelper.InsertParagraphBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByOrgTable) when cNode.@checked:
                {
                    //publishHelper.InsertHeading(userId, cNode.text.Trim(), _context, true);
                    _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, leveloneData.id, "All",
                        templateId, cNode, cNode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, "Type1",
                        cacheKey, false);
                    if (cNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            leveloneData.id + cNode.id + cNode.id + cNode.type, cNode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable_Chapter) when cNode.@checked:
                {
                    _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, leveloneData.id, "All",
                        templateId, cNode, cNode.id, budgetYearBudMan, showOnlyModified, budgetPhaseId, "Type1",
                        cacheKey, false, true);
                    if (cNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId,
                            leveloneData.id + cNode.id + cNode.id + cNode.type, cNode.Uid, budgetYearBudMan,
                            showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl2) when cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) when
                    cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl2) when cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl1) when
                    cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl1) when
                    cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl1) when cNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl1) when cNode.@checked:
                {
                    bool isBlistInvestment =
                        cNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) ||
                        cNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl2) ||
                        cNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl1) ||
                        cNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl1);

                    _bmInvestHelper.InsertInvestmentTablesFlat(builder, userId, Convert.ToString(leveloneData.id),
                        budgetPhaseId,
                        showOnlyModified, cNode, budgetYearBudMan, incIntDesc, true, isBlistInvestment,
                        incBlistIntDesc);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetPerFunctAreaAndAccntGrpForOrg) when cNode.@checked:
                {
                    _FinplanYearlyBudgetSummaryHelper.Initialize(userId, builder, budgetYearBudMan, cNode).GetAwaiter()
                        .GetResult();
                    _FinplanYearlyBudgetSummaryHelper.InsertFpBudgetbyFunctionalArea(orgVersionContent, leveloneData.id)
                        .GetAwaiter().GetResult();
                    break;
                }
                default:
                {
                    var isNodeFromAssessment = cNode.parameters?.ContainsKey("isOpportunityAssessment") is true;

                    if (cNode.type == nameof(clsConstants.BM_Tree_Types.BPStrategies) &&
                        _utility.AreAnyChildrenChecked(cNode) || isNodeFromAssessment)
                    {
                        InsertHeaderIfRequired(publishHelper, builder, userId, ref isHeaderRequired, ref isPageBreak, !isNodeFromAssessment);
                        InsertSaHeadingIfRequired(publishHelper, builder, userId, ref isSAHeadingRequired, leveloneData);

                        if (isNodeFromAssessment)
                        {
                            _bmOppHelper.InsertStrategyDataForBudgetProposalDocument(userId, budgetYearBudMan,
                                publishHelper, new List<PublishTreeNode> { cNode }, true);
                        }
                        else
                        {
                            publishHelper.InsertHeading(userId, cNode.text, _context, false);
                            _bmOppHelper.InsertStrategyDataForBudgetProposalDocument(userId, budgetYearBudMan,
                                publishHelper, cNode.items);
                        }
                    }
                    break;
                }
            }

            if (isPageBreak)
            {
                builder.InsertBreak(BreakType.PageBreak);
            }

            return isSAHeadingRequired;
        }
        catch (Exception)
        {
            publishHelper.InsertError(
                $"ProcessChildNodesForMultiLevel failed. Node Id - {cNode.id}. Node Type - {cNode.type}");
            return isSAHeadingRequired;
        }
    }


    private void ProcessChildNodesForServiceId(string userId, DocumentBuilder builder, PublishTreeNode parentNode,
        string templateId, string orgId, string orgName, bool incIntDesc, string budgetPhaseId,
        bool showOnlyModified, int budgetYearBudMan, bool incBlistIntDesc, Guid cacheKey,
        string levelSetUp = "ServiceId")
    {
        var orgVersionContent =
            _utility.GetOrgVersionSpecificContent(userId, _utility.GetForecastPeriod(budgetYearBudMan, 1));
        
        UserData userDetails = _utility.GetUserDetails(userId);
        Dictionary<string, clsLanguageString> langStringValuesDocExport =
            _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
        Dictionary<string, clsLanguageString> langStrings33 =
            _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
        
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
        
        bool insertTarget = false;
        bool goalSectionSelected = false;
        bool nodeProcessed = false;
        
        foreach (var currentNode in parentNode.items.Where(currentNode => currentNode.@checked || _utility.AreAnyChildrenChecked(currentNode)))
        {
            publishHelper.StartNewLevel(currentNode);
            publishHelper.StartNode(currentNode);
            
            string nodeDescIdSid = $"{orgId}{parentNode.id}{currentNode.id}{currentNode.type}";
            bool anyChildrenChecked = _utility.AreAnyChildrenChecked(currentNode);
            
            switch (currentNode.type)
            {
                case nameof(clsConstants.BM_Tree_Types.ChapterIntro) when currentNode.@checked:
                {
                    JObject result = _utility.GetActionTypeDescriptionWithAbstractText(userId, orgId, null,
                        "BP-SU-SA", null, budgetYearBudMan, Convert.ToString(parentNode.id));
                    string description = (string)result["description"];
                    string abstractText = (string)result["abstractText"];

                    Dictionary<string, clsLanguageString> langStrings =
                        _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                            "docexport");
                    if (!string.IsNullOrEmpty(description) || !string.IsNullOrEmpty(abstractText))
                    {
                        publishHelper.InsertHeading(userId,
                            langStrings["ChapterIntro_title"].LangText,
                            _context);
                    }

                    if (!string.IsNullOrEmpty(abstractText))
                    {
                        if (publishHelper.GetType() == typeof(WebHelperBm))
                        {
                            publishHelper.InsertAbstract(abstractText);
                        }
                        else
                        {
                            Color color = Color.Black;
                            builder.ParagraphFormat.ClearFormatting();
                            _docStyle.FormatFont(builder, false, color.ToArgb(), "Calibri", 0, false, 11);
                            builder.Write(abstractText);
                            builder.ParagraphFormat.ClearFormatting();
                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }

                    publishHelper.InsertDescriptionCk5(description, userId, 0);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.FPAssignmentServiceAreaLevel) when
                    currentNode.@checked:
                    _bmAssignmentHelper.InsertFPAssignmentsData(userId, builder, currentNode,
                        budgetYearBudMan, 3, orgId, parentNode.id);
                    break;
                case nameof(clsConstants.BM_Tree_Types.StratergyTextServiceAreaLevel) when
                    currentNode.@checked:
                    _bmGoalsandtarget.InsertStratergyTextCityLevel(userId, publishHelper, parentNode.id, 0, "",
                        budgetYearBudMan);
                    break;
                case nameof(clsConstants.BM_Tree_Types.ServiceIdDescription) when
                    currentNode.@checked:
                    //Insert strategy text
                    _bmIntroHelper.CreateServiceAreaDescription(builder, userId, currentNode, orgId,
                        Convert.ToString(parentNode.id));
                    break;
                //if (currentNode.type == nameof(clsConstants.BM_Tree_Types.BudpropExplanation) && currentNode.@checked)
                //{
                //    //Insert strategy text
                //    _bmIntroHelper.GetBudPropExplanation(builder, userId, currentNode, orgId, Convert.ToString(parentNode.id));
                //}
                case nameof(clsConstants.BM_Tree_Types.ServiceAreaKeyFigures) when
                    currentNode.@checked:
                {
                    var saKeyFigures = _budgetManagement.FormatServiceAreaKeyFigures(userId,
                            publishHelper.BaseStoragePath, budgetYearBudMan, orgId, parentNode.id).GetAwaiter()
                        .GetResult();
                    publishHelper.InsertDataIntoBlob(saKeyFigures, publishHelper.GetCurrentChapterPath(),
                        "keyfigures", PubContentHandler.BlobType.Json);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvInFpYears) when currentNode.@checked:
                {
                    if (!nodeProcessed)
                    {
                        var uidPctTotalGrossExpenses =
                            parentNode.items.FirstOrDefault(x => x.type == "PctTotalGrossExpenses") == null
                                ? ""
                                : parentNode.items.FirstOrDefault(x => x.type == "PctTotalGrossExpenses").Uid;
                        var uidInvInFpYears = parentNode.items.FirstOrDefault(x => x.type == "InvInFpYears") == null
                            ? ""
                            : parentNode.items.FirstOrDefault(x => x.type == "InvInFpYears").Uid;
                        nodeProcessed = true;
                        bool casePublish = false;
                        //data fetch
                        string jsonStr = _budgetManagement.GetPctTotalGrossExpenses(userId, budgetYearBudMan, orgId,
                            parentNode.id, orgName, parentNode.text);
                        string jsonStrGrp = _bmInvestHelper.GetInvInFpYears(userId,
                            new List<int>()
                            {
                                budgetYearBudMan, budgetYearBudMan + 1, budgetYearBudMan + 2, budgetYearBudMan + 3
                            }, orgId, parentNode.id, budgetPhaseId, showOnlyModified);
                        var divideByMillions =
                            _utility.GetParameterValue(userId, "BM_FINPLAN_INFOGRAPHICS_DIVIDE_BY_MILL");
                        string expLang = _utility.GetUserDetails(userId).language_preference;
                        var _languageStringsBM = _utility.GetLanguageStrings(expLang, userId, "BudgetManagement");
                        // side by side graph only for Web
                        if (publishHelper.GetType() == typeof(WebHelperBm) &&
                            currentNode.type == nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) &&
                            currentNode.@checked &&
                            parentNode.items.FirstOrDefault(x =>
                                x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)) != null && parentNode
                                .items.FirstOrDefault(x =>
                                    x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)).@checked)
                        {
                            if (jsonStrGrp != string.Empty && jsonStr != string.Empty)
                            {
                                publishHelper.InsertLineBreak();
                                publishHelper.InsertLineBreak();
                                publishHelper.GenerateGraphSideBySide(jsonStr, jsonStrGrp,
                                    _languageStringsBM["PctTotalGrossExpenses_text"].LangText,
                                    _languageStringsBM["InvInFpYears_text"].LangText,
                                    !string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true"
                                        ? _languageStringsBM["InfoGraphics_divedBy_million"].LangText
                                        : _languageStringsBM["InfoGraphics_divedBy_thousand"].LangText,
                                    uidPctTotalGrossExpenses, uidInvInFpYears);
                                publishHelper.InsertLineBreak();
                                publishHelper.InsertLineBreak();
                                casePublish = true;
                            }
                        }

                        if (!casePublish)
                        {
                            if (jsonStr != string.Empty &&
                                currentNode.type == nameof(clsConstants.BM_Tree_Types.PctTotalGrossExpenses) &&
                                currentNode.@checked)
                            {
                                _bmTenantSpecificSA.GeneratePctTotalGrossExpensesOrInvInFpYearsGrh(userId,
                                    publishHelper, builder, jsonStr, string.Empty, "PctTotalGrossExpenses", null);
                            }

                            if (jsonStrGrp != string.Empty &&
                                parentNode.items.FirstOrDefault(x =>
                                    x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)) != null &&
                                parentNode.items.FirstOrDefault(x =>
                                    x.type == nameof(clsConstants.BM_Tree_Types.InvInFpYears)).@checked)
                            {
                                _bmTenantSpecificSA.GeneratePctTotalGrossExpensesOrInvInFpYearsGrh(userId,
                                    publishHelper, builder, jsonStrGrp, string.Empty, "InvInFpYears",
                                    divideByMillions);
                            }
                        }
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.ServiceTable) when anyChildrenChecked:
                {
                    publishHelper.InsertHeading(userId,
                        _languageStringsExportCommon["BM_doc_budget_table_title"].LangText, _context, true);

                    string type = "Type2";
                    foreach (var item in currentNode.items)
                    {
                        publishHelper.StartNewLevel(item);
                        publishHelper.StartNode(item);
                        string nodeDescId = $"{orgId}{parentNode.id}{currentNode.id}{item.id}{item.type}";
                        if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByAccountGroupTable) &&
                            item.@checked)
                        {
                            _bmBudBySaHelper.InsertBudgetByAccountGroupTable(userId, builder, orgId,
                                Convert.ToString(parentNode.id), cacheKey, budgetYearBudMan, budgetPhaseId, item,
                                nodeDescId, showOnlyModified, type);
                            if (item.isEditableNode)
                            {
                                _customNode.InsertNodeDescription(builder, userId, nodeDescId, item.Uid,
                                    budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                            }
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.SaOpexIncGraphlvl1) &&
                                 item.@checked && publishHelper.GetType() == typeof(WebHelperBm))
                        {
                            publishHelper.StartCollapsibleList(item.text, true,
                                _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                            publishHelper.StartCollapsibleListItem(
                                _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                            string strAccGraphOne = _BMWebGraphs.GetAccountingGraphOneData(userId, budgetYearBudMan,
                                Convert.ToString(parentNode.id), string.Empty, budgetPhaseId);
                            if (strAccGraphOne == string.Empty)
                            {
                                return;
                            }

                            //builder.InsertImage(_graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true));
                            using (var retImage =
                                   _graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne,
                                       600, true))
                            {
                                if (retImage != null)
                                {
                                    var imageInfo = _utility.ConvertImagetoByte(retImage);
                                    builder.InsertImage(imageInfo);
                                }
                            }

                            publishHelper.EndCollapsibleListItem();
                            publishHelper.EndCollapsibleList();
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable) &&
                                 item.@checked)
                        {
                            _bmBudBySaHelper.InsertBudgetByServiceTable(userId, builder, orgId,
                                Convert.ToString(parentNode.id), templateId, item, nodeDescId, budgetYearBudMan,
                                showOnlyModified, budgetPhaseId, type, cacheKey);
                            if (item.isEditableNode)
                            {
                                _customNode.InsertNodeDescription(builder, userId, nodeDescId, item.Uid,
                                    budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                            }
                        }

                        //Graph for budget by service and budget by service
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByService) && item.@checked)
                        {
                            bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                            string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, orgId,
                                Convert.ToString(parentNode.id), isWebHelper);
                            if (jsonStr == string.Empty)
                            {
                                return;
                            }

                            publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                            bool divideByMillions =
                                _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                            if (divideByMillions)
                            {
                                publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8,
                                    false, true);
                            }
                            else
                            {
                                publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8,
                                    false, true);
                            }

                            dynamic json = JToken.Parse(jsonStr);
                            string graphOneName = "BudgetByService";
                            dynamic jsonRet = new JObject();
                            jsonRet.Add("infile", json);
                            jsonRet.infile.type = graphOneName;
                            jsonRet.infile.numformat = "";

                            String mergedData = JsonConvert.SerializeObject(jsonRet);

                            using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                            {
                                if (retImage != null)
                                {
                                    //builder.InsertImage(retImage);
                                    var imageInfo = _utility.ConvertImagetoByte(retImage);
                                    builder.InsertImage(imageInfo);
                                }
                            }

                            publishHelper.InsertParagraphBreak();
                            //line break between graph and table
                            if (publishHelper.GetType() != typeof(WebHelperBm))
                            {
                                publishHelper.InsertLineBreak();
                            }
                        }

                        //Graph for budget by service and budget by Department
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByDepartment) &&
                                 item.@checked)
                        {
                            bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                            string jsonStr = _budgetManagement.GetBudgetByDepartment(userId, budgetYearBudMan,
                                orgId, Convert.ToString(parentNode.id), isWebHelper, budgetPhaseId);
                            if (jsonStr == string.Empty)
                            {
                                return;
                            }

                            publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);

                            bool divideByMillions = _utility.CheckOnDivideByMillions(userId,
                                "Million_BM_doc_budget_organization_title");
                            if (divideByMillions)
                            {
                                publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8,
                                    false, true);
                            }
                            else
                            {
                                publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8,
                                    false, true);
                            }

                            dynamic json = JToken.Parse(jsonStr);
                            string graphOneName = "BudgetByDepartment";
                            dynamic jsonRet = new JObject();
                            jsonRet.Add("infile", json);
                            jsonRet.infile.type = graphOneName;
                            jsonRet.infile.numformat = "";

                            String mergedData = JsonConvert.SerializeObject(jsonRet);

                            using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                            {
                                if (retImage != null)
                                {
                                    //builder.InsertImage(retImage);
                                    var imageInfo = _utility.ConvertImagetoByte(retImage);
                                    builder.InsertImage(imageInfo);
                                }
                            }

                            publishHelper.InsertParagraphBreak();
                            //line break between graph and table
                            if (publishHelper.GetType() != typeof(WebHelperBm))
                            {
                                publishHelper.InsertLineBreak();
                            }
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByOrgTable) && item.@checked)
                        {
                            _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, orgId,
                                Convert.ToString(parentNode.id), templateId, item, nodeDescId, budgetYearBudMan,
                                showOnlyModified, budgetPhaseId, type, cacheKey, false);
                            if (item.isEditableNode)
                            {
                                _customNode.InsertNodeDescription(builder, userId, nodeDescId, item.Uid,
                                    budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                            }
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable_Chapter) &&
                                 item.@checked)
                        {
                            _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, orgId,
                                Convert.ToString(parentNode.id), templateId, item, nodeDescId, budgetYearBudMan,
                                showOnlyModified, budgetPhaseId, type, cacheKey, false, true);
                            if (item.isEditableNode)
                            {
                                _customNode.InsertNodeDescription(builder, userId, nodeDescId, item.Uid,
                                    budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                            }
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.BudgetByService_Chapter) &&
                                 item.@checked)
                        {
                            bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                            string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, orgId,
                                Convert.ToString(parentNode.id), isWebHelper, true);
                            publishHelper.InsertHeading(userId, item.text.Trim(), _context, true);
                            if (!string.IsNullOrEmpty(jsonStr))
                            {
                                //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                                bool divideByMillions =
                                    _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                                if (divideByMillions)
                                {
                                    publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText,
                                        8, false, true);
                                }
                                else
                                {
                                    publishHelper.InsertText(
                                        langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false, true);
                                }

                                dynamic json = JToken.Parse(jsonStr);
                                string graphOneName = "BudgetByService_Chapter";
                                dynamic jsonRet = new JObject();
                                jsonRet.Add("infile", json);
                                jsonRet.infile.type = graphOneName;
                                jsonRet.infile.numformat = "";

                                String mergedData = JsonConvert.SerializeObject(jsonRet);

                                using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                                {
                                    if (retImage != null)
                                    {
                                        //builder.InsertImage(retImage);
                                        var imageInfo = _utility.ConvertImagetoByte(retImage);
                                        builder.InsertImage(imageInfo);
                                    }
                                }

                                publishHelper.InsertParagraphBreak();
                            }
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.customNode) && item.@checked)
                        {
                            _customNode.InsertNestedCustomNodes(builder, userId, item, budgetPhaseId,
                                showOnlyModified, true, true);
                        }
                        else if (item.type == nameof(clsConstants.BM_Tree_Types.customNode) && item.@checked)
                        {
                            _customNode.InsertNestedCustomNodes(builder, userId, item, budgetPhaseId,
                                showOnlyModified, true, true);
                        }

                        publishHelper.EndCurrentLevel();
                        publishHelper.EndNode(item);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.ServiceIDStrategyAndChallenge) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.StrategyAndChallenge) when
                    currentNode.@checked:
                    //Insert strategy text
                    _bmIntroHelper.CreateStrategyObjectives(builder, userId, currentNode, orgId,
                        Convert.ToString(parentNode.id));
                    break;
                case nameof(clsConstants.BM_Tree_Types.KostraAnalysis) when currentNode.@checked:
                {
                    bool kostraTabGridPerReportingArea =
                        _utility.CheckOnKostraTableGridPerReportingArea(userId, "TRUE");

                    if (kostraTabGridPerReportingArea)
                    {
                        //Apply the old logic based on connection between the org_id and the reporting area
                        //There should be one table grid per reporting area with the description for that report area made in KOSTRA module.
                        Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(userId);
                        Dictionary<string, string> reportingArea = _kostraData.GetReportingAreaDataForServiceIds(
                            userId,
                            Convert.ToString(parentNode.id), orgLevelValue["FINPLAN_LEVEL_2"]);

                        foreach (var item in reportingArea)
                        {
                            _bmBudBySaHelper.InsertKostraAnalysis(userId, builder, currentNode, item.Key,
                                item.Value, templateId,
                                false, kostraTabGridPerReportingArea);
                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }
                    else
                    {
                        //Display one Kostra table per service area with a description based on a link between the service_id (level_1 / level_2 value) and the indicators needed in report
                        _bmBudBySaHelper.InsertKostraAnalysis(userId, builder, currentNode, orgId,
                            Convert.ToString(parentNode.id), templateId,
                            false, kostraTabGridPerReportingArea);
                    }

                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescIdSid, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.KostraProfile) when currentNode.@checked &&
                                                                           publishHelper.GetType() == typeof(WebHelperBm):
                {
                    bool kostraTabGridPerReportingArea =
                        _utility.CheckOnKostraTableGridPerReportingArea(userId, "TRUE");

                    if (kostraTabGridPerReportingArea)
                    {
                        //Apply the old logic based on connection between the org_id and the reporting area
                        //There should be one table grid per reporting area with the description for that report area made in KOSTRA module.
                        Dictionary<string, string> orgLevelValue = _utility.GetOrglevels(userId);
                        Dictionary<string, string> reportingArea = _kostraData.GetReportingAreaDataForServiceIds(
                            userId,
                            Convert.ToString(parentNode.id), orgLevelValue["FINPLAN_LEVEL_2"]);

                        foreach (var item in reportingArea)
                        {
                            kostraProfileInput input = new kostraProfileInput
                            {
                                User = userId,
                                Builder = builder,
                                KNode = currentNode,
                                OrgIdOrAreaCode = item.Key,
                                ServiceIdOrAreaName = item.Value,
                                TemplateId = templateId,
                                IsTenantSpecific = false,
                                IsUsingOrgRepArea = kostraTabGridPerReportingArea
                            };
                            _bmBudBySaHelper.InsertKostraProfileGraph(input).GetAwaiter().GetResult();
                            builder.InsertBreak(BreakType.ParagraphBreak);
                        }
                    }
                    else
                    {
                        kostraProfileInput input = new kostraProfileInput
                        {
                            User = userId,
                            Builder = builder,
                            KNode = currentNode,
                            OrgIdOrAreaCode = orgId,
                            ServiceIdOrAreaName = Convert.ToString(parentNode.id),
                            TemplateId = templateId,
                            IsTenantSpecific = false,
                            IsUsingOrgRepArea = kostraTabGridPerReportingArea
                        };

                        //Display one Kostra table per service area with a description based on a link between the service_id (level_1 / level_2 value) and the indicators needed in report
                        _bmBudBySaHelper.InsertKostraProfileGraph(input).GetAwaiter().GetResult();
                    }

                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescIdSid, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.OperationBudgetServiceArea) when
                    currentNode.@checked:
                {
                    //Insert CAB Grid
                    if (showOnlyModified)
                    {
                        _bmBudBySaHelper.InsertCabGridForSingleBudgetPhase(userId, builder, orgId, budgetPhaseId,
                            showOnlyModified, currentNode, budgetYearBudMan, false,
                            Convert.ToString(parentNode.id));
                    }
                    else
                    {
                        var orgToGroup = _utility.GetParameterValue(userId, "CAB_ORG_GROUPING_L2");
                        if (!string.IsNullOrEmpty(orgToGroup))
                        {
                            orgToGroup = "org_lvl_2";
                        }

                        _bmBudBySaHelper.InsertCabGridNew(userId, builder, orgId, budgetPhaseId, showOnlyModified,
                            currentNode, budgetYearBudMan, false, incIntDesc, Convert.ToString(parentNode.id),
                            orgtoGroup: orgToGroup);
                    }
                    //if (currentNode.isEditableNode)
                    //{
                    //    _customNode.InsertNodeDescription(builder, userId, nodeDescIdSid, currentNode.Uid, budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    //}
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.NewAction) when currentNode.@checked:
                {
                    //New Action Grid
                    //New Action Grid
                    List<PublishTreeNode> ServiceAreaAction = new List<PublishTreeNode>();
                    List<clsOrgLevelOneAndTwoDetails> filteredLevelAndLevel2nonFilteredAction;
                    List<string> level1 = new List<string>();
                    List<string> level2 = new List<string>();
                    List<clsOrgStructure> lstOrgStructure =
                        _utility.GetTenantOrgStructure(orgVersionContent, userId);
                    int budgetYear = _utility.GetActiveBudgetYear(userId,
                        nameof(clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR));
                    List<DocTreeActionHelper> nonFilteredActionWithChange =
                        _budgetManagement.NonFilteredActionNames(userId, new List<int> { 6, 21, 31, 41, 9, 7 },
                            budgetYear);
                    //budget phase
                    List<int> budgetPhaseChangeId =
                        _investment.GetChangeDataUsingBudgetPhase(budgetPhaseId, userId, showOnlyModified,
                            budgetYear);
                    List<DocTreeActionHelper> nonFilteredAction = nonFilteredActionWithChange
                        .Where(x => budgetPhaseChangeId.Contains(x.changeId)).ToList();

                    List<clsOrgLevelOneAndTwoDetails> lstLevel1AndTwoDetailsnonFilteredAction =
                        _utility.GetOrgIdsAndServiceIdsDetailsFromStorage(orgVersionContent, userId,
                            lstOrgStructure, budgetYear, nonFilteredAction, "nonFilteredAction");
                    filteredLevelAndLevel2nonFilteredAction = lstLevel1AndTwoDetailsnonFilteredAction
                        .Where(x => x.orgId == orgId && x.serviceId == Convert.ToString(parentNode.id)).ToList();
                    foreach (var item in filteredLevelAndLevel2nonFilteredAction)
                    {
                        level1.AddRange(item.level1);
                        level2.AddRange(item.level2);
                    }

                    IEnumerable<DocTreeActionHelper> newActionNames =
                        _budgetManagement.GetActionNames2(userId, nonFilteredAction, level1, level2);
                    Dictionary<string, string> limitCodeData = _budgetManagement.GetLimitCode(newActionNames);
                    foreach (var limit in limitCodeData)
                    {
                        PublishTreeNode limitType = _budgetManagement.AddTreeNodeFromDoc(
                            Convert.ToString(limit.Key), Convert.ToString(limit.Value),
                            nameof(clsConstants.BM_Tree_Types.LimitCode), false, true);
                        foreach (var aa in newActionNames.Where(x => x.limitCode == limit.Key)
                                     .OrderByDescending(x => Math.Abs(x.Year1Amount)))
                        {
                            PublishTreeNode ActionName = _budgetManagement.AddTreeNodeFromDoc(
                                Convert.ToString(aa.tempId), Convert.ToString(aa.Actions),
                                nameof(clsConstants.BM_Tree_Types.Action), false, true);
                            limitType.items.Add(ActionName);
                        }

                        ServiceAreaAction.Add(limitType);
                    }

                    IEnumerable<PublishTreeNode> items = ServiceAreaAction;
                    _bmNewActionHelper.InsertActionDescriptions(budgetYearBudMan, builder,
                        userId, items, incIntDesc,
                        nameof(clsConstants.BM_Tree_Types.NewAction), budgetPhaseId, showOnlyModified, orgId,
                        incBlistIntDesc, Convert.ToString(parentNode.id), cabGridSelected: true,
                        lstLimitCodes: _bmBudBySaHelper.LimitCodes,
                        insertBookmark: true, bookmarkTitle: currentNode.text);
                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescIdSid, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BlistActionText) when currentNode.@checked:
                {
                    List<PublishTreeNode> ServiceAreaAction = new List<PublishTreeNode>();
                    List<clsOrgStructure> lstOrgStructure =
                        _utility.GetTenantOrgStructure(orgVersionContent, userId);
                    int budgetYear = _utility.GetActiveBudgetYear(userId,
                        nameof(clsConstants.BudgetYear.BUDMAN_BUDGET_YEAR));
                    List<DocTreeActionHelper> nonFilteredBlist =
                        _budgetManagement.NonFilteredBList(userId, new List<int> { 31, 41, 9, 21 }, budgetYear);
                    List<clsOrgLevelOneAndTwoDetails> lstLevel1AndTwoDetailsnonFilteredBlist =
                        _utility.GetOrgIdsAndServiceIdsDetailsFromStorage(orgVersionContent, userId,
                            lstOrgStructure, budgetYear, nonFilteredBlist, "nonFilteredBlist");
                    var filteredLevelAndLevel2nonFilteredBlist = lstLevel1AndTwoDetailsnonFilteredBlist
                        .Where(x => x.orgId == orgId && x.serviceId == Convert.ToString(parentNode.id)).ToList();
                    List<string> level1 = new List<string>();
                    List<string> level2 = new List<string>();
                    Dictionary<int, string> ActionType = new Dictionary<int, string>();
                    ActionType.Add(31,
                        langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_FinPlan_CR").Value.LangText);
                    ActionType.Add(41,
                        langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_FinPlan_NP").Value.LangText);
                    ActionType.Add(9,
                        langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_FinPlan_ActionType9").Value
                            .LangText);
                    ActionType.Add(21,
                        langStringValuesDocExport.FirstOrDefault(d => d.Key == "doc_BList_21").Value.LangText);
                    foreach (var item in filteredLevelAndLevel2nonFilteredBlist)
                    {
                        level1.AddRange(item.level1);
                        level2.AddRange(item.level2);
                    }

                    foreach (var a in ActionType)
                    {
                        PublishTreeNode ActionTypes = _budgetManagement.AddTreeNodeFromDoc(Convert.ToString(a.Key),
                            Convert.ToString(a.Value),
                            nameof(clsConstants.BM_Tree_Types.ActionType), false, true);
                        dynamic ActionNames = _budgetManagement.GetBListActionsForDocTree(userId, a.Key, orgId,
                            Convert.ToString(parentNode.id), nonFilteredBlist, budgetYear);
                        foreach (var aa in ActionNames)
                        {
                            PublishTreeNode ActionName = _budgetManagement.AddTreeNodeFromDoc(
                                Convert.ToString(aa.tempId), Convert.ToString(aa.Actions),
                                nameof(clsConstants.BM_Tree_Types.Action), false, true);
                            ActionTypes.items.Add(ActionName);
                        }

                        ServiceAreaAction.Add(ActionTypes);
                    }

                    //New Action Grid
                    IEnumerable<PublishTreeNode> items = null;

                    items = ServiceAreaAction;
                    _bmNewActionHelper.InsertBListDeletedActionSetTable(budgetYearBudMan, builder,
                        userId, items, incBlistIntDesc,
                        nameof(clsConstants.BM_Tree_Types.BlistActionText), orgId, "NewBlistActon_text",
                        Convert.ToString(parentNode.id), levelSetUp);
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        _bmNewActionHelper.InsertActionDescriptions(budgetYearBudMan, builder, userId, items,
                            incIntDesc,
                            nameof(clsConstants.BM_Tree_Types.BlistActionText), budgetPhaseId, showOnlyModified,
                            orgId, incBlistIntDesc, "",
                            insertBookmark: true, bookmarkTitle: currentNode.text, levelSetUp: levelSetUp);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetServiceArea) when
                    currentNode.@checked:
                    _bmStatusHelper.InsertBudgetDescription(builder, userId, 3, orgId,
                        Convert.ToString(parentNode.id));
                    break;
                //if (currentNode.type == clsConstants.BM_Tree_Types.PivotCAB.ToString() && currentNode.@checked)
                //{
                //    //Insert strategy text
                //    _pivotCab.InsertPivotCABGrid(userId, builder, budgetYearBudMan, orgId, Convert.ToString(parentNode.id), currentNode);
                //}
                case nameof(clsConstants.BM_Tree_Types.InvestmentsServiceAreaLvl) when
                    currentNode.@checked:
                {
                    IEnumerable<PublishTreeNode> items = null;
                    if (currentNode.items.Any())
                    {
                        items = currentNode.items;
                        _bmInvestHelper.InsertInvestmentTablesServiceAreaLevel(builder, userId, items,
                            budgetPhaseId,
                            showOnlyModified, incIntDesc, currentNode, incBlistIntDesc);
                    }

                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescIdSid, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty);
                    }

                    break;
                }
                //activity indicate section
                case nameof(clsConstants.BM_Tree_Types.ActivityIndicatorSA) or nameof(clsConstants.BM_Tree_Types.ActivityIndicatorLvl1) when
                    currentNode.@checked:
                    _bmActivityIndicator.InsertIndicatorData(userId, builder, orgId,
                        Convert.ToString(parentNode.id), budgetYearBudMan,
                        currentNode, budgetPhaseId, showOnlyModified, currentNode.orgLevel);
                    //Bug 56727 - Indicator should not be linked to target
                    //insertTarget = true;
                    break;
                case nameof(clsConstants.BM_Tree_Types.GoalsSA) or nameof(clsConstants.BM_Tree_Types.GoalsLvl1) when currentNode.@checked:
                {
                    goalSectionSelected = true;
                    var targetNode = parentNode.items.FirstOrDefault(x =>
                        x.type.Equals(nameof(clsConstants.BM_Tree_Types.TargetSA),
                            StringComparison.InvariantCultureIgnoreCase) ||
                        x.type.Equals(nameof(clsConstants.BM_Tree_Types.TargetLvl1),
                            StringComparison.InvariantCultureIgnoreCase));

                    _bmGoalsandtarget.InsertGoalsSection(userId, builder, orgId, Convert.ToString(parentNode.id),
                        budgetYearBudMan, currentNode, targetNode, true,
                        showOnlyModified, budgetPhaseId, true);
                    insertTarget = true;
                    targetSectionLvl2Selected = true;
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.SaOpexIncGraphlvl1) when
                    currentNode.@checked && publishHelper.GetType() == typeof(WebHelperBm):
                {
                    publishHelper.StartCollapsibleList(currentNode.text, true,
                        _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                    publishHelper.StartCollapsibleListItem(
                        _languageStringsExportCommon["BM_doc_title_SaOpexIncGraph"].LangText);
                    string strAccGraphOne = _BMWebGraphs.GetAccountingGraphOneData(userId, budgetYearBudMan,
                        orgId == "" ? null : orgId, Convert.ToString(parentNode.id), budgetPhaseId);
                    if (strAccGraphOne == string.Empty)
                    {
                        return;
                    }

                    //builder.InsertImage(_graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true));
                    using (var retImage =
                           _graphsExportHelper.ExportAccountingGraphOne(userId, builder, strAccGraphOne, 600, true))
                    {
                        if (retImage != null)
                        {
                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                            builder.InsertImage(imageInfo);
                        }
                    }

                    publishHelper.EndCollapsibleListItem();
                    publishHelper.EndCollapsibleList();
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetProposalStrategySA) when
                    currentNode.@checked:
                    _bmOppHelper.InsertBudgetProposalStrategyData(userId, orgId, parentNode.id, budgetYearBudMan,
                        publishHelper, currentNode);
                    break;
                case nameof(clsConstants.BM_Tree_Types.customNode) when currentNode.@checked:
                    _customNode.InsertNestedCustomNodes(builder, userId, currentNode, budgetPhaseId,
                        showOnlyModified, true, true);
                    break;
                case nameof(clsConstants.BM_Tree_Types.reportingWidget) when
                    currentNode.@checked:
                {
                    if (currentNode.parameters != null && currentNode.parameters.ContainsKey("masterNodeId"))
                    {
                        _bmNewActionHelper.InsertReportingWidget(userId, currentNode.text, _context, builder,
                            budgetYearBudMan, currentNode.parameters.GetValueOrDefault("masterNodeId"),
                            currentNode.id);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByAccountGroupTable) when
                    currentNode.@checked:
                {
                    string desc = "$SERTBL";
                    string nodeDescId = $"{orgId}{parentNode.id}{desc}{currentNode.id}{currentNode.type}";
                    _bmBudBySaHelper.InsertBudgetByAccountGroupTable(userId, builder, orgId,
                        Convert.ToString(parentNode.id), cacheKey, budgetYearBudMan, budgetPhaseId, currentNode,
                        nodeDescId, showOnlyModified, "Type2");
                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescId, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable) when
                    currentNode.@checked:
                {
                    string desc = "$SERTBL";
                    string nodeDescId = $"{orgId}{parentNode.id}{desc}{currentNode.id}{currentNode.type}";
                    _bmBudBySaHelper.InsertBudgetByServiceTable(userId, builder, orgId,
                        Convert.ToString(parentNode.id), templateId, currentNode, nodeDescId, budgetYearBudMan,
                        showOnlyModified, budgetPhaseId, "Type2", cacheKey);
                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescId, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                //Graph for budget by service and budget by service
                case nameof(clsConstants.BM_Tree_Types.BudgetByService) when currentNode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, orgId,
                        Convert.ToString(parentNode.id), isWebHelper);
                    if (jsonStr == string.Empty)
                    {
                        return;
                    }

                    publishHelper.InsertHeading(userId, currentNode.text.Trim(), _context, true);
                    bool divideByMillions =
                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                    if (divideByMillions)
                    {
                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8, false,
                            true);
                    }
                    else
                    {
                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false,
                            true);
                    }

                    dynamic json = JToken.Parse(jsonStr);
                    string graphOneName = "BudgetByService";
                    dynamic jsonRet = new JObject();
                    jsonRet.Add("infile", json);
                    jsonRet.infile.type = graphOneName;
                    jsonRet.infile.numformat = "";

                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                    {
                        if (retImage != null)
                        {
                            //builder.InsertImage(retImage);
                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                            builder.InsertImage(imageInfo);
                        }
                    }

                    publishHelper.InsertParagraphBreak();
                    //line break between graph and table
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        publishHelper.InsertLineBreak();
                    }

                    break;
                }
                //Graph for budget by service and budget by Department
                case nameof(clsConstants.BM_Tree_Types.BudgetByDepartment) when
                    currentNode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr = _budgetManagement.GetBudgetByDepartment(userId, budgetYearBudMan, orgId,
                        Convert.ToString(parentNode.id), isWebHelper, budgetPhaseId);
                    if (jsonStr == string.Empty)
                    {
                        return;
                    }

                    publishHelper.InsertHeading(userId, currentNode.text.Trim(), _context, true);

                    bool divideByMillions =
                        _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_organization_title");
                    if (divideByMillions)
                    {
                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8, false,
                            true);
                    }
                    else
                    {
                        publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8, false,
                            true);
                    }

                    dynamic json = JToken.Parse(jsonStr);
                    string graphOneName = "BudgetByDepartment";
                    dynamic jsonRet = new JObject();
                    jsonRet.Add("infile", json);
                    jsonRet.infile.type = graphOneName;
                    jsonRet.infile.numformat = "";

                    String mergedData = JsonConvert.SerializeObject(jsonRet);

                    using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                    {
                        if (retImage != null)
                        {
                            //builder.InsertImage(retImage);
                            var imageInfo = _utility.ConvertImagetoByte(retImage);
                            builder.InsertImage(imageInfo);
                        }
                    }

                    publishHelper.InsertParagraphBreak();
                    //line break between graph and table
                    if (publishHelper.GetType() != typeof(WebHelperBm))
                    {
                        publishHelper.InsertLineBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByOrgTable) when currentNode.@checked:
                {
                    string desc = "$SERTBL";
                    string nodeDescId = $"{orgId}{parentNode.id}{desc}{currentNode.id}{currentNode.type}";
                    _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, orgId,
                        Convert.ToString(parentNode.id), templateId, currentNode, nodeDescId, budgetYearBudMan,
                        showOnlyModified, budgetPhaseId, "Type2", cacheKey, false);
                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescId, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByServiceTable_Chapter) when
                    currentNode.@checked:
                {
                    string desc = "$SERTBL";
                    string nodeDescId = $"{orgId}{parentNode.id}{desc}{currentNode.id}{currentNode.type}";
                    _bmBudBySaHelper.InsertBudgetByOrganizationTable(userId, builder, orgId,
                        Convert.ToString(parentNode.id), templateId, currentNode, nodeDescId, budgetYearBudMan,
                        showOnlyModified, budgetPhaseId, "Type2", cacheKey, false, true);
                    if (currentNode.isEditableNode)
                    {
                        _customNode.InsertNodeDescription(builder, userId, nodeDescId, currentNode.Uid,
                            budgetYearBudMan, showOnlyModified ? budgetPhaseId : string.Empty, false, true);
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetByService_Chapter) when
                    currentNode.@checked:
                {
                    bool isWebHelper = publishHelper.GetType() == typeof(WebHelperBm);
                    string jsonStr = _budgetManagement.GetBudgetByService(userId, budgetYearBudMan, orgId,
                        Convert.ToString(parentNode.id), isWebHelper, true);
                    publishHelper.InsertHeading(userId, currentNode.text.Trim(), _context, true);
                    if (!string.IsNullOrEmpty(jsonStr))
                    {
                        //publishHelper.InsertHeading(userId, StyleIdentifier.Heading2, cNode.text, _context, true, cNode.text);

                        bool divideByMillions =
                            _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
                        if (divideByMillions)
                        {
                            publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_million"].LangText, 8,
                                false, true);
                        }
                        else
                        {
                            publishHelper.InsertText(langStrings33["InfoGraphics_divedBy_thousand"].LangText, 8,
                                false, true);
                        }

                        dynamic json = JToken.Parse(jsonStr);
                        string graphOneName = "BudgetByService_Chapter";
                        dynamic jsonRet = new JObject();
                        jsonRet.Add("infile", json);
                        jsonRet.infile.type = graphOneName;
                        jsonRet.infile.numformat = "";

                        String mergedData = JsonConvert.SerializeObject(jsonRet);

                        using (var retImage = publishHelper.GenerateGraph(mergedData, 800, graphOneName))
                        {
                            if (retImage != null)
                            {
                                //builder.InsertImage(retImage);
                                var imageInfo = _utility.ConvertImagetoByte(retImage);
                                builder.InsertImage(imageInfo);
                            }
                        }

                        publishHelper.InsertParagraphBreak();
                    }

                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl2) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl1) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl1) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl1) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl1) when
                    currentNode.@checked:
                case nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl2) when
                    currentNode.@checked:
                {
                    bool isBlistInvestment =
                        currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) ||
                        currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl2) ||
                        currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailStatusLvl1) ||
                        currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl1);
                    List<int> budgetPhaseChangeId =
                        _investment.GetChangeDataUsingBudgetPhase(budgetPhaseId, userId, showOnlyModified,
                            budgetYearBudMan);
                    if (showOnlyModified)
                    {
                        publishHelper.InsertHeading(userId,
                            langStringValuesDocExport["doc_inv_title_withOnlyChange"].LangText,
                            _context);
                    }
                    else
                    {
                        var mLInvdata = _economicPlanExport.GetInvestmentTotalProjectFinNetYears(
                            userDetails.tenant_id, orgId, Convert.ToString(parentNode.id), budgetYearBudMan,
                            ReportType.MultiLevel, budgetPhaseChangeId, userId);
                        var mLInvdataReq = _economicPlanExport.GetInvestmentTotalProjectFinNetYearsRequiredInv(
                            userDetails.tenant_id, orgId, Convert.ToString(parentNode.id), budgetYearBudMan,
                            ReportType.MultiLevel, budgetPhaseChangeId, userId);
                        if (currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2))
                        {
                            if (mLInvdataReq != null && mLInvdataReq.Count() > 0)
                            {
                                publishHelper.InsertHeading(userId,
                                    langStringValuesDocExport["doc_inv_req_title"].LangText, _context,
                                    true);
                            }
                        }
                        // added the condition for the story : 172423 (getting two headings for the investment detail node level 2)
                        else
                        {
                            if (mLInvdata != null && mLInvdata.Count() > 0 && currentNode.type !=
                                nameof(clsConstants.BM_Tree_Types.InvestmentMainDetailLvl2))
                            {
                                publishHelper.InsertHeading(userId,
                                    langStringValuesDocExport["doc_inv_title"].LangText, _context, true);
                            }
                        }
                    }

                    _bmInvestHelper.InsertInvestmentTablesFlat(builder, userId, Convert.ToString(parentNode.id),
                        budgetPhaseId, showOnlyModified, currentNode, budgetYearBudMan, incIntDesc, false,
                        isBlistInvestment, incBlistIntDesc, "multilevel", orgId);
                    break;
                }
                case nameof(clsConstants.BM_Tree_Types.BudgetPerFunctAreaAndAccntGrpForOrg) when
                    currentNode.@checked:
                    _FinplanYearlyBudgetSummaryHelper.Initialize(userId, builder, budgetYearBudMan, currentNode)
                        .GetAwaiter().GetResult();
                    _FinplanYearlyBudgetSummaryHelper
                        .InsertFpBudgetbyFunctionalArea(orgVersionContent, parentNode.id).GetAwaiter().GetResult();
                    break;
                default:
                {
                    if ((currentNode.type == nameof(clsConstants.BM_Tree_Types.TargetSA) ||
                         currentNode is { type: nameof(clsConstants.BM_Tree_Types.TargetLvl1), @checked: true }) &&
                        currentNode.@checked && insertTarget == false)
                    {
                        _bmGoalsandtarget.InsertTargetSection(userId, builder, orgId, Convert.ToString(parentNode.id),
                            budgetYearBudMan, currentNode, null, true,
                            showOnlyModified, budgetPhaseId, goalSectionSelected);
                        targetSectionLvl2Selected = true;
                    }

                    else if (currentNode.type == nameof(clsConstants.BM_Tree_Types.TargetPlanTextSA) &&
                             currentNode.@checked || targetSectionLvl2Selected)
                    {
                        if (publishHelper.GetType() != typeof(WebHelperBm))
                        {
                            _bmGoalsandtarget.InsertPlanTargetText(userId, builder, orgId,
                                Convert.ToString(parentNode.id), budgetYearBudMan, currentNode, null,
                                showOnlyModified, budgetPhaseId, goalSectionSelected, targetSectionLvl2Selected);
                        }

                        targetSectionLvl2Selected = false;
                    }

                    else if (currentNode.type == nameof(clsConstants.BM_Tree_Types.BPStrategies) &&
                        anyChildrenChecked || currentNode.parameters.ContainsKey("isOpportunityAssessment"))
                    {
                        if (currentNode.parameters.ContainsKey("isOpportunityAssessment"))
                        {
                            _bmOppHelper.InsertStrategyDataForBudgetProposalDocument(userId, budgetYearBudMan,
                                publishHelper, new List<PublishTreeNode> { currentNode }, true);
                        }
                        else
                        {
                            publishHelper.InsertHeading(userId, currentNode.text, _context, false);
                            _bmOppHelper.InsertStrategyDataForBudgetProposalDocument(userId, budgetYearBudMan,
                                publishHelper, currentNode.items);
                        }
                    }

    
                    else if (currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentsStatus) &&
                             anyChildrenChecked || currentNode.type == nameof(clsConstants.BM_Tree_Types.Investments) &&
                             anyChildrenChecked)
                    {
                        bool isBlistInvestment =
                            currentNode.type == nameof(clsConstants.BM_Tree_Types.InvestmentsStatus);
                        List<int> budgetPhaseChangeId =
                            _investment.GetChangeDataUsingBudgetPhase(budgetPhaseId, userId, showOnlyModified,
                                budgetYearBudMan);
                        foreach (var item in currentNode.items)
                        {
                            publishHelper.StartNewLevel(item);
                            publishHelper.StartNode(item);
                            if (item.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainTableLvl2) &&
                                item.@checked ||
                                item.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2) &&
                                item.@checked)
                            {
                                if (showOnlyModified)
                                {
                                    publishHelper.InsertHeading(userId,
                                        langStringValuesDocExport["doc_inv_title_withOnlyChange"].LangText,
                                        _context);
                                }
                                else
                                {
                                    var mLInvdata = _economicPlanExport.GetInvestmentTotalProjectFinNetYears(
                                        userDetails.tenant_id, orgId, Convert.ToString(parentNode.id), budgetYearBudMan,
                                        ReportType.MultiLevel, budgetPhaseChangeId, userId);
                                    var mLInvdataReq =
                                        _economicPlanExport.GetInvestmentTotalProjectFinNetYearsRequiredInv(
                                            userDetails.tenant_id, orgId, Convert.ToString(parentNode.id),
                                            budgetYearBudMan, ReportType.MultiLevel, budgetPhaseChangeId, userId);
                                    if (item.type == nameof(clsConstants.BM_Tree_Types.InvestmentMainStatusTableLvl2))
                                    {
                                        if (mLInvdataReq != null && mLInvdataReq.Count() > 0)
                                        {
                                            publishHelper.InsertHeading(userId,
                                                langStringValuesDocExport["doc_inv_req_title"].LangText,
                                                _context, true);
                                        }
                                    }
                                    else
                                    {
                                        if (mLInvdata != null && mLInvdata.Count() > 0)
                                        {
                                            publishHelper.InsertHeading(userId,
                                                langStringValuesDocExport["doc_inv_title"].LangText,
                                                _context, true);
                                        }
                                    }
                                }
                            }

                            publishHelper.EndCurrentLevel();
                            publishHelper.EndNode(item);
                        }

                        _bmInvestHelper.InsertInvestmentTables(builder, userId, Convert.ToString(parentNode.id),
                            currentNode.items, budgetPhaseId, showOnlyModified, currentNode, budgetYearBudMan,
                            incIntDesc, false, isBlistInvestment, incBlistIntDesc, "multilevel", orgId);
                    }
                    break;
                }
            }

            publishHelper.EndCurrentLevel();
            publishHelper.EndNode(currentNode);
        }
    }
}