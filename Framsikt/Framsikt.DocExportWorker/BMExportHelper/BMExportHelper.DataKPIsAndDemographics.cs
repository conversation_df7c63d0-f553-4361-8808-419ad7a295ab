using Aspose.Words;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.DocExportWorker.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json.Linq;

namespace Framsikt.DocExportWorker;

public partial class BmExportHelper
{
    private void InsertKostraCityChallenge(string userId, DocumentBuilder builder, int budgetYear,
        string templateId)
    {
        InsertKostraCityChallengeSection(builder, userId, templateId, budgetYear - 1);
    }


    private void InsertStatusFinKPI(string userId, DocumentBuilder builder, int budgetYear,
        PublishTreeNode exportRoot,
        string templateId, bool showOnlyModified, string budgetPhaseId)
    {
        InsertKpiSection(builder, userId, templateId, budgetYear);
    }


    private void InsertCostReductionPotential(string userId, DocumentBuilder builder, int budgetYear,
        PublishTreeNode exportRoot,
        string templateId, bool showOnlyModified, string budgetPhaseId, List<string> treePath)
    {
        clsConfig config = _utility.GetSectionConfig(userId, Guid.Parse(templateId), null,
            clsConstants.GetSectionConfigConstants["KostraMainCRP"]);
        config.templateId = Guid.Parse(templateId);
        InsertCostReductionPotentialSection(builder, userId, config, templateId, budgetYear, exportRoot,
            showOnlyModified, budgetPhaseId, treePath);
    }


    private void InsertOverallDemography(DocumentBuilder builder, string user, string templateId, int budYear,
        PublishTreeNode ind,
        bool showOnlyModified, string budgetPhaseId, List<string> treePath, bool incIntDesc)
    {
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
        List<string> sectionConfig = _kostraData.GetTabsIncludedInDoc(templateId, user, "po");
        string expLang = _utility.GetUserDetails(user).language_preference;
        Dictionary<string, clsLanguageString> LanguageStrings_export_common =
            _utility.GetLanguageStrings(expLang, user, "docexport");
        //publishHelper.InsertPageTitle(builder, user, StyleIdentifier.Heading1, LanguageStrings_export_common["doc_bm_pop_fct_title"].LangText);
        if (sectionConfig.Any())
        {
            builder.InsertBreak(BreakType.LineBreak);
            publishHelper.InsertHeading(user, ind.text, _context, true);
            if (!ind.type.Equals("customNode", StringComparison.InvariantCultureIgnoreCase) && ind.isEditableNode)
            {
                _customNode.InsertNodeDescription(builder, user, ind, treePath, budYear,
                    showOnlyModified ? budgetPhaseId : string.Empty, true, false);
            }
        }

        InsertPopStatMainGraph(user, builder, publishHelper, templateId, budYear, ind, showOnlyModified,
            budgetPhaseId, incIntDesc);
    }


    private void InsertPopulationDevelopmentSection(DocumentBuilder builder, string user, string templateId,
        int budYear, PublishTreeNode ind, bool showOnlyModified, string budgetPhaseId, List<string> treePath,
        bool incIntDesc)
    {
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(user, builder, _docStyle, _context, _container);
        List<string> sectionConfig = _kostraData.GetTabsIncludedInDoc(templateId, user, "po");

        string indCode = ind.id;
        string name = ind.text;
        builder.ParagraphFormat.ClearFormatting();

        if (sectionConfig.Any() ||
            publishHelper.GetType() == typeof(WordHelper) && _utility.AreAnyChildrenChecked(ind))
        {
            publishHelper.InsertHeading(user, name, _context, true);

            if (!ind.type.Equals("customNode", StringComparison.InvariantCultureIgnoreCase) && ind.isEditableNode)
            {
                _customNode.InsertNodeDescription(builder, user, ind, treePath, budYear,
                    showOnlyModified ? budgetPhaseId : string.Empty, true, false);
            }
        }

        //loop through each config section
        bool insertLinebreakBeforeCustomNode = false;
        foreach (var configSection in ind.items)
        {
            publishHelper.StartNewLevel(configSection);
            if (configSection.items.Any(x => x.@checked))
            {
                insertLinebreakBeforeCustomNode = true;
                //Insert config section header
                if (configSection.type != "customNode")
                {
                    publishHelper.InsertHeading(user, configSection.text, _context);
                }

                switch (configSection.type)
                {
                    case "PopulationStatHistorySection":
                        generatePopStatDetialGraph(user, builder, templateId, indCode, "PopStatHistoryPopulation",
                            nameof(PublishSection.GetSection.POPSTATHIS) + "_" + indCode, budYear - 1,
                            configSection, incIntDesc, budgetPhaseId, showOnlyModified);
                        break;

                    case "PopStatHistoryGrowthSection":
                        generatePopStatDetialGraph(user, builder, templateId, indCode, "PopStatHistoryGrowth",
                            nameof(PublishSection.GetSection.POPSTATHISGROWTH) + "_" + indCode, budYear - 1,
                            configSection, incIntDesc, budgetPhaseId, showOnlyModified);
                        break;

                    case "PopStatForecastSection":
                        generatePopStatDetialGraph(user, builder, templateId, indCode, "PopStatForecastPopulation",
                            nameof(PublishSection.GetSection.POPSTATFRCSTPOP) + "_" + indCode, budYear - 1,
                            configSection, incIntDesc, budgetPhaseId, showOnlyModified);
                        break;

                    case "PopStatForecastGrowthSection":
                        generatePopStatDetialGraph(user, builder, templateId, indCode, "PopStatForecastGrowth",
                            nameof(PublishSection.GetSection.POPSTATFRCSTGRWTH) + "_" + indCode, budYear - 1,
                            configSection, incIntDesc, budgetPhaseId, showOnlyModified);
                        break;

                    case "PopStatForecastExpenseSection":
                        clsConfig configData = _utility.GetSectionConfig(user, new Guid(templateId), "po",
                            clsConstants.GetPopStatTabs["PopStatForecastPopulationExpense"]);
                        clsPopStats popStats = new clsPopStats
                        {
                            history = configData.sectionConfig.history,
                            forecast = configData.sectionConfig.forecast,
                            templateId = new Guid(templateId),
                            indicatorCode = indCode
                        };
                        string data =
                            pPopStat.GetPopForecastByIndicatorTypeForPopulationExpense(user, popStats, budYear - 1);
                        bool isGraphIncluded = false;
                        bool isTableIncluded = false;
                        //Insert graph/table based on user selection
                        foreach (var contentType in configSection.items)
                        {
                            publishHelper.StartNewLevel(contentType);

                            if (contentType.type == "PopulationGraph" && contentType.@checked)
                            {
                                isGraphIncluded = true;
                                InsertPopFctByIndicatorGraph(user, data, builder,
                                    nameof(PublishSection.GetSection.POPSTATPOPEXP) + "_" + indCode);
                            }

                            if (contentType.type == "PopulationTable" && contentType.@checked)
                            {
                                if (isGraphIncluded)
                                {
                                    publishHelper.InsertLineBreak();
                                }

                                InsertPopulationForecastGrid(user, data, builder, _context, indCode,
                                    "PopStatForecastPopulationExpense");
                            }

                            if (contentType.type == "customNode" && contentType.@checked)
                            {
                                if (isGraphIncluded || isTableIncluded)
                                {
                                    publishHelper.InsertLineBreak();
                                }

                                _customNode.InsertCustomNode(builder, user, Guid.Parse(configSection.id),
                                    budgetPhaseId, showOnlyModified, true, false, false, null, null, incIntDesc);
                            }

                            publishHelper.EndCurrentLevel();
                        }

                        break;
                }
            }

            if (configSection.type.ToLower() == "populationtext" && configSection.@checked)
            {
                insertLinebreakBeforeCustomNode = true;
                TenantDBContext kDbContext = _utility.GetTenantDBContext();
                UserData userDetails = _utility.GetUserDetails(user);

                var populationTextInfo = kDbContext.tko_ind_explanation.FirstOrDefault(x =>
                    x.fk_tenant_id == userDetails.tenant_id && x.year == budYear - 1
                                                            && x.fk_template_id.ToString() == templateId &&
                                                            x.fk_indicator_code == ind.id);
                string populationText =
                    populationTextInfo == null || string.IsNullOrEmpty(populationTextInfo.description)
                        ? string.Empty
                        : populationTextInfo.description;

                publishHelper.InsertDescriptionCk5(populationText, user);
            }

            if (configSection.type == "customNode" && configSection.@checked)
            {
                if (insertLinebreakBeforeCustomNode)
                {
                    publishHelper.InsertLineBreak();
                }

                _customNode.InsertCustomNode(builder, user, Guid.Parse(configSection.id), budgetPhaseId,
                    showOnlyModified, true, false, false, null, null, incIntDesc);
            }

            publishHelper.EndCurrentLevel();
        }

        //Insert Evaluation
        //int year = sectionConfig.Count > 0 ? budYear : pPopStat.GetBudgetYear(user);
        //int year = pPopStat.GetBudgetYear(user);
        //clsIndicatorEvaluation eval = _kostraData.GetIndicatorEvaluation(user, year, indCode, templateId);
        //InsertEESection(builder, eval, user);
    }


    //#endregion 19706 Story

    private void KeyfigForDocument(string userId, DocumentBuilder builder, int budgetYear, string nodeText)
    {
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
        //Bug #82603 - No data in web publish , but there is data in word document
        dynamic keyfigs = _cab.GetKeyFiguresData(userId, budgetYear);
        List<KeyFigures> data = keyfigs.data.ToObject<List<KeyFigures>>();
        int rowCount = data.Count();
        int counter = 1;
        if (data.Any())
        {
            publishHelper.StartTable("DocumentKeyFig", "", "");

            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                    "BudgetManagement");
            publishHelper.InsertHeading(userId, nodeText, _context, true);
            List<ColumnDetails> lstColumnDetails = new List<ColumnDetails>();
            List<string> lstColumnDataDetails = new List<string>();

            ColumnDetails cellTemplate = publishHelper.GetTemplate(CellTypes.Information);

            lstColumnDetails = new List<ColumnDetails>();
            lstColumnDataDetails = new List<string>();
            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            cellTemplate.Column = 0;
            cellTemplate.PreferredWidth = 60;
            cellTemplate.Alignment = ParagraphAlignment.Left;
            cellTemplate.BottomBorder = 1;
            cellTemplate.Fontsize = 9;
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(langStringFinPlan.FirstOrDefault(x => x.Key == "doc_keyfig_col1").Value
                .LangText);

            cellTemplate = publishHelper.GetTemplate(CellTypes.Header1);
            cellTemplate.Column = 0;
            cellTemplate.PreferredWidth = 20;
            cellTemplate.Alignment = ParagraphAlignment.Right;
            cellTemplate.BottomBorder = 1;
            cellTemplate.Fontsize = 9;
            lstColumnDetails.Add(cellTemplate);
            lstColumnDataDetails.Add(langStringFinPlan.FirstOrDefault(x => x.Key == "doc_keyfig_col2").Value
                .LangText);

            publishHelper.CreateRow(lstColumnDetails, lstColumnDataDetails, true);

            foreach (var item in data)
            {
                List<ColumnDetails> gridColDet = new List<ColumnDetails>()
                {
                    new ColumnDetails()
                    {
                        Column = 1, Padding = 0, IsBold = false, LeftBorder = 0, RightBorder = 0, TopBorder = 0,
                        BottomBorder = 0,
                        Fontsize = 9, FontColor = FramsiktColors.DefaultTextColor,
                        Alignment = ParagraphAlignment.Left, WrapText = false, PreferredWidth = 70
                    },
                    new ColumnDetails()
                    {
                        Column = 1, Padding = 0, IsBold = false, LeftBorder = 0, RightBorder = 0, TopBorder = 0,
                        BottomBorder = 0,
                        Fontsize = 9, FontColor = FramsiktColors.DefaultTextColor,
                        Alignment = ParagraphAlignment.Right, WrapText = false, PreferredWidth = 30
                    }
                };
                if (rowCount == counter)
                {
                    gridColDet.ForEach(x => x.BottomBorder = 1);
                }

                List<string> gridData = new List<string>()
                {
                    item.KeyFigureText,
                    item.Value,
                };
                publishHelper.CreateRow1(gridColDet, gridData, false, RowType.Detail);
                counter++;
            }

            publishHelper.EndTable();
        }
    }


    private void InsertFinancialSustainabilityRadarGraphAndTableForKPI(string userId, int budgetYear,
        DocumentBuilder builder, PublishTreeNode childNode)
    {
        IPublishHelper publishHelper =
            PublishHelperFactory.CreatePublishHelper(userId, builder, _docStyle, _context, _container);
        try
        {
            //98833
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dBContext = _utility.GetTenantDBContext();
            Dictionary<string, clsLanguageString> langStringsDocExport =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
            Dictionary<string, clsLanguageString> langStringValuesNumberFormat =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                    "NumberFormats");
            Dictionary<string, clsLanguageString> langStringsBM =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name,
                    "BudgetManagement");
            string screenId = "FP";
            int goalRelativeDefaultValue = 100;
            List<string> kpiServices = new List<string>() { };
            var kpiData = (from a in dBContext.gmd_kpi_setup
                join b in dBContext.tmd_kpi_setup on new { a = a.kpi_id.ToString() } equals new { a = b.service_id }
                where b.fk_tenant_id == userDetails.tenant_id && b.use_for_radar_chart
                select new
                {
                    goalValue = b.objective,
                    kpiId = b.service_id,
                    kpiName = b.fk_language_id,
                    hightargetFlag = a.high_target_flag
                });

            Dictionary<string, string> kpiNumberTypeData;
            kpiNumberTypeData =
                _utility.GetNumberTypeForChartForKPIs(userId, kpiData.Select(x => x.kpiId).ToList());

            dynamic year1RelativeData = new JArray();
            dynamic year4RelativeData = new JArray();
            dynamic goalRelativeData = new JArray();
            dynamic categoriesData = new JArray();
            Dictionary<string, List<decimal>> radarGraphData = new Dictionary<string, List<decimal>>();
            //Grid data
            var colorsForKPIs = _utility.GetColors(userId, ColorsFor.Graph);

            JArray gridDataRealValues = new JArray();
            JArray gridDataRelativeValues = new JArray();
            foreach (var item in kpiData)
            {
                dynamic gridRealDataRow = new JObject();
                dynamic gridRelativeDataRow = new JObject();
                /*real Data start */
                gridRealDataRow.name = langStringsDocExport[item.kpiName].LangText;
                gridRealDataRow.clolor = colorsForKPIs["0"];
                gridRealDataRow.type = "column";
                dynamic gdDataArray = new JArray();
                List<decimal> KPIValues =
                    _kpiData.GetKPIYearValues(userId, budgetYear, Convert.ToInt32(item.kpiId), screenId);
                gdDataArray.Add(item.goalValue);
                gdDataArray.Add(KPIValues[0]);
                gdDataArray.Add(KPIValues[3]);
                gridRealDataRow.Add("data", gdDataArray);
                /*real data end*/
                /*relative data start*/
                gridDataRealValues.Add(gridRealDataRow);
                gridRelativeDataRow.name = langStringsDocExport[item.kpiName].LangText;
                gridRelativeDataRow.clolor = colorsForKPIs["0"];
                gridRelativeDataRow.type = "column";
                gdDataArray = new JArray();
                gdDataArray.Add(goalRelativeDefaultValue);
                var numbertype = kpiNumberTypeData.FirstOrDefault(x => x.Key == item.kpiId);
                decimal realGoalDecimalValue = GetFormattedValue(item.goalValue,
                    numbertype.Key == null ? string.Empty : numbertype.Value);
                decimal year1DecimalValue = GetFormattedValue(KPIValues[0],
                    numbertype.Key == null ? string.Empty : numbertype.Value);
                decimal year4DecimalValue = GetFormattedValue(KPIValues[3],
                    numbertype.Key == null ? string.Empty : numbertype.Value);
                gdDataArray.Add(CalculateRelativeValueForRadarGraph(year1DecimalValue, realGoalDecimalValue,
                    item.hightargetFlag, goalRelativeDefaultValue));
                gdDataArray.Add(CalculateRelativeValueForRadarGraph(year4DecimalValue, realGoalDecimalValue,
                    item.hightargetFlag, goalRelativeDefaultValue));
                gridRelativeDataRow.Add("data", gdDataArray);
                gridDataRelativeValues.Add(gridRelativeDataRow);
                /*relative data end*/
                radarGraphData.Add(item.kpiName, KPIValues);
                year1RelativeData.Add(CalculateRelativeValueForRadarGraph(year1DecimalValue, realGoalDecimalValue,
                    item.hightargetFlag, goalRelativeDefaultValue));
                year4RelativeData.Add(CalculateRelativeValueForRadarGraph(year4DecimalValue, realGoalDecimalValue,
                    item.hightargetFlag, goalRelativeDefaultValue));
                goalRelativeData.Add(goalRelativeDefaultValue);
                categoriesData.Add(langStringsDocExport[item.kpiName].LangText);
            }

            InsertRadarGraph(userId, builder, goalRelativeData, year1RelativeData, year4RelativeData,
                categoriesData, budgetYear);

            InsertFinancialSustainabilityRealTable(userId, builder, childNode.id, kpiNumberTypeData, kpiData,
                gridDataRealValues, budgetYear);
            InsertFinancialSustainabilityRelativeTable(userId, builder, childNode.id, kpiNumberTypeData, kpiData,
                gridDataRelativeValues, budgetYear);
        }
        catch (Exception e)
        {
            publishHelper.InsertError("error in financial sustainability radar graph or table" + e);
        }
    }
}