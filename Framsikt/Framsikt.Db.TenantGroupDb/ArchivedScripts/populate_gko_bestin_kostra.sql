
DECLARE @year INT
SET @year = (SELECT datepart(year, getdate()))


INSERT INTO gko_bestin_kostra (fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, aggregation_type, type, updated)  
select  e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value, min(g.fk_region_code) as fk_region_code, ' ' as  region_name, 'min' as aggregation_type, 'city' as type,  getdate() as updated
from
(SELECT t.fk_indicator_code, s.kostra_group, t.year, r.value_type, MIN(t.indicator_value) as indicator_value 
FROM gmd_kostra_lables r, gco_municipalities s, gko_kostra_data t
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year IN (@year-4, @year-3, @year-2, @year-1)
AND r.active = 1
and t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.pk_indicator_code = t.fk_indicator_code
GROUP BY t.fk_indicator_code, s.kostra_group, t.year, r.value_type ) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
left outer join gko_kostra_data g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code
and e.indicator_value = g.indicator_value
AND g.year = e.year
where g.fk_region_code is not null 
GROUP BY e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value

INSERT INTO gko_bestin_kostra (fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, aggregation_type, type, updated)  
select  e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value, min(g.fk_region_code) as fk_region_code, ' ' as  region_name, 'max' as aggregation_type, 'city' as type,  getdate() as updated
from
(SELECT t.fk_indicator_code, s.kostra_group, t.year, r.value_type, MAX(t.indicator_value) as indicator_value 
FROM gmd_kostra_lables r, gco_municipalities s, gko_kostra_data t
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year IN (@year-4, @year-3, @year-2, @year-1)
AND r.active = 1
and t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.pk_indicator_code = t.fk_indicator_code
GROUP BY t.fk_indicator_code, s.kostra_group, t.year, r.value_type ) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
left outer join gko_kostra_data g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code
and e.indicator_value = g.indicator_value
AND g.year = e.year
where g.fk_region_code is not null 
GROUP BY e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value



INSERT INTO gko_bestin_kostra (fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, aggregation_type, type, updated)  
select  e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value, min(g.fk_region_code) as fk_region_code, ' ' as  region_name, 'min' as aggregation_type, 'corp' as type,  getdate() as updated
from
(SELECT t.fk_indicator_code, s.kostra_group, t.year, r.value_type, MIN(t.indicator_value) as indicator_value 
FROM gmd_kostra_lables r, gco_municipalities s, gko_kostra_data_corp t
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year IN (@year-4, @year-3, @year-2, @year-1)
AND r.active = 1
and t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.pk_indicator_code = t.fk_indicator_code
GROUP BY t.fk_indicator_code, s.kostra_group, t.year, r.value_type ) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
left outer join gko_kostra_data_corp g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code
and e.indicator_value = g.indicator_value
AND g.year = e.year
where g.fk_region_code is not null 
GROUP BY e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value



INSERT INTO gko_bestin_kostra (fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, aggregation_type, type, updated)  
select  e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value, min(g.fk_region_code) as fk_region_code, ' ' as  region_name, 'max' as aggregation_type, 'corp' as type,  getdate() as updated
from
(SELECT t.fk_indicator_code, s.kostra_group, t.year, r.value_type, MAX(t.indicator_value) as indicator_value 
FROM gmd_kostra_lables r, gco_municipalities s, gko_kostra_data_corp t
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year IN (@year-4, @year-3, @year-2, @year-1)
AND r.active = 1
and t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.pk_indicator_code = t.fk_indicator_code
GROUP BY t.fk_indicator_code, s.kostra_group, t.year, r.value_type ) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
left outer join gko_kostra_data_corp g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code
and e.indicator_value = g.indicator_value
AND g.year = e.year
where g.fk_region_code is not null 
GROUP BY e.fk_indicator_code, e.kostra_group, e.year, e.value_type, e.indicator_value

go

UPDATE gko_bestin_kostra SET region_name = b.region_name 
FROM gko_bestin_kostra a, gco_region_codes b
WHERE a.fk_region_code = b.pk_region_code

GO

INSERT INTO gko_bestin_kostra (fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, aggregation_type, type, updated)  
SELECT fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, 'best', type, updated 
FROM gko_bestin_kostra 
WHERE aggregation_type = 'MIN' AND value_type = 'ASC'
go

INSERT INTO gko_bestin_kostra (fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, aggregation_type, type, updated)  
SELECT fk_indicator_code, kostra_group, year, value_type, indicator_value, fk_region_code, region_name, 'best', type, updated FROM gko_bestin_kostra 
WHERE aggregation_type = 'MAX' AND value_type = 'DESC'
go

