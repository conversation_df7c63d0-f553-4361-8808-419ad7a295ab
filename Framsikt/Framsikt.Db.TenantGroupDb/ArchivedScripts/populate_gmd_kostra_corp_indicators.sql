
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109244', N'1096095', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsresultat i prosent av brutto driftsinntekter', CAST(0x0000A4B9002486E1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109275', N'1096126', N'04937', N'A1. Konsern - Finansiel<PERSON> nøkkeltall og adm., styring og fellesutg. - n<PERSON><PERSON><PERSON><PERSON><PERSON> (K)', N'Netto driftsresultat i prosent av brutto driftsinntekter', CAST(0x0000A4B9002487F8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109303', N'1096154', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Overskudd før lån og avsetninger i % av brutto driftsinntekter', CAST(0x0000A4B900248844 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109334', N'1096185', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto finans og avdrag i % av brutto driftsinntekter', CAST(0x0000A4B900248838 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109364', N'1096215', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto avdrag i prosent av brutto driftsinntekter', CAST(0x0000A4B9002487F3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109395', N'1096246', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto finans i prosent av brutto driftsinntekter', CAST(0x0000A4B900248836 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109425', N'1096276', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Netto tap/gevinst på finansielle instrumenter i prosent av brutto driftsinntekter', CAST(0x0000A4B9002486AA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109517', N'1096399', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Mva-kompensasjon påløpt i investeringsregnskapet, i % av brutto driftsintekter', CAST(0x0000A4B9002487EF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109548', N'1096368', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Mva-kompensasjon påløpt i driftsregnskapet, i % av brutto driftsinntekter', CAST(0x0000A4B9002487EC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1109578', N'1096429', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Disposisjonsfond i prosent av brutto driftsinntekter', CAST(0x0000A4B900248790 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1676831', N'1100843', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Antall A-objekter', CAST(0x0000A4B90024926B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1676862', N'1100874', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Antall piper pr. innbygger', CAST(0x0000A4B900249279 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1676890', N'1100902', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Årsgebyr for feiing og tilsyn (gjelder rapporteringsåret +1)', CAST(0x0000A4B900249282 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1676921', N'1100933', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Anslått erstatning til bygningsbranner pr. innbygger , kroner', CAST(0x0000A4B900249269 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1676951', N'1100963', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Netto driftsutgifter til funksjon 338 pr. innbygger', CAST(0x0000A4B90024929E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1676982', N'1100994', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Netto driftsutgifter til funksjon 338 i prosent av funksjon 338 + 339', CAST(0x0000A4B90024929B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677012', N'1101024', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Årsverk til funksjon 338 pr. 1000 innbyggere', CAST(0x0000A4B90024928A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677043', N'1656773', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Andel A-objekter som har fått tilsyn', CAST(0x0000A4B900249261 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677074', N'1101086', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Andel piper feiet', CAST(0x0000A4B900249266 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677104', N'1656802', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Andel personer med kompetanse som fører tilsyn med særskilte brannobjekter', CAST(0x0000A4B900249263 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677135', N'1101147', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Årsverk av feier pr. 1000 innbyggere funksjon 338', CAST(0x0000A4B900249285 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677165', N'1101177', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Antall bygningsbranner pr. 1000 innbyggere', CAST(0x0000A4B900249273 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677562', N'1100112', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Netto driftsutgifter til kommunalt disponerte boliger per innbygger i kr', CAST(0x0000A4B900249258 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677593', N'1100143', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Netto driftsutgifter til boligformål pr innbygger i kroner', CAST(0x0000A4B90024924F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677621', N'1100172', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Netto driftsutgifter, boligformål, i prosent av samlede netto driftsutgifter', CAST(0x0000A4B90024925C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677652', N'1100203', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Brutto investeringsutgifter til boligformål per innbygger i kroner', CAST(0x0000A4B900249228 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677682', N'1100233', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Kommunalt disponerte boliger per 1000 innbyggere', CAST(0x0000A4B900249231 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677713', N'1100264', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Kommunalt eide boliger som andel av totalt antall kommunalt disp. boliger', CAST(0x0000A4B90024923A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677743', N'1100294', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel kommunale boliger som er tilgjengelige for rullestolbrukere', CAST(0x0000A4B9002491C5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677805', N'1100325', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Brutto driftsutgifter per kommunalt disponert bolig', CAST(0x0000A4B900249224 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677835', N'1100356', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Herav: lønnsutgifter per eid kommunal bolig', CAST(0x0000A4B90024922F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677866', N'1100386', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall søknader per 1000 innbyggere', CAST(0x0000A4B90024920B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1677896', N'1100417', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall nye søknader per 1000 innbyggere', CAST(0x0000A4B900249208 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679023', N'1099747', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter i kr pr innbygger, samferdsel i alt', CAST(0x0000A4B9002491A4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679054', N'1099778', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter i kr pr innbygger, komm veier og gater', CAST(0x0000A4B9002491A0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679082', N'1099806', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter i kr pr km kommunal vei og gate', CAST(0x0000A4B9002491B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679113', N'1099837', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter i kr pr innbygger, samferdselsbedr/transporttiltak, konse', CAST(0x0000A4B9002491B2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679143', N'1099867', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Andel netto driftsutgifter for samf i alt av samlede netto driftsutg', CAST(0x0000A4B900249164 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679174', N'1099898', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter ekskl avskrivinger i kr per innbyggger, samferdsel i alt', CAST(0x0000A4B900249195 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679204', N'1099928', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter ekskl avskrivninger i kr pr innbygger, kommunale veier og', CAST(0x0000A4B900249199 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679235', N'1099959', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Netto driftsutgifter ekskl avskrivninger i kr pr km kommunal vei og gate', CAST(0x0000A4B90024919E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679296', N'1100020', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Brutto investeringsutgifter i kr pr innbygger, komm veier og gater', CAST(0x0000A4B90024917D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1679327', N'1100051', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Brutto investeringsutgifter i kr pr innbygger, samferdselsbedr/transporttiltak', CAST(0x0000A4B900249180 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791151', N'1094635', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter grunnskolesektor (202, 215, 222, 223) i prosent av samlede netto driftsutgifter', CAST(0x0000A4B9002489F5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791182', N'1094666', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til grunnskole (202), i prosent av samlede netto driftsutgifter', CAST(0x0000A4B9002489FE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791211', N'1094694', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skolefritidstilbud (215), i prosent av samlede netto driftsutgifter', CAST(0x0000A4B900248A08 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791242', N'1094725', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skolelokaler (222), i prosent av samlede netto driftsutgifter', CAST(0x0000A4B900248A11 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791272', N'1094755', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skoleskyss (223), i prosent av samlede netto driftsutgifter', CAST(0x0000A4B900248A1A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791303', N'1094786', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter grunnskolesektor (202, 215, 222, 223), per innbygger', CAST(0x0000A4B9002489FB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791333', N'1094816', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til grunnskole (202), per innbygger', CAST(0x0000A4B900248A00 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791364', N'1094847', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skolefritidstilbud (215), per innbygger', CAST(0x0000A4B900248A0B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791395', N'1094878', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skolelokaler (222), per innbygger', CAST(0x0000A4B900248A14 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791425', N'1094908', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Herav avskrivninger til skolelokaler (222), per innbygger', CAST(0x0000A4B9002489DA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791456', N'1094939', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skoleskyss (223), per innbygger', CAST(0x0000A4B900248A1E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1791486', N'1094969', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til grunnskolesektor (202, 215, 222, 223), per innbygger 6-15 år', CAST(0x0000A4B900248A05 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792643', N'1096856', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Netto driftsutgifter til barnehager per innbygger', CAST(0x0000A4B90024894B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792703', N'1096915', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'MVA-kompensasjon drift, barnehager per innbygger (kroner)', CAST(0x0000A4B900248941 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792733', N'1096945', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'MVA-kompensasjon investering, barnehager per innbygger (kroner)', CAST(0x0000A4B900248944 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792764', N'1096976', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Brutto investeringsutgifter til barnehagesektoren per innbygger', CAST(0x0000A4B9002488FB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792794', N'1097006', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Netto driftsutgifter per innbygger 1-5 år i kroner, barnehager', CAST(0x0000A4B900248949 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792825', N'1097037', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 1-5 år med barnehageplass', CAST(0x0000A4B9002488A1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792856', N'1097068', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 0-5 år med barnehageplass', CAST(0x0000A4B900248895 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792886', N'1097098', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 0 år med barnehageplass i forhold til innbyggere 0 år', CAST(0x0000A4B900248893 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792917', N'1097129', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 1-2 år med barnehageplass i forhold til innbyggere 1-2 år', CAST(0x0000A4B90024889C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1792947', N'1097159', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 3-5 år med barnehageplass i forhold til innbyggere 3-5 år', CAST(0x0000A4B9002488A8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839363', N'1099017', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter kultursektoren i prosent av kommunens totale netto driftsutgifter', CAST(0x0000A4B9002490DB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839394', N'1099048', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter for kultursektoren per innbygger i kroner', CAST(0x0000A4B9002490CC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839423', N'4909-61', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'MVA-kompensasjon drift, kultur, per innbygger (kroner)', CAST(0x0000A4B9002490C3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839454', N'4909-62', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'MVA-kompensasjon investering, kultur, per innbygger (kroner)', CAST(0x0000A4B9002490C7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839484', N'1099076', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Brutto investeringsutgifter til kultursektoren per innbygger', CAST(0x0000A4B9002490B4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839515', N'1099107', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter aktivitetstilbud barn og unge (F231)', CAST(0x0000A4B9002490CA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839545', N'1099137', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til folkebibliotek (F370)', CAST(0x0000A4B9002490ED AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839576', N'1099168', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kino (f373)', CAST(0x0000A4B9002490FA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839607', N'1099198', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til museer (f375)', CAST(0x0000A4B900249126 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839637', N'1099229', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kunstformidling (F377)', CAST(0x0000A4B90024911E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839668', N'1099260', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til idrett (F380)', CAST(0x0000A4B9002490F5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1839698', N'1099290', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter kommunale idrettsbygg og idrettsanlegg (F381)', CAST(0x0000A4B9002490CF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915333', N'1914603', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til kommunal eiendomsforvaltning per innbygger', CAST(0x0000A4B9002485ED AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915364', N'1914634', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'MVA-kompensasjon drift, kommunal eiendomsforvaltning per innbygger', CAST(0x0000A4B9002485DE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915393', N'1914662', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'MVA-kompensasjon investering, kommunal eiendomsforvaltning per innbygger', CAST(0x0000A4B9002485E0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915424', N'1914693', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til kommunal forvaltning av eiendommer per innbygger', CAST(0x0000A4B9002485F6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915454', N'1914723', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til kommunal eiendomsforvaltning, i prosent av samlede netto driftsutgifter', CAST(0x0000A4B9002485F3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915485', N'1914754', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til kommunal eiendomsforvaltning per innbygger', CAST(0x0000A4B900248589 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915515', N'1914784', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på formålsbyggene i kvadratmeter per innbygger', CAST(0x0000A4B900248604 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915546', N'1914815', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på formålsbyggene kommunen eier i kvadratmeter per innbygger', CAST(0x0000A4B900248606 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915577', N'1914846', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på formålsbyggene kommunen leier i kvadratmeter per innbygger', CAST(0x0000A4B90024860A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915607', N'1914876', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til kommunal eiendomsforvaltning per kvadratmeter.', CAST(0x0000A4B9002485CE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915638', N'1914907', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til eiendomsforvaltning på eide bygg per kvadratmeter', CAST(0x0000A4B90024863F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'1915668', N'1914937', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til eiendomsforvaltning på leide bygg per kvadratmeter', CAST(0x0000A4B900248642 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2149820', N'1097921', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutgifter til sosialtjenesten pr. innbygger', CAST(0x0000A4B900248E6C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2149851', N'1097952', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'MVA-kompensasjon drift, sosialtjenesten pr. innbygger (kroner)', CAST(0x0000A4B900248E42 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2149879', N'1097980', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'MVA-kompensasjon investering, sosialtjenesten pr. innbygger (kroner)', CAST(0x0000A4B900248E49 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2149910', N'1098011', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutg. til sosialtjenesten i prosent av samlede netto driftsutgifter', CAST(0x0000A4B900248E59 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2149940', N'1098041', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutgifter til sosialtjenesten pr. innbygger 20-66 år', CAST(0x0000A4B900248E6F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2149971', N'1098072', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutg. til råd, veiledning og sos.forebyggend arb. pr. innb, 20-66 år', CAST(0x0000A4B900248E56 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2150001', N'1098102', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel netto driftsutg. til råd, veiledning og sosialt forebyggende arbeid', CAST(0x0000A4B900248CED AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2150032', N'1098133', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutgifter til økonomisk sosialhjelp pr innbygger 20-66 år', CAST(0x0000A4B900248E66 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2150063', N'1098164', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel netto driftsutgifter til økonomisk sosialhjelp', CAST(0x0000A4B900248DD8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2150093', N'1098194', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutg. til tilbud til pers. med rusprobl. pr. innb. 20-66 år', CAST(0x0000A4B900248E5C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2150124', N'1098225', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel netto driftsutgifter til tilbud til personer med rusproblemer', CAST(0x0000A4B900248DDC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2150154', N'1098255', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Brutto investeringsutgifter pr. innbygger', CAST(0x0000A4B900248E1C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151646', N'1649803', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Antall avtalte årsverk eksklusive lange fravær i kommunen som konsern', CAST(0x0000A4B9002492E7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151677', N'1649862', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i kommuneadministrasjonen', CAST(0x0000A4B9002492B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151705', N'1649893', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i barnehager', CAST(0x0000A4B9002492A9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151736', N'1649923', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i grunnskolen', CAST(0x0000A4B9002492AE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151766', N'1649954', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i helse- og sosialtjenester', CAST(0x0000A4B9002492B3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151797', N'1649984', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i tekniske tjenester', CAST(0x0000A4B9002492C1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151827', N'1650015', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i kultur', CAST(0x0000A4B9002492BE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151858', N'1650046', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær, annet', CAST(0x0000A4B9002492C4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151889', N'1650076', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Antall avtalte årsverk i kommunen som konsern', CAST(0x0000A4B9002492ED AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151919', N'1650137', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk i kommuneadministrasjonen', CAST(0x0000A4B9002492D7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151950', N'6417-13', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk i barnehager', CAST(0x0000A4B9002492C9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2151980', N'6417-14', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk i grunnskolen', CAST(0x0000A4B9002492D2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152377', N'1097190', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Netto driftsutgifter pr. innbygger i kroner, kommunehelsetjenesten', CAST(0x0000A4B900248B07 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152408', N'1097221', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Netto driftsutgifter i prosent av samlede netto driftsutgifter', CAST(0x0000A4B900248B05 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152436', N'1097250', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Netto driftsutg til forebygging, helsestasjons- og skolehelsetj pr innb 0-5 år', CAST(0x0000A4B900248B02 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152467', N'1097281', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Netto driftsutg til forebygging, helsestasjons- og skolehelsetj pr innb 0-20 å', CAST(0x0000A4B900248B00 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152497', N'1097311', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Netto driftsutgifter til forebyggende arbeid, helse pr. innbygger', CAST(0x0000A4B900248B12 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152528', N'1097342', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Netto driftsutg til diagnose, behandling og rehabilitering pr. innbygger', CAST(0x0000A4B900248AFD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152558', N'1097372', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Brutto investeringsutgifter pr. innbygger', CAST(0x0000A4B900248AE1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152589', N'1097403', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Legeårsverk pr 10 000 innbyggere, kommunehelsetjenesten', CAST(0x0000A4B900248AF7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152620', N'1097434', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Fysioterapiårsverk per 10 000 innbyggere, kommunehelsetjenesten', CAST(0x0000A4B900248AE4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152650', N'1097464', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel nyfødte med hjemmebesøk innen to uker etter hjemkomst', CAST(0x0000A4B900248A36 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152681', N'1097495', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel spedbarn som har fullført helseundersøkelse innen utg av 8 leveuke', CAST(0x0000A4B900248A66 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2152711', N'1097525', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel barn som har fullført helseundersøkelse ved 2-3 års alder', CAST(0x0000A4B900248A2C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2154933', N'1097556', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Netto driftsutgifter pleie og omsorg i prosent av kommunens totale netto driftsutgifter', CAST(0x0000A4B900248CB7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2154964', N'1097587', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Institusjoner (f253+261) - andel av netto driftsutgifter til plo', CAST(0x0000A4B900248C9A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2154992', N'1097615', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Tjenester til hjemmeboende (f254) - andel av netto driftsutgifter til plo', CAST(0x0000A4B900248CD1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155023', N'1097646', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Aktivisering, støttetjenester (f234) - andel av netto driftsutgifter til plo', CAST(0x0000A4B900248B1A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155053', N'1097676', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Netto driftsutgifter pr. innbygger i kroner, pleie- og omsorgtjenesten', CAST(0x0000A4B900248CBA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155084', N'1097707', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Netto driftsutgifter, pleie og omsorg pr. innbygger 80 år og over', CAST(0x0000A4B900248CC2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155114', N'1097737', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Netto driftsutgifter, pleie og omsorg pr. innbygger 67 år og over', CAST(0x0000A4B900248CBF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155145', N'1097768', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel årsverk i brukerrettede tjenester m/ fagutdanning', CAST(0x0000A4B900248B26 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155176', N'1097860', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel legemeldt sykefravær av totalt antall kommunale årsverk i brukerrettet tjeneste', CAST(0x0000A4B900248C2D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155206', N'1097890', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter pr. mottaker av kommunale pleie og omsorgstjenester', CAST(0x0000A4B900248CA1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155237', N'4905-13', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Lønnsutgifter pr kommunalt årsverk ekskl. fravær, pleie og omsorg', CAST(0x0000A4B900248CA9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2155267', N'4905-14', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Årsverk ekskl. fravær i brukerrettede tjenester pr. mottaker', CAST(0x0000A4B900248C78 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536612', N'1098286', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Netto driftsutgifter til sammen per innbygger', CAST(0x0000A4B900248EC3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536643', N'1098317', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Netto driftsutgifter per innbygger 0-17 år, barnevernstjenesten', CAST(0x0000A4B900248EC1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536671', N'1098345', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Netto driftsutgifter (funksjon 244, 251, 252) per barn i barnevernet', CAST(0x0000A4B900248EBA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536702', N'1098376', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Netto driftsutgifter (funksjon 244, 251, 252) per barn med tiltak', CAST(0x0000A4B900248EBF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536763', N'1098437', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel netto driftsutgifter til saksbehandling (funksjon 244)', CAST(0x0000A4B900248E8C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536793', N'1098467', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel netto driftsutgifter til barn som ikke er plassert av barnevernet (funks', CAST(0x0000A4B900248E89 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536824', N'1098498', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel netto driftsutgifter til barn som er plassert av barnevernet (funksjon 2', CAST(0x0000A4B900248E87 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536885', N'1098559', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Barn med undersøkelse ift. antall innbyggere 0-17 år', CAST(0x0000A4B900248E98 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536916', N'1098590', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel barn med barnevernstiltak ift. innbyggere 0-17 år', CAST(0x0000A4B900248E7A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2536946', N'1098620', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel barn med barnevernstiltak ift. innbyggere 0-22 år', CAST(0x0000A4B900248E7D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540264', N'1098803', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til fysisk planlegging i prosent av kommunens samlede netto driftsutgifter', CAST(0x0000A4B900248FA9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540295', N'1098833', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til fysisk planlegging per innbygger.', CAST(0x0000A4B900248FAB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540323', N'1098711', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'MVA-kompensasjon drift, fysisk planl/kulturminner/natur og nærmiljø, per innb (kroner)', CAST(0x0000A4B900248FA2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540354', N'1098742', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'MVA-komp investering, fysisk planl/kulturminner/natur og nærmiljø, per innb (kroner)', CAST(0x0000A4B900248FA0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540384', N'1098772', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til fysisk planlegging, kulturminner, natur og nærmiljø per innbygger.', CAST(0x0000A4B900248F2E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540507', N'1098925', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til plansaksbehandling per innbygger', CAST(0x0000A4B900248FB8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540537', N'1098956', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til plansaksbehandling per innbygger.', CAST(0x0000A4B900248F39 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'2540598', N'4908-13', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Alder for kommuneplanens arealdel', CAST(0x0000A4B900248ECA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-100', N'4901-97', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, samferdsel, i % av totale lønnsutgifter', CAST(0x0000A4B9002487DA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-101', N'4901-98', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, bolig, i % av totale lønnsutgifter', CAST(0x0000A4B9002487B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-102', N'4901-99', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, næring, i % av totale lønnsutgifter', CAST(0x0000A4B9002487D3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-103', N'4901-100', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, brann og ulykkesvern, i % av totale lønnsutgifter', CAST(0x0000A4B9002487BB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-104', N'4901-101', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, interkommunale samarbeid (§ 27-samarbeid), i % av totale lønnsutgifter', CAST(0x0000A4B9002487C8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-105', N'4901-102', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, tjenester utenfor ordinært kommunalt ansvarsområde, i % av totale lønnsutgifter', CAST(0x0000A4B9002487E1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-106', N'4901-103', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter administrasjon og styring i % av brutto dr.utg. innen området', CAST(0x0000A4B900248853 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-107', N'4901-104', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Salgs- og leieinntekter styring og kontrollvirksomhet i % av brutto dr.utg. innen området', CAST(0x0000A4B9002486B7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-108', N'4901-105', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Salgs- og leieinntekter administrasjon i % av brutto dr.utg. innen området', CAST(0x0000A4B9002486B4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-109', N'4901-106', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter barnehage i % av brutto dr.utg innen området', CAST(0x0000A4B900248856 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-110', N'4901-107', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter grunnskoleopplæring i % av brutto dr.utg innen området', CAST(0x0000A4B900248860 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-111', N'4901-108', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter helse og omsorg i % av brutto dr.utg. innen området', CAST(0x0000A4B900248864 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-112', N'4901-109', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter kommunehelse i % av brutto dr.utg. innen området', CAST(0x0000A4B90024886E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-113', N'4901-110', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter pleie og omsorg i % av brutto dr.utg. innen området', CAST(0x0000A4B900248876 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-114', N'4901-111', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter sosialtjenesten i % av brutto dr.utg. innen området', CAST(0x0000A4B90024887B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-115', N'4901-112', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter barnevern i % av brutto dr.utg. innen området', CAST(0x0000A4B900248857 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-116', N'4901-113', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter vann,- avløp og renovasjon/avfall i % av brutto dr.utg. innen området', CAST(0x0000A4B90024887E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-117', N'4901-114', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinnt. fys. planl., kulturminne, natur og nærmiljø i % av brutto dr.utg. innen området', CAST(0x0000A4B90024884A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-118', N'4901-115', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter kultur i % av brutto dr.utg. innen området', CAST(0x0000A4B900248870 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-119', N'4901-116', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter kirke i % av brutto dr.utg. innen området', CAST(0x0000A4B90024886B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-120', N'4901-117', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter samferdsel i % av brutto dr.utg. innen området', CAST(0x0000A4B900248879 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-121', N'4901-118', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter bolig i % av brutto dr.utg. innen området', CAST(0x0000A4B90024885A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-122', N'4901-119', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter næring i % av brutto dr.utg. innen området', CAST(0x0000A4B900248874 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-123', N'4901-120', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter brann og ulykkesvern i % av brutto dr.utg. innen området', CAST(0x0000A4B90024885E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-124', N'4901-121', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter interkommunale samarbeid (§ 27-samarbeid) i % av brutto dr.utg innen området', CAST(0x0000A4B900248869 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-125', N'4901-122', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinnt. tjenester utenfor ord. komm. tjenesteområde i % av brutto dr.utg. innen området', CAST(0x0000A4B900248850 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-126', N'4901-123', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto invutg, administrasjon og styring, i prosent av tot brutto invutgifter', CAST(0x0000A4B900248743 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-127', N'4901-124', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Brutto invutg, styring og kontrollvirksomhet, i prosent av tot brutto ', CAST(0x0000A4B90024868F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-128', N'4901-125', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Brutto inv.utg, administrasjon, i % av tot brutto inv.utgifter', CAST(0x0000A4B90024868A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-129', N'4901-126', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, barnehage, i % av totale brutto inv.utg', CAST(0x0000A4B90024874B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-13', N'4901-13', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Akkumulert regnskapsmessig merforbruk i prosent av brutto driftsinntekter', CAST(0x0000A4B9002486BB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-130', N'4901-127', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, grunnskoleopplæring, i % av totale brutto inv.utg', CAST(0x0000A4B900248759 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-131', N'4901-128', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, helse og omsorg, i % av totale brutto inv.utg', CAST(0x0000A4B90024875E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-132', N'4901-129', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, kommunehelse, i % av totale brutto inv.utg', CAST(0x0000A4B900248778 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-133', N'4901-130', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, pleie og omsorg, i % av totale brutto inv.utg', CAST(0x0000A4B90024877F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-134', N'4901-131', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, sosialtj., i % av totale brutto inv.utg', CAST(0x0000A4B900248785 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-135', N'4901-132', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, barnevern, i % av totale brutto inv.utgifter', CAST(0x0000A4B90024874D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-136', N'4901-133', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto inv.utg, vann, avløp, renovasjon/avfall, i % av tot brutto inv.utg', CAST(0x0000A4B900248748 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-137', N'4901-134', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto invutg, fysplanl/kultminne/natur/nærmiljø, i prosent av tot brutto invu', CAST(0x0000A4B900248745 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-138', N'4901-135', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutgifter, kultur, i % av totale brutto inv.utgifter', CAST(0x0000A4B90024878D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-139', N'4901-136', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, kirke, i % av totale brutto inv.utg', CAST(0x0000A4B900248764 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-14', N'4901-14', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Arbeidskapital ex. premieavvik i prosent av brutto driftsinntekter', CAST(0x0000A4B9002486D9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-140', N'4901-137', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, samferdsel, i % av totale brutto inv.utgifter', CAST(0x0000A4B900248782 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-141', N'4901-138', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, bolig, i % av totale brutto inv.utg', CAST(0x0000A4B900248752 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-142', N'4901-139', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, næring, i % av totale brutto inv.utgifter', CAST(0x0000A4B90024877C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-143', N'4901-140', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, brann og ulykkesvern, i % av totale brutto inv.utg', CAST(0x0000A4B900248757 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-144', N'4901-141', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg, interkommunale samarbeid (§ 27-samarbeid), i % av totale brutto inv.utg', CAST(0x0000A4B900248762 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-145', N'4901-142', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto investeringsutg., tjenester utenfor ord. komm. tjenesteområde, i % av totale brutto inv.utg', CAST(0x0000A4B900248789 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-146', N'4901-143', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til administrasjon og styring i kr. pr. innb.', CAST(0x0000A4B9002486F7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-147', N'4901-144', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter til administrasjon og styring i kr. pr. innb.', CAST(0x0000A4B900248804 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-148', N'4901-145', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønnsutgifter til administrasjon og styring i kr. pr. innb', CAST(0x0000A4B9002487E7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-149', N'4901-146', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksjon 100 Politisk styring , i kr. pr. innb.', CAST(0x0000A4B900248701 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-15', N'4901-15', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto lånegjeld i prosent av brutto driftsinntekter', CAST(0x0000A4B90024883E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-150', N'4901-147', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksjon 110, Kontroll og revisjon, i kr. pr. innb.', CAST(0x0000A4B900248706 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-151', N'4901-148', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksjon 120 Administrasjon, i kr. pr. innb.', CAST(0x0000A4B900248708 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-152', N'4901-149', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksj. 121, Forvaltningsutgifter i eiendomsforvaltningen, i kr. pr. innb.', CAST(0x0000A4B9002486FE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-153', N'4901-150', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksjon 130 Administrasjonslokaler, i kr. pr. innb.', CAST(0x0000A4B90024870B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-154', N'4901-151', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til F 170 og 171 (premieavvik), i kr. pr. innb.', CAST(0x0000A4B9002486FC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-155', N'4901-154', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksjon 180 Diverse fellesutgifter, i kr. pr. innb.', CAST(0x0000A4B90024870F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-156', N'4901-155', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter til funksjon 190 Interne serviceenheter, i kr. pr. innb.', CAST(0x0000A4B900248711 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-16', N'4901-16', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Langsiktig gjeld i prosent av brutto driftsinntekter', CAST(0x0000A4B9002487A9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-17', N'4901-17', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Pensjonsforpliktelse i prosent av brutto driftsinntekter', CAST(0x0000A4B9002486AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-21', N'4901-18', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Overføring fra driftsregnskapet, i % av brutto investeringsutgifter', CAST(0x0000A4B900248841 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-22', N'4901-19', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Tilskudd, refusjoner, salgsinntekter m.v., i % av brutto investeringsutgifter', CAST(0x0000A4B900248886 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-23', N'4901-20', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- Herav salg av fast eiendom', CAST(0x0000A4B9002486B2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-24', N'4901-21', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Diverse intern finansiering, i % av brutto investeringsutgifter', CAST(0x0000A4B900248793 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-25', N'4901-22', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- Herav udekket/udisponert i investeringsregnskapet', CAST(0x0000A4B9002486B9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-26', N'4901-23', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Bruk av lån (netto), i % av brutto investeringsutgifter', CAST(0x0000A4B9002486DC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-27', N'4901-24', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- Herav aksjer og andeler', CAST(0x0000A4B900248680 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-28', N'4901-25', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Skatt på inntekt og formue (inkludert naturressursskatt) i % av brutto driftsinntekter', CAST(0x0000A4B900248880 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-29', N'4901-26', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Naturressursskatt i % av brutto driftsinntekter', CAST(0x0000A4B9002486A1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-30', N'4901-27', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Statlig rammeoverføring i % av brutto driftsinntekter', CAST(0x0000A4B900248884 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-31', N'4901-28', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Andre statlige tilskudd til driftsformål i % av brutto driftsinntekter', CAST(0x0000A4B9002486D6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-32', N'4901-29', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Eiendomsskatt i % av brutto driftsinntekter', CAST(0x0000A4B9002487A0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-33', N'4901-30', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav eiendomsskatt på annen eiendom', CAST(0x0000A4B900248691 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-34', N'4901-31', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav eiendomsskatt på boliger og fritidseiendommer', CAST(0x0000A4B900248694 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-35', N'4901-32', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Salgs- og leieinntekter i % av brutto driftsinntekter', CAST(0x0000A4B900248866 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-36', N'4901-33', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Andre driftsinntekter i % av brutto driftsinntekter', CAST(0x0000A4B9002486D1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-38', N'4901-35', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter i kroner per innbygger', CAST(0x0000A4B9002486F5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-39', N'4901-36', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter i kroner per innbygger', CAST(0x0000A4B9002487A6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-40', N'4901-37', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter i kroner per innbygger', CAST(0x0000A4B900248800 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-41', N'4901-38', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsinntekter i kroner per innbygger', CAST(0x0000A4B9002486DE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-42', N'4901-39', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsresultat i kroner per innbygger', CAST(0x0000A4B9002487F6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-43', N'4901-40', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Frie inntekter i kroner per innbygger', CAST(0x0000A4B9002487A3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-44', N'4901-41', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto lånegjeld i kroner per innbygger', CAST(0x0000A4B90024883C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-45', N'4901-42', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Pensjonsforpliktelse i kroner per innbygger', CAST(0x0000A4B900248848 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-46', N'4901-43', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter administrasjon og styring, i % av tot. brutto driftsutgifter', CAST(0x0000A4B9002486F2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-47', N'4901-44', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Brutto driftsutgifter styring og kontrollvirksomhet, i % av tot. brutto driftsutgifter', CAST(0x0000A4B900248687 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-48', N'4901-45', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Brutto driftsutgifter administrasjon, i % av tot. brutto driftsutgifter', CAST(0x0000A4B900248684 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-49', N'4901-46', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, barnehage, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248714 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-50', N'4901-47', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, grunnskoleopplæring, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248724 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-51', N'4901-48', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, helse og omsorg, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248727 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-52', N'4901-49', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, kommunehelse, i % av totale brutto driftsutgifter', CAST(0x0000A4B90024872E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-53', N'4901-50', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, pleie og omsorg, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248736 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-54', N'4901-51', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, sosialtj., i % av totale brutto driftsutgifter', CAST(0x0000A4B90024873C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-55', N'4901-52', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, barnevern, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248717 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-56', N'4901-53', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutg, vann, avløp, renov./avfall, i % av tot brutto driftsutg', CAST(0x0000A4B9002486F0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-57', N'4901-54', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutg, fys. planl./kult.minne/natur/nærmiljø, i % av tot brutto driftsutg.', CAST(0x0000A4B9002486E4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-58', N'4901-55', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, kultur, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248730 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-59', N'4901-56', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, kirke, i % av totale brutto driftsutgifter', CAST(0x0000A4B90024872B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-60', N'4901-57', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, samferdsel, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248739 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-61', N'4901-58', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, bolig, i % av totale brutto driftsutgifter', CAST(0x0000A4B90024871E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-62', N'4901-59', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, næring, i % av totale brutto driftsutgifter', CAST(0x0000A4B900248733 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-63', N'4901-60', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, brann og ulykkesvern, i % av totale brutto driftsutg', CAST(0x0000A4B900248721 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-64', N'4901-61', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutg, interkommunale samarbeid (§ 27-samarbeid), i % av totale brutto driftsutgifter', CAST(0x0000A4B9002486E7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-65', N'4901-62', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Brutto driftsutgifter, tjenester utenfor ord. komm. ansvarsområde, i % av totale brutto driftsutg.', CAST(0x0000A4B900248740 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-66', N'4901-63', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter til administrasjon og styring i % av totale netto driftsutg', CAST(0x0000A4B900248807 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-67', N'4901-64', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Netto driftsutgifter styring og kontrollvirksomhet, i % av totale netto driftsutgifter', CAST(0x0000A4B9002486A8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-68', N'4901-65', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Netto driftsutgifter administrasjon, i % av totale netto driftsutgifter', CAST(0x0000A4B9002486A6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-69', N'4901-66', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, barnehage, i % av totale netto driftsutgifter', CAST(0x0000A4B900248809 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-70', N'4901-67', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, grunnskoleopplæring, i % av totale netto driftsutgifter', CAST(0x0000A4B900248819 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-71', N'4901-68', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, helse og omsorg, i % av totale netto driftsutg.', CAST(0x0000A4B90024881B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-72', N'4901-69', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, kommunehelse, i % av totale netto driftsutgifter', CAST(0x0000A4B900248823 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-73', N'4901-70', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, pleie og omsorg, i % av totale netto driftsutg.', CAST(0x0000A4B90024882D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-74', N'4901-71', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, sosialtj., i % av totale netto driftsutgifter', CAST(0x0000A4B900248832 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-75', N'4901-72', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, barnevern, i % av totale netto driftsutgifter', CAST(0x0000A4B90024880B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-76', N'4901-73', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutg, vann, avløp, renovasjon/avfall, i % av tot netto driftsutg', CAST(0x0000A4B9002487FD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-77', N'4901-74', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutg, fys.planl./kult.minne/natur/nærmiljø, i % av totale netto driftsutg', CAST(0x0000A4B9002487FA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-78', N'4901-75', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, kultur, i % av totale netto driftsutgifter', CAST(0x0000A4B900248826 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-79', N'4901-76', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, kirke, i % av totale netto driftsutgifter', CAST(0x0000A4B900248820 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-80', N'4901-77', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, samferdsel, i % av totale netto driftsutgifter', CAST(0x0000A4B90024882F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-81', N'4901-78', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, bolig, i % av totale netto driftsutgifter', CAST(0x0000A4B90024880E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-82', N'4901-79', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, næring, i % av totale netto driftsutgifter', CAST(0x0000A4B900248829 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-83', N'4901-80', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, brann og ulykkesvern, i % av totale netto driftsutg', CAST(0x0000A4B900248812 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-84', N'4901-81', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Netto driftsutgifter, interkommunale samarbeid (§ 27-samarbeid) , i % av totale netto driftsutgifter', CAST(0x0000A4B90024881D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-86', N'4901-83', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn administrasjon og styring i prosent av totale lønnsutgifter', CAST(0x0000A4B9002487AC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-87', N'4901-84', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Lønn styring og kontrollvirksomhet, i % av totale lønnsutgifter', CAST(0x0000A4B90024869F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-88', N'4901-85', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'- herav Lønn administrasjon, i % av totale lønnsutgifter', CAST(0x0000A4B90024869D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-89', N'4901-86', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, barnehage, i % av totale lønnsutgifter', CAST(0x0000A4B9002487AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-90', N'4901-87', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, grunnskoleopplæring, i % av totale lønnsutgifter', CAST(0x0000A4B9002487C3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-91', N'4901-88', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, helse og omsorg, i % av totale lønnsutgifter', CAST(0x0000A4B9002487C6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-92', N'4901-89', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, kommunehelse, i % av totale lønnsutgifter', CAST(0x0000A4B9002487CD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-93', N'4901-90', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, pleie og omsorg, i % av totale lønnsutgifter', CAST(0x0000A4B9002487D8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-94', N'4901-91', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, sosialtjenesten, i % av totale lønnsutgifter', CAST(0x0000A4B9002487DE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-95', N'4901-92', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, barnevern, i % av totale lønnsutgifter', CAST(0x0000A4B9002487B4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-96', N'4901-93', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, vann, avløp, renovasjon/avfall, i % av totale lønnsutgifter', CAST(0x0000A4B9002487E4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-97', N'4901-94', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, fysisk planlegging, kulturminne/natur/nærmiljø, i % av totale lønnsutgifter', CAST(0x0000A4B9002487BE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-98', N'4901-95', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, kultur, i % av totale lønnsutgifter', CAST(0x0000A4B9002487D1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'4937-99', N'4901-96', N'04937', N'A1. Konsern - Finansielle nøkkeltall og adm., styring og fellesutg. - nøkkeltall (K)', N'Lønn, kirke, i % av totale lønnsutgifter', CAST(0x0000A4B9002487CA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-13', N'4914-13', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Antall utrykninger: sum utrykninger til branner og andre utrykninger pr. 1000 innbyggere', CAST(0x0000A4B90024927F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-14', N'4914-14', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Netto driftsutgifter til funksjon 339 pr. innbygger', CAST(0x0000A4B9002492A1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-15', N'4914-15', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Netto driftsutgifter pr. innbygger i kroner', CAST(0x0000A4B900249297 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-16', N'4914-16', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'MVA-kompensasjon drift, brann- og ulykkesvern, pr. innbygger (kroner)', CAST(0x0000A4B90024928F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-17', N'4914-17', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'MVA-kompensasjon investering, brann- og ulykkesvern, pr. innbygger (kroner)', CAST(0x0000A4B900249293 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-18', N'4914-18', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Årsverk til funksjon 339 pr. 1000 innbyggere', CAST(0x0000A4B90024928C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-19', N'4914-19', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Årsverk i brann- og ulykkesvern pr. 1000 innbyggere', CAST(0x0000A4B900249288 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6491-20', N'4914-20', N'06491', N'P1. Konsern - Brann- og ulykkesvern - nøkkeltall (K)', N'Antall boligbranner pr. 1000 innbyggere', CAST(0x0000A4B90024926E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-13', N'1100447', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andelen nye søknader', CAST(0x0000A4B9002491F5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-14', N'4912-13', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel søkere som har fått avslag på kommunal bolig', CAST(0x0000A4B9002491F1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-15', N'4912-14', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nye søkere som har fått avslag på kommunal bolig', CAST(0x0000A4B9002491D0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-16', N'4912-15', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede husstander av alle husstander som er tildelt bolig', CAST(0x0000A4B9002491D6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-17', N'4912-16', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede flyktninger', CAST(0x0000A4B9002491D3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-18', N'4912-17', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede med behov for tilrettelagt bolig', CAST(0x0000A4B9002491DC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-19', N'4912-18', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede med psykiske lidelser', CAST(0x0000A4B9002491DE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-20', N'4912-19', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede rusmiddelmisbrukere', CAST(0x0000A4B9002491E3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-21', N'4912-20', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede rusmiddelmisbrukere med psykiske lidelser', CAST(0x0000A4B9002491E6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-22', N'4912-21', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede med andre problemer', CAST(0x0000A4B9002491D9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-23', N'4912-22', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel nyinnflyttede uten behovsprøving', CAST(0x0000A4B9002491E9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-24', N'4912-23', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel flyktninger på venteliste', CAST(0x0000A4B9002491BB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-25', N'4912-24', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel med behov for tilrettelagt bolig, på venteliste', CAST(0x0000A4B9002491CA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-26', N'4912-25', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel med psykiske lidelser, på venteliste', CAST(0x0000A4B9002491CE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-27', N'4912-26', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel rusmiddelmisbrukere på venteliste', CAST(0x0000A4B9002491EF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-28', N'4912-27', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel rusmiddelmisbrukere med psykiske lidelser, på venteliste', CAST(0x0000A4B9002491EC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-29', N'4912-28', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel med andre problemer, på venteliste', CAST(0x0000A4B9002491C8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-30', N'4912-29', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel husstander i midlertidig botilbud med varighet 0-3 mnd', CAST(0x0000A4B9002491BE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-31', N'4912-30', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel husstander med barn midlertidige botilbud', CAST(0x0000A4B9002491C2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-32', N'4912-31', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Andel husstander med barn i midlertidige botilbud i 0-3 mnd', CAST(0x0000A4B9002491C0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-33', N'4912-32', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Kommunalt disponerte omsorgsboliger per 1000 innbyggere', CAST(0x0000A4B900249237 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-34', N'4912-33', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Kommunalt eide omsorgsboliger som andel av alle kommunalt disponerte omsorgsboliger', CAST(0x0000A4B90024924C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-35', N'4912-34', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall nye boliger godkjent for finansiering av Husbanken, per 1000 innbyggere', CAST(0x0000A4B900249206 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-36', N'4912-35', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall utbedrede boliger godkjent for finansiering av Husbanken, per 1000 innbyggere', CAST(0x0000A4B900249210 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-37', N'4912-36', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall brukte boliger godkjent for finansiering av Husbanken, per 1000 innbyggere', CAST(0x0000A4B900249200 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-38', N'4912-37', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Beløp per måned per husstand i statlig bostøtte fra Husbanken', CAST(0x0000A4B900249221 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-39', N'4912-38', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall husstander tilkjent statlig bostøtte fra Husbanken per 1000 innbyggere', CAST(0x0000A4B900249203 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-40', N'4912-39', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall boliger godkjent av kommunen for finansiering med startlån, per 1000 innbyggere', CAST(0x0000A4B9002491FD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-41', N'4912-40', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Beløp per innbygger i startlån videretildelt av kommunen', CAST(0x0000A4B90024921E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-42', N'4912-41', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall boliger godkjent av kommunen for boligtilskudd til tilpasning, per 1000 innbyggere', CAST(0x0000A4B9002491FA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-43', N'4912-42', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Beløp per innbygger i boligtilskudd til tilpasning videretildelt av kommunen', CAST(0x0000A4B900249216 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-44', N'4912-43', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Antall boliger godkjent av kommunen for boligtilskudd til etablering, per 1000 innbyggere', CAST(0x0000A4B9002491F8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6493-45', N'4912-44', N'06493', N'N1. Konsern - Bolig - Nøkkeltall (K)', N'Beløp per innbygger i boligtilskudd til etablering videretildelt av kommunen', CAST(0x0000A4B900249214 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-15', N'4911-15', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Kommunalt tilskudd i kr pr km privat vei, konsern', CAST(0x0000A4B90024918B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-16', N'4911-16', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Gang- og sykkelvei i km som er et kommunalt ansvar pr 10 000 innb, konsern', CAST(0x0000A4B900249183 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-17', N'4911-17', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Andel kommunale veier og gater med fartsgrense 40 km/t eller lavere, konsern', CAST(0x0000A4B90024915F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-18', N'4911-18', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Brutto driftsutgifter i kr pr km kommunal vei og gate', CAST(0x0000A4B900249178 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-19', N'4911-19', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Brutto driftsutg i kr pr innbygger for komm veier', CAST(0x0000A4B900249174 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-20', N'4911-20', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Brutto driftsutgifter i kr pr km kommunal vei og gate inkl gang/sykkelvei', CAST(0x0000A4B90024917B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-22', N'4911-22', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Brutto driftsutg ekskl avskrivninger i kr pr innbygger for komm veier og gater', CAST(0x0000A4B900249170 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-23', N'4911-23', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Korrigerte brutto driftsutg i kr pr km komm vei og gate', CAST(0x0000A4B900249192 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-24', N'4911-24', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Andel kommunale veier og gater uten fast dekke', CAST(0x0000A4B900249162 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-25', N'4911-25', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Antall parkeringsplasser skiltet for forflytningshemmede pr 10 000 innbygger', CAST(0x0000A4B900249169 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6497-26', N'4911-26', N'06497', N'M1. Konsern - Samferdsel - nivå 2 (K)', N'Antall utstedte parkeringstillatelser for forflytningshemmede pr 10 000 innbyg', CAST(0x0000A4B90024916C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-13', N'4897-13', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til grunnskole (202), per innbygger 6-15 år', CAST(0x0000A4B900248A03 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-14', N'4897-14', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skolefritidstilbud (215), per innbygger 6-9 år', CAST(0x0000A4B900248A0E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-15', N'4897-15', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skolelokaler (222), per innbygger 6-15 år', CAST(0x0000A4B900248A17 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-16', N'4897-16', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til skoleskyss (223), per innbygger 6-15 år', CAST(0x0000A4B900248A20 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-17', N'4897-17', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til voksenopplæring (213), i prosent av samlede netto driftsutgifter', CAST(0x0000A4B900248A22 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-18', N'4897-18', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Netto driftsutgifter til voksenopplæring (213), per innbygger', CAST(0x0000A4B900248A26 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-19', N'4897-19', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Brutto investeringsutgifter til grunnskolesektor (202, 215, 222, 223), per innbygger', CAST(0x0000A4B9002489BB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-20', N'4897-20', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i kommunens grunnskoler, av kommunens innbyggere 6-15 år', CAST(0x0000A4B900248991 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-21', N'4897-21', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får særskilt norskopplæring', CAST(0x0000A4B900248976 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-22', N'4897-22', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får morsmålsopplæring', CAST(0x0000A4B900248974 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-23', N'4897-23', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får spesialundervisning', CAST(0x0000A4B900248979 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-24', N'4897-24', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får spesialundervisning, 1.-4. trinn', CAST(0x0000A4B90024897B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-25', N'4897-25', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får spesialundervisning, 5.-7. trinn', CAST(0x0000A4B90024897D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-26', N'4897-26', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får spesialundervisning, 8.-10. trinn', CAST(0x0000A4B900248984 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-27', N'4897-27', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel timer spesialundervisning av antall lærertimer totalt', CAST(0x0000A4B9002489B5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-28', N'4897-28', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i grunnskolen som får tilbud om skoleskyss', CAST(0x0000A4B900248987 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-29', N'4897-29', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel innbyggere 6-9 år i kommunal og privat SFO', CAST(0x0000A4B90024899B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-30', N'4897-30', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel innbyggere 6-9 år i kommunal SFO', CAST(0x0000A4B90024899F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-31', N'4897-31', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i kommunal og privat SFO med 100% plass', CAST(0x0000A4B90024898A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-32', N'4897-32', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever i kommunal SFO med 100% plass', CAST(0x0000A4B90024898F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-33', N'4897-33', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel av 6 åringer som fortsetter i SFO andre året', CAST(0x0000A4B90024896B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-34', N'4897-34', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til grunnskolesektor (202, 215, 222, 223), per elev', CAST(0x0000A4B9002489E5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-35', N'4897-35', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Korr. brutto driftsutgifter til grunnskole, skolelokaler og skoleskyss (202, 222, 223), per elev', CAST(0x0000A4B9002489E2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-36', N'4897-36', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Lønnsutgifter til grunnskole, skolelokaler og skoleskyss (202, 222, 223), per elev', CAST(0x0000A4B9002489F3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-37', N'4897-37', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til grunnskole (202), per elev', CAST(0x0000A4B9002489DD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-38', N'4897-38', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Driftsutgifter til undervisningsmateriell (202), per elev i grunnskolen', CAST(0x0000A4B9002489C0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-39', N'4897-39', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Driftsutgifter til inventar og utstyr (202), per elev i grunnskolen', CAST(0x0000A4B9002489BE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-40', N'4897-40', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til skolefritidstilbud (215), per komm. bruker', CAST(0x0000A4B9002489E7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-41', N'4897-41', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Brutto driftsutgifter til skolefritidstilbud (215), per komm. og priv. bruker', CAST(0x0000A4B9002489B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-42', N'4897-42', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til skolelokaler (222), per elev', CAST(0x0000A4B9002489E9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-43', N'4897-43', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Leie av lokaler og grunn (222), per elev', CAST(0x0000A4B9002489EF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-44', N'4897-44', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til skoleskyss (223), per elev som får skoleskyss', CAST(0x0000A4B9002489EC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-45', N'4897-45', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Elever per kommunal skole', CAST(0x0000A4B9002489C3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-46', N'4897-46', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Gjennomsnittlig gruppestørrelse, 1.-10.årstrinn', CAST(0x0000A4B9002489C9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-47', N'4897-47', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Gjennomsnittlig gruppestørrelse, 1.-4.årstrinn', CAST(0x0000A4B9002489CC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-48', N'4897-48', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Gjennomsnittlig gruppestørrelse, 5.-7.årstrinn', CAST(0x0000A4B9002489CF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-49', N'4897-49', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Gjennomsnittlig gruppestørrelse, 8.-10.årstrinn', CAST(0x0000A4B9002489D5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-50', N'4897-50', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel elever med direkte overgang fra grunnskole til videregående opplæring', CAST(0x0000A4B900248997 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-51', N'4897-51', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Gjennomsnittlige grunnskolepoeng', CAST(0x0000A4B9002489D8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-52', N'4897-52', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel avtalte årsverk i grunnskolesektor', CAST(0x0000A4B900248971 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-53', N'4897-53', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel avtalte årsverk eksklusive lange fravær i grunnskolesektor', CAST(0x0000A4B90024896F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-54', N'4897-54', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere som er 40 år og yngre', CAST(0x0000A4B9002489AC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-55', N'4897-55', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere som er 50 år og eldre', CAST(0x0000A4B9002489AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-56', N'4897-56', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere som er 60 år og eldre', CAST(0x0000A4B9002489B2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-57', N'4897-57', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere i heltidsstilling', CAST(0x0000A4B9002489A1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-58', N'4897-58', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere med universitets-/høgskoleutdanning og pedagogisk utdanning', CAST(0x0000A4B9002489A4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-59', N'4897-59', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere med universitets-/høgskoleutdanning uten pedagogisk utdanning', CAST(0x0000A4B9002489A6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6804-60', N'4897-60', N'06804', N'D1. Konsern - Grunnskoleopplæring - nøkkeltall (K)', N'Andel lærere med videregående utdanning eller lavere', CAST(0x0000A4B9002489A9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6806-29', N'4684-29', N'06806', N'D1. Konsern - Grunnskole - grunnlagsdata (K)', N'Nivå 3 - Antall kommunale grunnskoler', CAST(0x0000A4B900248969 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6806-30', N'4684-30', N'06806', N'D1. Konsern - Grunnskole - grunnlagsdata (K)', N'Nivå 3 - Antall elever i kommunale grunnskoler', CAST(0x0000A4B900248964 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6806-57', N'4684-57', N'06806', N'D1. Konsern - Grunnskole - grunnlagsdata (K)', N'Nivå 3 - Antall elever i kommunal og privat SFO', CAST(0x0000A4B900248961 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-13', N'4903-13', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel plasser i åpen barnehage i forhold til innbyggere 0-5 år', CAST(0x0000A4B9002488EC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-14', N'4903-14', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn i kommunale barnehager i forhold til alle barn i barnehage', CAST(0x0000A4B9002488AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-15', N'4903-15', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn i barnehage med oppholdstid 33 timer eller mer per uke', CAST(0x0000A4B9002488AD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-16', N'4903-16', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn i barnehage med oppholdstid 32 timer eller mindre per uke', CAST(0x0000A4B9002488AA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-17', N'4903-17', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel minoritetsspråklige barn i barnehage i forhold til innvandrerbarn 0-5 år', CAST(0x0000A4B9002488E1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-18', N'4903-18', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel minoritetsspråklige barn i barnehage i forhold til innvandrerbarn 1-5 år', CAST(0x0000A4B9002488E4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-19', N'4903-19', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel minoritetsspråklige barn i barnehage i forhold til innvandrerbarn 0-5 år', CAST(0x0000A4B9002488DF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-20', N'4903-20', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel minoritetsspråklige barn i barnehage i forhold til innvandrerbarn 1-5 år', CAST(0x0000A4B9002488E6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-21', N'4903-21', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel minoritetsspråklige barn i barnehage i forhold til alle barn med barnehageplass', CAST(0x0000A4B9002488DC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-22', N'4903-22', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 0 år i kommunale barnehager i forhold til innbyggere 0 år', CAST(0x0000A4B900248891 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-23', N'4903-23', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 1-2 år i kommunale barnehager i forhold til innbyggere 1-2 år', CAST(0x0000A4B900248898 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-24', N'4903-24', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn 3-5 år i kommunale barnehager i forhold til innbyggere 3-5 år', CAST(0x0000A4B9002488A4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-25', N'4903-25', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn med oppholdstid 33 timer eller mer per uke i kommunal barnehage', CAST(0x0000A4B9002488B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-26', N'4903-26', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn med oppholdstid 32 timer eller mindre per uke i kommunal barnehage', CAST(0x0000A4B9002488B2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-27', N'4903-27', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte oppholdstimer per årsverk i kommunale barnehager', CAST(0x0000A4B90024892A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-28', N'4903-28', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte brutto driftsutgifter i kroner per barn i kommunal barnehage', CAST(0x0000A4B900248921 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-29', N'4903-29', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte brutto driftsutgifter til kommunale barnehager per korrigert oppholdstime (kr)', CAST(0x0000A4B900248928 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-30', N'4903-30', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Kommunale overføringer av driftsmidler til private barnehager per korrigert oppholdstime (kr)', CAST(0x0000A4B90024890D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-31', N'4903-31', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel ansatte med barnehagelærerutdanning', CAST(0x0000A4B90024888C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-32', N'4903-32', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel ansatte med annen pedagogisk utdanning', CAST(0x0000A4B900248889 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-33', N'4903-33', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel styrere og pedagogiske ledere med godkjent barnehagelærerutdanning', CAST(0x0000A4B9002488F1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-34', N'4903-34', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel styrere med annen pedagogisk utdanning', CAST(0x0000A4B9002488EF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-35', N'4903-35', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel pedagogiske ledere med annen pedagogisk utdanning', CAST(0x0000A4B9002488E9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-36', N'4903-36', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel ansatte menn til basisvirksomhet i barnehagene', CAST(0x0000A4B90024888E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-37', N'4903-38', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte oppholdstimer per årsverk til basisvirksomhet, kommunale barnehager', CAST(0x0000A4B900248934 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-38', N'4903-39', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte oppholdstimer per årsverk til basisvirksomhet, private barnehager', CAST(0x0000A4B900248936 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-39', N'4903-37', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte oppholdstimer per årsverk til basisvirksomhet, alle barnehager', CAST(0x0000A4B90024892D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-40', N'4903-40', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Funksjon 201 - Opphold og stimulering', CAST(0x0000A4B9002488FD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-41', N'4903-41', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Funksjon 211 - Tilrettelagte tiltak', CAST(0x0000A4B900248900 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-42', N'4903-42', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Funksjon 221 - Lokaler og skyss', CAST(0x0000A4B900248908 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-43', N'4903-43', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Oppholdsbetaling', CAST(0x0000A4B90024894E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-44', N'4903-44', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Statstilskudd', CAST(0x0000A4B900248951 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-45', N'4903-45', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Kommunale driftsmidler', CAST(0x0000A4B90024890B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-46', N'4903-46', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte brutto driftsutgifter f201 per korrigert oppholdstime i kommunale barnehager (kr)', CAST(0x0000A4B90024891C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-47', N'4903-48', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Antall barn korrigert for alder per årsverk til basisvirksomhet i kommunale barnehager', CAST(0x0000A4B9002488F4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-48', N'4903-49', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Antall barn korrigert for alder per årsverk til basisvirksomhet i private barnehager', CAST(0x0000A4B9002488F6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-49', N'4903-50', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Brutto driftsutg. styrket tilb. til førskolebarn (f 211) pr barn med ekstra ressurser, alle barneh.', CAST(0x0000A4B9002488F8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-51', N'4903-52', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn som får ekstra ressurser til styrket tilbud til førskolebarn, i for', CAST(0x0000A4B9002488BB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-52', N'4903-53', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn som får ekstra ressurser til styrket tilbud til førskolebarn, i for', CAST(0x0000A4B9002488BD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-53', N'4903-54', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Korrigerte brutto driftsutg per barn, ekskl minoritetsspråklige, som får ekstr', CAST(0x0000A4B90024891A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-54', N'4903-55', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barn, ekskl. minoritetsspråklige, som får ekstra ressurser, ift.  alle barn i komm. barnehager', CAST(0x0000A4B9002488C0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-55', N'4903-56', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Utgifter til kommunale lokaler og skyss per barn i kommunal barnehage (kr)', CAST(0x0000A4B900248954 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-56', N'4903-57', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Leke- og oppholdsareal per barn i kommunale barnehager (m2)', CAST(0x0000A4B90024893C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-57', N'4903-58', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Leke- og oppholdsareal per barn i private barnehager (m2)', CAST(0x0000A4B90024893E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-58', N'4903-59', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Leke- og oppholdsareal per barn i barnehage (m2)', CAST(0x0000A4B900248939 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-59', N'4903-60', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barnehager med åpningstid fra 0 inntil 6 timer per dag', CAST(0x0000A4B9002488C5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-60', N'4903-61', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barnehager med åpningstid fra 6 inntil 9 timer per dag', CAST(0x0000A4B9002488D7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-61', N'4903-62', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barnehager med åpningstid fra 9 inntil 10 timer per dag', CAST(0x0000A4B9002488D9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6808-62', N'4903-63', N'06808', N'C1. Konsern - Barnehager - nivå 2 (K)', N'Andel barnehager med åpningstid 10 timer eller mer per dag', CAST(0x0000A4B9002488C2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6810-25', N'4683-29', N'06810', N'C1. Konsern - Barnehager - nivå 3 (K)', N'Nivå 3 - 1-5 åringer med barnehageplass', CAST(0x0000A4B90024895A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6810-26', N'4683-25', N'06810', N'C1. Konsern - Barnehager - nivå 3 (K)', N'Nivå 3 - 0-åringer med barnehageplass', CAST(0x0000A4B900248957 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6810-49', N'4683-48', N'06810', N'C1. Konsern - Barnehager - nivå 3 (K)', N'Nivå 3 - Antall kommunale barnehager', CAST(0x0000A4B90024895C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6810-50', N'4683-49', N'06810', N'C1. Konsern - Barnehager - nivå 3 (K)', N'Nivå 3 - Antall private barnehager', CAST(0x0000A4B90024895F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-13', N'1099321', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til musikk- og kulturskoler (F383)', CAST(0x0000A4B900249133 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-14', N'1099351', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til andre kulturaktiviteter (F385)', CAST(0x0000A4B9002490E7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-15', N'4909-13', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter kommunale kulturbygg (F386 )', CAST(0x0000A4B9002490D5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-16', N'4909-14', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutg. komm. idrettsbygg og idrettsanlegg (f381) ift. kommunens tot. driftsutgifter (i %)', CAST(0x0000A4B9002490D1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-17', N'4909-15', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kommunale idrettsbygg per innbygger', CAST(0x0000A4B90024910B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-18', N'4909-16', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Brutto invest.utg. til komm. idrettsbygg og idrettsanl (f381) i % av bto invest.utg. kultursektoren', CAST(0x0000A4B9002490A8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-19', N'4909-17', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Brutto investeringsutgifter til kommunale idrettsbygg og idrettsanlegg (f381) per innbygger', CAST(0x0000A4B9002490AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-20', N'4909-18', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter kommunale kulturbygg (f386) i forhold til kommunens totale driftsutgifter (i %)', CAST(0x0000A4B9002490D8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-21', N'4909-19', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kommunale kulturbygg per innbygger', CAST(0x0000A4B90024910E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-22', N'4909-20', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Brutto investeringsutgifter til komm. kulturbygg (f386) i % av brutto invest.utg til kultursektoren', CAST(0x0000A4B9002490AB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-23', N'4909-21', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Brutto investeringsutgifter til kommunale kulturbygg (f386) per innbygger', CAST(0x0000A4B9002490B1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-24', N'4909-22', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til folkebibliotek i forhold til kommunens totale driftsutgifter (i prosent)', CAST(0x0000A4B9002490F0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-25', N'4909-23', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til folkebibliotek per innbygger', CAST(0x0000A4B9002490F2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-26', N'4909-24', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Utlån alle medier fra folkebibliotek per innbygger', CAST(0x0000A4B900249157 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-27', N'4909-25', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Bokutlån fra folkebibliotek per innbygger i alt', CAST(0x0000A4B9002490A3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-28', N'4909-26', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Barnelitteratur, antall bokutlån barnelitteratur per innbygger 0-13 år', CAST(0x0000A4B90024909B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-29', N'4909-27', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Voksenlitteratur, bokutlån voksenlitteratur per innbygger 14 år og over', CAST(0x0000A4B90024915C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-30', N'4909-28', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Utlån, andre media i alt fra folkebibliotek per innbygger', CAST(0x0000A4B90024915A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-31', N'4909-29', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Omløpshastighet barnebøker i folkebibliotek', CAST(0x0000A4B90024913F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-32', N'4909-30', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Omløpshastighet skjønnlitteratur for voksne i folkebibliotek', CAST(0x0000A4B90024914B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-33', N'4909-31', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Tilvekst alle medier i folkebibliotek per 1000 innbygger', CAST(0x0000A4B900249153 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-34', N'4909-32', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Besøk i folkebibliotek per innbygger', CAST(0x0000A4B90024909E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-35', N'4909-33', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Medie- og lønnsutgifter i folkebibliotek per innbygger', CAST(0x0000A4B9002490C0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-36', N'4909-34', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Årsverk, antall innbyggere per årsverk i folkebibliotek', CAST(0x0000A4B900249098 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-37', N'4909-35', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kino i forhold til kommunens totale driftsutgifter (i prosent)', CAST(0x0000A4B9002490FF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-38', N'4909-36', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kino per innbygger', CAST(0x0000A4B900249107 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-39', N'4909-37', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kino per besøkende', CAST(0x0000A4B900249102 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-40', N'4909-38', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Antall innbyggere per kinosete', CAST(0x0000A4B900249090 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-41', N'4909-39', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Besøk per kinoforestilling', CAST(0x0000A4B9002490A0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-42', N'4909-40', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til idrett per innbygger', CAST(0x0000A4B9002490F7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-43', N'4909-41', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til aktivitetstilbud barn og unge per innbygger', CAST(0x0000A4B9002490DD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-44', N'4909-42', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til aktivitetstilbud barn og unge per innbygger 6-18 år', CAST(0x0000A4B9002490E0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-45', N'4909-43', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til aktivitetstilbud barn og unge per innbygger 6-20 år', CAST(0x0000A4B9002490E3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-46', N'4909-44', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Antall årsverk ved kommunalt drevet fritidssenter', CAST(0x0000A4B90024908B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-47', N'4909-46', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Årlig totale åpningstimer i kommunale fritidssenter per 1000 innb. 6-20 år', CAST(0x0000A4B900249093 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-48', N'4909-47', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Årlig totalt besøk i kommunalt drevne fritidssenter per 1000 innb. 6-20 år', CAST(0x0000A4B900249095 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-49', N'4909-45', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Tilskudd til frivillige barne- og ungdomsforeninger per lag som mottar tilskud', CAST(0x0000A4B90024914E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-50', N'4909-48', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Antall frivillige lag som mottar kommunale driftstilskudd', CAST(0x0000A4B90024908E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-51', N'4909-49', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Kommunale driftstilskudd til lag og foreninger pr lag som mottar tilskudd', CAST(0x0000A4B9002490B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-52', N'4909-50', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kommunale musikk- og kulturskoler, i % av samlede netto driftsutgifter', CAST(0x0000A4B900249118 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-53', N'4909-51', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kommunale musikk- og kulturskoler per innbygger', CAST(0x0000A4B900249115 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-54', N'4909-52', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kommunale musikk- og kulturskoler, per innbygger 6-15 år', CAST(0x0000A4B90024911C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-55', N'4909-53', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Korrigerte brutto driftsutgifter til kommunale musikk- og kulturskoler, per bruker', CAST(0x0000A4B9002490BE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-56', N'4909-54', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Brutto driftsutgifter til kommunale musikk- og kulturskoler, per bruker', CAST(0x0000A4B9002490A6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-57', N'4909-55', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Andel elever (brukere) i grunnskolealder i kommunens musikk- og kulturskole, a', CAST(0x0000A4B90024907B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-58', N'4909-56', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Andel barn i grunnskolealder på venteliste til komm. musikk- og kulturskole, av antall barn 6-15 år', CAST(0x0000A4B900249078 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-59', N'4909-57', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til muséer per innbygger', CAST(0x0000A4B900249130 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-60', N'4909-58', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til kunstformidling per innbygger', CAST(0x0000A4B900249122 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'6936-61', N'4909-59', N'06936', N'K1. Konsern - Kultur - nivå 2 (K)', N'Netto driftsutgifter til andre kulturaktiviteter per innbygger', CAST(0x0000A4B9002490E9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-13', N'7142-13', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til kommunal forvaltning av eiendommer per kvadratmeter', CAST(0x0000A4B9002485D1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-14', N'7142-14', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter i kommunal eiendomsforvaltning per kvadratmeter', CAST(0x0000A4B900248662 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-15', N'7142-15', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter i kommunal eiendomsforvaltning per kvadratmeter', CAST(0x0000A4B900248625 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-16', N'7142-16', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter i kommunal eiendomsforvaltning per kvadratmet', CAST(0x0000A4B90024864B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-17', N'7142-17', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader for kommunal eiendomsforvaltning per kvadratmeter', CAST(0x0000A4B90024859C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-18', N'7142-18', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til kommunal eiendomsforvaltning i % av samlede brutto investeringsutg.', CAST(0x0000A4B900248584 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-19', N'7142-19', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader for kommunal eiendomsforvaltning i % av brutto driftsutg. til eiendomsforvaltning', CAST(0x0000A4B90024859A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-20', N'7142-20', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til administrasjonslokaler per innbygger', CAST(0x0000A4B9002485E4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-21', N'7142-21', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til administrasjonslokaler per innbygger', CAST(0x0000A4B900248520 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-22', N'7142-22', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på administrasjonslokaler i kvadratmeter per innbygger', CAST(0x0000A4B900248601 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-23', N'7142-23', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til administrasjonslokaler per kvadratmeter', CAST(0x0000A4B9002485C6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-24', N'7142-24', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter, administrasjonslokaler per kvadratmeter', CAST(0x0000A4B900248665 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-25', N'7142-25', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter, administrasjonslokaler per kvadratmeter', CAST(0x0000A4B900248628 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-26', N'7142-26', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter, administrasjonslokaler per kvadratmeter', CAST(0x0000A4B90024864C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-27', N'7142-27', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til administrasjonslokaler per kvadratmeter, administrasjonslo', CAST(0x0000A4B9002485A2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-28', N'7142-28', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til administrasjonslokaler i prosent av brutto driftsutg. til administrasjonslokaler', CAST(0x0000A4B90024859E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-29', N'7142-29', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til førskolelokaler per innbygger', CAST(0x0000A4B9002485E7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-30', N'7142-30', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til førskolelokaler per innbygger', CAST(0x0000A4B900248526 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-31', N'7142-31', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på førskolelokaler i kvadratmeter per innbygger 1-5 år', CAST(0x0000A4B90024860F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-32', N'7142-32', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til førskolelokaler per kvadratmeter', CAST(0x0000A4B9002485C8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-33', N'7142-33', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter, førskolelokaler per kvadratmeter', CAST(0x0000A4B900248673 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-34', N'7142-34', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter, førskolelokaler per kvadratmeter', CAST(0x0000A4B90024862A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-35', N'7142-35', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter, førskolelokaler per kvadratmeter', CAST(0x0000A4B900248650 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-36', N'7142-36', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til førskolelokaler per kvadratmeter', CAST(0x0000A4B9002485A8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-37', N'7142-37', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på førskolelokaler i kvadratmeter per barn i kommunal barnehage', CAST(0x0000A4B90024860D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-38', N'7142-38', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til førskolelokaler i prosent av brutto driftsutgifter til førskolelokaler', CAST(0x0000A4B9002485A4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-39', N'7142-39', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til skolelokaler per innbygger', CAST(0x0000A4B9002485FE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-40', N'7142-40', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til skolelokaler per innbygger', CAST(0x0000A4B900248597 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-41', N'7142-41', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på skolelokaler i kvadratmeter per innbygger 6-15 år', CAST(0x0000A4B900248622 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-42', N'7142-42', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til skolelokaler per kvadratmeter', CAST(0x0000A4B9002485DC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-43', N'7142-43', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter, skolelokaler per kvadratmeter', CAST(0x0000A4B90024867F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-44', N'7142-44', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter, skolelokaler per kvadratmeter', CAST(0x0000A4B90024863D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-45', N'7142-45', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter, skolelokaler per kvadratmeter', CAST(0x0000A4B90024865F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-46', N'7142-46', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til skolelokaler per kvadratmeter', CAST(0x0000A4B9002485C2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-47', N'7142-47', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på skolelokaler i kvadratmeter per elev', CAST(0x0000A4B90024861F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-48', N'7142-48', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til skolelokaler i prosent av brutto driftsutgifter til skolelokaler', CAST(0x0000A4B9002485BD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-49', N'7142-49', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til institusjonslokaler per innbygger', CAST(0x0000A4B9002485E9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-50', N'7142-50', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til institusjonslokaler per innbygger', CAST(0x0000A4B900248580 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-51', N'7142-51', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på institusjonslokaler i kvadratmeter per innbygger 80 år og over', CAST(0x0000A4B900248615 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-52', N'7142-52', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til institusjonslokaler per kvadratmeter', CAST(0x0000A4B9002485CB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-53', N'7142-53', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter, institusjonslokaler per kvadratmeter', CAST(0x0000A4B900248675 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-54', N'7142-54', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter, institusjonslokaler per kvadratmeter', CAST(0x0000A4B900248632 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-55', N'7142-55', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter, institusjonslokaler per kvadratmeter', CAST(0x0000A4B900248653 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-56', N'7142-56', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til institusjonslokaler per kvadratmeter', CAST(0x0000A4B9002485AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-57', N'7142-57', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på institusjonslokaler i kvadratmeter per beboer i institusjon', CAST(0x0000A4B900248613 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-58', N'7142-58', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til institusjonslokaler i prosent av brutto driftsutgifter til institusjonslokaler', CAST(0x0000A4B9002485AD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-59', N'7142-59', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til kommunale idrettsbygg per innbygger', CAST(0x0000A4B9002485F9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-60', N'7142-60', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til kommunale idrettsbygg per innbygger', CAST(0x0000A4B900248591 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-61', N'7142-61', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på kommunale idrettsbygg i kvadratmeter per innbygger', CAST(0x0000A4B900248618 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-62', N'7142-62', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til kommunale idrettsbygg per kvadratmeter', CAST(0x0000A4B9002485D4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-63', N'7142-63', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter, kommunale idrettsbygg per kvadratmeter', CAST(0x0000A4B900248679 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-64', N'7142-64', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter, kommunale idrettsbygg per kvadratmeter', CAST(0x0000A4B900248637 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-65', N'7142-65', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter, kommunale idrettsbygg per kvadratmeter', CAST(0x0000A4B900248657 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-66', N'7142-66', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til kommunale idrettsbygg per kvadratmeter', CAST(0x0000A4B9002485B6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-67', N'7142-67', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til kommunale idrettsbygg i prosent av brutto driftsutg. til kommunale idrettsbygg', CAST(0x0000A4B9002485B1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-68', N'7142-68', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Netto driftsutgifter til kommunale kulturbygg per innbygger', CAST(0x0000A4B9002485FC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-69', N'7142-69', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Brutto investeringsutgifter til kommunale kulturbygg per innbygger', CAST(0x0000A4B900248593 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-70', N'7142-70', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Samlet areal på kommunale kulturbygg i kvadratmeter per innbygger', CAST(0x0000A4B90024861C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-71', N'7142-71', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter til kommunale kulturbygg per kvadratmeter', CAST(0x0000A4B9002485D9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-72', N'7142-72', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til vedlikeholdsaktiviteter, kommunale kulturbygg per kvadratmeter', CAST(0x0000A4B90024867C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-73', N'7142-73', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til driftsaktiviteter, kommunale kulturbygg per kvadratmeter', CAST(0x0000A4B90024863A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-74', N'7142-74', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Utgifter til renholdsaktiviteter, kommunale kulturbygg per kvadratmeter', CAST(0x0000A4B90024865B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-75', N'7142-75', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til kommunale kulturbygg per kvadratmeter', CAST(0x0000A4B9002485BA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7144-76', N'7142-76', N'07144', N'4.1. Konsern - Eiendomsforvaltning for utvalgte kommunale formålsbygg - nøkkeltall (K)', N'Energikostnader til kommunale kulturbygg i prosent av brutto driftsutgifter til kommunale kulturbygg', CAST(0x0000A4B9002485B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-13', N'4906-13', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Sosialhjelpsmottakere', CAST(0x0000A4B900248E75 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-14', N'4906-14', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andelen sosialhjelpsmottakere i forhold til innbyggere', CAST(0x0000A4B900248DEC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-15', N'4906-15', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andelen sosialhjelpsmottakere i forhold til innbyggere i alderen 20-66 år', CAST(0x0000A4B900248DF5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-16', N'4906-16', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andelen sosialhjelpsmottakere i alderen 20-66 år, av innbyggerne 20-66 år', CAST(0x0000A4B900248DDE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-17', N'4906-17', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Årsverk i sosialtjenesten', CAST(0x0000A4B900248DFA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-18', N'4906-18', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Årsverk i sosialtjenesten pr. 1000 innbygger', CAST(0x0000A4B900248E0D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-19', N'4906-19', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Brutto driftsutgifter pr. sosialhjelpsmottaker, i kroner', CAST(0x0000A4B900248E10 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-20', N'4906-20', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Brutto driftsutgifter til økonomisk sosialhjelp pr. mottaker', CAST(0x0000A4B900248E1A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-21', N'4906-21', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Korrigerte driftsutgifter til sosialtjenesten pr. mottaker', CAST(0x0000A4B900248E2E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-22', N'4906-22', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Lønnsutgifter pr. sosialhjelpsmottaker, i kroner', CAST(0x0000A4B900248E31 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-23', N'4906-23', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Samlet stønadssum (bidrag + lån)', CAST(0x0000A4B900248E72 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-24', N'4906-24', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Gjennomsnittlig utbetaling pr. stønadsmåned', CAST(0x0000A4B900248E2B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-25', N'4906-25', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Gjennomsnittlig stønadslengde mottakere 18-24 år', CAST(0x0000A4B900248E22 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-26', N'4906-26', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Gjennomsnittlig stønadslengde mottakere 25-66 år', CAST(0x0000A4B900248E29 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-27', N'4906-27', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Sosialhjelpsmottakere med stønad i 6 måneder eller mer', CAST(0x0000A4B900248E77 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-28', N'4906-28', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel mottakere med sosialhjelp som hovedinntektskilde', CAST(0x0000A4B900248CEB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-29', N'4906-29', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutgifter til sosialtjenesten inkludert funksjon 273', CAST(0x0000A4B900248E68 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-30', N'4906-30', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'- herav driftsutgifter til funksjon 273', CAST(0x0000A4B900248CD8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-31', N'4906-31', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutg. til kommunale sysselsettingstiltak pr. innbygger 20-66 år', CAST(0x0000A4B900248E53 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-32', N'4906-32', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Lønnsutgifter til sosialtjenesten inkludert funksjon 273', CAST(0x0000A4B900248E34 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-33', N'4906-33', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Årsverk i sosialtjenesten inkludert stillinger til sysselsettingstiltak', CAST(0x0000A4B900248E0B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-34', N'4906-34', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Mottakere av kvalifiseringsstønad per 1000 innbyggere 20-66 år', CAST(0x0000A4B900248E3D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-35', N'4906-35', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Netto driftsutgifter til kvalifiseringsprogrammet per bruker', CAST(0x0000A4B900248E62 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-36', N'4906-36', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel med sosialhjelp som fast supplement samtidig med KVP', CAST(0x0000A4B900248CDD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-37', N'4906-37', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel med KVP og Husbankens bostøtte samtidig', CAST(0x0000A4B900248CDA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7786-38', N'4906-38', N'07786', N'G1. Konsern - Sosialtjenesten - nøkkeltall (K)', N'Andel med sosialhjelp som livsopphold før KVP av alle deltakere', CAST(0x0000A4B900248CE0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7791-13', N'6417-15', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk i helse- og sosialtjenester', CAST(0x0000A4B9002492D5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7791-14', N'6417-16', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk i tekniske tjenester', CAST(0x0000A4B9002492E2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7791-15', N'6417-17', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk i kultur', CAST(0x0000A4B9002492DB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7791-16', N'6417-18', N'07791', N'S1. Konsern - Sysselsetting i kommunene - nøkkeltall (K)', N'Andel avtalte årsverk annet', CAST(0x0000A4B9002492A4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-13', N'4904-13', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel barn som har fullført helseundersøkelse ved 4 års alder', CAST(0x0000A4B900248A2F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-14', N'4904-14', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel barn som har fullført helseundersøkelse innen utgangen av 1. skoletrinn', CAST(0x0000A4B900248A28 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-15', N'4904-15', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Åpningstid ved helsestasjon for ungdom. Sum timer per uke.', CAST(0x0000A4B900248AA9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-16', N'4904-16', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Åpningstid ved helsestasjon for ungdom per 1000 innbyggere 13-20 år', CAST(0x0000A4B900248AA7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-17', N'4904-17', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk i alt pr. 10 000 innbyggere 0-5 år. Funksjon 232', CAST(0x0000A4B900248AD6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-18', N'4904-18', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av leger pr. 10 000 innbyggere 0-5 år. Funksjon 232', CAST(0x0000A4B900248AC6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-19', N'4904-19', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av fysioterapeuter pr. 10 000 innb. 0-5 år. Funksjon 232', CAST(0x0000A4B900248AB1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-20', N'4904-20', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av helsesøstre pr. 10 000 innbyggere 0-5 år. Funksjon 232', CAST(0x0000A4B900248ABA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-21', N'4904-21', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av jordmødre pr. 10 000 fødte. Funksjon 232', CAST(0x0000A4B900248AC0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-22', N'4904-22', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av leger pr. 10 000 innbyggere. Funksjon 120 og 233', CAST(0x0000A4B900248AC9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-23', N'4904-23', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av leger pr. 10 000 innbyggere. Funksjon 241', CAST(0x0000A4B900248ACC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-24', N'4904-24', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel timer av kommunalt ansatte leger og turnuskandidater', CAST(0x0000A4B900248A7F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-25', N'4904-25', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av fysioterapeuter pr. 10 000 innbyggere. Funksjon 241', CAST(0x0000A4B900248AB4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-26', N'4904-26', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel timer av fysioterapeuter med fast lønn og turnuskandidater. Funksjon 241', CAST(0x0000A4B900248A6A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-27', N'4904-27', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Legetimer pr. uke pr. beboer i sykehjem', CAST(0x0000A4B900248AFB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-28', N'4904-28', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Fysioterapitimer pr. uke pr. beboer i sykehjem', CAST(0x0000A4B900248AEB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-29', N'4904-29', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Brutto driftsutgifter pr. innbygger. Funksjon 232, 233 og 241', CAST(0x0000A4B900248ADE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-30', N'4904-30', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Herav: lønnsutgifter pr. innbygger. Funksjon 232, 233 og 241', CAST(0x0000A4B900248AF4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-31', N'4904-31', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Brutto driftsutgifter per innbygger 0 - 5 år. Helsestasjons- og skolehelsetjeneste.', CAST(0x0000A4B900248ADB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-32', N'4904-32', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av ergoterapeuter pr. 10 000 innbyggere (khelse+plo)', CAST(0x0000A4B900248AAC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-33', N'4904-33', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av psykiatriske sykepleiere per 10 000 innbyggere (khelse+plo)', CAST(0x0000A4B900248AD3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-34', N'4904-34', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk til rehabilitering pr. 10 000 innbyggere (khelse + plo)', CAST(0x0000A4B900248AD8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-35', N'4904-35', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Antall personer med videreutdanning i psykisk helsearbeid per 10 000 innbyggere (khelse og plo)', CAST(0x0000A4B900248A86 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-36', N'4904-36', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av kommunale fysioterapeuter pr 10 000 innbyggere Funksjon 232, 233, 2', CAST(0x0000A4B900248AC2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-37', N'4904-37', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Årsverk av private fysioterapeuter per avtalehjemmel', CAST(0x0000A4B900248AD0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-38', N'4904-38', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Gjennomsnittlig listelengde', CAST(0x0000A4B900248AEF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-39', N'4904-39', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Gjennomsnittlig listelengde korrigert for kommunale timer', CAST(0x0000A4B900248AF1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-40', N'4904-40', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Antall åpne fastlegelister', CAST(0x0000A4B900248A84 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-41', N'4904-41', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Reservekapasitet fastlege', CAST(0x0000A4B900248B17 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-42', N'4904-42', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel kvinnelige leger', CAST(0x0000A4B900248A31 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7793-43', N'4904-43', N'07793', N'E1. Konsern - Kommunehelse - nøkkeltall (K)', N'Andel pasienter på liste uten lege', CAST(0x0000A4B900248A48 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-13', N'4905-15', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Mottakere av hjemmetjenester, pr 1000 innb 0-66 år', CAST(0x0000A4B900248CAC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-14', N'4905-16', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Mottakere av hjemmetjenester, pr. 1000 innb. 67-79 år.', CAST(0x0000A4B900248CAE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-15', N'4905-17', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Mottakere av hjemmetjenester, pr. 1000 innb. 80 år og over.', CAST(0x0000A4B900248CB1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-16', N'4905-18', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Korrigerte brutto driftsutg pr. mottaker av hjemmetjenester (i kroner)', CAST(0x0000A4B900248C9E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-17', N'4905-19', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Brukerbetaling, praktisk bistand, i prosent av korrigerte brutto driftsutg', CAST(0x0000A4B900248C7D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-18', N'4905-20', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel hjemmeboere med høy timeinnsats', CAST(0x0000A4B900248B4B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-19', N'4905-21', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel hjemmetj.mottakere med omfattende bistandsbehov, 67 år og over', CAST(0x0000A4B900248B51 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-20', N'4905-22', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'System for brukerundersøkelser i hjemmetjenesten', CAST(0x0000A4B900248CCB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-21', N'4905-23', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel beboere i bolig til pleie- og omsorgsformål 80 år og over', CAST(0x0000A4B900248B39 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-22', N'4905-24', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel beboere i bolig m/ heldøgns bemanning', CAST(0x0000A4B900248B35 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-23', N'4905-25', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel innbyggere 80 år og over i bolig med heldøgns bemanning', CAST(0x0000A4B900248B5E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-24', N'4905-26', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Plasser i institusjon i prosent av mottakere av pleie- og omsorgstjenester', CAST(0x0000A4B900248CC8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-25', N'4905-27', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Plasser i institusjon i prosent av innbyggere 80 år over', CAST(0x0000A4B900248CC5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-26', N'4905-28', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel plasser i institusjon og heldøgnsbemannet bolig i prosent av bef. 80+', CAST(0x0000A4B900248C6F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-27', N'4905-29', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel beboere på institusjon under 67 år', CAST(0x0000A4B900248B3F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-28', N'4905-31', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel beboere 80 år og over i institusjoner', CAST(0x0000A4B900248B33 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-29', N'4905-30', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel innbyggere 67 år og over som er beboere på institusjon', CAST(0x0000A4B900248B59 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-30', N'4905-32', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel innbyggere 67-79 år som er beboere på institusjon', CAST(0x0000A4B900248B5B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-31', N'4905-33', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel innbyggere 80 år og over som er beboere på institusjon', CAST(0x0000A4B900248B69 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-32', N'4905-34', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel beboere i institusjon av antall plasser (belegg)', CAST(0x0000A4B900248B3C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-33', N'4905-35', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel plasser avsatt til tidsbegrenset opphold', CAST(0x0000A4B900248C58 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-34', N'4905-36', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel plasser i skjermet enhet for personer med demens', CAST(0x0000A4B900248C76 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-35', N'4905-37', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel plasser avsatt til rehabilitering/habilitering', CAST(0x0000A4B900248C32 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-36', N'4905-38', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Legetimer pr. uke pr. beboer i sykehjem', CAST(0x0000A4B900248CA6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-37', N'4905-39', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Fysioterapitimer pr. uke pr. beboer i sykehjem', CAST(0x0000A4B900248C7F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-38', N'4905-40', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel plasser i enerom i pleie- og omsorgsinstitusjoner', CAST(0x0000A4B900248C65 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-39', N'4905-41', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel plasser i brukertilpasset enerom m/ eget bad/wc', CAST(0x0000A4B900248C5F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-40', N'4905-42', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'System for brukerundersøkelser i institusjon', CAST(0x0000A4B900248CCE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-41', N'4905-43', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Utgifter per beboerdøgn i institusjon', CAST(0x0000A4B900248CD4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-42', N'4905-44', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel kommunale institusjonsplasser av totalt antall institusjonsplasser', CAST(0x0000A4B900248B6D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-43', N'4905-45', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Korrigerte brutto driftsutgifter, institusjon, pr. kommunal plass', CAST(0x0000A4B900248CA3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-44', N'4905-46', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Korr.bto.driftsutg, pleie, av korr.bto.driftsutg, institusjon', CAST(0x0000A4B900248C9C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-45', N'4905-47', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Brukerbetaling i institusjon i forhold til korrigerte brutto driftsutgifter', CAST(0x0000A4B900248C7B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-46', N'4905-48', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Netto driftsutgifter til aktivisering/støttetjenester per innbygger 18 år og o', CAST(0x0000A4B900248CBD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-47', N'4905-49', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel hjemmetj.mottakere med omfattende bistandsbehov, 0-66 år', CAST(0x0000A4B900248B4E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-48', N'4905-50', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel hjemmetj.mottakere med omfattende bistandsbehov, 67-79 år', CAST(0x0000A4B900248B54 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-49', N'4905-51', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel hjemmetj.mottakere med omfattende bistandsbehov, 80 år og over', CAST(0x0000A4B900248B56 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-50', N'4905-52', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel brukere (ekskl. langtidsbeboere på institusjon) med individuell plan', CAST(0x0000A4B900248B42 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-51', N'4905-53', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel av brukere (%) med noe/avgrenset bistandsbehov', CAST(0x0000A4B900248B30 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-52', N'4905-54', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel av alle brukere som har omfattende bistandsbehov', CAST(0x0000A4B900248B28 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-53', N'4905-55', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Gjennomsnittlig antall tildelte timer pr uke, praktisk bistand', CAST(0x0000A4B900248C87 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-54', N'4905-56', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Gjennomsnittlig antall tildelte timer pr uke, hjemmesykepleie', CAST(0x0000A4B900248C84 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-55', N'4905-57', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Gjennomsnittlig antall tildelte timer i uken. Brukere utenfor institusjon', CAST(0x0000A4B900248C82 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-56', N'4905-58', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel brukere i institusjon som har omfattende bistandsbehov: Tidsbegrenset opphold.', CAST(0x0000A4B900248B48 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-57', N'4905-59', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel brukere i institusjon som har omfattende bistandsbehov: Langtidsopphold', CAST(0x0000A4B900248B45 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'7800-58', N'4905-60', N'07800', N'F1. Konsern - Pleie og omsorg - nøkkeltall (K)', N'Andel aleneboende mottakere utenfor inst. med både hjemmetj. og støttetj.', CAST(0x0000A4B900248B23 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-15', N'4907-15', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Brutto driftsutgifter per barn (funksjon 244)', CAST(0x0000A4B900248EA6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-16', N'4907-16', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Brutto driftsutgifter til tiltak (funksjon 251, 252) per barn med tiltak', CAST(0x0000A4B900248EB0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-17', N'4907-17', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Brutto driftsutgifter per barn som ikke er plassert av barnevernet (funksjon 251)', CAST(0x0000A4B900248EAD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-18', N'4907-18', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Brutto driftsutgifter per barn som er plassert av barnevernet (funksjon 252)', CAST(0x0000A4B900248EA9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-19', N'4907-19', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Barn med undersøkelse eller tiltak per årsverk', CAST(0x0000A4B900248E95 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-21', N'4907-21', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel undersøkelser med behandlingstid over tre måneder', CAST(0x0000A4B900248E8F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-22', N'4907-22', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel barn med tiltak per 31.12. med utarbeidet plan', CAST(0x0000A4B900248E80 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-23', N'4907-23', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Stillinger med fagutdanning per 1 000 barn 0-17 år', CAST(0x0000A4B900248EC6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-28', N'4907-28', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Andel undersøkelser som fører til tiltak', CAST(0x0000A4B900248E92 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-37', N'4907-37', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'System for brukerundersøkelser i barnevernstjenesten', CAST(0x0000A4B900248EC8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-38', N'4907-38', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Benyttet brukerundersøkelse siste år', CAST(0x0000A4B900248EA3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-39', N'4907-39', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Innført internkontroll i barnevernstjenesten', CAST(0x0000A4B900248EB7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-40', N'4907-40', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Barnevernstjenesten organisert i interkommunalt samarbeid', CAST(0x0000A4B900248E9A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8845-41', N'4907-41', N'08845', N'H1. Konsern - Barnevern - nøkkeltall (K)', N'Barnevernstjenesten organisert i NAV', CAST(0x0000A4B900248E9E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-100', N'4908-103', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel av kommunale planer (komm-pl + reg-pl) som det er fremmet innsigelse til.', CAST(0x0000A4B900248ED2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-114', N'4908-109', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel søknader om motorferdsel i utmark innvilget.', CAST(0x0000A4B900248EF4 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-13', N'4908-14', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Alder for kommuneplanens samfunnsdel', CAST(0x0000A4B900248ECD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-17', N'4908-18', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Antall bebyggelsesplaner vedtatt av kommunen siste år', CAST(0x0000A4B900248EF7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-21', N'4908-22', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til bygge-, delesaksbeh. og seksjonering per innbygger.', CAST(0x0000A4B900248FA5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-22', N'4908-23', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til bygge-, delesaksbeh. og seksjonering per innbygger.', CAST(0x0000A4B900248F2B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-23', N'4908-25', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til kart og oppmåling per innbygger.', CAST(0x0000A4B900248FAD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-24', N'4908-26', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til kart og oppmåling per innbygger.', CAST(0x0000A4B900248F31 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-25', N'4908-27', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til rekreasjon i tettsteder per innbygger.', CAST(0x0000A4B900249056 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-26', N'4908-28', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til rekreasjon i tettsteder per innbygger.', CAST(0x0000A4B900248F3C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-27', N'4908-29', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til naturforvaltning og friluftsliv per innbygger.', CAST(0x0000A4B900248FB6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-28', N'4908-30', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til naturforvaltning og friluftsliv per innbygger.', CAST(0x0000A4B900248F36 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-29', N'4908-31', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Netto driftsutgifter til kulturminnevern per innbygger.', CAST(0x0000A4B900248FB1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-30', N'4908-32', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto investeringsutgifter til kulturminnevern per innbygger.', CAST(0x0000A4B900248F33 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-33', N'4908-35', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutgifter til fysisk planlegging, kulturminnevern, natur og nærmiljø per innb.', CAST(0x0000A4B900248F1B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-34', N'4908-36', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel av dette lønnsutgifter.', CAST(0x0000A4B900248ECF AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-37', N'4908-46', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsinntekter til plansaksbehandling, per innbygger', CAST(0x0000A4B900248F08 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-38', N'4908-52', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto dr.utg bygge- og delesaksbeh og seksjonering pr m2 godkj. bruksareal. Kr', CAST(0x0000A4B900248EFB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-40', N'4908-59', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Korr. brutto driftsutg. bygge-, delesaksbeh. og seksjonering pr årsinnb. 1000kr', CAST(0x0000A4B900248F79 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-41', N'4908-43', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutgifter plansaksbehandling som andel av kommunens samlede brutto driftsutgifter', CAST(0x0000A4B900248F15 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-42', N'4908-44', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutg. til plansaksbehandling (funk. 301), per innb.', CAST(0x0000A4B900248F10 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-43', N'4908-45', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsinntekter plansaksbehandling som andel av kommunens samlede brutto driftsinntekter', CAST(0x0000A4B900248F04 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-45', N'4908-47', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kjøp av varer og tj. som erstatter kommunens egenprod, plansaksbehandling, per årsinnbygger', CAST(0x0000A4B900248F55 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-46', N'4908-42', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Korrigerte brutto driftsutgifter fysisk tilrettelegging og planlegging, per årsinnbygger', CAST(0x0000A4B900248F82 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-53', N'4908-40', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsinntekter fysisk planlegging som andel av kommunens samlede brutto driftsinntekter', CAST(0x0000A4B900248EFD AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-55', N'4908-58', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kjøp av varer og tj. til bygge-, delesaksbeh. og seksjonering per årsinnb. Kr', CAST(0x0000A4B900248F59 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-57', N'4908-60', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel driftsutg til byggesaksbeh av driftsutg til byggesak, kart og plan. Prosent', CAST(0x0000A4B900248EEA AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-58', N'4908-61', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel driftsinnt til byggesaksbeh av driftsinnt.byggesak, kart og plan. Prosent', CAST(0x0000A4B900248EE1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-59', N'4908-62', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutgifter kart og oppmåling som andel av kommunens samlede brutto driftsutgifter', CAST(0x0000A4B900248F13 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-60', N'4908-63', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutg. til kart og oppmåling (funk. 303), per innb.', CAST(0x0000A4B900248F0A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-61', N'4908-64', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsinntekter kart og oppmåling som andel av kommunens samlede brutto driftsinntekter', CAST(0x0000A4B900248F01 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-62', N'4908-65', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsinntekter til kart og oppmåling, per innbygger', CAST(0x0000A4B900248F05 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-63', N'4908-66', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kjøp av varer og tj. som erstatter kommunens egenprod, kart og oppmåling, per årsinnbygger', CAST(0x0000A4B900248F4F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-64', N'4908-67', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Korrigerte brutto driftsutgifter kart og oppmåling, per årsinnbygger', CAST(0x0000A4B900248F8D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-65', N'4908-68', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel driftsinnt fra kart/oppm av driftsinnt fra byggesak, kart og pl. Prosent', CAST(0x0000A4B900248EDE AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-66', N'4908-69', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel driftsutg kart/oppmåling av driftsutg til byggesak, kart og plan. Prosent', CAST(0x0000A4B900248EE5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-67', N'4908-70', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Saksbeh.gebyr, privat reg.plan, boligformål. jf. PBL-08 § 33-1.', CAST(0x0000A4B90024905B AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-68', N'4908-71', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Saksbeh.gebyret for oppføring av enebolig, jf. PBL-08 §20-1 a', CAST(0x0000A4B900249061 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-69', N'4908-72', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Standardgebyr for oppmålingsforetning for areal tilsvarende en boligtomt 750 m2.', CAST(0x0000A4B900249069 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-73', N'4908-77', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Gjennomsnittlig saksbehandlingstid for byggesaker med 12 ukers frist (kalender', CAST(0x0000A4B900248F42 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-74', N'4908-76', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Gjennomsnittlig saksbehandlingstid for byggesaker med 3 ukers frist (kalenderd', CAST(0x0000A4B900248F44 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-75', N'4908-78', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel søkn. om tiltak der komm. har overskredet lovpålagt saksbehandlingstid', CAST(0x0000A4B900248EF2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-76', N'4908-79', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Gjennomsnittlig saksbehandlingstid, opprettelse av grunneiendom(kalenderdager)', CAST(0x0000A4B900248F47 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-78', N'4908-81', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutg til kulturminner, natur og nærmiljø (funk 335, 360 og 365), i', CAST(0x0000A4B900248F0D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-79', N'4908-82', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutgifter til rekreasjon i tettsteder per innbygger.', CAST(0x0000A4B900248F29 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-80', N'4908-83', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kjøp av varer og tj. som erstatter komm egenproduksjon, rekreasjon i tettsteder, pr årsinnbygg', CAST(0x0000A4B900248F4C AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-81', N'4908-84', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Korrigerte brutto driftsutgifter rekreasjon i tettsteder, per årsinnbygger', CAST(0x0000A4B900248F9D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-82', N'4908-85', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutgifter til naturforvaltning og friluftsliv per innbygger.', CAST(0x0000A4B900248F20 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-83', N'4908-86', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kjøp av varer og tj. som erstatter komm egenprod., naturforvaltning og friluftsliv, pr årsinnbygg', CAST(0x0000A4B900248F49 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-84', N'4908-87', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Korrigerte brutto driftsutgifter naturforvaltning og friluftsliv, per årsinnbygger', CAST(0x0000A4B900248F9A AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-85', N'4908-88', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Brutto driftsutgifter til kulturminnevern per innbygger.', CAST(0x0000A4B900248F1E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-86', N'4908-89', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kjøp av varer og tjenester som erstatter kommunens egenproduksjon, kulturminnevern, per årsinnbygger', CAST(0x0000A4B900248F5E AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-87', N'4908-90', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Korrigerte brutto driftsutgifter kulturminnevern, per årsinnbygger', CAST(0x0000A4B900248F97 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-89', N'4908-92', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kommunale energikostnader', CAST(0x0000A4B900248F6D AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-90', N'4908-93', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Kommunale energikostnader, per innbygger', CAST(0x0000A4B900248F73 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-91', N'4908-94', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel energikostnader i prosent av kommunens samlede brutto driftsutgifter', CAST(0x0000A4B900248EEC AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-92', N'4908-95', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel av leke- og rekreasjonsarealer som er under kommunalt driftsansvar.', CAST(0x0000A4B900248ED5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-93', N'4908-96', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Friluftslivsområder m/kommunal råderett gjennom offentlig eie eller bruksavtale. 10000 årsinnb.', CAST(0x0000A4B900248F3F AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-94', N'4908-97', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Rekr- og friluftslivsomr med kommunalt ansvar tilrettelagt med universell utfo', CAST(0x0000A4B900249058 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-95', N'4908-98', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Samlet lengde av turveier, turstier og skiløyper (km)', CAST(0x0000A4B900249065 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-96', N'4908-99', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Turstier og løyper tilrettelagt for sommerbruk per 10 000 innbygger', CAST(0x0000A4B900249075 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-97', N'4908-100', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Andel av turstier og sommerløyper som er under kommunalt driftsansvar', CAST(0x0000A4B900248EDB AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-98', N'4908-101', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Samlet lengde maskinpreparerte skiløyper.', CAST(0x0000A4B900249067 AS DateTime), 1)
GO
INSERT [dbo].[gmd_kostra_corp_indicators] ([pk_corp_indicator_code], [fk_indicator_code], [kostra_table_id], [kostra_table], [ssb_description], [updated], [updated_by]) VALUES (N'8855-99', N'4908-102', N'08855', N'J1. Konsern - Fysisk planlegging, kulturminner, natur og nærmiljø - nivå 2 (K)', N'Sykkel-, gangveier/turstier mv. m/kom. driftsansvar per 10 000 innb.', CAST(0x0000A4B900249073 AS DateTime), 1)
GO
