
SET IDENTITY_INSERT [dbo].[gmd_fp_demographic_intervals] ON 

GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (1, 12, N'0 år', N'0 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (2, 13, N'1-2 år', N'1-2 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (3, 22, N'10-12 år', N'10-12 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (4, 23, N'13-15 år', N'13-15 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (5, 31, N'16-17 år', N'16-17 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (6, 32, N'18-19 år', N'18-19 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (7, 41, N'20-44 år', N'20-44 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (8, 14, N'3-5 år', N'3-5 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (9, 42, N'45-66 år', N'45-66 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (10, 21, N'6-9 år', N'6-9 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (11, 51, N'67-79 år', N'67-79 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (12, 52, N'80-89 år', N'80-89 år', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (13, 53, N'90 år eller eldre', N'90 år eller eldre', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (14, 11, N'Standard barn', N'Standard barn', 1, CAST(0x0000A48100BCC4EA AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (15, 10, N'0-5 år', N'0 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (16, 10, N'0-5 år', N'1-2 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (17, 10, N'0-5 år', N'3-5 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (18, 20, N'6-15 år', N'6-9 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (19, 20, N'6-15 år', N'10-12 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (20, 20, N'6-15 år', N'13-15 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (21, 30, N'16-19 år', N'16-17 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (22, 30, N'16-19 år', N'18-19 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (23, 40, N'20-66 år', N'20-44 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (24, 40, N'20-66 år', N'45-66 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (25, 50, N'67 år og eldre', N'67-79 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (26, 50, N'67 år og eldre', N'80-89 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (27, 50, N'67 år og eldre', N'90 år eller eldre', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (28, 15, N'1-5 år', N'1-2 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
INSERT [dbo].[gmd_fp_demographic_intervals] ([pk_id], [interval_id], [interval_name], [fk_age_interval], [active], [updated], [updated_by]) VALUES (29, 15, N'1-5 år', N'3-5 år', 1, CAST(0x0000A48100BCCDCD AS DateTime), 1016)
GO
SET IDENTITY_INSERT [dbo].[gmd_fp_demographic_intervals] OFF
GO
