SET IDENTITY_INSERT [dbo].[gmd_age_intervals_mapping] ON 

INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (1, N'201', N'0 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (2, N'201', N'1-2 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (3, N'201', N'3-5 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (4, N'201', N'Standard Barn', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (5, N'202', N'6-12 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (6, N'202', N'13-15 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (7, N'215', N'6-9 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (8, N'215', N'10-12 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (9, N'215', N'13-15 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (10, N'223', N'6-9 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (11, N'223', N'10-12 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (12, N'223', N'13-15 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (13, N'251', N'67-79 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (14, N'251', N'80-89 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (15, N'251', N'90 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (16, N'251', N'eller eldre', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (17, N'253', N'67-79 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (18, N'253', N'80-89 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (19, N'253', N'90 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (20, N'253', N'eller eldre', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (21, N'254', N'67-79 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (22, N'254', N'80-89 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (23, N'254', N'90 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (24, N'254', N'eller eldre', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (25, N'256', N'67-79 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (26, N'256', N'80-89 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (27, N'256', N'90 år', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
INSERT [dbo].[gmd_age_intervals_mapping] ([pk_id], [fk_kostra_function_code], [age_intervals], [updated_by], [updated]) VALUES (28, N'256', N'eller eldre', 1, CAST(N'2014-11-10 11:06:33.143' AS DateTime))
SET IDENTITY_INSERT [dbo].[gmd_age_intervals_mapping] OFF
