
SET IDENTITY_INSERT [dbo].[gko_kpi] ON 

GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (18, N'1096126', N'Profit vs revenue', N'en-US', 1, 1, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (19, N'1096429', N'Reserve fund', N'en-US', 1, 2, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (20, N'4901-15', N'Debt vs revenue', N'en-US', 1, 3, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (21, N'4901-41', N'Debt pr citizen', N'en-US', 1, 4, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (22, N'1096126', N'Netto driftsresultat i prosent av brutto driftsinntekter', N'nb-NO', 1, 1, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (23, N'1096429', N'Disposisjonsfond i prosent av brutto driftsinntekter', N'nb-NO', 1, 2, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (24, N'4901-15', N'Netto lånegjeld i prosent av brutto driftsinntekter', N'nb-NO', 1, 3, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_kpi] ([pk_id], [fk_indicator_code], [kpi_name], [language_code], [active], [kpi_id], [updated], [updated_by]) VALUES (25, N'4901-41', N'Netto lånegjeld i kroner per innbygger', N'nb-NO', 1, 4, CAST(0x0000A3C000000000 AS DateTime), 1)
GO
SET IDENTITY_INSERT [dbo].[gko_kpi] OFF
GO