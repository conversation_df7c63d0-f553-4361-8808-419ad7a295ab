SET IDENTITY_INSERT [dbo].[gmd_assessment_areas] ON
GO

INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (1, 1, N'Quality', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (3, 1, N'Kvalitet', 1, N'nb-NO', 1, CAST(N'2014-02-10 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (4, 2, N'Productivity', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (5, 2, N'Produktivitet', 1, N'nb-NO', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (6, 3, N'Revenue', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (7, 3, N'Omsetning', 1, N'nb-NO', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (8, 4, N'Coverage', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (9, 4, N'Dekningsgrad', 1, N'nb-NO', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (10, 5, N'Structure', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (11, 5, N'Struktur', 1, N'nb-NO', 1, CAST(N'2014-02-10 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (12, 6, N'Activity', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (13, 6, N'Aktivitet', 1, N'nb-NO', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (14, 7, N'Price level', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (15, 7, N'Prisnivå', 1, N'nb-NO', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (16, 8, N'Work organisation', 1, N'en-US', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))
INSERT [dbo].[gmd_assessment_areas] ([pk_id], [area_id], [area_name], [active], [language_code], [updated_by], [updated]) VALUES (17, 8, N'Arbeidsdeling', 1, N'nb-NO', 1, CAST(N'2014-10-02 00:00:00.000' AS DateTime))

SET IDENTITY_INSERT [dbo].[gmd_assessment_areas] OFF
GO
