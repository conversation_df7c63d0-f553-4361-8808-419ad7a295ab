SET IDENTITY_INSERT [dbo].[gmd_pop_indicator_intervals] ON 

INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (1, N'pf1', N'0 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (2, N'pf1', N'1-2 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (3, N'pf1', N'3-5 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (4, N'pf2', N'6-9 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (5, N'pf2', N'10-12 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (6, N'pf2', N'13-15 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (7, N'pf3', N'16-17 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (8, N'pf3', N'18-19 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (9, N'pf4', N'20-44 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (10, N'pf4', N'45-66 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (11, N'pf5', N'67-79 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (12, N'pf5', N'80-89 år', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[gmd_pop_indicator_intervals] ([pk_id], [fk_pop_indicator_id], [age_interval], [updated], [updated_by]) VALUES (13, N'pf5', N'90 år eller eldre', CAST(N'2014-11-05 00:00:00.000' AS DateTime), 1)
SET IDENTITY_INSERT [dbo].[gmd_pop_indicator_intervals] OFF