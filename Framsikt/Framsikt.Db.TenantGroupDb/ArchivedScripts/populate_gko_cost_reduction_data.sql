delete from [gko_cost_reduction_data]
go

DECLARE @YEAR INT
SET @YEAR = (SELECT DATEPART(YEAR, GETDATE())-1);

INSERT INTO [gko_cost_reduction_data] (fk_municipality_id, fk_report_area_code, age_group, kostra_group, fk_indicator_code, inhabitants, 
[bestin_region_code], [bestin_kostra_group], [bestin_kostra_value],[exp_bestin_region_code], [exp_bestin_kostra_group], [exp_bestin_kostra_value],type, updated, updated_by)
SELECT b.fk_municipality_id, a.fk_report_area_code, a.age_group, c.kostra_group, d.fk_indicator_code,  SUM(b.forecast) as inhabitants, 
MAX(g.fk_region_code), ' ', e.indicator_value, ' ', ' ', 0, 'city', getdate(), 1016
FROM [gmd_area_age_intervals] a, gco_pop_forecast b, gco_municipalities c, gko_main_indicators d,
(SELECT t.fk_indicator_code, s.kostra_group, MIN(t.indicator_value) as indicator_value
FROM gko_main_indicators r, gco_municipalities s, gko_kostra_data t
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year = @YEAR
AND r.type = 4
and t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.fk_indicator_code = t.fk_indicator_code
GROUP BY t.fk_indicator_code, s.kostra_group ) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
left outer join gko_kostra_data g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code
and e.indicator_value = g.indicator_value
WHERE a.age_interval = b.age_interval
AND b.forecast_type = 'MMMM'
AND b.year = @YEAR
AND b.fk_municipality_id = c.pk_municipality_id
AND d.type = 4 AND d.active = 1
AND a.fk_report_area_code = d.fk_report_area_code
AND c.kostra_group = e.kostra_group
AND d.fk_indicator_code = e.fk_indicator_code
and g.fk_region_code is not null
GROUP BY b.fk_municipality_id, a.fk_report_area_code, a.age_group, c.kostra_group, d.fk_indicator_code, e.indicator_value;

INSERT INTO [gko_cost_reduction_data] (fk_municipality_id, fk_report_area_code, age_group, kostra_group, fk_indicator_code, inhabitants, 
[bestin_region_code], [bestin_kostra_group], [bestin_kostra_value],[exp_bestin_region_code], [exp_bestin_kostra_group], [exp_bestin_kostra_value],type, updated, updated_by)
SELECT b.fk_municipality_id, a.fk_report_area_code, a.age_group, c.kostra_group, d.fk_indicator_code,  SUM(b.forecast) as inhabitants, 
MAX(g.fk_region_code), ' ', e.indicator_value, ' ', ' ', 0, 'corp', getdate(), 1016
FROM [gmd_area_age_intervals] a, gco_pop_forecast b, gco_municipalities c, gko_main_indicators d,
(SELECT t.fk_indicator_code, s.kostra_group, MIN(t.indicator_value) as indicator_value
FROM gko_main_indicators r, gco_municipalities s, gko_kostra_data_corp t
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year = @YEAR
AND r.type = 4
and t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.fk_indicator_code = t.fk_indicator_code
GROUP BY t.fk_indicator_code, s.kostra_group ) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
left outer join gko_kostra_data_corp g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code
and e.indicator_value = g.indicator_value
WHERE a.age_interval = b.age_interval
AND b.forecast_type = 'MMMM'
AND b.year = @YEAR
AND b.fk_municipality_id = c.pk_municipality_id
AND d.type = 4 AND d.active = 1
AND a.fk_report_area_code = d.fk_report_area_code
AND c.kostra_group = e.kostra_group
AND d.fk_indicator_code = e.fk_indicator_code
and g.fk_region_code is not null
GROUP BY b.fk_municipality_id, a.fk_report_area_code, a.age_group, c.kostra_group, d.fk_indicator_code, e.indicator_value;



with tbl_city_adjusted as (SELECT a.fk_report_area_code, c.kostra_group, d.fk_indicator_code,  
MAX(g.fk_region_code) as region_code, e.indicator_value
FROM [gmd_area_age_intervals] a, gco_municipalities c, gko_main_indicators d, 
(SELECT r.fk_report_area_code, t.fk_indicator_code, s.kostra_group, MIN(t.indicator_value / v.expence_need) as indicator_value
FROM gko_main_indicators r, gco_municipalities s, gko_kostra_data t, gmd_expence_need v
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year = @YEAR
AND r.type = 4
AND t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.fk_indicator_code = t.fk_indicator_code
AND r.fk_report_area_code = v.fk_report_area_code 
AND t.fk_region_code = v.fk_municipality_id
AND t.year = v.year
GROUP BY r.fk_report_area_code, t.fk_indicator_code, s.kostra_group) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
LEFT OUTER JOIN gmd_expence_need w
On e.fk_report_area_code = w.fk_report_area_code
AND w.year = @YEAR
left outer join gko_kostra_data g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code 
and e.indicator_value= g.indicator_value / w.expence_need 
AND w.fk_municipality_id = g.fk_region_code
AND w.year = g.year
WHERE  d.type = 4 AND d.active = 1
AND a.fk_report_area_code = d.fk_report_area_code
AND c.kostra_group = e.kostra_group
AND d.fk_indicator_code = e.fk_indicator_code
and g.fk_region_code is not null
GROUP BY a.fk_report_area_code,c.kostra_group, d.fk_indicator_code, e.indicator_value)
UPDATE gko_cost_reduction_data SET  exp_bestin_region_code = k.region_code, exp_bestin_kostra_value = k.indicator_value
FROM gko_cost_reduction_data a, tbl_city_adjusted k
WHERE a.kostra_group = k.kostra_group
AND a.fk_report_area_code = k.fk_report_area_code
AND a.type = 'city'
;


with tbl_city_adjusted as (SELECT a.fk_report_area_code, c.kostra_group, d.fk_indicator_code,  
MAX(g.fk_region_code) as region_code, e.indicator_value
FROM [gmd_area_age_intervals] a, gco_municipalities c, gko_main_indicators d, 
(SELECT r.fk_report_area_code, t.fk_indicator_code, s.kostra_group, MIN(t.indicator_value / v.expence_need) as indicator_value
FROM gko_main_indicators r, gco_municipalities s, gko_kostra_data_corp t, gmd_expence_need v
WHERE  s.pk_municipality_id = t.fk_region_code
AND t.year = @YEAR
AND r.type = 4
AND t.indicator_value !=0  --Lagt til fordi  det kommer 0 på mange indikatorer. riktig hvis det kan være 0, men det legges inn 0 der det ikke er rapportert tall også
AND r.fk_indicator_code = t.fk_indicator_code
AND r.fk_report_area_code = v.fk_report_area_code 
AND t.fk_region_code = v.fk_municipality_id
AND t.year = v.year
GROUP BY r.fk_report_area_code, t.fk_indicator_code, s.kostra_group) e
left outer join gco_municipalities f
ON  e.kostra_group = f.kostra_group
LEFT OUTER JOIN gmd_expence_need w
On e.fk_report_area_code = w.fk_report_area_code
AND w.year = @YEAR
left outer join gko_kostra_data_corp g
ON e.fk_indicator_code = g.fk_indicator_code
AND f.pk_municipality_id = g.fk_region_code 
and e.indicator_value= g.indicator_value / w.expence_need 
AND w.fk_municipality_id = g.fk_region_code
AND w.year = g.year
WHERE  d.type = 4 AND d.active = 1
AND a.fk_report_area_code = d.fk_report_area_code
AND c.kostra_group = e.kostra_group
AND d.fk_indicator_code = e.fk_indicator_code
and g.fk_region_code is not null
GROUP BY a.fk_report_area_code,c.kostra_group, d.fk_indicator_code, e.indicator_value)

UPDATE gko_cost_reduction_data SET  exp_bestin_region_code = k.region_code, exp_bestin_kostra_value = k.indicator_value
FROM gko_cost_reduction_data a, tbl_city_adjusted k
WHERE a.kostra_group = k.kostra_group
AND a.fk_report_area_code = k.fk_report_area_code
AND a.type = 'corp'
;



WITH tbl_inhabitants AS (
SELECT b.fk_municipality_id, a.fk_report_area_code, SUM(b.forecast) as sum_inhabitants
from [gmd_area_age_intervals] a, gco_pop_forecast b, gko_main_indicators d
WHERE a.age_interval = b.age_interval
AND b.forecast_type = 'MMMM'
AND b.year = @YEAR+1
AND d.type = 4 AND d.active = 1
AND a.fk_report_area_code = d.fk_report_area_code
GROUP BY b.fk_municipality_id, a.fk_report_area_code, d.fk_indicator_code)
UPDATE gko_cost_reduction_data SET inhabitants = k.sum_inhabitants 
FROM gko_cost_reduction_data a, tbl_inhabitants k
WHERE a.fk_municipality_id = k.fk_municipality_id
AND a.fk_report_area_code = k.fk_report_area_code
;

UPDATE [gko_cost_reduction_data] SET bestin_kostra_group = b.region_name
FROM gko_cost_reduction_data a, gco_region_codes b
WHERE a.bestin_region_code = b.pk_region_code
;


UPDATE [gko_cost_reduction_data] SET exp_bestin_kostra_group = b.region_name
FROM gko_cost_reduction_data a, gco_region_codes b
WHERE a.EXP_bestin_region_code = b.pk_region_code

;