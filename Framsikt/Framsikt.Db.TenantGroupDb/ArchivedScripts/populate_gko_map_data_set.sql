delete from gko_map_data_set
go

with ReportNetExpence as (
SELECT b.fk_region_code, 
CONVERT(INT,SUM(b.indicator_value / d.expence_need)) AS expence_adjusted_value  
FROM gko_main_indicators a, gko_kostra_data b, gmd_expence_need d
WHERE a.type = 3
AND a.active = 1
AND b.fk_indicator_code = a.fk_indicator_code 
AND a.fk_report_area_code = d.fk_report_area_code
AND b.year = 2014 
AND b.fk_region_code = d.fk_municipality_id
AND b.year = d.year
GROUP BY b.fk_region_code
)

INSERT INTO gko_map_data_set (pk_municipality_id, municipality_name, county_id, county_name, latitude, longitude, geojson_properties, geojson_geometry, kostra_group,  ko_revenue, expence_adjusted_value,
total_population, area, pop_density, popgrowth_perc_3years, "0to5_pct", "6to15_pct", "80to89_pct", "90_older_pct", cr_school, cr_care, cr_child_care, cr_kindergarten, cr_total)
SELECT b.pk_municipality_id, b.municipality_name, b.county_id, b.county_name, a.latitude, a.longitude, a.geojson_properties, a.geojson_geometry, b.kostra_group,  rev.indicator_value as 'ko_revenue',   
CONVERT(INT, net.expence_adjusted_value) AS expence_adjusted_value, po1.population as total_population, a.ground_areal_km2 as area, CONVERT(DEC(9,1),convert(dec (18,3), po1.population) / convert (dec(18,3), a.ground_areal_km2)) as pop_density,  
convert(dec(3,1), convert (dec(18,2), (po1.population - po2.population))/convert(dec(18,2), po2.population)*100) as popgrowth_perc_3years,  
convert(dec(3,1),convert(dec(18,2), po3.population)*100/convert(dec(18,2), po1.population)) as '0to5_pct',  
convert(dec(3,1),convert(dec(18,2), po4.population)*100/convert(dec(18,2), po1.population)) as '6to15_pct',  
convert(dec(3,1),convert(dec(18,2), po5.population)*100/convert(dec(18,2), po1.population)) as '80to89_pct',  
convert(dec(3,1),convert(dec(18,2), po6.population)*100/convert(dec(18,2), po1.population)) as '90_older_pct',  
cr1.indicator_value AS 'cr_school',   
cr2.indicator_value as 'cr_care', cr3.indicator_value as 'cr_child_care', cr4.indicator_value as 'cr_kindergarten',  
cr5.indicator_value as 'cr_total'  
FROM gco_municipalities as b  
LEFT OUTER JOIN gco_region_codes a on a.pk_region_code = b.pk_municipality_id  
LEFT OUTER JOIN gko_kostra_data cr1 on cr1.fk_region_code = b.pk_municipality_id AND cr1.fk_indicator_code = 'gsk' AND cr1.year = 2015   
LEFT OUTER JOIN gko_kostra_data cr2 on cr2.fk_region_code = b.pk_municipality_id AND cr2.fk_indicator_code = 'po' AND cr2.year = 2015
LEFT OUTER JOIN gko_kostra_data cr3 on cr3.fk_region_code = b.pk_municipality_id AND cr3.fk_indicator_code = 'bv' AND cr3.year = 2015 
LEFT OUTER JOIN gko_kostra_data cr4 on cr4.fk_region_code = b.pk_municipality_id AND cr4.fk_indicator_code = 'bh' AND cr4.year = 2015 
LEFT OUTER JOIN gko_kostra_data cr5 on cr5.fk_region_code = b.pk_municipality_id AND cr5.fk_indicator_code = 'total' AND cr5.year = 2015 
LEFT OUTER JOIN gko_kostra_data rev on rev.fk_region_code = b.pk_municipality_id AND rev.fk_indicator_code = '4901-40' AND rev.year = datepart(year, getdate()) - 1  
LEFT OUTER JOIN vw_gmd_population_filter po1 on po1.municipality_id = b.pk_municipality_id AND po1.summary_level = 'total'  
LEFT OUTER JOIN vw_gmd_population_filter po2 on po2.municipality_id = b.pk_municipality_id AND po2.summary_level = 'total_old' AND po2.population != 0  
LEFT OUTER JOIN vw_gmd_population_filter po3 on po3.municipality_id = b.pk_municipality_id AND po3.summary_level = 'pf1' AND po3.population != 0  
LEFT OUTER JOIN vw_gmd_population_filter po4 on po4.municipality_id = b.pk_municipality_id AND po4.summary_level = 'pf2' AND po4.population != 0  
LEFT OUTER JOIN vw_gmd_population_filter po5 on po5.municipality_id = b.pk_municipality_id AND po5.summary_level = '80-89 år' AND po5.population != 0  
LEFT OUTER JOIN vw_gmd_population_filter po6 on po6.municipality_id = b.pk_municipality_id AND po6.summary_level = '90 år eller eldre' AND po6.population != 0  
LEFT OUTER JOIN ReportNetExpence net on net.fk_region_code =  b.pk_municipality_id 