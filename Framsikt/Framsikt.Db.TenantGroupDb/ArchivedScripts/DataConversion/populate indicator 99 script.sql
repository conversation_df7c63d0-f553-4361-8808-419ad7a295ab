DELETE FROM gko_kostra_data WHERE fk_indicator_code = '99-Saldering'
go
DELETE FROM gko_kostra_data_corp WHERE fk_indicator_code = '99-Saldering'
go



INSERT INTO gko_kostra_data (fk_indicator_code, fk_region_code, year, indicator_value)
SELECT '99-Saldering', b.fk_region_code, year, 100 - sum(indicator_value) as total_value
FROM gko_main_indicators a, gko_kostra_data b
WHERE a.type = 2 
AND a.fk_report_area_code != '99'
AND a.fk_indicator_code = b.fk_indicator_code
GROUP BY b.fk_region_code, year
HAVING sum(indicator_value) != 0
order by fk_region_code, year
go

INSERT INTO gko_kostra_data_corp (fk_indicator_code, fk_region_code, year, indicator_value)
SELECT '99-Saldering', b.fk_region_code, year, 100 - sum(indicator_value) as total_value
FROM gko_main_indicators a, gko_kostra_data_corp b
WHERE a.type = 2 
AND a.fk_indicator_code = b.fk_indicator_code
AND a.fk_report_area_code != '99'
GROUP BY b.fk_region_code, year
HAVING sum(indicator_value) != 0
order by fk_region_code, year
go
