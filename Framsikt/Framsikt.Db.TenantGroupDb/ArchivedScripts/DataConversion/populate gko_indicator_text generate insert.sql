/***************************************************************************
Roar Dale 2015.02.20
--Importing explanations from SSB on kostra-indicators
--Used to generete the inserts in "populate gko_indicator_text.sql" in VSO

NB!! The script must be run on a local database. To import data from Excel is not supported on Azure-database.

More documentation:
How to set up sql to read from Excel - See "Dropbox (Framsikt AS)\Framsikt_Intern_Norge\Dokumentasjon produkt\Oppsett av SQL server for å lese fra regneark.docx"
Uthenting av data fra SSB ****************Her kommer det en oppskrift*****************

Tekstene hentes ut til Excel fra linker som ligger i "Oversikt over linker til inkator forklaringer.xlsx"
Hent en ny excelfil Bruk Data, Hent fra Web, Legg inn linken, Marker tabellmerket øverst til ventre, Importer, dataene kommer pent i kolonner
Lagre filen med faktaark_id som ligger i regnearket. Denne brukes i scriptet nedenfor som filnavn for å lese fra Excel

The texts retrieved for Excel from links located in the "Overview of links to inkator forklaringer.xlsx"
Get a new excel file and use Data, Get from Web, Submit link, Mark the table-marker on top left, Import and the data comes neatly in columns
Save the file with faktaark_id located in the worksheet. This is used in the script below as filename to read from Excel

********************************/

--Create temporary tabel for import from Excel
drop table tmp_faktaark;

create table tmp_faktaark (
faktaark varchar(35) not null,
faktaark_descr varchar(255),
rownum int not null,
type varchar(255),
ssb_description varchar(2000),
fk_indicator_code varchar(25),
indicator_descr varchar(255),
indicator_descr_id varchar(35) );

--Import data from Excel (Needs some setup to do this). Replace the path for the Excelfiles
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '1074771228169132' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\1074771228169132.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '1076396311104976' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\1076396311104976.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS1076396311104976' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS1076396311104976.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1234344509P31514' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1234344509P31514.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KSK1234344509P31514' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KSK1234344509P31514.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '98327788422242' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\98327788422242.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '1083331780476108' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\1083331780476108.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95193998817111' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95193998817111.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95194000717112' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95194000717112.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95194000717112' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95194000717112.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95194189217212' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95194189217212.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95194189217212' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95194189217212.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1362668412P14644' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1362668412P14644.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95254370311646' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95254370311646.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95254370311646' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95254370311646.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95254922811832' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95254922811832.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95254922811832' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95254922811832.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95132706111964' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95132706111964.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95132706111964' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95132706111964.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1108978802P00016160' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1108978802P00016160.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95194002517113' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95194002517113.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95194002517113' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95194002517113.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '110561962432987' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\110561962432987.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '110561957228893' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\110561957228893.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95255192011919' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95255192011919.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95254593411740' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95254593411740.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95254593411740' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95254593411740.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95254433811665' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95254433811665.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS95254433811665' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS95254433811665.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '1011612341290961' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\1011612341290961.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95254948711853' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95254948711853.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1180339579P00012955' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1180339579P00012955.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '100886350676655' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\100886350676655.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1179481761P00014222' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1179481761P00014222.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '1012901161263736' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\1012901161263736.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KS1012901161263736' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KS1012901161263736.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '95194188017210' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\95194188017210.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1179481893P00014232' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1179481893P00014232.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1351583720P2003' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1351583720P2003.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select '1044538188455430' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\1044538188455430.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1136193477P00023356' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1136193477P00023356.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1173448622P00013919' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1173448622P00013919.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'KSK1173448622P00013919' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\KSK1173448622P00013919.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 
insert into tmp_faktaark (faktaark, rownum, type, ssb_description ) select 'K1336732044P15152' as faktaark, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS rownum, F1 as type, F2 as ssb_description FROM OpenDataSource( 'Microsoft.ACE.OLEDB.12.0', 'Data Source="d:\temp\SSBdef\K1336732044P15152.xlsx";Extended properties="Excel 12.0 Xml;HDR=NO;IMEX=1"')...[Ark1$] a where F1 is not null or F2 is not null; 

--Check that texts ar not truncated. length of 255 is suspicious, then the text propably is truncated. The columns in Excel must be formated as text and not General to avoid truncation.
select faktaark, max(len(ssb_description))
 from tmp_faktaark 
group by faktaark
order by faktaark

select * from tmp_faktaark 
where faktaark = '1076396311104976'
order by faktaark, rownum

--Copy reportingarea (raporteringsområde) out on every row
update tmp_faktaark 
set faktaark_descr = b.type
from tmp_faktaark a, (select faktaark, type from tmp_faktaark where rownum = 1 and type != ' ' ) b
where a.faktaark = b.faktaark

update tmp_faktaark 
set faktaark_descr = b.type
from tmp_faktaark a, (select faktaark, type from tmp_faktaark where rownum = 2 and type != ' ' ) b
where a.faktaark = b.faktaark
and a.faktaark_descr is null

--Delete lines with original reportingarea (raporteringsområde)
delete from tmp_faktaark where rownum = 1 and ssb_description is null;
delete from tmp_faktaark where rownum = 2 and ssb_description is null;

--Indicator titles are copied down on all lines that belong to the title
select A.*, b.ssb_description
from tmp_faktaark a, (
select faktaark, rownum, ssb_description from tmp_faktaark 
) b, (
select faktaark, rownum, rownum-2 as startrownum,  
LEAD(rownum, 1, 999999) OVER (PARTITION BY faktaark, type ORDER BY rownum ASC) -3  AS endrownum,
 type from tmp_faktaark where type = 'Informasjon'
 ) c
where a.faktaark = b.faktaark
and a.faktaark = c.faktaark
and a.rownum between c.startrownum and c.endrownum
and b.rownum = c.startrownum
order by a.faktaark, a.rownum


update tmp_faktaark 
set indicator_descr = b.ssb_description
from tmp_faktaark a, (
select faktaark, rownum, ssb_description from tmp_faktaark 
) b, (
select faktaark, rownum, rownum-2 as startrownum,  
LEAD(rownum, 1, 999999) OVER (PARTITION BY faktaark, type ORDER BY rownum ASC) -3  AS endrownum,
 type from tmp_faktaark where type = 'Informasjon'
 ) c
where a.faktaark = b.faktaark
and a.faktaark = c.faktaark
and a.rownum between c.startrownum and c.endrownum
and b.rownum = c.startrownum

--Indicator ID are copied down on all lines that belong to the ID
update tmp_faktaark 
set indicator_descr_id = b.ssb_description
from tmp_faktaark a, (
select faktaark, rownum, ssb_description from tmp_faktaark 
) b, (
select faktaark, rownum, rownum-2 as startrownum,  
LEAD(rownum, 1, 999999) OVER (PARTITION BY faktaark, type ORDER BY rownum ASC) -3  AS endrownum,
 type from tmp_faktaark where type = 'Informasjon'
 ) c
where a.faktaark = b.faktaark
and a.faktaark = c.faktaark
and a.rownum between c.startrownum and c.endrownum
and b.rownum = c.startrownum + 1

--Delete headingrows that are copied down on the rows
select *
from tmp_faktaark a
where exists ( select 1 from (
select faktaark, rownum-2 as rownum from tmp_faktaark where type = 'Informasjon'
union select faktaark, rownum-1 as rownum from tmp_faktaark where type = 'Informasjon' ) b
where a.faktaark = b.faktaark
and a.rownum = b.rownum )

delete from tmp_faktaark 
from tmp_faktaark a 
where exists ( select 1 from (
select faktaark, rownum-2 as rownum from tmp_faktaark where type = 'Informasjon'
union select faktaark, rownum-1 as rownum from tmp_faktaark where type = 'Informasjon' ) b
where a.faktaark = b.faktaark
and a.rownum = b.rownum )

select * from tmp_faktaark 
order by faktaark, rownum

--Copy "informasjon", "Kavlitet" and "Fotnote" on all lines that belong to the record
select a.*, b.faktaark, b.type, b.startrownum, b.endrownum
from tmp_faktaark a, (
select faktaark, type, rownum as startrownum,  
LEAD(rownum, 1, 999999) OVER (PARTITION BY faktaark ORDER BY rownum ASC) -1  AS endrownum 
from (select faktaark, type, rownum from tmp_faktaark where type is not null ) x
--order by rownum
 ) b
where a.rownum  between b.startrownum and b.endrownum
and a.faktaark = b.faktaark
and a.type is null
order by a.faktaark, a.rownum

update tmp_faktaark set type = b.type
from tmp_faktaark a, (
select faktaark, type, rownum as startrownum,  
LEAD(rownum, 1, 999999) OVER (PARTITION BY faktaark ORDER BY rownum ASC) -1  AS endrownum 
from (select faktaark, type, rownum from tmp_faktaark where type is not null ) x
--order by rownum
 ) b
where a.rownum  between b.startrownum and b.endrownum
and a.faktaark = b.faktaark
and a.type is null;

--Finding indicator_code (here its used a local copy of kostra data. May be replaced by gko_kostra_data and/or gko_kostra_data_corp.
select distinct indicator_descr 
from tmp_faktaark a
--1824

select max(len(indicator_description)) from tmp_kostra_data b

select distinct indicator_descr 
from tmp_faktaark a, tmp_kostra_data b
where rtrim(substring(a.indicator_descr,1,79)) = rtrim(substring(b.indicator_description,1,79))


--Uppdate fk_indicator_code

--If update to fk_indicator_code has to be re-run, reset to null:
--update tmp_faktaark set fk_indicator_code = null;

--Set fk_idicator_code where textsare equal. Tekstes on kostradata are max 79 characters (?)
update tmp_faktaark 
set fk_indicator_code = b.fk_indicator_code
from tmp_faktaark a, tmp_kostra_data b
where rtrim(substring(a.indicator_descr,1,79)) = rtrim(substring(b.indicator_description,1,79))

--Update where "prosent" are used instead of %
select distinct indicator_descr 
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and  rtrim(substring(replace(a.indicator_descr, '%', 'prosent'),1,79)) = rtrim(substring(b.indicator_description,1,79))

update tmp_faktaark 
set fk_indicator_code = b.fk_indicator_code
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and  rtrim(substring(replace(a.indicator_descr, '%', 'prosent'),1,79)) = rtrim(substring(b.indicator_description,1,79))

--Update where one of the texts has "dot" on the end
select distinct indicator_descr 
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and  rtrim(substring(a.indicator_descr,1,len(a.indicator_descr)-1 )) = rtrim(substring(b.indicator_description,1,len(b.indicator_description)))
or rtrim(substring(a.indicator_descr,1,len(a.indicator_descr) )) = rtrim(substring(b.indicator_description,1,len(b.indicator_description)-1 ))

update tmp_faktaark 
set fk_indicator_code = b.fk_indicator_code
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and  rtrim(substring(a.indicator_descr,1,len(a.indicator_descr)-1 )) = rtrim(substring(b.indicator_description,1,len(b.indicator_description)))
or rtrim(substring(a.indicator_descr,1,len(a.indicator_descr) )) = rtrim(substring(b.indicator_description,1,len(b.indicator_description)-1 ))


--*********************  NOT TO BE RUN (Check with Øyvind)
/*select distinct indicator_descr 
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and ( rtrim(substring(replace(a.indicator_descr, ', konsern', ''),1,len(a.indicator_descr)-1 )) = rtrim(substring(b.indicator_description,1,len(a.indicator_descr) ))
or rtrim(substring(replace(a.indicator_descr, ', konsern', ''),1,len(a.indicator_descr) )) = rtrim(substring(b.indicator_description,1,len(a.indicator_descr)-1 )) )
--218 

update tmp_faktaark 
set fk_indicator_code = b.fk_indicator_code
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and rtrim(substring(replace(a.indicator_descr, ', konsern', ''),1,79)) = rtrim(substring(b.indicator_description,1,79))
*/

--Selects for missing fk_indicator_code
select distinct a.indicator_descr
from tmp_faktaark a
where a.fk_indicator_code is null
--483

select faktaark, faktaark_descr, rownum, type, ssb_description, indicator_descr
from tmp_faktaark a
where a.fk_indicator_code is null
order by faktaark_descr, rownum

select distinct a.indicator_descr, b.indicator_description, b.fk_indicator_code, len(b.indicator_description)
from tmp_faktaark a, tmp_kostra_data b
where a.fk_indicator_code is null
and rtrim(substring(a.indicator_descr,1,25)) = rtrim(substring(b.indicator_description,1,25))
order by a.indicator_descr


--Select to verify the texts are OK
select * from tmp_faktaark 
order by rownum

select distinct a.fk_indicator_code, a.indicator_descr, type,
replace(STUFF((SELECT char(10) + b.ssb_description as [text()] from tmp_faktaark b where a.fk_indicator_code = b.fk_indicator_code and a.type = b.type
FOR XML PATH('') ), 1, 1, ''), char(10), '<p>') as ssb_description
from tmp_faktaark a 
where fk_indicator_code is not null
--and indicator_descr like 'Netto driftsutgifter til grunnskole (202), per innbygger%'
order by 2

--Create table with html formated text per indicator_code and type. Type is set in bold <b> and </b>. Linebreaks are set with <p>
drop table tmp_faktaark_formatert

select distinct a.fk_indicator_code, a.indicator_descr, a.type,
   CASE a.type
      WHEN 'Informasjon' THEN 1
      WHEN 'Kvalitet' THEN 2
      WHEN 'Fotnote' THEN 3
      ELSE NULL
   END as sort_order,
'<b>' + a.type + ':</b> ' +
replace(STUFF((SELECT char(10) + b.ssb_description as [text()] from tmp_faktaark b where a.fk_indicator_code = b.fk_indicator_code and a.type = b.type
FOR XML PATH('') ), 1, 1, ''), char(10), '<p>') as ssb_description
into tmp_faktaark_formatert
from tmp_faktaark a 
where fk_indicator_code is not null
ORDER BY a.fk_indicator_code, sort_order 

--Update the formated tekst with linebreak at the start of lines with "Kvalitet" and "Fotnote" (sort_order 2 and 3)
update tmp_faktaark_formatert set ssb_description = '<p>' + ssb_description 
where sort_order in ( 2, 3)

--Select the records that go into gko_indicator_text (fk_indicator_code, ssb_description)
select a.fk_indicator_code, 
a.ssb_description + isnull(b.ssb_description, '') + isnull(c.ssb_description, '')  
from tmp_faktaark_formatert a 
left outer join tmp_faktaark_formatert b on a.fk_indicator_code = b.fk_indicator_code and b.sort_order = 2
left outer join  tmp_faktaark_formatert c on a.fk_indicator_code = c.fk_indicator_code and c.sort_order = 3
where a.sort_order = 1

--Create insertscript "populate gko_indicator_text.sql" in VSO
select distinct 'insert into gko_indicator_text (fk_indicator_code, text) values (''' + a.fk_indicator_code + ''',''' + 
a.ssb_description + isnull(b.ssb_description, '') + isnull(c.ssb_description, '')  +  ''' )
GO '
from tmp_faktaark_formatert a 
left outer join tmp_faktaark_formatert b on a.fk_indicator_code = b.fk_indicator_code and b.sort_order = 2
left outer join  tmp_faktaark_formatert c on a.fk_indicator_code = c.fk_indicator_code and c.sort_order = 3
where a.sort_order = 1
