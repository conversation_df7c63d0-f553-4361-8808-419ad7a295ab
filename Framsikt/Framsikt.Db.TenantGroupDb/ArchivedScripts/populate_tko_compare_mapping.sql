
SET IDENTITY_INSERT [dbo].[tko_compare_mapping] ON 

GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (1, 1, N'EAKUO', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (2, 1, N'0412', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (3, 1, N'0427', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (4, 1, N'0233', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (5, 2, N'0219', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (6, 2, N'0213', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (7, 2, N'0220', CAST(0x0000A3C000000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (8, 1, N'EKG13', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (10, 2, N'EKG13', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (12, 2, N'EAK', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (13, 3, N'0805', CAST(0x0000A3E500000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (14, 3, N'0706', CAST(0x0000A3E500000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (15, 3, N'0704', CAST(0x0000A3E500000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (16, 3, N'EKA07', CAST(0x0000A3E500000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (17, 3, N'EKG13', CAST(0x0000A3E500000000 AS DateTime), 1)
GO
INSERT [dbo].[tko_compare_mapping] ([pk_id], [fk_tenant_id], [fk_region_code], [updated], [updated_by]) VALUES (18, 3, N'EAK', CAST(0x0000A3E500000000 AS DateTime), 1)
GO
SET IDENTITY_INSERT [dbo].[tko_compare_mapping] OFF
GO
