SET IDENTITY_INSERT [dbo].[tmd_pop_ind_mapping] ON 

INSERT [dbo].[tmd_pop_ind_mapping] ([pk_id], [fk_pop_indicator_id], [fk_org_id], [fk_tenant_id], [updated], [updated_by]) VALUES (1, N'pf1', N'21', 1, CAST(N'2014-11-11 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[tmd_pop_ind_mapping] ([pk_id], [fk_pop_indicator_id], [fk_org_id], [fk_tenant_id], [updated], [updated_by]) VALUES (2, N'pf2', N'12', 2, CAST(N'2014-11-11 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[tmd_pop_ind_mapping] ([pk_id], [fk_pop_indicator_id], [fk_org_id], [fk_tenant_id], [updated], [updated_by]) VALUES (3, N'pf5', N'51', 1, CAST(N'2014-11-11 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[tmd_pop_ind_mapping] ([pk_id], [fk_pop_indicator_id], [fk_org_id], [fk_tenant_id], [updated], [updated_by]) VALUES (4, N'pf1', N'01', 2, CAST(N'2014-11-11 00:00:00.000' AS DateTime), 1)
INSERT [dbo].[tmd_pop_ind_mapping] ([pk_id], [fk_pop_indicator_id], [fk_org_id], [fk_tenant_id], [updated], [updated_by]) VALUES (5, N'pf5', N'05', 2, CAST(N'2014-11-11 00:00:00.000' AS DateTime), 1)
SET IDENTITY_INSERT [dbo].[tmd_pop_ind_mapping] OFF