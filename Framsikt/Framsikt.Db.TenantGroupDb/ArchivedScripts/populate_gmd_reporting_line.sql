DELETE FROM [gmd_reporting_line]
GO

SET IDENTITY_INSERT [dbo].[gmd_reporting_line] ON 

GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (1, N'1A', N'1870', N'Frie disponible inntekter', 1, 10, N'Skatt', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (2, N'1A', N'1800', N'Frie disponible inntekter', 1, 11, N'Rammestilskudd', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (3, N'1A', N'1874', N'Frie disponible inntekter', 1, 12, N'Skatt på eiendom', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (4, N'1A', N'1877', N'Frie disponible inntekter', 1, 13, N'Andre direkte eller indirete skatter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (5, N'1A', N'1600', N'Frie disponible inntekter', 1, 14, N'Brukerbetaling', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (6, N'1A', N'1620', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (7, N'1A', N'1629', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (8, N'1A', N'1630', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (9, N'1A', N'1640', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (10, N'1A', N'1650', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (11, N'1A', N'1660', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (12, N'1A', N'1810', N'Frie disponible inntekter', 1, 16, N'Andre generelle statstilskudd', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (13, N'1A', N'1900', N'Finansinntekter og utgifter', 2, 20, N'Renteinntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (14, N'1A', N'1905', N'Finansinntekter og utgifter', 2, 21, N'Utbytte og eieruttak', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (15, N'1A', N'1500', N'Finansinntekter og utgifter', 2, 22, N'Renteutgifter, provisjoner og andre finansutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (16, N'1A', N'1510', N'Finansinntekter og utgifter', 2, 24, N'Avdrag på lån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (17, N'1A', N'1530', N'Avsetninger', 3, 30, N'Dektning av tidligere merforbruk', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (18, N'1A', N'1540', N'Avsetninger', 3, 31, N'Ubundne avsetnigner', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (19, N'1A', N'1548', N'Avsetninger', 3, 31, N'Ubundne avsetninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (21, N'1A', N'1550', N'Avsetninger', 3, 32, N'Bundne avsetninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (22, N'1A', N'1930', N'Avsetninger', 3, 33, N'Bruk av tidligere års mindreforbruk', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (23, N'1A', N'1940', N'Avsetninger', 3, 34, N'Bruk av ubundne avsetninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (25, N'1A', N'1950', N'Avsetninger', 3, 35, N'Bruk av bundne avsetninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (26, N'drift', N'1600', N'Driftsinntekter', 5, 50, N'Brukerbetaling', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (27, N'drift', N'1620', N'Driftsinntekter', 5, 51, N'Andre salgs- og leieinntekter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (28, N'drift', N'1629', N'Driftsinntekter', 5, 51, N'Andre salgs- og leieinntekter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (29, N'drift', N'1630', N'Driftsinntekter', 5, 51, N'Andre salgs- og leieinntekter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (30, N'drift', N'1640', N'Driftsinntekter', 5, 51, N'Andre salgs- og leieinntekter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (31, N'drift', N'1650', N'Driftsinntekter', 5, 51, N'Andre salgs- og leieinntekter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (32, N'drift', N'1660', N'Driftsinntekter', 5, 51, N'Andre salgs- og leieinntekter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (33, N'drift', N'1700', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (34, N'drift', N'1710', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (36, N'drift', N'1729', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (37, N'drift', N'1730', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (38, N'drift', N'1750', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (39, N'drift', N'1770', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (40, N'drift', N'1775', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (41, N'drift', N'1780', N'Driftsinntekter', 5, 52, N'Overføringer med krav til motytelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (42, N'drift', N'1800', N'Driftsinntekter', 5, 53, N'Rammetilskudd', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (43, N'drift', N'1810', N'Driftsinntekter', 5, 54, N'Andre statlige overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (44, N'drift', N'1830', N'Driftsinntekter', 5, 55, N'Andre overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (45, N'1A', N'1850', N'Frie disponible inntekter', 1, 15, N'Andre inntekter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (46, N'drift', N'1880', N'Driftsinntekter', 5, 55, N'Andre overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (47, N'drift', N'1890', N'Driftsinntekter', 5, 55, N'Andre overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (48, N'drift', N'1895', N'Driftsinntekter', 5, 55, N'Andre overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (49, N'drift', N'1870', N'Driftsinntekter', 5, 56, N'Inntekts- og formuesskatt ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (50, N'drift', N'1874', N'Driftsinntekter', 5, 57, N'Eiendomsskatt', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (51, N'drift', N'1875', N'Driftsinntekter', 5, 57, N'Eiendomsskatt', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (52, N'drift', N'1877', N'Driftsinntekter', 5, 58, N'Andre direkte og indirekte skatter ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (53, N'drift', N'1010', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (54, N'drift', N'1020', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (55, N'drift', N'1030', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (56, N'drift', N'1040', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (57, N'drift', N'1050', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (58, N'drift', N'1070', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (59, N'drift', N'1075', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (60, N'drift', N'1080', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (61, N'drift', N'1089', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (62, N'drift', N'1160', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (63, N'drift', N'1165', N'Driftsutgifter', 6, 60, N'Lønnsutgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (64, N'drift', N'1090', N'Driftsutgifter', 6, 61, N'Sosiale utgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (65, N'drift', N'1099', N'Driftsutgifter', 6, 62, N'Sosiale utgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (66, N'drift', N'1100', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (67, N'drift', N'1105', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (68, N'drift', N'1110', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (69, N'drift', N'1114', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (70, N'drift', N'1115', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (71, N'drift', N'1120', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (72, N'drift', N'1130', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (73, N'drift', N'1140', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (74, N'drift', N'1150', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (75, N'drift', N'1170', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (76, N'drift', N'1180', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (77, N'drift', N'1181', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (78, N'drift', N'1182', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (79, N'drift', N'1183', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (80, N'drift', N'1184', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (81, N'drift', N'1185', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (82, N'drift', N'1190', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (83, N'drift', N'1195', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (84, N'drift', N'1200', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (85, N'drift', N'1209', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (86, N'drift', N'1210', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (87, N'drift', N'1220', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (88, N'drift', N'1230', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (89, N'drift', N'1240', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (90, N'drift', N'1250', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (91, N'drift', N'1260', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (92, N'drift', N'1270', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (93, N'drift', N'1300', N'Driftsutgifter', 6, 64, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (94, N'drift', N'1330', N'Driftsutgifter', 6, 64, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (95, N'drift', N'1350', N'Driftsutgifter', 6, 64, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (96, N'drift', N'1370', N'Driftsutgifter', 6, 64, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (97, N'drift', N'1375', N'Driftsutgifter', 6, 64, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (98, N'drift', N'1380', N'Driftsutgifter', 6, 64, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (99, N'drift', N'1400', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (100, N'drift', N'1429', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (101, N'drift', N'1430', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (102, N'drift', N'1450', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (103, N'drift', N'1470', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (104, N'drift', N'1475', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (105, N'drift', N'1480', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (106, N'drift', N'1490', N'Driftsutgifter', 6, 65, N'Overføringer', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (107, N'drift', N'1590', N'Driftsutgifter', 6, 66, N'Avskrivninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (108, N'drift', N'1290', N'Driftsutgifter', 6, 67, N'Fordelte utgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (109, N'drift', N'1690', N'Driftsutgifter', 6, 67, N'Fordelte utgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (110, N'drift', N'1790', N'Driftsutgifter', 6, 67, N'Fordelte utgifter', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (111, N'drift', N'1900', N'Eksterne finansinntekter', 8, 80, N'Renteinntekter og utbytte', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (112, N'drift', N'1901', N'Eksterne finansinntekter', 8, 80, N'Renteinntekter og utbytte', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (113, N'drift', N'1905', N'Eksterne finansinntekter', 8, 80, N'Renteinntekter og utbytte', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (114, N'drift', N'1909', N'Eksterne finansinntekter', 8, 81, N'Gevinst finansielle instrumenter (omløpsmidler) ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (115, N'drift', N'1920', N'Driftsutgifter', 6, 82, N'Mottatte avdrag på utlån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (116, N'drift', N'1921', N'Eksterne finansinntekter', 8, 82, N'Mottatte avdrag på utlån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (117, N'drift', N'1500', N'Eksterne finansutgifter', 9, 90, N'Renteutgifter og låneomkostninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (118, N'drift', N'1501', N'Eksterne finansutgifter', 9, 90, N'Renteutgifter og låneomkostninger', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (119, N'drift', N'1509', N'Eksterne finansutgifter', 9, 91, N'Tap finansielle instrumenter (omløpsmidler) ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (120, N'drift', N'1510', N'Eksterne finansutgifter', 9, 92, N'Avdrag på lån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (121, N'drift', N'1511', N'Eksterne finansutgifter', 9, 92, N'Avdrag på lån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (122, N'drift', N'1520', N'Driftsutgifter', 6, 93, N'Utlån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (123, N'drift', N'1521', N'Eksterne finansutgifter', 9, 93, N'Utlån', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (124, N'drift', N'1990', N'Driftsutgifter', 6, 100, N'Motpost avskrivninger ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (125, N'balanse', N'231', N'Kortsiktig gjeld', 11, 110, N'Kassekredittgjeld', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (127, N'balanse', N'232', N'Kortsiktig gjeld', 11, 111, N'Annen kortsiktig gjeld ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (129, N'balanse', N'233', N'Kortsiktig gjeld', 11, 112, N'Konsernintern kortsiktig gjeld ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (131, N'balanse', N'234', N'Kortsiktig gjeld', 11, 113, N'Derivater ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (133, N'balanse', N'239', N'Kortsiktig gjeld', 11, 114, N'Premieavvik ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (135, N'balanse', N'240', N'Langsiktig gjeld', 12, 120, N'Pensjonsforpliktelse ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (137, N'balanse', N'241', N'Langsiktig gjeld', 12, 121, N'Ihendehaverobligasjoner ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (139, N'balanse', N'243', N'Langsiktig gjeld', 12, 122, N'Sertifikatlån ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (141, N'balanse', N'245', N'Langsiktig gjeld', 12, 123, N'Andre lån / Annen langsiktig gjeld ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (144, N'balanse', N'247', N'Langsiktig gjeld', 12, 125, N'Konsernintern langsiktig gjeld ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (146, N'balanse', N'251', N'Egenkapital', 13, 130, N'Bundne driftsfond ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (148, N'balanse', N'253', N'Egenkapital', 13, 131, N'Ubundne investeringsfond ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (150, N'balanse', N'255', N'Egenkapital', 13, 132, N'Bundne investeringsfond ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (152, N'balanse', N'256', N'Egenkapital', 13, 133, N'Disposisjonsfond ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (154, N'balanse', N'2580', N'Egenkapital', 13, 134, N'Endring i regnskapsprinsipp som påvirker AK (investering) ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (156, N'balanse', N'2581', N'Egenkapital', 13, 135, N'Endring i regnskapsprinsipp som påvirker AK (drift) ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (158, N'balanse', N'25900', N'Egenkapital', 13, 136, N'Regnskapsmessig merforbruk ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (160, N'balanse', N'25950', N'Egenkapital', 13, 137, N'Regnskapsmessig mindreforbruk ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (162, N'balanse', N'25960', N'Egenkapital', 13, 138, N'Udisponert i investeringsregnskapet ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (164, N'balanse', N'25970', N'Egenkapital', 13, 139, N'Udekket i investeringsregnskapet ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (166, N'balanse', N'25990', N'Egenkapital', 13, 140, N'Kapitalkonto ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (168, N'balanse', N'29100', N'Memoriakonti', 14, 145, N'Memoriakonto ubrukte lånemidler ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (170, N'balanse', N'29110', N'Memoriakonti', 14, 146, N'Memoriakonto ubrukte konserninterne lånemidler ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (172, N'balanse', N'29200', N'Memoriakonti', 14, 147, N'Andre memoriakonti ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (174, N'balanse', N'29999', N'Memoriakonti', 14, 148, N'Motkonto for memoriakonti ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (176, N'1A', N'1570', N'Overføring til investeringsregnskapet ', 4, 40, N'Overføring til investeringsregnskapet ', CAST(0x0000A3D800000000 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (177, N'3inv', N'0010', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201027804 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (178, N'3inv', N'0020', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A4420104FB2C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (179, N'3inv', N'0030', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201050FCF AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (180, N'3inv', N'0040', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A442010523BE AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (181, N'3inv', N'0050', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201053341 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (182, N'3inv', N'0070', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201053E7B AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (183, N'3inv', N'0075', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201054C1D AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (184, N'3inv', N'0080', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201055733 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (185, N'3inv', N'0089', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A442010584B0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (186, N'3inv', N'0090', N'Driftsutgifter', 22, 222, N'Sosiale utgifter', CAST(0x0000A4420105E88F AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (187, N'3inv', N'0099', N'Driftsutgifter', 22, 222, N'Sosiale utgifter', CAST(0x0000A4420105FCC1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (188, N'3inv', N'0160', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201068E49 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (189, N'3inv', N'0165', N'Driftsutgifter', 22, 220, N'Lønnsutgifter', CAST(0x0000A44201069FEF AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (190, N'3inv', N'0100', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201075AD8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (191, N'3inv', N'0105', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201076BC5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (192, N'3inv', N'0110', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201077701 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (193, N'3inv', N'0114', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420107A990 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (194, N'3inv', N'0115', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420107B50F AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (195, N'3inv', N'0120', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420107E77C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (196, N'3inv', N'0130', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420107EFD0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (197, N'3inv', N'0140', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420107FE7C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (198, N'3inv', N'0150', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201080992 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (199, N'3inv', N'0170', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201083759 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (200, N'3inv', N'0180', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201083DA7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (201, N'3inv', N'0181', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201084383 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (202, N'3inv', N'0182', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201084C8B AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (203, N'3inv', N'0183', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010854D7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (204, N'3inv', N'0184', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201086A38 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (205, N'3inv', N'0185', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201087A6A AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (206, N'3inv', N'0190', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A44201088C52 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (207, N'3inv', N'0195', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010892D2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (208, N'3inv', N'0200', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109A9A3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (209, N'3inv', N'0209', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109BBF8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (210, N'3inv', N'0210', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109E041 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (211, N'3inv', N'0220', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109E70B AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (212, N'3inv', N'0230', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109ECB8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (213, N'3inv', N'0240', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109F4B8 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (214, N'3inv', N'0250', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4420109FAE3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (215, N'3inv', N'0260', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010A00D2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (216, N'3inv', N'0270', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010A07FC AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (217, N'3inv', N'0280', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010A1049 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (218, N'3inv', N'0290', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010A1C36 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (219, N'3inv', N'0285', N'Driftsutgifter', 22, 224, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A442010A31D5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (220, N'3inv', N'0300', N'Driftsutgifter', 22, 226, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon', CAST(0x0000A442010CB34D AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (221, N'3inv', N'0330', N'Driftsutgifter', 22, 226, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon', CAST(0x0000A442010CBEC1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (222, N'3inv', N'0350', N'Driftsutgifter', 22, 226, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon', CAST(0x0000A442010CC6DE AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (223, N'3inv', N'0370', N'Driftsutgifter', 22, 226, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon', CAST(0x0000A442010CD3C6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (224, N'3inv', N'0375', N'Driftsutgifter', 22, 226, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon', CAST(0x0000A442010CDABB AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (225, N'3inv', N'0380', N'Driftsutgifter', 22, 226, N'Kjøp av tjenester som erstatter kommunal tjenesteproduksjon', CAST(0x0000A442010CE278 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (226, N'3inv', N'0400', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D5252 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (227, N'3inv', N'0429', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D5C02 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (228, N'3inv', N'0430', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D6E47 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (229, N'3inv', N'0450', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D7435 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (230, N'3inv', N'0470', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D79FD AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (231, N'3inv', N'0475', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D7FF3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (232, N'3inv', N'0480', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D8FA7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (233, N'3inv', N'0490', N'Driftsutgifter', 22, 228, N'Overføringer', CAST(0x0000A442010D9FF7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (234, N'3inv', N'0500', N'Driftsutgifter', 22, 230, N'Renteutgifter og omkostninger', CAST(0x0000A442010E2454 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (235, N'3inv', N'0501', N'Driftsutgifter', 22, 230, N'Renteutgifter og omkostninger', CAST(0x0000A442010E2EAB AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (236, N'3inv', N'0690', N'Driftsutgifter', 22, 232, N'Fordelte utgifter', CAST(0x0000A442010FD949 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (237, N'3inv', N'0290', N'Driftsutgifter', 22, 232, N'Fordelte utgifter', CAST(0x0000A442010FFE45 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (238, N'3inv', N'0790', N'Driftsutgifter', 22, 232, N'Fordelte utgifter', CAST(0x0000A442011015D7 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (239, N'3inv', N'0510', N'Driftsutgifter', 24, 240, N'Avdrag på lån', CAST(0x0000A44201211716 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (240, N'3inv', N'0511', N'Driftsutgifter', 24, 240, N'Avdrag på lån', CAST(0x0000A442012135DE AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (241, N'3inv', N'0520', N'Driftsutgifter', 24, 242, N'Utlån', CAST(0x0000A44201217D81 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (242, N'3inv', N'0521', N'Driftsutgifter', 24, 242, N'Utlån', CAST(0x0000A44201218A4E AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (243, N'3inv', N'0529', N'Driftsutgifter', 24, 244, N'Aksjer og andeler', CAST(0x0000A4420121921C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (244, N'3inv', N'0530', N'Driftsutgifter', 24, 246, N'Dekning av tidligere års underskudd', CAST(0x0000A4420123CB60 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (245, N'3inv', N'0548', N'Driftsutgifter', 24, 248, N'Avsatt til ubundne investeringsfond', CAST(0x0000A442012483B2 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (246, N'3inv', N'0550', N'Driftsutgifter', 24, 250, N'Avsatt til bundne investeringsfond', CAST(0x0000A4420124C1EC AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (247, N'3inv', N'0910', N'Driftsutgifter', 26, 260, N'Bruk av lån', CAST(0x0000A442012606EB AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (248, N'3inv', N'0911', N'Driftsutgifter', 26, 260, N'Bruk av lån', CAST(0x0000A44201260CDF AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (249, N'3inv', N'0929', N'Driftsutgifter', 26, 262, N'Salg av aksjer og andeler', CAST(0x0000A44201267287 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (250, N'3inv', N'0920', N'Driftsutgifter', 26, 264, N'Mottatte avdrag på utlån', CAST(0x0000A4420126B02C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (251, N'3inv', N'0921', N'Driftsutgifter', 26, 264, N'Mottatte avdrag på utlån', CAST(0x0000A4420126B658 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (252, N'3inv', N'0970', N'Driftsutgifter', 26, 266, N'Overført fra driftsregnskapet', CAST(0x0000A4420126FA89 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (253, N'3inv', N'0930', N'Driftsutgifter', 26, 268, N'Bruk av tidligere års udisponert', CAST(0x0000A442012744B1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (254, N'3inv', N'0940', N'Driftsutgifter', 26, 270, N'Bruk av disposisjonsfond', CAST(0x0000A442012784F0 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (255, N'3inv', N'0950', N'Driftsutgifter', 26, 272, N'Bruk av bundne driftsfond', CAST(0x0000A4420127BC82 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (256, N'3inv', N'0948', N'Driftsutgifter', 26, 274, N'Bruk av ubundne investeringsfond', CAST(0x0000A4420127EE7C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (257, N'3inv', N'0958', N'Driftsutgifter', 26, 276, N'Bruk av bundne investeringsfond', CAST(0x0000A442012805D1 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (258, N'3inv', N'0660', N'Driftsutgifter', 20, 200, N'Salg av driftsmidler og fast eiendom', CAST(0x0000A44201296C04 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (259, N'3inv', N'0670', N'Driftsutgifter', 20, 200, N'Salg av driftsmidler og fast eiendom', CAST(0x0000A44201297339 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (260, N'3inv', N'0600', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129C434 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (261, N'3inv', N'0620', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129D1BA AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (262, N'3inv', N'0629', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129DE32 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (263, N'3inv', N'0629', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129DF54 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (264, N'3inv', N'0630', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129F140 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (265, N'3inv', N'0640', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129F72A AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (266, N'3inv', N'0650', N'Driftsutgifter', 20, 202, N'Andre salgsinntekter', CAST(0x0000A4420129FCF3 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (267, N'3inv', N'0700', N'Driftsutgifter', 20, 204, N'Overføringer med krav til motytelse', CAST(0x0000A442012A5EAD AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (268, N'3inv', N'0710', N'Driftsutgifter', 20, 204, N'Overføringer med krav til motytelse', CAST(0x0000A442012A6703 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (269, N'3inv', N'0730', N'Driftsutgifter', 20, 204, N'Overføringer med krav til motytelse', CAST(0x0000A442012A8769 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (270, N'3inv', N'0750', N'Driftsutgifter', 20, 204, N'Overføringer med krav til motytelse', CAST(0x0000A442012A91FA AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (271, N'3inv', N'0770', N'Driftsutgifter', 20, 204, N'Overføringer med krav til motytelse', CAST(0x0000A442012A9963 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (272, N'3inv', N'0775', N'Driftsutgifter', 20, 204, N'Overføringer med krav til motytelse', CAST(0x0000A442012AA1F5 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (273, N'3inv', N'0729', N'Driftsutgifter', 20, 206, N'Kompensasjon for merverdiavgift', CAST(0x0000A442012AF059 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (274, N'3inv', N'0810', N'Driftsutgifter', 20, 208, N'Statlige overføringer', CAST(0x0000A442012B50EA AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (275, N'3inv', N'0830', N'Driftsutgifter', 20, 210, N'Andre overføringer', CAST(0x0000A442012B8F9C AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (276, N'3inv', N'0850', N'Driftsutgifter', 20, 210, N'Andre overføringer', CAST(0x0000A442012B995E AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (278, N'3inv', N'0880', N'Driftsutgifter', 20, 210, N'Andre overføringer', CAST(0x0000A442012BD28B AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (279, N'3inv', N'0890', N'Driftsutgifter', 20, 210, N'Andre overføringer', CAST(0x0000A442012BDE82 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (280, N'3inv', N'0895', N'Driftsutgifter', 20, 210, N'Andre overføringer', CAST(0x0000A442012BEA2B AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (281, N'3inv', N'0900', N'Driftsutgifter', 20, 212, N'Renteinntekter og utbytte', CAST(0x0000A442012CAFDD AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (282, N'3inv', N'0901', N'Driftsutgifter', 20, 212, N'Renteinntekter og utbytte', CAST(0x0000A442012CB9A9 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (283, N'3inv', N'0905', N'Driftsutgifter', 20, 212, N'Renteinntekter og utbytte', CAST(0x0000A442012CC8D6 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (284, N'drift', N'1930', N'Bruk av avsetninger', 11, 110, N'Bruk av tidligere års regnskapsmessige mindreforbruk', CAST(0x0000A46000994B6F AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (285, N'drift', N'1940', N'Bruk av avsetninger', 11, 111, N'Bruk av disposisjonsfond', CAST(0x0000A460009994FA AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (286, N'drift', N'1950', N'Bruk av avsetninger', 11, 112, N'Bruk av bundne driftsfond', CAST(0x0000A4600099ECDC AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (287, N'drift', N'1570', N'Avsetninger', 13, 130, N'Overført til investeringsregnskapet', CAST(0x0000A460009A5C49 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (288, N'drift', N'1530', N'Avsetninger', 12, 121, N'Avsatt til dekning av tidligere års regnskapsmessige merforbruk', CAST(0x0000A460009AA04B AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (289, N'drift', N'1540', N'Avsetninger', 12, 122, N'Avsatt til disposisjonsfond', CAST(0x0000A460009ADCBE AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (290, N'drift', N'1550', N'Avsetninger', 12, 123, N'Avsatt til bundne fond', CAST(0x0000A460009B08AF AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (291, N'financing', N'0660', N'Salg av driftsmidler og eiendom', 50, 200, N'Salg av driftsmidler og fast eiendom', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (292, N'financing', N'0670', N'Salg av driftsmidler og eiendom', 50, 200, N'Salg av driftsmidler og fast eiendom', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (293, N'financing', N'0700', N'Øremerkede investeringstilskudd', 40, 202, N'Øremerkede investeringstilskudd', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (294, N'financing', N'0730', N'Øremerkede investeringstilskudd', 40, 202, N'Øremerkede investeringstilskudd', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (295, N'financing', N'0750', N'Øremerkede investeringstilskudd', 40, 202, N'Øremerkede investeringstilskudd', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (296, N'financing', N'0770', N'Øremerkede investeringstilskudd', 40, 202, N'Øremerkede investeringstilskudd', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (315, N'financing', N'0910', N'Øremerkede investeringslån', 30, 260, N'Øremerkede investeringslån', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (316, N'financing', N'0911', N'Øremerkede investeringslån', 30, 260, N'Øremerkede investeringslån', CAST(0x0000A4C200C1632E AS DateTime), 1016)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (326, N'drift', N'1280', N'Driftsutgifter', 6, 63, N'Kjøp av varer og tjenester som inngår i kommunens tjenesteproduksjon', CAST(0x0000A4CA00969717 AS DateTime), 1)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (1289, N'drift', N'1850', N'Driftsinntekter', 5, 55, N'Andre overføringer', CAST(0x0000A50800D97E8F AS DateTime), 1002)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (1290, N'1A', N'1700', N'Frie disponible inntekter', 1, 17, N'Overføringer med krav til motytelse', CAST(0x0000A50B016F8F72 AS DateTime), 2017)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (1291, N'1A', N'1909', N'Finansinntekter og utgifter', 2, 23, N'Gevinst finansielle instrumenter', CAST(0x0000A50C01619052 AS DateTime), 2017)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (1292, N'1A', N'1090', N'Sentrale utgifter', 100, 101, N'Pensjon', CAST(0x0000A51500CFFE12 AS DateTime), 2002)
GO
INSERT [dbo].[gmd_reporting_line] ([pk_id], [report], [fk_kostra_account_code], [line_group], [line_group_id], [line_item_id], [line_item], [updated], [updated_by]) VALUES (1293, N'1A', N'1490', N'Sentrale utgifter', 100, 102, N'Lønnsreserve', CAST(0x0000A51500D021C0 AS DateTime), 2002)
GO
SET IDENTITY_INSERT [dbo].[gmd_reporting_line] OFF
GO
