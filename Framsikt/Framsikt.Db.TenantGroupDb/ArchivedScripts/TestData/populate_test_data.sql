:r .\scripts\populate_gco_kostra_accounts.sql
:r .\scripts\populate_gco_municipalities.sql
:r .\scripts\populate_gco_pop_forecast_01.sql
:r .\scripts\populate_gco_pop_forecast_02.sql
:r .\scripts\populate_gco_pop_forecast_03.sql
:r .\scripts\populate_gco_pop_forecast_04.sql
:r .\scripts\populate_gco_pop_forecast_05.sql
:r .\scripts\populate_gco_pop_forecast_06.sql
:r .\scripts\populate_gco_pop_forecast_07.sql
:r .\scripts\populate_gco_pop_forecast_08.sql
:r .\scripts\populate_gco_pop_forecast_09.sql
:r .\scripts\populate_gco_pop_forecast_10.sql
:r .\scripts\populate_gco_tenants.sql
:r .\scripts\populate_tco_accounts.sql
:r .\scripts\populate_tco_functions.sql
:r .\scripts\populate_tco_org_hierarchy.sql
:r .\scripts\populate_tco_org_level.sql
:r .\scripts\populate_tco_org_structure.sql
:r .\scripts\populate_tfp_trans_detail.sql
:r .\scripts\populate_tfp_trans_header.sql
:r .\scripts\populate_tmd_demographic_mapping.sql
:r .\scripts\populate_tmd_org_kostrafunc_mapping.sql