
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (1,'21','2020',20,'MMMM',100,100,100,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (1,'21','2150',20,'MMMM',100,100,0,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (1,'21','2222',20,'MMMM',100,100,0,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (1,'21','2230',20,'MMMM',100,100,0,getdate(),1016,1)
go


INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (1,'51','2521',50,'MMMM',100,100,0,getdate(),1016,1)
go

INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (1,'51','2611',50,'MMMM',100,100,0,getdate(),1016,1)
go



INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (2,'12','2021',20,'MMMM',100,100,100,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (2,'12','2151',20,'MMMM',100,100,0,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (2,'12','2221',20,'MMMM',100,100,0,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (2,'12','2231',20,'MMMM',100,100,0,getdate(),1016,1)
go


INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (2,'01','2011',10,'MMMM',100,100,0,getdate(),1016,1)
go
INSERT INTO [tmd_demographic_mapping] ([fk_tenant_id],[fk_org_id],[fk_function_code],[fk_interval_id],fk_forecast_type,[coverage_pct],
[compensation_pct],[unit_cost],[updated],[updated_by], active)
VALUES (2,'01','2111',10,'MMMM',100,100,0,getdate(),1016,1)
go
