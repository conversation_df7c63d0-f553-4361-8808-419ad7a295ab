
SET IDENTITY_INSERT [dbo].[tco_org_values] ON 

GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (1, N'01', 2, N'Barnehage', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (2, N'10', 2, N'Politisk styring', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (3, N'11', 1, N'RÅDMANNEN MED STAB', N'F7', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (4, N'11', 2, N'Samferdsel og fellesarealer', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (5, N'1100', 1, N'Rådmannen', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (6, N'1101', 1, N'Fellesutgifter', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (7, N'1102', 1, N'Omstilling/ utvikling', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (8, N'1103', 1, N'Næring', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (9, N'1110', 1, N'Personaltjenesten', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (10, N'1111', 1, N'Økonomitjenesten', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (11, N'1112', 1, N'IKT- og kontorstøttetjenesten', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (12, N'1113', 1, N'Plan- og utredningstjenesten', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (13, N'1114', 1, N'IKT Inn-Trøndelag', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (14, N'1190', 1, N'IIUA', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (15, N'12', 2, N'Grunnskoleopplæring', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (16, N'13', 2, N'Sosiale tjenester', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (17, N'130', 2, N'Administrasjon Kommunale BHG', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (18, N'13000', 2, N'Barnehager administrasjon', N'130', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (19, N'131', 2, N'Konnerud/Fjell - ikke i bruk fra 1/8-14', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (20, N'13100', 2, N'Fjell og Konnerud barnehageområde', N'131', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (21, N'13101', 2, N'Fjell bh', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (22, N'13102', 2, N'Fjellhagen bh', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (23, N'13105', 2, N'Austadgata førskole', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (24, N'13202', 2, N'Jordbrekkskogen bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (25, N'13203', 2, N'Konnerud bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (26, N'13204', 2, N'Solhaugen bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (27, N'13205', 2, N'Svendsedammen bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (28, N'133', 2, N'Strømsø/Konnerud Kommunale BHG', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (29, N'13300', 2, N'Strømsø barnehageområde', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (30, N'13301', 2, N'Danvik bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (31, N'13303', 2, N'Gulskogen bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (32, N'13304', 2, N'Marienlyst bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (33, N'13305', 2, N'Nordbylunden bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (34, N'13306', 2, N'St.Hansberget bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (35, N'13307', 2, N'Strømsø bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (36, N'13308', 2, N'Bikkjestykket bhg', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (37, N'134', 2, N'Bragernes/Fjell Kommunale BHG', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (38, N'13400', 2, N'Bragernes barnehageområde', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (39, N'13401', 2, N'Aronsløkka bh', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (40, N'13402', 2, N'Bacheparken bh', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (41, N'13403', 2, N'Brannposten bh', N'133', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (42, N'13404', 2, N'Dalegårdsveien  bh', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (43, N'13405', 2, N'Parktunet bh', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (44, N'13406', 2, N'Åssiden barnehage', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (45, N'13407', 2, N'Dampsentralen BH', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (46, N'13408', 2, N'Lilleløkka BH', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (47, N'13409', 2, N'Sørbyløkka BH', N'134', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (48, N'14', 2, N'Vann og avløp', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (49, N'140', 2, N'Norges beste barnehage', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (50, N'14000', 2, N'Norges beste barnehage', N'140', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (51, N'15', 2, N'Fellesutgifter/ufordelt', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (52, N'150', 2, N'Spes.ped Barnehage', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (53, N'15000', 2, N'§5-7 midler barnehagebarn', N'150', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (54, N'15001', 2, N'Tilsk.neds.funksjonsevne-øremerket', N'150', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (55, N'16', 2, N'Sentrale inntekter', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (56, N'17', 2, N'Overføringer kommunale bedrifter', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (57, N'18', 2, N'Finansielle poster', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (58, N'19', 2, N'Årsoppgjørsdisposisjoner', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (59, N'02', 2, N'Oppvekst', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (60, N'20', 2, N'§27/BRIS/Drammensregionen', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (61, N'201', 2, N'Aronsløkka skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (62, N'20100', 2, N'Aronsløkka skole', N'201', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (63, N'202', 2, N'Bragernes skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (64, N'20200', 2, N'Bragernes skole', N'202', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (65, N'203', 2, N'Brandengen skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (66, N'20300', 2, N'Brandengen skole', N'203', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (67, N'20301', 2, N'SFO/Brandengen', N'203', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (68, N'204', 2, N'Børresen skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (69, N'20400', 2, N'Børresen skole', N'204', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (70, N'20450', 2, N'Nøsted skole', N'204', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (71, N'205', 2, N'Danvik skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (72, N'20500', 2, N'Danvik skole', N'205', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (73, N'20501', 2, N'SFO/Danvik', N'205', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (74, N'206', 2, N'Fjell skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (75, N'20600', 2, N'Fjell skole', N'206', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (76, N'20601', 2, N'SFO/Fjell', N'206', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (77, N'207', 2, N'Frydenhaug skole', N'20', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (78, N'20700', 2, N'Frydenhaug skole', N'207', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (79, N'20702', 2, N'SFO/Frydenhaug', N'207', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (80, N'208', 2, N'Galterud skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (81, N'20800', 2, N'Galterud skole', N'208', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (82, N'209', 2, N'Gulskogen skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (83, N'20900', 2, N'Gulskogen skole', N'209', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (84, N'20902', 2, N'Gulskogen og Rødskog skoler - enhet Rødskog', N'209', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (85, N'21', 1, N'AVDELING OPPVEKST', N'F7', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (86, N'210', 2, N'Hallermoen skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (87, N'2100', 1, N'Felles administrasjon avd for skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (88, N'21000', 2, N'Hallermoen skole', N'210', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (89, N'21001', 2, N'SFO/Hallermoen', N'210', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (90, N'211', 2, N'Kjøsterud skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (91, N'2110', 1, N'Beitstad skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (92, N'21100', 2, N'Kjøsterud skole', N'211', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (93, N'2111', 1, N'Binde skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (94, N'2112', 1, N'Egge barneskole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (95, N'2113', 1, N'Byafossen skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (96, N'2114', 1, N'Kvam skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (97, N'2115', 1, N'Lø skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (98, N'2116', 1, N'Moen skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (99, N'2117', 1, N'Mære skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (100, N'2118', 1, N'Ogndal skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (101, N'212', 2, N'Konnerud skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (102, N'2120', 1, N'Skarpnes skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (103, N'21200', 2, N'Konnerud skole', N'212', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (104, N'2121', 1, N'Steinkjer skole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (105, N'2130', 1, N'Egge ungdomsskole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (106, N'2131', 1, N'Steinkjer ungdomsskole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (107, N'2140', 1, N'Steinkjer kommunale kulturskole', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (108, N'215', 2, N'Skoger skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (109, N'2150', 1, N'Steinkjer voksenopplæring', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (110, N'21500', 2, N'Skoger skole', N'215', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (111, N'21501', 2, N'SFO/Skoger', N'215', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (112, N'2160', 1, N'Barnas Hus', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (113, N'2161', 1, N'Byafossen komm barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (114, N'2162', 1, N'Tufbakken Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (115, N'2163', 1, N'Nordre Kvam Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (116, N'2164', 1, N'Søndre Egge Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (117, N'2165', 1, N'Sørlia Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (118, N'2166', 1, N'Vårtun Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (119, N'2167', 1, N'Fagerheim Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (120, N'2169', 1, N'Sannan Barnehage', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (121, N'218', 2, N'Svensedammen skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (122, N'2180', 1, N'Tjenesteenhet PPT', N'21', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (123, N'21800', 2, N'Svensedammen skole', N'218', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (124, N'219', 2, N'Vestbygda skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (125, N'21900', 2, N'Vestbygda skole', N'219', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (126, N'21901', 2, N'SFO/Vestbygda', N'219', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (127, N'220', 2, N'Øren skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (128, N'22000', 2, N'Øren skole', N'220', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (129, N'221', 2, N'Åskollen skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (130, N'22100', 2, N'Åskollen skole', N'221', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (131, N'22101', 2, N'SFO/Åskollen', N'221', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (132, N'222', 2, N'Åssiden skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (133, N'22200', 2, N'Åssiden skole', N'222', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (134, N'223', 2, N'Marienlyst skole', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (135, N'22300', 2, N'Marienlyst skole', N'223', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (136, N'229', 2, N'Utviklingsbasen', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (137, N'22900', 2, N'Utviklingsbasen', N'229', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (138, N'03', 2, N'Brannvern', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (139, N'301', 2, N'Gulskogen Helse og Omsorgsdistrikt', N'05', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (140, N'30100', 2, N'Hovedansvar Gulskogen', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (141, N'30101', 2, N'Forebyggende Gulskogen', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (142, N'30110', 2, N'Hjemmesykepleie Gulskogen', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (143, N'30115', 2, N'Dagsenter Gulskogen', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (144, N'30116', 2, N'BPA Kommunal regi Gulskogen', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (145, N'30120', 2, N'Drift Gulskogen BOSS', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (146, N'30121', 2, N'Gulskogen BOSS', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (147, N'30122', 2, N'Natt Gulskogen BOSS', N'301', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (148, N'311', 2, N'Åssiden Helse og Omsorgsdistrikt', N'06', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (149, N'31100', 2, N'Hovedansvar Åssiden HOD', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (150, N'31101', 2, N'Forebyggende Åssiden', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (151, N'31110', 2, N'Hjemmesykepleie Åssiden', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (152, N'31111', 2, N'Hjemmesykepleie Landfalløya', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (153, N'31112', 2, N'Hjemmehjemp Åssiden', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (154, N'31115', 2, N'Dagsenter Åssiden', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (155, N'31116', 2, N'BPA kommunal regi Åssiden', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (156, N'31120', 2, N'Drift Saniteten BOSS', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (157, N'31121', 2, N'Natt Saniteten BOSS', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (158, N'31122', 2, N'2. etg Saniteten BOSS Solstua', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (159, N'31123', 2, N'3. etg Saniteten BOSS Seniorstua', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (160, N'31124', 2, N'4. etg Saniteten BOSS Kvisten', N'311', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (161, N'321', 2, N'Hamborgstrøm Helse og Omsorgsdistrikt', N'07', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (162, N'32100', 2, N'Hovedansvar Hamborgstrøm HOD', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (163, N'32101', 2, N'Forebyggende Hamborgstrøm', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (164, N'32102', 2, N'Vaskeri Hamborgstrøm', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (165, N'32110', 2, N'Hjemmesykepleie Hamborgstrøm', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (166, N'32115', 2, N'Villa Fredrikke aktivitetshus', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (167, N'32116', 2, N'BPA kommunal regi Hamborgstrøm', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (168, N'32120', 2, N'Hamborgstrøm BOSS Drift', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (169, N'32121', 2, N'Hamborgstrøm BOSS avd Hotvedt', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (170, N'32122', 2, N'Hamborgstrøm BOSS avd Øren', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (171, N'32123', 2, N'Hamborgstrøm BOSS natt', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (172, N'32124', 2, N'Hamborgstrøm BOSS forsterket', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (173, N'32130', 2, N'Filten serviceboliger', N'321', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (174, N'33', 1, N'AVDELING HELSE', N'F7', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (175, N'3300', 1, N'Avdeling for helse og rehabilitering', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (176, N'3305', 1, N'Tjenesteenhet NAV - sosiale tjenester', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (177, N'331', 2, N'Losjeplassen Helse og Omsorgsdistrikt', N'08', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (178, N'3310', 1, N'Tjenesteenhet barn og familie', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (179, N'33100', 2, N'Hovedansvar Losjeplassen HOD', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (180, N'33101', 2, N'Forebyggende Losjeplassen', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (181, N'33110', 2, N'Hjemmesykepleie Losjeplassen', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (182, N'33115', 2, N'Dagsenter Losjeplassen', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (183, N'33120', 2, N'Losjeplassen BOSS drift', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (184, N'33121', 2, N'Losjeplassen BOSS 2. etg', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (185, N'33122', 2, N'Losjeplassen BOSS 3. etg', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (186, N'33123', 2, N'Losjeplassen BOSS natt', N'331', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (187, N'3320', 1, N'Tjenesteenhet avlastning barn og unge', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (188, N'3340', 1, N'Tjenesteenhet døgnrehabilitering', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (189, N'3350', 1, N'Tjenesteenhet arbeidssentralen arbeid og aktivise', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (190, N'3360', 1, N'Tjenesteenhet for legevakt og legetjenester', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (191, N'3380', 1, N'Tjenesteenhet Barnevern', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (192, N'3390', 1, N'Tjenesteenhet DMS', N'33', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (193, N'341', 2, N'Marienlyst Helse og Omsorgsdistrikt', N'08', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (194, N'34100', 2, N'Hovedansvar Marienlyst HOD', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (195, N'34101', 2, N'Forebyggende Marienlyst', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (196, N'34102', 2, N'Felles nattjeneste', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (197, N'34110', 2, N'Hjemmesykepleie Marienlyst', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (198, N'34115', 2, N'Marienlyst dagsenter', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (199, N'34116', 2, N'BPA kommunal regi Marienlyst', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (200, N'34120', 2, N'Strømsø BOSS drift', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (201, N'34121', 2, N'Strømsø BOSS avd B', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (202, N'34122', 2, N'Strømsø BOSS avd C', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (203, N'34123', 2, N'Strømsø BOSS avd D', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (204, N'34124', 2, N'Strømsø BOSS avd E', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (205, N'34125', 2, N'Strømsø BOSS natt', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (206, N'34130', 2, N'Austadgata 36', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (207, N'34131', 2, N'Omsorgsboliger drift', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (208, N'34132', 2, N'Schwartzgate', N'341', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (209, N'351', 2, N'Åskollen Helse og Omsorgsdistrikt', N'10', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (210, N'35100', 2, N'Hovedansvar Åskollen HOD', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (211, N'35101', 2, N'Forebyggende Åskollen', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (212, N'35110', 2, N'Hjemmesykepleie', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (213, N'35115', 2, N'Åskollen dagsenter', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (214, N'35116', 2, N'BPA kommunal regi Åskollen', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (215, N'35120', 2, N'Åskollen BOSS drift', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (216, N'35121', 2, N'Åskollen BOSS 1. etg', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (217, N'35122', 2, N'Åskollen BOSS 2. etg', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (218, N'35123', 2, N'Åskollen BOSS natt', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (219, N'35130', 2, N'Seilmakerstua psykisk', N'351', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (220, N'361', 2, N'Fjell Helse og Omsorgsdistrikt', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (221, N'36100', 2, N'Hovedansvar Fjell HOD', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (222, N'36101', 2, N'Forebyggende Fjell', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (223, N'36110', 2, N'Hjemmesykepleie Fjell', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (224, N'36112', 2, N'Respirator HTB Fjell', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (225, N'36116', 2, N'BPA kommunal regi Fjell', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (226, N'36120', 2, N'Fjell BOSS drift', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (227, N'36121', 2, N'Fjell BOSS Furua', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (228, N'36122', 2, N'Fjell BOSS Eika', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (229, N'36123', 2, N'Fjell BOSS natt', N'361', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (230, N'371', 2, N'Konnerud Helse og Omsorgsdistrikt', N'12', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (231, N'37100', 2, N'Hovedansvar Konnerud HOD', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (232, N'37101', 2, N'Forebyggende Konnerud', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (233, N'37110', 2, N'Hjemmesykepleie Konnerud', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (234, N'37115', 2, N'Dagsenter Konnerud', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (235, N'37116', 2, N'BPA kommunal regi Konnerud', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (236, N'37120', 2, N'Konnerud BOSS drift', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (237, N'37121', 2, N'Konnerud BOSS 1. etg', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (238, N'37122', 2, N'Konnerud BOSS 2. etg', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (239, N'37123', 2, N'Konnerud BOSS natt', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (240, N'37130', 2, N'Fredholt BOSS drift', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (241, N'37131', 2, N'Fredholt BOSS Rød', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (242, N'37132', 2, N'Fredholt BOSS Blå', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (243, N'37133', 2, N'Fredholt BOSS natt', N'371', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (244, N'391', 2, N'Tjenestetildeling og samordning', N'13', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (245, N'39100', 2, N'Hovedansvar Tjenestetildeling og samordning', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (246, N'39110', 2, N'Boligtjenesten', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (247, N'39120', 2, N'Administrative fellestjenester', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (248, N'39121', 2, N'Drifts trygghetsalarmer', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (249, N'39130', 2, N'Kontor for tjenestetildeling', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (250, N'39131', 2, N'Brukerrettede tjenester', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (251, N'39132', 2, N'Brukervalgt hjemmetjenester', N'391', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (252, N'04', 2, N'Byutvikling', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (253, N'45', 1, N'AVDELING SAMFUNNSUTVIKLING', N'F7', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (254, N'4500', 1, N'Avdeling for kommunaltekniske tjenester', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (255, N'451', 2, N'Helsetjenesten', N'14', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (256, N'4510', 1, N'Tjenesteenhet vegtrafikk og park', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (257, N'45100', 2, N'Hovedansvar Helsetjenesten', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (258, N'45110', 2, N'Samfunnsmedisinsk', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (259, N'45111', 2, N'Sykehjemsleger', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (260, N'45112', 2, N'Fastlege ordningen', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (261, N'45113', 2, N'Smittevernkontor', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (262, N'45114', 2, N'Miljørettet helsevern', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (263, N'45115', 2, N'Poliklinisk /ambulerende team', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (264, N'45116', 2, N'Laboratorietjenesten', N'451', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (265, N'4522', 1, N'Tjenesteenhet vann og avløp - område vann', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (266, N'4523', 1, N'Tjenesteenhet vann og avløp - område avløp', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (267, N'4524', 1, N'Tjenesteenhet renovasjon - område renovasjon', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (268, N'4525', 1, N'Tjenesteenhet vann og avløp - område slam', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (269, N'4526', 1, N'Tjenesteenhet renovasjon - område næringsavfall', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (270, N'4530', 1, N'Tjenesteenhet utbygging', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (271, N'4540', 1, N'Tjenesteenhet byggesak og oppmåling', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (272, N'4550', 1, N'Tjenesteenhet eiendom', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (273, N'4560', 1, N'Tjenesteenhet kjøkken', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (274, N'4571', 1, N'Tjenestenhet bibliotek', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (275, N'4572', 1, N'Tjenesteenhet kino og kulturarrangement', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (276, N'4573', 1, N'Tjenesteenhet flyktninge- og aktivitetstjenesten', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (277, N'4580', 1, N'Tjenesteenhet landbruk', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (278, N'4590', 1, N'Maskinregnskapet (dummy)', N'45', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (279, N'471', 2, N'Senter for rusforebygging', N'13', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (280, N'47100', 2, N'Hovedansvar Senter for rusforebygging', N'471', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (281, N'47110', 2, N'Forebyggende uteteam', N'471', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (282, N'47120', 2, N'Rehabiliteringsteam', N'471', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (283, N'47130', 2, N'Rusakutt', N'471', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (284, N'47131', 2, N'Fengselshelsetjenesten', N'471', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (285, N'47132', 2, N'Bo 7 og botiltak', N'471', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (286, N'05', 2, N'Helse og omsorg', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (287, N'502', 2, N'Senter for oppvekst', N'02', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (288, N'50200', 2, N'Drift av senter for oppvekst', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (289, N'50205', 2, N'Æresrelatert vold', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (290, N'50206', 2, N'Prosjekter', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (291, N'50210', 2, N'Barneverntjenesten', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (292, N'50211', 2, N'Sosial- og barnevernvakta', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (293, N'50212', 2, N'Enslige mindreårige flyktninger', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (294, N'50219', 2, N'Tiltak', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (295, N'50220', 2, N'Habiliteringstjenesten', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (296, N'50221', 2, N'Solenga', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (297, N'50222', 2, N'Hestenga', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (298, N'50230', 2, N'PP-tjenesten', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (299, N'50240', 2, N'Tiltak i og utenfor hjemmet', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (300, N'50243', 2, N'Støttetiltak i hjemmet', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (301, N'50250', 2, N'Oppvekst teamet', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (302, N'50260', 2, N'Barnevern administrasjon', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (303, N'50261', 2, N'Bragernes barnevern', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (304, N'50262', 2, N'Fjell/Konnerud barnevern', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (305, N'50263', 2, N'Strømsø barnevern', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (306, N'50264', 2, N'Fosterhjem', N'502', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (307, N'505', 2, N'Byplan', N'04', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (308, N'50500', 2, N'Byplan', N'505', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (309, N'50501', 2, N'Skjenkebevillinger', N'505', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (310, N'50502', 2, N'Byggesak', N'505', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (311, N'50504', 2, N'Byplansjef', N'505', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (312, N'506', 2, N'Byprosjekter', N'04', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (313, N'50600', 2, N'Byprosjekter', N'506', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (314, N'508', 2, N'Helsestasjonene', N'02', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (315, N'50800', 2, N'Helsestasjonene', N'508', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (316, N'50801', 2, N'Helsestasjon Marienlyst', N'508', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (317, N'50802', 2, N'Helsestasjon Fjell', N'508', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (318, N'50803', 2, N'Helsestasjon Konnerud', N'508', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (319, N'50804', 2, N'Helsestasjon / Skoleleger', N'508', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (320, N'50805', 2, N'Skolehelsetjenesten', N'508', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (321, N'51', 1, N'AVDELING OMSORG', N'F7', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (322, N'5100', 1, N'Bistand og omsorg felles', N'51', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (323, N'511', 2, N'Introduksjonssenteret', N'13', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (324, N'5110', 1, N'Tjenesteenhet hjemmetjenester nord', N'51', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (325, N'51100', 2, N'Etablering kvalifisering og arbeid', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (326, N'51101', 2, N'Norsk opplæring praksis', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (327, N'51102', 2, N'Introduksjonstønad', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (328, N'51103', 2, N'Introduksjonssenter avd VO Kjøsterud', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (329, N'51104', 2, N'Språktjenester', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (330, N'51105', 2, N'Grunnskole introduksjonssenteret', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (331, N'51106', 2, N'Drift Intro.', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (332, N'51110', 2, N'Bo-og støttetilbud', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (333, N'51111', 2, N'Ekstramidler funksjonshemmet', N'511', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (334, N'5120', 1, N'Tjenesteenhet hjemmetjenester sør', N'51', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (335, N'513', 2, N'Kemneren i Drammensregionen', N'20', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (336, N'5130', 1, N'Tjenesteenhet boliger for funksjonshemmede', N'51', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (337, N'51300', 2, N'Kemneren i Drammensregionen', N'513', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (338, N'5140', 1, N'Tjenesteenhet sykehjem', N'51', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (339, N'5150', 1, N'Tjenesteenhet bolig- og tildelingskontoret', N'51', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (340, N'519', 2, N'Vei natur og idrett', N'11', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (341, N'51900', 2, N'VNI sentralt', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (342, N'51901', 2, N'Park', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (343, N'51902', 2, N'Idrett', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (344, N'51903', 2, N'Drammenshallen', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (345, N'51904', 2, N'Vei funksjon nord', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (346, N'51905', 2, N'Vei funksjon sør', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (347, N'51906', 2, N'Vei vedlikehold', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (348, N'51907', 2, N'Vei elektro', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (349, N'51908', 2, N'Marienlyst', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (350, N'51909', 2, N'Fontener', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (351, N'51910', 2, N'Vei felles', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (352, N'51920', 2, N'', N'519', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (353, N'525', 2, N'Vann og avløp', N'14', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (354, N'52500', 2, N'Vann og avløp', N'525', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (355, N'52510', 2, N'Vannforsyning', N'525', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (356, N'52511', 2, N'Vann prosess', N'525', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (357, N'52520', 2, N'Avløp og rensing', N'525', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (358, N'52521', 2, N'Avløp prosess', N'525', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (359, N'52590', 2, N'Lager', N'525', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (360, N'526', 2, N'Næringsutvikling', N'08', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (361, N'52600', 2, N'Næringsutvikling', N'526', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (362, N'527', 2, N'NAV Drammen', N'13', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (363, N'52710', 2, N'NAV Drammen driftsutgifter', N'527', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (364, N'52715', 2, N'NAV Drammen lønnsutgifter', N'527', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (365, N'52720', 2, N'Sosialhjelpsutgifter', N'527', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (366, N'52730', 2, N'KVP - lønn ansatte', N'527', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (367, N'52731', 2, N'KVP - lønn deltagere og tiltak', N'527', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (368, N'52740', 2, N'Husbankens virkemidler', N'527', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (369, N'530', 2, N'Kultur', N'06', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (370, N'53000', 2, N'Kultur stab', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (371, N'53001', 2, N'Tilskudd kulturformål', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (372, N'53010', 2, N'Kulturskolen', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (373, N'53011', 2, N'Den kulturelle skolesekken', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (374, N'53012', 2, N'Kultur Oasen', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (375, N'53013', 2, N'Miljølæreplan', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (376, N'53020', 2, N'Drammen bibliotek', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (377, N'53030', 2, N'Fritid', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (378, N'53032', 2, N'G60', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (379, N'53033', 2, N'Fjell nærmiljøkontor', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (380, N'53035', 2, N'Interkultur', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (381, N'53036', 2, N'Nøstedhallen', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (382, N'53039', 2, N'Drammen Frivilligsentraler', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (383, N'53043', 2, N'Bestillerenhet Tjenestetildeling', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (384, N'53044', 2, N'Bestillerenhet Barnevern', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (385, N'53045', 2, N'Bestillerenhet Habilitering', N'530', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (386, N'06', 2, N'Kultur', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (387, N'601', 2, N'Brukerbetaling P01-P14', N'04', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (388, N'60104', 2, N'Tvangsmulktinntekter P04', N'601', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (389, N'603', 2, N'Sentrale utg innt P01 til P14', N'01', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (390, N'60301', 2, N'Sentrale utg P01 Barnehage', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (391, N'60302', 2, N'Sentrale utg P02 Barnevern', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (392, N'60303', 2, N'Sentrale utg P03 Brannvern', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (393, N'60304', 2, N'Sentrale utg P04 Byutvikling', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (394, N'60305', 2, N'Sentrale utg P05 Helse', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (395, N'60306', 2, N'Sentrale utg P06 Kultur', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (396, N'60307', 2, N'Sentrale utg P07 LOS', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (397, N'60308', 2, N'Sentrale utg P08 Miljø komp næring', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (398, N'60309', 2, N'Sentrale utg P09 PLO', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (399, N'60310', 2, N'Sentrale utg P10 Politisk virk.', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (400, N'60311', 2, N'Sentrale utg P11 Samferdsel', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (401, N'60312', 2, N'Sentrale utg P12 Skole', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (402, N'60314', 2, N'Sentrale utg P14 VAR', N'603', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (403, N'605', 2, N'Sentrale prosjektmidler', N'02', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (404, N'60502', 2, N'Sentrale prosjekter P02 Barnevern', N'605', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (405, N'60509', 2, N'Vederlagsinntekter P05', N'605', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (406, N'60512', 2, N'Sentrale prosjekter P12 Skole', N'605', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (407, N'07', 2, N'Ledelse organisering og styring', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (408, N'070', 2, N'Kommuneadvokaten i Drammensregionen  og Overformynderi', N'07', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (409, N'700', 2, N'Frie disponible inntekter', N'16', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (410, N'7000', 2, N'Kommuneadvokaten i Drammensregionen', N'070', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (411, N'70000', 2, N'Sentrale inntekter', N'700', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (412, N'7001', 2, N'Overformynderi', N'070', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (413, N'701', 2, N'Renter og avdrag', N'18', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (414, N'70100', 2, N'Renter og avdrag avskrivn inntekter', N'701', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (415, N'702', 2, N'Inntekter fra bedriftene', N'17', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (416, N'70200', 2, N'Inntekter fra bedriftene', N'702', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (417, N'706', 2, N'Avskrivninger', N'15', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (418, N'70600', 2, N'Avskrivninger', N'706', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (419, N'709', 2, N'Årsoppgjørsdisp.', N'19', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (420, N'70900', 2, N'Årsoppgjørsdisposisjoner', N'709', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (421, N'08', 2, N'Miljø kompetanse og næring', N'F6', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (422, N'080', 2, N'Rådmannens seksjoner', N'07', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (423, N'8000', 2, N'Drift seksjoner', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (424, N'8010', 2, N'Lønn Rådmann seksjon', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (425, N'8020', 2, N'Lønn Arbeidsgiver seksjon', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (426, N'8022', 2, N'Lærlinger', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (427, N'8030', 2, N'Lønn Plan- og økonomi seksjon', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (428, N'8080', 2, N'Informasjon og samfunnskontakt', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (429, N'8081', 2, N'Politisk styring', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (430, N'8090', 2, N'Revisjon og kontrollutvalg', N'080', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (431, N'81', 1, N'FELLESOMRÅDET', N'F7', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 2, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (432, N'8100', 1, N'Skatter og rammetilskudd', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (433, N'8101', 1, N'Renter og avdrag kommunal drift', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (434, N'8102', 1, N'Renter og avdrag utlån', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (435, N'8103', 1, N'Kalkulatoriske renter og avskrivninger', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (436, N'8109', 1, N'Diverse felles', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (437, N'8111', 1, N'Pensjon', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (438, N'8190', 1, N'Interne finansieringstransaksjoner', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (439, N'8199', 1, N'Årets resultat  (2)', N'81', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (440, N'090', 2, N'Service og administrasjon', N'15', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (441, N'9000', 2, N'Service og administrasjon', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (442, N'9001', 2, N'Hovedtillitsvalgte og hovedverneombud', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (443, N'9002', 2, N'Bygninger', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (444, N'9010', 2, N'Innkjøp', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (445, N'9020', 2, N'Økonomi', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (446, N'9050', 2, N'Servicetorg', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (447, N'9060', 2, N'Byarkiv', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (448, N'9061', 2, N'Grafisk senter', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (449, N'9080', 2, N'Kart og Geodata', N'090', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (450, N'091', 2, N'D-IKT', N'20', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 3, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (451, N'9100', 2, N'D-IKT Lønn og administrasjon', N'091', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (452, N'9101', 2, N'D-IKT Data', N'091', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (453, N'9102', 2, N'D-IKT Telefoni', N'091', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 4, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (454, N'F6', 2, N'Drammen kommune', N' ', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 1, N'2010v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_values] ([pk_id], [org_id], [fk_tenant_id], [org_name], [parent], [date_from], [date_to], [active], [hierarchy_level], [fk_org_version], [updated], [updated_by]) VALUES (455, N'F7', 1, N'Steinkjer kommune', N' ', CAST(0xB1DC0A00 AS Date), CAST(0xC1B61000 AS Date), 1, 1, N'2012v1', CAST(0x0000A3CE00000000 AS DateTime), 1)
GO
SET IDENTITY_INSERT [dbo].[tco_org_values] OFF
GO
