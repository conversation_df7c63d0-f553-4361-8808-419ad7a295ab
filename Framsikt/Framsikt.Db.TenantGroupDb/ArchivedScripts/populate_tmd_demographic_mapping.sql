
SET IDENTITY_INSERT [dbo].[tmd_demographic_mapping] ON 

GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (1, 1, N'21', N'2011', 15, N'MMMM', CAST(94.00 AS Numeric(5, 2)), CAST(94.00 AS Numeric(5, 2)), CAST(94.00 AS Numeric(5, 2)), CAST(94.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100000.00 AS Numeric(18, 2)), CAST(100000.00 AS Numeric(18, 2)), CAST(100000.00 AS Numeric(18, 2)), CAST(100000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B76F AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (2, 1, N'21', N'2021', 20, N'MMMM', CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(90000.00 AS Numeric(18, 2)), CAST(90000.00 AS Numeric(18, 2)), CAST(90000.00 AS Numeric(18, 2)), CAST(90000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B76F AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (3, 1, N'21', N'2151', 11, N'MMMM', CAST(40.00 AS Numeric(5, 2)), CAST(40.00 AS Numeric(5, 2)), CAST(40.00 AS Numeric(5, 2)), CAST(40.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(60.00 AS Numeric(5, 2)), CAST(23000.00 AS Numeric(18, 2)), CAST(23000.00 AS Numeric(18, 2)), CAST(23000.00 AS Numeric(18, 2)), CAST(23000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B76F AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (4, 1, N'21', N'2212', 11, N'MMMM', CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B76F AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (5, 1, N'21', N'2222', 11, N'MMMM', CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B76F AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (6, 1, N'21', N'2230', 11, N'MMMM', CAST(50.00 AS Numeric(5, 2)), CAST(50.00 AS Numeric(5, 2)), CAST(50.00 AS Numeric(5, 2)), CAST(50.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(7000.00 AS Numeric(18, 2)), CAST(7000.00 AS Numeric(18, 2)), CAST(7000.00 AS Numeric(18, 2)), CAST(7000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B76F AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (7, 1, N'33', N'2410', 11, N'MMMM', CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), CAST(10000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B774 AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (8, 1, N'51', N'2530', 53, N'MMMM', CAST(13.00 AS Numeric(5, 2)), CAST(13.00 AS Numeric(5, 2)), CAST(13.00 AS Numeric(5, 2)), CAST(13.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(950000.00 AS Numeric(18, 2)), CAST(950000.00 AS Numeric(18, 2)), CAST(950000.00 AS Numeric(18, 2)), CAST(950000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B774 AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (9, 1, N'51', N'2540', 50, N'MMMM', CAST(18.00 AS Numeric(5, 2)), CAST(18.00 AS Numeric(5, 2)), CAST(18.00 AS Numeric(5, 2)), CAST(18.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(200000.00 AS Numeric(18, 2)), CAST(200000.00 AS Numeric(18, 2)), CAST(200000.00 AS Numeric(18, 2)), CAST(200000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B774 AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (10, 1, N'33', N'2320', 11, N'MMMM', CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(1000.00 AS Numeric(18, 2)), CAST(1000.00 AS Numeric(18, 2)), CAST(1000.00 AS Numeric(18, 2)), CAST(1000.00 AS Numeric(18, 2)), 1, CAST(0x0000A4910128B777 AS DateTime), N'1002')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (11, 7, N'14', N'2012', 15, N'MMMM', CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(90.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(114755.00 AS Numeric(18, 2)), CAST(114755.00 AS Numeric(18, 2)), CAST(114755.00 AS Numeric(18, 2)), CAST(114755.00 AS Numeric(18, 2)), 1, CAST(0x0000A4AD0082AD8E AS DateTime), N'7005')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (12, 7, N'14', N'2020', 20, N'MMMM', CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(89188.00 AS Numeric(18, 2)), CAST(89188.00 AS Numeric(18, 2)), CAST(89188.00 AS Numeric(18, 2)), CAST(89188.00 AS Numeric(18, 2)), 1, CAST(0x0000A4AD0082B9DC AS DateTime), N'7005')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (13, 7, N'14', N'2150', 21, N'MMMM', CAST(45.00 AS Numeric(5, 2)), CAST(45.00 AS Numeric(5, 2)), CAST(45.00 AS Numeric(5, 2)), CAST(45.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(100.00 AS Numeric(5, 2)), CAST(24198.00 AS Numeric(18, 2)), CAST(24198.00 AS Numeric(18, 2)), CAST(24198.00 AS Numeric(18, 2)), CAST(24198.00 AS Numeric(18, 2)), 1, CAST(0x0000A4AD0082C044 AS DateTime), N'7005')
GO
INSERT [dbo].[tmd_demographic_mapping] ([pk_id], [fk_tenant_id], [fk_org_id], [fk_function_code], [fk_interval_id], [fk_forecast_type], [coverage_pct_1], [coverage_pct_2], [coverage_pct_3], [coverage_pct_4], [compensation_pct_1], [compensation_pct_2], [compensation_pct_3], [compensation_pct_4], [unit_cost_1], [unit_cost_2], [unit_cost_3], [unit_cost_4], [active], [updated], [updated_by]) VALUES (14, 7, N'14', N'2230', 20, N'MMMM', CAST(25.80 AS Numeric(5, 2)), CAST(25.80 AS Numeric(5, 2)), CAST(25.80 AS Numeric(5, 2)), CAST(25.80 AS Numeric(5, 2)), CAST(0.00 AS Numeric(5, 2)), CAST(0.00 AS Numeric(5, 2)), CAST(0.00 AS Numeric(5, 2)), CAST(0.00 AS Numeric(5, 2)), CAST(7698.00 AS Numeric(18, 2)), CAST(7698.00 AS Numeric(18, 2)), CAST(7698.00 AS Numeric(18, 2)), CAST(7698.00 AS Numeric(18, 2)), 1, CAST(0x0000A4AD0082E9CD AS DateTime), N'7005')
GO
SET IDENTITY_INSERT [dbo].[tmd_demographic_mapping] OFF
GO
