INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0010', N'Fastlønn', N'oapmahaccat', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0020', N'Lønn til vikarer', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0030', N'Lønn til ekstrahjelp', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0040', N'Overtidslønn', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0050', N'Annen lønn og trekkpliktige godtgjørelser', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0070', N'Lønn nybygg og nyanlegg', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0075', N'Lønn renhold', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0080', N'Godtgjørelse folkevalgte', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0089', N'Trekkpliktig/oppgavepliktig, ikke arbeidsgiveravgiftspliktig lønn', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0090', N'Pensjonsinnskudd og trekkpliktige forsikringsordninger', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0099', N'Arbeidsgiveravgift ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0100', N'Kontormateriell', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0105', N'Undervisningsmateriell', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0110', N'Medisinsk forbruksmateriell', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0114', N'Medikamenter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0115', N'Matvarer', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0120', N'Samlepost annet forbruksmateriell, råvarer og tjenester', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0130', N'Post, banktjenester, telefon, internett/bredbånd', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0140', N'Annonse, reklame, informasjon', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0150', N'Opplæring, kurs', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0160', N'Utgifter og godtgjørelser for reiser, diett, bil m.v. som er oppgavepliktige', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0165', N'Andre oppgavepliktige godtgjørelser', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0170', N'Transportutgifter og drift av egne og leide transportmidler (herunder anleggsmaskiner ol.)', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0180', N'Strøm', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0181', N'Fjernvarme', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0182', N'Fyringsolje og fyringsparafin', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0183', N'Naturgass', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0184', N'Bioenergi', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0185', N'Forsikringer og utgifter til vakthold og sikring', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0190', N'Leie av lokaler og grunn', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0195', N'Avgifter, gebyrer, lisenser o.l. ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0200', N'Kjøp og finansiell leasing av driftsmidler', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0209', N'Medisinsk utstyr', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0210', N'Kjøp, leie og leasing av transportmidler', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0220', N'Leie av driftsmidler', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0230', N'Vedlikehold, byggetjenester og nybygg ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0240', N'Serviceavtaler, reparasjoner og vaktmestertjenester', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0250', N'Materialer til vedlikehold, påkostning og nybygg ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0260', N'Renholds- og vaskeritjenester ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0270', N'Andre tjenester (som inngår i egenproduksjon) ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0280', N'Grunnerverv', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0285', N'Kjøp av eksisterende bygg/anlegg ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0290', N'Internkjøp ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0300', N'Kjøp fra staten', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0330', N'Kjøp fra fylkeskommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0350', N'Kjøp fra kommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0370', N'Kjøp fra andre (private)', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0375', N'Kjøp fra IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0380', N'Kjøp fra (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0400', N'Overføring til staten', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0429', N'Merverdiavgift som gir rett til momskompensasjon', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0430', N'Overføring til fylkeskommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0450', N'Overføring til kommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0470', N'Overføring til andre (private)', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0475', N'Overføring til IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0480', N'Overføring til (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0490', N'(Reserverte bevilgninger/avsetninger)', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0500', N'Renteutgifter, provisjoner og andre finansutgifter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0501', N'Konserninterne renteutgifter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0509', N'Tap finansielle instrumenter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0510', N'Avdrag', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0511', N'Konserninterne avdrag ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0520', N'Utlån', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0521', N'Konserninterne utlån ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0529', N'Kjøp av aksjer og andeler', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0530', N'Dekning av tidligere års regnskapsmessige merforbruk/dekning av tidligere års udekket', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0540', N'Avsetninger til disposisjonsfond', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0548', N'Avsetninger til ubundne investeringsfond', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0550', N'Avsetninger til bundne fond ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0570', N'Overføring til investeringsregnskapet', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0580', N'Regnskapsmessig mindreforbruk/udisponert', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0590', N'Avskrivninger', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0600', N'Brukerbetaling for kommunale tjenester', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0620', N'Annet salg av varer og tjenester, gebyrer o.l. utenfor avgiftsområdet', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0629', N'Billettinntekter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0630', N'Husleieinntekter, festeavgifter, utleie av lokaler', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0640', N'Avgiftspliktige gebyrer', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0650', N'Annet avgiftspliktig salg av varer og tjenester', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0660', N'Salg av driftsmidler ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0670', N'Salg av fast eiendom ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0690', N'Fordelte utgifter / Internsalg ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0700', N'Refusjon fra staten', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0710', N'Sykelønnsrefusjon', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0729', N'Kompensasjon for merverdiavgift', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0730', N'Refusjon fra fylkeskommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0750', N'Refusjon fra kommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0770', N'Refusjon fra andre (private)', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0775', N'Salg til IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0780', N'Salg til (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0790', N'Internsalg ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0800', N'Rammetilskudd ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0810', N'Andre statlige overføringer', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0830', N'Overføring fra fylkeskommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0850', N'Overføring fra kommuner', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0870', N'Skatt på inntekt og formue', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0874', N'Eiendomsskatt annen eiendom', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0875', N'Eiendomsskatt boliger og fritidseiendommer', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0877', N'Andre direkte og indirekte skatter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0880', N'Overføringer fra (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0890', N'Overføringer fra andre (private)', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0895', N'Overføring fra IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0900', N'Renteinntekter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0901', N'Konserninterne renteinntekter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0905', N'Utbytte og eieruttak', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0909', N'Gevinst finansielle instrumenter', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0910', N'Bruk av lån', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0911', N'Bruk av konserninterne lån', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0920', N'Mottatte avdrag på utlån', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0921', N'Mottatte avdrag på konserninterne utlån ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0929', N'Salg av aksjer og andeler', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0930', N'Bruk av tidligere års regnskapsmessige mindreforbruk/bruk av tidligere års udisponert', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0940', N'Bruk av disposisjonsfond ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0948', N'Bruk av ubundne investeringsfond', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0950', N'Bruk av bundne driftsfond ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0958', N'Bruk av bundne investeringsfond', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0970', N'Overføringer fra driftsregnskapet', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0980', N'Regnskapsmessig merforbruk/ udekket', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'0990', N'Motpost avskrivninger ', N' ', N'investment', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1010', N'Fastlønn', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1020', N'Lønn til vikarer', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1030', N'Lønn til ekstrahjelp', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1040', N'Overtidslønn', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1050', N'Annen lønn og trekkpliktige godtgjørelser', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1070', N'Lønn vedlikehold', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1075', N'Lønn renhold', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1080', N'Godtgjørelse folkevalgte', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1089', N'Trekkpliktig/oppgavepliktig, ikke arbeidsgiveravgiftspliktig lønn', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1090', N'Pensjonsinnskudd og trekkpliktige forsikringsordninger', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1099', N'Arbeidsgiveravgift ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1100', N'Kontormateriell', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1105', N'Undervisningsmateriell', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1110', N'Medisinsk forbruksmateriell', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1114', N'Medikamenter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1115', N'Matvarer', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1120', N'Samlepost annet forbruksmateriell, råvarer og tjenester', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1130', N'Post, banktjenester, telefon, internett/bredbånd', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1140', N'Annonse, reklame, informasjon', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1150', N'Opplæring, kurs', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1160', N'Utgifter og godtgjørelser for reiser, diett, bil m.v. som er oppgavepliktige', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1165', N'Andre oppgavepliktige godtgjørelser', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1170', N'Transportutgifter og drift av egne og leide transportmidler (herunder anleggsmaskiner ol.)', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1180', N'Strøm', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1181', N'Fjernvarme', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1182', N'Fyringsolje og fyringsparafin', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1183', N'Naturgass', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1184', N'Bioenergi', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1185', N'Forsikringer og utgifter til vakthold og sikring', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1190', N'Leie av lokaler og grunn', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1195', N'Avgifter, gebyrer, lisenser o.l. ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1200', N'Kjøp og finansiell leasing av driftsmidler', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1209', N'Medisinsk utstyr', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1210', N'Kjøp, leie og leasing av transportmidler', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1220', N'Leie av driftsmidler', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1230', N'Vedlikehold, byggetjenester og nybygg', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1240', N'Serviceavtaler, reparasjoner og vaktmestertjenester', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1250', N'Materialer til vedlikehold, påkostning og nybygg ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1260', N'Renholds- og vaskeritjenester ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1270', N'Andre tjenester (som inngår i egenproduksjon) ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1280', N'Grunnerverv', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1285', N'Kjøp av eksisterende bygg/anlegg ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1290', N'Internkjøp ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1300', N'Kjøp fra staten', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1330', N'Kjøp fra fylkeskommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1350', N'Kjøp fra kommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1370', N'Kjøp fra andre (private)', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1375', N'Kjøp fra IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1380', N'Kjøp fra (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1400', N'Overføring til staten', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1429', N'Merverdiavgift som gir rett til momskompensasjon', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1430', N'Overføring til fylkeskommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1450', N'Overføring til kommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1470', N'Overføring til andre (private)', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1475', N'Overføring til IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1480', N'Overføring til (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1490', N'(Reserverte bevilgninger/avsetninger)', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1500', N'Renteutgifter, provisjoner og andre finansutgifter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1501', N'Konserninterne renteutgifter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1509', N'Tap finansielle instrumenter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1510', N'Avdrag', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1511', N'Konserninterne avdrag', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1520', N'Utlån', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1521', N'Konserninterne utlån ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1529', N'Kjøp av aksjer og andeler', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1530', N'Dekning av tidligere års regnskapsmessige merforbruk/dekning av tidligere års udekket', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1540', N'Avsetninger til disposisjonsfond ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1548', N'Avsetninger til ubundne investeringsfond', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1550', N'Avsetninger til bundne fond ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1570', N'Overføring til investeringsregnskapet ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1580', N'Regnskapsmessig mindreforbruk/udisponert', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1590', N'Avskrivninger ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1600', N'Brukerbetaling for kommunale tjenester', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1620', N'Annet salg av varer og tjenester, gebyrer o.l. utenfor avgiftsområdet', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1629', N'Billettinntekter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1630', N'Husleieinntekter, festeavgifter, utleie av lokaler', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1640', N'Avgiftspliktige gebyrer', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1650', N'Annet avgiftspliktig salg av varer og tjenester', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1660', N'Salg av driftsmidler ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1670', N'Salg av fast eiendom', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1690', N'Fordelte utgifter / Internsalg ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1700', N'Refusjon fra staten', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1710', N'Sykelønnsrefusjon', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1729', N'Kompensasjon for merverdiavgift', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1730', N'Refusjon fra fylkeskommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1750', N'Refusjon fra kommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1770', N'Refusjon fra andre (private)', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1775', N'Salg til IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1780', N'Salg til (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1790', N'Internsalg ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1800', N'Rammetilskudd ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1810', N'Andre statlige overføringer', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1830', N'Overføring fra fylkeskommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1850', N'Overføring fra kommuner', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1870', N'Skatt på inntekt og formue', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1874', N'Eiendomsskatt annen eiendom', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1875', N'Eiendomsskatt boliger og fritidseiendommer', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1877', N'Andre direkte og indirekte skatter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1880', N'Overføringer fra (fylkes)kommunalt foretak i egen kommune/fylkeskommune', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1890', N'Overføringer fra andre (private)', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1895', N'Overføring fra IKS der kommunen/fylkeskommunen selv er deltager', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1900', N'Renteinntekter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1901', N'Konserninterne renteinntekter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1905', N'Utbytte og eieruttak', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1909', N'Gevinst finansielle instrumenter', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1910', N'Bruk av lån', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1911', N'Bruk av konserninterne lån', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1920', N'Mottatte avdrag på utlån', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1921', N'Mottatte avdrag på konserninterne utlån ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1929', N'Salg av aksjer og andeler', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1930', N'Bruk av tidligere års regnskapsmessige mindreforbruk/bruk av tidligere års udisponert', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1940', N'Bruk av disposisjonsfond ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1948', N'Bruk av ubundne investeringsfond', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1950', N'Bruk av bundne driftsfond ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1958', N'Bruk av bundne investeringsfond', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1970', N'Overføringer fra driftsregnskapet', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1980', N'Regnskapsmessig merforbruk/ udekket', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'1990', N'Motpost avskrivninger ', N' ', N'operations', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'210', N'Kasse og bankinnskudd', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'211', N'Ihendehaverobligasjoner', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'212', N'Sertifikater ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'213', N'Kortsiktige fordringer ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'214', N'Konserninterne kortsiktige fordringer ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'215', N'Derivater ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'218', N'Aksjer og andeler ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'219', N'Premieavvik ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'220', N'Pensjonsmidler ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'221', N'Aksjer og andeler ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'222', N'Utlån ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'223', N'Konserninterne langsiktige fordringer ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'224', N'Utstyr, maskiner og transportmidler ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'227', N'Faste eiendommer og anlegg ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'231', N'Kassekredittlån', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'232', N'Annen kortsiktig gjeld ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'233', N'Konsernintern kortsiktig gjeld', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'234', N'Derivater ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'239', N'Premieavvik ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'240', N'Pensjonsforpliktelse ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'241', N'Ihendehaverobligasjoner', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'243', N'Sertifikatlån', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'245', N'Andre lån/Annen langsiktig gjeld', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'247', N'Konsernintern langsiktig gjeld', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'251', N'Bundne driftsfond', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'253', N'Ubundne investeringsfond ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'255', N'Bundne investeringsfond ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'256', N'Disposisjonsfond ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'2580', N'Endring i regnskapsprinsipp som påvirker AK (investering)', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'2581', N'Endring i regnskapsprinsipp som påvirker AK (drift)', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'25900', N'Regnskapsmessig merforbruk ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'25950', N'Regnskapsmessig mindreforbruk ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'25960', N'Udisponert i investeringsregnskapet ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'25970', N'Udekket i investeringsregnskapet ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'25990', N'Kapitalkonto ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'29100', N'Memoriakonto ubrukte lånemidler', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'29110', N'Memoriakonto ubrukte konserninterne lånemidler', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'29200', N'Andre memoriakonti', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO
INSERT [dbo].[gco_kostra_accounts] ([pk_kostra_account_code], [display_name], [description], [type], [income_flag], [updated], [updated_by]) VALUES (N'29999', N'Motkonto for memoriakonti ', N' ', N'balance', 0, CAST(0x0000A4680099D045 AS DateTime), 1016)
GO