
SET IDENTITY_INSERT [dbo].[gmd_area_age_intervals] ON 

GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (1, N'01', N'6-15 år', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (2, N'01', N'6-15 år', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (3, N'01', N'6-15 år', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (4, N'02', N'67 år +', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (5, N'02', N'67 år +', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (6, N'02', N'67 år +', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (7, N'03', N'0-17 år', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (8, N'03', N'0-17 år', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (9, N'03', N'0-17 år', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (10, N'03', N'0-17 år', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (11, N'03', N'0-17 år', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (12, N'03', N'0-17 år', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (13, N'03', N'0-17 år', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (14, N'04', N'1-5 år', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (15, N'04', N'1-5 år', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (16, N'05', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (17, N'05', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (18, N'05', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (19, N'05', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (20, N'05', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (21, N'05', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (22, N'05', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (23, N'05', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (24, N'05', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (25, N'05', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (26, N'05', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (27, N'05', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (28, N'05', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (29, N'06', N'20 - 66 år', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (30, N'06', N'20 - 66 år', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (31, N'07', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (32, N'07', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (33, N'07', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (34, N'07', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (35, N'07', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (36, N'07', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (37, N'07', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (38, N'07', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (39, N'07', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (40, N'07', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (41, N'07', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (42, N'07', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (43, N'07', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (44, N'08', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (45, N'08', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (46, N'08', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (47, N'08', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (48, N'08', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (49, N'08', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (50, N'08', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (51, N'08', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (52, N'08', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (53, N'08', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (54, N'08', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (55, N'08', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (56, N'08', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (57, N'09', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (58, N'09', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (59, N'09', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (60, N'09', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (61, N'09', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (62, N'09', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (63, N'09', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (64, N'09', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (65, N'09', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (66, N'09', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (67, N'09', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (68, N'09', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (69, N'09', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (70, N'10', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (71, N'10', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (72, N'10', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (73, N'10', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (74, N'10', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (75, N'10', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (76, N'10', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (77, N'10', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (78, N'10', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (79, N'10', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (80, N'10', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (81, N'10', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (82, N'10', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (83, N'11', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (84, N'11', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (85, N'11', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (86, N'11', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (87, N'11', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (88, N'11', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (89, N'11', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (90, N'11', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (91, N'11', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (92, N'11', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (93, N'11', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (94, N'11', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (95, N'11', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (96, N'12', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (97, N'12', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (98, N'12', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (99, N'12', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (100, N'12', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (101, N'12', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (102, N'12', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (103, N'12', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (104, N'12', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (105, N'12', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (106, N'12', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (107, N'12', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (108, N'12', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (109, N'13', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (110, N'13', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (111, N'13', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (112, N'13', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (113, N'13', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (114, N'13', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (115, N'13', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (116, N'13', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (117, N'13', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (118, N'13', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (119, N'13', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (120, N'13', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (121, N'13', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (122, N'14', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (123, N'14', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (124, N'14', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (125, N'14', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (126, N'14', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (127, N'14', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (128, N'14', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (129, N'14', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (130, N'14', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (131, N'14', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (132, N'14', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (133, N'14', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (134, N'14', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (135, N'15', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (136, N'15', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (137, N'15', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (138, N'15', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (139, N'15', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (140, N'15', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (141, N'15', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (142, N'15', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (143, N'15', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (144, N'15', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (145, N'15', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (146, N'15', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (147, N'15', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (148, N'16', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (149, N'16', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (150, N'16', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (151, N'16', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (152, N'16', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (153, N'16', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (154, N'16', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (155, N'16', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (156, N'16', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (157, N'16', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (158, N'16', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (159, N'16', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (160, N'16', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (161, N'17', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (162, N'17', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (163, N'17', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (164, N'17', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (165, N'17', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (166, N'17', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (167, N'17', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (168, N'17', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (169, N'17', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (170, N'17', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (171, N'17', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (172, N'17', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (173, N'17', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (174, N'18', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (175, N'18', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (176, N'18', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (177, N'18', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (178, N'18', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (179, N'18', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (180, N'18', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (181, N'18', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (182, N'18', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (183, N'18', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (184, N'18', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (185, N'18', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (186, N'18', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (187, N'19', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (188, N'19', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (189, N'19', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (190, N'19', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (191, N'19', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (192, N'19', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (193, N'19', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (194, N'19', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (195, N'19', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (196, N'19', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (197, N'19', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (198, N'19', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (199, N'19', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (200, N'20', N'Alle', N'0 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (201, N'20', N'Alle', N'1-2 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (202, N'20', N'Alle', N'3-5 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (203, N'20', N'Alle', N'6-9 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (204, N'20', N'Alle', N'10-12 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (205, N'20', N'Alle', N'13-15 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (206, N'20', N'Alle', N'16-17 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (207, N'20', N'Alle', N'18-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (208, N'20', N'Alle', N'20-44 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (209, N'20', N'Alle', N'45-66 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (210, N'20', N'Alle', N'67-79 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (211, N'20', N'Alle', N'80-89 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (212, N'20', N'Alle', N'90 år eller eldre', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
INSERT [dbo].[gmd_area_age_intervals] ([pk_id], [fk_report_area_code], [age_group], [age_interval], [updated], [updated_by]) VALUES (213, N'21', N'16 - 19 år', N'16-19 år', CAST(0x0000A44A00000000 AS DateTime), 2016)
GO
SET IDENTITY_INSERT [dbo].[gmd_area_age_intervals] OFF
GO
