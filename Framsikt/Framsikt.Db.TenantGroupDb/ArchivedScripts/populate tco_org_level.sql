
SET IDENTITY_INSERT [dbo].[tco_org_level] ON 

GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (1, 1, N'2012v1', 2, N'Tjenesteområde', 1, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (2, 1, N'2012v1', 3, N'Ansvar', 0, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (3, 2, N'2010v1', 2, N'Programområde', 1, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (4, 2, N'2010v1', 3, N'Virksomhet', 0, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (5, 2, N'2010v1', 4, N'Ansvar', 0, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (6, 2, N'2010v1', 1, N'Drammen kommune', 0, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
INSERT [dbo].[tco_org_level] ([pk_id], [fk_tenant_id], [fk_org_version], [org_level], [level_name], [fp_flag], [updated], [updated_by]) VALUES (7, 1, N'2012v1', 1, N'Steinkjer kommune', 0, CAST(0x0000A42C00000000 AS DateTime), 1)
GO
SET IDENTITY_INSERT [dbo].[tco_org_level] OFF
GO