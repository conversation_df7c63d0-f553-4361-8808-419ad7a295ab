
SET IDENTITY_INSERT [dbo].[gko_slider_values] ON 

GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (1, N'ko_revenue', N'KCF011', CAST(-10.0 AS Decimal(3, 1)), CAST(25.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(130000.00 AS Decimal(18, 2)), 1, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (2, N'total_population', N'KCF014', CAST(-50.0 AS Decimal(3, 1)), CAST(50.0 AS Decimal(3, 1)), CAST(200.00 AS Decimal(18, 2)), CAST(650000.00 AS Decimal(18, 2)), 3, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (3, N'area', N'KCF005', CAST(-99.0 AS Decimal(3, 1)), CAST(99.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(10000.00 AS Decimal(18, 2)), 4, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (4, N'pop_density', N'KCF012', CAST(-10.0 AS Decimal(3, 1)), CAST(10.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(2200.00 AS Decimal(18, 2)), 5, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (5, N'popgrowth_perc_3years', N'KCF013', CAST(-25.0 AS Decimal(3, 1)), CAST(25.0 AS Decimal(3, 1)), CAST(-15.00 AS Decimal(18, 2)), CAST(15.00 AS Decimal(18, 2)), 6, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (6, N'0to5_pct', N'KCF001', CAST(-20.0 AS Decimal(3, 1)), CAST(20.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(10.00 AS Decimal(18, 2)), 7, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (7, N'6to15_pct', N'KCF002', CAST(-20.0 AS Decimal(3, 1)), CAST(20.0 AS Decimal(3, 1)), CAST(5.00 AS Decimal(18, 2)), CAST(20.00 AS Decimal(18, 2)), 8, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (8, N'80to89_pct', N'KCF003', CAST(-20.0 AS Decimal(3, 1)), CAST(20.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(15.00 AS Decimal(18, 2)), 9, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (9, N'90_older_pct', N'KCF004', CAST(-20.0 AS Decimal(3, 1)), CAST(20.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), 10, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (10, N'cr_total', N'KCF010', CAST(0.0 AS Decimal(3, 1)), CAST(0.0 AS Decimal(3, 1)), CAST(1.00 AS Decimal(18, 2)), CAST(440.00 AS Decimal(18, 2)), 11, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (11, N'cr_kindergarten', N'KCF008', CAST(0.0 AS Decimal(3, 1)), CAST(0.0 AS Decimal(3, 1)), CAST(1.00 AS Decimal(18, 2)), CAST(440.00 AS Decimal(18, 2)), 12, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (12, N'cr_care', N'KCF006', CAST(0.0 AS Decimal(3, 1)), CAST(0.0 AS Decimal(3, 1)), CAST(1.00 AS Decimal(18, 2)), CAST(440.00 AS Decimal(18, 2)), 13, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (13, N'cr_child_care', N'KCF007', CAST(0.0 AS Decimal(3, 1)), CAST(0.0 AS Decimal(3, 1)), CAST(1.00 AS Decimal(18, 2)), CAST(440.00 AS Decimal(18, 2)), 14, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (14, N'cr_school', N'KCF009', CAST(0.0 AS Decimal(3, 1)), CAST(0.0 AS Decimal(3, 1)), CAST(1.00 AS Decimal(18, 2)), CAST(440.00 AS Decimal(18, 2)), 15, CAST(0x0000A42800000000 AS DateTime), 1)
GO
INSERT [dbo].[gko_slider_values] ([pk_id], [slider], [title], [min_pct_slider], [max_pct_slider], [min_value], [max_value], [display_order], [updated], [updated_by]) VALUES (15, N'expence_adjusted_value', N'KCF015', CAST(-50.0 AS Decimal(3, 1)), CAST(50.0 AS Decimal(3, 1)), CAST(0.00 AS Decimal(18, 2)), CAST(280000.00 AS Decimal(18, 2)), 2, CAST(0x0000A42800000000 AS DateTime), 1)
GO
SET IDENTITY_INSERT [dbo].[gko_slider_values] OFF
GO
