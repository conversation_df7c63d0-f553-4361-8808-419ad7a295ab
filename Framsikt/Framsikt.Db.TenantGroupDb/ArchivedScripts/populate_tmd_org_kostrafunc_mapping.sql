SET IDENTITY_INSERT [dbo].[tmd_org_kostrafunc_mapping] ON 

GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (86, 2, N'201', N'01', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (88, 2, N'211', N'01', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (91, 2, N'221', N'01', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (94, 2, N'231', N'02', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (126, 2, N'338', N'04', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (127, 2, N'339', N'04', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (95, 2, N'232', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (96, 2, N'233', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (97, 2, N'234', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (98, 2, N'241', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (99, 2, N'242', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (100, 2, N'243', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (101, 2, N'244', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (102, 2, N'251', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (103, 2, N'252', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (104, 2, N'253', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (105, 2, N'254', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (106, 2, N'255', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (107, 2, N'256', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (108, 2, N'261', N'05', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (136, 2, N'365', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (137, 2, N'370', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (138, 2, N'373', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (139, 2, N'375', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (140, 2, N'377', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (141, 2, N'380', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (142, 2, N'381', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (143, 2, N'383', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (144, 2, N'385', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (145, 2, N'386', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (146, 2, N'390', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (147, 2, N'392', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (148, 2, N'393', N'06', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (78, 2, N'100', N'07', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (135, 2, N'360', N'08', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (1, 1, N'100', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (2, 1, N'110', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (115, 2, N'301', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (116, 2, N'302', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (117, 2, N'303', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (118, 2, N'304', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (119, 2, N'305', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (120, 2, N'315', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (121, 2, N'320', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (122, 2, N'325', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (123, 2, N'329', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (124, 2, N'330', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (125, 2, N'332', N'11', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (87, 2, N'202', N'12', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (89, 2, N'213', N'12', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (90, 2, N'215', N'12', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (92, 2, N'222', N'12', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (93, 2, N'223', N'12', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (109, 2, N'265', N'13', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (110, 2, N'273', N'13', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (111, 2, N'275', N'13', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (112, 2, N'276', N'13', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (113, 2, N'281', N'13', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (114, 2, N'283', N'13', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (128, 2, N'340', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (129, 2, N'345', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (130, 2, N'350', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (131, 2, N'353', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (132, 2, N'354', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (133, 2, N'355', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (134, 2, N'357', N'14', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (79, 2, N'110', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (80, 2, N'120', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (81, 2, N'121', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (82, 2, N'130', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (83, 2, N'170', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (84, 2, N'171', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (85, 2, N'180', N'15', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (149, 2, N'800', N'18', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (150, 2, N'840', N'18', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (151, 2, N'860', N'18', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (152, 2, N'870', N'18', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (153, 2, N'880', N'18', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (154, 2, N'899', N'19', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (9, 1, N'201', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (10, 1, N'202', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (11, 1, N'211', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (12, 1, N'213', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (13, 1, N'215', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (14, 1, N'221', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (15, 1, N'222', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (16, 1, N'223', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (17, 1, N'231', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (18, 1, N'232', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (19, 1, N'233', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (20, 1, N'234', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (21, 1, N'241', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (22, 1, N'242', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (23, 1, N'243', N'21', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (24, 1, N'244', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (25, 1, N'251', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (26, 1, N'252', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (32, 1, N'265', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (33, 1, N'273', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (34, 1, N'275', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (35, 1, N'276', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (36, 1, N'281', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (37, 1, N'283', N'33', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (4, 1, N'121', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (38, 1, N'301', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (39, 1, N'302', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (40, 1, N'303', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (41, 1, N'304', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (42, 1, N'305', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (43, 1, N'315', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (44, 1, N'320', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (45, 1, N'325', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (46, 1, N'329', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (47, 1, N'330', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (48, 1, N'332', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (49, 1, N'338', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (50, 1, N'339', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (51, 1, N'340', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (52, 1, N'345', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (53, 1, N'350', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (54, 1, N'353', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (55, 1, N'354', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (56, 1, N'355', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (57, 1, N'357', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (58, 1, N'360', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (59, 1, N'365', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (60, 1, N'370', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (61, 1, N'373', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (62, 1, N'375', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (63, 1, N'377', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (64, 1, N'380', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (65, 1, N'381', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (66, 1, N'383', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (67, 1, N'385', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (68, 1, N'386', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (69, 1, N'390', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (70, 1, N'392', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (71, 1, N'393', N'45', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (27, 1, N'253', N'51', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (28, 1, N'254', N'51', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (29, 1, N'255', N'51', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (30, 1, N'256', N'51', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (31, 1, N'261', N'51', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (3, 1, N'120', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (5, 1, N'130', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (6, 1, N'170', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (7, 1, N'171', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (8, 1, N'180', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (72, 1, N'800', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (73, 1, N'840', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (74, 1, N'860', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (75, 1, N'870', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (76, 1, N'880', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
INSERT [dbo].[tmd_org_kostrafunc_mapping] ([pk_id], [fk_tenant_id], [fk_kostra_function_code], [fk_org_id], [active], [date_from], [date_to], [updated], [updated_by]) VALUES (77, 1, N'899', N'81', 1, CAST(0x5B950A00 AS Date), CAST(0xC1B61000 AS Date), CAST(0x0000A3B800000000 AS DateTime), N'oyvind')
GO
SET IDENTITY_INSERT [dbo].[tmd_org_kostrafunc_mapping] OFF
GO
