-- Step 1 Create MASTER KEY for Security
CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'YvM&$%{KB(0bm"G';  

-- Step 2 Create Database Scoped Credential
CREATE DATABASE SCOPED CREDENTIAL FramsiktDevCredentials   
WITH IDENTITY = 'framsiktdevadmin',  
SECRET = 'Dev_a723H7KA3T';

--Step 3 Creating External DataSource for reference
CREATE EXTERNAL DATA SOURCE APPDBDATASOURCE with
(
    TYPE = RDBMS,
    LOCATION = 'ig873o3x7z.database.windows.net',
    DATABASE_NAME = 'SQL-DEV-APP',
    CREDENTIAL = FramsiktDevCredentials
)

--Step 4 Creating External Table
CREATE EXTERNAL TABLE [dbo].[tco_users](
[pk_id] [int] NOT NULL,
[user_name] [nvarchar](100) NOT NULL,
[language_preference] [nvarchar](10) NULL,
[first_name] [nvarchar](50) NULL,
[middle_name] [nvarchar](50) NULL,
[last_name] [nvarchar](50) NULL,
[profile_image] [nvarchar](200) NULL,
[valid_till] [datetime] NULL, 
IsActive  bit)
WITH
(
	DATA_SOURCE = APPDBDATASOURCE
);