
function RunSQL($path)
{
    $SQLCommandText = @(Get-Content -Encoding UTF8 -Path $path) 
    foreach($SQLString in  $SQLCommandText) 
    { 
        if($SQLString -ne "go") 
        { 
            #Preparation of SQL packet 
            $SQLPacket += $SQLString + "`n" 
        } 
        else 
        { 
            Write-Host "---------------------------------------------" 
            Write-Host "Executed SQL packet:" 
            Write-Host $SQLPacket 
            $IsSQLErr = $false 
            #Execution of SQL packet 
            try 
            { 
                $SQLCommand = New-Object System.Data.SqlClient.SqlCommand($SQLPacket, $SQLConnection) 
                $SQLCommand.ExecuteScalar() 
            } 
            catch 
            { 
 
                $IsSQLErr = $true 
                Write-Host $Error[0] -ForegroundColor Red 
                $SQLPacket | Out-File -FilePath ($PWD.Path + "\SQLErrors.txt") -Append 
                $Error[0] | Out-File -FilePath ($PWD.Path + "\SQLErrors.txt") -Append 
                "----------" | Out-File -FilePath ($PWD.Path + "\SQLErrors.txt") -Append 
				$ErString = ("Error while executing the script - " + $path)
				Throw $ErString
            } 
            if(-not $IsSQLErr) 
            { 
                Write-Host "Execution successful" 
            } 
            else 
            { 
                Write-Host "Execution failed"  -ForegroundColor Red 
            } 
            $SQLPacket = "" 
        } 
    } 

}

If (Test-Path "..\..\..\Framsikt_Metadata")
{
	Remove-Item "..\..\..\Framsikt_Metadata" -Recurse -Force
}
git clone --quiet https://rajeshe:<EMAIL>/DefaultCollection/Framsikt%20Product%20Development/_git/Framsikt_Metadata "..\..\..\Framsikt_Metadata"

#Connect to Tenant Database 
try 
{ 
    $SQLConnection = New-Object System.Data.SqlClient.SqlConnection 
    #The MS SQL Server user and password is specified 
       $SQLConnection.ConnectionString = "Server=ig873o3x7z.database.windows.net;Database=SQL-DEV-T1;User ID=framsiktdevadmin;Password=**************;" 

    $SQLConnection.Open() 
} 
#Error of connection 
catch 
{ 
    Write-Host $Error[0] -ForegroundColor Red 
	throw "Error while executing the script"
    exit 1 
} 

RunSQL -path "..\..\..\Framsikt_Metadata\Framsikt_Metadata\TenantDb\populate_gco_language_strings.sql"

#Disconnection from MS SQL Server 
$SQLConnection.Close() 
Write-Host "-----------------------------------------" 
Write-Host $file "execution done"

#Connect to App Database 
try 
{ 
    $SQLConnection = New-Object System.Data.SqlClient.SqlConnection 
    #The MS SQL Server user and password is specified 
       $SQLConnection.ConnectionString = "Server=ig873o3x7z.database.windows.net;Database=SQL-DEV-APP;User ID=framsiktdevadmin;Password=**************;" 

    $SQLConnection.Open() 
} 
#Error of connection 
catch 
{ 
    Write-Host $Error[0] -ForegroundColor Red 
	throw "Error while executing the script"
    exit 1 
} 

RunSQL -path "..\..\..\Framsikt_Metadata\Framsikt_Metadata\AppDb\populate_gco_language_strings.sql"
RunSQL -path "..\..\..\Framsikt_Metadata\Framsikt_Metadata\AppDb\populate_gco_application_settings.sql"
RunSQL -path "..\..\..\Framsikt_Metadata\Framsikt_Metadata\AppDb\populate_gmd_auth_activities.sql"
RunSQL -path "..\..\..\Framsikt_Metadata\Framsikt_Metadata\AppDb\populate_gmd_auth_activity_role_mapping.sql"

#Disconnection from MS SQL Server 
$SQLConnection.Close() 
Write-Host "-----------------------------------------" 
Write-Host $file "execution done"
