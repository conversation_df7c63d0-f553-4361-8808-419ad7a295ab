<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <SonarQubeExclude>true</SonarQubeExclude>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>Framsikt.Db.TenantGroupDb</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{deb1b0ff-ed1a-4f96-b528-b02312a46d0c}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureDatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath />
    <RootNamespace>Framsikt.Db.TenantGroupDb</RootNamespace>
    <AssemblyName>Framsikt.Db.TenantGroupDb</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <AnsiNulls>False</AnsiNulls>
    <QuotedIdentifier>False</QuotedIdentifier>
    <IsChangeTrackingOn>False</IsChangeTrackingOn>
    <IsChangeTrackingAutoCleanupOn>True</IsChangeTrackingAutoCleanupOn>
    <ChangeTrackingRetentionPeriod>2</ChangeTrackingRetentionPeriod>
    <ChangeTrackingRetentionUnit>Days</ChangeTrackingRetentionUnit>
    <IsEncryptionOn>False</IsEncryptionOn>
    <IsBrokerPriorityHonored>False</IsBrokerPriorityHonored>
    <Trustworthy>False</Trustworthy>
    <AutoUpdateStatisticsAsynchronously>False</AutoUpdateStatisticsAsynchronously>
    <PageVerify>CHECKSUM</PageVerify>
    <ServiceBrokerOption>DisableBroker</ServiceBrokerOption>
    <DateCorrelationOptimizationOn>False</DateCorrelationOptimizationOn>
    <Parameterization>SIMPLE</Parameterization>
    <AllowSnapshotIsolation>True</AllowSnapshotIsolation>
    <ReadCommittedSnapshot>True</ReadCommittedSnapshot>
    <VardecimalStorageFormatOn>True</VardecimalStorageFormatOn>
    <SupplementalLoggingOn>False</SupplementalLoggingOn>
    <CompatibilityMode>100</CompatibilityMode>
    <AnsiNullDefault>False</AnsiNullDefault>
    <AnsiPadding>False</AnsiPadding>
    <AnsiWarnings>False</AnsiWarnings>
    <ArithAbort>False</ArithAbort>
    <ConcatNullYieldsNull>False</ConcatNullYieldsNull>
    <NumericRoundAbort>False</NumericRoundAbort>
    <RecursiveTriggersEnabled>False</RecursiveTriggersEnabled>
    <DatabaseChaining>False</DatabaseChaining>
    <DatabaseState>ONLINE</DatabaseState>
    <CloseCursorOnCommitEnabled>False</CloseCursorOnCommitEnabled>
    <DefaultCursor>GLOBAL</DefaultCursor>
    <AutoClose>False</AutoClose>
    <AutoCreateStatistics>True</AutoCreateStatistics>
    <AutoShrink>False</AutoShrink>
    <AutoUpdateStatistics>True</AutoUpdateStatistics>
    <TornPageDetection>False</TornPageDetection>
    <DatabaseAccess>MULTI_USER</DatabaseAccess>
    <Recovery>FULL</Recovery>
    <EnableFullTextSearch>False</EnableFullTextSearch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">10.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="Security\" />
    <Folder Include="Scripts\" />
    <Folder Include="Snapshots" />
    <Folder Include="Scripts\TestData" />
    <Folder Include="Scripts\DataConversion" />
    <Folder Include="PowershellScript" />
    <Folder Include="dbo\Stored Procedures" />
    <Folder Include="dbo\User Defined Types" />
    <Folder Include="Scripts\PreDeployment" />
    <Folder Include="dbo\Function" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\gco_kostra_accounts.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gco_municipalities.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gco_tenants.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_action_types.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_reporting_line.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tco_accounts.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tco_org_level.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tfp_action_details.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tfp_actions.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tfp_trans_detail.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tfp_trans_header.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tmd_indicator_mapping.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gco_pop_forecast.sql" />
    <Build Include="dbo\Tables\gco_region_codes.sql" />
    <Build Include="dbo\Tables\gmd_age_intervals_mapping.sql" />
    <Build Include="dbo\Tables\gmd_assessment_areas.sql" />
    <Build Include="dbo\Tables\gmd_forecast_type.sql" />
    <Build Include="dbo\Tables\gmd_kostra_function.sql" />
    <Build Include="dbo\Tables\tfp_accounting_data.sql" />
    <Build Include="dbo\Tables\tfp_action_defaults.sql" />
    <Build Include="dbo\Tables\tfp_budget_data.sql" />
    <Build Include="dbo\Tables\tfp_temp_table.sql" />
    <Build Include="dbo\Tables\tmd_demographic_mapping.sql" />
    <Build Include="dbo\Tables\tsa_assessment_actions.sql" />
    <Build Include="dbo\Tables\tsa_assessment_delegates_mapping.sql" />
    <Build Include="dbo\Tables\tsa_assessments.sql" />
    <Build Include="dbo\Tables\tsa_assessment_service_area_mapping.sql" />
    <Build Include="dbo\Views\vwGcoPopForecast.sql" />
    <Build Include="dbo\Tables\tco_discussions.sql" />
    <Build Include="dbo\Tables\tco_discussion_responses.sql" />
    <Build Include="dbo\Tables\tsa_assessment_area_mapping.sql" />
    <Build Include="dbo\Tables\tco_discussion_boards.sql" />
    <Build Include="dbo\Tables\tmd_forecast_type.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tko_ind_explanation.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tko_compare_mapping.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_reporting_areas.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_pop_indicator_intervals.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_kostra_lables.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_indicator_defaults.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gko_population_indicator.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gko_kpi.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gmd_discussion_board_types.sql" />
    <Build Include="dbo\Tables\gko_kostra_data.sql" />
    <Build Include="dbo\Views\vw_gmd_comparison_filter.sql" />
    <Build Include="dbo\Tables\tmd_pop_ind_mapping.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gco_language_strings.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vwModelPopulationIndicatorMapping.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\gko_slider_values.sql" />
    <Build Include="dbo\Views\vw_gko_filter_defaults.sql" />
    <Build Include="dbo\Views\vwModelRegionCodesDetails.sql" />
    <Build Include="dbo\Tables\gko_main_indicators.sql" />
    <Build Include="dbo\Tables\tco_functions.sql" />
    <Build Include="dbo\Tables\gko_indicator_text.sql" />
    <Build Include="dbo\Views\vwKostraRegionDetail.sql" />
    <Build Include="dbo\Views\vwKostraOwnDetails.sql" />
    <Build Include="dbo\Views\vwForecastTypes.sql" />
    <Build Include="dbo\Views\vwModelKpiData.sql" />
    <Build Include="dbo\Tables\gko_kostra_data_histr.sql" />
    <Build Include="dbo\Tables\tko_indicator_target.sql" />
    <Build Include="dbo\Tables\gko_indicator_target.sql" />
    <Build Include="dbo\Tables\gko_kostra_data_corp.sql" />
    <Build Include="dbo\Tables\gmd_expence_need.sql" />
    <Build Include="dbo\Views\vwKostraData.sql" />
    <Build Include="dbo\Tables\gmd_kostra_corp_indicators.sql" />
    <Build Include="dbo\Tables\tco_projects.sql" />
    <Build Include="dbo\Tables\tco_investments.sql" />
    <Build Include="dbo\Tables\tco_investment_detail.sql" />
    <Build Include="dbo\Tables\tfp_inv_transactions.sql" />
    <Build Include="dbo\Tables\tco_investment_approvers.sql" />
    <Build Include="dbo\Tables\tco_org_version.sql" />
    <Build Include="dbo\Tables\tco_doc_export.sql" />
    <Build Include="dbo\Tables\tco_notifications.sql" />
    <Build Include="dbo\Tables\gko_cost_reduction_data.sql" />
    <Build Include="dbo\Tables\gko_map_data_set.sql" />
    <Build Include="dbo\Tables\gmd_area_age_intervals.sql" />
    <Build Include="dbo\Tables\gko_kostra_data_corp_histr.sql" />
    <Build Include="dbo\Tables\gko_bestin_kostra.sql" />
    <Build Include="dbo\Tables\tco_parameters.sql" />
    <Build Include="dbo\Tables\tsa_participants.sql" />
    <Build Include="dbo\Tables\tsa_assessment_action_details.sql" />
    <Build Include="dbo\Tables\gmd_assumption_types.sql" />
    <Build Include="dbo\Tables\tfp_assumption_accounts.sql" />
    <Build Include="dbo\Tables\tfp_assumption_rate_history.sql" />
    <Build Include="dbo\Tables\tco_parameters.sql" />
    <Build Include="dbo\Tables\tsa_participants.sql" />
    <Build Include="dbo\Tables\tsa_assessment_action_details.sql" />
    <Build Include="dbo\Tables\gmd_fp_demographic_intervals.sql" />
    <Build Include="dbo\Tables\tmd_org_kostrafunc_mapping_1.sql" />
    <Build Include="dbo\Tables\tco_doc_versions.sql" />
    <Build Include="dbo\Tables\tco_expl_histr.sql" />
    <Build Include="dbo\Tables\tmd_acc_defaults.sql" />
    <Build Include="dbo\Tables\tco_budget_totals.sql" />
    <Build Include="dbo\Tables\tbm_budget_meeting.sql" />
    <Build Include="dbo\Tables\gmd_kpi_setup.sql" />
    <Build Include="dbo\Tables\tmd_kpi_setup.sql" />
    <Build Include="dbo\Tables\tco_action_type_details.sql" />
    <Build Include="dbo\Tables\tfp_temp_header.sql" />
    <Build Include="dbo\Tables\tfp_temp_detail.sql" />
    <Build Include="dbo\Tables\tco_user_orgrole.sql" />
    <Build Include="dbo\Tables\tbm_budget_task.sql" />
    <Build Include="dbo\Tables\tbm_budtask_sa_mapping.sql" />
    <Build Include="dbo\Tables\tbm_budtask_invited.sql" />
    <Build Include="dbo\Tables\tfp_budget_limits.sql" />
    <Build Include="dbo\Tables\tfp_inv_header.sql" />
    <Build Include="dbo\Tables\tfp_delete_header.sql" />
    <Build Include="dbo\Tables\tfp_delete_detail.sql" />
    <Build Include="dbo\Tables\tbu_trans_detail.sql" />
    <Build Include="dbo\Tables\tco_doc_format.sql" />
    <Build Include="dbo\Tables\tmd_pension_type.sql" />
    <Build Include="dbo\Tables\tco_resources.sql" />
    <Build Include="dbo\Tables\tbu_employments.sql" />
    <Build Include="dbo\Tables\tbu_employments_add_on.sql" />
    <Build Include="dbo\Tables\tco_positions.sql" />
    <Build Include="dbo\Tables\gmd_emp_tax_rates.sql" />
    <Build Include="dbo\Tables\gmd_emp_type.sql" />
    <Build Include="dbo\Tables\gmd_ks_position_group.sql" />
    <Build Include="dbo\Tables\gmd_ks_positions.sql" />
    <Build Include="dbo\Tables\tfp_action_log.sql" />
    <Build Include="dbo\Views\vwTkoCompareMapping.sql" />
    <Build Include="dbo\Views\vwGcoRegionCodes.sql" />
    <Build Include="dbo\Views\vwGkoKpi.sql" />
    <Build Include="dbo\Views\vwGkoKostraData.sql" />
    <Build Include="dbo\Views\vwTmdPopIndMapping.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Tables\tmd_free_dim_definition.sql" />
    <Build Include="dbo\Tables\tco_free_dim_values.sql" />
    <Build Include="dbo\Tables\tbu_budget_changes.sql" />
    <Build Include="dbo\Tables\gmd_holiday_type_1.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteFromTbuTransDetail.sql" />
    <Build Include="dbo\User Defined Types\udtEmploymentIDs.sql" />
    <Build Include="dbo\User Defined Types\udtDepartmentCodes.sql" />
    <Build Include="dbo\User Defined Types\udtTbuTransDetailType.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTbuTransDetail.sql" />
    <Build Include="dbo\Tables\tfp_budget_changes.sql" />
    <Build Include="dbo\Tables\tbu_budget_status.sql" />
    <Build Include="dbo\Tables\tbu_trans_detail_original.sql" />
    <Build Include="dbo\Tables\tfp_investment_budget_changes.sql" />
    <Build Include="dbo\Tables\tko_templates.sql" />
    <Build Include="dbo\Tables\tco_section_config.sql" />
    <Build Include="dbo\Tables\gko_main_indicators_kostrafunction.sql" />
    <Build Include="dbo\Tables\tco_document_setup.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateTbuEmployments.sql" />
    <Build Include="dbo\Tables\tbu_employments_original.sql" />
    <Build Include="dbo\Tables\tbu_employments_add_on_original.sql" />
    <Build Include="dbo\Tables\gko_prod_indicators.sql" />
    <Build Include="dbo\User Defined Types\udtTenantIds.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteFromTkoIndExp.sql" />
    <Build Include="dbo\User Defined Types\udtTkoIndExp.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTkoIndExp.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteFromTcoSectionConfig.sql" />
    <Build Include="dbo\User Defined Types\udtITcoSectionConfig.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTcoSectionConfig.sql" />
    <Build Include="dbo\Tables\gmd_tenant_types.sql" />
    <Build Include="dbo\Views\vw_tenant_language_strings.sql" />
    <Build Include="dbo\Tables\gco_language_string_overrides.sql">
      <AnsiNulls>On</AnsiNulls>
      <QuotedIdentifier>On</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vw_gco_language_strings.sql" />
    <Build Include="dbo\User Defined Types\udtTemplateIds.sql" />
    <Build Include="dbo\Tables\tco_user_parameters.sql" />
    <Build Include="dbo\Tables\tco_adjustment_codes.sql" />
    <Build Include="dbo\Tables\tco_budget_phase.sql" />
    <Build Include="dbo\Tables\gco_color_defaults.sql" />
    <Build Include="dbo\User Defined Types\udtTemplateIds.sql" />
    <Build Include="dbo\Tables\gco_color_overrides.sql" />
    <Build Include="dbo\Views\vw_tenant_color_defaults.sql" />
    <Build Include="dbo\Views\vw_gco_colors.sql" />
    <Build Include="dbo\Tables\tco_free_category.sql" />
    <Build Include="dbo\Tables\tko_user_templates.sql" />
    <Build Include="dbo\Tables\tco_category.sql" />
    <Build Include="dbo\Tables\tco_service_values.sql" />
    <Build Include="dbo\Tables\tco_service_level.sql" />
    <Build Include="dbo\Tables\tco_relation_values.sql" />
    <Build Include="dbo\Tables\tco_relation_definition.sql" />
    <Build Include="dbo\Tables\tfp_assumption_exceptions.sql" />
    <Build Include="dbo\Tables\tco_org_hierarchy.sql" />
    <Build Include="dbo\Tables\tco_user_servicerole.sql" />
    <Build Include="dbo\Tables\tbu_forecast_transactions_1.sql" />
    <Build Include="dbo\Tables\tmd_fp_level_defaults.sql" />
    <Build Include="dbo\Tables\tfp_temp_budget_limits.sql" />
    <Build Include="dbo\Tables\gco_language_string_overrides_tenant.sql" />
    <Build Include="dbo\Tables\gko_templates.sql" />
    <Build Include="dbo\Tables\gko_template_definitions.sql" />
    <Build Include="dbo\Tables\twh_temp_finplan_report.sql" />
    <Build Include="dbo\Tables\twh_finplan_report.sql" />
    <Build Include="dbo\Views\vw_twh_finplan_report.sql" />
    <Build Include="dbo\Tables\tco_fp_alter_codes_1.sql" />
    <Build Include="dbo\User Defined Types\udtAccountCodes.sql" />
    <Build Include="dbo\User Defined Types\udtActionIds.sql" />
    <Build Include="dbo\User Defined Types\udtTfpTransDetail.sql" />
    <Build Include="dbo\Stored Procedures\prcBudgetAssumptionDeleteFromTfpTransDetail.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTfpTransDetail.sql" />
    <Build Include="dbo\Tables\tco_fdv_codes.sql" />
    <Build Include="dbo\Tables\tfp_city_goals.sql" />
    <Build Include="dbo\Tables\tfp_effect_target.sql" />
    <Build Include="dbo\Tables\tco_investment_program.sql" />
    <Build Include="dbo\Tables\tbu_temp_employments_add_on.sql" />
    <Build Include="dbo\Tables\tbu_resource_description.sql" />
    <Build Include="dbo\Tables\tmd_fp_level_kostrafunction.sql" />
    <Build Include="dbo\Tables\twh_buddoc_reports.sql" />
    <Build Include="dbo\Views\vw_doc_function_report.sql" />
    <Build Include="dbo\Views\vw_doc_org_report.sql" />
    <Build Include="dbo\Views\vw_doc_accgrp_report.sql" />
    <Build Include="dbo\Views\vw_doc_change_graphs.sql" />
    <Build Include="dbo\Views\vw_doc_service_org_pivot.sql" />
    <Build Include="dbo\Tables\tco_doc_customnode.sql" />
    <Build Include="dbo\Views\vw_doc_2B_inv_program.sql" />
    <Build Include="dbo\Views\vw_doc_investment_overview.sql" />
    <Build Include="dbo\Views\vw_doc_grants.sql" />
    <Build Include="dbo\Tables\tco_inv_program.sql" />
    <Build Include="dbo\Tables\twh_temp_changed_actions_1.sql" />
    <Build Include="dbo\Views\vw_doc_financing_overview.sql" />
    <Build Include="dbo\Views\vw_doc_1B_finplan.sql" />
    <Build Include="dbo\Views\vw_doc_1A_finplan.sql" />
    <Build Include="dbo\Views\vw_doc_sa_budget.sql" />
    <Build Include="dbo\Tables\tco_departments.sql" />
    <Build Include="dbo\Tables\tco_application_flag.sql" />
    <Build Include="dbo\Tables\twh_temp_buddoc_reports.sql" />
    <Build Include="dbo\Tables\tco_publish_link.sql" />
    <Build Include="dbo\Views\vw_twh_investment_report.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteFromTbuForecastTransactions.sql" />
    <Build Include="dbo\User Defined Types\udtTbuForecastTransactionsType.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTbuForecastTransactions.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateTbuEmploymentsForecastChangeFlag.sql" />
    <Build Include="dbo\Tables\tbu_employments_forecast.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteFromTbuEmploymentsForecast.sql" />
    <Build Include="dbo\User Defined Types\udtTbuEmploymentsForecast.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTbuEmploymentForecast.sql" />
    <Build Include="dbo\Tables\tmd_salary_acc_def.sql" />
    <Build Include="dbo\Tables\tco_doc_treeSelection.sql" />
    <Build Include="dbo\Views\vw_doc_org_2_lvls.sql" />
    <Build Include="dbo\Tables\tco_publish_chapter_info.sql" />
    <Build Include="dbo\Tables\twh_budget_report.sql" />
    <Build Include="dbo\Tables\tco_reporting_template.sql" />
    <Build Include="dbo\Tables\tbu_employments_forecast_add_ons.sql" />
    <Build Include="dbo\Views\vw_doc_sa_budget_1.sql" />
    <Build Include="dbo\Tables\tmp_forecast_transid.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeForecast.sql" />
    <Build Include="dbo\Tables\tmd_reporting_line.sql" />
    <Build Include="dbo\Tables\twh_temp_changed_budget.sql" />
    <Build Include="dbo\Tables\twh_temp_triggered_budget.sql" />
    <Build Include="dbo\Tables\twh_temp_triggered_actions.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeBudgetReport.sql" />
    <Build Include="dbo\Tables\tco_monthrep_texts.sql" />
    <Build Include="dbo\Tables\tco_custom_node.sql" />
    <Build Include="dbo\Tables\tco_publish_template.sql" />
    <Build Include="dbo\Tables\twh_report_job_queue_1.sql" />
    <Build Include="dbo\Stored Procedures\procLockOrginalBudget.sql" />
    <Build Include="dbo\Stored Procedures\prcResetAgaPenHolCalc.sql" />
    <Build Include="dbo\Tables\tmr_deviation_report.sql" />
    <Build Include="dbo\Tables\tmr_report_flag.sql" />
    <Build Include="dbo\Tables\tmr_finplan_actions.sql" />
    <Build Include="dbo\Tables\gco_log_page_view.sql" />
    <Build Include="dbo\Tables\gco_log_publish_job.sql" />
    <Build Include="dbo\Tables\tfp_stage_action_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedActions.sql" />
    <Build Include="dbo\Tables\tco_job_status.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateActionTypeForActionImport.sql" />
    <Build Include="dbo\Tables\tmr_absence_details.sql" />
    <Build Include="dbo\Tables\tmr_absence_aggregated.sql" />
    <Build Include="dbo\Tables\tco_periodic_key.sql" />
    <Build Include="dbo\Views\vw_doc_1A_budget.sql" />
    <Build Include="dbo\Views\vw_doc_1B_budget.sql" />
    <Build Include="dbo\Views\vw_doc_2B_budget.sql" />
    <Build Include="dbo\Tables\tmd_finplan_line_setup.sql" />
    <Build Include="dbo\Tables\tbu_staging_employments.sql" />
    <Build Include="dbo\tin_import_batches.sql" />
    <Build Include="dbo\Tables\tfp_stage_newyearactions.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateNewYearActions.sql" />
    <Build Include="dbo\Views\vw_bud_visma.sql" />
    <Build Include="dbo\Views\vw_bud_agresso.sql" />
    <Build Include="dbo\Tables\tfp_erp_export_log_agresso.sql" />
    <Build Include="dbo\Tables\tfp_erp_export_log_visma.sql" />
    <Build Include="dbo\Tables\tfp_erp_exports.sql" />
    <Build Include="dbo\Stored Procedures\procDataToFinPlanTable.sql" />
    <Build Include="dbo\Stored Procedures\procMoveActionsLastYear.sql" />
    <Build Include="dbo\Stored Procedures\procMoveBudgetChanges.sql" />
    <Build Include="dbo\Stored Procedures\procMoveOriginalBudget.sql" />
    <Build Include="dbo\Stored Procedures\procMoveReverseDemo.sql" />
    <Build Include="dbo\Stored Procedures\procMoveRevisedBudget.sql" />
    <Build Include="dbo\Tables\tco_NewYearAction_audit.sql" />
    <Build Include="dbo\Tables\tco_action_tags.sql" />
    <Build Include="dbo\Tables\tmr_data_warehouse.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMonthRepWh.sql" />
    <Build Include="dbo\Tables\tmr_data_warehouse_period.sql" />
    <Build Include="dbo\Tables\tmr_report_status.sql" />
    <Build Include="dbo\User Defined Types\udtBatchType.sql" />
    <Build Include="dbo\Stored Procedures\procDeleteExistingActionData.sql" />
    <Build Include="dbo\Stored Procedures\procLockEmploymentData.sql" />
    <Build Include="dbo\Tables\tco_monthly_report_forecast_tags.sql" />
    <Build Include="dbo\Stored Procedures\prcGetErpDeltaAgresso.sql" />
    <Build Include="dbo\Stored Procedures\prcGetErpDeltaVisma.sql" />
    <Build Include="dbo\Tables\tmr_period_setup.sql" />
    <Build Include="dbo\Tables\tmr_calendar.sql" />
    <Build Include="dbo\Tables\gco_section_config.sql" />
    <Build Include="dbo\Tables\tmr_city_goals_status.sql" />
    <Build Include="dbo\Tables\tmr_effect_target_status.sql" />
    <Build Include="dbo\Views\vw_bud_agresso_original.sql" />
    <Build Include="dbo\Views\vw_bud_visma_original.sql" />
    <Build Include="dbo\Tables\tco_users_settings.sql" />
    <Build Include="dbo\Views\vwUserDetails.sql" />
    <Build Include="dbo\Tables\tmr_investment_status.sql" />
    <Build Include="dbo\Tables\tmr_inv_data_warehouse.sql" />
    <Build Include="dbo\Tables\tco_progress_status.sql" />
    <Build Include="dbo\Tables\tco_blob_text_reference.sql" />
    <Build Include="dbo\Tables\tbu_yearlybudget_description_1.sql" />
    <Build Include="dbo\Stored Procedures\procUpdateBudgetLimit.sql" />
    <Build Include="dbo\User Defined Types\udtTfpTempBudgetLimits.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTfpTempBudgetLimits.sql" />
    <Build Include="dbo\Views\vw_doc_1A_forecast.sql" />
    <Build Include="dbo\Views\vw_doc_1B_forecast.sql" />
    <Build Include="dbo\Views\vw_doc_B3_forecast.sql" />
    <Build Include="dbo\Tables\tco_doc_node_description.sql" />
    <Build Include="dbo\Tables\tfp_stage_investment_action_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedInvestments.sql" />
    <Build Include="dbo\User Defined Types\udtTcoInvestments.sql" />
    <Build Include="dbo\User Defined Types\udtTcoInvestmentDetail.sql" />
    <Build Include="dbo\User Defined Types\udtTfpInvTransactions.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTcoInvestments.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTcoInvestmentDetails.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTfpInvTransactions.sql" />
    <Build Include="dbo\Views\vw_doc_1B_org_forecast.sql" />
    <Build Include="dbo\Tables\gmd_publish_tree_node_definitions.sql" />
    <Build Include="dbo\Tables\tco_main_projects.sql" />
    <Build Include="dbo\User Defined Types\udtTfpTransHeader.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTfpTransHeader.sql" />
    <Build Include="dbo\Tables\tfp_effect_target_detail.sql" />
    <Build Include="dbo\Tables\tco_deviation_types.sql" />
    <Build Include="dbo\Tables\tfp_accounting_data_detail.sql" />
    <Build Include="dbo\Tables\tco_activity_indicator.sql" />
    <Build Include="dbo\Tables\tco_focusarea.sql" />
    <Build Include="dbo\Tables\tfp_vision_ambision.sql" />
    <Build Include="dbo\Tables\twh_forecast_report.sql" />
    <Build Include="dbo\Tables\tco_key_figures.sql" />
    <Build Include="dbo\Tables\tco_investment_codes.sql" />
    <Build Include="dbo\Tables\tco_inv_portfolio.sql" />
    <Build Include="dbo\Tables\tco_insertable_elements.sql" />
    <Build Include="dbo\Tables\tco_users.sql" />
    <Build Include="dbo\Tables\tco_publish_config.sql" />
    <Build Include="dbo\Tables\tmd_hq_tenant_definition.sql" />
    <Build Include="dbo\Tables\tfp_hq_actions_mapping.sql" />
    <Build Include="dbo\Tables\tmd_hq_departments_mapping.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncHQFinplanTransactions.sql" />
    <Build Include="dbo\Tables\thq_temp_changed_actions.sql" />
    <Build Include="dbo\Tables\thq_temp_inprocess_actions.sql" />
    <Build Include="dbo\Tables\twh_temp_triggered_del_actions.sql" />
    <Build Include="dbo\Tables\twh_temp_changed_del_actions.sql" />
    <Build Include="dbo\Views\vw_twh_delete_finplan_report.sql" />
    <Build Include="dbo\Tables\gco_parameter_definitions.sql" />
    <Build Include="dbo\Views\vw_tco_parameters.sql" />
    <Build Include="dbo\Tables\tco_activity_indicator_group.sql" />
    <Build Include="dbo\User Defined Types\udtFinplanLevelValues.sql" />
    <Build Include="dbo\Stored Procedures\procUpdateBudgetLimitsPerService.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateKeyFigures.sql" />
    <Build Include="dbo\Views\vw_twh_budget_totals.sql" />
    <Build Include="dbo\Views\vw_2A_forecast.sql" />
    <Build Include="dbo\Views\vw_doc_2B_forecast.sql" />
    <Build Include="dbo\Views\vw_doc_total_graph.sql" />
    <Build Include="dbo\Views\vw_doc_action_list.sql" />
    <Build Include="dbo\Views\vw_investment_graph.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_agresso.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_visma.sql" />
    <Build Include="dbo\Views\vw_investment_graph_total.sql" />
    <Build Include="dbo\Tables\tco_inv_interest_deduction.sql" />
    <Build Include="dbo\Tables\tbu_stage_budget_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedBudgets.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncHQInvestmentTransactions.sql" />
    <Build Include="dbo\Stored Procedures\prcimportBudgetsFromStaging.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateInvestmentBudgetFromForecast.sql" />
    <Build Include="dbo\Tables\tfp_hq_investment_mapping.sql" />
    <Build Include="dbo\Stored Procedures\prcimportActionBudgetsintoTbuTransDetail.sql" />
    <Build Include="dbo\Views\vw_doc_1B_forecast_details.sql" />
    <Build Include="dbo\Tables\tps_fin_inv_propopsal.sql" />
    <Build Include="dbo\Tables\tps_operations_proposal.sql" />
    <Build Include="dbo\Tables\tps_budget_proposal.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializePoliticalProposal.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation_1A.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation_1B.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation_2A.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation_2B.sql" />
    <Build Include="dbo\Function\getDatafromDW.sql" />
    <Build Include="dbo\Function\getDataforForecast.sql" />
    <Build Include="dbo\Function\getDataforProposedBudgetChanges.sql" />
    <Build Include="dbo\Tables\tmr_doc_ForecastDetails.sql" />
    <Build Include="dbo\Stored Procedures\prcPopulateForecastDetails.sql" />
    <Build Include="dbo\Function\getDeviationData.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeDocReport.sql" />
    <Build Include="dbo\Views\vw_pbi_org_dep_data.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteUpdateExistingBudgets.sql" />
    <Build Include="dbo\Tables\tps_descriptions.sql" />
    <Build Include="dbo\Views\vw_pbi_function_data.sql" />
    <Build Include="dbo\Views\vw_pbi_account_data.sql" />
    <Build Include="dbo\Views\vw_pbi_project_data.sql" />
    <Build Include="dbo\Views\vw_pbi_tmd_reporting_line.sql" />
    <Build Include="dbo\Views\vw_pbi_freedim1.sql" />
    <Build Include="dbo\Views\vw_pbi_freedim2.sql" />
    <Build Include="dbo\Views\vw_pbi_freedim3.sql" />
    <Build Include="dbo\Views\vw_pbi_freedim4.sql" />
    <Build Include="dbo\Views\vw_pbi_budget_data.sql" />
    <Build Include="dbo\Views\vw_pbi_forecast_data.sql" />
    <Build Include="dbo\Views\vw_pbi_accounting_data.sql" />
    <Build Include="dbo\Views\vw_pbi_investment_data.sql" />
    <Build Include="dbo\Tables\tco_investments_descriptions.sql" />
    <Build Include="dbo\Tables\tbi_assignments.sql" />
    <Build Include="dbo\Tables\tbi_assignment_monthly_status.sql" />
    <Build Include="dbo\User Defined Types\udtTcoInvestmentDescriptions.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertIntoTcoInvestmentsDescriptions.sql" />
    <Build Include="dbo\Tables\tbi_assignments_texts.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncHQBudget.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncHQAccountingData.sql" />
    <Build Include="dbo\Tables\tbi_tasks.sql" />
    <Build Include="dbo\Tables\tmr_activity.sql" />
    <Build Include="dbo\Tables\tco_descriptions.sql" />
    <Build Include="dbo\Views\vw_doc_B3_budget.sql" />
    <Build Include="dbo\Tables\tmr_investment_status_detail.sql" />
    <Build Include="dbo\Tables\gpi_date_table.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulationforService_1B.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateChangeFlagTbuTransDetail.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateChangeFlagtbuForecastTransactions.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateChangeFlagToFalseTbuTransDetail.sql" />
    <Build Include="dbo\Tables\tbi_checklist_items.sql" />
    <Build Include="dbo\Tables\tbi_checklist_items_detail.sql" />
    <Build Include="dbo\Tables\tbi_checklist_purpose.sql" />
    <Build Include="dbo\Tables\tbi_checklist_type.sql" />
    <Build Include="dbo\Views\vw_doc_budget_details.sql" />
    <Build Include="dbo\Tables\tfp_target_indicator_detail.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteTfpTransDetailTfpInvFromTbuTransDetail.sql" />
    <Build Include="dbo\Tables\tfp_stage_import_tfp_Trans_detail_and_Inv.sql" />
    <Build Include="dbo\Stored Procedures\prcDeletetTfpStageImportTfpTransDetailAndInv.sql" />
    <Build Include="dbo\Tables\tco_concurrency_tracker.sql" />
    <Build Include="dbo\Views\vw_B4_forecast.sql" />
    <Build Include="dbo\Tables\tco_salary_acc_category.sql" />
    <Build Include="dbo\Tables\tco_pub_frontpage_links.sql" />
    <Build Include="dbo\Views\vw_mr_total_graph_annual.sql" />
    <Build Include="dbo\Views\vw_mr_deviation_graph.sql" />
    <Build Include="dbo\Views\vw_mr_investment_graph_accounting.sql" />
    <Build Include="dbo\Views\vw_accgrp_monthrep.sql" />
    <Build Include="dbo\Views\vw_acc_graphs_dataset.sql" />
    <Build Include="dbo\Tables\tco_inv_budgetyear_config.sql" />
    <Build Include="dbo\Views\vw_mr_cost_rev_graph_sa.sql" />
    <Build Include="dbo\Tables\tbu_staging_employments_add_on.sql" />
    <Build Include="dbo\Views\vw_doc_mr_finstatus.sql" />
    <Build Include="dbo\Tables\tco_key_figures_monthrep.sql" />
    <Build Include="dbo\Stored Procedures\UpdateReportDataBudget.sql" />
    <Build Include="dbo\Views\vw_doc_forecast_finstatus.sql" />
    <Build Include="dbo\Tables\tmr_accounting_warehouse.sql" />
    <Build Include="dbo\Tables\tfp_finplan_warehouse.sql" />
    <Build Include="dbo\Tables\tbu_stage_accounting_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedAccountingData.sql" />
    <Build Include="dbo\Stored Procedures\prcimportAccountingDetailsFromStaging.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeForecastReport.sql" />
    <Build Include="dbo\Stored Procedures\UpdateReportDataForecast.sql" />
    <Build Include="dbo\Tables\twh_temp_triggered_forecast.sql" />
    <Build Include="dbo\Tables\twh_temp_changed_forecast.sql" />
    <Build Include="dbo\Tables\tco_custom_node_master.sql" />
    <Build Include="dbo\Stored Procedures\prcimportActionBudgetsintoTbuTransDetailNextYear.sql" />
    <Build Include="dbo\Tables\gco_periodic_key.sql" />
    <Build Include="dbo\Views\vw_twh_inv_fin_report.sql" />
    <Build Include="dbo\Tables\tmr_stage_investmentforecast_import.sql" />
    <Build Include="dbo\Views\vw_twh_mr_budget_change_report.sql" />
    <Build Include="dbo\Tables\tbu_imported_positions_history.sql" />
    <Build Include="dbo\Tables\tbu_stage_positions.sql" />
    <Build Include="dbo\Views\vw_doc_finplan_cost_rev_graph_sa.sql" />
    <Build Include="dbo\Tables\tbu_trans_detail_cost.sql" />
    <Build Include="dbo\Tables\tbu_employments_cost.sql" />
    <Build Include="dbo\Tables\tbu_employments_add_on_cost.sql" />
    <Build Include="dbo\Stored Procedures\procLockCostEmploymentData.sql" />
    <Build Include="dbo\Stored Procedures\procLockCostBudget.sql" />
    <Build Include="dbo\Stored Procedures\prcStaffplanningInsertImportIdAndUpdateColumns.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncHQFinancing.sql" />
    <Build Include="dbo\Tables\tfp_hq_financing_mapping.sql" />
    <Build Include="dbo\Tables\gmd_treatment_definitions.sql" />
    <Build Include="dbo\Stored Procedures\prcStaffPlanningValidateImportedPositions.sql" />
    <Build Include="dbo\Tables\tmd_emp_type.sql" />
    <Build Include="dbo\Views\vw_doc_investments_blist.sql" />
    <Build Include="dbo\Stored Procedures\procWhatIsWrongWithMySetup.sql" />
    <Build Include="dbo\Tables\tco_error_in_setup.sql" />
    <Build Include="dbo\Tables\tco_investment_phase.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeFinplanDocument.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeFinplanReport.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateFinplanData.sql" />
    <Build Include="dbo\Stored Procedures\procCheckNonAllocated.sql" />
    <Build Include="dbo\Tables\tco_error_in_not_allocated.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_1.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_2.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_3.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_4.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_5.sql" />
    <Build Include="dbo\Tables\gco_doc_table_defs.sql" />
    <Build Include="dbo\Tables\gco_column_defs.sql" />
    <Build Include="dbo\Tables\tco_column_def_overrides.sql" />
    <Build Include="dbo\Views\vw_doc_mr_central_items.sql" />
    <Build Include="dbo\Views\vw_doc_finplan_central_items.sql" />
    <Build Include="dbo\Views\vw_doc_finplan_sa_summary.sql" />
    <Build Include="dbo\Tables\tco_doc_table_def_overrides.sql" />
    <Build Include="dbo\Tables\tco_counters.sql" />
    <Build Include="dbo\Tables\tco_user_adjustment_codes.sql" />
    <Build Include="dbo\Tables\tco_pub_colour_palette.sql" />
    <Build Include="dbo\Tables\tco_pub_colour_palette_overrides.sql" />
    <Build Include="dbo\Stored Procedures\procCheckDataValidation.sql" />
    <Build Include="dbo\Tables\tco_error_data_check.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateKeyFiguresMonthRep.sql" />
    <Build Include="dbo\Tables\tco_pub_colour_palette.sql" />
    <Build Include="dbo\Tables\tpl_strategic_plan.sql" />
    <Build Include="dbo\Tables\tpl_main_plan.sql" />
    <Build Include="dbo\Tables\tpl_theme_plan.sql" />
    <Build Include="dbo\Tables\tco_general_descriptions.sql" />
    <Build Include="dbo\Stored Procedures\prcVerifyPayrollCacl.sql" />
    <Build Include="dbo\Tables\TempVerifyPayroll.sql" />
    <Build Include="dbo\Views\vw_distinct_userDetails.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateMRImportedInvestments.sql" />
    <Build Include="dbo\Tables\tbi_checklist_text_history.sql" />
    <Build Include="dbo\Views\vw_acc_investments.sql" />
    <Build Include="dbo\Views\vw_pub_excel_export_finplan.sql" />
    <Build Include="dbo\Views\vw_pub_excel_export_investment.sql" />
    <Build Include="dbo\Stored Procedures\prcGraphPersTemplateSetup.sql" />
    <Build Include="dbo\Tables\tps_admin_config.sql" />
    <Build Include="dbo\Tables\tps_admin_limitcode_lineitem_config.sql" />
    <Build Include="dbo\Tables\tco_dashboards.sql" />
    <Build Include="dbo\Tables\gco_widgets.sql" />
    <Build Include="dbo\Tables\tco_org_validations.sql" />
    <Build Include="dbo\Tables\gco_org_validations.sql" />
    <Build Include="dbo\Stored Procedures\prcValidationOfOrgData.sql" />
    <Build Include="dbo\Views\vw_acc_screen_budget.sql" />
    <Build Include="dbo\Views\vw_acc_screen_actuals.sql" />
    <Build Include="dbo\Function\getDataforChangedForecast.sql" />
    <Build Include="dbo\Views\vw_mr_total_graph_previous.sql" />
    <Build Include="dbo\Stored Procedures\prc_initialize_BudgetForms.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_1A.sql" />
    <Build Include="dbo\Tables\tmr_absence_age_groups.sql" />
    <Build Include="dbo\Tables\gco_population_history.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_framsikt_1A.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_1B.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_framsikt_1B.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_B3.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_B4.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_2A.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_2B.sql" />
    <Build Include="dbo\Tables\tco_widgets.sql" />
    <Build Include="dbo\Tables\tin_integration_status.sql" />
    <Build Include="dbo\Tables\tco_indicator_setup.sql" />
    <Build Include="dbo\Tables\tmd_indicator_results.sql" />
    <Build Include="dbo\Views\vw_doc_mr_sa_items.sql" />
    <Build Include="dbo\Tables\tco_dashboards_distribution.sql" />
    <Build Include="dbo\Tables\tmd_program_code_setup.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_comment.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_desc.sql" />
    <Build Include="dbo\Tables\tmr_checklist_descriptions.sql" />
    <Build Include="dbo\Tables\gko_employment_statistics.sql" />
    <Build Include="dbo\Tables\tco_prod_publish_history.sql" />
    <Build Include="dbo\Tables\tco_combined_indicator_header.sql" />
    <Build Include="dbo\Tables\tco_combined_indicators_description.sql" />
    <Build Include="dbo\Tables\tco_combined_indicator_detail.sql" />
    <Build Include="dbo\Tables\tco_integrations.sql" />
    <Build Include="dbo\Stored Procedures\PrcValidateImportedActivityIndicators.sql" />
    <Build Include="dbo\Tables\tco_stage_activity_indicators_import.sql" />
    <Build Include="dbo\Stored Procedures\PrcUpdateImportedActivityIndicators.sql" />
    <Build Include="dbo\Stored Procedures\prcDeleteAccountingData.sql" />
    <Build Include="dbo\Tables\gmd_demographic_intervals_ks_export.sql" />
    <Build Include="dbo\Views\vw_doc_mr_accgrp_internal_sa.sql" />
    <Build Include="dbo\Tables\tco_stage_target_indicators_import.sql" />
    <Build Include="dbo\Stored Procedures\PrcUpdateImportedTargetIndicators.sql" />
    <Build Include="dbo\Stored Procedures\PrcValidateImportedTargetIndicators.sql" />
    <Build Include="dbo\Tables\tpl_mr_status_plan.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_58inv.sql" />
    <Build Include="dbo\Tables\tbu_stage_dimensions_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedProjectData.sql" />
    <Build Include="dbo\Tables\tco_integ_parameters.sql" />
    <Build Include="dbo\Tables\tco_assignments_descriptions.sql" />
    <Build Include="dbo\Views\vw_staff_summary.sql" />
    <Build Include="dbo\Views\vw_staff_summary_original.sql" />
    <Build Include="dbo\Views\vw_staff_summary_forecast.sql" />
    <Build Include="dbo\Tables\gco_climate_data.sql" />
    <Build Include="dbo\Tables\gco_climate_source.sql" />
    <Build Include="dbo\Tables\gco_climate_emission_types.sql" />
    <Build Include="dbo\Tables\gco_climate_sector.sql" />
    <Build Include="dbo\Tables\tbu_employments_edit_status.sql" />
    <Build Include="dbo\Tables\tco_fridim_grants_note.sql" />
    <Build Include="dbo\Tables\tco_fridim_grants_desc.sql" />
    <Build Include="dbo\Tables\tco_climate_action_header.sql" />
    <Build Include="dbo\Tables\tco_climate_action_detail.sql" />
    <Build Include="dbo\Tables\tco_media_library.sql" />
    <Build Include="dbo\Tables\tco_media_has_tags.sql" />
    <Build Include="dbo\Tables\tmr_finplan_reporting.sql" />
    <Build Include="tbi_task_monthly_status.sql" />
    <Build Include="dbo\Tables\tco_publish_images.sql" />
    <Build Include="dbo\Views\vw_doc_cost_graphs_sa.sql" />
    <Build Include="dbo\Tables\gco_log_performance.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeForecast_investment.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation1A1B_2020_1.sql" />
    <Build Include="dbo\Tables\tco_interest_rates.sql" />
    <Build Include="dbo\Views\vw_doc_cab_grid.sql" />
    <Build Include="dbo\Stored Procedures\prcVerifyStaffPlanning.sql" />
    <Build Include="dbo\Tables\TempVerifyStaffplanning.sql" />
    <Build Include="dbo\Tables\TempVerifyStaffplanningsummary.sql" />
    <Build Include="dbo\Tables\tps_assignments.sql" />
    <Build Include="dbo\Tables\tbu_employments_tax_rate.sql" />
    <Build Include="dbo\Tables\tps_proposal_sharing.sql" />
    <Build Include="dbo\Tables\gco_un_susdev_goals.sql" />
    <Build Include="dbo\Tables\gco_un_susdev_targets.sql" />
    <Build Include="dbo\Tables\tfp_strategy_text.sql" />
    <Build Include="dbo\Tables\tfp_strategy_goal.sql" />
    <Build Include="dbo\Tables\tco_account_activity.sql" />
    <Build Include="dbo\Tables\twh_budget_report_v2.sql" />
    <Build Include="dbo\Views\vw_twh_budgetreport.sql" />
    <Build Include="dbo\Tables\tco_plan_type.sql" />
    <Build Include="dbo\Tables\tbi_assignment_goal.sql" />
    <Build Include="dbo\Tables\tbi_assignment_target.sql" />
    <Build Include="dbo\Tables\tbi_assignment_strategy.sql" />
    <Build Include="dbo\Stored Procedures\procYBInvestmentLockOrginalBudget.sql" />
    <Build Include="dbo\Tables\tbi_assignment_copy_log.sql" />
    <Build Include="dbo\Tables\tbi_assignment_copy_log.sql" />
    <Build Include="dbo\Tables\tfp_strategy_target.sql" />
    <Build Include="dbo\Tables\tco_focus_area_custom_node_master.sql" />
    <Build Include="dbo\Tables\tsam_period_status.sql" />
    <Build Include="dbo\Tables\tsam_limit_code_setup.sql" />
    <Build Include="dbo\Tables\tsam_base_transactions.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeSAMbase.sql" />
    <Build Include="dbo\Tables\tpl_plan.sql" />
    <Build Include="dbo\Tables\tpl_plan_participants.sql" />
    <Build Include="dbo\Tables\gco_plan_category.sql" />
    <Build Include="dbo\Tables\tpl_plan_template_details.sql" />
    <Build Include="dbo\Tables\gco_plantype_colors.sql" />
    <Build Include="dbo\Tables\tpl_goal.sql" />
    <Build Include="dbo\Tables\tpl_target.sql" />
    <Build Include="dbo\Tables\tpl_target_detail.sql" />
    <Build Include="dbo\Tables\tpl_plan_goal.sql" />
    <Build Include="dbo\Tables\tpl_plan_target.sql" />
    <Build Include="dbo\Tables\tsa_assessment_areas.sql" />
    <Build Include="dbo\Tables\tsa_assessment_delegation.sql" />
    <Build Include="dbo\Tables\tmr_data_warehouse_absence.sql" />
    <Build Include="dbo\Tables\gmd_un_indicator_mapping.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeAbsenceWh.sql" />
    <Build Include="dbo\Stored Procedures\prcAutoAccessOrgRole.sql" />
    <Build Include="dbo\Tables\tpl_strategy.sql" />
    <Build Include="dbo\Tables\tpl_plan_strategy.sql" />
    <Build Include="dbo\Tables\tpl_focusarea.sql" />
    <Build Include="dbo\Tables\tpl_strategy_goal.sql" />
    <Build Include="dbo\Tables\tpl_strategy_target.sql" />
    <Build Include="dbo\Tables\tpl_column_selector.sql" />
    <Build Include="dbo\Tables\tco_auth_user_role_mapping.sql" />
    <Build Include="dbo\Stored Procedures\prcimportBudgetsFromStagingWithPeriodicKeyZero.sql" />
    <Build Include="dbo\Tables\tco_main_project_setup.sql" />
    <Build Include="dbo\Tables\tfp_proj_transactions.sql" />
    <Build Include="dbo\Tables\tco_proj_hierarchy.sql" />
    <Build Include="dbo\Tables\tco_proj_version.sql" />
    <Build Include="dbo\Tables\tco_proj_level.sql" />
    <Build Include="dbo\Tables\tco_proj_level_data.sql" />
    <Build Include="dbo\Tables\tpl_assignments.sql" />
    <Build Include="dbo\Tables\tpl_plan_assignments.sql" />
    <Build Include="dbo\Tables\tpl_assignments_goal.sql" />
    <Build Include="dbo\Tables\tpl_assignments_target.sql" />
    <Build Include="dbo\Tables\tpl_assignments_category.sql" />
    <Build Include="dbo\Tables\tsa_assessment_area_actions.sql" />
    <Build Include="dbo\Tables\tsa_assessment_area_actions_detail.sql" />
    <Build Include="dbo\Tables\tsa_assessment_area_descriptions.sql" />
    <Build Include="dbo\Tables\tpl_actions.sql" />
    <Build Include="dbo\Tables\tpl_plan_actions.sql" />
    <Build Include="dbo\Tables\tpl_actions_detail.sql" />
    <Build Include="dbo\Tables\tsam_period_transactions.sql" />
    <Build Include="dbo\Tables\tpl_plan_object_processing_details.sql" />
    <Build Include="dbo\Tables\tpl_tfp_action_mapping.sql" />
    <Build Include="dbo\Tables\tpl_tfp_goal_mapping.sql" />
    <Build Include="dbo\Tables\tpl_tfp_target_mapping.sql" />
    <Build Include="dbo\Tables\tco_goals.sql" />
    <Build Include="dbo\Tables\tco_goals_distribution.sql" />
    <Build Include="dbo\Tables\tco_targets.sql" />
    <Build Include="dbo\Tables\tco_targets_distribution.sql" />
    <Build Include="dbo\Tables\tfp_stage_investment_project_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedInvestmentProjects.sql" />
    <Build Include="dbo\Tables\tco_projects_setup.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeForecast_investment_2020.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMonthRepWhInvestment.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeSAMtransactions.sql" />
    <Build Include="dbo\Tables\tpl_tfp_focusarea_mapping_1.sql" />
    <Build Include="dbo\Tables\tpl_actions_goal.sql" />
    <Build Include="dbo\Tables\tpl_actions_target.sql" />
    <Build Include="dbo\Tables\tco_projects_type_setup.sql" />
    <Build Include="dbo\Stored Procedures\prcValidationOfAccounts.sql" />
    <Build Include="dbo\Stored Procedures\prcGenerateInvestmentFinplandoc.sql" />
    <Build Include="dbo\Tables\tfp_inv_document_data.sql" />
    <Build Include="dbo\Tables\tco_financing_descriptions.sql" />
    <Build Include="dbo\Tables\twh_project_status.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateProjectStatWh.sql" />
    <Build Include="dbo\Views\vw_doc_mr_overview_budchange.sql" />
    <Build Include="dbo\Views\vw_integ_dim_audit.sql" />
    <Build Include="dbo\Tables\tco_data_archive_log.sql" />
    <Build Include="dbo\Stored Procedures\prcDataArchive.sql" />
    <Build Include="dbo\Tables\tpl_tfp_strategy_mapping.sql" />
    <Build Include="dbo\Tables\tmr_proj_transactions.sql" />
    <Build Include="dbo\Tables\tmr_proj_status.sql" />
    <Build Include="dbo\Tables\tmr_proj_data_warehouse.sql" />
    <Build Include="dbo\Tables\gco_frequency.sql" />
    <Build Include="dbo\Tables\gco_security_estimate.sql" />
    <Build Include="dbo\Tables\gco_feasibility.sql" />
    <Build Include="dbo\Tables\tco_proj_responsible.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeForecast_investment_2021.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMonthRepWhInvestment_2021.sql" />
    <Build Include="dbo\Tables\tco_proj_temp_conversion.sql" />
    <Build Include="dbo\Views\vw_mr_proj_overview.sql" />
    <Build Include="dbo\Tables\gco_dw_load_version_tracking.sql" />
    <Build Include="dbo\Stored Procedures\UpdateTrackingChangeVersion.sql" />
    <Build Include="dbo\Tables\gco_external_data_config.sql" />
    <Build Include="dbo\Tables\tmd_external_data.sql" />
    <Build Include="dbo\Views\vw_doc_mr_bud_changes.sql" />
    <Build Include="dbo\Tables\tpl_plan_texts.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation2A_2021.sql" />
    <Build Include="dbo\Stored Procedures\prcPoliticalSimulation2B_2021.sql" />
    <Build Include="dbo\Tables\tmr_stage_projectinvfcast_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateMRImportedProjInvests.sql" />
    <Build Include="dbo\Tables\tco_table_grouping.sql" />
    <Build Include="dbo\Tables\gco_table_grouping_def.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_agresso_original.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_visma_original.sql" />
    <Build Include="dbo\Tables\twh_investment_report.sql" />
    <Build Include="dbo\Tables\tco_budget_phase_year_config.sql" />
    <Build Include="dbo\Tables\tps_answers_responsible.sql" />
    <Build Include="dbo\Tables\tps_qa_workflow_log.sql" />
    <Build Include="dbo\Tables\tps_questions_answers.sql" />
    <Build Include="dbo\Tables\tps_admin_party.sql" />
    <Build Include="dbo\Tables\tps_admin_qa_themes.sql" />
    <Build Include="dbo\Tables\tps_admin_qa_round.sql" />
    <Build Include="dbo\Tables\gco_progress_status.sql" />
    <Build Include="dbo\Tables\tpl_frontpagegoals_image.sql" />
    <Build Include="dbo\Views\vw_doc_bm_investment_graph.sql" />
    <Build Include="dbo\Views\vw_dash_forecast_net_res.sql" />
    <Build Include="dbo\Tables\tps_admin_investments_config.sql" />
    <Build Include="dbo\Tables\tco_public_status.sql" />
    <Build Include="dbo\Stored Procedures\prcBudgetRoundingSalAcc.sql" />
    <Build Include="dbo\Tables\tpl_plan_documents.sql" />
    <Build Include="dbo\Tables\tco_account_level.sql" />
    <Build Include="dbo\Tables\tcon_consol_company_setup.sql" />
    <Build Include="dbo\Tables\gco_consol_company_types.sql" />
    <Build Include="dbo\Tables\tcon_stage_consol_import.sql" />
    <Build Include="dbo\Tables\tcon_consol_transactions.sql" />
    <Build Include="dbo\Tables\tal_org_owner.sql" />
    <Build Include="dbo\Tables\tal_org_executive.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateConsolidationImport.sql" />
    <Build Include="dbo\Tables\gal_alert_definition.sql" />
    <Build Include="dbo\Tables\tal_alert_config.sql" />
    <Build Include="dbo\Tables\tal_user_alert_config.sql" />
    <Build Include="dbo\Tables\gco_log_events.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedAbsenceData.sql" />
    <Build Include="dbo\Tables\gco_elimination_rules.sql" />
    <Build Include="dbo\Tables\tcon_budform_1A.sql" />
    <Build Include="dbo\Tables\tcon_budform_2A.sql" />
    <Build Include="dbo\Tables\tcon_budform_B3.sql" />
    <Build Include="dbo\Tables\prcInitializeConsoleBudForm.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeConsoleBudForm.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeProjBudChange.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeProjBudChangeToTFP.sql" />
    <Build Include="dbo\Tables\tmr_rebud_aggr_rules.sql" />
    <Build Include="dbo\Tables\tpl_column_def_overrides.sql" />
    <Build Include="dbo\Tables\tpl_plan_objects_config.sql" />
    <Build Include="dbo\Tables\tmr_cons_budform_1A.sql" />
    <Build Include="dbo\Tables\tmr_cons_budform_2A.sql" />
    <Build Include="dbo\Tables\tmr_cons_budform_B3.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRConsoleBudform.sql" />
    <Build Include="dbo\Tables\gco_reporting_columns.sql" />
    <Build Include="dbo\Tables\tal_deadline_alert.sql" />
    <Build Include="dbo\Tables\gal_macros_definition.sql" />
    <Build Include="dbo\Tables\gal_macros_mapping.sql" />
    <Build Include="dbo\Tables\tfp_action_change_log.sql" />
    <Build Include="dbo\Tables\tco_bud_prop_log_status.sql" />
    <Build Include="dbo\Tables\tpl_investments.sql" />
    <Build Include="dbo\Tables\tpl_plan_investments.sql" />
    <Build Include="dbo\Tables\tpl_investments_detail.sql" />
    <Build Include="dbo\Tables\tpl_investments_goal.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_6.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_7.sql" />
    <Build Include="dbo\Tables\tco_org_data_level_8.sql" />
    <Build Include="dbo\Stored Procedures\prcCopyFinActionStatusRiskDesc.sql" />
    <Build Include="dbo\Tables\tco_hide_action_config.sql" />
    <Build Include="dbo\Stored Procedures\prcCopyGtiStatusRiskDesc.sql" />
    <Build Include="dbo\Tables\tpl_planningstrategy.sql" />
    <Build Include="dbo\Tables\tpl_planningstrategy_tasks.sql" />
    <Build Include="dbo\Tables\tpl_planningstrategy_info.sql" />
    <Build Include="dbo\Tables\tpl_tfp_assignment_mapping.sql" />
    <Build Include="dbo\Tables\tpl_tfp_assignment_comment.sql" />
    <Build Include="dbo\Tables\tal_user_substitute.sql" />
    <Build Include="dbo\Tables\tco_doc_widget.sql" />
    <Build Include="dbo\Tables\tco_budform_setup.sql" />
    <Build Include="dbo\Stored Procedures\prcGetPlanInvestmentDynamicData.sql" />
    <Build Include="dbo\Tables\tco_color_palette.sql" />
    <Build Include="dbo\Tables\tco_fonts.sql" />
    <Build Include="dbo\Tables\tco_fonts_definition.sql" />
    <Build Include="dbo\Tables\tco_color_palette_definition.sql" />
    <Build Include="dbo\Tables\tpl_plan_publish_versions.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRGoals.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRActivity.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRfinplanactions.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRAssignments.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRTasks.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRChecklists.sql" />
    <Build Include="dbo\Tables\tmr_report_setup_status.sql" />
    <Build Include="dbo\Tables\tco_custom_node_user_access.sql" />
    <Build Include="dbo\Tables\tco_custom_node_template_settings.sql" />
    <Build Include="dbo\Tables\tco_custom_node_settings_description.sql" />
    <Build Include="dbo\Tables\tmr_proj_bud_changes.sql" />
    <Build Include="dbo\Tables\tsa_tfp_investment_mapping.sql" />
    <Build Include="dbo\Tables\tpl_climateactions.sql" />
    <Build Include="dbo\Tables\tpl_climateactions_goal.sql" />
    <Build Include="dbo\Tables\tpl_climateactions_target.sql" />
    <Build Include="dbo\Tables\tpl_climateactions_strategy.sql" />
    <Build Include="dbo\Tables\tpl_climateactions_detail.sql" />
    <Build Include="dbo\Tables\tpl_climateactions_finance_detail.sql" />
    <Build Include="dbo\Stored Procedures\prc_erp_preview_visma.sql" />
    <Build Include="dbo\Stored Procedures\prc_erp_preview_agresso.sql" />
    <Build Include="dbo\Tables\tco_color_graph_override.sql" />
    <Build Include="dbo\Tables\gco_color_graph_definition.sql" />
    <Build Include="dbo\Views\vw_doc_funds_overview.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportDepartments.sql" />
    <Build Include="dbo\Tables\tco_attachments.sql" />
    <Build Include="dbo\Tables\tco_fee_table_definition.sql" />
    <Build Include="dbo\Tables\tco_fee_details.sql" />
    <Build Include="dbo\Tables\tco_fee_group_definition.sql" />
    <Build Include="dbo\Tables\tpl_tfp_climateaction_mapping.sql" />
    <Build Include="dbo\Tables\tco_climate_goal_registration.sql" />
    <Build Include="dbo\Stored Procedures\prc_OrgLevelToLangString.sql" />
    <Build Include="dbo\Tables\tco_bud_prop_finished_status.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedDimensionAccountData.sql" />
    <Build Include="dbo\Stored Procedures\proc_erp_finplan_export.sql" />
    <Build Include="dbo\Tables\tfp_erp_finplan_export_log.sql" />
    <Build Include="dbo\Tables\tfp_erp_finplan_exports.sql" />
    <Build Include="dbo\Stored Procedures\prcMetadataSynk.sql" />
    <Build Include="dbo\Tables\tco_report_formula.sql" />
    <Build Include="dbo\Tables\tpl_plan_previewtemplate.sql" />
    <Build Include="dbo\Tables\tps_operations_description.sql" />
    <Build Include="dbo\Stored Procedures\prcInsertDocVersionContent_1.sql" />
    <Build Include="dbo\Tables\tpl_plan_distribution.sql" />
    <Build Include="dbo\Views\vw_doc_cab_grid_pivot_1.sql" />
    <Build Include="dbo\Views\vw_mr_proj_oe.sql" />
    <Build Include="dbo\Tables\tps_investments_description.sql" />
    <Build Include="dbo\Tables\tpl_planstrategy_type.sql" />
    <Build Include="dbo\Tables\gco_log_collaboration.sql" />
    <Build Include="dbo\Tables\tco_url.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRPlan.sql" />
    <Build Include="dbo\Tables\tpl_goal_sort.sql" />
    <Build Include="dbo\Tables\tpl_target_sort.sql" />
    <Build Include="dbo\Tables\tco_kostra_widget_setup.sql" />
    <Build Include="dbo\Tables\tco_pop_city_setup.sql" />
    <Build Include="dbo\Tables\gco_pop_curcuit_data.sql" />
    <Build Include="dbo\Tables\gco_pop_curcuits.sql" />
    <Build Include="dbo\Tables\tco_pop_hierarchy.sql" />
    <Build Include="dbo\Tables\tco_pop_city_levels.sql" />
    <Build Include="gmd_pop_curcuit_age_interval.sql" />
    <Build Include="gco_ssb_curcuit_data.sql" />
    <Build Include="dbo\Tables\tco_goals_header.sql" />
    <Build Include="dbo\Tables\tco_docwf_responsibles.sql" />
    <Build Include="dbo\Tables\tco_docwf_template.sql" />
    <Build Include="dbo\Tables\tpl_metadata_contact_information.sql" />
    <Build Include="dbo\Tables\tpl_metadata_relevant_links.sql" />
    <Build Include="dbo\Tables\gco_pop_ssb_curcuits.sql" />
    <Build Include="dbo\Tables\tpl_metadata_setup.sql" />
    <Build Include="dbo\Tables\tpl_metadata_setup_texts.sql" />
    <Build Include="dbo\Tables\tpl_metadata_texts.sql" />
    <Build Include="dbo\Tables\tco_docwf_process.sql" />
    <Build Include="dbo\Tables\tco_docwf_process_approver_viewer.sql" />
    <Build Include="dbo\Tables\tco_docwf_proccess_log.sql" />
    <Build Include="dbo\Tables\tco_collaboration_comments.sql" />
    <Build Include="dbo\Tables\tmr_planreport_setup.sql" />
    <Build Include="dbo\Tables\tco_publish_review_parent.sql" />
    <Build Include="dbo\Tables\tco_publish_review_child.sql" />
    <Build Include="dbo\Views\vw_dash_investments.sql" />
    <Build Include="dbo\Tables\gco_period_list.sql" />
    <Build Include="dbo\Tables\tpl_focusarea_sort_1.sql" />
    <Build Include="dbo\Tables\tbf_budget_form_2B_ldip.sql" />
    <Build Include="dbo\Tables\tco_docwf_doc_part_log.sql" />
    <Build Include="dbo\Views\vw_mr_fp_action_status_report_1.sql" />
    <Build Include="dbo\Views\vw_mr_goal_target_status_report.sql" />
    <Build Include="dbo\Tables\tco_dashboard_roles.sql" />
    <Build Include="dbo\Tables\tco_targets_header.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedDimensionFunctionData.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRStrategy.sql" />
    <Build Include="dbo\Tables\tco_docwf_table_comments.sql" />
    <Build Include="dbo\Tables\gco_population_growth.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMrPlanTask.sql" />
    <Build Include="dbo\Views\vw_mr_proj_report.sql" />
    <Build Include="dbo\Tables\tco_collaborativeeditor_version.sql" />
    <Build Include="dbo\Tables\tco_stage_absence_employees_import.sql" />
    <Build Include="dbo\Tables\tco_newyear_customnode_mapping.sql" />
    <Build Include="dbo\Tables\tmr_planstratask_setup.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedPositionData.sql" />
    <Build Include="dbo\Tables\tco_newyear_log.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportAbsence.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedAbsVisWsData.sql" />
    <Build Include="dbo\Tables\tco_position_details.sql" />
    <Build Include="dbo\Tables\tfp_kpi_data.sql" />
    <Build Include="dbo\Stored Procedures\prcFetchKPIdata.sql" />
    <Build Include="dbo\Tables\gco_pub_colour_palette.sql" />
    <Build Include="dbo\Tables\gco_pub_colors.sql" />
    <Build Include="dbo\Tables\tpl_actions_strategy.sql" />
    <Build Include="dbo\Stored Procedures\prcBudFinplanCheck.sql" />
    <Build Include="dbo\Views\vw_log_page_view.sql" />
    <Build Include="dbo\Views\vw_plan_report_screen.sql" />
    <Build Include="dbo\Stored Procedures\prcFetchKPIdataPolsim.sql" />
    <Build Include="dbo\Tables\tps_kpi_data_polsim.sql" />
    <Build Include="dbo\Tables\tcon_consol_budform_1A.sql" />
    <Build Include="dbo\Tables\tcon_consol_budform_1B.sql" />
    <Build Include="dbo\Tables\tcon_consol_budform_2A.sql" />
    <Build Include="dbo\Tables\tcon_consol_budform_2B.sql" />
    <Build Include="dbo\Tables\tcon_consol_budform_B3.sql" />
    <Build Include="dbo\Tables\tcon_consol_budform_58inv.sql" />
    <Build Include="dbo\Stored Procedures\prcAbsenceCalculation.sql" />
    <Build Include="dbo\Tables\tpl_plan_node_config.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeConsoleBudFormSubTenant.sql" />
    <Build Include="dbo\Views\vw_bmdoc_1B_consolidated.sql" />
    <Build Include="dbo\Views\vw_bmdoc_2B_consolidated.sql" />
    <Build Include="dbo\Views\vw_mr_assignment_report.sql" />
    <Build Include="dbo\Tables\tco_api_repoting_log.sql" />
    <Build Include="dbo\Tables\tmr_climate_category.sql" />
    <Build Include="dbo\Tables\tmr_climate_emission_source.sql" />
    <Build Include="dbo\Tables\tmr_climate_accounting_data.sql" />
    <Build Include="dbo\Tables\tmr_stage_climate_accounting_data.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateMRClimateImportData.sql" />
    <Build Include="dbo\Tables\tal_polsim_qa_tracking.sql" />
    <Build Include="dbo\Tables\tmr_stage_action_import.sql" />
    <Build Include="dbo\Views\vw_bmdoc_investment_plan.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRPositionManYears.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateMrAction.sql" />
    <Build Include="dbo\Views\vw_budform_report.sql" />
    <Build Include="dbo\Tables\tco_fund_development.sql" />
    <Build Include="dbo\Tables\tco_fund_dpt_filter.sql" />
    <Build Include="dbo\Tables\tco_description_text.sql" />
    <Build Include="dbo\Tables\tpd_plandiscs.sql" />
    <Build Include="dbo\Tables\tpd_plandiscs_distribution.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMREquality.sql" />
    <Build Include="dbo\Tables\tpd_plandiscs_embeddedviews.sql" />
    <Build Include="dbo\Tables\tpd_plandiscs_roles.sql" />
    <Build Include="dbo\Tables\tmr_equality_data_warehouse.sql" />
    <Build Include="dbo\Tables\tpl_investments_target.sql" />
    <Build Include="dbo\Tables\tpl_investments_strategy.sql" />
    <Build Include="dbo\Tables\tmr_acc_lookup_warehouse.sql" />
    <Build Include="dbo\Tables\tbi_assignments_contributor.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRTenFactor.sql" />
    <Build Include="dbo\Tables\tmr_ten_factor_data_warehouse.sql" />
    <Build Include="dbo\Tables\gco_ten_factor_factors.sql" />
    <Build Include="dbo\Tables\tbu_stage_org_structure_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateOrgStructureImport.sql" />
    <Build Include="dbo\Tables\gco_ten_factor_questions.sql" />
    <Build Include="dbo\Tables\gco_ten_factor_data.sql" />
    <Build Include="dbo\Tables\tco_dimension_warnings.sql" />
    <Build Include="dbo\Tables\tco_reporting_api_request_log.sql" />
    <Build Include="dbo\Stored Procedures\prc_insert_dimension_import_main_project.sql" />
    <Build Include="dbo\Tables\tbi_assignment_admin.sql" />
    <Build Include="dbo\Tables\tmr_absence_employees_processed_data_warehouse.sql" />
    <Build Include="dbo\Views\vw_mr_absence_employees_data_warehouse.sql" />
    <Build Include="dbo\Tables\tmr_absence_employees_data_warehouse.sql" />
    <Build Include="dbo\Views\vw_mr_absence_employees_details_data_warehouse.sql" />
    <Build Include="dbo\Views\vw_mr_absence_employees_1.sql" />
    <Build Include="dbo\Views\vw_mr_absence_employees_details_1.sql" />
    <Build Include="dbo\Tables\tmr_absence_employees.sql" />
    <Build Include="dbo\Tables\tmr_absence_employees_processed_1.sql" />
    <Build Include="dbo\Tables\tbi_assignment_live_status.sql" />
    <Build Include="dbo\Stored Procedures\procMoveOELastYear.sql" />
    <Build Include="dbo\Tables\gco_salary_table.sql" />
    <Build Include="dbo\Stored Procedures\prcFetchKPIdataMR.sql" />
    <Build Include="dbo\Tables\tmr_kpi_data.sql" />
    <Build Include="dbo\Stored Procedures\prcGenereateSalaryTableBudchange.sql" />
    <Build Include="dbo\Tables\tbu_stage_salary_table_bud_changes.sql" />
    <Build Include="dbo\Tables\gco_accessibility_declaration.sql" />
    <Build Include="dbo\Tables\gco_accessibility_declaration_issues.sql" />
    <Build Include="dbo\Tables\tco_accessibility_declaration.sql" />
    <Build Include="dbo\Tables\tco_user_dashboards.sql" />
    <Build Include="dbo\Tables\tco_newyear_Blist_inv_log.sql" />
    <Build Include="dbo\Tables\gco_modules_new.sql" />
    <Build Include="dbo\Tables\tco_module_mapping.sql" />
    <Build Include="dbo\Tables\gco_accessibility_nodes.sql" />
    <Build Include="dbo\Tables\gco_integ_error_types.sql" />
    <Build Include="dbo\Views\vw_mrdoc_2B_consolidated.sql" />
    <Build Include="dbo\Views\vw_mrdoc_1B_consolidated.sql" />
    <Build Include="dbo\Views\vw_mr_inv_graph_sa.sql" />
    <Build Include="dbo\Tables\tco_stage_account_structure_import .sql" />
    <Build Include="dbo\Tables\tco_global_news_log.sql" />
    <Build Include="dbo\Tables\tpl_plan_strategy_categories.sql" />
    <Build Include="dbo\Tables\tco_kostra_widget_groups.sql" />
    <Build Include="dbo\Tables\tpl_planningstrategy_category_config.sql" />
    <Build Include="dbo\Tables\tbi_template_tree_details.sql" />
    <Build Include="dbo\Tables\tbi_custom_nodes.sql" />
    <Build Include="dbo\Tables\tbi_custom_nodes_delegation.sql" />
    <Build Include="dbo\Stored Procedures\PrcValidateImportedAccountStructureData.sql" />
    <Build Include="dbo\Stored Procedures\prcApiIntegrationAccounts.sql" />
    <Build Include="dbo\Tables\tco_import_account_log.sql" />
    <Build Include="dbo\Tables\tco_import_log.sql" />
    <Build Include="dbo\Tables\tbu_stage_budget_integrations.sql" />
    <Build Include="dbo\Tables\tco_api_log.sql" />
    <Build Include="dbo\Stored Procedures\prc_insert_dimension_import_accounts.sql" />
    <Build Include="dbo\Tables\tco_monthrep_kpi_texts.sql" />
    <Build Include="dbo\Views\vw_yb_1B_consolidated.sql" />
    <Build Include="dbo\Views\vw_yb_2B_consolidated.sql" />
    <Build Include="dbo\Tables\tpl_assignments_strategy.sql" />
    <Build Include="dbo\Tables\tco_budprop_access.sql" />
    <Build Include="dbo\Tables\tfp_sync_datawarehouse.sql" />
    <Build Include="dbo\Views\vw_tfp_sync_warehouse.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncFinplanWarehouse.sql" />
    <Build Include="dbo\Tables\tco_sync_company_setup.sql" />
    <Build Include="dbo\Tables\tco_attribute_values.sql" />
    <Build Include="dbo\Tables\tco_man_years.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateTcoManYears.sql" />
    <Build Include="dbo\Tables\tfp_sync_budget_round.sql" />
    <Build Include="dbo\Tables\gco_KS_position_mapping.sql" />
    <Build Include="dbo\Tables\tfp_sync_actions.sql" />
    <Build Include="dbo\Tables\tfp_sync_text_nodes.sql" />
    <Build Include="dbo\Tables\tfp_sync_text_node_delegation.sql" />
    <Build Include="dbo\Tables\tco_custom_node_budphase_mapping.sql" />
    <Build Include="dbo\Views\vwActualManYearsFormerYears.sql" />
    <Build Include="dbo\Tables\tco_attributes.sql" />
    <Build Include="dbo\Tables\gco_indicator_control_parameter.sql" />
    <Build Include="dbo\Tables\tfp_sync_background_job_status.sql" />
    <Build Include="dbo\Views\vw_mr_forecast_report.sql" />
    <Build Include="dbo\Tables\gco_log_query.sql" />
    <Build Include="dbo\Views\vw_operations_report.sql" />
    <Build Include="dbo\Tables\tco_budget_authority.sql" />
    <Build Include="dbo\Tables\twh_investment_report_data.sql" />
    <Build Include="dbo\Stored Procedures\prcGenerateInvestmentReportData.sql" />
    <Build Include="dbo\Views\vw_mr_proj_overview_aggregated.sql" />
    <Build Include="dbo\Views\vw_investment_report.sql" />
    <Build Include="dbo\Tables\tco_stage_relation_values.sql" />
    <Build Include="dbo\Tables\tco_stage_attribute_values.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedRelationValuesData.sql" />
    <Build Include="dbo\Tables\tco_filter_accounts.sql" />
    <Build Include="dbo\Tables\tco_filter_functions.sql" />
    <Build Include="dbo\Tables\tco_filter_free_dim_1.sql" />
    <Build Include="dbo\Tables\tco_filter_free_dim_2.sql" />
    <Build Include="dbo\Tables\tco_filter_free_dim_3.sql" />
    <Build Include="dbo\Tables\tco_filter_free_dim_4.sql" />
    <Build Include="dbo\Stored Procedures\prcGenerateFilterData.sql" />
    <Build Include="dbo\Tables\tco_equality.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateTcoEquality.sql" />
    <Build Include="dbo\Tables\tfp_proj_sync_datawarehouse.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncProjWarehouse.sql" />
    <Build Include="dbo\Tables\tco_api_auth_activities.sql" />
    <Build Include="dbo\Tables\tfp_sync_focus_area.sql" />
    <Build Include="dbo\Tables\tfp_sync_goals.sql" />
    <Build Include="dbo\Tables\tfp_sync_tags.sql" />
    <Build Include="dbo\Tables\tfp_sync_targets.sql" />
    <Build Include="dbo\Tables\tco_api_auth_client_mapping.sql" />
    <Build Include="dbo\Tables\tfp_sync_transfer_job_status.sql" />
    <Build Include="dbo\Tables\tco_sync_activity_indicator_group.sql" />
    <Build Include="dbo\Tables\tco_sync_activity_indicator.sql" />
    <Build Include="dbo\Tables\tco_sync_indicator_setup.sql" />
    <Build Include="dbo\Tables\tmd_sync_indicator_results.sql" />
    <Build Include="dbo\Tables\tfp_sync_target_indicators.sql" />
    <Build Include="dbo\Tables\gco_img_category.sql" />
    <Build Include="dbo\Tables\gco_img_category_mapping.sql" />
    <Build Include="dbo\Tables\tfp_sync_transfer_error_log.sql" />
    <Build Include="dbo\Views\vw_finplan_report.sql" />
    <Build Include="dbo\Views\vw_budgetreport.sql" />
    <Build Include="dbo\Tables\tfp_sync_strategy.sql" />
    <Build Include="dbo\Tables\tfp_sync_assignments.sql" />
    <Build Include="dbo\Tables\tbu_sync_original_budget.sql" />
    <Build Include="dbo\Stored Procedures\prcSyncOrgBudWarehouse.sql" />
    <Build Include="dbo\Tables\tfp_sync_main_projects.sql" />
    <Build Include="dbo\Tables\tfp_sync_yb_background_job_status.sql" />
    <Build Include="dbo\Tables\flat_org_hierarchy_dep.sql" />
    <Build Include="dbo\Tables\flat_attribute_values.sql" />
    <Build Include="dbo\Tables\flat_function_service_values.sql" />
    <Build Include="dbo\Tables\flat_accounts.sql" />
    <Build Include="dbo\Tables\flat_projects.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateFlatTables.sql" />
    <Build Include="dbo\Tables\tco_finplan_adj_codes_actions_temp.sql" />
    <Build Include="dbo\Tables\tfp_sync_category.sql" />
    <Build Include="dbo\Tables\tco_reporting_template_delegation.sql" />
    <Build Include="dbo\Tables\tco_log_doc_widget_publish.sql" />
    <Build Include="dbo\Views\vw_tfp_sync_warehouse_budget.sql" />
    <Build Include="dbo\Tables\twh_budalloc_data.sql" />
    <Build Include="dbo\Stored Procedures\prc_update_budallocwh.sql" />
    <Build Include="dbo\Tables\tco_stage_assignment_import.sql" />
    <Build Include="dbo\Tables\tfp_action_allocate_status.sql" />
    <Build Include="dbo\Tables\tfp_action_allocate_auto.sql" />
    <Build Include="dbo\Views\vw_tfp_sync_proj_warehouse.sql" />
    <Build Include="dbo\Tables\tco_sync_reporting_template.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_original_control_V2.sql" />
    <Build Include="dbo\Tables\tmr_stage_ISY_import.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeISYImport.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportedInvestmentForecast.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportInvestmentStatus.sql" />
    <Build Include="dbo\Tables\flat_users.sql" />
    <Build Include="dbo\Stored Procedures\prcUpdateFlatUsers.sql" />
    <Build Include="dbo\Tables\gco_log_ckeditor_content.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_original_control_2.sql" />
    <Build Include="dbo\Tables\tco_bud_authority_access.sql" />
    <Build Include="dbo\Tables\tfp_sync_template_transfer.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportInvestmentStatus.sql" />
    <Build Include="dbo\Tables\tmr_stage_status_ISY_import.sql" />
    <Build Include="dbo\Tables\gco_log_api_request_log.sql" />
    <Build Include="dbo\Tables\tco_stage_activity_indicators.sql" />
    <Build Include="dbo\Tables\gco_KS_position_group_definition.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportActivityIndicator.sql" />
    <Build Include="dbo\Tables\tco_add_on_setup.sql" />
    <Build Include="dbo\Tables\tco_holiday_setup.sql" />
    <Build Include="dbo\Tables\tmr_ISY_fin_import_log.sql" />
    <Build Include="dbo\Tables\tmr_ISY_status_desc_import_log.sql" />
    <Build Include="dbo\Tables\tco_ISY_projects.sql" />
    <Build Include="dbo\Tables\tmr_report_log_status.sql" />
    <Build Include="dbo\Tables\tmr_sync_report_setup.sql" />
    <Build Include="dbo\Views\vw_mr_operations_proj_overview.sql" />
    <Build Include="dbo\Tables\gco_ducky_mapping.sql" />
    <Build Include="dbo\Tables\gco_ducky_grouping.sql" />
    <Build Include="dbo\Tables\gco_ducky_details.sql" />
    <Build Include="dbo\Tables\gco_ducky_population.sql" />
    <Build Include="dbo\Tables\tco_user_dashboard_roles.sql" />
    <Build Include="dbo\Tables\tbu_stage_project_structure_import.sql" />
    <Build Include="dbo\Stored Procedures\prcValidateImportProjectStructure.sql" />
    <Build Include="dbo\Tables\tco_integ_postype_exclude.sql" />
    <Build Include="dbo\Tables\tco_integ_abscode_include.sql" />
    <Build Include="dbo\Tables\tfp_investment_allocate_status.sql" />
    <Build Include="dbo\Tables\tfp_investment_allocate_auto.sql" />
    <Build Include="dbo\Tables\twh_budalloc_project_data.sql" />
    <Build Include="dbo\Stored Procedures\prc_update_budalloc_proj_wh.sql" />
    <Build Include="dbo\Views\vw_mr_proj_structure_overview.sql" />
    <Build Include="dbo\Tables\tco_user_widget_url.sql" />
    <Build Include="dbo\Function\getFunctionServices.sql" />
    <Build Include="dbo\Function\getAttributesForForecast.sql" />
    <Build Include="dbo\Tables\tmd_salary_positions_accounts.sql" />
    <Build Include="dbo\Tables\tmr_projstructure_template_details.sql" />
    <Build Include="dbo\Tables\gco_user_widget_group.sql" />
    <Build Include="dbo\Tables\gco_user_widgets.sql" />
    <Build Include="dbo\Tables\gmd_kfr_rationals_matrix.sql" />
    <Build Include="dbo\Tables\tco_user_widget_activity.sql" />
    <Build Include="dbo\Tables\gco_dashboard_grouping.sql" />
    <Build Include="dbo\Tables\twh_budalloc_data_accountview.sql" />
    <Build Include="dbo\Stored Procedures\prc_update_budalloc_accountview.sql" />
    <Build Include="dbo\Tables\tco_filter_account_levels.sql" />
    <Build Include="dbo\Tables\tco_field_settings.sql" />
    <Build Include="dbo\Tables\tco_user_activity_indicator_widget.sql" />
    <Build Include="dbo\Tables\tal_integration_tenant_mapping.sql" />
    <Build Include="dbo\Tables\tal_integration_user_mapping.sql" />
    <Build Include="dbo\Tables\tal_integration_alert.sql" />
    <Build Include="dbo\Tables\tco_start_page_widget.sql" />
    <Build Include="dbo\Tables\tal_stage_admin_alert_import.sql" />
    <Build Include="dbo\Tables\tco_proj_levels_descriptions.sql" />
    <Build Include="dbo\Tables\tco_operational_main_projects.sql" />
    <Build Include="dbo\Tables\tco_operational_project_type.sql" />
    <Build Include="dbo\Tables\tmr_proj_operations_status.sql" />
    <Build Include="dbo\Tables\tpd_admin_plandiscs_setup.sql" />
    <Build Include="dbo\Tables\tpd_admin_plandiscs_setup_log.sql" />
    <Build Include="dbo\Tables\gmd_holiday_table_1.sql" />
    <Build Include="dbo\Tables\tpd_plandisc_admin_setup_delegation.sql" />
    <Build Include="dbo\Tables\tpd_admin_setup_assignment_dates.sql" />
    <Build Include="dbo\Tables\tbu_temp_employments_forecast_add_on.sql" />
    <Build Include="dbo\Views\vw_doc_operational_main_projects.sql" />
    <Build Include="dbo\Tables\tbu_stage_salary_forecast.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeMRClimateIndicators.sql" />
    <Build Include="dbo\Tables\tmr_climate_indicators_status.sql" />
    <Build Include="dbo\Tables\tco_budget_phase_publish_status.sql" />
    <Build Include="dbo\Tables\tsa_tfp_action_mapping.sql" />
    <Build Include="dbo\Views\vw_mr_proj_VAT_income.sql" />
    <Build Include="dbo\Views\vw_mr_proj_alter_code.sql" />
    <Build Include="dbo\Tables\tfp_sync_static_node_delegation.sql" />
    <Build Include="dbo\Tables\tfp_sync_climate_indicators.sql" />
    <Build Include="dbo\Tables\tfp_sync_climate_actions.sql" />
    <Build Include="dbo\Stored Procedures\prcSalaryForecastUpdateImportIdAndColumns.sql" />
    <Build Include="dbo\Stored Procedures\prcSalaryForecastValidateImportedPositions.sql" />
    <Build Include="dbo\Tables\tpd_plandisc_assignment_meetings.sql" />
    <Build Include="dbo\Tables\tpd_plandisc_track_assignments.sql" />
    <Build Include="dbo\Tables\tpd_plandisc_assignment_category.sql" />
    <Build Include="dbo\Tables\tpd_plandisc_assignment_label.sql" />
    <Build Include="dbo\Tables\tco_budprop_template_config.sql" />
    <Build Include="dbo\Tables\tco_static_node_budphase_mapping.sql" />
    <Build Include="dbo\Stored Procedures\proc_bud_original_control_updates_2.sql" />
    <Build Include="dbo\Tables\tal_stage_absence_access_import.sql" />
    <Build Include="dbo\Tables\tco_activity_indicator_track.sql" />
    <Build Include="dbo\Tables\tco_azure_table_descriptions.sql" />
    <Build Include="dbo\Tables\tco_azure_blob_descriptions.sql" />
    <Build Include="dbo\Tables\tfp_strategy_header.sql" />
    <Build Include="dbo\Tables\gmd_publish_content_types.sql" />
    <Build Include="dbo\Tables\tbi_busplan_access.sql" />
    <Build Include="dbo\Tables\tco_monthrep_descriptions.sql" />
    <Build Include="dbo\Stored Procedures\prcInitializeForecastV2.sql" />
    <Build Include="dbo\Tables\tpl_assignment_sort.sql" />
    <Build Include="dbo\Tables\tmr_budget_changes_period_mapping.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Framsikt.Db.TenantGroupDb.publish.xml" />
    <None Include="Framsikt_1.Db.TenantGroupDb.publish.xml" />
    <None Include="Framsikt_2.Db.TenantGroupDb.publish.xml" />
    <None Include="Framsikt_local.publish.xml" />
    <None Include="Snapshots\Framsikt.Db.TenantGroupDb_20141028_17-28-56.dacpac" />
    <None Include="Dev-Tenant-Group-1.publish.xml" />
    <None Include="Framsikt.Master.publish.xml" />
    <None Include="Dev-Tenant-Group-1-New.publish.xml" />
    <None Include="Dev-Framsikt-Master.publish.xml" />
    <None Include="PowershellScript\dev_update_metadata.ps1" />
    <None Include="PowershellScript\local_exec_dev_update_metadata.ps1" />
    <None Include="Scripts\PreDeployment\tco_users_configuration.sql" />
    <None Include="dbo\Function\fnSplitString.sql" />
    <None Include="dbo\Views\vw_mr_cost_rev_graph_sa_annual.sql" />
    <None Include="dbo\Stored Procedures\prcValidateImportFriDims.sql" />
    <None Include="dbo\Tables\tco_stage_absence_import.sql" />
    <None Include="dbo\Tables\tpl_strategy_sort.sql" />
    <None Include="dbo\Tables\tmr_strategy_monthly_status.sql" />
    <None Include="dbo\Tables\tco_stage_position_import.sql" />
    <None Include="dbo\Tables\tco_employee_data.sql" />
    <None Include="dbo\Tables\tmr_man_years_data_warehouse.sql" />
    <None Include="dbo\Tables\tco_position_category.sql" />
    <None Include="dbo\Stored Procedures\prcValidateImportedObjectValues.sql" />
    <None Include="dbo\Stored Procedures\prcValidateImportedAssignmentData.sql" />
    <None Include="dbo\Tables\tco_user_dashboard_distribution.sql" />
    <None Include="dbo\Tables\tco_widget_node_master.sql" />
    <None Include="dbo\Tables\tco_widget_node_delegation.sql" />
    <None Include="dbo\Tables\tps_admin_qa_round_setup.sql" />
    <None Include="dbo\Tables\tfp_effect_climate_detail.sql" />
    <None Include="dbo\Tables\tco_climates_distribution.sql" />
    <None Include="dbo\Tables\tbu_employments_changes_tracker.sql" />
    <None Include="dbo\Tables\tco_climate_finance_details.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="Framsikt.Db.TenantGroupDb.refactorlog" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="Script.PostDeployment.sql" />
  </ItemGroup>
</Project>