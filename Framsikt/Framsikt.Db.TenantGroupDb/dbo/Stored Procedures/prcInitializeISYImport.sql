

CREATE OR ALTER PROCEDURE [prcInitializeISYImport]    
@fk_tenant_id INT,    
@forecast_period INT,    
@fk_user_id INT    
    
AS    
    
    
--DECLARE @fk_tenant_id INT = 530, @forecast_period INT = 202310, @fk_user_id INT = 1132    
    
DECLARE @budget_year INT    
    
SET @budget_year = @forecast_period/100    
    
CREATE TABLE #projbudgetdata    
(    
    [fk_tenant_id] INT NOT NULL,     
    [budget_year] INT NOT NULL,     
    [fk_account_code] NVARCHAR(25) NOT NULL,    
    [fk_department_code] NVARCHAR(25) NOT NULL,    
 [fk_function_code] NVARCHAR(25) NOT NULL,    
    [fk_project_code] NVARCHAR(25) NOT NULL,    
 [free_dim_1] NVARCHAR(25) NOT NULL,    
 [free_dim_2] NVARCHAR(25) NOT NULL,    
 [free_dim_3] NVARCHAR(25) NOT NULL,    
 [free_dim_4] NVARCHAR(25) NOT NULL,    
    [inv_status] INT NOT NULL,    
    [adjustment_code_status] NVARCHAR(25) NOT NULL,    
    [amount] DECIMAL(18, 2) NOT NULL,     
)    
    
CREATE TABLE #projbudgetdata2    
(    
    [fk_tenant_id] INT NOT NULL,     
    [budget_year] INT NOT NULL,     
    [fk_account_code] NVARCHAR(25) NOT NULL,    
    [fk_department_code] NVARCHAR(25) NOT NULL,    
 [fk_function_code] NVARCHAR(25) NOT NULL,    
    [fk_project_code] NVARCHAR(25) NOT NULL,    
 [free_dim_1] NVARCHAR(25) NOT NULL,    
 [free_dim_2] NVARCHAR(25) NOT NULL,    
 [free_dim_3] NVARCHAR(25) NOT NULL,    
 [free_dim_4] NVARCHAR(25) NOT NULL,    
    [amount] DECIMAL(18, 2) NOT NULL,     
)    
    
INSERT INTO #projbudgetdata (    
fk_tenant_id    
,budget_year    
,fk_account_code    
,fk_department_code    
,fk_function_code    
,fk_project_code    
,free_dim_1    
,free_dim_2    
,free_dim_3    
,free_dim_4    
,inv_status    
,adjustment_code_status    
,amount )    
SELECT     
a.fk_tenant_id    
,a.year    
,a.fk_account_code    
,a.fk_department_code    
,a.fk_function_code    
,a.fk_project_code    
,a.free_dim_1    
,a.free_dim_2    
,a.free_dim_3    
,a.free_dim_4    
,inv_status = ISNULL(mp.inv_status,'-1')    
,adjustment_code_status = CASE WHEN UAD.status = 1 THEN 'Godkjent'    
                               WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'    
                               ELSE 'Åpen'    
                               END    
,sum(amount) as amount    
FROM tfp_proj_transactions a    
JOIN tfp_budget_changes bc ON a.fk_tenant_id = bc.fk_tenant_id AND a.fk_change_id = bc.pk_change_id AND bc.budget_year <= @budget_year    
JOIN tco_projects pj ON a.fk_tenant_id = pj.fk_tenant_id AND a.fk_project_code = pj.pk_project_code AND @budget_year BETWEEN DATEPART(YEAR,pj.date_from) AND DATEPART(YEAR,pj.date_to)     
JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id and pj.fk_main_project_code = mp.pk_main_project_code AND @budget_year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)    
JOIN tco_user_adjustment_codes UAD ON a.fk_tenant_id = UAD.fk_tenant_id and a.fk_user_adjustment_code = UAD.pk_adj_code    
JOIN tco_accounts tca ON a.fk_tenant_id = tca.pk_tenant_id and a.fk_account_code = tca.pk_account_code AND @budget_year BETWEEN DATEPART(YEAR,tca.dateFrom) AND DATEPART(YEAR,tca.dateTo)  
JOIN gmd_reporting_line grl ON tca.fk_kostra_account_code = grl.fk_kostra_account_code AND grl.report = '55_OVINV' AND grl.line_group_id = 10  
WHERE a.fk_tenant_id = @fk_tenant_id     
AND a.year in (@budget_year-1, @budget_year-2, @budget_year)   
GROUP BY     
a.fk_tenant_id    
,a.year    
,a.fk_account_code    
,a.fk_department_code    
,a.fk_function_code    
,a.fk_project_code    
,a.free_dim_1    
,a.free_dim_2    
,a.free_dim_3    
,a.free_dim_4    
,mp.inv_status    
,UAD.status    
,UAD.include_in_calculation    
,BC.budget_year    
    
    
INSERT INTO #projbudgetdata2 (    
fk_tenant_id    
,budget_year    
,fk_account_code    
,fk_department_code    
,fk_function_code    
,fk_project_code    
,free_dim_1    
,free_dim_2    
,free_dim_3    
,free_dim_4    
,amount )    
SELECT     
fk_tenant_id    
,budget_year    
,fk_account_code    
,fk_department_code    
,fk_function_code    
,fk_project_code    
,free_dim_1    
,free_dim_2    
,free_dim_3    
,free_dim_4    
,sum(amount) as amount    
FROM #projbudgetdata    
WHERE inv_status not in (3,4,5,6)    
AND adjustment_code_status = 'Godkjent'    
GROUP BY     
fk_tenant_id    
,budget_year    
,fk_account_code    
,fk_department_code    
,fk_function_code    
,fk_project_code    
,free_dim_1    
,free_dim_2    
,free_dim_3    
,free_dim_4    
    
    
    
    
    
    
    
select*into #max_account from    
(select fk_project_code, fk_account_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata2    
group by fk_project_code,fk_account_code) CALC    
where rank = 1    
and fk_project_code != ''    
    
    
      
    
    
select*into #max_department from    
(select fk_project_code, fk_department_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata    
group by fk_project_code,fk_department_code) CALC    
where rank = 1    
and fk_project_code != ''    
    
    
    
select*into #max_function from    
(select fk_project_code, fk_function_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata    
group by fk_project_code,fk_function_code) CALC    
where rank = 1    
and fk_project_code != ''    
    
    
    
    
select*into #max_free_dim_1 from    
(select fk_project_code, free_dim_1,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata    
group by fk_project_code,free_dim_1) CALC    
where rank = 1    
and fk_project_code != ''    
    
    
    
select*into #max_free_dim_2 from    
(select fk_project_code, free_dim_2,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata    
group by fk_project_code,free_dim_2) CALC    
where rank = 1    
and fk_project_code != ''    
    
    
    
select*into #max_free_dim_3 from    
(select fk_project_code, free_dim_3,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata    
group by fk_project_code,free_dim_3) CALC    
where rank = 1    
and fk_project_code != ''    
    
    
    
select*into #max_free_dim_4 from    
(select fk_project_code, free_dim_4,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(abs(amount)) desc)    
from #projbudgetdata    
group by fk_project_code,free_dim_4) CALC    
where rank = 1    
and fk_project_code != ''    
    
      
    
    
    
SELECT    
a.fk_tenant_id    
,a.budget_year    
,a.fk_project_code    
,b.fk_account_code    
,c.fk_department_code    
,d.fk_function_code    
,e.free_dim_1    
,f.free_dim_2    
,g.free_dim_3    
,h.free_dim_4    
INTO #projbudgetdatamax    
FROM #projbudgetdata2 a    
LEFT JOIN #max_account b on a.fk_project_code = b.fk_project_code    
LEFT JOIN #max_department c on a.fk_project_code = c.fk_project_code    
LEFT JOIN #max_function d on a.fk_project_code = d.fk_project_code    
LEFT JOIN #max_free_dim_1 e on a.fk_project_code = e.fk_project_code    
LEFT JOIN #max_free_dim_2 f on a.fk_project_code = f.fk_project_code    
LEFT JOIN #max_free_dim_3 g on a.fk_project_code = g.fk_project_code    
LEFT JOIN #max_free_dim_4 h on a.fk_project_code = h.fk_project_code    
GROUP BY a.fk_tenant_id, a.budget_year, a.fk_project_code, b.fk_account_code, c.fk_department_code, d.fk_function_code, e.free_dim_1, f.free_dim_2, g.free_dim_3, h.free_dim_4    
    
    
UPDATE tmr_stage_ISY_import     
set fk_account_code = b.fk_account_code, fk_department_code = b.fk_department_code, fk_function_code = b.fk_function_code, free_dim_1 = b.free_dim_1, free_dim_2 = b.free_dim_2, free_dim_3 = b.free_dim_3, free_dim_4 = b.free_dim_4    
FROM tmr_stage_ISY_import a    
JOIN #projbudgetdatamax b ON a.fk_tenant_id = b.fk_tenant_id AND RTRIM(LTRIM(a.fk_project_code)) = RTRIM(LTRIM(b.fk_project_code))
WHERE a.fk_tenant_id = @fk_tenant_id and a.forecast_period = @forecast_period    
    
    
DROP TABLE IF EXISTS #projbudgetdata    
DROP TABLE IF EXISTS #projbudgetdata2    
DROP TABLE IF EXISTS #max_account    
DROP TABLE IF EXISTS #max_department    
DROP TABLE IF EXISTS #max_function    
DROP TABLE IF EXISTS #max_free_dim_1    
DROP TABLE IF EXISTS #max_free_dim_2    
DROP TABLE IF EXISTS #max_free_dim_3    
DROP TABLE IF EXISTS #max_free_dim_4    
DROP TABLE IF EXISTS #projbudgetdatamax    
