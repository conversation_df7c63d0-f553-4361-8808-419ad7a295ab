CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportProjectStructure]
	@tenant_id int,        
	 @user_id int,        
	 @job_id int
AS
--create table for project levels
CREATE TABLE #projLevelData(
	[pk_id] [int] IDENTITY(1,1) NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[fk_proj_version] [nvarchar](25) NOT NULL,
	[proj_level] [int] NOT NULL,
	[level_name] [nvarchar](50) NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL,
)
insert into #projLevelData(fk_tenant_id,fk_proj_version,proj_level,level_name,updated,updated_by)
select a.fk_tenant_id,a.fk_proj_version,a.proj_level,a.level_name,a.updated,a.updated_by from tco_proj_level a where a.fk_tenant_id = @tenant_id
IF(@job_id = -1)
	BEGIN
		 UPDATE tbu_stage_project_structure_import        
			 SET fk_proj_version_error = 0,        
				 fk_proj_code_error = 0,        
				  proj_gr_1_error = 0,         
				  proj_gr_2_error = 0,        
				  proj_gr_3_error = 0,        
				  proj_gr_4_error = 0,        
				  proj_gr_5_error = 0,        
				  proj_gr_name_1_error = 0,        
				  proj_gr_name_2_error = 0,
				  proj_gr_name_3_error=0,
				  proj_gr_name_4_error = 0,        
				  proj_gr_name_5_error = 0,        
				  error_count = 0       
			 WHERE fk_tenant_id = @tenant_id AND         
			  updated_by = @user_id;

		-- validate project version
		UPDATE	tbu_stage_project_structure_import
		SET		fk_proj_version_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND (tbu_stage_project_structure_import.fk_proj_version IS NULL OR tbu_stage_project_structure_import.fk_proj_version = ''
				OR fk_proj_version NOT IN (SELECT pk_proj_version from tco_proj_version where fk_tenant_id = @tenant_id));
		-- validate project code

			UPDATE	tbu_stage_project_structure_import
			SET		fk_proj_code_error = 1, error_count = error_count + 1
			FROM	tbu_stage_project_structure_import
			WHERE	fk_tenant_id = @tenant_id AND updated_by = @user_id AND (tbu_stage_project_structure_import.fk_proj_code IS NULL OR tbu_stage_project_structure_import.fk_proj_code = '' OR 
					(fk_proj_code NOT IN (SELECT pk_project_code from tco_projects where fk_tenant_id = @tenant_id AND fk_main_project_code!='')));
		-- validate proj_grp_1 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_1_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND ((1 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_1 = '' AND proj_gr_1  IS NULL AND ISNUMERIC(proj_gr_1) = 0))
				OR (1 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_1,'')!='' AND proj_gr_1  IS NOT NULL AND ISNUMERIC(proj_gr_1) != 0)));
		-- validate proj_grp_2 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_2_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND ((2 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_2 = '' AND proj_gr_2  IS NULL AND ISNUMERIC(proj_gr_2) = 0))
				OR (2 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_2,'')!='' AND proj_gr_2  IS NOT NULL AND ISNUMERIC(proj_gr_2) != 0)));
				-- validate proj_grp_3 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_3_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND ((3 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_3 = '' AND proj_gr_3  IS NULL AND ISNUMERIC(proj_gr_3) = 0))
				OR (3 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_3,'')!='' AND proj_gr_3  IS NOT NULL AND ISNUMERIC(proj_gr_3) != 0)));
		-- validate proj_grp_4 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_4_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND ((4 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_4 != '' AND proj_gr_4  IS NOT NULL AND ISNUMERIC(proj_gr_4) = 0))
				OR (4 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_4,'')!='' AND proj_gr_4 IS NOT NULL AND ISNUMERIC(proj_gr_4) != 0)));
				-- validate proj_grp_5 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_5_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND ((5 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_5 = '' AND proj_gr_5  IS NULL AND ISNUMERIC(proj_gr_5) = 0))
				OR (5 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_5,'')!='' AND proj_gr_5  IS NOT NULL AND ISNUMERIC(proj_gr_5) != 0)))
					-- validate proj_grp_name_1 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_1_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND (proj_gr_1_error = 1 
				OR ( 1 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_1_error = 0 AND (proj_gr_name_1 = '' OR proj_gr_name_1 IS NULL))));
		-- validate proj_grp_name_2 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_2_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND (proj_gr_2_error = 1 
				OR ( 2 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_2_error = 0 AND (proj_gr_name_2 = '' OR proj_gr_name_2  IS NULL))));
				-- validate proj_name_grp_3 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_3_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND (proj_gr_3_error = 1 
				OR ( 3 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_3_error = 0 AND (proj_gr_name_3 = '' OR proj_gr_name_3  IS NULL))));
		-- validate proj_grp_name_4 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_4_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND (proj_gr_4_error = 1 
				OR ( 4 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_4_error = 0 AND (proj_gr_name_4 = '' OR proj_gr_name_4  IS NULL))));
				-- validate proj_grp_name_5 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_5_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id  AND (proj_gr_5_error = 1 
				OR ( 5 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_5_error = 0 AND (proj_gr_name_5 = '' OR proj_gr_name_5  IS NULL))));
END
ELSE
	BEGIN
		 UPDATE tbu_stage_project_structure_import        
			 SET fk_proj_version_error = 0,        
				 fk_proj_code_error = 0,        
				  proj_gr_1_error = 0,         
				  proj_gr_2_error = 0,        
				  proj_gr_3_error = 0,        
				  proj_gr_4_error = 0,        
				  proj_gr_5_error = 0,        
				  proj_gr_name_1_error = 0,        
				  proj_gr_name_2_error = 0,
				  proj_gr_name_3_error=0,
				  proj_gr_name_4_error = 0,        
				  proj_gr_name_5_error = 0,        
				  error_count = 0       
				 WHERE fk_tenant_id = @tenant_id AND         
				  job_Id = @job_id;

			-- validate project version
		UPDATE	tbu_stage_project_structure_import
		SET		fk_proj_version_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id AND (tbu_stage_project_structure_import.fk_proj_version IS NULL OR tbu_stage_project_structure_import.fk_proj_version = ''
				OR fk_proj_version NOT IN (SELECT pk_proj_version from tco_proj_version where fk_tenant_id = @tenant_id));
		-- validate project code

			UPDATE	tbu_stage_project_structure_import
			SET		fk_proj_code_error = 1, error_count = error_count + 1
			FROM	tbu_stage_project_structure_import
			WHERE	fk_tenant_id = @tenant_id AND job_Id = @job_id AND (tbu_stage_project_structure_import.fk_proj_code IS NULL OR tbu_stage_project_structure_import.fk_proj_code = '' OR 
					(fk_proj_code NOT IN (SELECT pk_project_code from tco_projects where fk_tenant_id = @tenant_id AND fk_main_project_code!='')));
		-- validate proj_grp_1 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_1_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND ((1 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_1 = '' AND proj_gr_1  IS NULL AND ISNUMERIC(proj_gr_1) = 0))
				OR (1 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_1,'')!='' AND proj_gr_1  IS NOT NULL AND ISNUMERIC(proj_gr_1) != 0)));
		-- validate proj_grp_2 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_2_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND ((2 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_2 = '' AND proj_gr_2  IS NULL AND ISNUMERIC(proj_gr_2) = 0))
				OR (2 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_2,'')!='' AND proj_gr_2  IS NOT NULL AND ISNUMERIC(proj_gr_2) != 0)));
				-- validate proj_grp_3 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_3_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND ((3 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_3 = '' AND proj_gr_3  IS NULL AND ISNUMERIC(proj_gr_3) = 0))
				OR (3 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_3,'')!='' AND proj_gr_3  IS NOT NULL AND ISNUMERIC(proj_gr_3) != 0)));
		-- validate proj_grp_4 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_4_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND ((4 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_4 != '' AND proj_gr_4  IS NOT NULL AND ISNUMERIC(proj_gr_4) = 0))
				OR (4 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_4,'')!='' AND proj_gr_4 IS NOT NULL AND ISNUMERIC(proj_gr_4) != 0)));
				-- validate proj_grp_5 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_5_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND ((5 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (proj_gr_5 = '' AND proj_gr_5  IS NULL AND ISNUMERIC(proj_gr_5) = 0))
				OR (5 not in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id = @tenant_id) AND (ISNULL(proj_gr_5,'')!='' AND proj_gr_5  IS NOT NULL AND ISNUMERIC(proj_gr_5) != 0)))
					-- validate proj_grp_name_1 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_1_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND (proj_gr_1_error = 1 
				OR ( 1 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_1_error = 0 AND (proj_gr_name_1 = '' OR proj_gr_name_1 IS NULL))));
		-- validate proj_grp_name_2 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_2_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND (proj_gr_2_error = 1 
				OR ( 2 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_2_error = 0 AND (proj_gr_name_2 = '' OR proj_gr_name_2  IS NULL))));
				-- validate proj_name_grp_3 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_3_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND (proj_gr_3_error = 1 
				OR ( 3 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_3_error = 0 AND (proj_gr_name_3 = '' OR proj_gr_name_3  IS NULL))));
		-- validate proj_grp_name_4 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_4_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND  (proj_gr_4_error = 1 
				OR ( 4 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_4_error = 0 AND (proj_gr_name_4 = '' OR proj_gr_name_4  IS NULL))));
				-- validate proj_grp_name_5 
		UPDATE	tbu_stage_project_structure_import
		SET		proj_gr_name_5_error = 1, error_count = error_count + 1
		FROM	tbu_stage_project_structure_import
		WHERE	fk_tenant_id = @tenant_id AND
				job_Id = @job_id  AND (proj_gr_5_error = 1 
				OR ( 5 in (SELECT DISTINCT(proj_level) FROM #projLevelData where fk_tenant_id =  @tenant_id) AND (proj_gr_5_error = 0 AND (proj_gr_name_5 = '' OR proj_gr_name_5  IS NULL))));
END
drop table if exists #projLevelData
RETURN 0