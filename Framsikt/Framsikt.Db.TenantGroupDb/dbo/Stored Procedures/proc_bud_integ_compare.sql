CREATE PROC proc_bud_integ_compare
@fk_tenant_id INT
,@export_id INT
,@budget_year INT
,@user_id INT

AS

--DECLARE
--@fk_tenant_id INT = 2097
--,@export_id INT = 123
--,@budget_year INT = 2023
--,@user_id INT = 9006404




DECLARE @erp_type NVARCHAR(50)

SET @erp_type = (select erp_type from gco_tenants
where pk_id = @fk_tenant_id)

DROP TABLE IF EXISTS #temp_erp_budget
CREATE TABLE #temp_erp_budget
(
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[period] [int] NOT NULL,
	[amount] [decimal](18, 2) NOT NULL
)

--Fetch from ERP log to compare
IF @erp_type = 'Agresso'
BEGIN
	INSERT INTO #temp_erp_budget
	SELECT fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, period = CASE WHEN period = 'jan' THEN CONVERT(VARCHAR(4),@budget_year)+'01'
					WHEN period = 'feb' THEN CONVERT(VARCHAR(4),@budget_year)+'02'
					WHEN period = 'mar' THEN CONVERT(VARCHAR(4),@budget_year)+'03'
					WHEN period = 'apr' THEN CONVERT(VARCHAR(4),@budget_year)+'04'
					WHEN period = 'may' THEN CONVERT(VARCHAR(4),@budget_year)+'05'
					WHEN period = 'jun' THEN CONVERT(VARCHAR(4),@budget_year)+'06'
					WHEN period = 'jul' THEN CONVERT(VARCHAR(4),@budget_year)+'07'
					WHEN period = 'aug' THEN CONVERT(VARCHAR(4),@budget_year)+'08'
					WHEN period = 'sep' THEN CONVERT(VARCHAR(4),@budget_year)+'09'
					WHEN period = 'oct' THEN CONVERT(VARCHAR(4),@budget_year)+'10'
					WHEN period = 'nov' THEN CONVERT(VARCHAR(4),@budget_year)+'11'
					WHEN period = 'dec' THEN CONVERT(VARCHAR(4),@budget_year)+'12'
					END
	, amount = SUM(Amount)
	FROM   
	   (SELECT fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, jan,	feb,	mar,	apr,	may,	jun,	jul,	aug,	sep,	oct,	nov,	dec
		  FROM tfp_erp_export_log_agresso
		  where fk_tenant_id = @fk_tenant_id
		  and export_id = @export_id) p  
	UNPIVOT  
	   (Amount FOR period IN   
		  (jan,	feb,	mar,	apr,	may,	jun,	jul,	aug,	sep,	oct,	nov,	dec)  
	)AS unpvt
	GROUP BY fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, period
END

IF @erp_type = 'Visma'
BEGIN
	INSERT INTO #temp_erp_budget
	SELECT fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, period = CASE WHEN period = 'jan' THEN CONVERT(VARCHAR(4),@budget_year)+'01'
					WHEN period = 'feb' THEN CONVERT(VARCHAR(4),@budget_year)+'02'
					WHEN period = 'mar' THEN CONVERT(VARCHAR(4),@budget_year)+'03'
					WHEN period = 'apr' THEN CONVERT(VARCHAR(4),@budget_year)+'04'
					WHEN period = 'may' THEN CONVERT(VARCHAR(4),@budget_year)+'05'
					WHEN period = 'jun' THEN CONVERT(VARCHAR(4),@budget_year)+'06'
					WHEN period = 'jul' THEN CONVERT(VARCHAR(4),@budget_year)+'07'
					WHEN period = 'aug' THEN CONVERT(VARCHAR(4),@budget_year)+'08'
					WHEN period = 'sep' THEN CONVERT(VARCHAR(4),@budget_year)+'09'
					WHEN period = 'oct' THEN CONVERT(VARCHAR(4),@budget_year)+'10'
					WHEN period = 'nov' THEN CONVERT(VARCHAR(4),@budget_year)+'11'
					WHEN period = 'dec' THEN CONVERT(VARCHAR(4),@budget_year)+'12'
					END
	, amount = SUM(Amount)
	FROM   
	   (SELECT fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, jan,	feb,	mar,	apr,	may,	jun,	jul,	aug,	sep,	oct,	nov,	dec
		  FROM tfp_erp_export_log_visma
		  where fk_tenant_id = @fk_tenant_id
		  and export_id = @export_id) p  
	UNPIVOT  
	   (Amount FOR period IN   
		  (jan,	feb,	mar,	apr,	may,	jun,	jul,	aug,	sep,	oct,	nov,	dec)  
	)AS unpvt
	GROUP BY fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, period
END


--Calculate differences
DROP TABLE IF EXISTS #temp_diff_list
select fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,period
,erp_log_amt = SUM(erp_log_amt), customer_control_amt = SUM(customer_control_amt), diff  = SUM(erp_log_amt-customer_control_amt)
INTO #temp_diff_list
FROM
(
	select fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,period,erp_log_amt = amount, customer_control_amt = 0
	from #temp_erp_budget
	UNION ALL
	select fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,period,erp_log_amt = 0, customer_control_amt = amount
	from tbu_stage_budget_integrations
	where fk_tenant_id = @fk_tenant_id
	AND budget_year = @budget_year
	and [user_id] = @user_id
	) A
GROUP BY fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,period
HAVING SUM(erp_log_amt-customer_control_amt) <> 0


--Table that shows differences against the export id you selected
select * from #temp_diff_list
