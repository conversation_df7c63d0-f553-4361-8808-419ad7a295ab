
CREATE OR ALTER PROCEDURE [dbo].[prcGenerateInvestmentFinplandoc]
		@fk_tenant_id int,
		@budget_year int,
		@user_id INT

	AS


	DECLARE @org_version varchar(25)
	DECLARE @proj_version varchar(25)

	DECLARE @param2 nvarchar(500) 
	DECLARE @param3 nvarchar(500) 
	DECLARE @param4 nvarchar(500) 
	DECLARE @param10 nvarchar(500) 
	DECLARE @param11 nvarchar(500) 
	DECLARE @param12 nvarchar(500) 
	DECLARE @param13 nvarchar(500) 
	DECLARE @param14 nvarchar(500) 
	DECLARE @param15 nvarchar(500) 
	DECLARE @param16 nvarchar(500) 
	DECLARE @param17 nvarchar(500) 
	DECLARE @param18 nvarchar(500)
	DECLARE @param19 nvarchar(500)
	DECLARE @param20 nvarchar(500)
	DECLARE @param21 nvarchar(500)
	DECLARE @param22 nvarchar(500)
	DECLARE @param23 nvarchar(500)
	DECLARE @default_change_id INT
	DECLARE @langstring_mainproject NVARCHAR(80) 
	
	DECLARE @graph_group varchar(100) 
	DECLARE @graph_org_group varchar(100)
	DECLARE @language VARCHAR(10)

	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)

	
	DECLARE @tbl_reporting_line as table (report varchar(50)
	, fk_kostra_account_code VARCHAR(25)
	, line_group_id INT
	, line_group varchar(200)
	, line_item_id INT
	, line_item varchar(150))


	INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)
	SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id, 
	line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),
	line_item_id,
	line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))
	FROM gmd_reporting_line rl
	LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language
	LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language
	LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @fk_tenant_id AND tg.Language = @language
	LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @fk_tenant_id AND tI.Language = @language

	SET @param2  = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'FINPLAN_LEVEL_1')
	SET @param3  = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'FINPLAN_LEVEL_2') 
	SET @param4  = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_USE_HEADER_ORG')
	--SET @param10 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_SUB_HEADER')
	--SET @param11 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_SUM_CODE')
	--SET @param12 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_SUM_CODE_2')
	--SET @param13 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_SUM_CODE_SA')
	SET @param14 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_HIDE_RUNNING')
	SET @param15 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_10YEARS_RUNNING')
	SET @param16 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'FP_INV_NET_AMOUNTS')
	SET @param17 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BMDOC_INV_EXPAND_RUNNING')
	SET @param18 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'FINPLAN_INVESTMENT_LEVEL')
	SET @param19 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'BM_INV_DOC_SHOW_ONLY_1010')

	SET @param10 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 10 AND table_id = 'BMInvestmentsAll')
	SET @param11 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 11 AND table_id = 'BMInvestmentsAll')
	SET @param12 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 12 AND table_id = 'BMInvestmentsAll')
	SET @param13 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 13 AND table_id = 'BMInvestmentsSA')
	SET @param20 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 14 AND table_id = 'BMInvestmentsSA')
	SET @param21 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 15 AND table_id = 'BMInvestmentsSA')
	SET @param22 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 1 AND table_id = 'BlistInvestment')
	SET @param23 = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id ANd level_id = 2 AND table_id = 'BlistInvestment')

	SET @graph_group = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id AND level_id = 1 AND table_id = 'BMInvestmentGraph')
	SET @graph_org_group = (SELECT MIN(grouping_id) FROM tco_table_grouping WHERE fk_tenant_id = @fk_tenant_id AND level_id = 2 AND table_id = 'BMInvestmentGraph')


	SET @langstring_mainproject = 
		(
		SELECT SUBSTRING(isnull(s3.description, ISNULL(s2.description, s.description)),1,80) 
		FROM gco_language_strings s
		LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language
		LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @fk_tenant_id
		WHERE s.id = 'finproj_popup_mainProjMissing' AND s.language = @language)

	IF @langstring_mainproject IS NULL 
	BEGIN
	 SET @langstring_mainproject = 'Missing langstring finproj_popup_mainProjMissing'
	END

	SET @default_change_id = (
	SELECT top 1 pk_change_id FROM tfp_budget_changes ch
	JOIN tco_budget_phase ph ON ch.fk_tenant_id = ph.fk_tenant_id AND ch.fk_budget_phase_id = ph.pk_budget_phase_id
	WHERE ch.fk_tenant_id = @fk_tenant_id AND ch.budget_year = @budget_year
	ORDER BY ph.sort_order, ch.pk_change_id)


	IF @default_change_id is null 
	BEGIN 
	PRINT 'year has not been started yet. One budget change must exist'
	RETURN
	END

	SET @org_version = (SELECT pk_org_version FROM tco_org_version WHERE fk_tenant_id = @fk_tenant_id AND @budget_year*100+1 BETWEEN period_from AND period_to)
	SET @proj_version = (select pk_proj_version from  tco_proj_version WHERE fk_tenant_id = @fk_tenant_id AND @budget_year BETWEEN period_from AND period_to)

	if @proj_version is null 
	begin
	SET @proj_version = ''
	end

	drop table if exists #hlptab
	drop table if exists #hlptab2
	drop table if exists #hlptab3

	SELECT *
	INTO #hlptab
	FROM
	(
	select 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,mp.is_temp
	,mp.completion_date
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_project_code
	,p.project_name
	,pt.fk_change_id
	,pt.fk_alter_code
	,fk_adjustment_code = CONVERT(VARCHAR(4),@budget_year)+'VBUD' --CASE WHEN bc.budget_year = @budget_year THEN CONVERT(VARCHAR(4),@budget_year)+'ØP' ELSE CONVERT(VARCHAR(4),@budget_year)+'IB' END
	,fk_prog_code = isnull(p.fk_prog_code,'1')
	,fk_user_adjustment_code = CONVERT(VARCHAR(4),@budget_year)+'VBUD' --CASE WHEN bc.budget_year = @budget_year THEN CONVERT(VARCHAR(4),@budget_year)+'ØP' ELSE CONVERT(VARCHAR(4),@budget_year)+'IB' END
	,user_adj_desc = ''
	,amount = SUM(pt.amount)
	,header_dept = mp.fk_department_code
	,header_function = ISNULL(mp.fk_function_code,'')
	,ps.approval_reference
	,ps.approval_ref_url
	,ps.original_finish_year
	,BC.org_budget_flag
	,pt.vat_rate
	,pt.vat_refund
	,mp.priority
	,pt.updated_by
	,fk_investment_phase_id = ISNULL(mp.fk_investment_phase_id,0)
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,PS.goalId
	,PS.targetId
	,PS.tags
	,trans_desc	 = pt.description
	,pt.updated
	,bud_process = CASE	WHEN bc.budget_year = @budget_year THEN CONVERT(VARCHAR(4),@budget_year)+'ØP'
							WHEN bc.budget_year < @budget_year AND UAD.status = 0 AND UAD.include_in_calculation = 1 THEN CONVERT(VARCHAR(4),@budget_year)+'IGB'
							ELSE CONVERT(VARCHAR(4),@budget_year)+'IB' END
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, 1 as org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year < @budget_year
			UNION
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	where PT.fk_tenant_id = @fk_tenant_id
	GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
	pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,
	pt.year,p.fk_prog_code,pt.fk_user_adjustment_code, UAD.description,UAD.prefix_adjCode,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, BC.org_budget_flag, 
	pt.vat_rate, pt.vat_refund,	pt.free_dim_1, pt.free_dim_2, pt.free_dim_3, pt.free_dim_4, mp.priority,pt.updated_by, mp.fk_investment_phase_id,UAD.status
	,UAD.include_in_calculation, BC.budget_year
	,PS.goalId
	,PS.targetId
	,PS.tags
	,pt.description
	,pt.updated

	UNION ALL
	select 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,mp.is_temp
	,mp.completion_date
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_project_code
	,p.project_name
	,pt.fk_change_id
	,pt.fk_alter_code
	,pt.fk_adjustment_code
	,fk_prog_code = isnull(p.fk_prog_code,'1')
	,fk_user_adjustment_code = CASE WHEN UAD.prefix_adjCode IS NULL OR UAD.prefix_adjCode = '' THEN pt.fk_user_adjustment_code ELSE UAD.prefix_adjCode END
	,user_adj_desc = UAD.description
	,amount = SUM(pt.amount)
	,header_dept = mp.fk_department_code
	,header_function = ISNULL(mp.fk_function_code,'')
	,ps.approval_reference
	,ps.approval_ref_url
	,ps.original_finish_year
	,BC.org_budget_flag
	,pt.vat_rate
	,pt.vat_refund
	,mp.priority
	,pt.updated_by
	,fk_investment_phase_id = ISNULL(mp.fk_investment_phase_id,0)
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,PS.goalId
	,PS.targetId
	,PS.tags
	,trans_desc	 = pt.description
	,pt.updated
	,bud_process = CONVERT(VARCHAR(4),@budget_year)+'RB'
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 0
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	where PT.fk_tenant_id = @fk_tenant_id
	GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
	pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,
	pt.year,p.fk_prog_code,pt.fk_user_adjustment_code, UAD.description,UAD.prefix_adjCode,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, BC.org_budget_flag, 
	pt.vat_rate, pt.vat_refund,	pt.free_dim_1, pt.free_dim_2, pt.free_dim_3, pt.free_dim_4, mp.priority,pt.updated_by, mp.fk_investment_phase_id,UAD.status
	,UAD.include_in_calculation, BC.budget_year
	,PS.goalId
	,PS.targetId
	,PS.tags
	,pt.description
	,pt.updated
	) TRANS


	update h SET h.project_name = 'Prosjektet mangler oppsett for budsjettåret'
	FROM #hlptab h
	JOIN tco_projects P on h.fk_tenant_id = P.fk_tenant_id and h.fk_project_code = P.pk_project_code and @budget_year NOT BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	WHERE h.project_name is null




	UPDATE a SET a.fk_change_id = @default_change_id
	FROM #hlptab a
	JOIN tfp_budget_changes ch ON ch.fk_tenant_id = a.fk_tenant_id AND a.fk_change_id = ch.pk_change_id and ch.budget_year < @budget_year
	; 

	SELECT fk_tenant_id,@budget_year AS budget_year,pk_main_project_code,main_project_name,inv_status,
	is_temp,completion_date,fk_account_code,fk_department_code,
	fk_function_code,free_dim_1, free_dim_2, free_dim_3,free_dim_4,fk_project_code,project_name,fk_change_id,
	fk_alter_code,fk_adjustment_code,fk_user_adjustment_code, user_adj_desc,
	fk_prog_code,header_dept, header_function,approval_reference, approval_ref_url,original_finish_year,
	year_0_amount = CASE WHEN year = @budget_year-1 THEN amount else 0 end,
	year_1_amount = CASE WHEN year = @budget_year THEN amount else 0 end,
	year_2_amount = CASE WHEN year = @budget_year+1 THEN amount else 0 end,
	year_3_amount = CASE WHEN year = @budget_year+2 THEN amount else 0 end,
	year_4_amount = CASE WHEN year = @budget_year+3 THEN amount else 0 end,
	year_5_amount = CASE WHEN year = @budget_year+4 THEN amount else 0 end,
	year_6_amount = CASE WHEN year = @budget_year+5 THEN amount else 0 end,
	year_7_amount = CASE WHEN year = @budget_year+6 THEN amount else 0 end,
	year_8_amount = CASE WHEN year = @budget_year+7 THEN amount else 0 end,
	year_9_amount = CASE WHEN year = @budget_year+8 THEN amount else 0 end,
	year_10_amount = CASE WHEN year = @budget_year+9 THEN amount else 0 end,
	year_11_amount = CASE WHEN year = @budget_year+10 THEN amount else 0 end,
	year_12_amount = CASE WHEN year = @budget_year+11 THEN amount else 0 end,
	year_13_amount = CASE WHEN year = @budget_year+12 THEN amount else 0 end,
	year_14_amount = CASE WHEN year = @budget_year+13 THEN amount else 0 end,
	year_15_amount = CASE WHEN year = @budget_year+14 THEN amount else 0 end,
	year_16_amount = CASE WHEN year = @budget_year+15 THEN amount else 0 end,
	year_17_amount = CASE WHEN year = @budget_year+16 THEN amount else 0 end,
	year_18_amount = CASE WHEN year = @budget_year+17 THEN amount else 0 end,
	year_19_amount = CASE WHEN year = @budget_year+18 THEN amount else 0 end,
	year_20_amount = CASE WHEN year = @budget_year+19 THEN amount else 0 end,
	actual_amt_year = CONVERT(DEC(18,2),0),
	net_cost = CASE WHEN inv_status = 2 OR year = -1 OR year = -2 THEN 0 else amount END,
	previously_budgeted = CASE WHEN year < @budget_year AND year != -1 AND year != -2 THEN amount ELSE 0 END,
	approved_cost = CASE WHEN year = -1 THEN amount else 0 end, 
	cost_estimate_p50 = CASE WHEN year = -2 THEN amount else 0 end, 
	org_budget_flag, vat_rate, vat_refund,
	priority, updated_by, fk_investment_phase_id
	,adjustment_code_status
	,goalId
	,targetId
	,tags
	,trans_desc
	,updated
	,bud_process
	INTO #hlptab2
	 FROM #hlptab


INSERT INTO #hlptab2 (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,inv_status,
is_temp,completion_date,fk_account_code,fk_department_code,fk_function_code,free_dim_1,free_dim_2,
free_dim_3,free_dim_4,fk_project_code,project_name,fk_change_id,fk_alter_code,fk_adjustment_code,
fk_user_adjustment_code,user_adj_desc,fk_prog_code,header_dept,header_function,approval_reference,
approval_ref_url,original_finish_year,year_0_amount,year_1_amount,year_2_amount,year_3_amount,
year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,
year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,
year_18_amount,year_19_amount,year_20_amount,actual_amt_year,net_cost,previously_budgeted,
approved_cost,cost_estimate_p50,org_budget_flag,vat_rate,vat_refund,priority,updated_by,fk_investment_phase_id,
adjustment_code_status,goalId,targetId,tags,trans_desc,updated,bud_process)
SELECT 
a.fk_tenant_id,
@budget_year AS budget_year,
mp.pk_main_project_code,
mp.main_project_name,
mp.inv_status,
mp.is_temp,
completion_date = ISNULL(mp.completion_date, '0001-01-01'),
a.fk_account_code,
a.department_code,
a.fk_function_code,
a.free_dim_1, 
a.free_dim_2, 
a.free_dim_3,
a.free_dim_4,
a.fk_project_code,
project_name = ISNULL(pr.project_name, ''),
fk_change_id = 0,
fk_alter_code = '',
fk_adjustment_code = '',
fk_user_adjustment_code = '', 
user_adj_desc = '',
fk_prog_code = '',
header_dept = ISNULL(mp.fk_department_code, ''), 
header_function = ISNULL(mp.fk_function_code, ''),
approval_reference = ISNULL(ps.approval_reference, ''),
approval_ref_url = ISNULL(ps.approval_ref_url, ''),
original_finish_year = ISNULL(ps.original_finish_year, 0),
year_0_amount = 0,
year_1_amount = 0,
year_2_amount = 0,
year_3_amount = 0,
year_4_amount = 0,
year_5_amount = 0,
year_6_amount = 0,
year_7_amount = 0,
year_8_amount = 0,
year_9_amount = 0,
year_10_amount =0,
year_11_amount =0,
year_12_amount =0,
year_13_amount =0,
year_14_amount =0,
year_15_amount =0,
year_16_amount =0,
year_17_amount =0,
year_18_amount =0,
year_19_amount =0,
year_20_amount =0,
actual_amt_year = a.amount,
net_cost = 0,
previously_budgeted = 0,
approved_cost = 0, 
cost_estimate_p50 = 0,
org_budget_flag = 0, 
vat_rate = 0, 
vat_refund = 0,
priority = ISNULL(mp.priority, 0),
a.updated_by, 
fk_investment_phase_id = ISNULL(mp.fk_investment_phase_id, 0),
adjustment_code_status = 'Godkjent',
goalId = ISNULL(ps.goalId, '********-0000-0000-0000-************'),
targetId = ISNULL(ps.targetId, '********-0000-0000-0000-************'),
tags = ISNULL(ps.tags, ''),
trans_desc = ISNULL(a.description, ''),
updated = a.updated,
bud_process = ''
FROM tfp_accounting_data a
LEFT JOIN tco_projects pr ON a.fk_tenant_id = pr.fk_tenant_id AND a.fk_project_code = pr.pk_project_code AND a.gl_year BETWEEN datepart(year,pr.date_from) and datepart(year,pr.date_to)
LEFT JOIN tco_main_projects mp ON pr.fk_tenant_id = mp.fk_tenant_id AND pr.fk_main_project_code = mp.pk_main_project_code AND a.gl_year between DATEPART(year, mp.budget_year_from) and DATEPART (year,mp.budget_year_to)
LEFT JOIN tco_accounts e ON a.fk_account_code = e.pk_account_code AND a.fk_tenant_id = e.pk_tenant_id AND a.gl_year BETWEEN  datepart(year,e.dateFrom) and datepart(year,e.dateTo)
LEFT JOIN gmd_reporting_line r ON r.fk_kostra_account_code = e.fk_kostra_account_code AND r.report = '55_OVINV'
LEFT JOIN tco_functions f ON a.fk_function_code = f.pk_function_code AND a.fk_tenant_id = f.pk_tenant_id AND a.gl_year BETWEEN  datepart(year,f.dateFrom) and datepart(year,f.dateTo)
LEFT JOIN tco_departments DP ON DP.pk_department_code = a.department_code AND DP.fk_tenant_id = a.fk_tenant_id AND a.gl_year between DP.year_from and DP.year_to
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = a.fk_tenant_id AND PS.pk_main_project_code = mp.pk_main_project_code
LEFT JOIN tco_free_dim_values fd1 ON a.fk_tenant_id = fd1.fk_tenant_id AND a.free_dim_1 = fd1.free_dim_code AND fd1.free_dim_column = 'free_dim_1'
LEFT JOIN tco_free_dim_values fd2 ON a.fk_tenant_id = fd2.fk_tenant_id AND a.free_dim_2 = fd2.free_dim_code AND fd2.free_dim_column = 'free_dim_2'
LEFT JOIN tco_free_dim_values fd3 ON a.fk_tenant_id = fd3.fk_tenant_id AND a.free_dim_3 = fd3.free_dim_code AND fd3.free_dim_column = 'free_dim_3'
LEFT JOIN tco_free_dim_values fd4 ON a.fk_tenant_id = fd4.fk_tenant_id AND a.free_dim_4 = fd4.free_dim_code AND fd4.free_dim_column = 'free_dim_4'
WHERE a.fk_tenant_id = @fk_tenant_id and a.gl_year = @budget_year


	 -- This result set can be put into a permanenttable that the view can build on (should add finplan levels, investment description + other header info as well to make the query efficient
 
	 SELECT a.fk_tenant_id,a.budget_year,fp_level_1_value = CONVERT(NVARCHAR(25),''),fp_level_1_name= CONVERT(NVARCHAR(100),''),
	 fp_level_2_value = CONVERT(NVARCHAR(25),''), fp_level_2_name = CONVERT(NVARCHAR(100),''),
	 pk_main_project_code = CASE  
		WHEN a.inv_status IS NULL AND rl.line_item_id IN (1010,1020,1030,1040) THEN 'ZZZ'
		WHEN a.inv_status IS NULL AND rl.line_item_id NOT IN  (1010,1020,1030,1040) THEN '' 
		ELSE a.pk_main_project_code end,
	 main_project_name = CASE  
		WHEN a.inv_status IS NULL AND rl.line_item_id IN (1010,1020,1030,1040) THEN @langstring_mainproject + ' - ' + a.fk_project_code
		WHEN a.inv_status IS NULL AND rl.line_item_id NOT IN  (1010,1020,1030,1040) THEN '' 
		ELSE a.main_project_name end,
	 inv_status = CASE  
		WHEN a.inv_status IS NULL AND rl.line_item_id IN (1010,1020,1030,1040) THEN 0 
		WHEN a.inv_status IS NULL AND rl.line_item_id NOT IN  (1010,1020,1030,1040) THEN 100 
		ELSE a.inv_status end,
	 ISNULL(a.is_temp,0) AS is_temp,
	 ISNULL(a.completion_date,'1900-01-01') AS completion_date,
	 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.free_dim_1, a.free_dim_2, a.free_dim_3,a.free_dim_4,
	 ISNULL(a.fk_project_code,'ZZ') AS fk_project_code,ISNULL(a.project_name,'Uten prosjekt') AS project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,
	 a.fk_user_adjustment_code, a.user_adj_desc,
	 a.fk_prog_code,a.header_dept, a.header_function,
	 oh.org_id_1 as header_org_id_1, oh.org_name_1 as header_org_name_1, oh.org_id_2  as header_org_id_2, oh.org_name_2 as header_org_name_2,
	 oh.org_id_3  as header_org_id_3, oh.org_name_3 as header_org_name_3,oh.org_id_4  as header_org_id_4, oh.org_name_4 as header_org_name_4,
	 oh.org_id_5  as header_org_id_5, oh.org_name_5 as header_org_name_5,
	 od.org_id_1 as detail_org_id_1, od.org_name_1 as detail_org_name_1, od.org_id_2  as detail_org_id_2, od.org_name_2 as detail_org_name_2,
	 od.org_id_3  as detail_org_id_3, od.org_name_3 as detail_org_name_3,od.org_id_4  as detail_org_id_4, od.org_name_4 as detail_org_name_4,
	 od.org_id_5  as detail_org_id_5, od.org_name_5 as detail_org_name_5,
	 ISNULL(sh.service_id_1,'')  as header_service_id_1, ISNULL(sh.service_name_1,'') as header_service_name_1, ISNULL(sh.service_id_2,'')  as header_service_id_2, ISNULL(sh.service_name_2,'') as header_service_name_2,
	 ISNULL(sh.service_id_3,'')  as header_service_id_3, ISNULL(sh.service_name_3,'') as header_service_name_3, ISNULL(sh.service_id_4,'')  as header_service_id_4, ISNULL(sh.service_name_4,'') as header_service_name_4,
	 ISNULL(sh.service_id_5,'')  as header_service_id_5, ISNULL(sh.service_name_5,'') as header_service_name_5,														
	 ISNULL(sd.service_id_1,'')  as detail_service_id_1, ISNULL(sd.service_name_1,'') as detail_service_name_1, ISNULL(sd.service_id_2,'')  as detail_service_id_2, ISNULL(sd.service_name_2,'') as detail_service_name_2,
	 ISNULL(sd.service_id_3,'')  as detail_service_id_3, ISNULL(sd.service_name_3,'') as detail_service_name_3, ISNULL(sd.service_id_4,'')  as detail_service_id_4, ISNULL(sd.service_name_4,'') as detail_service_name_4,
	 ISNULL(sd.service_id_5,'')  as detail_service_id_5, ISNULL(sd.service_name_5,'') as detail_service_name_5,
	 ISNULL(ip.description,'') as program_code_description,rl.line_group_id, rl.line_item_id,rl.line_group, rl.line_item
	 ,year_0_amount = sum(year_0_amount) 
	 ,year_1_amount = sum(year_1_amount) 
	 ,year_2_amount = sum(year_2_amount) 
	 ,year_3_amount = sum(year_3_amount) 
	 ,year_4_amount = sum(year_4_amount) 
	 ,year_5_amount = sum(year_5_amount) 
	 ,year_6_amount = sum(year_6_amount) 
	 ,year_7_amount = sum(year_7_amount) 
	 ,year_8_amount = sum(year_8_amount) 
	 ,year_9_amount = sum(year_9_amount) 
	 ,year_10_amount = sum(year_10_amount)
	 ,year_11_amount = sum(year_11_amount)
	 ,year_12_amount = sum(year_12_amount)
	 ,year_13_amount = sum(year_13_amount)
	 ,year_14_amount = sum(year_14_amount)
	 ,year_15_amount = sum(year_15_amount)
	 ,year_16_amount = sum(year_16_amount)
	 ,year_17_amount = sum(year_17_amount)
	 ,year_18_amount = sum(year_18_amount)
	 ,year_19_amount = sum(year_19_amount)
	 ,year_20_amount = sum(year_20_amount)
	 ,sum_finplan = convert(dec(18,2),0),
	finished_year = isnull(DATEPART(YEAR, completion_date),@budget_year), 
	display_proj_columns = CASE WHEN a.inv_status = 2 AND @param14 = 'TRUE' THEN 0 ELSE 1 END,
	SUM(approved_cost) as approved_cost, 
	SUM(cost_estimate_p50) AS cost_estimate_p50,
	ISNULL(a.approval_reference,'') AS approval_reference,
	ISNULL(a.approval_ref_url,'') AS approval_ref_url,
	ISNULL(a.original_finish_year,0) AS original_finish_year,
	inv_year_1_amount = convert(dec(18,2),0), 
	inv_year_2_amount = convert(dec(18,2),0), 
	inv_year_3_amount = convert(dec(18,2),0), 
	inv_year_4_amount = convert(dec(18,2),0), 
	inv_year_5_amount = convert(dec(18,2),0), 
	inv_year_6_amount = convert(dec(18,2),0), 
	inv_year_7_amount = convert(dec(18,2),0), 
	inv_year_8_amount = convert(dec(18,2),0),
	inv_year_9_amount = convert(dec(18,2),0), 
	inv_year_10_amount = convert(dec(18,2),0),
	fin_year_1_amount = convert(dec(18,2),0), 
	fin_year_2_amount = convert(dec(18,2),0), 
	fin_year_3_amount = convert(dec(18,2),0), 
	fin_year_4_amount = convert(dec(18,2),0), 
	fin_total_amount = convert(dec(18,2),0),
	SUM(previously_budgeted) as previously_budgeted,
	gross_cost = convert(dec(18,2),0),
	financed_amount = convert(dec(18,2),0),
	SUM(net_cost) AS net_cost,
	sub_header_code = CONVERT(NVARCHAR(25),''),sub_header_description = CONVERT(NVARCHAR(500),''),
	sum_code = CONVERT(NVARCHAR(25),''),sum_level = CONVERT(NVARCHAR(500),''),sum_code_2 = CONVERT(NVARCHAR(25),''),sum_description_2 = CONVERT(NVARCHAR(500),''),
	sub_level_sa_code = CONVERT(NVARCHAR(25),''),sub_level_sa_description= CONVERT(NVARCHAR(500),''),
	type = CASE WHEN rl.line_item_id IN (1010,1020,1030,1040) THEN 'i' ELSE 'f' END,
	proj_gr_1 = ISNULL(ph.proj_gr_1,'ZZ'),
	proj_gr_2 = ISNULL(ph.proj_gr_2,'ZZ'),
	proj_gr_3 = ISNULL(ph.proj_gr_3,'ZZ'),
	proj_gr_4 = ISNULL(ph.proj_gr_4,'ZZ'),
	proj_gr_5 = ISNULL(ph.proj_gr_5,'ZZ'),
	proj_gr_name_1 = ISNULL(ph.proj_gr_name_1, 'Ingen verdi'),
	proj_gr_name_2 = ISNULL(ph.proj_gr_name_2, 'Ingen verdi'),
	proj_gr_name_3 = ISNULL(ph.proj_gr_name_3, 'Ingen verdi'),
	proj_gr_name_4 = ISNULL(ph.proj_gr_name_4, 'Ingen verdi'),
	proj_gr_name_5 = ISNULL(ph.proj_gr_name_5, 'Ingen verdi'), org_budget_flag,
	graph_type = CONVERT(NVARCHAR(25),''),
	graph_l1_value = CONVERT(NVARCHAR(25),''),
	graph_l1_name = CONVERT(NVARCHAR(100),''), 
	graph_l2_value = CONVERT(NVARCHAR(25),''),
	graph_l2_name = CONVERT(NVARCHAR(100),''), 
	graph_org_id = CONVERT(NVARCHAR(25),''),
	graph_org_name = CONVERT(NVARCHAR(100),''),
	a.vat_rate, a.vat_refund, ISNULL(a.priority,0) AS priority, a.updated_by, a.fk_investment_phase_id
	,adjustment_code_status
	,goalid
	,targetid
	,tags
	,trans_desc
	,oe_flag = 0
	,a.updated
	,bud_process
	,dynamic_gr_1 = CONVERT(NVARCHAR(500),'')
	,dynamic_gr_2 = CONVERT(NVARCHAR(500),'')
	,blist_gr_1 = CONVERT(NVARCHAR(500),'')
	,blist_gr_2 = CONVERT(NVARCHAR(500),'')
	,actual_amt_year = sum(actual_amt_year)
	,sum_finplan_net = convert(dec(18,2),0)
	 INTO #hlptab3
	 FROM #hlptab2 a
	 LEFT JOIN tco_org_hierarchy oh ON a.header_dept = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version
	 JOIN tco_org_hierarchy od ON a.fk_department_code = od.fk_department_code AND a.fk_tenant_id = od.fk_tenant_id AND od.fk_org_version = @org_version
	 LEFT JOIN tco_service_values sh ON a.header_function = sh.fk_function_code AND a.fk_tenant_id = sh.fk_tenant_id
	 LEFT JOIN tco_service_values sd ON a.fk_function_code = sd.fk_function_code AND a.fk_tenant_id = sd.fk_tenant_id
	 LEFT JOIN tco_inv_program ip ON a.fk_prog_code = ip.pk_prog_code AND a.fk_tenant_id = ip.fk_tenant_id
	 LEFT JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
	 LEFT JOIN tco_proj_hierarchy ph ON a.fk_tenant_id = ph.fk_tenant_id AND a.fk_project_code = ph.fk_project_code AND ph.fk_proj_version = @proj_version
	 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
	 group by a.fk_tenant_id,a.budget_year,a.pk_main_project_code,a.main_project_name,a.inv_status,a.is_temp,completion_date,
	 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,
	 a.fk_prog_code,a.header_dept, a.header_function,
	 oh.org_id_1, oh.org_name_1, oh.org_id_2, oh.org_name_2,
	 oh.org_id_3, oh.org_name_3,oh.org_id_4, oh.org_name_4,
	 oh.org_id_5, oh.org_name_5,
	 od.org_id_1, od.org_name_1, od.org_id_2, od.org_name_2,
	 od.org_id_3, od.org_name_3, od.org_id_4, od.org_name_4,
	 od.org_id_5, od.org_name_5,  sh.service_id_1, sh.service_name_1, sh.service_id_2, sh.service_name_2,
	 sh.service_id_3, sh.service_name_3,sh.service_id_4, sh.service_name_4,
	 sh.service_id_5, sh.service_name_5,
	 sd.service_id_1, sd.service_name_1, sd.service_id_2, sd.service_name_2,
	 sd.service_id_3, sd.service_name_3,sd.service_id_4, sd.service_name_4,
	 sd.service_id_5, sd.service_name_5,
	ip.description,rl.line_group_id, rl.line_item_id,a.approval_reference,a.approval_ref_url, a.original_finish_year,
	ph.proj_gr_1, ph.proj_gr_2, ph.proj_gr_3, ph.proj_gr_4, ph.proj_gr_5,
	ph.proj_gr_name_1, ph.proj_gr_name_2, ph.proj_gr_name_3, ph.proj_gr_name_4, ph.proj_gr_name_5, org_budget_flag,
	rl.line_group, rl.line_item,a.free_dim_1, a.free_dim_2, a.free_dim_3,a.free_dim_4, a.vat_rate, a.vat_refund,
	a.priority, a.updated_by, a.fk_user_adjustment_code, a.user_adj_desc, a.fk_investment_phase_id
	,adjustment_code_status
	,goalid
	,targetid
	,tags
	,trans_desc
	,a.updated
	,bud_process

	UPDATE #hlptab3 SET 
	header_org_id_1 = detail_org_id_1,
	header_org_id_2 = detail_org_id_2,
	header_org_id_3 = detail_org_id_3,
	header_org_id_4 = detail_org_id_4,
	header_org_id_5 = detail_org_id_5,
	header_org_name_1 = detail_org_name_1,
	header_org_name_2 = detail_org_name_2,
	header_org_name_3 = detail_org_name_3,
	header_org_name_4 = detail_org_name_4,
	header_org_name_5 = detail_org_name_5,
	header_dept = fk_department_code
	WHERE inv_status = 100 or header_org_id_1 is null;




	PRINT 'Update amount columns'

	UPDATE #hlptab3 SET 
	sum_finplan = CASE 
		WHEN @param16 = 'TRUE' AND line_item_id IN (1010,1020,1030,1040,2010) THEN  ISNULL(year_1_amount,0)+ ISNULL(year_2_amount,0)+ ISNULL(year_3_amount,0)+ ISNULL(year_4_amount,0) 
		WHEN line_item_id IN (1010,1020,1030,1040) THEN  ISNULL(year_1_amount,0)+ ISNULL(year_2_amount,0)+ ISNULL(year_3_amount,0)+ ISNULL(year_4_amount,0) 
		ELSE 0 END,
	fin_year_1_amount = CASE WHEN line_item_id NOT IN (1010,1020,1030,1040,2010)   THEN ISNULL(year_1_amount,0) ELSE 0 END, 
	fin_year_2_amount = CASE WHEN line_item_id NOT IN (1010,1020,1030,1040,2010)   THEN ISNULL(year_2_amount,0) ELSE 0 END, 
	fin_year_3_amount = CASE WHEN line_item_id NOT IN (1010,1020,1030,1040,2010)   THEN ISNULL(year_3_amount,0) ELSE 0 END, 
	fin_year_4_amount = CASE WHEN line_item_id NOT IN (1010,1020,1030,1040,2010)   THEN ISNULL(year_4_amount,0) ELSE 0 END,
	gross_cost = CASE
		WHEN inv_status = 2 THEN 0
		WHEN @param16 = 'TRUE' AND line_item_id IN (1010,1020,1030,1040,2010) THEN net_cost
		WHEN line_item_id IN (1010,1020,1030,1040) THEN net_cost
		ELSE 0
		END,
	financed_amount = CASE
		WHEN line_item_id NOT IN (1010,1020,1030,1040,2010) THEN net_cost
		ELSE 0
		END,
	previously_budgeted = CASE 
		WHEN line_item_id IN (1010,1020,1030,1040) THEN previously_budgeted
		ELSE 0
		END,
	sum_finplan_net = ISNULL(year_1_amount,0)+ ISNULL(year_2_amount,0)+ ISNULL(year_3_amount,0)+ ISNULL(year_4_amount,0)
	;

	UPDATE #hlptab3 SET fin_total_amount = fin_year_1_amount + fin_year_2_amount + fin_year_3_amount + fin_year_4_amount


	IF @param16 = 'TRUE'
	-- param16 is FP_INV_NET_AMOUNTS
	BEGIN

	UPDATE #hlptab3 SET
	inv_year_1_amount = year_1_amount, 
	inv_year_2_amount = year_2_amount, 
	inv_year_3_amount = year_3_amount, 
	inv_year_4_amount = year_4_amount, 
	inv_year_5_amount = year_5_amount,
	inv_year_6_amount = year_6_amount, 
	inv_year_7_amount = year_7_amount, 
	inv_year_8_amount = year_8_amount, 
	inv_year_9_amount = year_9_amount, 
	inv_year_10_amount = year_10_amount
	WHERE line_item_id  IN (1010,1020,1030,1040,2010) 

	END

	ELSE 

	BEGIN
	UPDATE #hlptab3 SET
	inv_year_1_amount = year_1_amount, 
	inv_year_2_amount = year_2_amount, 
	inv_year_3_amount = year_3_amount, 
	inv_year_4_amount = year_4_amount, 
	inv_year_5_amount = year_5_amount,
	inv_year_6_amount = year_6_amount, 
	inv_year_7_amount = year_7_amount, 
	inv_year_8_amount = year_8_amount, 
	inv_year_9_amount = year_9_amount, 
	inv_year_10_amount = year_10_amount
	WHERE line_item_id IN (1010,1020,1030,1040) 
	END


	PRINT 'Find finplan levels'

	UPDATE #hlptab3 SET fp_level_1_value = 
	CASE	WHEN @param4 = 'TRUE' AND @param2 = 'org_id_1' THEN ISNULL(header_org_id_1,detail_org_id_1)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_2' THEN ISNULL(header_org_id_2,detail_org_id_2)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_3' THEN ISNULL(header_org_id_3,detail_org_id_3)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_4' THEN ISNULL(header_org_id_4,detail_org_id_4)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_5' THEN ISNULL(header_org_id_5,detail_org_id_5)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_1' THEN ISNULL(header_service_id_1,detail_service_id_1)		
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_2' THEN ISNULL(header_service_id_2,detail_service_id_2)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_3' THEN ISNULL(header_service_id_3,detail_service_id_3)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_4' THEN ISNULL(header_service_id_4,detail_service_id_4)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_5' THEN ISNULL(header_service_id_5,detail_service_id_5)	
			WHEN @param2 = 'org_id_1' THEN detail_org_id_1
			WHEN @param2 = 'org_id_2' THEN detail_org_id_2
			WHEN @param2 = 'org_id_3' THEN detail_org_id_3
			WHEN @param2 = 'org_id_4' THEN detail_org_id_4
			WHEN @param2 = 'org_id_5' THEN detail_org_id_5
			WHEN @param2 = 'service_id_1' THEN detail_service_id_1		
			WHEN @param2 = 'service_id_2' THEN detail_service_id_2
			WHEN @param2 = 'service_id_3' THEN detail_service_id_3
			WHEN @param2 = 'service_id_4' THEN detail_service_id_4
			WHEN @param2 = 'service_id_5' THEN detail_service_id_5	
			ELSE '' END,
	fp_level_1_name = 
	CASE	WHEN @param4 = 'TRUE' AND @param2 = 'org_id_1' THEN ISNULL(header_org_name_1,detail_org_name_1)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_2' THEN ISNULL(header_org_name_2,detail_org_name_2)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_3' THEN ISNULL(header_org_name_3,detail_org_name_3)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_4' THEN ISNULL(header_org_name_4,detail_org_name_4)
			WHEN @param4 = 'TRUE' AND @param2 = 'org_id_5' THEN ISNULL(header_org_name_5,detail_org_name_5)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_1' THEN ISNULL(header_service_name_1,detail_service_name_1)		
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_2' THEN ISNULL(header_service_name_2,detail_service_name_2)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_3' THEN ISNULL(header_service_name_3,detail_service_name_3)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_4' THEN ISNULL(header_service_name_4,detail_service_name_4)
			WHEN @param4 = 'TRUE' AND @param2 = 'service_id_5' THEN ISNULL(header_service_name_5,detail_service_name_5)	
			WHEN @param2 = 'org_id_1' THEN detail_org_name_1
			WHEN @param2 = 'org_id_2' THEN detail_org_name_2
			WHEN @param2 = 'org_id_3' THEN detail_org_name_3
			WHEN @param2 = 'org_id_4' THEN detail_org_name_4
			WHEN @param2 = 'org_id_5' THEN detail_org_name_5
			WHEN @param2 = 'service_id_1' THEN detail_service_name_1		
			WHEN @param2 = 'service_id_2' THEN detail_service_name_2
			WHEN @param2 = 'service_id_3' THEN detail_service_name_3
			WHEN @param2 = 'service_id_4' THEN detail_service_name_4
			WHEN @param2 = 'service_id_5' THEN detail_service_name_5	
			ELSE '' END,
	fp_level_2_value = 
	CASE	WHEN @param4 = 'TRUE' AND @param3 = 'org_id_1' THEN ISNULL(header_org_id_1,detail_org_id_1)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_2' THEN ISNULL(header_org_id_2,detail_org_id_2)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_3' THEN ISNULL(header_org_id_3,detail_org_id_3)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_4' THEN ISNULL(header_org_id_4,detail_org_id_4)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_5' THEN ISNULL(header_org_id_5,detail_org_id_5)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_1' THEN ISNULL(header_service_id_1,detail_service_id_1)		
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_2' THEN ISNULL(header_service_id_2,detail_service_id_2)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_3' THEN ISNULL(header_service_id_3,detail_service_id_3)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_4' THEN ISNULL(header_service_id_4,detail_service_id_4)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_5' THEN ISNULL(header_service_id_5,detail_service_id_5)	
			WHEN @param3 = 'org_id_1' THEN detail_org_id_1
			WHEN @param3 = 'org_id_2' THEN detail_org_id_2
			WHEN @param3 = 'org_id_3' THEN detail_org_id_3
			WHEN @param3 = 'org_id_4' THEN detail_org_id_4
			WHEN @param3 = 'org_id_5' THEN detail_org_id_5
			WHEN @param3 = 'service_id_1' THEN detail_service_id_1		
			WHEN @param3 = 'service_id_2' THEN detail_service_id_2
			WHEN @param3 = 'service_id_3' THEN detail_service_id_3
			WHEN @param3 = 'service_id_4' THEN detail_service_id_4
			WHEN @param3 = 'service_id_5' THEN detail_service_id_5	
			ELSE '' END,
	fp_level_2_name = 
	CASE	WHEN @param4 = 'TRUE' AND @param3 = 'org_id_1' THEN ISNULL(header_org_name_1,detail_org_name_1)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_2' THEN ISNULL(header_org_name_2,detail_org_name_2)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_3' THEN ISNULL(header_org_name_3,detail_org_name_3)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_4' THEN ISNULL(header_org_name_4,detail_org_name_4)
			WHEN @param4 = 'TRUE' AND @param3 = 'org_id_5' THEN ISNULL(header_org_name_5,detail_org_name_5)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_1' THEN ISNULL(header_service_name_1,detail_service_name_1)		
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_2' THEN ISNULL(header_service_name_2,detail_service_name_2)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_3' THEN ISNULL(header_service_name_3,detail_service_name_3)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_4' THEN ISNULL(header_service_name_4,detail_service_name_4)
			WHEN @param4 = 'TRUE' AND @param3 = 'service_id_5' THEN ISNULL(header_service_name_5,detail_service_name_5)	
			WHEN @param3 = 'org_id_1' THEN detail_org_name_1
			WHEN @param3 = 'org_id_2' THEN detail_org_name_2
			WHEN @param3 = 'org_id_3' THEN detail_org_name_3
			WHEN @param3 = 'org_id_4' THEN detail_org_name_4
			WHEN @param3 = 'org_id_5' THEN detail_org_name_5
			WHEN @param3 = 'service_id_1' THEN detail_service_name_1		
			WHEN @param3 = 'service_id_2' THEN detail_service_name_2
			WHEN @param3 = 'service_id_3' THEN detail_service_name_3
			WHEN @param3 = 'service_id_4' THEN detail_service_name_4
			WHEN @param3 = 'service_id_5' THEN detail_service_name_5	
			ELSE '' END

		
	PRINT 'Setting all the sub headers based on parameters'

	UPDATE #hlptab3 set
	sub_header_code = CASE	
			WHEN @param10 = 'prog_code' THEN ISNULL (fk_prog_code, '')
			WHEN @param10 = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @param10 = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @param10 = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @param10 = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @param10 = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @param10 = 'service_id_header_1' THEN ISNULL(header_service_id_1,'')
			WHEN @param10 = 'service_id_header_2' THEN ISNULL(header_service_id_2,'')
			WHEN @param10 = 'service_id_header_3' THEN ISNULL(header_service_id_3,'')
			WHEN @param10 = 'service_id_header_4' THEN ISNULL(header_service_id_4,'')
			WHEN @param10 = 'service_id_header_5' THEN ISNULL(header_service_id_5,'')
			WHEN @param10 = 'org_id_1' THEN detail_org_id_1
			WHEN @param10 = 'org_id_2' THEN detail_org_id_2
			WHEN @param10 = 'org_id_3' THEN detail_org_id_3
			WHEN @param10 = 'org_id_4' THEN detail_org_id_4
			WHEN @param10 = 'org_id_5' THEN detail_org_id_5
			WHEN @param10 = 'service_id_1' THEN detail_service_id_1		
			WHEN @param10 = 'service_id_2' THEN detail_service_id_2
			WHEN @param10 = 'service_id_3' THEN detail_service_id_3
			WHEN @param10 = 'service_id_4' THEN detail_service_id_4
			WHEN @param10 = 'service_id_5' THEN detail_service_id_5
			WHEN @param10 = 'status' AND inv_status = 0 THEN '2'
			WHEN @param10 = 'status' AND inv_status = 1 THEN '0'
			WHEN @param10 = 'status' AND inv_status = 2 THEN '1'		
			WHEN @param10 = 'status' THEN CONVERT(NVARCHAR(25),inv_status)
			WHEN @param10 = 'proj_gr_1' THEN proj_gr_1
			WHEN @param10 = 'proj_gr_2' THEN proj_gr_2
			WHEN @param10 = 'proj_gr_3' THEN proj_gr_3
			WHEN @param10 = 'proj_gr_4' THEN proj_gr_4
			WHEN @param10 = 'proj_gr_5' THEN proj_gr_5

		ELSE fk_prog_code
	END,
	sub_header_description = CASE	
			WHEN @param10 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param10 = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @param10 = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @param10 = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @param10 = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @param10 = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @param10 = 'service_id_header_1' THEN ISNULL(header_service_name_1,'')
			WHEN @param10 = 'service_id_header_2' THEN ISNULL(header_service_name_2,'')
			WHEN @param10 = 'service_id_header_3' THEN ISNULL(header_service_name_3,'')
			WHEN @param10 = 'service_id_header_4' THEN ISNULL(header_service_name_4,'')
			WHEN @param10 = 'service_id_header_5' THEN ISNULL(header_service_name_5,'')
			WHEN @param10 = 'org_id_1' THEN detail_org_name_1
			WHEN @param10 = 'org_id_2' THEN detail_org_name_2
			WHEN @param10 = 'org_id_3' THEN detail_org_name_3
			WHEN @param10 = 'org_id_4' THEN detail_org_name_4
			WHEN @param10 = 'org_id_5' THEN detail_org_name_5
			WHEN @param10 = 'service_id_1' THEN detail_service_name_1		
			WHEN @param10 = 'service_id_2' THEN detail_service_name_2
			WHEN @param10 = 'service_id_3' THEN detail_service_name_3
			WHEN @param10 = 'service_id_4' THEN detail_service_name_4
			WHEN @param10 = 'service_id_5' THEN detail_service_name_5
			WHEN @param10 = 'status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param10 = 'status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param10 = 'proj_gr_1' THEN proj_gr_name_1
			WHEN @param10 = 'proj_gr_2' THEN proj_gr_name_2
			WHEN @param10 = 'proj_gr_3' THEN proj_gr_name_3
			WHEN @param10 = 'proj_gr_4' THEN proj_gr_name_4
			WHEN @param10 = 'proj_gr_5' THEN proj_gr_name_5

		ELSE program_code_description
	END,
	sum_code =
	CASE	WHEN @param11 = 'prog_code' THEN ISNULL (fk_prog_code, '')
			WHEN @param11 = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @param11 = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @param11 = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @param11 = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @param11 = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @param11 = 'service_id_header_1' THEN ISNULL(header_service_id_1,'')
			WHEN @param11 = 'service_id_header_2' THEN ISNULL(header_service_id_2,'')
			WHEN @param11 = 'service_id_header_3' THEN ISNULL(header_service_id_3,'')
			WHEN @param11 = 'service_id_header_4' THEN ISNULL(header_service_id_4,'')
			WHEN @param11 = 'service_id_header_5' THEN ISNULL(header_service_id_5,'')
			WHEN @param11 = 'org_id_1' THEN detail_org_id_1
			WHEN @param11 = 'org_id_2' THEN detail_org_id_2
			WHEN @param11 = 'org_id_3' THEN detail_org_id_3
			WHEN @param11 = 'org_id_4' THEN detail_org_id_4
			WHEN @param11 = 'org_id_5' THEN detail_org_id_5
			WHEN @param11 = 'service_id_1' THEN detail_service_id_1		
			WHEN @param11 = 'service_id_2' THEN detail_service_id_2
			WHEN @param11 = 'service_id_3' THEN detail_service_id_3
			WHEN @param11 = 'service_id_4' THEN detail_service_id_4
			WHEN @param11 = 'service_id_5' THEN detail_service_id_5
			WHEN @param11 = 'status' AND inv_status = 0 THEN '2'
			WHEN @param11 = 'status' AND inv_status = 1 THEN '0'
			WHEN @param11 = 'status' AND inv_status = 2 THEN '1'		
			WHEN @param11 = 'status' THEN CONVERT(NVARCHAR(25),inv_status)
			WHEN @param11 = 'proj_gr_1' THEN proj_gr_1
			WHEN @param11 = 'proj_gr_2' THEN proj_gr_2
			WHEN @param11 = 'proj_gr_3' THEN proj_gr_3
			WHEN @param11 = 'proj_gr_4' THEN proj_gr_4
			WHEN @param11 = 'proj_gr_5' THEN proj_gr_5
		ELSE detail_org_id_2
	END,
	sum_level = 
	CASE	WHEN @param11 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param11 = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @param11 = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @param11 = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @param11 = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @param11 = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @param11 = 'service_id_header_1' THEN ISNULL(header_service_name_1,'')
			WHEN @param11 = 'service_id_header_2' THEN ISNULL(header_service_name_2,'')
			WHEN @param11 = 'service_id_header_3' THEN ISNULL(header_service_name_3,'')
			WHEN @param11 = 'service_id_header_4' THEN ISNULL(header_service_name_4,'')
			WHEN @param11 = 'service_id_header_5' THEN ISNULL(header_service_name_5,'')
			WHEN @param11 = 'org_id_1' THEN detail_org_name_1
			WHEN @param11 = 'org_id_2' THEN detail_org_name_2
			WHEN @param11 = 'org_id_3' THEN detail_org_name_3
			WHEN @param11 = 'org_id_4' THEN detail_org_name_4
			WHEN @param11 = 'org_id_5' THEN detail_org_name_5
			WHEN @param11 = 'service_id_1' THEN detail_service_name_1		
			WHEN @param11 = 'service_id_2' THEN detail_service_name_2
			WHEN @param11 = 'service_id_3' THEN detail_service_name_3
			WHEN @param11 = 'service_id_4' THEN detail_service_name_4
			WHEN @param11 = 'service_id_5' THEN detail_service_name_5
			WHEN @param11 = 'status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param11 = 'status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param11 = 'proj_gr_1' THEN proj_gr_name_1
			WHEN @param11 = 'proj_gr_2' THEN proj_gr_name_2
			WHEN @param11 = 'proj_gr_3' THEN proj_gr_name_3
			WHEN @param11 = 'proj_gr_4' THEN proj_gr_name_4
			WHEN @param11 = 'proj_gr_5' THEN proj_gr_name_5
			ELSE detail_org_name_2
	END, 
	sum_code_2 = CASE	
			WHEN @param12 = 'prog_code' THEN ISNULL (fk_prog_code, '')
			WHEN @param12 = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @param12 = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @param12 = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @param12 = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @param12 = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @param12 = 'service_id_header_1' THEN ISNULL(header_service_id_1,'')
			WHEN @param12 = 'service_id_header_2' THEN ISNULL(header_service_id_2,'')
			WHEN @param12 = 'service_id_header_3' THEN ISNULL(header_service_id_3,'')
			WHEN @param12 = 'service_id_header_4' THEN ISNULL(header_service_id_4,'')
			WHEN @param12 = 'service_id_header_5' THEN ISNULL(header_service_id_5,'')
			WHEN @param12 = 'org_id_1' THEN detail_org_id_1
			WHEN @param12 = 'org_id_2' THEN detail_org_id_2
			WHEN @param12 = 'org_id_3' THEN detail_org_id_3
			WHEN @param12 = 'org_id_4' THEN detail_org_id_4
			WHEN @param12 = 'org_id_5' THEN detail_org_id_5
			WHEN @param12 = 'service_id_1' THEN detail_service_id_1		
			WHEN @param12 = 'service_id_2' THEN detail_service_id_2
			WHEN @param12 = 'service_id_3' THEN detail_service_id_3
			WHEN @param12 = 'service_id_4' THEN detail_service_id_4
			WHEN @param12 = 'service_id_5' THEN detail_service_id_5
			WHEN @param12 = 'status' AND inv_status = 0 THEN '2'
			WHEN @param12 = 'status' AND inv_status = 1 THEN '0'
			WHEN @param12 = 'status' AND inv_status = 2 THEN '1'		
			WHEN @param12 = 'status' THEN CONVERT(NVARCHAR(25),inv_status)
			WHEN @param12 = 'proj_gr_1' THEN proj_gr_1
			WHEN @param12 = 'proj_gr_2' THEN proj_gr_2
			WHEN @param12 = 'proj_gr_3' THEN proj_gr_3
			WHEN @param12 = 'proj_gr_4' THEN proj_gr_4
			WHEN @param12 = 'proj_gr_5' THEN proj_gr_5
		ELSE ''
	END,
	sum_description_2 = 
	CASE	WHEN @param12 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param12 = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @param12 = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @param12 = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @param12 = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @param12 = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @param12 = 'service_id_header_1' THEN ISNULL(header_service_name_1,'')
			WHEN @param12 = 'service_id_header_2' THEN ISNULL(header_service_name_2,'')
			WHEN @param12 = 'service_id_header_3' THEN ISNULL(header_service_name_3,'')
			WHEN @param12 = 'service_id_header_4' THEN ISNULL(header_service_name_4,'')
			WHEN @param12 = 'service_id_header_5' THEN ISNULL(header_service_name_5,'')
			WHEN @param12 = 'org_id_1' THEN detail_org_name_1
			WHEN @param12 = 'org_id_2' THEN detail_org_name_2
			WHEN @param12 = 'org_id_3' THEN detail_org_name_3
			WHEN @param12 = 'org_id_4' THEN detail_org_name_4
			WHEN @param12 = 'org_id_5' THEN detail_org_name_5
			WHEN @param12 = 'service_id_1' THEN detail_service_name_1		
			WHEN @param12 = 'service_id_2' THEN detail_service_name_2
			WHEN @param12 = 'service_id_3' THEN detail_service_name_3
			WHEN @param12 = 'service_id_4' THEN detail_service_name_4
			WHEN @param12 = 'service_id_5' THEN detail_service_name_5
			WHEN @param12 = 'status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param12 = 'status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param12 = 'proj_gr_1' THEN proj_gr_name_1
			WHEN @param12 = 'proj_gr_2' THEN proj_gr_name_2
			WHEN @param12 = 'proj_gr_3' THEN proj_gr_name_3
			WHEN @param12 = 'proj_gr_4' THEN proj_gr_name_4
			WHEN @param12 = 'proj_gr_5' THEN proj_gr_name_5
		ELSE ''
	END,


	sub_level_sa_code = CASE	
			WHEN @param13 = 'prog_code' AND fk_prog_code = '' THEN 'ZZ'
			WHEN @param13 = 'prog_code' THEN ISNULL (fk_prog_code, 'ZZ')
			WHEN @param13 = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @param13 = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @param13 = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @param13 = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @param13 = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @param13 = 'service_id_header_1' THEN ISNULL(header_service_id_1,'')
			WHEN @param13 = 'service_id_header_2' THEN ISNULL(header_service_id_2,'')
			WHEN @param13 = 'service_id_header_3' THEN ISNULL(header_service_id_3,'')
			WHEN @param13 = 'service_id_header_4' THEN ISNULL(header_service_id_4,'')
			WHEN @param13 = 'service_id_header_5' THEN ISNULL(header_service_id_5,'')
			WHEN @param13 = 'org_id_1' THEN detail_org_id_1
			WHEN @param13 = 'org_id_2' THEN detail_org_id_2
			WHEN @param13 = 'org_id_3' THEN detail_org_id_3
			WHEN @param13 = 'org_id_4' THEN detail_org_id_4
			WHEN @param13 = 'org_id_5' THEN detail_org_id_5
			WHEN @param13 = 'service_id_1' THEN detail_service_id_1		
			WHEN @param13 = 'service_id_2' THEN detail_service_id_2
			WHEN @param13 = 'service_id_3' THEN detail_service_id_3
			WHEN @param13 = 'service_id_4' THEN detail_service_id_4
			WHEN @param13 = 'service_id_5' THEN detail_service_id_5
			WHEN @param13 = 'status' AND inv_status = 0 THEN '2'
			WHEN @param13 = 'status' AND inv_status = 1 THEN '0'
			WHEN @param13 = 'status' AND inv_status = 2 THEN '1'		
			WHEN @param13 = 'status' THEN CONVERT(NVARCHAR(25),inv_status)
			WHEN @param13 = 'proj_gr_1' THEN proj_gr_1
			WHEN @param13 = 'proj_gr_2' THEN proj_gr_2
			WHEN @param13 = 'proj_gr_3' THEN proj_gr_3
			WHEN @param13 = 'proj_gr_4' THEN proj_gr_4
			WHEN @param13 = 'proj_gr_5' THEN proj_gr_5
		ELSE ''
	END,

	sub_level_sa_description = 
	CASE	
			WHEN @param13 = 'prog_code' AND fk_prog_code = '' THEN 'Programkode mangler'
			WHEN @param13 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param13 = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @param13 = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @param13 = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @param13 = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @param13 = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @param13 = 'service_id_header_1' THEN ISNULL(header_service_name_1,'')
			WHEN @param13 = 'service_id_header_2' THEN ISNULL(header_service_name_2,'')
			WHEN @param13 = 'service_id_header_3' THEN ISNULL(header_service_name_3,'')
			WHEN @param13 = 'service_id_header_4' THEN ISNULL(header_service_name_4,'')
			WHEN @param13 = 'service_id_header_5' THEN ISNULL(header_service_name_5,'')
			WHEN @param13 = 'org_id_1' THEN detail_org_name_1
			WHEN @param13 = 'org_id_2' THEN detail_org_name_2
			WHEN @param13 = 'org_id_3' THEN detail_org_name_3
			WHEN @param13 = 'org_id_4' THEN detail_org_name_4
			WHEN @param13 = 'org_id_5' THEN detail_org_name_5
			WHEN @param13 = 'service_id_1' THEN detail_service_name_1		
			WHEN @param13 = 'service_id_2' THEN detail_service_name_2
			WHEN @param13 = 'service_id_3' THEN detail_service_name_3
			WHEN @param13 = 'service_id_4' THEN detail_service_name_4
			WHEN @param13 = 'service_id_5' THEN detail_service_name_5
			WHEN @param13 = 'status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param13 = 'status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param13 = 'proj_gr_1' THEN proj_gr_name_1
			WHEN @param13 = 'proj_gr_2' THEN proj_gr_name_2
			WHEN @param13 = 'proj_gr_3' THEN proj_gr_name_3
			WHEN @param13 = 'proj_gr_4' THEN proj_gr_name_4
			WHEN @param13 = 'proj_gr_5' THEN proj_gr_name_5
			ELSE ''
			END
		
	,dynamic_gr_1 = 
	CASE	WHEN @param20 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param20 = 'org_id_header_1' THEN ISNULL(header_org_id_1 + ' - ' + header_org_name_1,'')
			WHEN @param20 = 'org_id_header_2' THEN ISNULL(header_org_id_2 + ' - ' + header_org_name_2,'')
			WHEN @param20 = 'org_id_header_3' THEN ISNULL(header_org_id_3 + ' - ' + header_org_name_3,'')
			WHEN @param20 = 'org_id_header_4' THEN ISNULL(header_org_id_4 + ' - ' + header_org_name_4,'')
			WHEN @param20 = 'org_id_header_5' THEN ISNULL(header_org_id_5 + ' - ' + header_org_name_5,'')
			WHEN @param20 = 'service_id_header_1' THEN ISNULL(header_service_id_1 + ' - ' + header_service_name_1,'')
			WHEN @param20 = 'service_id_header_2' THEN ISNULL(header_service_id_2 + ' - ' + header_service_name_2,'')
			WHEN @param20 = 'service_id_header_3' THEN ISNULL(header_service_id_3 + ' - ' + header_service_name_3,'')
			WHEN @param20 = 'service_id_header_4' THEN ISNULL(header_service_id_4 + ' - ' + header_service_name_4,'')
			WHEN @param20 = 'service_id_header_5' THEN ISNULL(header_service_id_5 + ' - ' + header_service_name_5,'')
			WHEN @param20 = 'org_id_1' THEN detail_org_id_1 + ' - ' + detail_org_name_1
			WHEN @param20 = 'org_id_2' THEN detail_org_id_2 + ' - ' + detail_org_name_2
			WHEN @param20 = 'org_id_3' THEN detail_org_id_3 + ' - ' + detail_org_name_3
			WHEN @param20 = 'org_id_4' THEN detail_org_id_4 + ' - ' + detail_org_name_4
			WHEN @param20 = 'org_id_5' THEN detail_org_id_5 + ' - ' + detail_org_name_5
			WHEN @param20 = 'service_id_1' THEN detail_service_id_1 + ' - ' + detail_service_name_1		
			WHEN @param20 = 'service_id_2' THEN detail_service_id_2 + ' - ' + detail_service_name_2
			WHEN @param20 = 'service_id_3' THEN detail_service_id_3 + ' - ' + detail_service_name_3
			WHEN @param20 = 'service_id_4' THEN detail_service_id_4 + ' - ' + detail_service_name_4
			WHEN @param20 = 'service_id_5' THEN detail_service_id_5 + ' - ' + detail_service_name_5
			WHEN @param20 = 'status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param20 = 'status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param20 = 'proj_gr_1' THEN proj_gr_1 + ' - ' + proj_gr_name_1
			WHEN @param20 = 'proj_gr_2' THEN proj_gr_2 + ' - ' + proj_gr_name_2
			WHEN @param20 = 'proj_gr_3' THEN proj_gr_3 + ' - ' + proj_gr_name_3
			WHEN @param20 = 'proj_gr_4' THEN proj_gr_4 + ' - ' + proj_gr_name_4
			WHEN @param20 = 'proj_gr_5' THEN proj_gr_5 + ' - ' + proj_gr_name_5
			ELSE ''
			END
	,dynamic_gr_2 = 
	CASE	WHEN @param21 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param21 = 'org_id_header_1' THEN ISNULL(header_org_id_1 + ' - ' + header_org_name_1,'')
			WHEN @param21 = 'org_id_header_2' THEN ISNULL(header_org_id_2 + ' - ' + header_org_name_2,'')
			WHEN @param21 = 'org_id_header_3' THEN ISNULL(header_org_id_3 + ' - ' + header_org_name_3,'')
			WHEN @param21 = 'org_id_header_4' THEN ISNULL(header_org_id_4 + ' - ' + header_org_name_4,'')
			WHEN @param21 = 'org_id_header_5' THEN ISNULL(header_org_id_5 + ' - ' + header_org_name_5,'')
			WHEN @param21 = 'service_id_header_1' THEN ISNULL(header_service_id_1 + ' - ' + header_service_name_1,'')
			WHEN @param21 = 'service_id_header_2' THEN ISNULL(header_service_id_2 + ' - ' + header_service_name_2,'')
			WHEN @param21 = 'service_id_header_3' THEN ISNULL(header_service_id_3 + ' - ' + header_service_name_3,'')
			WHEN @param21 = 'service_id_header_4' THEN ISNULL(header_service_id_4 + ' - ' + header_service_name_4,'')
			WHEN @param21 = 'service_id_header_5' THEN ISNULL(header_service_id_5 + ' - ' + header_service_name_5,'')
			WHEN @param21 = 'org_id_1' THEN detail_org_id_1 + ' - ' + detail_org_name_1
			WHEN @param21 = 'org_id_2' THEN detail_org_id_2 + ' - ' + detail_org_name_2
			WHEN @param21 = 'org_id_3' THEN detail_org_id_3 + ' - ' + detail_org_name_3
			WHEN @param21 = 'org_id_4' THEN detail_org_id_4 + ' - ' + detail_org_name_4
			WHEN @param21 = 'org_id_5' THEN detail_org_id_5 + ' - ' + detail_org_name_5
			WHEN @param21 = 'service_id_1' THEN detail_service_id_1 + ' - ' + detail_service_name_1		
			WHEN @param21 = 'service_id_2' THEN detail_service_id_2 + ' - ' + detail_service_name_2
			WHEN @param21 = 'service_id_3' THEN detail_service_id_3 + ' - ' + detail_service_name_3
			WHEN @param21 = 'service_id_4' THEN detail_service_id_4 + ' - ' + detail_service_name_4
			WHEN @param21 = 'service_id_5' THEN detail_service_id_5 + ' - ' + detail_service_name_5
			WHEN @param21 = 'status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param21 = 'status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param21 = 'proj_gr_1' THEN proj_gr_1 + ' - ' + proj_gr_name_1
			WHEN @param21 = 'proj_gr_2' THEN proj_gr_2 + ' - ' + proj_gr_name_2
			WHEN @param21 = 'proj_gr_3' THEN proj_gr_3 + ' - ' + proj_gr_name_3
			WHEN @param21 = 'proj_gr_4' THEN proj_gr_4 + ' - ' + proj_gr_name_4
			WHEN @param21 = 'proj_gr_5' THEN proj_gr_5 + ' - ' + proj_gr_name_5
			ELSE ''
			END

	,blist_gr_1 = 
	CASE	WHEN @param22 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param22 = 'org_id_header_1' THEN ISNULL(header_org_id_1 + ' - ' + header_org_name_1,'')
			WHEN @param22 = 'org_id_header_2' THEN ISNULL(header_org_id_2 + ' - ' + header_org_name_2,'')
			WHEN @param22 = 'org_id_header_3' THEN ISNULL(header_org_id_3 + ' - ' + header_org_name_3,'')
			WHEN @param22 = 'org_id_header_4' THEN ISNULL(header_org_id_4 + ' - ' + header_org_name_4,'')
			WHEN @param22 = 'org_id_header_5' THEN ISNULL(header_org_id_5 + ' - ' + header_org_name_5,'')
			WHEN @param22 = 'service_id_header_1' THEN ISNULL(header_service_id_1 + ' - ' + header_service_name_1,'')
			WHEN @param22 = 'service_id_header_2' THEN ISNULL(header_service_id_2 + ' - ' + header_service_name_2,'')
			WHEN @param22 = 'service_id_header_3' THEN ISNULL(header_service_id_3 + ' - ' + header_service_name_3,'')
			WHEN @param22 = 'service_id_header_4' THEN ISNULL(header_service_id_4 + ' - ' + header_service_name_4,'')
			WHEN @param22 = 'service_id_header_5' THEN ISNULL(header_service_id_5 + ' - ' + header_service_name_5,'')
			WHEN @param22 = 'org_id_1' THEN detail_org_id_1 + ' - ' + detail_org_name_1
			WHEN @param22 = 'org_id_2' THEN detail_org_id_2 + ' - ' + detail_org_name_2
			WHEN @param22 = 'org_id_3' THEN detail_org_id_3 + ' - ' + detail_org_name_3
			WHEN @param22 = 'org_id_4' THEN detail_org_id_4 + ' - ' + detail_org_name_4
			WHEN @param22 = 'org_id_5' THEN detail_org_id_5 + ' - ' + detail_org_name_5
			WHEN @param22 = 'service_id_1' THEN detail_service_id_1 + ' - ' + detail_service_name_1		
			WHEN @param22 = 'service_id_2' THEN detail_service_id_2 + ' - ' + detail_service_name_2
			WHEN @param22 = 'service_id_3' THEN detail_service_id_3 + ' - ' + detail_service_name_3
			WHEN @param22 = 'service_id_4' THEN detail_service_id_4 + ' - ' + detail_service_name_4
			WHEN @param22 = 'service_id_5' THEN detail_service_id_5 + ' - ' + detail_service_name_5
			WHEN @param22 = 'inv_status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param22 = 'inv_status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param22 = 'proj_gr_1' THEN proj_gr_1 + ' - ' + proj_gr_name_1
			WHEN @param22 = 'proj_gr_2' THEN proj_gr_2 + ' - ' + proj_gr_name_2
			WHEN @param22 = 'proj_gr_3' THEN proj_gr_3 + ' - ' + proj_gr_name_3
			WHEN @param22 = 'proj_gr_4' THEN proj_gr_4 + ' - ' + proj_gr_name_4
			WHEN @param22 = 'proj_gr_5' THEN proj_gr_5 + ' - ' + proj_gr_name_5
			ELSE ''
			END
	,blist_gr_2 = 
	CASE	WHEN @param23 = 'prog_code' THEN ISNULL (program_code_description, '')
			WHEN @param23 = 'org_id_header_1' THEN ISNULL(header_org_id_1 + ' - ' + header_org_name_1,'')
			WHEN @param23 = 'org_id_header_2' THEN ISNULL(header_org_id_2 + ' - ' + header_org_name_2,'')
			WHEN @param23 = 'org_id_header_3' THEN ISNULL(header_org_id_3 + ' - ' + header_org_name_3,'')
			WHEN @param23 = 'org_id_header_4' THEN ISNULL(header_org_id_4 + ' - ' + header_org_name_4,'')
			WHEN @param23 = 'org_id_header_5' THEN ISNULL(header_org_id_5 + ' - ' + header_org_name_5,'')
			WHEN @param23 = 'service_id_header_1' THEN ISNULL(header_service_id_1 + ' - ' + header_service_name_1,'')
			WHEN @param23 = 'service_id_header_2' THEN ISNULL(header_service_id_2 + ' - ' + header_service_name_2,'')
			WHEN @param23 = 'service_id_header_3' THEN ISNULL(header_service_id_3 + ' - ' + header_service_name_3,'')
			WHEN @param23 = 'service_id_header_4' THEN ISNULL(header_service_id_4 + ' - ' + header_service_name_4,'')
			WHEN @param23 = 'service_id_header_5' THEN ISNULL(header_service_id_5 + ' - ' + header_service_name_5,'')
			WHEN @param23 = 'org_id_1' THEN detail_org_id_1 + ' - ' + detail_org_name_1
			WHEN @param23 = 'org_id_2' THEN detail_org_id_2 + ' - ' + detail_org_name_2
			WHEN @param23 = 'org_id_3' THEN detail_org_id_3 + ' - ' + detail_org_name_3
			WHEN @param23 = 'org_id_4' THEN detail_org_id_4 + ' - ' + detail_org_name_4
			WHEN @param23 = 'org_id_5' THEN detail_org_id_5 + ' - ' + detail_org_name_5
			WHEN @param23 = 'service_id_1' THEN detail_service_id_1 + ' - ' + detail_service_name_1		
			WHEN @param23 = 'service_id_2' THEN detail_service_id_2 + ' - ' + detail_service_name_2
			WHEN @param23 = 'service_id_3' THEN detail_service_id_3 + ' - ' + detail_service_name_3
			WHEN @param23 = 'service_id_4' THEN detail_service_id_4 + ' - ' + detail_service_name_4
			WHEN @param23 = 'service_id_5' THEN detail_service_id_5 + ' - ' + detail_service_name_5
			WHEN @param23 = 'inv_status' AND inv_status = 0 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Newinv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 1 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_PrevInv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 2 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_Runninginv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 3 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_EvaInv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 4 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ParkedInv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 5 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_DeletedInv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 6 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_RequiredInv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 7 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_ConsequenceadjustedInv' AND ls.Language = @language)
			WHEN @param23 = 'inv_status' AND inv_status = 8 THEN (SELECT isnull(lst.description, ls.Description) FROM gco_language_strings ls LEFT JOIN gco_language_string_overrides_tenant lst ON ls.ID = lst.ID AND ls.Language = lst.Language AND ls.context = lst.context AND lst.fk_tenant_id = @fk_tenant_id WHERE ls.[ID] = 'inv_status_FinishedInv' AND ls.Language = @language)
			WHEN @param23 = 'proj_gr_1' THEN proj_gr_1 + ' - ' + proj_gr_name_1
			WHEN @param23 = 'proj_gr_2' THEN proj_gr_2 + ' - ' + proj_gr_name_2
			WHEN @param23 = 'proj_gr_3' THEN proj_gr_3 + ' - ' + proj_gr_name_3
			WHEN @param23 = 'proj_gr_4' THEN proj_gr_4 + ' - ' + proj_gr_name_4
			WHEN @param23 = 'proj_gr_5' THEN proj_gr_5 + ' - ' + proj_gr_name_5
			ELSE ''
			END
-- logic for the investment graph


	UPDATE #hlptab3 
	SET graph_type = 'Investering',
	graph_l1_value = CASE
			WHEN line_item_id != 1010 THEN CONVERT(VARCHAR(24), line_item_id)
			WHEN @graph_group = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @graph_group = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @graph_group = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @graph_group = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @graph_group = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @graph_group = 'service_id_header_1' THEN ISNULL(header_service_id_1,'')
			WHEN @graph_group = 'service_id_header_2' THEN ISNULL(header_service_id_2,'')
			WHEN @graph_group = 'service_id_header_3' THEN ISNULL(header_service_id_3,'')
			WHEN @graph_group = 'service_id_header_4' THEN ISNULL(header_service_id_4,'')
			WHEN @graph_group = 'service_id_header_5' THEN ISNULL(header_service_id_5,'')
			WHEN @graph_group = 'org_id_1' THEN detail_org_id_1
			WHEN @graph_group = 'org_id_2' THEN detail_org_id_2
			WHEN @graph_group = 'org_id_3' THEN detail_org_id_3
			WHEN @graph_group = 'org_id_4' THEN detail_org_id_4
			WHEN @graph_group = 'org_id_5' THEN detail_org_id_5
			WHEN @graph_group = 'service_id_1' THEN detail_service_id_1		
			WHEN @graph_group = 'service_id_2' THEN detail_service_id_2
			WHEN @graph_group = 'service_id_3' THEN detail_service_id_3
			WHEN @graph_group = 'service_id_4' THEN detail_service_id_4
			WHEN @graph_group = 'service_id_5' THEN detail_service_id_5
			WHEN @graph_group = 'proj_gr_1' THEN proj_gr_1
			WHEN @graph_group = 'proj_gr_2' THEN proj_gr_2
			WHEN @graph_group = 'proj_gr_3' THEN proj_gr_3
			WHEN @graph_group = 'proj_gr_4' THEN proj_gr_4
			WHEN @graph_group = 'proj_gr_5' THEN proj_gr_5
	ELSE detail_org_id_2 END,
	graph_l1_name = CASE
			WHEN line_item_id != 1010 THEN line_item
			WHEN @graph_group = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @graph_group = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @graph_group = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @graph_group = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @graph_group = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @graph_group = 'service_id_header_1' THEN ISNULL(header_service_name_1,'')
			WHEN @graph_group = 'service_id_header_2' THEN ISNULL(header_service_name_2,'')
			WHEN @graph_group = 'service_id_header_3' THEN ISNULL(header_service_name_3,'')
			WHEN @graph_group = 'service_id_header_4' THEN ISNULL(header_service_name_4,'')
			WHEN @graph_group = 'service_id_header_5' THEN ISNULL(header_service_name_5,'')
			WHEN @graph_group = 'org_id_1' THEN detail_org_name_1
			WHEN @graph_group = 'org_id_2' THEN detail_org_name_2
			WHEN @graph_group = 'org_id_3' THEN detail_org_name_3
			WHEN @graph_group = 'org_id_4' THEN detail_org_name_4
			WHEN @graph_group = 'org_id_5' THEN detail_org_name_5
			WHEN @graph_group = 'service_id_1' THEN detail_service_name_1		
			WHEN @graph_group = 'service_id_2' THEN detail_service_name_2
			WHEN @graph_group = 'service_id_3' THEN detail_service_name_3
			WHEN @graph_group = 'service_id_4' THEN detail_service_name_4
			WHEN @graph_group = 'service_id_5' THEN detail_service_name_5
			WHEN @graph_group = 'proj_gr_1' THEN proj_gr_name_1
			WHEN @graph_group = 'proj_gr_2' THEN proj_gr_name_2
			WHEN @graph_group = 'proj_gr_3' THEN proj_gr_name_3
			WHEN @graph_group = 'proj_gr_4' THEN proj_gr_name_4
			WHEN @graph_group = 'proj_gr_5' THEN proj_gr_name_5
	ELSE detail_org_name_2 END,
	graph_l2_value = CASE
			WHEN line_item_id != 1010 THEN CONVERT(VARCHAR(24), line_item_id)
			else ISNULL(pk_main_project_code, 'XX') end,
	graph_l2_name = CASE
			WHEN line_item_id != 1010 THEN line_item
			else ISNULL(main_project_name,'') end,
	graph_org_id = CASE
			WHEN @graph_org_group = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @graph_org_group = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @graph_org_group = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @graph_org_group = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @graph_org_group = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @graph_org_group = 'org_id_1' THEN detail_org_id_1
			WHEN @graph_org_group = 'org_id_2' THEN detail_org_id_2
			WHEN @graph_org_group = 'org_id_3' THEN detail_org_id_3
			WHEN @graph_org_group = 'org_id_4' THEN detail_org_id_4
			WHEN @graph_org_group = 'org_id_5' THEN detail_org_id_5
	ELSE detail_org_id_2 END,
		graph_org_name = CASE
			WHEN @graph_org_group = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @graph_org_group = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @graph_org_group = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @graph_org_group = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @graph_org_group = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @graph_org_group = 'org_id_1' THEN detail_org_name_1
			WHEN @graph_org_group = 'org_id_2' THEN detail_org_name_2
			WHEN @graph_org_group = 'org_id_3' THEN detail_org_name_3
			WHEN @graph_org_group = 'org_id_4' THEN detail_org_name_4
			WHEN @graph_org_group = 'org_id_5' THEN detail_org_name_5
	ELSE detail_org_name_2 END
	WHERE line_group_id = 10

	UPDATE #hlptab3 
	SET graph_type = 'Finansiering', 
	graph_l1_value = ISNULL(CONVERT(NVARCHAR(25), line_item_id), 'XX'),
	graph_l1_name = ISNULL(line_item,''),
	graph_l2_value = ISNULL(CONVERT(NVARCHAR(25), line_item_id), 'XX'),
	graph_l2_name = ISNULL(line_item,''),
	graph_org_id = CASE
			WHEN @graph_org_group = 'org_id_header_1' THEN ISNULL(header_org_id_1,'')
			WHEN @graph_org_group = 'org_id_header_2' THEN ISNULL(header_org_id_2,'')
			WHEN @graph_org_group = 'org_id_header_3' THEN ISNULL(header_org_id_3,'')
			WHEN @graph_org_group = 'org_id_header_4' THEN ISNULL(header_org_id_4,'')
			WHEN @graph_org_group = 'org_id_header_5' THEN ISNULL(header_org_id_5,'')
			WHEN @graph_org_group = 'org_id_1' THEN detail_org_id_1
			WHEN @graph_org_group = 'org_id_2' THEN detail_org_id_2
			WHEN @graph_org_group = 'org_id_3' THEN detail_org_id_3
			WHEN @graph_org_group = 'org_id_4' THEN detail_org_id_4
			WHEN @graph_org_group = 'org_id_5' THEN detail_org_id_5
	ELSE detail_org_id_2 END,
		graph_org_name = CASE
			WHEN @graph_org_group = 'org_id_header_1' THEN ISNULL(header_org_name_1,'')
			WHEN @graph_org_group = 'org_id_header_2' THEN ISNULL(header_org_name_2,'')
			WHEN @graph_org_group = 'org_id_header_3' THEN ISNULL(header_org_name_3,'')
			WHEN @graph_org_group = 'org_id_header_4' THEN ISNULL(header_org_name_4,'')
			WHEN @graph_org_group = 'org_id_header_5' THEN ISNULL(header_org_name_5,'')
			WHEN @graph_org_group = 'org_id_1' THEN detail_org_name_1
			WHEN @graph_org_group = 'org_id_2' THEN detail_org_name_2
			WHEN @graph_org_group = 'org_id_3' THEN detail_org_name_3
			WHEN @graph_org_group = 'org_id_4' THEN detail_org_name_4
			WHEN @graph_org_group = 'org_id_5' THEN detail_org_name_5
	ELSE detail_org_name_2 END
	WHERE line_group_id != 10

--Added for flag on operational expence
select pk_main_project_code
INTO #helptab_oe
from tfp_trans_detail PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
where PT.fk_tenant_id = @fk_tenant_id
and budget_year = @budget_year
GROUP BY pk_main_project_code
HAVING SUM(year_1_amount) != 0
OR SUM(year_2_amount) != 0
OR SUM(year_3_amount) != 0
OR SUM(year_4_amount) != 0

UPDATE a set oe_flag = 1
from #hlptab3 a
JOIN #helptab_oe b on a.pk_main_project_code = b.pk_main_project_code


	
	DELETE FROM twh_investment_report WHERE budget_year = @budget_year AND fk_tenant_id = @fk_tenant_id

		INSERT INTO twh_investment_report (fk_tenant_id,budget_year,
		pk_main_project_code,main_project_name,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,
		fk_function_code,free_dim_1, free_dim_2, free_dim_3,free_dim_4,
		fk_project_code,project_name,proj_gr_1, proj_gr_2, proj_gr_3, proj_gr_4, proj_gr_5,
		proj_gr_name_1, proj_gr_name_2, proj_gr_name_3, proj_gr_name_4, proj_gr_name_5,	
		fk_change_id,fk_alter_code,fk_adjustment_code,fk_user_adjustment_code,fk_prog_code,
		header_dept,header_function,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,header_org_id_3,
		header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,detail_org_id_1,detail_org_name_1,
		detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,detail_org_id_4,detail_org_name_4,detail_org_id_5,
		detail_org_name_5,header_service_id_1,header_service_name_1,header_service_id_2,header_service_name_2,header_service_id_3,
		header_service_name_3,header_service_id_4,header_service_name_4,header_service_id_5,header_service_name_5,
		detail_service_id_1,detail_service_name_1,detail_service_id_2,detail_service_name_2,detail_service_id_3,
		detail_service_name_3,detail_service_id_4,detail_service_name_4,detail_service_id_5,detail_service_name_5,
		program_code_description,line_group_id,line_item_id
		,year_0_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount
		,year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,year_18_amount,year_19_amount,year_20_amount
		,inv_year_1_amount,	inv_year_2_amount,inv_year_3_amount,inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,
		inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount,sum_finplan,finished_year,
		display_proj_columns,approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year,fin_year_1_amount,
		fin_year_2_amount,fin_year_3_amount,fin_year_4_amount,fin_total_amount,previously_budgeted,gross_cost,
		financed_amount,net_cost,type, updated, updated_by, org_budget_flag, vat_rate, vat_refund, priority, fk_investment_phase_id
		,adjustment_code_status
		,goalid,targetid
		,tags
		,trans_desc
		,bud_process
		,actual_amt_year
		,fk_user_adjustment_code_no_desc
		,sum_finplan_net)	
		SELECT fk_tenant_id,budget_year,
		pk_main_project_code,main_project_name,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,
		fk_function_code,free_dim_1, free_dim_2, free_dim_3,free_dim_4,
		fk_project_code,project_name,
		proj_gr_1, proj_gr_2, proj_gr_3, proj_gr_4, proj_gr_5,
		proj_gr_name_1, proj_gr_name_2, proj_gr_name_3, proj_gr_name_4, proj_gr_name_5,
		fk_change_id,fk_alter_code,fk_adjustment_code,
		fk_user_adjustment_code = CASE WHEN user_adj_desc = '' THEN fk_user_adjustment_code else fk_user_adjustment_code + ' - ' + substring(user_adj_desc,1,200) end,
		fk_prog_code,header_dept,header_function,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,
		header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,
		detail_org_id_1,detail_org_name_1,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,
		detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5,header_service_id_1,header_service_name_1,
		header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,
		header_service_name_4,header_service_id_5,header_service_name_5,detail_service_id_1,detail_service_name_1,
		detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,
		detail_service_name_4,detail_service_id_5,detail_service_name_5,program_code_description,line_group_id,
		line_item_id
		,year_0_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount
		,year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,year_18_amount,year_19_amount,year_20_amount
		,inv_year_1_amount,inv_year_2_amount,inv_year_3_amount,
		inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,
		inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount,sum_finplan,finished_year,display_proj_columns,
		approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year,fin_year_1_amount,fin_year_2_amount,
		fin_year_3_amount,fin_year_4_amount,fin_total_amount,previously_budgeted,gross_cost,financed_amount,
		net_cost,
		type = CASE WHEN line_item_id IN (1010,1020,1030,1040) THEN 'i' else 'f' end
		,updated, updated_by, org_budget_flag, vat_rate, vat_refund, priority, fk_investment_phase_id
		,adjustment_code_status
		,goalid = ISNULL(goalid,'********-0000-0000-0000-************')
		,targetid = ISNULL(targetid,'********-0000-0000-0000-************')
		,tags = ISNULL(tags,'')
		,trans_desc  = ISNULL(trans_desc,'')
		,bud_process
		,actual_amt_year
		,fk_user_adjustment_code_no_desc = ISNULL(fk_user_adjustment_code, '')
		,sum_finplan_net
	  FROM #hlptab3 

	  --Inserting blank rows to ensure all inv statuses are present in the dataset, ref bug #89274
	  
		INSERT INTO twh_investment_report (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,proj_gr_1,proj_gr_name_1,proj_gr_2,proj_gr_name_2,proj_gr_3,proj_gr_name_3,proj_gr_4,proj_gr_name_4,proj_gr_5,proj_gr_name_5				
		,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function			
		,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,detail_org_id_1,detail_org_name_1			
		,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5
		,header_service_id_1,header_service_name_1,header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,header_service_name_4,header_service_id_5,header_service_name_5		
		,detail_service_id_1,detail_service_name_1,detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,detail_service_name_4,detail_service_id_5,detail_service_name_5		
		,program_code_description,line_group_id,line_item_id,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount				
		,year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,year_18_amount,year_19_amount,year_20_amount				
		,inv_year_1_amount,inv_year_2_amount,inv_year_3_amount,inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount			
		,sum_finplan,finished_year,display_proj_columns,approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year		
		,fin_year_1_amount,fin_year_2_amount,fin_year_3_amount,fin_year_4_amount,fin_total_amount			
		,previously_budgeted,gross_cost,financed_amount,net_cost					
		,type,investment_description,org_budget_flag			
		,updated,updated_by,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,priority,fk_user_adjustment_code	
		,fk_investment_phase_id,adjustment_code_status,goalId,targetId,tags,trans_desc,bud_process, actual_amt_year,fk_user_adjustment_code_no_desc, sum_finplan_net)	
		select fk_tenant_id			= @fk_tenant_id 
		,budget_year				= @budget_year
		,pk_main_project_code		= ''
		,main_project_name			= ''
		,proj_gr_1					= ''
		,proj_gr_name_1				= ''
		,proj_gr_2					= ''
		,proj_gr_name_2				= ''
		,proj_gr_3					= ''
		,proj_gr_name_3				= ''
		,proj_gr_4					= ''
		,proj_gr_name_4				= ''
		,proj_gr_5					= ''
		,proj_gr_name_5				= ''
		,inv_status					= 0
		,is_temp					= 0
		,completion_date			= '1900-01-01'
		,fk_account_code			= ''
		,fk_department_code			= ''
		,fk_function_code			= ''
		,fk_project_code			= ''
		,project_name				= ''
		,fk_change_id				= ''
		,fk_alter_code				= ''
		,fk_adjustment_code			= ''
		,fk_prog_code				= 1
		,header_dept				= ''
		,header_function			= ''
		,header_org_id_1			= ''
		,header_org_name_1			= ''
		,header_org_id_2			= ''
		,header_org_name_2			= ''
		,header_org_id_3			= ''
		,header_org_name_3			= ''
		,header_org_id_4			= ''
		,header_org_name_4			= ''
		,header_org_id_5			= ''
		,header_org_name_5			= ''
		,detail_org_id_1			= ''
		,detail_org_name_1			= ''
		,detail_org_id_2			= ''
		,detail_org_name_2			= ''
		,detail_org_id_3			= ''
		,detail_org_name_3			= ''
		,detail_org_id_4			= ''
		,detail_org_name_4			= ''
		,detail_org_id_5			= ''
		,detail_org_name_5			= ''
		,header_service_id_1		= ''
		,header_service_name_1		= ''
		,header_service_id_2		= ''
		,header_service_name_2		= ''
		,header_service_id_3		= ''
		,header_service_name_3		= ''
		,header_service_id_4		= ''
		,header_service_name_4		= ''
		,header_service_id_5		= ''
		,header_service_name_5		= ''
		,detail_service_id_1		= ''
		,detail_service_name_1		= ''
		,detail_service_id_2		= ''
		,detail_service_name_2		= ''
		,detail_service_id_3		= ''
		,detail_service_name_3		= ''
		,detail_service_id_4		= ''
		,detail_service_name_4		= ''
		,detail_service_id_5		= ''
		,detail_service_name_5		= ''
		,program_code_description	= ''
		,line_group_id				= ''
		,line_item_id				= ''
		,year_1_amount				= 0
		,year_2_amount				= 0
		,year_3_amount				= 0
		,year_4_amount				= 0
		,year_5_amount				= 0
		,year_6_amount				= 0
		,year_7_amount				= 0
		,year_8_amount				= 0
		,year_9_amount				= 0
		,year_10_amount				= 0
		,year_11_amount				= 0
		,year_12_amount				= 0
		,year_13_amount				= 0
		,year_14_amount				= 0
		,year_15_amount				= 0
		,year_16_amount				= 0
		,year_17_amount				= 0
		,year_18_amount				= 0
		,year_19_amount				= 0
		,year_20_amount				= 0
		,inv_year_1_amount			= 0
		,inv_year_2_amount			= 0
		,inv_year_3_amount			= 0
		,inv_year_4_amount			= 0
		,inv_year_5_amount			= 0
		,inv_year_6_amount			= 0
		,inv_year_7_amount			= 0
		,inv_year_8_amount			= 0
		,inv_year_9_amount			= 0
		,inv_year_10_amount			= 0
		,sum_finplan				= 0
		,finished_year				= 0
		,display_proj_columns		= 1
		,approved_cost				= 0
		,cost_estimate_p50			= 0
		,approval_reference			= ''
		,approval_ref_url			= ''
		,original_finish_year		= 0
		,fin_year_1_amount			= 0
		,fin_year_2_amount			= 0
		,fin_year_3_amount			= 0
		,fin_year_4_amount			= 0
		,fin_total_amount			= 0
		,previously_budgeted		= 0
		,gross_cost					= 0
		,financed_amount			= 0
		,net_cost					= 0
		,type						= 'i'
		,investment_description		= ''
		,org_budget_flag			= 1
		,updated					= getdate()
		,updated_by					= @user_id
		,free_dim_1					= ''
		,free_dim_2					= ''
		,free_dim_3					= ''
		,free_dim_4					= ''
		,vat_rate					= 0
		,vat_refund					= 0
		,priority					= 0
		,fk_user_adjustment_code	= ''
		,fk_investment_phase_id		= 0
		,adjustment_code_status		= ''
		,goalId						= '********-0000-0000-0000-************'
		,targetId					= '********-0000-0000-0000-************'
		,tags						= ''
		,trans_desc					= ''
		,bud_process				= ''
		,actual_amt_year			= 0
		,fk_user_adjustment_code_no_desc = ''
		,sum_finplan_net			= 0
		
		INSERT INTO twh_investment_report (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,proj_gr_1,proj_gr_name_1,proj_gr_2,proj_gr_name_2,proj_gr_3,proj_gr_name_3,proj_gr_4,proj_gr_name_4,proj_gr_5,proj_gr_name_5				
		,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function			
		,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,detail_org_id_1,detail_org_name_1			
		,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5
		,header_service_id_1,header_service_name_1,header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,header_service_name_4,header_service_id_5,header_service_name_5		
		,detail_service_id_1,detail_service_name_1,detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,detail_service_name_4,detail_service_id_5,detail_service_name_5		
		,program_code_description,line_group_id,line_item_id,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount				
		,year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,year_18_amount,year_19_amount,year_20_amount				
		,inv_year_1_amount,inv_year_2_amount,inv_year_3_amount,inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount			
		,sum_finplan,finished_year,display_proj_columns,approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year		
		,fin_year_1_amount,fin_year_2_amount,fin_year_3_amount,fin_year_4_amount,fin_total_amount			
		,previously_budgeted,gross_cost,financed_amount,net_cost					
		,type,investment_description,org_budget_flag			
		,updated,updated_by,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,priority,fk_user_adjustment_code	
		,fk_investment_phase_id,adjustment_code_status,goalId,targetId,tags,trans_desc,bud_process, actual_amt_year, fk_user_adjustment_code_no_desc, sum_finplan_net)	
		select fk_tenant_id			= @fk_tenant_id 
		,budget_year				= @budget_year
		,pk_main_project_code		= ''
		,main_project_name			= ''
		,proj_gr_1					= ''
		,proj_gr_name_1				= ''
		,proj_gr_2					= ''
		,proj_gr_name_2				= ''
		,proj_gr_3					= ''
		,proj_gr_name_3				= ''
		,proj_gr_4					= ''
		,proj_gr_name_4				= ''
		,proj_gr_5					= ''
		,proj_gr_name_5				= ''
		,inv_status					= 3
		,is_temp					= 0
		,completion_date			= '1900-01-01'
		,fk_account_code			= ''
		,fk_department_code			= ''
		,fk_function_code			= ''
		,fk_project_code			= ''
		,project_name				= ''
		,fk_change_id				= ''
		,fk_alter_code				= ''
		,fk_adjustment_code			= ''
		,fk_prog_code				= 1
		,header_dept				= ''
		,header_function			= ''
		,header_org_id_1			= ''
		,header_org_name_1			= ''
		,header_org_id_2			= ''
		,header_org_name_2			= ''
		,header_org_id_3			= ''
		,header_org_name_3			= ''
		,header_org_id_4			= ''
		,header_org_name_4			= ''
		,header_org_id_5			= ''
		,header_org_name_5			= ''
		,detail_org_id_1			= ''
		,detail_org_name_1			= ''
		,detail_org_id_2			= ''
		,detail_org_name_2			= ''
		,detail_org_id_3			= ''
		,detail_org_name_3			= ''
		,detail_org_id_4			= ''
		,detail_org_name_4			= ''
		,detail_org_id_5			= ''
		,detail_org_name_5			= ''
		,header_service_id_1		= ''
		,header_service_name_1		= ''
		,header_service_id_2		= ''
		,header_service_name_2		= ''
		,header_service_id_3		= ''
		,header_service_name_3		= ''
		,header_service_id_4		= ''
		,header_service_name_4		= ''
		,header_service_id_5		= ''
		,header_service_name_5		= ''
		,detail_service_id_1		= ''
		,detail_service_name_1		= ''
		,detail_service_id_2		= ''
		,detail_service_name_2		= ''
		,detail_service_id_3		= ''
		,detail_service_name_3		= ''
		,detail_service_id_4		= ''
		,detail_service_name_4		= ''
		,detail_service_id_5		= ''
		,detail_service_name_5		= ''
		,program_code_description	= ''
		,line_group_id				= ''
		,line_item_id				= ''
		,year_1_amount				= 0
		,year_2_amount				= 0
		,year_3_amount				= 0
		,year_4_amount				= 0
		,year_5_amount				= 0
		,year_6_amount				= 0
		,year_7_amount				= 0
		,year_8_amount				= 0
		,year_9_amount				= 0
		,year_10_amount				= 0
		,year_11_amount				= 0
		,year_12_amount				= 0
		,year_13_amount				= 0
		,year_14_amount				= 0
		,year_15_amount				= 0
		,year_16_amount				= 0
		,year_17_amount				= 0
		,year_18_amount				= 0
		,year_19_amount				= 0
		,year_20_amount				= 0
		,inv_year_1_amount			= 0
		,inv_year_2_amount			= 0
		,inv_year_3_amount			= 0
		,inv_year_4_amount			= 0
		,inv_year_5_amount			= 0
		,inv_year_6_amount			= 0
		,inv_year_7_amount			= 0
		,inv_year_8_amount			= 0
		,inv_year_9_amount			= 0
		,inv_year_10_amount			= 0
		,sum_finplan				= 0
		,finished_year				= 0
		,display_proj_columns		= 1
		,approved_cost				= 0
		,cost_estimate_p50			= 0
		,approval_reference			= ''
		,approval_ref_url			= ''
		,original_finish_year		= 0
		,fin_year_1_amount			= 0
		,fin_year_2_amount			= 0
		,fin_year_3_amount			= 0
		,fin_year_4_amount			= 0
		,fin_total_amount			= 0
		,previously_budgeted		= 0
		,gross_cost					= 0
		,financed_amount			= 0
		,net_cost					= 0
		,type						= 'i'
		,investment_description		= ''
		,org_budget_flag			= 1
		,updated					= getdate()
		,updated_by					= @user_id
		,free_dim_1					= ''
		,free_dim_2					= ''
		,free_dim_3					= ''
		,free_dim_4					= ''
		,vat_rate					= 0
		,vat_refund					= 0
		,priority					= 0
		,fk_user_adjustment_code	= ''
		,fk_investment_phase_id		= 0
		,adjustment_code_status		= ''
		,goalId						= '********-0000-0000-0000-************'
		,targetId					= '********-0000-0000-0000-************'
		,tags						= ''
		,trans_desc					= ''
		,bud_process				= ''
		,actual_amt_year			= 0
		,fk_user_adjustment_code_no_desc = ''
		,sum_finplan_net			= 0
		
		INSERT INTO twh_investment_report (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,proj_gr_1,proj_gr_name_1,proj_gr_2,proj_gr_name_2,proj_gr_3,proj_gr_name_3,proj_gr_4,proj_gr_name_4,proj_gr_5,proj_gr_name_5				
		,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function			
		,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,detail_org_id_1,detail_org_name_1			
		,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5
		,header_service_id_1,header_service_name_1,header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,header_service_name_4,header_service_id_5,header_service_name_5		
		,detail_service_id_1,detail_service_name_1,detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,detail_service_name_4,detail_service_id_5,detail_service_name_5		
		,program_code_description,line_group_id,line_item_id,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount				
		,year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,year_18_amount,year_19_amount,year_20_amount				
		,inv_year_1_amount,inv_year_2_amount,inv_year_3_amount,inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount			
		,sum_finplan,finished_year,display_proj_columns,approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year		
		,fin_year_1_amount,fin_year_2_amount,fin_year_3_amount,fin_year_4_amount,fin_total_amount			
		,previously_budgeted,gross_cost,financed_amount,net_cost					
		,type,investment_description,org_budget_flag			
		,updated,updated_by,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,priority,fk_user_adjustment_code	
		,fk_investment_phase_id,adjustment_code_status,goalId,targetId,tags,trans_desc,bud_process, actual_amt_year, fk_user_adjustment_code_no_desc, sum_finplan_net)	
		select fk_tenant_id			= @fk_tenant_id 
		,budget_year				= @budget_year
		,pk_main_project_code		= ''
		,main_project_name			= ''
		,proj_gr_1					= ''
		,proj_gr_name_1				= ''
		,proj_gr_2					= ''
		,proj_gr_name_2				= ''
		,proj_gr_3					= ''
		,proj_gr_name_3				= ''
		,proj_gr_4					= ''
		,proj_gr_name_4				= ''
		,proj_gr_5					= ''
		,proj_gr_name_5				= ''
		,inv_status					= 4
		,is_temp					= 0
		,completion_date			= '1900-01-01'
		,fk_account_code			= ''
		,fk_department_code			= ''
		,fk_function_code			= ''
		,fk_project_code			= ''
		,project_name				= ''
		,fk_change_id				= ''
		,fk_alter_code				= ''
		,fk_adjustment_code			= ''
		,fk_prog_code				= 1
		,header_dept				= ''
		,header_function			= ''
		,header_org_id_1			= ''
		,header_org_name_1			= ''
		,header_org_id_2			= ''
		,header_org_name_2			= ''
		,header_org_id_3			= ''
		,header_org_name_3			= ''
		,header_org_id_4			= ''
		,header_org_name_4			= ''
		,header_org_id_5			= ''
		,header_org_name_5			= ''
		,detail_org_id_1			= ''
		,detail_org_name_1			= ''
		,detail_org_id_2			= ''
		,detail_org_name_2			= ''
		,detail_org_id_3			= ''
		,detail_org_name_3			= ''
		,detail_org_id_4			= ''
		,detail_org_name_4			= ''
		,detail_org_id_5			= ''
		,detail_org_name_5			= ''
		,header_service_id_1		= ''
		,header_service_name_1		= ''
		,header_service_id_2		= ''
		,header_service_name_2		= ''
		,header_service_id_3		= ''
		,header_service_name_3		= ''
		,header_service_id_4		= ''
		,header_service_name_4		= ''
		,header_service_id_5		= ''
		,header_service_name_5		= ''
		,detail_service_id_1		= ''
		,detail_service_name_1		= ''
		,detail_service_id_2		= ''
		,detail_service_name_2		= ''
		,detail_service_id_3		= ''
		,detail_service_name_3		= ''
		,detail_service_id_4		= ''
		,detail_service_name_4		= ''
		,detail_service_id_5		= ''
		,detail_service_name_5		= ''
		,program_code_description	= ''
		,line_group_id				= ''
		,line_item_id				= ''
		,year_1_amount				= 0
		,year_2_amount				= 0
		,year_3_amount				= 0
		,year_4_amount				= 0
		,year_5_amount				= 0
		,year_6_amount				= 0
		,year_7_amount				= 0
		,year_8_amount				= 0
		,year_9_amount				= 0
		,year_10_amount				= 0
		,year_11_amount				= 0
		,year_12_amount				= 0
		,year_13_amount				= 0
		,year_14_amount				= 0
		,year_15_amount				= 0
		,year_16_amount				= 0
		,year_17_amount				= 0
		,year_18_amount				= 0
		,year_19_amount				= 0
		,year_20_amount				= 0
		,inv_year_1_amount			= 0
		,inv_year_2_amount			= 0
		,inv_year_3_amount			= 0
		,inv_year_4_amount			= 0
		,inv_year_5_amount			= 0
		,inv_year_6_amount			= 0
		,inv_year_7_amount			= 0
		,inv_year_8_amount			= 0
		,inv_year_9_amount			= 0
		,inv_year_10_amount			= 0
		,sum_finplan				= 0
		,finished_year				= 0
		,display_proj_columns		= 1
		,approved_cost				= 0
		,cost_estimate_p50			= 0
		,approval_reference			= ''
		,approval_ref_url			= ''
		,original_finish_year		= 0
		,fin_year_1_amount			= 0
		,fin_year_2_amount			= 0
		,fin_year_3_amount			= 0
		,fin_year_4_amount			= 0
		,fin_total_amount			= 0
		,previously_budgeted		= 0
		,gross_cost					= 0
		,financed_amount			= 0
		,net_cost					= 0
		,type						= 'i'
		,investment_description		= ''
		,org_budget_flag			= 1
		,updated					= getdate()
		,updated_by					= @user_id
		,free_dim_1					= ''
		,free_dim_2					= ''
		,free_dim_3					= ''
		,free_dim_4					= ''
		,vat_rate					= 0
		,vat_refund					= 0
		,priority					= 0
		,fk_user_adjustment_code	= ''
		,fk_investment_phase_id		= 0
		,adjustment_code_status		= ''
		,goalId						= '********-0000-0000-0000-************'
		,targetId					= '********-0000-0000-0000-************'
		,tags						= ''
		,trans_desc					= ''
		,bud_process				= ''
		,actual_amt_year			= 0
		,fk_user_adjustment_code_no_desc = ''
		,sum_finplan_net			= 0
	  
		INSERT INTO twh_investment_report (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,proj_gr_1,proj_gr_name_1,proj_gr_2,proj_gr_name_2,proj_gr_3,proj_gr_name_3,proj_gr_4,proj_gr_name_4,proj_gr_5,proj_gr_name_5				
		,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function			
		,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,detail_org_id_1,detail_org_name_1			
		,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5
		,header_service_id_1,header_service_name_1,header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,header_service_name_4,header_service_id_5,header_service_name_5		
		,detail_service_id_1,detail_service_name_1,detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,detail_service_name_4,detail_service_id_5,detail_service_name_5		
		,program_code_description,line_group_id,line_item_id,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount				
		,year_11_amount,year_12_amount,year_13_amount,year_14_amount,year_15_amount,year_16_amount,year_17_amount,year_18_amount,year_19_amount,year_20_amount				
		,inv_year_1_amount,inv_year_2_amount,inv_year_3_amount,inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount			
		,sum_finplan,finished_year,display_proj_columns,approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year		
		,fin_year_1_amount,fin_year_2_amount,fin_year_3_amount,fin_year_4_amount,fin_total_amount			
		,previously_budgeted,gross_cost,financed_amount,net_cost					
		,type,investment_description,org_budget_flag			
		,updated,updated_by,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,priority,fk_user_adjustment_code	
		,fk_investment_phase_id,adjustment_code_status,goalId,targetId,tags,trans_desc,bud_process, actual_amt_year, fk_user_adjustment_code_no_desc, sum_finplan_net)	
		select fk_tenant_id			= @fk_tenant_id 
		,budget_year				= @budget_year
		,pk_main_project_code		= ''
		,main_project_name			= ''
		,proj_gr_1					= ''
		,proj_gr_name_1				= ''
		,proj_gr_2					= ''
		,proj_gr_name_2				= ''
		,proj_gr_3					= ''
		,proj_gr_name_3				= ''
		,proj_gr_4					= ''
		,proj_gr_name_4				= ''
		,proj_gr_5					= ''
		,proj_gr_name_5				= ''
		,inv_status					= 5
		,is_temp					= 0
		,completion_date			= '1900-01-01'
		,fk_account_code			= ''
		,fk_department_code			= ''
		,fk_function_code			= ''
		,fk_project_code			= ''
		,project_name				= ''
		,fk_change_id				= ''
		,fk_alter_code				= ''
		,fk_adjustment_code			= ''
		,fk_prog_code				= 1
		,header_dept				= ''
		,header_function			= ''
		,header_org_id_1			= ''
		,header_org_name_1			= ''
		,header_org_id_2			= ''
		,header_org_name_2			= ''
		,header_org_id_3			= ''
		,header_org_name_3			= ''
		,header_org_id_4			= ''
		,header_org_name_4			= ''
		,header_org_id_5			= ''
		,header_org_name_5			= ''
		,detail_org_id_1			= ''
		,detail_org_name_1			= ''
		,detail_org_id_2			= ''
		,detail_org_name_2			= ''
		,detail_org_id_3			= ''
		,detail_org_name_3			= ''
		,detail_org_id_4			= ''
		,detail_org_name_4			= ''
		,detail_org_id_5			= ''
		,detail_org_name_5			= ''
		,header_service_id_1		= ''
		,header_service_name_1		= ''
		,header_service_id_2		= ''
		,header_service_name_2		= ''
		,header_service_id_3		= ''
		,header_service_name_3		= ''
		,header_service_id_4		= ''
		,header_service_name_4		= ''
		,header_service_id_5		= ''
		,header_service_name_5		= ''
		,detail_service_id_1		= ''
		,detail_service_name_1		= ''
		,detail_service_id_2		= ''
		,detail_service_name_2		= ''
		,detail_service_id_3		= ''
		,detail_service_name_3		= ''
		,detail_service_id_4		= ''
		,detail_service_name_4		= ''
		,detail_service_id_5		= ''
		,detail_service_name_5		= ''
		,program_code_description	= ''
		,line_group_id				= ''
		,line_item_id				= ''
		,year_1_amount				= 0
		,year_2_amount				= 0
		,year_3_amount				= 0
		,year_4_amount				= 0
		,year_5_amount				= 0
		,year_6_amount				= 0
		,year_7_amount				= 0
		,year_8_amount				= 0
		,year_9_amount				= 0
		,year_10_amount				= 0
		,year_11_amount				= 0
		,year_12_amount				= 0
		,year_13_amount				= 0
		,year_14_amount				= 0
		,year_15_amount				= 0
		,year_16_amount				= 0
		,year_17_amount				= 0
		,year_18_amount				= 0
		,year_19_amount				= 0
		,year_20_amount				= 0
		,inv_year_1_amount			= 0
		,inv_year_2_amount			= 0
		,inv_year_3_amount			= 0
		,inv_year_4_amount			= 0
		,inv_year_5_amount			= 0
		,inv_year_6_amount			= 0
		,inv_year_7_amount			= 0
		,inv_year_8_amount			= 0
		,inv_year_9_amount			= 0
		,inv_year_10_amount			= 0
		,sum_finplan				= 0
		,finished_year				= 0
		,display_proj_columns		= 1
		,approved_cost				= 0
		,cost_estimate_p50			= 0
		,approval_reference			= ''
		,approval_ref_url			= ''
		,original_finish_year		= 0
		,fin_year_1_amount			= 0
		,fin_year_2_amount			= 0
		,fin_year_3_amount			= 0
		,fin_year_4_amount			= 0
		,fin_total_amount			= 0
		,previously_budgeted		= 0
		,gross_cost					= 0
		,financed_amount			= 0
		,net_cost					= 0
		,type						= 'i'
		,investment_description		= ''
		,org_budget_flag			= 1
		,updated					= getdate()
		,updated_by					= @user_id
		,free_dim_1					= ''
		,free_dim_2					= ''
		,free_dim_3					= ''
		,free_dim_4					= ''
		,vat_rate					= 0
		,vat_refund					= 0
		,priority					= 0
		,fk_user_adjustment_code	= ''
		,fk_investment_phase_id		= 0
		,adjustment_code_status		= ''
		,goalId						= '********-0000-0000-0000-************'
		,targetId					= '********-0000-0000-0000-************'
		,tags						= ''
		,trans_desc					= ''
		,bud_process				= ''
		,actual_amt_year			= 0
		,fk_user_adjustment_code_no_desc = ''
		,sum_finplan_net			= 0

	IF @param19 = 'TRUE'
	-- param16 is BM_INV_DOC_SHOW_ONLY_1010
	BEGIN


	UPDATE #hlptab3 SET
	inv_year_1_amount = 0, 
	inv_year_2_amount = 0, 
	inv_year_3_amount = 0, 
	inv_year_4_amount = 0, 
	inv_year_5_amount = 0,
	inv_year_6_amount = 0, 
	inv_year_7_amount = 0, 
	inv_year_8_amount = 0, 
	inv_year_9_amount = 0, 
	inv_year_10_amount = 0,
	gross_cost = 0, 
	previously_budgeted = 0
	WHERE line_item_id  NOT IN  (1010,1020)

	END

	DELETE FROM tfp_inv_document_data WHERE budget_year = @budget_year AND fk_tenant_id = @fk_tenant_id

		INSERT INTO tfp_inv_document_data (fk_tenant_id,budget_year,fp_level_1_value,fp_level_1_name,fp_level_2_value,fp_level_2_name,
		pk_main_project_code,main_project_name,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,
		fk_function_code,fk_project_code,project_name,proj_gr_1, proj_gr_2, proj_gr_3, proj_gr_4, proj_gr_5,
		proj_gr_name_1, proj_gr_name_2, proj_gr_name_3, proj_gr_name_4, proj_gr_name_5,	
		fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,
		header_dept,header_function,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,header_org_id_3,
		header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,detail_org_id_1,detail_org_name_1,
		detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,detail_org_id_4,detail_org_name_4,detail_org_id_5,
		detail_org_name_5,header_service_id_1,header_service_name_1,header_service_id_2,header_service_name_2,header_service_id_3,
		header_service_name_3,header_service_id_4,header_service_name_4,header_service_id_5,header_service_name_5,
		detail_service_id_1,detail_service_name_1,detail_service_id_2,detail_service_name_2,detail_service_id_3,
		detail_service_name_3,detail_service_id_4,detail_service_name_4,detail_service_id_5,detail_service_name_5,
		program_code_description,line_group_id,line_item_id,year_1_amount,year_2_amount,year_3_amount,year_4_amount,
		year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,inv_year_1_amount,
		inv_year_2_amount,inv_year_3_amount,inv_year_4_amount,inv_year_5_amount,inv_year_6_amount,
		inv_year_7_amount,inv_year_8_amount,inv_year_9_amount,inv_year_10_amount,sum_finplan,finished_year,
		display_proj_columns,approved_cost,cost_estimate_p50,approval_reference,approval_ref_url,original_finish_year,fin_year_1_amount,
		fin_year_2_amount,fin_year_3_amount,fin_year_4_amount,fin_total_amount,previously_budgeted,gross_cost,
		financed_amount,net_cost,sub_header_code,sub_header_description,sum_code,sum_level,sum_code_2,sum_description_2,
		sub_level_sa_code,sub_level_sa_description,type, graph_type, graph_l1_value, graph_l1_name, graph_l2_value,
		graph_l2_name, graph_org_id, graph_org_name,fk_investment_phase_id,
		updated, updated_by,oe_flag
		,dynamic_gr_1
		,dynamic_gr_2
		,blist_gr_1
		,blist_gr_2)	
		SELECT fk_tenant_id,budget_year,fp_level_1_value,fp_level_1_name,fp_level_2_value,fp_level_2_name,
		pk_main_project_code,main_project_name,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,
		fk_function_code,fk_project_code,project_name,
		proj_gr_1, proj_gr_2, proj_gr_3, proj_gr_4, proj_gr_5,
		proj_gr_name_1, proj_gr_name_2, proj_gr_name_3, proj_gr_name_4, proj_gr_name_5,
		fk_change_id,fk_alter_code,fk_adjustment_code,
		fk_prog_code,header_dept,header_function,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,
		header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,
		detail_org_id_1,detail_org_name_1,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,
		detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5,header_service_id_1,header_service_name_1,
		header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,
		header_service_name_4,header_service_id_5,header_service_name_5,detail_service_id_1,detail_service_name_1,
		detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,
		detail_service_name_4,detail_service_id_5,detail_service_name_5,program_code_description,line_group_id,
		line_item_id
		,year_1_amount			=SUM(year_1_amount		)
		,year_2_amount			=SUM(year_2_amount		)
		,year_3_amount			=SUM(year_3_amount		)
		,year_4_amount			=SUM(year_4_amount		)
		,year_5_amount			=SUM(year_5_amount		)
		,year_6_amount			=SUM(year_6_amount		)
		,year_7_amount			=SUM(year_7_amount		)
		,year_8_amount			=SUM(year_8_amount		)
		,year_9_amount			=SUM(year_9_amount		)
		,year_10_amount			=SUM(year_10_amount		)
		,inv_year_1_amount		=SUM(inv_year_1_amount	)
		,inv_year_2_amount		=SUM(inv_year_2_amount	)
		,inv_year_3_amount		=SUM(inv_year_3_amount	)
		,inv_year_4_amount		=SUM(inv_year_4_amount	)
		,inv_year_5_amount		=SUM(inv_year_5_amount	)
		,inv_year_6_amount		=SUM(inv_year_6_amount	)
		,inv_year_7_amount		=SUM(inv_year_7_amount	)
		,inv_year_8_amount		=SUM(inv_year_8_amount	)
		,inv_year_9_amount		=SUM(inv_year_9_amount	)
		,inv_year_10_amount		=SUM(inv_year_10_amount	)
		,sum_finplan			=SUM(sum_finplan		)
		,finished_year,display_proj_columns
		,approved_cost			= SUM (approved_cost)
		,cost_estimate_p50			= SUM (cost_estimate_p50)
		,approval_reference,approval_ref_url,original_finish_year
		,fin_year_1_amount		=SUM(fin_year_1_amount	)
		,fin_year_2_amount		=SUM(fin_year_2_amount	)
		,fin_year_3_amount		=SUM(fin_year_3_amount	)
		,fin_year_4_amount		=SUM(fin_year_4_amount	)
		,fin_total_amount		=SUM(fin_total_amount	)
		,previously_budgeted	=SUM(previously_budgeted)
		,gross_cost				=SUM(gross_cost			)
		,financed_amount		=SUM(financed_amount	)
		,net_cost				=SUM(net_cost)
		,sub_header_code,sub_header_description,sum_code,sum_level,sum_code_2,sum_description_2,sub_level_sa_code,
		sub_level_sa_description,type,graph_type, graph_l1_value, graph_l1_name, graph_l2_value,
		graph_l2_name, graph_org_id, graph_org_name, fk_investment_phase_id,getdate() as updated, @user_id as updated_by
		,oe_flag
		,dynamic_gr_1
		,dynamic_gr_2
		,blist_gr_1
		,blist_gr_2
	  FROM #hlptab3 WHERE org_budget_flag = 1 and adjustment_code_status = 'Godkjent'
	  GROUP BY
	  fk_tenant_id,budget_year,fp_level_1_value,fp_level_1_name,fp_level_2_value,fp_level_2_name,
		pk_main_project_code,main_project_name,inv_status,is_temp,completion_date,fk_account_code,fk_department_code,
		fk_function_code,fk_project_code,project_name,
		proj_gr_1, proj_gr_2, proj_gr_3, proj_gr_4, proj_gr_5,
		proj_gr_name_1, proj_gr_name_2, proj_gr_name_3, proj_gr_name_4, proj_gr_name_5,
		fk_change_id,fk_alter_code,fk_adjustment_code,
		fk_prog_code,header_dept,header_function,header_org_id_1,header_org_name_1,header_org_id_2,header_org_name_2,
		header_org_id_3,header_org_name_3,header_org_id_4,header_org_name_4,header_org_id_5,header_org_name_5,
		detail_org_id_1,detail_org_name_1,detail_org_id_2,detail_org_name_2,detail_org_id_3,detail_org_name_3,
		detail_org_id_4,detail_org_name_4,detail_org_id_5,detail_org_name_5,header_service_id_1,header_service_name_1,
		header_service_id_2,header_service_name_2,header_service_id_3,header_service_name_3,header_service_id_4,
		header_service_name_4,header_service_id_5,header_service_name_5,detail_service_id_1,detail_service_name_1,
		detail_service_id_2,detail_service_name_2,detail_service_id_3,detail_service_name_3,detail_service_id_4,
		detail_service_name_4,detail_service_id_5,detail_service_name_5,program_code_description,line_group_id,
		line_item_id
		,finished_year,display_proj_columns
		,approval_reference,approval_ref_url,original_finish_year
		,sub_header_code,sub_header_description,sum_code,sum_level,sum_code_2,sum_description_2,sub_level_sa_code,
		sub_level_sa_description,type,graph_type, graph_l1_value, graph_l1_name, graph_l2_value,
		graph_l2_name, graph_org_id, graph_org_name, fk_investment_phase_id
		,oe_flag
		,dynamic_gr_1
		,dynamic_gr_2
		,blist_gr_1
		,blist_gr_2
GO


