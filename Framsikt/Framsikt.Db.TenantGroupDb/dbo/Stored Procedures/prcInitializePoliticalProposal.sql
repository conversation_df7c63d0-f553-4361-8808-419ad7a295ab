CREATE OR ALTER PROCEDURE [dbo].[prcInitializePoliticalProposal]
	@tenant_id int,
	@budget_year int,
	@user_id int,
	@proposal_id UNIQUEIDENTIFIER,
	@isServiceSetUp bit

AS
BEGIN	

	print 'start new 1A and 1B'
	exec [prcPoliticalSimulation1A1B_2020]  @tenant_id=@tenant_id,@budget_year=@budget_year,@user_id=@user_id,@proposal_id=@proposal_id

	--IF @budget_year < 2021 
	--	BEGIN
	--		print ' start 2A FOR YEAR LESS THAN 2021'
	--		exec prcPoliticalSimulation_2A @tenant_id=@tenant_id,@budget_year=@budget_year,@user_id=@user_id,@proposal_id=@proposal_id	 

	--		print ' start 2B FOR YEAR LESS THAN 2021'
	--		exec prcPoliticalSimulation_2B @tenant_id=@tenant_id,@budget_year=@budget_year,@user_id=@user_id,@proposal_id=@proposal_id
	--	END
	--<PERSON><PERSON><PERSON>

		BEGIN
			print ' start 2A FOR YEAR GREATER THAN OR EQUAL TO 2021'
			exec prcPoliticalSimulation2A_2021 @tenant_id=@tenant_id,@budget_year=@budget_year,@user_id=@user_id,@proposal_id=@proposal_id	 

			print ' start 2B FOR YEAR GREATER THAN OR EQUAL TO 2021'
			exec prcPoliticalSimulation2B_2021 @tenant_id=@tenant_id,@budget_year=@budget_year,@user_id=@user_id,@proposal_id=@proposal_id
		END
	

	--Declare @cityID  NVARCHAR(25)

	--SET @cityID= (SELECT  distinct a.org_id_1 from tco_org_hierarchy a 
	--join tco_org_version b on a.fk_org_version = b.pk_org_version and a.fk_tenant_id = b.fk_tenant_id 
	--where a.fk_tenant_id = @tenant_id  and b.active =1 and CAST((CAST (@budget_year as nvarchar(4)) + '01') as INT) between b.period_from and b.period_to)  
	
	--IF (@cityID is NOT NULL OR @cityID <> '')
	--BEGIN
	--	INSERT tps_assignments (fk_proposal_id,fk_tenant_id,budget_year,assignment_id,assignment_name,description,description_history,updated,updated_by,start_date,end_date,category)
	--	select @proposal_id,@tenant_id,@budget_year,unique_assignmentid,assignment_name,description,NEWID(),getutcdate(),@user_id ,start_date,end_date,category
	--	from tbi_assignments where budget_year = @budget_year  and org_id =@cityID and org_created_at =@cityID and category in 
	--	(select pk_cat_id from tco_category where fk_tenant_id = @tenant_id and bm_flag =1)
	--	and isBudgetProposal = 1
	--END

END