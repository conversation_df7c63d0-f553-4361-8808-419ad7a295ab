CREATE OR ALTER PROCEDURE [dbo].[proc_bud_agresso_original]
@tenant_id INT,
@budget_year INT,
@export_id INT
AS


DROP TABLE IF EXISTS #account_list
 
--Fetch accounts to be included
SELECT b.pk_tenant_id, b.pk_account_code, c.type
INTO #account_list
FROM tco_accounts b
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code
WHERE @budget_year between DATEPART(YEAR,b.dateFrom) and DATEPART(YEAR,b.dateTo)
AND b.pk_tenant_id = @tenant_id
GROUP BY b.pk_tenant_id, b.pk_account_code, c.type

--Get defaults and set the value  instead of empty
DECLARE @defProject VARCHAR(50)
DECLARE @defFreeDim1 VARCHAR(50)
DECLARE @defFreeDim2 VARCHAR(50)
DECLARE @defFreeDim3 VARCHAR(50)
DECLARE @defFreeDim4 VARCHAR(50)
DECLARE @defAlterCode VARCHAR(50)
DECLARE @orgVersion VARCHAR(50)
DECLARE @orgVersionMainTenant VARCHAR(50) 
DECLARE @syncDeptList table (default_department_sub VARCHAR(50)) 

select @orgVersion=pk_org_version from tco_org_version where fk_tenant_id=@tenant_id and @budget_year*100+1 between period_from and period_to
select @orgVersionMainTenant=pk_org_version from tco_org_version where fk_tenant_id=(select top 1 fk_tenant_id from tco_sync_company_setup where sub_tenant_id = @tenant_id) and @budget_year*100+1 between period_from and period_to 

select @defProject= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenant_id and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='PROJECT' and fk_org_version= @orgVersion

select @defFreeDim1= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenant_id and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_1' and fk_org_version= @orgVersion

select @defFreeDim2= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenant_id and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_2' and fk_org_version= @orgVersion

select @defFreeDim3= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenant_id and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_3' and fk_org_version= @orgVersion

select @defFreeDim4= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenant_id and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_4' and fk_org_version= @orgVersion

select @defAlterCode= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenant_id and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='ALTER_CODE' and fk_org_version= @orgVersion

INSERT INTO @syncDeptList (default_department_sub)
    SELECT default_department_sub 
    FROM tco_sync_company_setup 
    WHERE sub_tenant_id=@tenant_id AND [status]=1 AND fk_org_version=@orgVersionMainTenant

--Fetch for non investment accounts from tbu_trans
SELECT @export_id [ExportId],0[FkActionId],fk_tenant_id[FkTenantId],description, budget_year[BudgetYear]
, fk_account_code[FkAccountCode],department_code[DepartmentCode],fk_function_code[FkFunctionCode]
,[FkProjectCode]	=	CASE WHEN fk_project_code = '' or fk_project_code IS NULL THEN isnull(@defProject,'')
						ELSE isnull(fk_project_code,'')
						END
,[FreeDim1]			=	CASE WHEN free_dim_1 = '' or free_dim_1 IS NULL THEN isnull(@defFreeDim1,'')
						ELSE isnull(free_dim_1,'')
						END
,[FreeDim2]			=	CASE WHEN free_dim_2 = '' or free_dim_2 IS NULL THEN isnull(@defFreeDim2,'')
						ELSE isnull(free_dim_2,'')
						END
,[FreeDim3]			=	CASE WHEN free_dim_3 = '' or free_dim_3 IS NULL THEN isnull(@defFreeDim3,'')
						ELSE isnull(free_dim_3,'')
						END
,[FreeDim4]			=	CASE WHEN free_dim_4 = '' or free_dim_4 IS NULL THEN isnull(@defFreeDim4,'')
						ELSE isnull(free_dim_4,'')
						END
,fk_adjustment_code[FkAdjustmentCode],fk_alter_code[FkAlterCode],
SUM(jan) Jan, SUM(feb) Feb,SUM(mar) Mar,SUM(apr) Apr,SUM(may) May,SUM(jun) Jun,SUM(jul) Jul,SUM(aug) Aug,SUM(sep) Sep,SUM(oct) Oct,SUM(nov) Nov,SUM(dec) Dec
FROM
(SELECT fk_tenant_id, budget_year,ISNULL(a.description,'')[description], fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,fk_adjustment_code,fk_alter_code,period,amount_year_1, round(amount_year_1, -3) as belop_i_1000, 
jan =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '01' THEN amount_year_1 ELSE 0 END,
feb =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '02' THEN amount_year_1 ELSE 0 END,
mar =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '03' THEN amount_year_1 ELSE 0 END,
apr =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '04' THEN amount_year_1 ELSE 0 END,
may =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '05' THEN amount_year_1 ELSE 0 END,
jun =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '06' THEN amount_year_1 ELSE 0 END,
jul =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '07' THEN amount_year_1 ELSE 0 END,
aug =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '08' THEN amount_year_1 ELSE 0 END,
sep =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '09' THEN amount_year_1 ELSE 0 END,
oct =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '10' THEN amount_year_1 ELSE 0 END,
nov =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '11' THEN amount_year_1 ELSE 0 END,
dec =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '12' THEN amount_year_1 ELSE 0 END
FROM tbu_trans_detail_original a
JOIN #account_list ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND ac.type = 'operations'
WHERE amount_year_1 != 0
AND a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
AND (NOT EXISTS(SELECT 1 FROM @syncDeptList) -- No filter if @syncDeptList is empty
	 OR ( a.department_code NOT IN (SELECT default_department_sub FROM @syncDeptList)) -- Apply filter if @syncDeptList has values
	)
) A
GROUP BY fk_tenant_id, budget_year,fk_account_code,department_code,fk_function_code, fk_project_code, free_dim_1, free_dim_2,free_dim_3,free_dim_4,fk_adjustment_code,fk_alter_code,description

UNION ALL

--Fetch for investment accounts old model pre 2020
SELECT @export_id [ExportId],0[FkActionId],fk_tenant_id[FkTenantId],description, budget_year[BudgetYear]
, fk_account_code[FkAccountCode],department_code[DepartmentCode],fk_function_code[FkFunctionCode]
,[FkProjectCode]	=	CASE WHEN fk_project_code = '' or fk_project_code IS NULL THEN isnull(@defProject,'')
						ELSE isnull(fk_project_code,'')
						END
,[FreeDim1]			=	CASE WHEN free_dim_1 = '' or free_dim_1 IS NULL THEN isnull(@defFreeDim1,'')
						ELSE isnull(free_dim_1,'')
						END
,[FreeDim2]			=	CASE WHEN free_dim_2 = '' or free_dim_2 IS NULL THEN isnull(@defFreeDim2,'')
						ELSE isnull(free_dim_2,'')
						END
,[FreeDim3]			=	CASE WHEN free_dim_3 = '' or free_dim_3 IS NULL THEN isnull(@defFreeDim3,'')
						ELSE isnull(free_dim_3,'')
						END
,[FreeDim4]			=	CASE WHEN free_dim_4 = '' or free_dim_4 IS NULL THEN isnull(@defFreeDim4,'')
						ELSE isnull(free_dim_4,'')
						END
,fk_adjustment_code[FkAdjustmentCode],fk_alter_code[FkAlterCode],
SUM(jan) Jan, SUM(feb) Feb,SUM(mar) Mar,SUM(apr) Apr,SUM(may) May,SUM(jun) Jun,SUM(jul) Jul,SUM(aug) Aug,SUM(sep) Sep,SUM(oct) Oct,SUM(nov) Nov,SUM(dec) Dec
FROM
(SELECT fk_tenant_id, budget_year,ISNULL(a.description,'')[description], fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,fk_adjustment_code,fk_alter_code,period,amount_year_1, round(amount_year_1, -3) as belop_i_1000, 
jan =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '01' THEN amount_year_1 ELSE 0 END,
feb =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '02' THEN amount_year_1 ELSE 0 END,
mar =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '03' THEN amount_year_1 ELSE 0 END,
apr =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '04' THEN amount_year_1 ELSE 0 END,
may =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '05' THEN amount_year_1 ELSE 0 END,
jun =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '06' THEN amount_year_1 ELSE 0 END,
jul =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '07' THEN amount_year_1 ELSE 0 END,
aug =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '08' THEN amount_year_1 ELSE 0 END,
sep =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '09' THEN amount_year_1 ELSE 0 END,
oct =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '10' THEN amount_year_1 ELSE 0 END,
nov =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '11' THEN amount_year_1 ELSE 0 END,
dec =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '12' THEN amount_year_1 ELSE 0 END
FROM tbu_trans_detail_original a
JOIN #account_list ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND ac.type = 'investment'
WHERE amount_year_1 != 0
AND a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
and @budget_year < 2020
AND (NOT EXISTS(SELECT 1 FROM @syncDeptList) -- No filter if @syncDeptList is empty
	 OR ( a.department_code NOT IN (SELECT default_department_sub FROM @syncDeptList)) -- Apply filter if @syncDeptList has values
	)
) A
GROUP BY fk_tenant_id, budget_year,fk_account_code,department_code,fk_function_code, fk_project_code, free_dim_1, free_dim_2,free_dim_3,free_dim_4,fk_adjustment_code,fk_alter_code,description

UNION ALL

--Fetch for investments new model for 2020 and onwards
SELECT @export_id [ExportId],0[FkActionId],fk_tenant_id[FkTenantId],description, budget_year[BudgetYear]
, fk_account_code[FkAccountCode],department_code[DepartmentCode],fk_function_code[FkFunctionCode]
,[FkProjectCode]	=	CASE WHEN fk_project_code = '' or fk_project_code IS NULL THEN isnull(@defProject,'')
						ELSE isnull(fk_project_code,'')
						END
,[FreeDim1]			=	CASE WHEN free_dim_1 = '' or free_dim_1 IS NULL THEN isnull(@defFreeDim1,'')
						ELSE isnull(free_dim_1,'')
						END
,[FreeDim2]			=	CASE WHEN free_dim_2 = '' or free_dim_2 IS NULL THEN isnull(@defFreeDim2,'')
						ELSE isnull(free_dim_2,'')
						END
,[FreeDim3]			=	CASE WHEN free_dim_3 = '' or free_dim_3 IS NULL THEN isnull(@defFreeDim3,'')
						ELSE isnull(free_dim_3,'')
						END
,[FreeDim4]			=	CASE WHEN free_dim_4 = '' or free_dim_4 IS NULL THEN isnull(@defFreeDim4,'')
						ELSE isnull(free_dim_4,'')
						END
,fk_adjustment_code[FkAdjustmentCode],fk_alter_code[FkAlterCode]
,SUM(jan) Jan
, 0 as Feb,0 as Mar,0 as Apr,0 as May,0 as Jun,0 as Jul,0 as Aug,0 as Sep,0 as Oct,0 as Nov,0 as Dec
FROM
(
select PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, SUM(pt.amount) as jan
, ISNULL(PT.description,'')[description]
, @budget_year as budget_year
, fk_adjustment_code = CONVERT(VARCHAR(4),@budget_year)+'VBUD'
, fk_alter_code = ''
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN #account_list ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND ac.type = 'investment'
JOIN	( --Fetch budget rounds to be included in finplan (no budget changes for current year)
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @tenant_id
		and budget_year < @budget_year
		UNION
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @tenant_id
		and budget_year = @budget_year
		and org_budget_flag = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
LEFT JOIN tco_proj_version PV ON PT.fk_tenant_id = PV.fk_tenant_id and @budget_year between PV.period_from and PV.period_to and PV.active = 1
LEFT JOIN tco_proj_hierarchy projHier on projHier.fk_tenant_id = P.fk_tenant_id 
                                         and p.pk_project_code = projHier.fk_project_code 
									      and projHier.fk_proj_version = PV.pk_proj_version
LEFT JOIN tfp_investment_allocate_status tas ON tas.fk_tenant_id = UAD.fk_tenant_id AND tas.fk_user_adjustment_code = UAD.pk_adj_code 
							                AND tas.budget_year = UAD.budget_year and tas.fk_main_project_code = projHier.proj_gr_1
											AND PT.fk_change_id = tas.fk_change_id
where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL) --Only include active investments and financing (NULL)
AND pt.fk_tenant_id = @tenant_id
AND year = @budget_year
AND @budget_year >= 2020
AND (tas.status = 3 OR tas.status IS NULL) -- get either approved investments or investments not allocated
AND (NOT EXISTS(SELECT 1 FROM @syncDeptList) -- No filter if @syncDeptList is empty
	 OR ( pt.fk_department_code NOT IN (SELECT default_department_sub FROM @syncDeptList)) -- Apply filter if @syncDeptList has values
)
GROUP BY PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, PT.description
, PT.fk_user_adjustment_code
, PT.fk_alter_code
HAVING ABS(SUM(amount))>0
) INV_new
GROUP BY fk_tenant_id ,description, budget_year , fk_account_code,department_code ,fk_function_code,fk_project_code ,free_dim_1 ,free_dim_2 ,free_dim_3 ,free_dim_4 ,fk_adjustment_code,fk_alter_code
HAVING ABS(SUM(jan))>0



