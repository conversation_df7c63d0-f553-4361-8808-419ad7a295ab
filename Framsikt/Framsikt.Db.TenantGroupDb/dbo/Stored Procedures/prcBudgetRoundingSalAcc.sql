CREATE OR ALTER PROCEDURE [dbo].[prcBudgetRoundingSalAcc] @rounding  INT, @budget_year INT, @fk_tenant_id INT, @userid INT, @org_level INT, @org_id NVARCHAR (25), @fk_org_version VARCHAR(25), @is_salary_account BIT
as 

DECLAR<PERSON> @period12 INT
DECLARE @rounded_code INT
DECLARE @timestamp datetime2
DECLARE @inserted_rows INT

DECLARE @aggregated_budget TABLE(
    bu_trans_id uniqueidentifier NOT NULL,
	fk_tenant_id INT NOT NULL,
	budget_year INT NOT NULL,
	fk_account_code NVARCHAR(25) NOT NULL,
	department_code NVARCHAR(25) NOT NULL,
	fk_function_code NVARCHAR(25) NOT NULL,
	fk_project_code NVARCHAR(25) NOT NULL,
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR(25) NOT NULL,
	free_dim_3 NVARCHAR(25) NOT NULL,
	free_dim_4 NVARCHAR(25) NOT NULL,
	description NVARCHAR(1400) NOT NULL,
	total_amount_old dec(18,2) NOT NULL,
	total_amount_new dec(18,2) NOT NULL
);

DECLARE @account_list TABLE(
	pk_tenant_id INT,
	pk_account_code nvarchar(50)
);

IF @is_salary_account = 1
BEGIN
	INSERT @account_list
	select pk_tenant_id,pk_account_code
	from tco_accounts a
	JOIN gmd_reporting_line b ON a.fk_kostra_account_code = b.fk_kostra_account_code and b.report = 'YBUD1'
	where pk_tenant_id = @fk_tenant_id
	and @budget_year BETWEEN DATEPART(YEAR,dateFrom) AND DATEPART(YEAR,dateTo)
	AND b.line_group_id = 3000
END

IF  @is_salary_account = 0
BEGIN
	INSERT @account_list
	select pk_tenant_id,pk_account_code
	from tco_accounts a
	JOIN gmd_reporting_line b ON a.fk_kostra_account_code = b.fk_kostra_account_code and b.report = 'YBUD1'
	where pk_tenant_id = @fk_tenant_id
	and @budget_year BETWEEN DATEPART(YEAR,dateFrom) AND DATEPART(YEAR,dateTo)
END

SET NOCOUNT ON;
set noexec off;

SET @period12 = CONVERT(CHAR(4), @budget_year) + CONVERT(CHAR(2),12)

IF @rounding NOT IN  (10,100,1000) 
BEGIN
	RAISERROR ('Rounding not supported', 0, 1) WITH NOWAIT
	RETURN 
END

IF	@rounding = 10
	BEGIN
	set @rounded_code = -1
	END
IF	@rounding = 100
	BEGIN
	set @rounded_code = -2
	END
IF	@rounding = 1000
	BEGIN
	set @rounded_code = -3
	END
;


select @timestamp = sysdatetime();
PRINT 'Job starting at ' + convert(nvarchar(19),@timestamp);
RAISERROR ('START : Delete old rounded records', 0, 1) WITH NOWAIT;

IF	@org_level = 1
	BEGIN
	DELETE a FROM tbu_trans_detail a
	JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
	JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
	WHERE a.fk_tenant_id = @fk_tenant_id and budget_year = @budget_year and budget_type = 99
	END
IF @org_level = 2
	BEGIN
	DELETE a FROM tbu_trans_detail a
	JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
	JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
	WHERE a.fk_tenant_id = @fk_tenant_id and budget_year = @budget_year and budget_type = 99
	AND org_id_2 = @org_id
	END
IF @org_level = 3
	BEGIN
	DELETE a FROM tbu_trans_detail a
	JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
	JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
	WHERE a.fk_tenant_id = @fk_tenant_id and budget_year = @budget_year and budget_type = 99
	AND org_id_3 = @org_id
	END
IF @org_level = 4
	BEGIN
	DELETE a FROM tbu_trans_detail a
	JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
	JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
	WHERE a.fk_tenant_id = @fk_tenant_id and budget_year = @budget_year and budget_type = 99
	AND org_id_4 = @org_id
	END
IF @org_level = 5
	BEGIN
	DELETE a FROM tbu_trans_detail a
	JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
	JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
	WHERE a.fk_tenant_id = @fk_tenant_id and budget_year = @budget_year and budget_type = 99
	AND org_id_5 = @org_id
	END
	; 


select @timestamp = sysdatetime();
PRINT 'FINISH: Delete old rounded records at ' + convert(nvarchar(19),@timestamp);
RAISERROR ('START : Fetching budget', 0, 1) WITH NOWAIT;

IF @org_level = 1
	BEGIN
	
	INSERT INTO @aggregated_budget (bu_trans_id,fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, total_amount_new, total_amount_old)
	SELECT NEWID() AS bu_trans_id, a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description
	, ROUND(SUM(amount_year_1),@rounded_code) total_amount_new,  SUM(amount_year_1) total_amount_old
	FROM tbu_trans_detail a
	JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
	JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
	WHERE a.fk_tenant_id = @fk_tenant_id 
	AND budget_year = @budget_year
	GROUP BY a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description;
	
	END

IF @org_level = 2
BEGIN
	
INSERT INTO @aggregated_budget (bu_trans_id,fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, total_amount_new, total_amount_old)
SELECT NEWID() AS bu_trans_id, a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, ROUND(SUM(amount_year_1),@rounded_code) total_amount_new,  SUM(amount_year_1) total_amount_old
FROM tbu_trans_detail a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id 
AND budget_year = @budget_year
AND b.org_id_2 = @org_id
GROUP BY a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description;
	
END

IF @org_level = 3
BEGIN
	
INSERT INTO @aggregated_budget (bu_trans_id,fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, total_amount_new, total_amount_old)
SELECT NEWID() AS bu_trans_id, a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, ROUND(SUM(amount_year_1),@rounded_code) total_amount_new,  SUM(amount_year_1) total_amount_old
FROM tbu_trans_detail a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id 
AND budget_year = @budget_year
AND b.org_id_3 = @org_id
GROUP BY a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description;
		
END

IF @org_level = 4
BEGIN
	
INSERT INTO @aggregated_budget (bu_trans_id,fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, total_amount_new, total_amount_old)
SELECT NEWID() AS bu_trans_id, a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description
, ROUND(SUM(amount_year_1),@rounded_code) total_amount_new,  SUM(amount_year_1) total_amount_old
FROM tbu_trans_detail a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id 
AND budget_year = @budget_year
AND b.org_id_4 = @org_id
GROUP BY a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description;
	
END

IF @org_level = 5
BEGIN
	
INSERT INTO @aggregated_budget (bu_trans_id,fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description, total_amount_new, total_amount_old)
SELECT NEWID() AS bu_trans_id, a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description
, ROUND(SUM(amount_year_1),@rounded_code) total_amount_new,  SUM(amount_year_1) total_amount_old
FROM tbu_trans_detail a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @fk_org_version
JOIN @account_list c ON a.fk_tenant_id = c.pk_tenant_id AND a.fk_account_code = c.pk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id 
AND budget_year = @budget_year
AND b.org_id_5 = @org_id
GROUP BY a.fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1,free_dim_2,free_dim_3,free_dim_4,description;

END
;


select @timestamp = sysdatetime()
PRINT 'FINISH: Fetching budget at ' + convert(nvarchar(19),@timestamp)

RAISERROR ('START : Insert into tbu_trans_detail', 0, 1) WITH NOWAIT

INSERT INTO tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,
fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,
budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,
allocation_pct,total_amount,tax_flag,holiday_flag,fk_pension_type,fk_action_id)
SELECT 
NEWID() AS pk_id,bu_trans_id,fk_tenant_id,5,0,fk_account_code,department_code,
fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'',0,description,
budget_year,period = @period12,99
,amount_year_1 = total_amount_new - total_amount_old
,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id, GETDATE() AS updated, @userid AS updated_by,
0
,total_amount = total_amount_new - total_amount_old,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,0 as fk_action_id
FROM @aggregated_budget
where total_amount_new - total_amount_old != 0
order by bu_trans_id;

--SELECT * FROM @rounded_budget;

SET @inserted_rows = (SELECT COUNT(*) FROM @aggregated_budget);

select @timestamp = sysdatetime()
PRINT 'FINISH: Insert into tbu_trans_detail at ' + convert(nvarchar(19),@timestamp) + '. ' + convert(nvarchar(25), @inserted_rows) + ' rows inserted.'
PRINT 'Job finished. Thank you and good night.'

