CREATE OR ALTER PROCEDURE [dbo].[prcUpdateProjectStatWh]

AS


-- NEW IMPLEMENTATION START

CREATE TABLE #hlptab_inv (pk_id int identity not null,
                [fk_tenant_id] [int] NOT NULL DEFAULT 0,
                [budget_year] [int] NOT NULL DEFAULT 0,
                [period] [int] NOT NULL DEFAULT 0,
                [fk_account_code] [nvarchar](25) NOT NULL DEFAULT '',
                [fk_department_code] [nvarchar](25) NOT NULL DEFAULT '',
                [fk_function_code] [nvarchar](25) NOT NULL DEFAULT '',
                [fk_project_code] [nvarchar](25) NOT NULL DEFAULT '',
                [fp_year_1_amount] [decimal](18, 2) NOT NULL DEFAULT '',
                [net_cost] [decimal](27, 2) NOT NULL DEFAULT 0,
                [org_budget_year_1] [decimal](18, 2) NOT NULL DEFAULT 0,
                [accounting_year_1] [decimal](18, 2) NOT NULL DEFAULT 0,
                [accounting_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
                [prev_actual] [decimal](18, 2) NOT NULL DEFAULT 0,
                [approval_cost] [decimal](18, 2) NOT NULL DEFAULT 0,
                cost_estimate_p50 [decimal](18, 2) NOT NULL DEFAULT 0,
                [unaprv_bud_change] [decimal](18, 2) NOT NULL DEFAULT 0
)

--Fetch accounting data for all years


select distinct ac.pk_tenant_id, ac.pk_account_code INTO #accounts FROM tco_accounts ac 
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
    JOIN tco_module_mapping m ON  m.fk_tenant_id = ac.pk_tenant_id AND m.fk_module_id = 5

SELECT a.fk_tenant_id, a.gl_year, a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code
,a.period, a.amount
INTO #hlp_acc_data
FROM  tfp_accounting_data a
   JOIN #accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code 

SELECT a.fk_tenant_id, a.gl_year, a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code
,a.period, SUM(amount) AS amount  
INTO #hlptab1
FROM  #hlp_acc_data a
    JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND gl_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
    JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
    JOIN tco_module_mapping m ON  m.fk_tenant_id = a.fk_tenant_id AND m.fk_module_id = 5
GROUP BY  a.fk_tenant_id, a.gl_year, a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, a.period
HAVING SUM(amount) != 0 

--Fetch only for current budget year
DECLARE @budget_year INT = (SELECT DATEPART(YEAR,GETDATE()))

SELECT a.fk_tenant_id, @budget_year AS budget_year, CONVERT(VARCHAR(4),@budget_year)+ SUBSTRING(CONVERT(VARCHAR(6),period),5,2) AS period, 
a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code
,amount = CASE WHEN gl_year = @budget_year THEN a.amount ELSE 0 END
,amount_all = CASE WHEN gl_year < @budget_year THEN a.amount ELSE 0 END
INTO #hlptab2
FROM #hlptab1 a

--Fetch for previous year
SET @budget_year = @budget_year -1

INSERT INTO #hlptab2 (fk_tenant_id, budget_year, period,fk_account_code, department_code, fk_function_code, fk_project_code, amount, amount_all)
SELECT a.fk_tenant_id, @budget_year AS budget_year,CONVERT(VARCHAR(4),@budget_year)+ SUBSTRING(CONVERT(VARCHAR(6),period),5,2)  ,
a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code
,amount = CASE WHEN gl_year = @budget_year THEN a.amount ELSE 0 END
,amount_all =CASE  WHEN gl_year < @budget_year THEN a.amount ELSE 0 END
FROM #hlptab1 a

--Fetch for year -2
SET @budget_year = @budget_year -1

INSERT INTO #hlptab2 (fk_tenant_id, budget_year, period,fk_account_code, department_code, fk_function_code, fk_project_code, amount, amount_all)
SELECT a.fk_tenant_id, @budget_year AS budget_year,CONVERT(VARCHAR(4),@budget_year)+ SUBSTRING(CONVERT(VARCHAR(6),period),5,2)  ,
a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code
,amount = CASE WHEN gl_year = @budget_year THEN a.amount ELSE 0 END
,amount_all =CASE  WHEN gl_year < @budget_year THEN a.amount ELSE 0 END
FROM #hlptab1 a

DELETE FROM #hlptab2 WHERE amount=0 AND amount_all = 0 


--Insert accounting data into temp inv table
INSERT INTO #hlptab_inv (
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount
,net_cost
,org_budget_year_1
,accounting_year_1
,accounting_prev_year
,unaprv_bud_change
)
SELECT 
a.fk_tenant_id
,a.budget_year
,period
,fk_account_code,a.department_code,a.fk_function_code,fk_project_code
,fp_year_1_amount = 0
,net_cost = 0
,org_budget_year_1 = 0
,accounting_year_1 = a.amount
,accounting_prev_year = a.amount_all 
,unaprv_bud_change=0
FROM #hlptab2 a


--Start fetch investment details

SET @budget_year = (SELECT DATEPART(YEAR,GETDATE()))

--Fetch original budget current year
INSERT INTO #hlptab_inv (
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount
,net_cost
,org_budget_year_1
,accounting_year_1
,accounting_prev_year
,approval_cost
,cost_estimate_p50
,unaprv_bud_change
)
SELECT 
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount = SUM(fp_year_1_amount)
,net_cost = SUM(net_cost)
,org_budget_year_1 = SUM(org_budget_year_1)
,accounting_year_1 = 0
,accounting_prev_year = 0
,SUM(approval_cost)
,SUM(cost_estimate_p50)
,SUM(unaprv_bud_change)
FROM
(
        select 
        PT.fk_tenant_id
        ,budget_year = @budget_year
        ,period = @budget_year*100+1
        ,PT.fk_account_code
        ,PT.fk_department_code
        ,PT.fk_function_code
        ,PT.fk_project_code
        ,fp_year_1_amount = 0
        ,net_cost = 0
        ,org_budget_year_1 = SUM(amount)
        ,approval_cost = 0
        ,cost_estimate_p50 = 0
        ,unaprv_bud_change = 0
       from tfp_proj_transactions PT
       LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
       LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
       JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
       JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
       JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
       JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                    select fk_tenant_id, pk_change_id from tfp_budget_changes
                    where budget_year < @budget_year
                    UNION
                    select fk_tenant_id, pk_change_id from tfp_budget_changes
                    where budget_year = @budget_year
                    and org_budget_flag = 1
             )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
       where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
        and PT.year = @budget_year
        AND (from_sync = 0 OR from_sync IS NULL)
       GROUP BY PT.fk_tenant_id,PT.fk_account_code,PT.fk_department_code,PT.fk_function_code,
       PT.fk_project_code
       HAVING ABS(SUM(amount))>0
UNION ALL
--Fetch revised budget current year
       select 
        PT.fk_tenant_id
        ,budget_year = @budget_year
        ,period = @budget_year*100+1
        ,PT.fk_account_code
        ,PT.fk_department_code
        ,PT.fk_function_code
        ,PT.fk_project_code
        ,fp_year_1_amount = CASE WHEN PT.year = @budget_year AND uad.status = 1 THEN amount ELSE 0 END
        ,net_cost = CASE WHEN PT.year >0 AND uad.status = 1 THEN amount ELSE 0 END
        ,org_budget_year_1 = 0
        ,approval_cost = CASE WHEN PT.year = -1 AND uad.status = 1 THEN PT.amount else 0 end
        ,cost_estimate_p50 = CASE WHEN PT.year = -2 AND uad.status = 1 THEN PT.amount else 0 end
        ,unaprv_bud_change = CASE WHEN PT.year = @budget_year AND uad.status = 0 AND bc.rebudget_approved = 1 THEN amount ELSE 0 END
       from tfp_proj_transactions PT
       LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
       LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
       JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
       JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
       JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
       JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                    select fk_tenant_id, pk_change_id,rebudget_approved from tfp_budget_changes
                    where budget_year < @budget_year
                    UNION
                    select fk_tenant_id, pk_change_id, rebudget_approved from tfp_budget_changes
                    where budget_year = @budget_year
                    --and org_budget_flag = 1
             )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
       where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
        AND (from_sync = 0 OR from_sync IS NULL)
) TRANS
GROUP BY fk_tenant_id,budget_year,period,fk_account_code,fk_department_code,fk_function_code,fk_project_code

--Fetch for previous year

SET @budget_year = @budget_year -1

--Fetch original budget current year
INSERT INTO #hlptab_inv (
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount
,net_cost
,org_budget_year_1
,accounting_year_1
,accounting_prev_year
,approval_cost
,cost_estimate_p50
,unaprv_bud_change
)
SELECT 
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount = SUM(fp_year_1_amount)
,net_cost = SUM(net_cost)
,org_budget_year_1 = SUM(org_budget_year_1)
,accounting_year_1 = 0
,accounting_prev_year = 0
,sum(approval_cost)
,sum(cost_estimate_p50)
,SUM(unaprv_bud_change)
FROM
(
        select 
        PT.fk_tenant_id
        ,budget_year = @budget_year
        ,period = @budget_year*100+1
        ,PT.fk_account_code
        ,PT.fk_department_code
        ,PT.fk_function_code
        ,PT.fk_project_code
        ,fp_year_1_amount = 0
        ,net_cost = 0
        ,org_budget_year_1 = SUM(amount)
        ,approval_cost = 0
        ,cost_estimate_p50 = 0
        ,unaprv_bud_change=0
       from tfp_proj_transactions PT
       LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
       LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
       JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
       JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
       JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
       JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                    select fk_tenant_id, pk_change_id from tfp_budget_changes
                    where budget_year < @budget_year
                    UNION
                    select fk_tenant_id, pk_change_id from tfp_budget_changes
                    where budget_year = @budget_year
                    and org_budget_flag = 1
             )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
       where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
        and PT.year = @budget_year
        AND (from_sync = 0 OR from_sync IS NULL)
       GROUP BY PT.fk_tenant_id,PT.fk_account_code,PT.fk_department_code,PT.fk_function_code,PT.fk_project_code
       HAVING ABS(SUM(amount))>0
UNION ALL
--Fetch revised budget current year
       select 
        PT.fk_tenant_id
        ,budget_year = @budget_year
        ,period = @budget_year*100+1
        ,PT.fk_account_code
        ,PT.fk_department_code
        ,PT.fk_function_code
        ,PT.fk_project_code
        ,fp_year_1_amount = CASE WHEN PT.year = @budget_year AND uad.status = 1 THEN amount ELSE 0 END
        ,net_cost = CASE WHEN PT.year >0 AND uad.status = 1 THEN amount ELSE 0 END
        ,org_budget_year_1 = 0
        ,approval_cost = CASE WHEN PT.year = -1 AND uad.status = 1 THEN PT.amount else 0 end
        ,cost_estimate_p50 = CASE WHEN PT.year = -2 AND uad.status = 1 THEN PT.amount else 0 end
        ,unaprv_bud_change = CASE WHEN PT.year = @budget_year AND uad.status = 0 AND bc.rebudget_approved = 1 THEN amount ELSE 0 END
        from tfp_proj_transactions PT
       LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
       LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
       JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
       JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
       JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
       JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                    select fk_tenant_id, pk_change_id, rebudget_approved from tfp_budget_changes
                    where budget_year < @budget_year
                    UNION
                    select fk_tenant_id, pk_change_id, rebudget_approved from tfp_budget_changes
                    where budget_year = @budget_year
                    --and org_budget_flag = 1
             )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
       where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
       AND (from_sync = 0 OR from_sync IS NULL)
) TRANS
GROUP BY fk_tenant_id,budget_year,period,fk_account_code,fk_department_code,fk_function_code,fk_project_code


--Fetch for year-2

SET @budget_year = @budget_year -1

--Fetch original budget current year
INSERT INTO #hlptab_inv (
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount
,net_cost
,org_budget_year_1
,accounting_year_1
,accounting_prev_year
,approval_cost
,cost_estimate_p50
,unaprv_bud_change
)
SELECT 
fk_tenant_id
,budget_year
,period
,fk_account_code,fk_department_code,fk_function_code
,fk_project_code
,fp_year_1_amount = SUM(fp_year_1_amount)
,net_cost = SUM(net_cost)
,org_budget_year_1 = SUM(org_budget_year_1)
,accounting_year_1 = 0
,accounting_prev_year = 0
,SUM(approval_cost)
,SUM(cost_estimate_p50)
,SUM(unaprv_bud_change)
FROM
(
        select 
        PT.fk_tenant_id
        ,budget_year = @budget_year
        ,period = @budget_year*100+1
        ,PT.fk_account_code
        ,PT.fk_department_code
        ,PT.fk_function_code
        ,PT.fk_project_code
        ,fp_year_1_amount = 0
        ,net_cost = 0
        ,org_budget_year_1 = SUM(amount)
        ,approval_cost = 0
        ,cost_estimate_p50 = 0
        ,unaprv_bud_change=0
       from tfp_proj_transactions PT
       LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
       LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
       JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
       JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
       JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
       JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                    select fk_tenant_id, pk_change_id from tfp_budget_changes
                    where budget_year < @budget_year
                    UNION
                    select fk_tenant_id, pk_change_id from tfp_budget_changes
                    where budget_year = @budget_year
                    and org_budget_flag = 1
             )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
       where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
        and PT.year = @budget_year
        AND (from_sync = 0 OR from_sync IS NULL)
       GROUP BY PT.fk_tenant_id,PT.fk_account_code,PT.fk_department_code,PT.fk_function_code,PT.fk_project_code
       HAVING ABS(SUM(amount))>0
UNION ALL
--Fetch revised budget current year
       select 
        PT.fk_tenant_id
        ,budget_year = @budget_year
        ,period = @budget_year*100+1
        ,PT.fk_account_code
        ,PT.fk_department_code
        ,PT.fk_function_code
        ,PT.fk_project_code
        ,fp_year_1_amount = CASE WHEN PT.year = @budget_year AND uad.status = 1 THEN amount ELSE 0 END
        ,net_cost = CASE WHEN PT.year >0 AND uad.status = 1 THEN amount ELSE 0 END
        ,org_budget_year_1 = 0
        ,approval_cost = CASE WHEN PT.year = -1 AND uad.status = 1 THEN PT.amount else 0 end
        ,cost_estimate_p50 = CASE WHEN PT.year = -2 AND uad.status = 1 THEN PT.amount else 0 end
        ,unaprv_bud_change = CASE WHEN PT.year = @budget_year AND uad.status = 0 AND bc.rebudget_approved = 1 THEN amount ELSE 0 END
       from tfp_proj_transactions PT
       LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
       LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
       JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
       JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
       JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
       JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                    select fk_tenant_id, pk_change_id,rebudget_approved from tfp_budget_changes
                    where budget_year < @budget_year
                    UNION
                    select fk_tenant_id, pk_change_id, rebudget_approved from tfp_budget_changes
                    where budget_year = @budget_year
                    --and org_budget_flag = 1
             )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
       where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
        AND (from_sync = 0 OR from_sync IS NULL)
) TRANS
GROUP BY fk_tenant_id,budget_year,period,fk_account_code,fk_department_code,fk_function_code,fk_project_code


--NEW IMPLEMENTATION END

PRINT 'Update project status table'
BEGIN TRANSACTION

truncate table twh_project_status

--insert for new model
INSERT INTO twh_project_status (
fk_tenant_id
,budget_year
,period
,pk_investment_id
,fk_org_id
,org_name
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,project
,investment
,fp_year_1_amount
,pk_main_project_code
,net_cost
,main_project_code
,main_project_name
,fk_portfolio_code
,portfolio_name
,project_manager
,org_budget_year_1
,level_1_id
,level_1_description
,accounting_year_1
,accounting_prev_year
,approval_cost
,cost_estimate_p50
,unaprv_bud_change
)
SELECT fk_tenant_id
,budget_year
,period
,pk_investment_id = 0
,fk_org_id = ''
,org_name = ''
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,project = ''
,investment = ''
,fp_year_1_amount
,pk_main_project_code = ''
,net_cost
,main_project_code = ''
,main_project_name = ''
,fk_portfolio_code = ''
,portfolio_name = ''
,project_manager = ''
,org_budget_year_1
,level_1_id = ''
,level_1_description = ''
,accounting_year_1
,accounting_prev_year
,approval_cost
,cost_estimate_p50
,unaprv_bud_change
FROM #hlptab_inv

--insert for new model end

--INSERT INTO twh_project_status (fk_tenant_id,budget_year,period,pk_investment_id,fk_org_id,org_name,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project,investment,fp_year_1_amount,pk_main_project_code,net_cost,main_project_code,main_project_name,fk_portfolio_code,portfolio_name,project_manager,org_budget_year_1,level_1_id,level_1_description,accounting_year_1,accounting_prev_year,unaprv_bud_change)
--SELECT fk_tenant_id,budget_year,period,pk_investment_id,fk_org_id,org_name,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project,investment,fp_year_1_amount,pk_main_project_code,net_cost,main_project_code,main_project_name,fk_portfolio_code,portfolio_name,project_manager,org_budget_year_1,level_1_id,level_1_description,accounting_year_1,accounting_prev_year,unaprv_bud_change = 0
--FROM #hlptab_inv

COMMIT TRANSACTION

drop table if exists #hlptab1
drop table if exists #hlptab2
DROP TABLE IF EXISTS #hlptab_inv


RETURN 0
