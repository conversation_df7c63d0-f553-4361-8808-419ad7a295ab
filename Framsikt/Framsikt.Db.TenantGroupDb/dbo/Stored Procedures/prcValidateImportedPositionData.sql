CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedPositionData]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@jobID bigint

AS
		--Clear all the error information--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET 
				fk_employee_id_error = 0,
				fk_position_id_error = 0,
				gender_error = 0,
				birth_date_error = 0,
				fk_account_code_error = 0,
				fk_department_code_error = 0,
				fk_function_code_error = 0,
				fk_project_code_error = 0,
				free_dim_1_error = 0,
				free_dim_2_error = 0,
				free_dim_3_error = 0,
				free_dim_4_error = 0,
				public_position_type_value_error = 0,
				position_percentage_error = 0,
				presence_percentage_error = 0,
				position_start_date_error = 0,
				position_end_date_error = 0,
				position_type_name_error = 0,
				position_type_value_error = 0,
				position_code_name_error = 0,
				position_code_error = 0,
				from_date_error = 0,
				to_date_error = 0,
				yearly_salary_error = 0,
				seniority_date_error = 0,
				name_error = 0,
				public_position_code_name_error = 0,
				salary_step_error = 0,
				fk_salary_type_error = 0,
				fk_salary_type_desc_error = 0,
				fk_pension_type_error = 0,
				external_reference_error = 0,
				fk_tax_rate_error = 0,
				monthly_salary_error = 0,
				leave_from_date_error = 0,
				leave_percentage_error = 0,
				leave_to_date_error = 0,
				leave_type_error = 0,
				error_count = 0
			WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year AND 
				tco_stage_position_import.user_id = @user_id;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET 
				fk_employee_id_error = 0,
				fk_position_id_error = 0,
				gender_error = 0,
				birth_date_error = 0,
				fk_account_code_error = 0,
				fk_department_code_error = 0,
				fk_function_code_error = 0,
				fk_project_code_error = 0,
				free_dim_1_error = 0,
				free_dim_2_error = 0,
				free_dim_3_error = 0,
				free_dim_4_error = 0,
				public_position_type_value_error = 0,
				position_percentage_error = 0,
				presence_percentage_error = 0,
				position_start_date_error = 0,
				position_end_date_error = 0,
				position_type_name_error = 0,
				position_type_value_error = 0,
				position_code_name_error = 0,
				position_code_error = 0,
				from_date_error = 0,
				to_date_error = 0,
				yearly_salary_error = 0,
				seniority_date_error = 0,
				name_error = 0,
				public_position_code_name_error = 0,
				salary_step_error = 0,
				fk_salary_type_error = 0,
				fk_salary_type_desc_error = 0,
				fk_pension_type_error = 0,
				external_reference_error = 0,
				fk_tax_rate_error = 0,
				monthly_salary_error = 0,
				leave_from_date_error = 0,
				leave_percentage_error = 0,
				leave_to_date_error = 0,
				leave_type_error = 0,
				error_count = 0
			WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year AND 
				tco_stage_position_import.job_id = @jobID;
		END

	UPDATE tco_stage_position_import
	SET 
	fk_employee_id = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.fk_employee_id IS NULL;

	UPDATE tco_stage_position_import
	SET 
	fk_position_id = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.fk_position_id IS NULL;

	UPDATE tco_stage_position_import
	SET 
	gender = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.gender IS NULL;

	UPDATE tco_stage_position_import
	SET 
	birth_date = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.birth_date IS NULL;

	UPDATE tco_stage_position_import
	SET 
	fk_account_code = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.fk_account_code IS NULL;

	UPDATE tco_stage_position_import
	SET 
	fk_department_code = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.fk_department_code IS NULL;

	UPDATE tco_stage_position_import
	SET 
	fk_function_code = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.fk_function_code IS NULL;

	UPDATE tco_stage_position_import
	SET 
	fk_project_code = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.fk_project_code IS NULL;

	UPDATE tco_stage_position_import
	SET 
	free_dim_1 = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.free_dim_1 IS NULL;

	UPDATE tco_stage_position_import
	SET 
	free_dim_2 = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.free_dim_2 IS NULL;

	UPDATE tco_stage_position_import
	SET 
	free_dim_3 = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.free_dim_3 IS NULL;

	UPDATE tco_stage_position_import
	SET 
	free_dim_4 = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.free_dim_4 IS NULL;

	UPDATE tco_stage_position_import
	SET 
	public_position_type_value = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.public_position_type_value IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_percentage = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_percentage IS NULL;

	UPDATE tco_stage_position_import
	SET 
	presence_percentage = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.presence_percentage IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_start_date = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_start_date IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_end_date = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_end_date IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_type_name = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_type_name IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_type_value = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_type_value IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_code_name = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_code_name IS NULL;

	UPDATE tco_stage_position_import
	SET 
	position_code = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_code IS NULL;

	UPDATE tco_stage_position_import
	SET 
	from_date = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.from_date IS NULL;

	UPDATE tco_stage_position_import
	SET 
	to_date = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.to_date IS NULL;

	UPDATE tco_stage_position_import
	SET 
	yearly_salary = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.yearly_salary IS NULL;

	UPDATE tco_stage_position_import
	SET 
	seniority_date = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.seniority_date IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[name] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[name] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[public_position_code_name] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[public_position_code_name] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[salary_step] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[salary_step] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[fk_salary_type] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[fk_salary_type] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[fk_salary_type_desc] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[fk_salary_type_desc] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[fk_pension_type] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[fk_pension_type] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[external_reference] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[external_reference] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[fk_tax_rate] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[fk_tax_rate] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[monthly_salary] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[monthly_salary] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[leave_from_date] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[leave_from_date] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[leave_to_date] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[leave_to_date] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[leave_percentage] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[leave_percentage] IS NULL;

	UPDATE tco_stage_position_import
	SET 
	[leave_type] = ''
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.[leave_type] IS NULL;

	--Set default position_end_date if empty
	UPDATE tco_stage_position_import
	SET 
	position_end_date = '31.12.2099'
	WHERE tco_stage_position_import.fk_tenant_id = @tenant_id AND tco_stage_position_import.position_end_date = '';
	
	--Validate employee id--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_employee_id_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (fk_employee_id = '' OR ISNUMERIC(fk_employee_id) = 0);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_employee_id_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (fk_employee_id = '' OR ISNUMERIC(fk_employee_id) = 0);
		END

	--Validate position id--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_position_id_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (fk_position_id = '');
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_position_id_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (fk_position_id = '');
		END

	--Validate account codes--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_account_code_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			LEFT JOIN tco_accounts acc on acc.pk_account_code = a.fk_account_code AND 	a.fk_tenant_id = acc.pk_tenant_id 		
			WHERE a.fk_tenant_id = @tenant_id
			AND a.budget_year = @budget_year
			AND a.user_id = @user_id  
			AND acc.pk_account_code IS NULL
			AND a.fk_account_code != '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_account_code_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			LEFT JOIN tco_accounts acc on acc.pk_account_code = a.fk_account_code AND 	a.fk_tenant_id = acc.pk_tenant_id 		
			WHERE a.fk_tenant_id = @tenant_id
			AND a.budget_year = @budget_year
			AND a.job_id = @jobID  
			AND acc.pk_account_code IS NULL
			AND a.fk_account_code != '';
		END
	
	
	--Validate department codes --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_department_code_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import  
			LEFT JOIN tco_departments DEPTS ON DEPTS.pk_department_code = tco_stage_position_import.fk_department_code AND 
				tco_stage_position_import.fk_tenant_id = DEPTS.fk_tenant_id AND 
				@budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
			WHERE 
				tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year AND 
				tco_stage_position_import.user_id = @user_id  AND
				DEPTS.pk_department_code IS NULL OR tco_stage_position_import.fk_department_code = '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_department_code_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import  
			LEFT JOIN tco_departments DEPTS ON DEPTS.pk_department_code = tco_stage_position_import.fk_department_code AND 
				tco_stage_position_import.fk_tenant_id = DEPTS.fk_tenant_id AND
				@budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
			WHERE  
				tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year AND 
				tco_stage_position_import.job_id = @jobID  AND
				DEPTS.pk_department_code IS NULL OR tco_stage_position_import.fk_department_code = '';
		END
	
	
	--Validate function codes --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import set fk_function_code_error = 1, 
											error_count = error_count + 1
			FROM tco_stage_position_import
			LEFT OUTER JOIN tco_functions on tco_stage_position_import.fk_tenant_id = tco_functions.pk_tenant_id AND  
				tco_stage_position_import.fk_function_code = tco_functions.pk_Function_code
			WHERE
			tco_stage_position_import.fk_tenant_id = @tenant_id AND 
			tco_stage_position_import.budget_year = @budget_year AND 
			tco_stage_position_import.user_id = @user_id and
			tco_functions.pk_Function_code IS NULL AND 
			tco_stage_position_import.fk_function_code <> '' AND
			tco_stage_position_import.fk_function_code IS NOT NULL;	
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import set fk_function_code_error = 1, 
											error_count = error_count + 1
			FROM tco_stage_position_import
			LEFT OUTER JOIN tco_functions on tco_stage_position_import.fk_tenant_id = tco_functions.pk_tenant_id AND  
				tco_stage_position_import.fk_function_code = tco_functions.pk_Function_code
			WHERE
			tco_stage_position_import.fk_tenant_id = @tenant_id AND 
			tco_stage_position_import.budget_year = @budget_year AND 
			tco_stage_position_import.job_id = @jobID and
			tco_functions.pk_Function_code IS NULL AND 
			tco_stage_position_import.fk_function_code <> '' AND
			tco_stage_position_import.fk_function_code IS NOT NULL;	
		END

	--Validate Projects codes--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_project_code_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_projects prj on 
				tco_stage_position_import.fk_tenant_id = prj.fk_tenant_id AND
				tco_stage_position_import.fk_project_code = prj.pk_project_code AND 			
				tco_stage_position_import.budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND
				prj.active = 1
			WHERE prj.pk_project_code IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.user_id = @user_id
				AND tco_stage_position_import.fk_project_code != '' 
				AND tco_stage_position_import.fk_project_code IS NOT NULL
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_project_code_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_projects prj on 
				tco_stage_position_import.fk_tenant_id = prj.fk_tenant_id AND
				tco_stage_position_import.fk_project_code = prj.pk_project_code AND 			
				tco_stage_position_import.budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND
				prj.active = 1
			WHERE prj.pk_project_code IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.job_id = @jobID
				AND tco_stage_position_import.fk_project_code != '' 
				AND tco_stage_position_import.fk_project_code IS NOT NULL
		END
	  
		
	-- Validate free dim 1 --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_1_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_1 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.user_id = @user_id  AND
				tco_stage_position_import.free_dim_1 != '' AND
				tco_stage_position_import.free_dim_1 IS NOT NULL;  
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_1_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_1 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.job_id = @jobID  AND
				tco_stage_position_import.free_dim_1 != '' AND
				tco_stage_position_import.free_dim_1 IS NOT NULL;  
		END
	

	-- Validate free dim 2 --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_2_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_2 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.user_id = @user_id  AND
				tco_stage_position_import.free_dim_2 != '' AND
				tco_stage_position_import.free_dim_2 IS NOT NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_2_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_2 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.job_id = @jobID  AND
				tco_stage_position_import.free_dim_2 != '' AND
				tco_stage_position_import.free_dim_2 IS NOT NULL;
		END
	  

	-- Validate free dim 3 --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_3_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_3 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.user_id = @user_id  AND
				tco_stage_position_import.free_dim_3 != '' AND
				tco_stage_position_import.free_dim_3 IS NOT NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_3_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_3 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.job_id = @jobID  AND
				tco_stage_position_import.free_dim_3 != '' AND
				tco_stage_position_import.free_dim_3 IS NOT NULL;
		END
	  

	-- Validate free dim 4 --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_4_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_4 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.user_id = @user_id AND
				tco_stage_position_import.free_dim_4 != '' AND
				tco_stage_position_import.free_dim_4 IS NOT NULL; 
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET free_dim_4_error = 1, error_count = error_count + 1
			FROM tco_stage_position_import 
			LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tco_stage_position_import.free_dim_4 AND 
				tco_stage_position_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
			WHERE frdm.free_dim_column IS NULL
			AND tco_stage_position_import.fk_tenant_id = @tenant_id AND 
				tco_stage_position_import.budget_year = @budget_year  AND
				tco_stage_position_import.job_id = @jobID AND
				tco_stage_position_import.free_dim_4 != '' AND
				tco_stage_position_import.free_dim_4 IS NOT NULL; 
		END
	

	--Validate position percentage--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET position_percentage_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (position_percentage = '' OR ISNUMERIC(position_percentage) = 0);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET position_percentage_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (position_percentage = '' OR ISNUMERIC(position_percentage) = 0);
		END

	--Validate presence percentage--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET presence_percentage_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (presence_percentage = '' OR ISNUMERIC(presence_percentage) = 0 OR CAST(presence_percentage AS DECIMAL(7,2)) > 100);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET presence_percentage_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (presence_percentage = '' OR ISNUMERIC(presence_percentage) = 0 OR CAST(presence_percentage AS DECIMAL(7,2)) > 100);
		END
	
	--Validate position type name--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET position_type_name_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND position_type_name = '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET position_type_name_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND position_type_name = '';
		END

	--Validate position type value--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET position_type_value_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (position_type_value = '' OR LEN(position_type_value) > 2);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET position_type_value_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (position_type_value = '' OR LEN(position_type_value) > 2);
		END

	--Validate position code name--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET position_code_name_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND position_code_name = '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET position_code_name_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND position_code_name = '';
		END

	--Validate position code--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET position_code_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (position_code = '');
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET position_code_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (position_code = '');
		END

	--Validate position_start_date--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET position_start_date_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id
			AND position_start_date = '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET position_start_date_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND position_start_date = '';
		END

	--Validate yearly salary--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET yearly_salary_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND (yearly_salary = '' OR ISNUMERIC(yearly_salary) = 0);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET yearly_salary_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND (yearly_salary = '' OR ISNUMERIC(yearly_salary) = 0);
		END

	--Validate seniority date--
	--IF( @jobID = -1 )
	--	BEGIN
	--		UPDATE tco_stage_position_import
	--		SET seniority_date_error = 1, error_count = error_count + 1 
	--		FROM tco_stage_position_import a
	--		WHERE fk_tenant_id = @tenant_id
	--		AND budget_year = @budget_year
	--		AND user_id = @user_id  
	--		AND seniority_date = '';
	--	END
	--ELSE
	--	BEGIN
	--		UPDATE tco_stage_position_import
	--		SET seniority_date_error = 1, error_count = error_count + 1 
	--		FROM tco_stage_position_import a
	--		WHERE fk_tenant_id = @tenant_id
	--		AND budget_year = @budget_year
	--		AND job_id = @jobID  
	--		AND seniority_date = '';
	--	END

	--Validate name--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET name_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND [name] = '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET name_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND [name] = '';
		END

	--Validate public_position_code_name--
	--IF( @jobID = -1 )
	--	BEGIN
	--		UPDATE tco_stage_position_import
	--		SET public_position_code_name_error = 1, error_count = error_count + 1 
	--		FROM tco_stage_position_import a
	--		WHERE fk_tenant_id = @tenant_id
	--		AND budget_year = @budget_year
	--		AND user_id = @user_id  
	--		AND public_position_code_name = '';
	--	END
	--ELSE
	--	BEGIN
	--		UPDATE tco_stage_position_import
	--		SET public_position_code_name_error = 1, error_count = error_count + 1 
	--		FROM tco_stage_position_import a
	--		WHERE fk_tenant_id = @tenant_id
	--		AND budget_year = @budget_year
	--		AND job_id = @jobID  
	--		AND public_position_code_name = '';
	--	END

	--Validate salary_step--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET salary_step_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id 
			AND len(salary_step)>2
			--AND salary_step != ''
			--AND (ISNUMERIC(salary_step) = 0 OR (salary_step NOT BETWEEN 1 AND 80)); --129511
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET salary_step_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			And len(salary_step)>2
			--AND salary_step != ''
			--AND (ISNUMERIC(salary_step) = 0 OR (salary_step NOT BETWEEN 1 AND 80)); -->129511
		END

	--Validate fk_tax_rate--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_tax_rate_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id  
			AND fk_tax_rate != ''
			AND (ISNUMERIC(fk_tax_rate) = 0)
			OR ((CHARINDEX('.', fk_tax_rate) BETWEEN 1 AND 19) AND ( LEN(fk_tax_rate) - CHARINDEX('.', fk_tax_rate) > 3));
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET fk_tax_rate_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID
			AND fk_tax_rate != ''
			AND (ISNUMERIC(fk_tax_rate) = 0)
			OR ((CHARINDEX('.', fk_tax_rate) BETWEEN 1 AND 19) AND ( LEN(fk_tax_rate) - CHARINDEX('.', fk_tax_rate) > 3));
		END

	--Validate monthly_salary--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_position_import
			SET monthly_salary_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND user_id = @user_id
			AND monthly_salary != ''
			AND (ISNUMERIC(monthly_salary) = 0)
			OR ((CHARINDEX('.', monthly_salary) BETWEEN 1 AND 19) AND ( LEN(monthly_salary) - CHARINDEX('.', monthly_salary) > 2));
		END
	ELSE
		BEGIN
			UPDATE tco_stage_position_import
			SET monthly_salary_error = 1, error_count = error_count + 1 
			FROM tco_stage_position_import a
			WHERE fk_tenant_id = @tenant_id
			AND budget_year = @budget_year
			AND job_id = @jobID  
			AND monthly_salary != ''
			AND (ISNUMERIC(monthly_salary) = 0)
			OR ((CHARINDEX('.', monthly_salary) BETWEEN 1 AND 19) AND ( LEN(monthly_salary) - CHARINDEX('.', monthly_salary) > 2));
		END
	
RETURN 0