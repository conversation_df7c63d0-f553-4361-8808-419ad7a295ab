CREATE OR ALTER    PROCEDURE [dbo].[prcInitializeForecastReport] @forecast_period  INT= 0, @tenant_id INT = 0
as 

DECLAR<PERSON> @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @timestamp datetime2
DECLARE @data_updated INT
DECLARE @current_year INT
DECLARE @next_year INT
DECLARE @continue INT
DECLARE @rowcount INT


DECLARE @changed_table TABLE(
    fk_tenant_id INT  NOT NULL,
	forecast_period INT  NOT NULL
);


DECLARE @last_period INT

IF (SELECT datepart (month,GETDATE())-3) <=0 
BEGIN
set  @last_period =  (SELECT CONVERT(CHAR(4),datepart(year, getdate())-1)+ CONVERT(CHAR(2),'12'))
END
ELSE 
BEGIN
set @last_period = (SELECT CONVERT(CHAR(4),datepart(year, getdate())) + '0' + CONVERT(CHAR(1),(SELECT datepart (month,GETDATE())-3)))
END

IF @forecast_period = 0 AND @tenant_id = 0 
BEGIN

INSERT INTO @changed_table (fk_tenant_id, forecast_period)
SELECT DISTINCT fk_tenant_id, forecast_period 
FROM [dbo].[tmr_period_setup]
WHERE status = 2 AND forecast_period >= @last_period


END

IF @forecast_period != 0 AND @tenant_id = 0 
BEGIN

INSERT INTO @changed_table (fk_tenant_id, forecast_period)
SELECT DISTINCT fk_tenant_id, forecast_period 
FROM [dbo].[tmr_period_setup]
WHERE status = 2
AND forecast_period = @forecast_period

END

IF @forecast_period = 0 AND @tenant_id != 0 
BEGIN

INSERT INTO @changed_table (fk_tenant_id, forecast_period)
SELECT DISTINCT fk_tenant_id, forecast_period 
FROM [dbo].[tmr_period_setup]
WHERE status = 2
AND fk_tenant_id = @tenant_id

END

IF @forecast_period != 0 AND @tenant_id != 0 
BEGIN

INSERT INTO @changed_table (fk_tenant_id, forecast_period)
VALUES (@tenant_id, @forecast_period)

END

--SET @data_updated = (SELECT COUNT(*) FROM  @changed_table);

--IF @data_updated = 0 
--BEGIN
--PRINT 'No data changes last 24hrs. Job ends'
--RETURN
--END

--DELETE FROM [dbo].[twh_temp_triggered_budget] WHERE fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM @changed_table);
--DELETE FROM [dbo].[twh_temp_changed_budget] WHERE fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM @changed_table);


BEGIN
	UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'TWHFORECASTFULL';
	select @timestamp = sysdatetime();
	select @StartDate = sysdatetime();

	PRINT 'Job starting at ' + convert(nvarchar(19),@timestamp)
END 

RAISERROR ('START: Truncating table', 0, 1) WITH NOWAIT

BEGIN
SET  @continue = 1

WHILE @continue = 1
BEGIN
    PRINT GETDATE()
    SET ROWCOUNT 50000
    BEGIN TRANSACTION
    DELETE T FROM [twh_forecast_report] T INNER JOIN @changed_table c ON T.fk_tenant_id = c.fk_tenant_id AND T.forecast_period = c.forecast_period AND T.report_source in ('D','F')
    SET @rowcount = @@rowcount 
    COMMIT
    PRINT GETDATE()
    IF @rowcount = 0
    BEGIN
        SET @continue = 0
    END
END

END

SET ROWCOUNT 0;

CREATE TABLE  #TEMP_forecast_table (
	[fk_tenant_id] [int] NOT NULL,
	[forecast_period] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[account_name] [nvarchar](125) NULL,
	[department_code] [nvarchar](25) NOT NULL,
	[department_name] [nvarchar](125) NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[function_name] [nvarchar](125) NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[project_name] [nvarchar](125) NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[free_dim_1_name] [nvarchar](255) NULL,
	[free_dim_2_name] [nvarchar](255) NULL,
	[free_dim_3_name] [nvarchar](255) NULL,
	[free_dim_4_name] [nvarchar](255) NULL,
	[resource_id] [nvarchar](25) NULL,
	[resource_name] [nvarchar](512) NULL,
	[description] [nvarchar](255) NOT NULL,
	[period] [int] NOT NULL,
	[periodic_key] [nvarchar](50) NULL,
	[allocation_pct] [decimal](10, 7) NOT NULL,
	[org_id_1] [nvarchar](25) NULL,
	[org_name_1] [nvarchar](125) NULL,
	[org_id_2] [nvarchar](25) NULL,
	[org_name_2] [nvarchar](125) NULL,
	[org_id_3] [nvarchar](25) NULL,
	[org_name_3] [nvarchar](125) NULL,
	[org_id_4] [nvarchar](25) NULL,
	[org_name_4] [nvarchar](125) NULL,
	[org_id_5] [nvarchar](25) NULL,
	[org_name_5] [nvarchar](125) NULL,
	[service_id_1] [nvarchar](25) NULL,
	[service_name_1] [nvarchar](100) NULL,
	[service_id_2] [nvarchar](25) NULL,
	[service_name_2] [nvarchar](100) NULL,
	[service_id_3] [nvarchar](25) NULL,
	[service_name_3] [nvarchar](100) NULL,
	[service_id_4] [nvarchar](25) NULL,
	[service_name_4] [nvarchar](100) NULL,
	[service_id_5] [nvarchar](25) NULL,
	[service_name_5] [nvarchar](100) NULL,
	[fk_position_id] [nvarchar](25) NULL,
	[position_name] [nvarchar](150) NULL,
	[level_1_id] [nvarchar](25) NOT NULL,
	[level_1_description] [nvarchar](100) NOT NULL,
	[level_2_id] [nvarchar](25) NOT NULL,
	[level_2_description] [nvarchar](100) NOT NULL,
	[level_3_id] [nvarchar](25) NOT NULL,
	[level_3_description] [nvarchar](100) NOT NULL,
	[level_4_id] [nvarchar](25) NOT NULL,
	[level_4_description] [nvarchar](100) NOT NULL,
	[action_name] [nvarchar](255) NOT NULL,
	[forecast_amount] [decimal](18, 2) NOT NULL,
	[new_forecast] [decimal](18, 2) NOT NULL,
	[risk_amount] [decimal](18, 2) NOT NULL,
	[bud_amt_year] [decimal](18, 2) NOT NULL,
	[bud_amt_ytd] [decimal](18, 2) NOT NULL,
	[bud_amt_period] [decimal](18, 2) NOT NULL,
	[actual_amt_year] [decimal](18, 2) NOT NULL,
	[actual_amt_ytd] [decimal](18, 2) NOT NULL,
	[actual_amt_period] [decimal](18, 2) NOT NULL,
	[tech_amt_budget] [decimal](18, 2) NOT NULL,
	[tech_amt_running] [decimal](18, 2) NOT NULL,
	[org_bud_amt_year] [decimal](18, 2) NOT NULL,
	[actual_amt_last_year] [decimal](18, 2) NOT NULL,
	[actual_amt_last_ytd] [decimal](18, 2) NOT NULL,
	[budget_change_amount] [decimal](18, 2) NOT NULL,
	[risk_org_level] NVARCHAR(50) DEFAULT '' NOT NULL,
	[report_source] varchar(1) NOT NULL,
	[budget_type] INT DEFAULT 0 NOT NULL,
	[forecast_amt_incl_actions] DECIMAL(18, 2) NOT NULL DEFAULT 0
);




select @timestamp = sysdatetime();
PRINT 'Delete finished at ' + convert(nvarchar(19),@timestamp);

--RAISERROR ('START : Fetch budget and accounting data from warehouse', 0, 1) WITH NOWAIT
--BEGIN






--INSERT INTO  @forecast_table ([fk_tenant_id],[forecast_period],[fk_account_code],[account_name],[department_code],[department_name],
--[fk_function_code],[function_name],[fk_project_code],[project_name],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],
--[free_dim_1_name],[free_dim_2_name],[free_dim_3_name],[free_dim_4_name],[resource_id],[resource_name],[description],
--[period],[periodic_key],[allocation_pct],[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
--[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
--[service_id_4],[service_name_4],[service_id_5],[service_name_5],
--[fk_position_id],[position_name],[action_name],
--[level_1_id],[level_1_description],[level_2_id],[level_2_description],[level_3_id],[level_3_description],[level_4_id],[level_4_description],
--[forecast_amount],
--[new_forecast],[risk_amount],[bud_amt_year],[bud_amt_ytd],[bud_amt_period],[actual_amt_year],[actual_amt_ytd],[actual_amt_period],
--[tech_amt_budget],[tech_amt_running],[org_bud_amt_year],[actual_amt_last_year],[actual_amt_last_ytd],[budget_change_amount],
--[report_source])
--SELECT a.fk_tenant_id,a.forecast_period,a.fk_account_code,ac.description as account_name, a.fk_department_code, dp.department_name, 
--a.fk_function_code,fc.display_name as function_name, a.fk_project_code,ISNULL(pj.project_name, '') as project_name,
--a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
--ISNULL(f1.description, '') as free_dim_1_name,ISNULL(f2.description, '') as free_dim_2_name,
--ISNULL(f3.description, '') as free_dim_3_name,ISNULL(f4.description, '') as free_dim_4_name,
--'' as [resource_id],'' as [resource_name],'' as [description],
--[period],0 as [periodic_key],0 as [allocation_pct],
--[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
--[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
--[service_id_4],[service_name_4],[service_id_5],[service_name_5],
--0 as [fk_position_id],'' as [position_name],'' as [action_name],
--rl.level_1_id,rl.level_1_description,rl.level_2_id,rl.level_2_description,rl.level_3_id,rl.level_3_description,rl.level_4_id,rl.level_4_description,
--0 as [forecast_amount],
--0 as [new_forecast],0 as [risk_amount],[bud_amt_year],[bud_amt_ytd],[bud_amt_period],[actual_amt_year],[actual_amt_ytd],[actual_amt_period],
--[tech_amt_budget],[tech_amt_running],[org_bud_amt_year],0 as [actual_amt_last_year],[actual_amt_last_ytd],0 as [budget_change_amount],
--'W' AS [report_source]
--FROM [dbo].[tmr_data_warehouse_period] a
--JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.forecast_period = ch.forecast_period 
--JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id
----JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'
--JOIN tco_departments dp ON a.fk_department_code = dp.pk_department_code AND a.fk_tenant_id = dp.fk_tenant_id AND a.forecast_period/100 BETWEEN dp.year_from AND dp.year_to
--JOIN tco_functions fc ON a.fk_function_code = fc.pk_function_code AND a.fk_tenant_id = fc.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, fc.dateFrom) AND datepart(year, fc.dateTo)
----JOIN gco_tenants gt ON a.fk_tenant_id = gt.pk_id
--JOIN tmd_reporting_line rl ON rl.fk_account_code = a.fk_account_code AND rl.fk_tenant_id = a.fk_tenant_id AND rl.report = 'MNDRAPP'
----LEFT JOIN gmd_kostra_function kf ON kf.pk_kostra_function_code = fc.fk_kostra_function_code AND gt.tenant_type_id = kf.tenant_type
--LEFT JOIN tco_projects pj ON a.fk_project_code = pj.pk_project_code AND a.fk_tenant_id = pj.fk_tenant_id
--LEFT JOIN tco_free_dim_values f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1'
--LEFT JOIN tco_free_dim_values f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2'
--LEFT JOIN tco_free_dim_values f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3'
--LEFT JOIN tco_free_dim_values f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4'
--LEFT JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id
--LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version AND ov.active = 1
--LEFT JOIN tco_service_values sv ON a.fk_function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id


--END
--select @timestamp = sysdatetime();
--PRINT 'FINISH: Fetch budget and accounting data at ' + convert(nvarchar(19),@timestamp)



RAISERROR ('START : Fetch forecast from tbu_forecast_transactions', 0, 1) WITH NOWAIT
BEGIN


INSERT INTO  #TEMP_forecast_table ([fk_tenant_id],[forecast_period],[fk_account_code],[account_name],[department_code],[department_name],
[fk_function_code],[function_name],[fk_project_code],[project_name],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],
[free_dim_1_name],[free_dim_2_name],[free_dim_3_name],[free_dim_4_name],[resource_id],[resource_name],[description],
[period],[periodic_key],[allocation_pct],[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
[service_id_4],[service_name_4],[service_id_5],[service_name_5],
[fk_position_id],[position_name],[action_name],
[level_1_id],[level_1_description],[level_2_id],[level_2_description],[level_3_id],[level_3_description],[level_4_id],[level_4_description],
[forecast_amount],
[new_forecast],[risk_amount],[bud_amt_year],[bud_amt_ytd],[bud_amt_period],[actual_amt_year],[actual_amt_ytd],[actual_amt_period],
[tech_amt_budget],[tech_amt_running],[org_bud_amt_year],[actual_amt_last_year],[actual_amt_last_ytd],[budget_change_amount], 
[budget_type],[report_source],forecast_amt_incl_actions)
SELECT a.fk_tenant_id,a.forecast_period,a.fk_account_code,ac.description as account_name, a.department_code, 
ISNULL(dp.department_name,''), 
a.fk_function_code,fc.display_name as function_name, a.fk_project_code,ISNULL(pj.project_name, '') as project_name,
a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
ISNULL(f1.description, '') as free_dim_1_name,ISNULL(f2.description, '') as free_dim_2_name,
ISNULL(f3.description, '') as free_dim_3_name,ISNULL(f4.description, '') as free_dim_4_name,
a.resource_id,ISNULL (r.last_name + ', ' + r.first_name,ISNULL(em.res_name, '')) as resource_name,a.description,
a.period,ISNULL (pa.key_description,'') as periodic_key,a.allocation_pct, 
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5,oh.org_name_5,
sv.service_id_1,sv.service_name_1,sv.service_id_2,sv.service_name_2,sv.service_id_3,sv.service_name_3,sv.service_id_4,sv.service_name_4,sv.service_id_5,sv.service_name_5,
em.fk_position_id, po.position_name,'' as action_name,
rl.level_1_id,rl.level_1_description,rl.level_2_id,rl.level_2_description,rl.level_3_id,rl.level_3_description,rl.level_4_id,rl.level_4_description,
a.amount_year_1 as forecast_amount,
new_forecast = CASE WHEN a.budget_type = 10 THEN a.amount_year_1 ELSE 0 END,
risk_amount = CASE WHEN a.budget_type in (11,12,13,14,15) THEN a.amount_year_1 ELSE 0 END,
0 as [bud_amt_year],0 as [bud_amt_ytd],0 as [bud_amt_period],0 as [actual_amt_year],
[actual_amt_ytd] = CASE WHEN a.budget_type = 20 THEN a.amount_year_1 ELSE 0 END,
0 as [actual_amt_period],
[tech_amt_budget] = CASE WHEN a.budget_type = 20 THEN a.amount_year_1 ELSE 0 END,
[tech_amt_running] = CASE WHEN a.budget_type = 20 THEN a.amount_year_1 ELSE 0 END,
0 as [org_bud_amt_year],
0 as [actual_amt_last_year],
0 as [actual_amt_last_ytd],
0 as [budget_change_amount], budget_type, 'F' as [report_source],
forecast_amt_incl_actions = a.amount_year_1
FROM tbu_forecast_transactions a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.forecast_period = ch.forecast_period 
JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
--JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'
LEFT JOIN tco_departments dp ON a.department_code = dp.pk_department_code AND a.fk_tenant_id = dp.fk_tenant_id AND a.budget_year BETWEEN dp.year_from AND dp.year_to
JOIN tco_functions fc ON a.fk_function_code = fc.pk_function_code AND a.fk_tenant_id = fc.pk_tenant_id AND a.budget_year BETWEEN datepart(year, fc.dateFrom) AND datepart(year, fc.dateTo)
JOIN gco_tenants gt ON a.fk_tenant_id = gt.pk_id
JOIN tmd_reporting_line rl ON rl.fk_account_code = a.fk_account_code AND rl.fk_tenant_id = a.fk_tenant_id AND rl.report = 'MNDRAPP'
--LEFT JOIN gmd_kostra_function kf ON kf.pk_kostra_function_code = fc.fk_kostra_function_code AND gt.tenant_type_id = kf.tenant_type
LEFT JOIN tco_projects pj ON a.fk_project_code = pj.pk_project_code AND a.fk_tenant_id = pj.fk_tenant_id
LEFT JOIN tco_free_dim_values f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1'
LEFT JOIN tco_free_dim_values f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2'
LEFT JOIN tco_free_dim_values f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3'
LEFT JOIN tco_free_dim_values f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4'
LEFT JOIN tco_resources r ON a.fk_tenant_id = r.fk_tenant_id AND a.resource_id = r.pk_res_id
LEFT JOIN tbu_employments em ON a.fk_tenant_id = em.fk_tenant_id AND a.fk_employment_id = em.pk_employment_id AND a.budget_year = em.budget_year
LEFT JOIN tco_positions po ON em.fk_tenant_id = po.fk_tenant_id AND em.fk_position_id = po.position_id AND em.budget_year BETWEEN po.year_from AND po.year_to
LEFT JOIN (SELECT DISTINCT key_id, key_description FROM gco_periodic_key) pa ON a.fk_key_id = pa.key_id
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and a.forecast_period between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT JOIN tco_service_values sv ON a.fk_function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id
--GROUP BY a.fk_tenant_id,a.forecast_period,a.fk_account_code,ac.description, a.department_code, dp.department_name, 
--a.fk_function_code,fc.display_name, a.fk_project_code,pj.project_name,
--a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
--f1.description,f2.description,f3.description,f4.description,
--a.resource_id,r.last_name,r.first_name,em.res_name,a.description,
--a.period,pa.key_description,a.allocation_pct, 
--oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5,oh.org_name_5,
--sv.service_id_1,sv.service_name_1,sv.service_id_2,sv.service_name_2,sv.service_id_3,sv.service_name_3,sv.service_id_4,sv.service_name_4,sv.service_id_5,sv.service_name_5,
--em.fk_position_id, po.position_name,
--rl.level_1_id,rl.level_1_description,rl.level_2_id,rl.level_2_description,rl.level_3_id,rl.level_3_description,rl.level_4_id,rl.level_4_description,
--a.budget_type

END


SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name like 'FORCASTREP_INCL_DEV_ACTION' AND param_value = 'TRUE' AND fk_tenant_id = @tenant_id

RAISERROR ('START : Fetch deviation actions from tmr_deviation_report', 0, 1) WITH NOWAIT
BEGIN


INSERT INTO  #TEMP_forecast_table ([fk_tenant_id],[forecast_period],[fk_account_code],[account_name],[department_code],[department_name],
[fk_function_code],[function_name],[fk_project_code],[project_name],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],
[free_dim_1_name],[free_dim_2_name],[free_dim_3_name],[free_dim_4_name],[resource_id],[resource_name],[description],
[period],[periodic_key],[allocation_pct],[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
[service_id_4],[service_name_4],[service_id_5],[service_name_5],
[fk_position_id],[position_name],[action_name],
[level_1_id],[level_1_description],[level_2_id],[level_2_description],[level_3_id],[level_3_description],[level_4_id],[level_4_description],
[forecast_amount],
[new_forecast],[risk_amount],[bud_amt_year],[bud_amt_ytd],[bud_amt_period],[actual_amt_year],[actual_amt_ytd],[actual_amt_period],
[tech_amt_budget],[tech_amt_running],[org_bud_amt_year],[actual_amt_last_year],[actual_amt_last_ytd],[budget_change_amount], 
[budget_type],[report_source], forecast_amt_incl_actions)
SELECT a.fk_tenant_id,a.forecast_period,a.fk_account_code,ac.description as account_name, a.fk_department_code, dp.department_name, 
a.fk_function_code,fc.display_name as function_name, '' AS fk_project_code,
'' as project_name,
'' AS free_dim_1,'' AS free_dim_2,'' AS free_dim_3,'' AS free_dim_4,
'' as free_dim_1_name,'' as free_dim_2_name,
''  as free_dim_3_name,''  as free_dim_4_name,
'' AS resource_id,'' as resource_name,'' as description,
a.forecast_period,'' as periodic_key, 0 AS allocation_pct, 
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5,oh.org_name_5,
sv.service_id_1,sv.service_name_1,sv.service_id_2,sv.service_name_2,sv.service_id_3,sv.service_name_3,sv.service_id_4,
sv.service_name_4,sv.service_id_5,sv.service_name_5,
'' as fk_position_id, '' as position_name,a.deviation_action_name as action_name,
rl.level_1_id,rl.level_1_description,rl.level_2_id,rl.level_2_description,rl.level_3_id,rl.level_3_description,
rl.level_4_id,rl.level_4_description,
0 as forecast_amount,
new_forecast = 0,
risk_amount = 0,
0 as [bud_amt_year],0 as [bud_amt_ytd],0 as [bud_amt_period],0 as [actual_amt_year],
[actual_amt_ytd] = 0,
0 as [actual_amt_period],
[tech_amt_budget] = 0,
[tech_amt_running] = 0,
0 as [org_bud_amt_year],
0 as [actual_amt_last_year],
0 as [actual_amt_last_ytd],
0 as [budget_change_amount], '' AS budget_type, 'D' as [report_source],
forecast_amt_incl_actions = a.remaining_amount
FROM tmr_deviation_report a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.forecast_period = ch.forecast_period 
JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN tco_departments dp ON a.fk_department_code = dp.pk_department_code AND a.fk_tenant_id = dp.fk_tenant_id AND a.forecast_period/100 BETWEEN dp.year_from AND dp.year_to
JOIN tco_functions fc ON a.fk_function_code = fc.pk_function_code AND a.fk_tenant_id = fc.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, fc.dateFrom) AND datepart(year, fc.dateTo)
JOIN gco_tenants gt ON a.fk_tenant_id = gt.pk_id
JOIN tmd_reporting_line rl ON rl.fk_account_code = a.fk_account_code AND rl.fk_tenant_id = a.fk_tenant_id AND rl.report = 'MNDRAPP'
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and a.forecast_period between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT JOIN tco_service_values sv ON a.fk_function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id



END

select @timestamp = sysdatetime();
PRINT 'FINISH: fetching forecast at ' + convert(nvarchar(19),@timestamp)


BEGIN 
RAISERROR ('START: Finding org level', 0, 1) WITH NOWAIT


UPDATE #TEMP_forecast_table SET risk_org_level = ol.level_name
FROM #TEMP_forecast_table f, tco_org_level ol, tco_org_version ov
WHERE f.fk_tenant_id = ol.fk_tenant_id
AND ol.fk_tenant_id = ov.fk_tenant_id
AND ov.active = 1
AND ol.org_level = 2
AND f.budget_type = 11;

UPDATE #TEMP_forecast_table SET risk_org_level = ol.level_name
FROM #TEMP_forecast_table f, tco_org_level ol, tco_org_version ov
WHERE f.fk_tenant_id = ol.fk_tenant_id
AND ol.fk_tenant_id = ov.fk_tenant_id
AND ov.active = 1
AND ol.org_level = 3
AND f.budget_type = 12;

UPDATE #TEMP_forecast_table SET risk_org_level = ol.level_name
FROM #TEMP_forecast_table f, tco_org_level ol, tco_org_version ov
WHERE f.fk_tenant_id = ol.fk_tenant_id
AND ol.fk_tenant_id = ov.fk_tenant_id
AND ov.active = 1
AND ol.org_level = 4
AND f.budget_type = 13;

UPDATE #TEMP_forecast_table SET risk_org_level = ol.level_name
FROM #TEMP_forecast_table f, tco_org_level ol, tco_org_version ov
WHERE f.fk_tenant_id = ol.fk_tenant_id
AND ol.fk_tenant_id = ov.fk_tenant_id
AND ov.active = 1
AND ol.org_level = 5
AND f.budget_type = 14;


select @timestamp = sysdatetime();
PRINT 'FINISH: Finding org level at ' + convert(nvarchar(19),@timestamp)

END


RAISERROR ('START : Updating forecast table from temptable', 0, 1) WITH NOWAIT
BEGIN

INSERT INTO  twh_forecast_report ([fk_tenant_id],[forecast_period],[fk_account_code],[account_name],[department_code],[department_name],
[fk_function_code],[function_name],[fk_project_code],[project_name],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],
[free_dim_1_name],[free_dim_2_name],[free_dim_3_name],[free_dim_4_name],[resource_id],[resource_name],[description],
[period],[periodic_key],[allocation_pct],[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
[service_id_4],[service_name_4],[service_id_5],[service_name_5],
[fk_position_id],[position_name],[action_name],
[level_1_id],[level_1_description],[level_2_id],[level_2_description],[level_3_id],[level_3_description],[level_4_id],[level_4_description],
[forecast_amount],
[new_forecast],[risk_amount],[bud_amt_year],[bud_amt_ytd],[bud_amt_period],[actual_amt_year],[actual_amt_ytd],[actual_amt_period],
[tech_amt_budget],[tech_amt_running],[org_bud_amt_year],[actual_amt_last_year],[actual_amt_last_ytd],[budget_change_amount], 
deviation_amount,[risk_org_level], [report_source],
forecast_amt_incl_actions,dev_amt_incl_actions)
SELECT 
[fk_tenant_id],[forecast_period],[fk_account_code],[account_name],[department_code],[department_name],
[fk_function_code],[function_name],[fk_project_code],[project_name],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],
[free_dim_1_name],[free_dim_2_name],[free_dim_3_name],[free_dim_4_name],[resource_id],[resource_name],[description],
[period],[periodic_key],[allocation_pct],[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
[service_id_4],[service_name_4],[service_id_5],[service_name_5],
[fk_position_id],[position_name],[action_name],
[level_1_id],[level_1_description],[level_2_id],[level_2_description],[level_3_id],[level_3_description],[level_4_id],[level_4_description],
sum([forecast_amount]),
sum([new_forecast]),sum([risk_amount]),sum([bud_amt_year]),sum([bud_amt_ytd]),sum([bud_amt_period]),sum([actual_amt_year]),sum([actual_amt_ytd]),sum([actual_amt_period]),
sum([tech_amt_budget]),sum([tech_amt_running]),sum([org_bud_amt_year]),sum([actual_amt_last_year]),sum([actual_amt_last_ytd]),sum([budget_change_amount]),
sum([bud_amt_ytd])-sum([actual_amt_ytd]),
[risk_org_level], [report_source],
SUM(forecast_amt_incl_actions), SUM(forecast_amt_incl_actions) * -1
FROM #TEMP_forecast_table
GROUP BY 
[fk_tenant_id],[forecast_period],[fk_account_code],[account_name],[department_code],[department_name],
[fk_function_code],[function_name],[fk_project_code],[project_name],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],
[free_dim_1_name],[free_dim_2_name],[free_dim_3_name],[free_dim_4_name],[resource_id],[resource_name],[description],
[period],[periodic_key],[allocation_pct],[org_id_1],[org_name_1],[org_id_2],[org_name_2],[org_id_3],[org_name_3],[org_id_4],
[org_name_4],[org_id_5],[org_name_5],[service_id_1],[service_name_1],[service_id_2],[service_name_2],[service_id_3],[service_name_3],
[service_id_4],[service_name_4],[service_id_5],[service_name_5],
[fk_position_id],[position_name],[action_name],
[level_1_id],[level_1_description],[level_2_id],[level_2_description],[level_3_id],[level_3_description],[level_4_id],[level_4_description],
[risk_org_level], [report_source]
;

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Updating forecast table from temptable at ' + convert(nvarchar(19),@timestamp)


DROP TABLE #TEMP_forecast_table;



BEGIN
UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name IN ('TWHFORECASTFULL','TWHFORECASTUPDATE');
END 

PRINT 'Job ended ok'



GO


