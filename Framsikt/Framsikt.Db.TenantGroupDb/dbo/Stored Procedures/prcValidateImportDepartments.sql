CREATE OR ALTER PROCEDURE [dbo].[Prcvalidateimportdepartments] @tenant_id    INT,
                                                      @user_id      INT,
                                                      @dimension_id INT,
                                                      @job_id       BIGINT
AS
    IF( @job_id = -1 )
      BEGIN
          UPDATE tbu_stage_dimensions_import
          SET    department_code_error = 0,
                 department_name_error = 0,
                 department_status_error = 0,
                 year_from_error = 0,
                 year_to_error = 0,
                 error_count = 0
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id;
      END
    ELSE
      BEGIN
          UPDATE tbu_stage_dimensions_import
          SET    department_code_error = 0,
                 department_name_error = 0,
                 department_status_error = 0,
                 year_from_error = 0,
                 year_to_error = 0,
                 error_count = 0
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id;
      END

    IF( @job_id = -1 )
      BEGIN
          -- Validate Department Code
          UPDATE tbu_stage_dimensions_import
          SET    department_code_error = department_code_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( department_code IS NULL
                        OR department_code = '' );

          --Valaidate Departments Name
          UPDATE tbu_stage_dimensions_import
          SET    department_name_error = department_name_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( department_name IS NULL
                        OR department_name = '' );

          --Validate Departments Status
          UPDATE tbu_stage_dimensions_import
          SET    department_status_error = department_status_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( department_status IS NULL
                        OR department_status > 1
                        OR department_status < 0 );

          --Validate Department Year from NULL CHECK       
          UPDATE tbu_stage_dimensions_import
          SET    year_from_error = year_from_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( year_from IS NULL
                        OR year_from = ''
                        OR year_from = 0 );

          -- Validate Department Year to NULL CHECK  
          UPDATE tbu_stage_dimensions_import
          SET    year_to_error = year_to_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( year_to IS NULL
                        OR year_to = ''
                        OR year_to = 0 );

          --Validate Length of the Year
          UPDATE tbu_stage_dimensions_import
          SET    year_from_error = year_from_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_from) < 4
                        OR ( year_from > year_to ) );

          -- Validate length of the year
          UPDATE tbu_stage_dimensions_import
          SET    year_to_error = year_to_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_to) < 4
                        OR ( year_from > year_to ) );
      END
    ELSE
      BEGIN
          -- Validate Department Code
          UPDATE tbu_stage_dimensions_import
          SET    department_code_error = department_code_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( department_code IS NULL
                        OR department_code = '' );

          --Valaidate Departments Name
          UPDATE tbu_stage_dimensions_import
          SET    department_name_error = department_name_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( department_name IS NULL
                        OR department_name = '' );

          --Validate Departments Status
          UPDATE tbu_stage_dimensions_import
          SET    department_status_error = department_status_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( department_status IS NULL
                        OR department_status > 1
                        OR department_status < 0 );

          --Validate Department Year from NULL CHECK       
          UPDATE tbu_stage_dimensions_import
          SET    year_from_error = year_from_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( year_from IS NULL
                        OR year_from = ''
                        OR year_from = 0 );

          -- Validate Department Year to NULL CHECK  
          UPDATE tbu_stage_dimensions_import
          SET    year_to_error = year_to_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( year_to IS NULL
                        OR year_to = ''
                        OR year_to = 0 );

          --Validate Length of the Year
          UPDATE tbu_stage_dimensions_import
          SET    year_from_error = year_from_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_from) < 4
                        OR ( year_from > year_to ) );

          -- Validate length of the year
          UPDATE tbu_stage_dimensions_import
          SET    year_to_error = year_to_error + 1,
                 error_count = error_count + 1
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_to) < 4
                        OR ( year_from > year_to ) );
      END

    -- If the record exsits in tco_departments
    --UPDATE stg
    --SET    stg.department_code_error = stg.department_code_error + 1,
    --       stg.error_count = stg.error_count + 1
    --FROM   tbu_stage_dimensions_import stg
    --       JOIN tbu_stage_dimensions_import dep
    --         ON stg.fk_tenant_id = dep.fk_tenant_id
    --            AND stg.department_code = dep.department_code
    --            AND stg.year_from = dep.year_from
    --            AND stg.year_to = dep.year_to
    --            AND stg.department_status = dep.department_status
    --WHERE  stg.fk_tenant_id = @tenant_id
    --       AND stg.job_id = @job_id
    --       AND stg.dimension_type = @dimension_id;

    UPDATE stg
    SET    stg.department_code_error = stg.department_code_error + 1,
           stg.error_count = stg.error_count + 1
           FROM tbu_stage_dimensions_import stg
           WHERE fk_tenant_id = @tenant_id 
           AND dimension_type = @dimension_id 
           AND [user_id] = @user_id
           AND stg.department_code in 
           (select department_code 
           from tbu_stage_dimensions_import
           where fk_tenant_id = @tenant_id AND dimension_type = @dimension_id AND [user_id] = @user_id
           group by department_code, department_name, year_from, year_to, department_status
           having count(department_code)>1) 

    RETURN 0 