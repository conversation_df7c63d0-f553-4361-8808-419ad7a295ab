
CREATE OR ALTER PROCEDURE [dbo].[prcSyncProjWarehouse]
             @tenant_id int,
             @budget_year int,
    @user_id int
AS


--DECLARE @tenant_id INT = 7684,
--@budget_year INT = 2025,
--@user_id INT = 1132

DROP TABLE IF EXISTS #projbudgetdata
DROP TABLE IF EXISTS #projbudgetdata_2
DROP TABLE IF EXISTS #projbudgetdata_3
DROP TABLE IF EXISTS #projbudgetdata_4

DECLARE @subtenants TABLE 
(sub_tenant_id INT)


INSERT INTO @subtenants (sub_tenant_id)
SELECT sub_tenant_id FROM tco_sync_company_setup WHERE fk_tenant_id = @tenant_id AND sync_flag != 0 GROUP BY sub_tenant_id


DECLARE @changeid TABLE 
(fk_tenant_id INT,
fk_budget_phase_id       UNIQUEIDENTIFIER,
fk_change_id INT
)


DECLARE @phase_id UNIQUEIDENTIFIER
DECLARE @sortOrder INT

DECLARE db_cursor CURSOR FOR 

SELECT pk_budget_phase_id FROM tco_budget_phase ph
WHERE ph.fk_tenant_id = @tenant_id AND org_budget_flag = 1 ORDER BY sort_order

OPEN db_cursor  
FETCH NEXT FROM db_cursor INTO @phase_id

WHILE @@FETCH_STATUS = 0  
BEGIN  
     SET @sortOrder = 
                 (SELECT sort_order FROM tco_budget_phase ph
                WHERE ph.fk_tenant_id = @tenant_id AND org_budget_flag = 1 AND pk_budget_phase_id = @phase_id)

INSERT INTO @changeid (fk_tenant_id, fk_budget_phase_id, fk_change_id)        
SELECT ph.fk_tenant_id, @phase_id, ch.pk_change_id FROM tco_budget_phase ph
JOIN tfp_budget_changes ch ON ph.fk_tenant_id = ch.fk_tenant_id AND ch.budget_year = @budget_year AND ch.fk_budget_phase_id = ph.pk_budget_phase_id
AND ph.fk_tenant_id = @tenant_id
AND ch.org_budget_flag = 1
AND ph.sort_order <= @sortOrder
GROUP BY ph.fk_tenant_id,ph.pk_budget_phase_id, pk_change_id


      FETCH NEXT FROM db_cursor INTO @phase_id 
END 

CLOSE db_cursor  
DEALLOCATE db_cursor 


DECLARE @first_change_ID INT

SET @first_change_id = (SELECT top 1 ch.pk_change_id FROM tco_budget_phase ph
JOIN tfp_budget_changes ch ON ph.fk_tenant_id = ch.fk_tenant_id AND ch.budget_year = @budget_year AND ch.fk_budget_phase_id = ph.pk_budget_phase_id
AND ph.fk_tenant_id = @tenant_id
AND ch.org_budget_flag = 1
order by ph.sort_order, ch.pk_change_id)


CREATE TABLE #projbudgetdata
(
    [pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_change_id] INT NOT NULL,
    [fk_budget_phase_id] UNIQUEIDENTIFIER NOT NULL,
    [sub_tenant_id] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL,
    [fk_department_code] NVARCHAR(25) NOT NULL,
    [fk_project_code] NVARCHAR(25) NOT NULL,
    [inv_status] INT NOT NULL,
    [adjustment_code_status] NVARCHAR(25) NOT NULL,
    [type] NVARCHAR(25) NOT NULL,
    [org_bud_prev_year_main] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_main] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_main_expence] DECIMAL(18, 2) NOT NULL,
    [fp_year_1_amt_main_income] DECIMAL(18, 2) NOT NULL,
    [fp_year_1_amt_sub] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_sub_expence] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_sub_income] DECIMAL(18, 2) NOT NULL, 
)

CREATE TABLE #projbudgetdata_2
(
    [pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_change_id] INT NOT NULL,
    [fk_budget_phase_id] UNIQUEIDENTIFIER NOT NULL,
    [sub_tenant_id] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL,
    [fk_department_code] NVARCHAR(25) NOT NULL,
    [fk_project_code] NVARCHAR(25) NOT NULL,
    [inv_status] INT NOT NULL,
    [adjustment_code_status] NVARCHAR(25) NOT NULL,
    [type] NVARCHAR(25) NOT NULL,
    [org_bud_prev_year_main] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_main] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_main_expence] DECIMAL(18, 2) NOT NULL,
    [fp_year_1_amt_main_income] DECIMAL(18, 2) NOT NULL,
    [fp_year_1_amt_sub] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_sub_expence] DECIMAL(18, 2) NOT NULL, 
    [fp_year_1_amt_sub_income] DECIMAL(18, 2) NOT NULL, 
)



INSERT INTO #projbudgetdata (
fk_tenant_id
,budget_year
,fk_change_id
,fk_budget_phase_id
,sub_tenant_id
,fk_account_code
,fk_department_code
,fk_project_code
,inv_status
,adjustment_code_status
,type
,org_bud_prev_year_main
,fp_year_1_amt_main
,fp_year_1_amt_main_expence
,fp_year_1_amt_main_income
,fp_year_1_amt_sub
,fp_year_1_amt_sub_expence
,fp_year_1_amt_sub_income )
SELECT 
@tenant_id as fk_tenant_id
,@budget_year as budget_year
,a.fk_change_id
,b.pk_budget_phase_id AS fk_budget_phase_id
,s.sub_tenant_id
,a.fk_account_code
,a.fk_department_code
,a.fk_project_code
,inv_status = ISNULL(mp.inv_status,'-1')
,adjustment_code_status = CASE              WHEN UAD.status = 1 THEN 'Godkjent'
                                            WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
                                            ELSE 'Åpen'
                                            END
,type =                   CASE				WHEN RL.line_item_id = '1010' THEN 'Investering' 
                                            WHEN RL.line_item_id = '1020' THEN 'Investering'
                                            WHEN RL.line_item_id = '1030' THEN 'Investering'
                                            WHEN RL.line_item_id = '1040' THEN 'Investering'
                                            ELSE 'Finansiering' END
,0 as org_bud_prev_year_main
,sum(amount) as fp_year_1_amt_main
,fp_year_1_amt_main_expence = CASE WHEN rl.line_item_id IN ('1010','1020', '1030', '1040') then SUM(AMOUNT) ELSE 0 END
,fp_year_1_amt_main_income = CASE WHEN rl.line_item_id NOT IN ('1010','1020', '1030', '1040') then SUM(AMOUNT) ELSE 0 END
,0 as fp_year_1_amt_sub
,0 as fp_year_1_amt_sub_expence
,0 as fp_year_1_amt_sub_income
FROM tfp_proj_transactions a
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                                               select fk_tenant_id, pk_change_id, description, 1 as org_budget_flag,budget_year, fk_budget_phase_id from tfp_budget_changes
                                               where fk_tenant_id = @tenant_id
                                               and budget_year < @budget_year
                                               UNION
                                               select fk_tenant_id, pk_change_id, description, org_budget_flag,budget_year, fk_budget_phase_id from tfp_budget_changes
                                               where fk_tenant_id = @tenant_id
                                               and budget_year = @budget_year
                                               and org_budget_flag = 1
                               )  BC ON a.fk_tenant_id = BC.fk_tenant_id and a.fk_change_id = BC.pk_change_id
LEFT JOIN tco_budget_phase b ON BC.fk_tenant_id = b.fk_tenant_id AND BC.fk_budget_phase_id = b.pk_budget_phase_id
LEFT JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and a.year*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
LEFT JOIN tco_projects pj ON a.fk_tenant_id = pj.fk_tenant_id AND a.fk_project_code = pj.pk_project_code AND a.year BETWEEN DATEPART(YEAR,pj.date_from) AND DATEPART(YEAR,pj.date_to) 
LEFT JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id and pj.fk_main_project_code = mp.pk_main_project_code AND a.year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)
JOIN tco_user_adjustment_codes UAD ON a.fk_tenant_id = UAD.fk_tenant_id and a.fk_user_adjustment_code = UAD.pk_adj_code
LEFT JOIN tco_accounts e ON a.fk_account_code = e.pk_account_code AND a.fk_tenant_id = e.pk_tenant_id AND a.year BETWEEN  datepart(year,e.dateFrom) and datepart(year,e.dateTo)
JOIN gmd_reporting_line rl ON e.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
WHERE a.fk_tenant_id = @tenant_id 
AND a.year = @budget_year
GROUP BY 
a.fk_tenant_id
,a.year
,a.fk_change_id
,b.pk_budget_phase_id
,s.sub_tenant_id
,a.fk_account_code
,a.fk_department_code
,a.fk_project_code
,mp.inv_status
,UAD.status
,UAD.include_in_calculation
,BC.budget_year
,rl.line_item_id



update #projbudgetdata set fk_change_id = @first_change_ID
FROM #projbudgetdata a
JOIN tfp_budget_changes b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_change_id = b.pk_change_id AND b.budget_year < @budget_year
WHERE a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year



INSERT INTO #projbudgetdata_2 (
fk_tenant_id
,budget_year
,fk_change_id
,fk_budget_phase_id
,sub_tenant_id
,fk_account_code
,fk_department_code
,fk_project_code
,inv_status
,adjustment_code_status
,type
,org_bud_prev_year_main
,fp_year_1_amt_main
,fp_year_1_amt_main_expence
,fp_year_1_amt_main_income
,fp_year_1_amt_sub
,fp_year_1_amt_sub_expence
,fp_year_1_amt_sub_income )
SELECT 
@tenant_id as fk_tenant_id
,@budget_year as budget_year
,a.fk_change_id
,b.pk_budget_phase_id AS fk_budget_phase_id
,s.sub_tenant_id
,a.fk_account_code
,a.fk_department_code
,a.fk_project_code
,inv_status = ISNULL(mp.inv_status,'-1')
,adjustment_code_status = CASE              WHEN UAD.status = 1 THEN 'Godkjent'
                                            WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
                                            ELSE 'Åpen'
                                            END
,type =                   CASE				WHEN RL.line_item_id = '1010' THEN 'Investering' 
                                            WHEN RL.line_item_id = '1020' THEN 'Investering'
                                            WHEN RL.line_item_id = '1030' THEN 'Investering'
                                            WHEN RL.line_item_id = '1040' THEN 'Investering'
                                            ELSE 'Finansiering' END
,sum(amount) as org_bud_prev_year_main
,0 as fp_year_1_amt_main
,0 as fp_year_1_amt_main_expence
,0 as fp_year_1_amt_main_income
,0 as fp_year_1_amt_sub
,0 as fp_year_1_amt_sub_expence
,0 as fp_year_1_amt_sub_income
FROM tfp_proj_transactions a
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                                               select fk_tenant_id, pk_change_id, description, 1 as org_budget_flag,budget_year, fk_budget_phase_id from tfp_budget_changes
                                               where fk_tenant_id = @tenant_id
                                               and budget_year < @budget_year-1
                                               UNION
                                               select fk_tenant_id, pk_change_id, description, org_budget_flag,budget_year, fk_budget_phase_id from tfp_budget_changes
                                               where fk_tenant_id = @tenant_id
                                               and budget_year = @budget_year-1
                                               and org_budget_flag = 1
                               )  BC ON a.fk_tenant_id = BC.fk_tenant_id and a.fk_change_id = BC.pk_change_id
LEFT JOIN tco_budget_phase b ON BC.fk_tenant_id = b.fk_tenant_id AND BC.fk_budget_phase_id = b.pk_budget_phase_id
LEFT JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and a.year*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
LEFT JOIN tco_projects pj ON a.fk_tenant_id = pj.fk_tenant_id AND a.fk_project_code = pj.pk_project_code AND a.year BETWEEN DATEPART(YEAR,pj.date_from) AND DATEPART(YEAR,pj.date_to) 
LEFT JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id and pj.fk_main_project_code = mp.pk_main_project_code AND a.year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)
JOIN tco_user_adjustment_codes UAD ON a.fk_tenant_id = UAD.fk_tenant_id and a.fk_user_adjustment_code = UAD.pk_adj_code
LEFT JOIN tco_accounts e ON a.fk_account_code = e.pk_account_code AND a.fk_tenant_id = e.pk_tenant_id AND a.year BETWEEN  datepart(year,e.dateFrom) and datepart(year,e.dateTo)
JOIN gmd_reporting_line rl ON e.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
WHERE a.fk_tenant_id = @tenant_id 
AND a.year = @budget_year - 1
GROUP BY 
a.fk_tenant_id
,a.year
,a.fk_change_id
,b.pk_budget_phase_id
,s.sub_tenant_id
,a.fk_account_code
,a.fk_department_code
,a.fk_project_code
,mp.inv_status
,UAD.status
,UAD.include_in_calculation
,BC.budget_year
,rl.line_item_id





INSERT INTO #projbudgetdata_2 (
fk_tenant_id
,budget_year
,fk_change_id
,fk_budget_phase_id
,sub_tenant_id
,fk_account_code
,fk_department_code
,fk_project_code
,inv_status
,adjustment_code_status
,type
,org_bud_prev_year_main
,fp_year_1_amt_main
,fp_year_1_amt_main_expence
,fp_year_1_amt_main_income
,fp_year_1_amt_sub
,fp_year_1_amt_sub_expence
,fp_year_1_amt_sub_income )
SELECT 
@tenant_id as fk_tenant_id
,@budget_year as budget_year
,a.fk_change_id
,b.pk_budget_phase_id AS fk_budget_phase_id
,a.fk_tenant_id
,a.fk_account_code
,a.fk_department_code
,a.fk_project_code
,inv_status = ISNULL(mp.inv_status,'-1')
,adjustment_code_status = CASE           WHEN UAD.status = 1 THEN 'Godkjent'
                                         WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
                                         ELSE 'Åpen'
                                         END
,type =                                  CASE WHEN RL.line_item_id = '1010' THEN 'Investering' 
                                         WHEN RL.line_item_id = '1020' THEN 'Investering'
                                         WHEN RL.line_item_id = '1030' THEN 'Investering'
                                         WHEN RL.line_item_id = '1040' THEN 'Investering'
                                         ELSE 'Finansiering' END
,0 as org_bud_prev_year_main
,0 as fp_year_1_amt_main
,0 as fp_year_1_amt_main_expence
,0 as fp_year_1_amt_main_income
,sum(amount) as fp_year_1_amt_sub
,fp_year_1_amt_sub_expence = CASE WHEN rl.line_item_id IN ('1010','1020', '1030', '1040') then SUM(AMOUNT) ELSE 0 END
,fp_year_1_amt_sub_income = CASE WHEN rl.line_item_id NOT IN ('1010','1020', '1030', '1040') then SUM(AMOUNT) ELSE 0 END
FROM tfp_proj_transactions a
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
                                               select ch.fk_tenant_id, ch.pk_change_id, ch.description, 1 as org_budget_flag,ch.budget_year, ch.fk_budget_phase_id 
                                               from tfp_budget_changes ch
                                               JOIN @subtenants s ON s.sub_tenant_id = ch.fk_tenant_id
                                               WHERE budget_year < @budget_year
                                               UNION
                                               select ch.fk_tenant_id, ch.pk_change_id, ch.description, ch.org_budget_flag,ch.budget_year, ch.fk_budget_phase_id
                                               from tfp_budget_changes ch
                                               JOIN @subtenants s ON s.sub_tenant_id = ch.fk_tenant_id
                                               where budget_year = @budget_year
                                               and org_budget_flag = 1
                               )  BC ON a.fk_tenant_id = BC.fk_tenant_id and a.fk_change_id = BC.pk_change_id
LEFT JOIN tco_budget_phase b ON BC.fk_tenant_id = b.fk_tenant_id AND BC.fk_budget_phase_id = b.pk_budget_phase_id
LEFT JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and a.year*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN @subtenants s ON a.fk_tenant_id = s.sub_tenant_id
LEFT JOIN tco_projects pj ON a.fk_tenant_id = pj.fk_tenant_id AND a.fk_project_code = pj.pk_project_code AND a.year BETWEEN DATEPART(YEAR,pj.date_from) AND DATEPART(YEAR,pj.date_to) 
LEFT JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id and pj.fk_main_project_code = mp.pk_main_project_code AND a.year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)
JOIN tco_user_adjustment_codes UAD ON a.fk_tenant_id = UAD.fk_tenant_id and a.fk_user_adjustment_code = UAD.pk_adj_code
LEFT JOIN tco_accounts e ON a.fk_account_code = e.pk_account_code AND a.fk_tenant_id = e.pk_tenant_id AND a.year BETWEEN  datepart(year,e.dateFrom) and datepart(year,e.dateTo)
JOIN gmd_reporting_line rl ON e.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
WHERE a.fk_tenant_id = sub_tenant_id 
AND a.year = @budget_year
GROUP BY 
a.fk_tenant_id
,a.year
,a.fk_change_id
,b.pk_budget_phase_id
,s.sub_tenant_id
,a.fk_account_code
,a.fk_department_code
,a.fk_project_code
,mp.inv_status
,UAD.status
,UAD.include_in_calculation
,BC.budget_year
,rl.line_item_id






SELECT
p.fk_tenant_id
,budget_year
,c.fk_budget_phase_id
,sub_tenant_id 
,fp_year_1_amt_main = sum(fp_year_1_amt_main)
,fp_year_1_amt_main_expence = sum(fp_year_1_amt_main_expence)
,fp_year_1_amt_main_income = sum(fp_year_1_amt_main_income)
,GETDATE() AS updated
,@user_id AS updated_by
INTO #projbudgetdata_3
FROM #projbudgetdata p
JOIN @changeid c ON p.fk_change_id = c.fk_change_id AND p.fk_tenant_id = c.fk_tenant_id
WHERE p.fk_tenant_id = @tenant_id and p.budget_year = @budget_year
AND p.inv_status not in (3,4,5,6)
AND p.adjustment_code_status = 'Godkjent'
GROUP BY
p.fk_tenant_id
,p.budget_year
,c.fk_budget_phase_id
,p.sub_tenant_id


SELECT
p.fk_tenant_id
,budget_year
,sub_tenant_id
,org_bud_prev_year_main = sum(org_bud_prev_year_main)  
,fp_year_1_amt_sub = sum(fp_year_1_amt_sub)
,fp_year_1_amt_sub_expence = sum(fp_year_1_amt_sub_expence)
,fp_year_1_amt_sub_income = sum(fp_year_1_amt_sub_income)
,GETDATE() AS updated
,@user_id AS updated_by
INTO #projbudgetdata_4
FROM #projbudgetdata_2 p
WHERE p.fk_tenant_id = @tenant_id and p.budget_year = @budget_year
AND p.inv_status not in (3,4,5,6)
AND p.adjustment_code_status = 'Godkjent'
GROUP BY
p.fk_tenant_id
,p.budget_year
,p.sub_tenant_id



DELETE FROM [tfp_proj_sync_datawarehouse] WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year

INSERT INTO [tfp_proj_sync_datawarehouse]
 ([fk_tenant_id]
 ,[budget_year]
 ,[sub_tenant_id]
 ,[fk_budget_phase_id]
 ,[org_bud_prev_year_main]
 ,[fp_year_1_amt_main]
 ,[fp_year_1_amt_main_expence]
 ,[fp_year_1_amt_main_income]
 ,[fp_year_1_amt_sub] 
 ,[fp_year_1_amt_sub_expence]
 ,[fp_year_1_amt_sub_income] 
 ,[updated]
 ,[updated_by])
 SELECT
 a.fk_tenant_id
,a.budget_year
,a.sub_tenant_id
,a.fk_budget_phase_id
,org_bud_prev_year_main = SUM(ISNULL(b.org_bud_prev_year_main, 0)) 
,fp_year_1_amt_main = SUM(ISNULL(a.fp_year_1_amt_main, 0))
,fp_year_1_amt_main_expence = SUM(ISNULL(fp_year_1_amt_main_expence, 0))
,fp_year_1_amt_main_income = SUM(ISNULL(fp_year_1_amt_main_income, 0))
,fp_year_1_amt_sub = SUM(ISNULL(b.fp_year_1_amt_sub, 0))
,fp_year_1_amt_sub_expence = SUM(ISNULL(fp_year_1_amt_sub_expence, 0))
,fp_year_1_amt_sub_income = SUM(ISNULL(fp_year_1_amt_sub_income, 0))
,GETDATE() AS updated
,@user_id AS updated_by
FROM #projbudgetdata_3 a
LEFT JOIN #projbudgetdata_4 b ON a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year AND a.sub_tenant_id = b.sub_tenant_id
WHERE a.fk_tenant_id = @tenant_id and a.budget_year = @budget_year
GROUP BY
a.fk_tenant_id
,a.budget_year
,a.sub_tenant_id
,a.fk_budget_phase_id

GO