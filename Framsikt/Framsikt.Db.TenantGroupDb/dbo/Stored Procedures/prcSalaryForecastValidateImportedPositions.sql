CREATE OR ALTER PROCEDURE [dbo].[prcSalaryForecastValidateImportedPositions]
(
@tenantId int,
@budgetYear int,
@useSalaryTable int,
@salaryTableName nvarchar(255),
@forecastPeriod int
)
as
begin

	declare @budgetYearStart int
	declare @budgetYearEnd int
	set @budgetYearStart = convert(varchar(10), @budgetYear) + '01'
	set @budgetYearEnd = convert(varchar(10), @budgetYear) + '12'

	--Clear all the error information--
	UPDATE [tbu_stage_salary_forecast]
	SET fk_account_code_error = 0, 
		fk_department_code_error = 0,
		fk_function_code_error = 0,
		fk_project_code_error = 0,
		free_dim_1_error = 0,
		free_dim_2_error = 0,
		free_dim_3_error = 0,
		free_dim_4_error = 0,
	    fk_salary_type_error = 0,
		ext_emp_type_error = 0, 
		fk_pension_type_error = 0,
		start_period_error = 0,
		end_period_error = 0,
		res_name_error = 0,
		salary_step_error=0,
		fk_tax_rate_error=0,
		fk_res_id_error=0,
		error_count = 0
	WHERE fk_tenant_id = @tenantId
	and budget_year = @budgetYear
	and forecast_period = @forecastPeriod
	and not_to_include = 0

	/* Update fk_treatment_code column */
	update [tbu_stage_salary_forecast] set fk_treatment_code = sal.handling_id
	FROM [tbu_stage_salary_forecast] pos, [tco_salary_acc_category] sal
	where pos.fk_tenant_id = @tenantId
	and pos.budget_year = @budgetYear
	and pos.forecast_period = @forecastPeriod
	and pos.fk_salary_type = sal.pk_salary_acc_category
	and pos.fk_tenant_id = sal.fk_tenant_id
	and pos.not_to_include = 0
	/* End - Update fk_treatment_code column */

	--Validate account codes--
	UPDATE [tbu_stage_salary_forecast]
	SET fk_account_code_error = 1, error_count = error_count + 1 
	from [tbu_stage_salary_forecast] pos
		left outer join tco_accounts acc on pos.fk_tenant_id = acc.pk_tenant_id and pos.fk_account_code = acc.pk_account_code
		left outer join tmd_salary_positions_accounts spa 
        ON  pos.fk_tenant_id = spa.fk_tenant_id AND pos.budget_year = spa.budget_year and acc.pk_account_code = spa.fk_account_code 
		left outer join gmd_reporting_line rl on spa.fk_account_code  = rl.fk_kostra_account_code and rl.report <> 'YBUD1'
		where spa.fk_account_code IS NULL AND
		pos.fk_tenant_id = @tenantId AND 
		pos.budget_year = @budgetYear AND 
		pos.forecast_period = @forecastPeriod
		and pos.not_to_include = 0

	--Validate department codes --
	UPDATE [tbu_stage_salary_forecast]
	SET fk_department_code_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_departments DEPTS ON DEPTS.pk_department_code = [tbu_stage_salary_forecast].fk_department_code AND 
		[tbu_stage_salary_forecast].fk_tenant_id = DEPTS.fk_tenant_id AND
		@budgetYear BETWEEN DEPTS.year_from AND DEPTS.year_to
	WHERE DEPTS.pk_department_code IS NULL AND 
		[tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

    --Validate Functions--
	UPDATE [tbu_stage_salary_forecast]
	SET fk_function_code_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_functions fn on fn.pk_Function_code = [tbu_stage_salary_forecast].fk_function_code AND 
		[tbu_stage_salary_forecast].fk_tenant_id = fn.pk_tenant_id  AND fn.isActive = 1 AND
		@budgetYear BETWEEN DATEPART(year,fn.dateFrom) AND DATEPART(year,fn.dateTo)
	WHERE fn.pk_Function_code IS NULL AND 
		[tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;


	--Validate Projects--
	UPDATE [tbu_stage_salary_forecast]
	SET fk_project_code_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_projects prj on prj.pk_project_code = [tbu_stage_salary_forecast].fk_project_code AND 
		[tbu_stage_salary_forecast].fk_tenant_id = prj.fk_tenant_id  AND prj.active = 1
	WHERE prj.pk_project_code IS NULL
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].fk_project_code != '' 
		AND [tbu_stage_salary_forecast].fk_project_code IS NOT NULL
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate free dim 1 --
	UPDATE [tbu_stage_salary_forecast]
	SET free_dim_1_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = [tbu_stage_salary_forecast].free_dim_1 AND 
		[tbu_stage_salary_forecast].fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
		AND frdm.status = 1
	WHERE frdm.free_dim_column IS NULL
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].free_dim_1 != '' 
		AND [tbu_stage_salary_forecast].free_dim_1 IS NOT NULL
		AND [tbu_stage_salary_forecast].not_to_include = 0 ; 

	-- Validate free dim 2 --
	UPDATE [tbu_stage_salary_forecast]
	SET free_dim_2_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = [tbu_stage_salary_forecast].free_dim_2 AND 
		[tbu_stage_salary_forecast].fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
		AND frdm.status = 1
	WHERE frdm.free_dim_column IS NULL
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].free_dim_2 != '' 
		AND [tbu_stage_salary_forecast].free_dim_2 IS NOT NULL
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate free dim 3 --
	UPDATE [tbu_stage_salary_forecast]
	SET free_dim_3_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = [tbu_stage_salary_forecast].free_dim_3 AND 
		[tbu_stage_salary_forecast].fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
		AND frdm.status = 1
	WHERE frdm.free_dim_column IS NULL
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].free_dim_3 != '' 
		AND [tbu_stage_salary_forecast].free_dim_3 IS NOT NULL
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate free dim 4 --
	UPDATE [tbu_stage_salary_forecast]
	SET free_dim_4_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = [tbu_stage_salary_forecast].free_dim_4 AND 
		[tbu_stage_salary_forecast].fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
		AND frdm.status = 1
	WHERE frdm.free_dim_column IS NULL
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].free_dim_4 != '' 
		AND [tbu_stage_salary_forecast].free_dim_4 IS NOT NULL
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate salary type --
	UPDATE [tbu_stage_salary_forecast]
	SET fk_salary_type_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN [tco_salary_acc_category] sal on sal.pk_salary_acc_category = [tbu_stage_salary_forecast].fk_salary_type AND 
		[tbu_stage_salary_forecast].fk_tenant_id = sal.fk_tenant_id
	WHERE sal.pk_salary_acc_category IS NULL
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate pension type on employment --
	UPDATE [tbu_stage_salary_forecast]
	SET fk_pension_type_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	WHERE 
 [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].not_to_include = 0 and
		[tbu_stage_salary_forecast].fk_treatment_code = 1 
		and fk_pension_type not in (select pension_type from tmd_pension_type where fk_tenant_id = @tenantId and year_from <=@budgetYear and year_to>= @budgetYear)

	-- Validate pension type on add on which has no employment --
	UPDATE s1
	SET s1.fk_pension_type_error = 1, s1.error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] s1
	LEFT JOIN tmd_pension_type pen on pen.pension_type = s1.fk_pension_type AND 
		s1.fk_tenant_id = pen.fk_tenant_id
	WHERE pen.pension_type IS NULL
	AND s1.fk_tenant_id = @tenantId AND 
		s1.budget_year = @budgetYear AND 
		s1.forecast_period = @forecastPeriod
		AND s1.not_to_include = 0 AND
		NOT EXISTS 
		(
			/*retrieve addons that have employment id and exclude them from being validated*/
			select s2.employment_id from [dbo].[tbu_stage_salary_forecast] s2
			join [tbu_stage_salary_forecast] s3 ON s3.fk_Tenant_id = s2.fk_tenant_id
				and s3.budget_year = s2.budget_year
				and s3.forecast_period = s2.forecast_period
				and s3.not_to_include = 0
				and s3.fk_treatment_code = 1
				AND s3.employment_id = s2.employment_id
			where s2.fk_Tenant_id = @tenantId
			and s2.budget_year = @budgetYear
			and s2.forecast_period = @forecastPeriod
			and s2.not_to_include = 0
			and s2.fk_treatment_code = 2
			AND s1.employment_id = s2.employment_id
		);

		-- Validate tax rate --
	UPDATE [tbu_stage_salary_forecast]
	SET fk_tax_rate_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] sp
	LEFT JOIN  tbu_employments_tax_rate tr on 
		sp.fk_tenant_id = tr.fk_tenant_id
	WHERE (sp.fk_tax_rate IS NULL OR sp.fk_tax_rate not in(select a.tax_rate from tbu_employments_tax_rate a where a.fk_tenant_id = @tenantId))
	AND sp.fk_tenant_id = @tenantId AND 
		sp.budget_year = @budgetYear AND 
		sp.forecast_period = @forecastPeriod
		AND sp.not_to_include = 0 ;

	-- Validate emp type --
	UPDATE [tbu_stage_salary_forecast]
	SET ext_emp_type_error = 1, 
		error_count = error_count + 1
	FROM [tbu_stage_salary_forecast] 
	LEFT JOIN tmd_emp_type emp 
		ON emp.pk_emp_type_id = [tbu_stage_salary_forecast].ext_emp_type 
		AND emp.fk_tenant_id = [tbu_stage_salary_forecast].fk_tenant_id 
		AND emp.budget_year = @budgetYear 
	WHERE emp.pk_emp_type_id IS NULL
	AND [tbu_stage_salary_forecast].ext_emp_type IS NOT NULL -- Check if defined in [tbu_stage_salary_forecast]
	AND [tbu_stage_salary_forecast].fk_tenant_id = @tenantId 
	AND [tbu_stage_salary_forecast].budget_year = @budgetYear 
	AND [tbu_stage_salary_forecast].forecast_period = @forecastPeriod
	AND [tbu_stage_salary_forecast].not_to_include = 0;
		--Validate res_id --
	UPDATE t2
	SET t2.fk_res_id_error = 1
	FROM [tbu_stage_salary_forecast] t2
	WHERE t2.fk_treatment_code = 2
	AND t2.fk_res_id IS NOT NULL
	AND t2.fk_tenant_id = @tenantId
	AND t2.budget_year = @budgetYear
	AND t2.forecast_period = @forecastPeriod
	AND t2.not_to_include = 0
	AND NOT EXISTS (
		SELECT 1
		FROM [tbu_stage_salary_forecast] t1
		WHERE t1.fk_res_id = t2.fk_res_id
		AND t1.fk_treatment_code = 1
		AND t1.fk_tenant_id = t2.fk_tenant_id
		AND t1.budget_year = t2.budget_year
		AND t1.forecast_period = t2.forecast_period
		AND t1.not_to_include = 0
	);

	-- Validate res_name --
	UPDATE [tbu_stage_salary_forecast]
	SET res_name_error = 1, error_count = error_count + 1
	FROM [tbu_stage_salary_forecast]
	WHERE (res_name is null or res_name = '')
	and [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate start period --
	UPDATE [tbu_stage_salary_forecast] set error_count = 
	case when (start_period < @budgetYearStart and end_period between @budgetYearStart and @budgetYearEnd) THEN error_count + 0
		 when (start_period < 200000 or start_period > 300000) THEN error_count +1
		 when start_period > end_period then error_count +1
		 ELSE error_count + 0
	END, start_period_error = 
	case when (start_period < @budgetYearStart and end_period between @budgetYearStart and @budgetYearEnd) THEN 0
		 when (start_period < 200000 or start_period > 300000) THEN 1
		 when start_period > end_period then 1
		 ELSE 0
	END
	WHERE [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
		[tbu_stage_salary_forecast].budget_year = @budgetYear AND 
		[tbu_stage_salary_forecast].forecast_period = @forecastPeriod
		AND [tbu_stage_salary_forecast].not_to_include = 0 ;

	-- Validate end period --
UPDATE [tbu_stage_salary_forecast] set error_count = 
case when (end_period > @budgetYearEnd and start_period between @budgetYearStart and @budgetYearEnd) THEN error_count + 0
     when (end_period < 200000 or end_period > 300000) THEN error_count +1
     when end_period < start_period then error_count +1
     ELSE error_count + 0
END, end_period_error = 
case when (end_period > @budgetYearEnd and start_period between @budgetYearStart and @budgetYearEnd) THEN 0
     when (end_period < 200000 or end_period > 300000) THEN 1
     when end_period < start_period then 1
     ELSE 0
END
WHERE [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
      [tbu_stage_salary_forecast].budget_year = @budgetYear AND 
      [tbu_stage_salary_forecast].forecast_period = @forecastPeriod
      AND [tbu_stage_salary_forecast].not_to_include = 0 ;

-- Update import start period--
UPDATE [tbu_stage_salary_forecast] set import_start_period = 
case when (start_period < @budgetYearStart) THEN 1
     ELSE SUBSTRING(convert(varchar(10), start_period), 5, 2)
END	
WHERE [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
      [tbu_stage_salary_forecast].budget_year = @budgetYear AND 
      [tbu_stage_salary_forecast].forecast_period = @forecastPeriod
      AND [tbu_stage_salary_forecast].not_to_include = 0
      and [tbu_stage_salary_forecast].start_period_error = 0
      and [tbu_stage_salary_forecast].end_period_error = 0;

-- Update import end period--
UPDATE [tbu_stage_salary_forecast] set import_end_period = 
case when (end_period > @budgetYearEnd) THEN 12
     ELSE SUBSTRING(convert(varchar(10), end_period), 5, 2)
END	
WHERE [tbu_stage_salary_forecast].fk_tenant_id = @tenantId AND 
      [tbu_stage_salary_forecast].budget_year = @budgetYear AND 
      [tbu_stage_salary_forecast].forecast_period = @forecastPeriod
      AND [tbu_stage_salary_forecast].not_to_include = 0
      and [tbu_stage_salary_forecast].start_period_error = 0
      and [tbu_stage_salary_forecast].end_period_error = 0;

-- Update holiday_type_id
SET DATEFORMAT dmy
update [tbu_stage_salary_forecast] set fk_holiday_type_id = 
CASE 
    WHEN birth_date = '' THEN 5
    --#166032 - holiday weeks age for 6 weeks should start from 59
    WHEN ((budget_year - datepart(year,birth_date))) >= 59 THEN 6
    ELSE 5
END
where fk_tenant_id = @tenantId
and budget_year = @budgetYear
and forecast_period = @forecastPeriod
and not_to_include = 0
-- End - Update holiday_type_id

-- Validate SalaryStep --
IF(@useSalaryTable = 1)
BEGIN
    UPDATE [tbu_stage_salary_forecast]
    SET salary_step_error = 1, error_count = error_count + 1 
    where [tbu_stage_salary_forecast].fk_tenant_id = @tenantId  
    AND [tbu_stage_salary_forecast].budget_year = @budgetYear  
    AND [tbu_stage_salary_forecast].forecast_period = @forecastPeriod 
    AND [tbu_stage_salary_forecast].not_to_include = 0 AND [tbu_stage_salary_forecast].fk_treatment_code = 1 AND
    [tbu_stage_salary_forecast].salary_step NOT IN (SELECT salary_step from gco_salary_table WHERE salary_table_name = @salaryTableName) ;
END

BEGIN
    UPDATE [tbu_stage_salary_forecast]
    SET salary_step = null 
    where [tbu_stage_salary_forecast].fk_tenant_id = @tenantId  
    AND [tbu_stage_salary_forecast].budget_year = @budgetYear  
    AND [tbu_stage_salary_forecast].forecast_period = @forecastPeriod 
    AND [tbu_stage_salary_forecast].not_to_include = 0 AND [tbu_stage_salary_forecast].fk_treatment_code = 2 ;
END

end