CREATE or ALTER PROCEDURE [dbo].[prcInitializeMRClimateIndicators]      
@forecast_period INT,  @fk_tenant_id INT,  @user_id INT       
AS      
     
DECLARE @budget_year INT      
DECLARE @prev_forecast_period INT      
DECLARE @org_version VARCHAR(25)       
DECLARE @language VARCHAR(10)      
DECLARE @last_period INT      
DECLARE @ub_period INT      
DECLARE @flag_name varchar(30)      
DECLARE @flag_status INT      
DECLARE @flag INT      
DECLARE @last_reported_period INT      
DECLARE @last_reported_budget_year INT      
     
SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)      
SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)      
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)      
SET @budget_year = @forecast_period/100      
SET @last_period = (@budget_year*100)+12      
SET @ub_period = (@budget_year*100)+13      
     
SET @flag_name = 'MR_COPYREPORT_ORGLEVEL'      
SET @flag_status = (SELECT flag_status FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id  AND flag_name = @flag_name AND fk_org_version = @org_version)      
IF (@flag_status <> 0 AND @flag_status IS NOT NULL)      
BEGIN    
     
 SET  @last_reported_period = (select max(t3.forecast_period) from tco_indicator_setup t1       
        INNER JOIN  tfp_effect_climate_detail t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_indicator_code = t2.fk_indicator_code      
        INNER JOIN tmr_climate_indicators_status t3 on t1.fk_tenant_id = t3.fk_tenant_id and t1.pk_indicator_code = t3.fk_indicator_code  
        where t1.fk_tenant_id = @fk_tenant_id  and t2.org_level >=@flag_status and t1.indicator_type = 3  and t3.forecast_period < @forecast_period)    
     
 IF(@last_reported_period IS NOT NULL)    
 BEGIN    
     
  SET @last_reported_budget_year = @last_reported_period/100    
     
  DELETE from tmr_climate_indicators_status where fk_tenant_id= @fk_tenant_id and forecast_period = @forecast_period and pk_id in    
  (    
   select t3.pk_id from tco_indicator_setup t1       
   INNER JOIN  tfp_effect_climate_detail t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_indicator_code = t2.fk_indicator_code      
   INNER JOIN tmr_climate_indicators_status t3 on t1.fk_tenant_id = t3.fk_tenant_id and t1.pk_indicator_code = t3.fk_indicator_code     
   where t1.fk_tenant_id = @fk_tenant_id  and t2.org_level >=@flag_status and budget_year= @budget_year and t3.forecast_period = @forecast_period    
  )    
     
  INSERT INTO tmr_climate_indicators_status (fk_tenant_id, fk_climate_id, fk_indicator_code, achieved_value, forecast_value, updated, updated_by, is_reported, forecast_period, status_desc_id_history, status_desc,fk_indicator_uniq_id )    
   SELECT DISTINCT t3.fk_climate_id, t3.fk_tenant_id, t3.fk_indicator_code, t3.achieved_value, t3.forecast_value, t3.updated, t3.updated_by, t3.is_reported, @forecast_period AS forecast_period,     
   NEWID() AS status_desc_id_history, t3.status_desc, t2.pk_id   
   from tco_indicator_setup t1       
   INNER JOIN  tfp_effect_climate_detail t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_indicator_code = t2.fk_indicator_code      
   INNER JOIN tmr_climate_indicators_status t3 on t1.fk_tenant_id = t3.fk_tenant_id and t1.pk_indicator_code = t3.fk_indicator_code and t2.pk_id = t3.fk_indicator_uniq_id   
   where t1.fk_tenant_id = @fk_tenant_id  and t2.org_level >=@flag_status and t2.budget_year = @last_reported_budget_year  and t3.forecast_period= @last_reported_period      
 END    
END    
     
INSERT INTO tmr_climate_indicators_status (fk_tenant_id, fk_climate_id, fk_indicator_code, achieved_value, forecast_value, status_desc, is_reported, forecast_period,updated, updated_by, status_desc_id_history, fk_indicator_uniq_id)     
SELECT a.fk_tenant_id,b.fk_climate_id,a.pk_indicator_code,'' as achieved_value,'' as forecast_value,'' as  status_desc,1 as is_reported,      
@forecast_period as forecast_period,getdate() as updated,@user_id as updated_by, NEWID() AS status_desc_id_history, b.pk_id  
FROM tco_indicator_setup a     
JOIN tfp_effect_climate_detail b ON b.fk_tenant_id = a.fk_tenant_id AND b.fk_indicator_code = a.pk_indicator_code      
WHERE b.fk_tenant_id = @fk_tenant_id      
AND b.budget_year = @budget_year       
AND a.indicator_type = 3    
AND NOT EXISTS (      
SELECT * FROM tmr_climate_indicators_status d WHERE  a.fk_tenant_id = d.fk_tenant_id      
AND d.forecast_period = @forecast_period      
AND a.pk_indicator_code = d.fk_indicator_code      
AND b.fk_climate_id = d.fk_climate_id )      
  select * from tmr_climate_indicators_status;    
IF @forecast_period IN (@last_period, @ub_period)      
BEGIN      
PRINT 'UPDATE everythig to be reported when period is 12 '  + convert(varchar(400),GETDATE());      
UPDATE tmr_climate_indicators_status SET is_reported = 1 WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period;      
END      
PRINT 'FINISH: Copy climate indicators FROM finplan ' + convert(varchar(400),GETDATE());      
RETURN 0