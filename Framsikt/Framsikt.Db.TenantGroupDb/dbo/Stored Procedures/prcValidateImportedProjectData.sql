CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedProjectData]    
 @tenant_id int,    
 @user_id int,    
 @dimension_id int,    
 @dimension_type varchar(20),    
 @job_id bigint    
AS     
    IF( @job_id = -1 )    
      BEGIN    
          UPDATE tbu_stage_dimensions_import    
          SET    main_project_code_error = 0,    
                 project_code_error = 0,    
                 project_name_error = 0,    
                 year_from_error = 0,    
                 year_to_error = 0,    
       free_dim_1_error = 0,    
     free_dim_2_error = 0,    
     free_dim_3_error = 0,    
     free_dim_4_error = 0,    
     vat_rate_error = 0,    
     vat_refund_error = 0,    
     inv_status_error=0,    
     start_year_error=0,    
     completion_date_error=0,    
     original_finish_year_error=0,    
     fk_department_code_error=0,    
     fk_Function_code_error=0,    
     status_error=0,   
     prog_code_error=0,
     error_count = 0     
          WHERE  fk_tenant_id = @tenant_id    
                 AND [user_id] = @user_id    
                 AND dimension_type = @dimension_id;    
      END    
    ELSE    
      BEGIN    
          UPDATE tbu_stage_dimensions_import    
          SET    main_project_code_error = 0,    
                 project_code_error = 0,    
                 project_name_error = 0,    
                 year_from_error = 0,    
                 year_to_error = 0,    
       free_dim_1_error = 0,    
     free_dim_2_error = 0,    
     free_dim_3_error = 0,    
     free_dim_4_error = 0,    
     vat_rate_error = 0,    
     vat_refund_error = 0,    
     inv_status_error=0,    
     start_year_error=0,    
     completion_date_error=0,    
     original_finish_year_error=0,    
     fk_department_code_error=0,    
     fk_Function_code_error=0,    
     status_error=0,   
     prog_code_error=0,
     error_count = 0     
          WHERE  fk_tenant_id = @tenant_id    
                 AND [job_id] = @job_id    
                 AND dimension_type = @dimension_id;    
      END    
    
  DECLARE @pkIdTable TABLE(pkIds int)    
    
     -- Validate Project code    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET project_code_error = project_code_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND (project_code is NULL OR project_code = '');     
    END    
    ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET project_code_error = project_code_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND (project_code is NULL OR project_code = '');     
    END    
    
  -- Validate Project name    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET project_name_error = project_name_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND (project_name is NULL OR project_name = '');     
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET project_name_error = project_name_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND (project_name is NULL OR project_name = '');     
    END    
    
  -- Validate Status     
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET status_error = status_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND (status is NULL OR status = '' OR (status != '0' AND status != '1'));    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET status_error = status_error + 1, error_count = error_count + 1      
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND (status is NULL OR status = '' OR (status != '0' AND status != '1'));     
  END    
    
  -- Validate Year from NULL CHECK      
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET year_from_error = year_from_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND (year_from is NULL OR year_from = '' OR year_from =0);    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET year_from_error = year_from_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND (year_from is NULL OR year_from = '' OR year_from =0);    
    END    
    
    
  -- Validate Year to NULL CHECK    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET year_to_error = year_to_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND (year_to is NULL OR year_to = '' OR year_to =0);    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET year_to_error = year_to_error + 1, error_count = error_count + 1     
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND (year_to is NULL OR year_to = '' OR year_to =0);    
    END    
    
  -- Validate length of the year    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET year_from_error = year_from_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND  LEN(year_from) < 4;    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET year_from_error = year_from_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND  LEN(year_from) < 4;    
    END    
    
  -- Validate length of the year    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET year_to_error = year_to_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND  LEN(year_to) < 4;    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET year_to_error = year_to_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND  LEN(year_to) < 4;    
    END    
    
        
    -- check if  yearfrom  is greater then  yearto    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET year_from_error = year_from_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND  (year_from) > year_to;    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET year_from_error = year_from_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND  (year_from) > year_to;    
    END    
    
    -- check if to year is small then  yearfrom    
  IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET year_to_error = year_to_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_id AND  (year_to) < year_from;    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET year_to_error = year_to_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_id AND  (year_to) < year_from;    
    END    
   IF (@job_id = -1)    
    BEGIN    
   update tbu_stage_dimensions_import SET prog_code_error = prog_code_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND (prog_code not in (select pk_prog_code from tco_inv_program where fk_tenant_id = @tenant_id)) ;    
    END    
  ELSE    
    BEGIN    
   update tbu_stage_dimensions_import SET prog_code_error = prog_code_error + 1, error_count = error_count + 1    
   WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND (prog_code not in (select pk_prog_code from tco_inv_program where fk_tenant_id = @tenant_id));    
    END  
  IF(LOWER(TRIM(@dimension_type)) = LOWER(TRIM('Projects')))    
  BEGIN    
    IF (@job_id = -1)    
      BEGIN    
    UPDATE tbu_stage_dimensions_import    
    SET project_code_error =project_code_error + 1,year_from_error =year_from_error + 1,year_to_error =year_to_error + 1, error_count = error_count + 1     
    FROM tbu_stage_dimensions_import     
    JOIN tco_projects proj on tbu_stage_dimensions_import.project_code = proj.pk_project_code AND    
    tbu_stage_dimensions_import.project_name = proj.project_name  AND proj.active= tbu_stage_dimensions_import.status AND    
    CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE)  = proj.date_from AND     
    CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE)  = proj.date_to AND tbu_stage_dimensions_import.fk_tenant_id = proj.fk_tenant_id    
       WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    (tbu_stage_dimensions_import.project_code is NULL OR tbu_stage_dimensions_import.project_code ='')  AND  LEN(tbu_stage_dimensions_import.year_from) = 4    
    AND  LEN(tbu_stage_dimensions_import.year_to) = 4
    
    update tbu_stage_dimensions_import 
    SET prog_code_error = prog_code_error + 1, error_count = error_count + 1    
    WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND (prog_code not in (select pk_prog_code from tco_inv_program where fk_tenant_id = @tenant_id));    
    
    --UPDATE tbu_stage_dimensions_import      
    --SET project_code_error =project_code_error + 1,year_from_error =year_from_error + 1,year_to_error =year_to_error + 1, error_count = error_count + 1      
    --            FROM tbu_stage_dimensions_import      
    --            JOIN tco_projects proj on tbu_stage_dimensions_import.fk_tenant_id = proj.fk_tenant_id and tbu_stage_dimensions_import.project_code = proj.pk_project_code      
    --AND      
    --tbu_stage_dimensions_import.project_name = proj.project_name  AND tbu_stage_dimensions_import.status=proj.active and    
    --            ((CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE) BETWEEN  proj.date_from AND proj.date_to)       
    --OR      
    --            (CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE) BETWEEN  proj.date_from AND proj.date_to)    
    --OR      
    --            (CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE) = proj.date_from             
    --OR      
    --            CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE) = proj.date_to )    
    --OR     
    --(proj.date_from BETWEEN  CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE) AND CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-01-01'  AS DATE) )       
    --OR    
    --(proj.date_to BETWEEN  CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE) AND CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-01-01'  AS DATE) )       
    --)      
    --            WHERE tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND      
    --            tbu_stage_dimensions_import.user_id = @user_id AND      
    --            dimension_type = @dimension_id AND                 
    --            LEN(tbu_stage_dimensions_import.year_from) = 4      
    --            AND  LEN(tbu_stage_dimensions_import.year_to) = 4    
    
          -- Commenting out to allow existing project import per 102682    
    --UPDATE tbu_stage_dimensions_import    
    --SET main_project_code_error =main_project_code_error + 1, error_count = error_count + 1     
    --FROM tbu_stage_dimensions_import     
    --JOIN tco_projects proj on tbu_stage_dimensions_import.fk_tenant_id = proj.fk_tenant_id AND     
    --        tbu_stage_dimensions_import.project_code = proj.pk_project_code AND     
    --        tbu_stage_dimensions_import.main_project_code = proj.fk_main_project_code and     
    --        tbu_stage_dimensions_import.project_name = proj.project_name and proj.active= tbu_stage_dimensions_import.status AND    
    --                 CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE)  = proj.date_from AND     
    --        CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE)  = proj.date_to    
               
    --WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    --(tbu_stage_dimensions_import.main_project_code is NOT NULL OR tbu_stage_dimensions_import.main_project_code <> '')    
    --AND  LEN(tbu_stage_dimensions_import.year_from) = 4 AND  LEN(tbu_stage_dimensions_import.year_to) = 4    
    
    
    UPDATE tbu_stage_dimensions_import    
    SET main_project_code_error =main_project_code_error + 1,error_count = error_count + 1     
    FROM tbu_stage_dimensions_import      
    LEFT JOIN tco_main_projects mainproj on tbu_stage_dimensions_import.fk_tenant_id = mainproj.fk_tenant_id  and     
              tbu_stage_dimensions_import.main_project_code = mainproj.pk_main_project_code      
    WHERE tbu_stage_dimensions_import.fk_tenant_id = @tenant_id      
      and tbu_stage_dimensions_import.user_id = @user_id     
      AND dimension_type = @dimension_id      
      and tbu_stage_dimensions_import.main_project_code <> ''    
      AND mainproj.pk_main_project_code is NULL    
    
    Update tbu_stage_dimensions_import     
    set prog_code = '' where tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id      
    and prog_code is null    
    
       
    --Validation for VAT_RATE--    
    UPDATE tbu_stage_dimensions_import    
    SET vat_rate_error =vat_rate_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.vat_rate > 25    
    
       
    --Validation for VAT_REFUND--    
    UPDATE tbu_stage_dimensions_import    
    SET vat_refund_error = vat_refund_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.vat_refund > 100    
   END    
    ELSE    
      BEGIN    
    UPDATE tbu_stage_dimensions_import    
    SET project_code_error =project_code_error + 1,year_from_error =year_from_error + 1,year_to_error =year_to_error + 1, error_count = error_count + 1     
    FROM tbu_stage_dimensions_import     
    JOIN tco_projects proj on tbu_stage_dimensions_import.project_code = proj.pk_project_code AND     
    tbu_stage_dimensions_import.project_name = proj.project_name and proj.active= tbu_stage_dimensions_import.status AND    
    CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE)  = proj.date_from AND     
    CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE)  = proj.date_to AND tbu_stage_dimensions_import.fk_tenant_id = proj.fk_tenant_id AND proj.active=1    
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    (tbu_stage_dimensions_import.project_code is NULL OR tbu_stage_dimensions_import.project_code ='')  AND  LEN(tbu_stage_dimensions_import.year_from) = 4    
    AND  LEN(tbu_stage_dimensions_import.year_to) = 4    
        
    -- Commenting out to allow existing project import per 102682    
    --UPDATE tbu_stage_dimensions_import    
    --SET main_project_code_error =main_project_code_error + 1, error_count = error_count + 1     
    --FROM tbu_stage_dimensions_import     
    --JOIN tco_projects proj on tbu_stage_dimensions_import.fk_tenant_id = proj.fk_tenant_id AND     
    --        tbu_stage_dimensions_import.project_code = proj.pk_project_code AND     
    --        tbu_stage_dimensions_import.main_project_code = proj.fk_main_project_code and    
    --        tbu_stage_dimensions_import.project_name = proj.project_name and proj.active= tbu_stage_dimensions_import.status AND    
    --        CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE)  = proj.date_from AND     
    --        CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE)  = proj.date_to AND proj.active=1    
               
    --WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    --(tbu_stage_dimensions_import.main_project_code is NOT NULL OR tbu_stage_dimensions_import.main_project_code <> '')    
    --AND  LEN(tbu_stage_dimensions_import.year_from) = 4 AND  LEN(tbu_stage_dimensions_import.year_to) = 4    
    
    
    UPDATE tbu_stage_dimensions_import    
    SET main_project_code_error =main_project_code_error + 1,error_count = error_count + 1     
    FROM tbu_stage_dimensions_import      
    LEFT JOIN tco_main_projects mainproj on tbu_stage_dimensions_import.fk_tenant_id = mainproj.fk_tenant_id  and     
              tbu_stage_dimensions_import.main_project_code = mainproj.pk_main_project_code        
    WHERE tbu_stage_dimensions_import.fk_tenant_id = @tenant_id      
      and tbu_stage_dimensions_import.job_Id = @job_id     
      AND dimension_type = @dimension_id      
      and tbu_stage_dimensions_import.main_project_code <> ''    
      AND mainproj.pk_main_project_code is NULL    
    
    Update tbu_stage_dimensions_import     
    set prog_code = '' where tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id      
    and prog_code is null    
    
       
    --Validation for VAT_RATE--    
    UPDATE tbu_stage_dimensions_import    
    SET vat_rate_error =vat_rate_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.vat_rate > 25    
    
       
    --Validation for VAT_REFUND--    
    UPDATE tbu_stage_dimensions_import    
    SET vat_refund_error = vat_refund_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.vat_refund > 100    
   END    
  END    
   
   
  IF(LOWER(TRIM(@dimension_type)) = LOWER(TRIM('MainProjects')))    
  BEGIN    
    -- Validate same project with same from and to year is imported with differnt name   #111491  
    IF (@job_id = -1)    
     BEGIN    
     update  a    
     set year_from_error =a.year_from_error + 1,year_to_error =a.year_to_error + 1, error_count = a.error_count + 1     
     from tbu_stage_dimensions_import as a    
     join tbu_stage_dimensions_import as b on  a.[user_id]=b.[user_id] and a.main_project_code=b.main_project_code and a.year_from=b.year_from and a.year_to=b.year_to    
     where a.fk_tenant_id = @tenant_id AND a.[user_id] = @user_id AND a.dimension_type = @dimension_id and a.project_name!=b.project_name    
       
     END    
   ELSE    
     BEGIN    
     update  a    
     set year_from_error =a.year_from_error + 1,year_to_error =a.year_to_error + 1, error_count = a.error_count + 1     
     from tbu_stage_dimensions_import as a    
     join tbu_stage_dimensions_import as b on a.job_Id=b.job_Id and a.main_project_code=b.main_project_code and a.year_from=b.year_from and a.year_to=b.year_to    
     where a.fk_tenant_id = @tenant_id AND a.[job_id] = @job_id AND a.dimension_type = @dimension_id and a.project_name!=b.project_name    
       
     END    
    Declare @paramValue varchar(50)    
    IF (@job_id = -1)    
      BEGIN    
    --UPDATE tbu_stage_dimensions_import    
    --SET project_code_error =project_code_error + 1,error_count = error_count + 1     
    --FROM tbu_stage_dimensions_import      
    --LEFT JOIN tco_main_projects mainproj on tbu_stage_dimensions_import.fk_tenant_id = mainproj.fk_tenant_id  and     
    --          tbu_stage_dimensions_import.project_code = mainproj.pk_main_project_code        
    --WHERE tbu_stage_dimensions_import.fk_tenant_id = @tenant_id      
    --  and tbu_stage_dimensions_import.user_id = @user_id     
    --  AND dimension_type = @dimension_id      
    --  and tbu_stage_dimensions_import.project_code <> ''    
    --  AND mainproj.pk_main_project_code is not NULL    
    
    UPDATE tbu_stage_dimensions_import    
    SET project_code_error =project_code_error + 1,year_from_error =year_from_error + 1,year_to_error =year_to_error + 1, error_count = error_count + 1     
    FROM tbu_stage_dimensions_import     
    JOIN tco_main_projects mainproj on tbu_stage_dimensions_import.fk_tenant_id = mainproj.fk_tenant_id and tbu_stage_dimensions_import.project_code = mainproj.pk_main_project_code AND     
    (CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE) BETWEEN  mainproj.budget_year_from AND mainproj.budget_year_to) AND    
    (CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE) BETWEEN  mainproj.budget_year_from AND mainproj.budget_year_to) AND    
    (CAST(CASt(tbu_stage_dimensions_import.year_from as varchar(8)) + '-01-01'  AS DATE)  != mainproj.budget_year_from AND     
    CAST(CASt(tbu_stage_dimensions_import.year_to as varchar(8)) + '-12-31'  AS DATE)  != mainproj.budget_year_to )    
    WHERE tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.user_id = @user_id AND     
    dimension_type = @dimension_id AND       
    LEN(tbu_stage_dimensions_import.year_from) = 4    
    AND  LEN(tbu_stage_dimensions_import.year_to) = 4    
    
    --validation for status    
    UPDATE tbu_stage_dimensions_import    
    SET status_error = status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.status is not null and tbu_stage_dimensions_import.status<>'' and TRY_PARSE(tbu_stage_dimensions_import.status as int) IS NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.status as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET status_error = status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.status not in (0,1) and pk_id in (select pkIds from @pkIdTable)    
    
    --validaiton for investment status    
    UPDATE tbu_stage_dimensions_import    
    SET inv_status_error = inv_status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.inv_status is not null and tbu_stage_dimensions_import.inv_status <> '' and TRY_PARSE(tbu_stage_dimensions_import.inv_status as int) IS NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.inv_status as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET inv_status_error = inv_status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.inv_status not in (0,1,2,3,4,5,7,8) and  pk_id in (select pkIds from @pkIdTable)    
    
    select   top 1 @paramValue= value from STRING_SPLIT ((select param_value from tco_parameters where param_name='FINPLAN_INVESTMENT_LEVEL' and fk_tenant_id=@tenant_id),'_')    
    if(Lower(@paramValue)='service')    
    BEGIN    
        --Validate Functions--    
     UPDATE tbu_stage_dimensions_import         
     SET fk_Function_code_error = 1, error_count = error_count + 1    
     FROM tbu_stage_dimensions_import     
     LEFT JOIN tco_functions fn on     
      tbu_stage_dimensions_import.fk_tenant_id = fn.pk_tenant_id AND    
      tbu_stage_dimensions_import.fk_Function_code = fn.pk_Function_code AND       
      --tbu_stage_dimensions_import.start_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND    
      fn.isActive = 1    
     WHERE tbu_stage_dimensions_import.fk_Function_code IS Not NULL and tbu_stage_dimensions_import.fk_Function_code <>''    
     and fn.pk_Function_code IS NULL     
     AND     
      tbu_stage_dimensions_import.user_id = @user_id AND     
      dimension_type = @dimension_id AND     
      tbu_stage_dimensions_import.fk_tenant_id = @tenant_id    
       
    END    
    ELSE    
    BEGIN    
    --validtion for department code    
     UPDATE tbu_stage_dimensions_import    
     SET fk_department_code_error = 1, error_count = error_count + 1    
     FROM tbu_stage_dimensions_import     
     LEFT JOIN tco_departments DEPTS ON     
      tbu_stage_dimensions_import.fk_tenant_id = DEPTS.fk_tenant_id AND    
      tbu_stage_dimensions_import.fk_department_code = DEPTS.pk_department_code AND    
      --tbu_stage_dimensions_import.start_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND    
      DEPTS.[status] = 1     
     WHERE tbu_stage_dimensions_import.fk_department_code IS Not NULL and  tbu_stage_dimensions_import.fk_department_code <>''    
     and DEPTS.pk_department_code IS NULL    
     AND     
      tbu_stage_dimensions_import.user_id = @user_id AND     
      dimension_type = @dimension_id AND     
      tbu_stage_dimensions_import.fk_tenant_id = @tenant_id    
    END    
    
    --validate finish year    
    SET DATEFORMAT DMY;    
    SET LANGUAGE British;    
    UPDATE tbu_stage_dimensions_import    
    SET completion_date_error = completion_date_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND completion_date<>''and (isdate(completion_date)>0 and (LEN(completion_date)<10))     
    
    --Validate start year    
    UPDATE tbu_stage_dimensions_import    
    SET start_year_error = start_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND start_year is not null and start_year <>'' and  TRY_PARSE(start_year as int) is NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.original_finish_year as int) is not null and TRY_PARSE(start_year as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET start_year_error = start_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND(start_year is not null and start_year <>'' and completion_date<>'' and original_finish_year is not null and original_finish_year <>'') and (start_year> RIGHT(completion_date, 4) or start_year > original_finish_year) and  pk_id in (select pkIds from @pkIdTable)    
    
    --validate completion date.    
    UPDATE tbu_stage_dimensions_import    
    SET original_finish_year_error = original_finish_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND original_finish_year is not null and original_finish_year <>'' and TRY_PARSE(original_finish_year as int)is NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.original_finish_year as int) is not null and TRY_PARSE(start_year as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET original_finish_year_error = original_finish_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND (original_finish_year < start_year) and  pk_id in (select pkIds from @pkIdTable)    
   END    
    ELSE    
      BEGIN    
    --UPDATE tbu_stage_dimensions_import    
    --SET project_code_error =project_code_error + 1,error_count = error_count + 1     
    --FROM tbu_stage_dimensions_import      
    --LEFT JOIN tco_main_projects mainproj on tbu_stage_dimensions_import.fk_tenant_id = mainproj.fk_tenant_id  and     
    --          tbu_stage_dimensions_import.project_code = mainproj.pk_main_project_code        
    --WHERE tbu_stage_dimensions_import.fk_tenant_id = @tenant_id      
    --  and tbu_stage_dimensions_import.job_Id = @job_id     
    --  AND dimension_type = @dimension_id      
    --  and tbu_stage_dimensions_import.project_code <> ''    
    --  AND mainproj.pk_main_project_code is not NULL    
    
    --validation for status    
    UPDATE tbu_stage_dimensions_import    
    SET status_error = status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.status is not null and tbu_stage_dimensions_import.status<> '' and TRY_PARSE(tbu_stage_dimensions_import.status as int) IS NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.status as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET status_error = status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.status not in (0,1) and pk_id in (select pkIds from @pkIdTable)    
    
    --validaiton for investment status    
    UPDATE tbu_stage_dimensions_import    
    SET inv_status_error = inv_status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.inv_status is not null and tbu_stage_dimensions_import.inv_status <> '' and TRY_PARSE(tbu_stage_dimensions_import.inv_status as int) IS NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.inv_status as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET inv_status_error = inv_status_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND     
    tbu_stage_dimensions_import.inv_status not in (0,1,2,3,4,5,7,8) and  pk_id in (select pkIds from @pkIdTable)    
    
    select   top 1 @paramValue= value from STRING_SPLIT ((select param_value from tco_parameters where param_name='FINPLAN_INVESTMENT_LEVEL' and fk_tenant_id=@tenant_id),'_')    
    if(Lower(@paramValue)='service')    
    BEGIN    
        --Validate Functions--    
     UPDATE tbu_stage_dimensions_import    
     SET fk_Function_code_error = 1, error_count = error_count + 1    
     FROM tbu_stage_dimensions_import     
     LEFT JOIN tco_functions fn on     
      tbu_stage_dimensions_import.fk_tenant_id = fn.pk_tenant_id AND    
      tbu_stage_dimensions_import.fk_Function_code = fn.pk_Function_code AND       
      --tbu_stage_dimensions_import.start_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND    
      fn.isActive = 1    
     WHERE tbu_stage_dimensions_import.fk_Function_code IS Not NULL and tbu_stage_dimensions_import.fk_Function_code <>''    
     and fn.pk_Function_code IS NULL     
     AND     
      tbu_stage_dimensions_import.job_Id = @job_id AND     
      dimension_type = @dimension_id AND     
      tbu_stage_dimensions_import.fk_tenant_id = @tenant_id    
       
    END    
    ELSE    
    BEGIN    
    --validtion for department code    
     UPDATE tbu_stage_dimensions_import    
     SET fk_department_code_error = 1, error_count = error_count + 1    
     FROM tbu_stage_dimensions_import     
     LEFT JOIN tco_departments DEPTS ON     
      tbu_stage_dimensions_import.fk_tenant_id = DEPTS.fk_tenant_id AND    
      tbu_stage_dimensions_import.fk_department_code = DEPTS.pk_department_code AND    
      --tbu_stage_dimensions_import.start_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND    
      DEPTS.[status] = 1     
     WHERE tbu_stage_dimensions_import.fk_department_code IS Not NULL and tbu_stage_dimensions_import.fk_department_code <>''    
     and DEPTS.pk_department_code IS NULL     
     AND     
      tbu_stage_dimensions_import.job_Id = @job_id AND     
      dimension_type = @dimension_id AND     
      tbu_stage_dimensions_import.fk_tenant_id = @tenant_id    
    END    
    
    --validate finish year    
    SET DATEFORMAT DMY;    
    SET LANGUAGE British;    
    UPDATE tbu_stage_dimensions_import    
    SET completion_date_error = completion_date_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND  completion_date<>'' and (isdate(completion_date)>0 and (LEN(completion_date)<10))     
    
    --Validate start year    
    UPDATE tbu_stage_dimensions_import    
    SET start_year_error = start_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND start_year is not null and start_year <>'' and TRY_PARSE(start_year as int) is NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.original_finish_year as int) is not null and TRY_PARSE(start_year as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET start_year_error = start_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND (start_year> RIGHT(completion_date, 4) or start_year > original_finish_year) and  pk_id in (select pkIds from @pkIdTable)    
    
    --validate completion date.    
    UPDATE tbu_stage_dimensions_import    
    SET original_finish_year_error = original_finish_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id     
    AND original_finish_year is not null and original_finish_year <>''  and TRY_PARSE(original_finish_year as int)is NULL    
    
    
    DELETE FROM @pkIdTable    
    INSERT INTO @pkIdTable    
    select pk_id from tbu_stage_dimensions_import where TRY_PARSE(tbu_stage_dimensions_import.original_finish_year as int) is not null and TRY_PARSE(start_year as int) is not null    
    
    UPDATE tbu_stage_dimensions_import    
    SET original_finish_year_error = original_finish_year_error + 1, error_count = error_count + 1     
    WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id   
    AND (original_finish_year < start_year) and  pk_id in (select pkIds from @pkIdTable)    
   END    
  END    
    
RETURN 0 