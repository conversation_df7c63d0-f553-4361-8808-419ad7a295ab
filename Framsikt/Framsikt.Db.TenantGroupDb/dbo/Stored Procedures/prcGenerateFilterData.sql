
CREATE OR ALTER PROCEDURE [dbo].[prcGenerateFilterData]
@fk_tenant_id INT,
@budget_year INT,
@user_id INT

AS

IF @budget_year = 0

BEGIN

SET @budget_year = DATEPART(YEAR,GETDATE())

END


SELECT 
a.fk_tenant_id
,a.budget_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
INTO #hlptab_1
FROM tbu_trans_detail a
JOIN tco_user_adjustment_codes b ON a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year AND a.fk_adjustment_code = b.pk_adj_code AND b.status = 1
WHERE a.budget_year = @budget_year
GROUP BY 
a.fk_tenant_id
,a.budget_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4

INSERT INTO #hlptab_1 (fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4)
SELECT 
fk_tenant_id
,budget_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
FROM tbu_trans_detail_original
WHERE budget_year = @budget_year
GROUP BY 
fk_tenant_id
,budget_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4

INSERT INTO #hlptab_1 (fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4)
SELECT 
fk_tenant_id
,gl_year as budget_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
FROM tfp_accounting_data a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code and a.gl_year BETWEEN DATEPART(year, dateFrom) and DATEPART(year, dateTo)
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code and c.type = 'operations'
WHERE gl_year = @budget_year
GROUP BY 
fk_tenant_id
,gl_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4

INSERT INTO #hlptab_1 (fk_tenant_id, budget_year, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4)
SELECT 
fk_tenant_id
,gl_year+1 as budget_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
FROM tfp_accounting_data a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code and a.gl_year BETWEEN DATEPART(year, dateFrom) and DATEPART(year, dateTo)
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code and c.type = 'operations'
WHERE gl_year = @budget_year - 1
GROUP BY 
fk_tenant_id
,gl_year
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4


IF @fk_tenant_id = 0
BEGIN

--Konto

DELETE FROM tco_filter_accounts where budget_year = @budget_year

INSERT INTO tco_filter_accounts (
fk_tenant_id
,budget_year
,fk_department_code
,fk_account_code
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.fk_account_code, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.fk_account_code != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, fk_account_code

--Funksjon
DELETE FROM tco_filter_functions where budget_year = @budget_year

INSERT INTO tco_filter_functions (
fk_tenant_id
,budget_year
,fk_department_code
,fk_function_code
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.fk_function_code, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.fk_function_code != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, fk_function_code


--Prosjekt

DELETE FROM tco_filter_projects where budget_year = @budget_year

INSERT INTO tco_filter_projects (
fk_tenant_id
,budget_year
,fk_department_code
,fk_project_code
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.fk_project_code, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.fk_project_code != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, fk_project_code


--Fridim1

DELETE FROM tco_filter_free_dim_1 where budget_year = @budget_year

INSERT INTO tco_filter_free_dim_1 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_1
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_1, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_1 != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_1


--Fridim2

DELETE FROM tco_filter_free_dim_2 where budget_year = @budget_year

INSERT INTO tco_filter_free_dim_2 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_2
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_2, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_2 != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_2

--Fridim3

DELETE FROM tco_filter_free_dim_3 where budget_year = @budget_year

INSERT INTO tco_filter_free_dim_3 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_3
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_3, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_3 != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_3

--Fridim4

DELETE FROM tco_filter_free_dim_4 where budget_year = @budget_year

INSERT INTO tco_filter_free_dim_4 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_4
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_4, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_4 != ''
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_4


--account level filter

DELETE FROM tco_filter_account_levels where budget_year = @budget_year

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 1, b.level_1_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_1_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 2, b.level_2_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_2_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 3, b.level_3_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_3_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 4, b.level_4_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_4_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 5, b.level_5_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_5_id


END











IF @fk_tenant_id != 0
BEGIN

--Konto

DELETE FROM tco_filter_accounts where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_accounts (
fk_tenant_id
,budget_year
,fk_department_code
,fk_account_code
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.fk_account_code, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.fk_account_code != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, fk_account_code

--Funksjon
DELETE FROM tco_filter_functions where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_functions (
fk_tenant_id
,budget_year
,fk_department_code
,fk_function_code
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.fk_function_code, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.fk_function_code != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, fk_function_code


--Prosjekt

DELETE FROM tco_filter_projects where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_projects (
fk_tenant_id
,budget_year
,fk_department_code
,fk_project_code
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.fk_project_code, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.fk_project_code != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, fk_project_code


--Fridim1

DELETE FROM tco_filter_free_dim_1 where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_free_dim_1 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_1
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_1, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_1 != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_1


--Fridim2

DELETE FROM tco_filter_free_dim_2 where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_free_dim_2 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_2
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_2, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_2 != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_2

--Fridim3

DELETE FROM tco_filter_free_dim_3 where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_free_dim_3 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_3
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_3, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_3 != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_3

--Fridim4

DELETE FROM tco_filter_free_dim_4 where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_free_dim_4 (
fk_tenant_id
,budget_year
,fk_department_code
,free_dim_4
,updated
,updated_by)
SELECT a.fk_tenant_id, a.budget_year, a.department_code, a.free_dim_4, GETDATE() AS updated, @user_id AS updated_by
FROM #hlptab_1 a
JOIN tco_org_hierarchy b ON a.fk_tenant_id = b.fk_tenant_id AND a.department_code = b.fk_department_code
WHERE a.free_dim_4 != ''
AND a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, budget_year, department_code, free_dim_4

--account level filter

DELETE FROM tco_filter_account_levels where fk_tenant_id = @fk_tenant_id and budget_year = @budget_year

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 1, b.level_1_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_1_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 2, b.level_2_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_2_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 3, b.level_3_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_3_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 4, b.level_4_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_4_id

INSERT INTO tco_filter_account_levels (fk_tenant_id, [level], account_level_id, budget_year)
SELECT a.fk_tenant_id, 5, b.level_5_id, a.budget_year
FROM #hlptab_1 a JOIN tmd_reporting_line b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_account_code = b.fk_account_code
WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.budget_year, b.level_5_id

END

DROP TABLE IF EXISTS #hlptab_1

GO