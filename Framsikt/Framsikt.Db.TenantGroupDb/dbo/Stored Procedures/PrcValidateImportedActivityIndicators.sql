CREATE OR ALTER PROCEDURE [dbo].[PrcValidateImportedActivityIndicators]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@isServiceIdSetup bit,
	@serviceLevel int,
	@org_version NVARCHAR(50)
AS
		--Clear all the error information--
	UPDATE tco_stage_activity_indicators_import
	SET 
	framsikt_reference_error = 0,
	external_reference_error = 0 ,
	measurment_criteria_error = 0,
	value_type_error = 0,
	frequency_error = 0,
	frequency_str_error = 0,
	aggregation_activity_reporting_error = 0,
	aggregation_activity_reporting_str_error = 0,
	org_level_error = 0,
	org_level_str_error = 0,
	org_id_error = 0,
	service_id_error = 0,
	period_1_error = 0,
	period_2_error = 0,
	period_3_error = 0,
	period_4_error  = 0,
	period_5_error  = 0,
	period_6_error = 0,
	period_7_error = 0,
	period_8_error = 0,
	period_9_error = 0,
	period_10_error = 0,
	period_11_error = 0,
	period_12_error = 0,
	error_count = 0
	WHERE tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
		tco_stage_activity_indicators_import.budget_year = @budget_year AND 
		tco_stage_activity_indicators_import.user_id = @user_id;

	--update org_level from org_level_str
	update tco_stage_activity_indicators_import set org_level = org_level_str
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	TRY_PARSE(tco_stage_activity_indicators_import.org_level_str as int) is not null;

	--Validate org_level_str
	update tco_stage_activity_indicators_import set org_level_str_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	(
		(TRY_PARSE(tco_stage_activity_indicators_import.org_level_str as int) is null)
	);

	--update aggregation_activity_reporting from aggregation_activity_reporting_str
	update tco_stage_activity_indicators_import set aggregation_activity_reporting = aggregation_activity_reporting_str
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	TRY_PARSE(tco_stage_activity_indicators_import.aggregation_activity_reporting_str as int) is not null;

	--Validate aggregation_activity_reporting_str
	update tco_stage_activity_indicators_import set aggregation_activity_reporting_str_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	(
		(tco_stage_activity_indicators_import.aggregation_activity_reporting_str not in ('1', '2', '3'))
		or
		(TRY_PARSE(tco_stage_activity_indicators_import.aggregation_activity_reporting_str as int) is null)
	);

	--Validate measurement_criteria
	update tco_stage_activity_indicators_import set measurment_criteria_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	(tco_stage_activity_indicators_import.measurment_criteria is null or tco_stage_activity_indicators_import.measurment_criteria = '');

	--Validate Value Type
	update tco_stage_activity_indicators_import set value_type_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	value_type not in ('n0','##0.0 \%','n1','n2','n3','text')

	--update frequency from frequency_str
	update tco_stage_activity_indicators_import set frequency = frequency_str
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	TRY_PARSE(tco_stage_activity_indicators_import.frequency_str as int) is not null;

	--Validate Frequency_str
	update tco_stage_activity_indicators_import set frequency_str_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	(
		(frequency_str not in ('0', '1', '3', '4', '6', '12'))
		or
		(TRY_PARSE(tco_stage_activity_indicators_import.frequency_str as int) is null)
	);

	--Validate aggregation_activity_reporting
	update tco_stage_activity_indicators_import set aggregation_activity_reporting_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	aggregation_activity_reporting not in (1, 2, 3) and
	tco_stage_activity_indicators_import.aggregation_activity_reporting_str_error = 0;

	--Validate OrgLevel
	update tco_stage_activity_indicators_import set org_level_str_error = 1, 
													error_count = error_count + 1
	from tco_stage_activity_indicators_import
	left outer join tco_org_level on 
								tco_stage_activity_indicators_import.fk_tenant_id = tco_org_level.fk_tenant_id and
								tco_stage_activity_indicators_import.org_level = tco_org_level.org_level  
	left join tco_org_version on
								tco_org_level.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version = @org_version
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_org_level.org_level is null and
	tco_stage_activity_indicators_import.org_level_str_error = 0;

	--Validate OrgId from tco_org_hierarchy.org_id_1 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_activity_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_activity_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_activity_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_activity_indicators_import.org_id = tco_org_hierarchy.org_id_1 and
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.org_level = 1 and
	tco_stage_activity_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_1  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_2 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_activity_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_activity_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_activity_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_activity_indicators_import.org_id = tco_org_hierarchy.org_id_2 and
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.org_level = 2 and
	tco_stage_activity_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_2  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_3 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_activity_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_activity_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_activity_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_activity_indicators_import.org_id = tco_org_hierarchy.org_id_3 and
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.org_level = 3 and
	tco_stage_activity_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_3  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_4 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_activity_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_activity_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_activity_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_activity_indicators_import.org_id = tco_org_hierarchy.org_id_4 and
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.org_level = 4 and
	tco_stage_activity_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_4  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_5 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_activity_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_activity_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_activity_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_activity_indicators_import.org_id = tco_org_hierarchy.org_id_5 and
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.org_level = 5 and
	tco_stage_activity_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_5  is null;

	if(@isServiceIdSetup = 1)
	begin
		if(@serviceLevel = 1)
		begin
			--Validate service_id from tco_service_values.service_id_1 if @serviceLevel = 1
			update tco_stage_activity_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_activity_indicators_import
			left outer join tco_service_values on 
												tco_stage_activity_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_activity_indicators_import.service_id = tco_service_values.service_id_1
			where
			tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_activity_indicators_import.budget_year = @budget_year AND 
			tco_stage_activity_indicators_import.user_id = @user_id and
			tco_stage_activity_indicators_import.service_id <> '' and
			tco_service_values.service_id_1  is null;
		end
		else if(@serviceLevel = 2)
		begin
			--Validate service_id from tco_service_values.service_id_2 if @serviceLevel = 2
			update tco_stage_activity_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_activity_indicators_import
			left outer join tco_service_values on 
												tco_stage_activity_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_activity_indicators_import.service_id = tco_service_values.service_id_2
			where
			tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_activity_indicators_import.budget_year = @budget_year AND 
			tco_stage_activity_indicators_import.user_id = @user_id and
			tco_stage_activity_indicators_import.service_id <> '' and
			tco_service_values.service_id_2  is null;
		end
		else if(@serviceLevel = 3)
		begin
			--Validate service_id from tco_service_values.service_id_1 if @serviceLevel = 1
			update tco_stage_activity_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_activity_indicators_import
			left outer join tco_service_values on 
												tco_stage_activity_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_activity_indicators_import.service_id = tco_service_values.service_id_3
			where
			tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_activity_indicators_import.budget_year = @budget_year AND 
			tco_stage_activity_indicators_import.user_id = @user_id and
			tco_stage_activity_indicators_import.service_id <> '' and
			tco_service_values.service_id_3  is null;
		end
		else if(@serviceLevel = 4)
		begin
			--Validate service_id from tco_service_values.service_id_4
			update tco_stage_activity_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_activity_indicators_import
			left outer join tco_service_values on 
												tco_stage_activity_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_activity_indicators_import.service_id = tco_service_values.service_id_4
			where
			tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_activity_indicators_import.budget_year = @budget_year AND 
			tco_stage_activity_indicators_import.user_id = @user_id and
			tco_stage_activity_indicators_import.service_id <> '' and
			tco_service_values.service_id_4  is null;
		end
		else if(@serviceLevel = 5)
		begin
			--Validate service_id from tco_service_values.service_id_5
			update tco_stage_activity_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_activity_indicators_import
			left outer join tco_service_values on 
												tco_stage_activity_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_activity_indicators_import.service_id = tco_service_values.service_id_5
			where
			tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_activity_indicators_import.budget_year = @budget_year AND 
			tco_stage_activity_indicators_import.user_id = @user_id and
			tco_stage_activity_indicators_import.service_id <> '' and
			tco_service_values.service_id_5  is null;
		end
		
	end
	else
	begin
		update tco_stage_activity_indicators_import set service_id = ''
		from tco_stage_activity_indicators_import
		where
		tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
		tco_stage_activity_indicators_import.budget_year = @budget_year AND 
		tco_stage_activity_indicators_import.user_id = @user_id;
	end

	-- validate period_1
	update tco_stage_activity_indicators_import set period_1_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_1 is not null and tco_stage_activity_indicators_import.period_1 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_1) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_1 as decimal) is null)		 		
	)

	-- validate period_2
	update tco_stage_activity_indicators_import set period_2_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_2 is not null and tco_stage_activity_indicators_import.period_2 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_2) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_2 as decimal) is null)		 		
	)

	-- validate period_3
	update tco_stage_activity_indicators_import set period_3_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_3 is not null and tco_stage_activity_indicators_import.period_3 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_3) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_3 as decimal) is null)		 		
	)

	-- validate period_4
	update tco_stage_activity_indicators_import set period_4_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_4 is not null and tco_stage_activity_indicators_import.period_4 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_4) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_4 as decimal) is null)		 		
	)

	-- validate period_5
	update tco_stage_activity_indicators_import set period_5_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_5 is not null and tco_stage_activity_indicators_import.period_5 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_5) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_5 as decimal) is null)		 		
	)

	-- validate period_6
	update tco_stage_activity_indicators_import set period_6_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_6 is not null and tco_stage_activity_indicators_import.period_6 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_6) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_6 as decimal) is null)		 		
	)

	-- validate period_7
	update tco_stage_activity_indicators_import set period_7_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_7 is not null and tco_stage_activity_indicators_import.period_7 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_7) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_7 as decimal) is null)		 		
	)

	-- validate period_8
	update tco_stage_activity_indicators_import set period_8_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_8 is not null and tco_stage_activity_indicators_import.period_8 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_8) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_8 as decimal) is null)		 		
	)


	-- validate period_9
	update tco_stage_activity_indicators_import set period_9_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_9 is not null and tco_stage_activity_indicators_import.period_9 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_9) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_9 as decimal) is null)		 		
	)

	-- validate period_10
	update tco_stage_activity_indicators_import set period_10_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_10 is not null and tco_stage_activity_indicators_import.period_10 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_10) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_10 as decimal) is null)		 		
	)


	-- validate period_11
	update tco_stage_activity_indicators_import set period_11_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_11 is not null and tco_stage_activity_indicators_import.period_11 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_11) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_11 as decimal) is null)		 		
	)

	-- validate period_12
	update tco_stage_activity_indicators_import set period_12_error = 1, error_count = error_count + 1
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	(tco_stage_activity_indicators_import.period_12 is not null and tco_stage_activity_indicators_import.period_12 <> '') and
	tco_stage_activity_indicators_import.user_id = @user_id and 
	(		
		(tco_stage_activity_indicators_import.value_type = 'text' and len(tco_stage_activity_indicators_import.period_12) > 50)
		or
		(tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_activity_indicators_import.period_12 as decimal) is null)		 		
	)

RETURN 0
