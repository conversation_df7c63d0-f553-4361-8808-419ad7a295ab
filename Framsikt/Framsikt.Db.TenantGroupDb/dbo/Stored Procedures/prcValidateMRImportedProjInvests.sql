CREATE OR ALTER PROCEDURE [dbo].[prcValidateMRImportedProjInvests]  
 @tenant_id int,  
 @user_id int,  
 @budget_year int ,  
 @forecast_period int ,  
 @orgId varchar(10),  
 @org_version NVARCHAR(50)  
AS    
  
 SELECT  level_id,level,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,  
 est_finish_quarter,status,statuskvalitet,finstatus,risk,status_desc,user_id,tenant_id,forecast_period,sum(totalForecastChange)totalForecastChange, sum(annualForecastChange)annualForecastChange,sum(year2ForecastChange)year2ForecastChange,sum(year3ForecastChange)year3ForecastChange,sum(year4ForecastChange)year4ForecastChange,approval_ref_url,approval_reference 
 into #temp   from tmr_stage_projectinvfcast_import where tenant_id=@tenant_id AND [user_id] = @user_id AND forecast_period = @forecast_period  
 group by level_id,level,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,  
 est_finish_quarter,status,statuskvalitet,finstatus,risk,status_desc,user_id,tenant_id,forecast_period ,approval_ref_url,approval_reference 
  
 DELETE FROM tmr_stage_projectinvfcast_import where tenant_id=@tenant_id AND [user_id] = @user_id AND forecast_period = @forecast_period  
  
 INSERT tmr_stage_projectinvfcast_import (level_id,level,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,  
 est_finish_quarter,status,statuskvalitet,finstatus,risk,status_desc,user_id,tenant_id,forecast_period,totalForecastChange,annualForecastChange,year2ForecastChange,year3ForecastChange,year4ForecastChange,approval_ref_url,approval_reference )  
 SELECT  level_id,level,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,  
 est_finish_quarter,status,statuskvalitet,finstatus,risk,status_desc,user_id,tenant_id,forecast_period,totalForecastChange,annualForecastChange,year2ForecastChange,year3ForecastChange,year4ForecastChange,approval_ref_url,approval_reference   
 from #temp  
  
 DROP TABLE #temp  
  
  
 --Clear all the error information   
 UPDATE tmr_stage_projectinvfcast_import  
 SET level_id_error=0,  
 account_code_error=0,  
 department_code_error=0,  
 function_code_error=0,  
 project_code_error=0,  
 free_dim_1_error=0,  
 free_dim_2_error=0,  
 free_dim_3_error=0,  
 free_dim_4_error=0,  
 est_finish_quarter_error=0,  
 status_error=0,  
 finstatus_error=0,
 statuskvalitet_error = 0,
 statusDesc_error=0,  
 risk_error=0,  
 error_count=0,  
 altercode_error=0  
 WHERE tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id;  
  
 DECLARE  @Risks Table( RiskID int)  
 INSERT @Risks(RiskID)  
 SELECT 1 UNION  SELECT 2 UNION SELECT 3   

 
/* DATA FIXES ADDED BY SIMON STARTS */

--Fix est finish quarter if no data is coming from excel:
update PT set est_finish_quarter = CONVERT(NVARCHAR(2),DATEPART(DAY,MP.completion_date))+'.'+ CONVERT(NVARCHAR(2),DATEPART(MONTH,MP.completion_date))+'.'+ CONVERT(NVARCHAR(4),DATEPART(YEAR,MP.completion_date))
from tmr_stage_projectinvfcast_import PT
JOIN tco_projects P on PT.tenant_id = P.fk_tenant_id and PT.project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and est_finish_quarter = ''

--Blank risk, status and desc if level not supplied
update tmr_stage_projectinvfcast_import set status = '', statuskvalitet = '', finstatus = '', risk = '', status_desc = ''
where user_id = @user_id
and tenant_id = @tenant_id
and forecast_period = @forecast_period
and level = ''  

--Fix level id when level = MP
update PT set level_id = MP.pk_main_project_code
from tmr_stage_projectinvfcast_import PT
JOIN tco_projects P on PT.tenant_id = P.fk_tenant_id and PT.project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = 'MP'

--Fix level id when level = P
update PT set level_id = P.pk_project_code
from tmr_stage_projectinvfcast_import PT
JOIN tco_projects P on PT.tenant_id = P.fk_tenant_id and PT.project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = 'p'

--Fix level id when level = 1
update PT set level_id = PH.proj_gr_1
from tmr_stage_projectinvfcast_import PT
JOIN tco_proj_version PV ON PT.tenant_id = PV.fk_tenant_id and @budget_year between PV.period_from and PV.period_to
JOIN tco_proj_hierarchy PH ON PT.tenant_id = PH.fk_tenant_id and PT.project_code = PH.fk_project_code and PV.pk_proj_version = PH.fk_proj_version
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = '1'

--Fix level id when level = 2
update PT set level_id = PH.proj_gr_2
from tmr_stage_projectinvfcast_import PT
JOIN tco_proj_version PV ON PT.tenant_id = PV.fk_tenant_id and @budget_year between PV.period_from and PV.period_to
JOIN tco_proj_hierarchy PH ON PT.tenant_id = PH.fk_tenant_id and PT.project_code = PH.fk_project_code and PV.pk_proj_version = PH.fk_proj_version
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = '2'

--Fix level id when level = 3
update PT set level_id = PH.proj_gr_3
from tmr_stage_projectinvfcast_import PT
JOIN tco_proj_version PV ON PT.tenant_id = PV.fk_tenant_id and @budget_year between PV.period_from and PV.period_to
JOIN tco_proj_hierarchy PH ON PT.tenant_id = PH.fk_tenant_id and PT.project_code = PH.fk_project_code and PV.pk_proj_version = PH.fk_proj_version
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = '3'

--Fix level id when level = 4
update PT set level_id = PH.proj_gr_4
from tmr_stage_projectinvfcast_import PT
JOIN tco_proj_version PV ON PT.tenant_id = PV.fk_tenant_id and @budget_year between PV.period_from and PV.period_to
JOIN tco_proj_hierarchy PH ON PT.tenant_id = PH.fk_tenant_id and PT.project_code = PH.fk_project_code and PV.pk_proj_version = PH.fk_proj_version
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = '4'

--Fix level id when level = 5
update PT set level_id = PH.proj_gr_5
from tmr_stage_projectinvfcast_import PT
JOIN tco_proj_version PV ON PT.tenant_id = PV.fk_tenant_id and @budget_year between PV.period_from and PV.period_to
JOIN tco_proj_hierarchy PH ON PT.tenant_id = PH.fk_tenant_id and PT.project_code = PH.fk_project_code and PV.pk_proj_version = PH.fk_proj_version
where user_id = @user_id
and tenant_id = @tenant_id
and PT.forecast_period = @forecast_period
and level = '5'


--Fix status if level is supplied in the excel but no status, fetches then from the current status
update a set status = b.status
from tmr_stage_projectinvfcast_import a
JOIN tmr_proj_status b on a.tenant_id = b.fk_tenant_id and a.forecast_period = b.forecast_period and a.level = b.level and a.level_id = b.level_id
where a.user_id = @user_id
and a.tenant_id = @tenant_id
and a.forecast_period = @forecast_period
and a.status = 0

--Fix risk if level is supplied in the excel but no status, fetches then from the current status
update a set risk = b.risk
from tmr_stage_projectinvfcast_import a
JOIN tmr_proj_status b on a.tenant_id = b.fk_tenant_id and a.forecast_period = b.forecast_period and a.level = b.level and a.level_id = b.level_id
where a.user_id = @user_id
and a.tenant_id = @tenant_id
and a.forecast_period = @forecast_period
and a.risk = 0

--Fix status_desc if level is supplied in the excel but no status, fetches then from the current status
update a set status_desc = b.status_desc
from tmr_stage_projectinvfcast_import a
JOIN tmr_proj_status b on a.tenant_id = b.fk_tenant_id and a.forecast_period = b.forecast_period and a.level = b.level and a.level_id = b.level_id
where a.user_id = @user_id
and a.tenant_id = @tenant_id
and a.forecast_period = @forecast_period
and a.status_desc = ''

/* DATA FIXES ADDED BY SIMON ENDS */

/* #166235 */

--Fix statuskvalitet if level is supplied in the excel but no status, fetches then from the current status
update a set statuskvalitet = b.quality
from tmr_stage_projectinvfcast_import a
JOIN tmr_proj_status b on a.tenant_id = b.fk_tenant_id and a.forecast_period = b.forecast_period and a.level = b.level and a.level_id = b.level_id
where a.user_id = @user_id
and a.tenant_id = @tenant_id
and a.forecast_period = @forecast_period
and a.statuskvalitet = 0

--Fix finstatus if level is supplied in the excel but no status, fetches then from the current status
update a set finstatus = b.fin_status
from tmr_stage_projectinvfcast_import a
JOIN tmr_proj_status b on a.tenant_id = b.fk_tenant_id and a.forecast_period = b.forecast_period and a.level = b.level and a.level_id = b.level_id
where a.user_id = @user_id
and a.tenant_id = @tenant_id
and a.forecast_period = @forecast_period
and a.finstatus = 0

/* #166235 */

   -- Validate Project Code based on Invalid level_id  
 UPDATE tmr_stage_projectinvfcast_import  
 SET project_code_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import      
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id AND (tmr_stage_projectinvfcast_import.[level_id] = 'INVALID_LEVEL' AND (tmr_stage_projectinvfcast_import.[level] = 'MP' OR tmr_stage_projectinvfcast_import.[level] = 'P'));  

    -- Validate level based on invalid level_id  when level is not 'MP' or 'P'
 UPDATE tmr_stage_projectinvfcast_import  
 SET level_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import      
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id AND (tmr_stage_projectinvfcast_import.[level_id] = 'INVALID_LEVEL' AND (tmr_stage_projectinvfcast_import.[level] <> 'MP' AND tmr_stage_projectinvfcast_import.[level] <> 'P')); 

  UPDATE tmr_stage_projectinvfcast_import  
 SET level_error = 1, error_count = error_count + 1  
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id
 AND tmr_stage_projectinvfcast_import.level_error = 0
 AND tmr_stage_projectinvfcast_import.level_id  = 'EMPTY_LEVEL'
AND (tmr_stage_projectinvfcast_import.[level] IS NULL OR tmr_stage_projectinvfcast_import.[level] NOT IN ('P', 'MP', '1', '2', '3', '4', '5'))

 -- Validate Alter Code  
 UPDATE tmr_stage_projectinvfcast_import  
 SET altercode_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import imp  
 LEFT JOIN (
            select a.fk_tenant_id,pk_alter_code  
            from tco_fp_alter_codes a
            JOIN tco_relation_values b on a.fk_tenant_id = b.fk_tenant_id and a.pk_alter_code BETWEEN b.relation_value_from and b.relation_value_to
            where a.fk_tenant_id = @tenant_id
            and b.attribute_type = 'ACTIONTYPE'
            and b.attribute_value = '1000'
            and a.status = 1
            ) a ON imp.tenant_id = a.fk_tenant_id and imp.alter_code = a.pk_alter_code
   WHERE  IMP.tenant_id = @tenant_id AND   
 IMP.forecast_period = @forecast_period AND   
 IMP.[user_id] = @user_id  AND
  (a.[pk_alter_code] is NULL OR IMP.alter_code is null OR IMP.alter_code ='');  
  
 -- Validate Status  
 UPDATE tmr_stage_projectinvfcast_import  
 SET status_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_progress_status acc on acc.fk_tenant_id = tmr_stage_projectinvfcast_import.tenant_id    AND acc.status_id = tmr_stage_projectinvfcast_import.[status]  AND acc.[type]='MONTHREP_INV'  
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id AND acc.[status_id] is NULL AND tmr_stage_projectinvfcast_import.[status] <> 0;  

 -- Validate FinStatus  
 UPDATE tmr_stage_projectinvfcast_import  
 SET finstatus_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_progress_status acc on acc.fk_tenant_id = tmr_stage_projectinvfcast_import.tenant_id    AND acc.status_id = tmr_stage_projectinvfcast_import.[finstatus]  AND acc.[type]='MONTHREP_INV_FIN_STATUS'  
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id AND acc.[status_id] is NULL AND tmr_stage_projectinvfcast_import.[finstatus] <> 0;  
 
  -- Validate Quality  
 UPDATE tmr_stage_projectinvfcast_import  
 SET statuskvalitet_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_progress_status acc on acc.fk_tenant_id = tmr_stage_projectinvfcast_import.tenant_id    AND acc.status_id = tmr_stage_projectinvfcast_import.[statuskvalitet]  AND acc.[type]='MONTHREP_INV_QUALITY'  
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id AND acc.[status_id] is NULL AND tmr_stage_projectinvfcast_import.[statuskvalitet] <> 0;  

 -- Validate RISKS
 --UPDATE tmr_stage_projectinvfcast_import  
 --SET risk = NULL   
 --FROM tmr_stage_projectinvfcast_import      
 --WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.[user_id] = @user_id AND tmr_stage_projectinvfcast_import.[risk] = 0
 
 --UPDATE tmr_stage_projectinvfcast_import  
 --SET risk_error = 1, error_count = error_count + 1   
 --FROM tmr_stage_projectinvfcast_import   
 ----LEFT JOIN @Risks acc on acc.RiskID = tmr_stage_projectinvfcast_import.risk     
 --WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.[user_id] = @user_id AND (tmr_stage_projectinvfcast_import.[risk] <> 0 AND tmr_stage_projectinvfcast_import.[risk] <> 1 AND tmr_stage_projectinvfcast_import.[risk] <> 2 AND tmr_stage_projectinvfcast_import.[risk] <> 3);  

  UPDATE tmr_stage_projectinvfcast_import  
 SET risk_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_progress_status acc on acc.fk_tenant_id = tmr_stage_projectinvfcast_import.tenant_id    AND acc.status_id = tmr_stage_projectinvfcast_import.[risk]  AND acc.[type]='MONTHREP_INV_RISK'  
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.[user_id] = @user_id AND acc.[status_id] is NULL AND tmr_stage_projectinvfcast_import.[risk] <> 0; 
  
  
 --Validate account codes--  
 UPDATE tmr_stage_projectinvfcast_import  
 SET account_code_error = 1, error_count = error_count + 1   
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_accounts acc on acc.pk_account_code = tmr_stage_projectinvfcast_import.account_code AND   
  tmr_stage_projectinvfcast_import.tenant_id = acc.pk_tenant_id AND   
  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.[user_id] = @user_id   
 WHERE acc.pk_account_code IS NULL;  
  
    
  
 --Validate department codes --  
  
   
  
 IF (@orgId = '')  
 BEGIN  
  UPDATE tmr_stage_projectinvfcast_import  
  SET department_code_error = 1, error_count = error_count + 1  
  FROM tmr_stage_projectinvfcast_import   
  LEFT JOIN (select distinct fk_department_code as department_code from tco_org_hierarchy where fk_tenant_id = @tenant_id and  fk_org_version in (@org_version))  
     DEPTS ON DEPTS.department_code = tmr_stage_projectinvfcast_import.department_code WHERE   
   tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
   tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
   tmr_stage_projectinvfcast_import.[user_id] = @user_id   
  AND DEPTS.department_code IS NULL;  
 END  
 ELSE  
 BEGIN  
  select distinct fk_department_code from tco_org_hierarchy where fk_tenant_id = @tenant_id and  fk_org_version in (@org_version)  
  and org_id_2 =@orgId  
  
  UPDATE tmr_stage_projectinvfcast_import  
  SET department_code_error = 1, error_count = error_count + 1  
  FROM tmr_stage_projectinvfcast_import   
  LEFT JOIN (select distinct fk_department_code as department_code from tco_org_hierarchy where fk_tenant_id = @tenant_id and  fk_org_version in (@org_version)  
  and org_id_2 =@orgId) DEPTS ON DEPTS.department_code = tmr_stage_projectinvfcast_import.department_code WHERE   
   tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
   tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
   tmr_stage_projectinvfcast_import.[user_id] = @user_id   
  AND DEPTS.department_code IS NULL;  
 END   
   
 --Validate Functions--  
 UPDATE tmr_stage_projectinvfcast_import  
 SET function_code_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_functions fn on fn.pk_Function_code = tmr_stage_projectinvfcast_import.function_code AND   
  tmr_stage_projectinvfcast_import.tenant_id = fn.pk_tenant_id AND   
  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.[user_id] = @user_id   
 WHERE fn.pk_Function_code IS NULL;  
  
 --Validate Projects--  
  
 UPDATE tmr_stage_projectinvfcast_import  
 SET project_code_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_projects prj on prj.pk_project_code = tmr_stage_projectinvfcast_import.project_code AND   
  tmr_stage_projectinvfcast_import.tenant_id = prj.fk_tenant_id    
 WHERE prj.pk_project_code IS NULL  
 AND tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.user_id = @user_id    
  --AND tmr_stage_projectinvfcast_import.project_code != ''    #119979
  --AND tmr_stage_projectinvfcast_import.project_code IS NULL    
  
  
 -- Validate free dim 1 --  
 UPDATE tmr_stage_projectinvfcast_import  
 SET free_dim_1_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_projectinvfcast_import.free_dim_1 AND   
  tmr_stage_projectinvfcast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'  
 WHERE frdm.free_dim_column IS NULL  
 AND tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.user_id = @user_id    
  AND tmr_stage_projectinvfcast_import.free_dim_1 != ''   
  AND tmr_stage_projectinvfcast_import.free_dim_1 IS NOT NULL;    
  
 -- Validate free dim 2 --  
 UPDATE tmr_stage_projectinvfcast_import  
 SET free_dim_2_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_projectinvfcast_import.free_dim_2 AND   
  tmr_stage_projectinvfcast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'  
 WHERE frdm.free_dim_column IS NULL  
 AND tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.user_id = @user_id     
  AND tmr_stage_projectinvfcast_import.free_dim_2 != ''   
  AND tmr_stage_projectinvfcast_import.free_dim_2 IS NOT NULL;    
  
 -- Validate free dim 3 --  
 UPDATE tmr_stage_projectinvfcast_import  
 SET free_dim_3_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_projectinvfcast_import.free_dim_3 AND   
  tmr_stage_projectinvfcast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'  
 WHERE frdm.free_dim_column IS NULL  
 AND tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.user_id = @user_id     
  AND tmr_stage_projectinvfcast_import.free_dim_3 != ''   
  AND tmr_stage_projectinvfcast_import.free_dim_3 IS NOT NULL;    
  
 -- Validate free dim 4 --  
 UPDATE tmr_stage_projectinvfcast_import  
 SET free_dim_4_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_projectinvfcast_import.free_dim_4 AND   
  tmr_stage_projectinvfcast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'  
 WHERE frdm.free_dim_column IS NULL  
 AND tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
  tmr_stage_projectinvfcast_import.user_id = @user_id     
  AND tmr_stage_projectinvfcast_import.free_dim_4 != ''   
  AND tmr_stage_projectinvfcast_import.free_dim_4 IS NOT NULL;   
  
 -- Insert missing investments in budget_year_config (hotfix - BUG 49291)  
  
 --DROP TABLE IF EXISTS #missing_budget_year_config  
 --DROP TABLE IF EXISTS #last_budget_year  
  
 --SELECT DISTINCT tc.fk_tenant_id,tc.fk_level_id,max(tc.budget_year) as budget_year  
 --into #last_budget_year from tco_investments i  
 --LEFT join tco_inv_budgetyear_config tc on i.pk_level_id=tc.fk_level_id and i.fk_tenant_id=tc.fk_tenant_id  
 --JOIN tmr_stage_projectinvfcast_import imp ON imp.level_id = i.pk_level_id AND imp.tenant_id = i.fk_tenant_id AND imp.tenant_id = @tenant_id AND  imp.forecast_period = @forecast_period AND  imp.[user_id] = @user_id   
 --WHERE i.fk_tenant_id =@tenant_id and  tc.budget_year < @budget_year   
 --GROUP BY tc.fk_tenant_id,tc.fk_level_id  
  
  
 --SELECT DISTINCT tc.fk_tenant_id,tc.fk_level_id,@budget_year as budget_year,tc.inv_status,tc.priority,getdate() as updated,@user_id as updated_by,tc.level,tc.completion_date,tc.fk_investment_phase_id,tc.fk_org_id,tc.org_name   
 --into #missing_budget_year_config from tco_investments i  
 --JOIN tco_inv_budgetyear_config tc on i.pk_level_id=tc.fk_level_id and i.fk_tenant_id=tc.fk_tenant_id  
 --JOIN #last_budget_year ly ON ly.fk_level_id = i.pk_level_id AND ly.fk_tenant_id = i.fk_tenant_id AND ly.budget_year = tc.budget_year  
  
 --DELETE tmp FROM #missing_budget_year_config tmp  
 --JOIN tco_inv_budgetyear_config tc ON tmp.budget_year = tc.budget_year AND tmp.fk_level_id = tc.fk_level_id AND tmp.fk_tenant_id = tc.fk_tenant_id  
   
 --INSERT INTO tco_inv_budgetyear_config (fk_tenant_id,fk_level_id,budget_year,inv_status,priority,updated,updated_by,level,completion_date,fk_investment_phase_id,fk_org_id,org_name)  
 --SELECT fk_tenant_id,fk_level_id,budget_year,inv_status,priority,updated,updated_by,level,completion_date,fk_investment_phase_id,fk_org_id,org_name  
 --FROM #missing_budget_year_config tmp  
  
 -- Validate InvestmentID's   
  
 --SELECT DISTINCT i.pk_level_id,tc.level into #masterInvestments from tco_investments i  
 --LEFT join tco_inv_budgetyear_config tc on i.pk_level_id=tc.fk_level_id and i.fk_tenant_id=tc.fk_tenant_id  
 --WHERE i.fk_tenant_id =@tenant_id and  tc.budget_year =@budget_year   
  
 --UPDATE tmr_stage_projectinvfcast_import  
 --SET level_id_error = 1, error_count = error_count + 1  
 --WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.user_id = @user_id     
 --AND tmr_stage_projectinvfcast_import.level_id  = ''   
 --AND tmr_stage_projectinvfcast_import.level_id IS NULL;   
  
 --UPDATE tmr_stage_projectinvfcast_import  
 --SET level_id_error = 1, error_count = error_count + 1  
 --FROM tmr_stage_projectinvfcast_import   
 --LEFT JOIN (SELECT DISTINCT pk_level_id from #masterInvestments ) frdm on frdm.pk_level_id = tmr_stage_projectinvfcast_import.level_id    
 --WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.user_id = @user_id AND frdm.pk_level_id is NULL;  
  
 --UPDATE tmr_stage_projectinvfcast_import  
 --SET level = frdm.level  
 --FROM tmr_stage_projectinvfcast_import   
 --LEFT JOIN (SELECT   pk_level_id,level  from #masterInvestments) frdm on frdm.pk_level_id = tmr_stage_projectinvfcast_import.level_id    
 --WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.user_id = @user_id     
 --AND tmr_stage_projectinvfcast_import.level_id != ''   
 --AND tmr_stage_projectinvfcast_import.level_id IS NOT NULL;   
  
 --DROP TABLE #masterInvestments;  
    
 -- Validate totalForecastChange  
  
 UPDATE tmr_stage_projectinvfcast_import  
 SET totalForecastChange = 0   
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND tmr_stage_projectinvfcast_import.totalForecastChange  = ''   
 AND tmr_stage_projectinvfcast_import.totalForecastChange IS NULL;   
    
 -- Validate annualForecastChange  
  
 UPDATE tmr_stage_projectinvfcast_import  
 SET annualForecastChange = 0   
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND tmr_stage_projectinvfcast_import.annualForecastChange  = ''   
 AND tmr_stage_projectinvfcast_import.annualForecastChange IS NULL;   

  -- Validate year2ForecastChange   
 UPDATE tmr_stage_projectinvfcast_import  
 SET year2ForecastChange = 0   
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND tmr_stage_projectinvfcast_import.year2ForecastChange  = ''   
 AND tmr_stage_projectinvfcast_import.year2ForecastChange IS NULL; 
 
   -- Validate year3ForecastChange   
 UPDATE tmr_stage_projectinvfcast_import  
 SET year3ForecastChange = 0   
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND tmr_stage_projectinvfcast_import.year3ForecastChange  = ''   
 AND tmr_stage_projectinvfcast_import.year3ForecastChange IS NULL;  

   -- Validate year4ForecastChange   
 UPDATE tmr_stage_projectinvfcast_import  
 SET year4ForecastChange = 0   
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND tmr_stage_projectinvfcast_import.year4ForecastChange  = ''   
 AND tmr_stage_projectinvfcast_import.year4ForecastChange IS NULL;  
 
  -- Validate Estimated finished date  
  
 SELECT  level_id,Count(Distinct est_finish_quarter) estimatedquarter  into #estimatedquarter  from tmr_stage_projectinvfcast_import WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id  AND tmr_stage_projectinvfcast_import.est_finish_quarter <> '' AND tmr_stage_projectinvfcast_import.est_finish_quarter IS NOT NULL  
 GROUP by level_id  
 having Count(Distinct est_finish_quarter) > 1  
  
  
 UPDATE tmr_stage_projectinvfcast_import  
 SET est_finish_quarter_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN (SELECT DISTINCT level_id from #estimatedquarter ) frdm on frdm.level_id = tmr_stage_projectinvfcast_import.level_id    
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id AND frdm.level_id IS NOT NULL;   
  
 DROP TABLE #estimatedquarter  
   
 SET DATEFORMAT dmy  
 UPDATE tmr_stage_projectinvfcast_import set error_count =   
 CASE WHEN ISDATE(est_finish_quarter) = 0 THEN error_count +1  
   WHEN ISNUMERIC(est_finish_quarter) = 1 THEN error_count +1  
   --WHEN datepart(year,est_finish_quarter) < start_year THEN error_count+ 1  
   ELSE error_count + 0  
  END, est_finish_quarter_error =   
  CASE WHEN ISDATE(est_finish_quarter) = 0 THEN 1  
   WHEN ISNUMERIC(est_finish_quarter) = 1 THEN 1  
   --WHEN datepart(year,est_finish_quarter) < start_year THEN 1  
   ELSE 0  
   END  
  WHERE tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
   tmr_stage_projectinvfcast_import.user_id = @user_id AND  
   tmr_stage_projectinvfcast_import.forecast_period =@forecast_period  
  
  -- Validate Status  
  
 SELECT  level_id,Count(Distinct [status]) [status] into #Status  from tmr_stage_projectinvfcast_import WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id    
 GROUP by level_id  
 having Count(Distinct [status]) > 1  
  
  
 UPDATE tmr_stage_projectinvfcast_import  
 SET status_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN (SELECT DISTINCT level_id from #Status ) frdm on frdm.level_id = tmr_stage_projectinvfcast_import.level_id    
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND frdm.level_id IS NOT NULL;  
  
 DROP TABLE #Status  
  
  -- Validate Risks  
  
 SELECT  level_id,Count(Distinct risk)risk into #risk  from tmr_stage_projectinvfcast_import WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id    
 GROUP by level_id  
 having Count(Distinct risk) > 1  
  
  
 UPDATE tmr_stage_projectinvfcast_import  
 SET risk_error = 1, error_count = error_count + 1  
 FROM tmr_stage_projectinvfcast_import   
 LEFT JOIN (SELECT DISTINCT level_id from #risk ) frdm on frdm.level_id = tmr_stage_projectinvfcast_import.level_id    
 WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 tmr_stage_projectinvfcast_import.user_id = @user_id     
 AND frdm.level_id IS NOT NULL;   
  
  
 DROP TABLE #risk   
  
  
 ---- Validate Description  
  
 --SELECT  level_id,Count(Distinct status_desc)statusDesc into #statusDesc  from tmr_stage_projectinvfcast_import WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.user_id = @user_id  AND   tmr_stage_projectinvfcast_import.status_desc IS NOT NULL AND tmr_stage_projectinvfcast_import.status_desc <> ''   
 --GROUP by level_id  
 --having Count(Distinct status_desc) > 1  
  
  
 --UPDATE tmr_stage_projectinvfcast_import  
 --SET statusDesc_error = 1, error_count = error_count + 1  
 --FROM tmr_stage_projectinvfcast_import   
 --LEFT JOIN (SELECT DISTINCT level_id from #statusDesc ) frdm on frdm.level_id = tmr_stage_projectinvfcast_import.level_id    
 --WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --tmr_stage_projectinvfcast_import.user_id = @user_id     
 --AND frdm.level_id IS NOT NULL; ;   
  
  
 --DROP TABLE #statusDesc   
  
  
  
 --IF(@orgId <> '')  
 --BEGIN  
 -- -- Validate finished date, status,risk and status description if the investments id doesn't belong to the initial org id at which they were created  
 --  SELECT DISTINCT i.pk_level_id,tc.fk_org_id as [OrgID] into #InvOrgData from tco_investments i  
 --  LEFT join tco_inv_budgetyear_config tc on i.pk_level_id=tc.fk_level_id and i.fk_tenant_id=tc.fk_tenant_id  
 --  WHERE i.fk_tenant_id =@tenant_id and  tc.budget_year =@budget_year   
  
 --  UPDATE tmr_stage_projectinvfcast_import  
 --  SET orgID = frdm.OrgID  
 --  FROM tmr_stage_projectinvfcast_import   
 --  LEFT JOIN #InvOrgData frdm on frdm.pk_level_id = tmr_stage_projectinvfcast_import.level_id    
 --  WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --  tmr_stage_projectinvfcast_import.user_id = @user_id;   
  
 --  UPDATE tmr_stage_projectinvfcast_import  
 --  SET est_finish_quarter_error = 1,status_error = 1,statusDesc_error = 1,risk_error = 1, error_count = error_count + 4  
 --  FROM tmr_stage_projectinvfcast_import   
 --  LEFT JOIN (SELECT DISTINCT pk_level_id from #InvOrgData where OrgID <> @orgId ) frdm on frdm.pk_level_id = tmr_stage_projectinvfcast_import.level_id    
 --  WHERE  tmr_stage_projectinvfcast_import.tenant_id = @tenant_id AND   
 --  tmr_stage_projectinvfcast_import.forecast_period = @forecast_period AND   
 --  tmr_stage_projectinvfcast_import.user_id = @user_id AND frdm.pk_level_id IS NOT NULL;   
  
 --  DROP TABLE #InvOrgData  
  
 --END  
  
  
RETURN 0