
CREATE OR ALTER PROCEDURE [dbo].[proc_bud_original_control_V2]
@tenant_id INT,
@budget_year INT,
@batch_id varchar (12) = ''
AS

--DECLARE
--@tenant_id INT = 2092,
--@budget_year INT = 2023,
--@batch_id varchar (12) = '230207150734'

--Find erp type and check if valid
DECLARE @erp_type NVARCHAR(20) = (select erp_type from gco_tenants where pk_id = @tenant_id)

IF @erp_type NOT IN ('Agresso', 'Visma')
BEGIN
	PRINT 'ERP type not supported'
	RETURN
END

DECLARE @export_id INT = 1

--Set BatchId
	DECLARE @now datetime;
	SET @now = GETDATE();
	DECLARE @rolling_id nvarchar(2);
	SET @rolling_id = (SELECT CAST(RIGHT(ABS(CAST(CAST(NEWID() AS VARBINARY) AS INT)),2) AS VARCHAR(2)))
	SET @batch_id = CONCAT(CONVERT(VARCHAR,  @now, 12), RIGHT('000'+CAST(datepart(hour, @now) AS VARCHAR(2)),2), RIGHT('000'+CAST(datepart(minute, @now) AS VARCHAR(2)),2), @rolling_id)

DROP TABLE IF EXISTS #temp_budget_dimensions
CREATE TABLE #temp_budget_dimensions(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[export_id] [bigint] NOT NULL,
	[batch_id] [nvarchar](14) NOT NULL,
	[budget_type] [nvarchar](25) NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[amount] [decimal](18, 2) NOT NULL)

DROP TABLE IF EXISTS #account_list_1
CREATE TABLE #account_list_1 (pk_tenant_id INT, pk_Account_code NVARCHAR(25),[type] varchar(50))
--Fetch accounts to be included
INSERT INTO #account_list_1
SELECT b.pk_tenant_id, b.pk_account_code, c.[type]
FROM tco_accounts b
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code
WHERE @budget_year between DATEPART(YEAR,b.dateFrom) and DATEPART(YEAR,b.dateTo)
AND b.pk_tenant_id = @tenant_id
GROUP BY b.pk_tenant_id, b.pk_account_code, c.[type]


IF @erp_type = 'Visma'
BEGIN
	
	DROP TABLE IF EXISTS #export_dump_visma
	CREATE TABLE #export_dump_visma(
	[export_id] [bigint] NOT NULL,
	[fk_action_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[description] [nvarchar](700) NOT NULL,
	[company] [nvarchar](100) NOT NULL,
	[region] [nvarchar](100) NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[free_dim_5] [nvarchar](25) NOT NULL,
	[free_dim_6] [nvarchar](25) NOT NULL,
	[fk_adjustment_code] [nvarchar](25) NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[revised_budget] [decimal](18, 2) NOT NULL,
	[jan] [decimal](18, 2) NOT NULL,
	[feb] [decimal](18, 2) NOT NULL,
	[mar] [decimal](18, 2) NOT NULL,
	[apr] [decimal](18, 2) NOT NULL,
	[may] [decimal](18, 2) NOT NULL,
	[jun] [decimal](18, 2) NOT NULL,
	[jul] [decimal](18, 2) NOT NULL,
	[aug] [decimal](18, 2) NOT NULL,
	[sep] [decimal](18, 2) NOT NULL,
	[oct] [decimal](18, 2) NOT NULL,
	[nov] [decimal](18, 2) NOT NULL,
	[dec] [decimal](18, 2) NOT NULL)

	insert into #export_dump_visma
	EXEC [dbo].[proc_bud_visma_original]
	@tenant_id = @tenant_id,
	@budget_year = @budget_year,
	@export_id = @export_id

--Fetch grouped dimensions to be used in further controls
	INSERT INTO #temp_budget_dimensions
	([fk_tenant_id],export_id,batch_id,[budget_year],[budget_type],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code]
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,[amount])
	select [fk_tenant_id]
	,export_id
	,batch_id = @batch_id
	,[budget_year]
	,[budget_type] = b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,amount = SUM([jan]+[feb]+[mar]+[apr]+[may]+[jun]+[jul]+[aug]+[sep]+[oct]+[nov]+[dec])
	FROM #export_dump_visma a
	JOIN #account_list_1 b on a.fk_tenant_id = b.pk_tenant_id  AND a.fk_account_code = b.pk_account_code
	GROUP BY [fk_tenant_id],export_id,[budget_year]
	,b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]

END

IF @erp_type = 'Agresso'
BEGIN

	DROP TABLE IF EXISTS #export_dump_agresso
	CREATE TABLE #export_dump_agresso(
	[export_id] [bigint] NOT NULL,
	[fk_action_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[description] [nvarchar](700) NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[fk_adjustment_code] [nvarchar](25) NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[jan] [decimal](18, 2) NOT NULL,
	[feb] [decimal](18, 2) NOT NULL,
	[mar] [decimal](18, 2) NOT NULL,
	[apr] [decimal](18, 2) NOT NULL,
	[may] [decimal](18, 2) NOT NULL,
	[jun] [decimal](18, 2) NOT NULL,
	[jul] [decimal](18, 2) NOT NULL,
	[aug] [decimal](18, 2) NOT NULL,
	[sep] [decimal](18, 2) NOT NULL,
	[oct] [decimal](18, 2) NOT NULL,
	[nov] [decimal](18, 2) NOT NULL,
	[dec] [decimal](18, 2) NOT NULL)

	INSERT INTO #export_dump_agresso
	EXEC [dbo].[proc_bud_agresso_original]
	@tenant_id = @tenant_id,
	@budget_year = @budget_year,
	@export_id = @export_id


	--Fetch grouped dimensions to be used in further controls
	INSERT INTO #temp_budget_dimensions
	([fk_tenant_id],export_id,batch_id,[budget_year],[budget_type],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code]
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,[amount])
	select [fk_tenant_id]
	,export_id
	,batch_id = @batch_id
	,[budget_year]
	,[budget_type] = b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,amount = SUM([jan]+[feb]+[mar]+[apr]+[may]+[jun]+[jul]+[aug]+[sep]+[oct]+[nov]+[dec])
	FROM #export_dump_agresso a
	JOIN #account_list_1 b on a.fk_tenant_id = b.pk_tenant_id  AND a.fk_account_code = b.pk_account_code
	GROUP BY [fk_tenant_id],export_id,[budget_year]
	,b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]

END

/* DIMENSION CHECKS START - account check not needed because it's checked for in the SP that generates the data to send  */
DROP TABLE IF EXISTS #error_list
CREATE TABLE #error_list
(fk_tenant_id INT,
export_id INT,
batch_id VARCHAR(14),
error_type NVARCHAR(100),
dim_1_type NVARCHAR(50),
dim_2_type NVARCHAR(50),
dim_1_id NVARCHAR(50),
dim_2_id NVARCHAR(50),	
dim_to_change NVARCHAR(50),
[id_new] NVARCHAR(50))

INSERT INTO #error_list
SELECT 
fk_tenant_id
,export_id
,batch_id
,error_type
,dim_1_type
,dim_2_type
,dim_1_id
,dim_2_id	
,dim_to_change
,id_new = ''
FROM
(

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_TEMPORARY_CONTROL_SP'
	,dim_1_type = 'fk_project_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_project_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_project_code'
	from #temp_budget_dimensions a
	JOIN tco_projects b on a.fk_tenant_id = b.fk_tenant_id and a.fk_project_code = b.pk_project_code and a.budget_year BETWEEN DATEPART(YEAR,b.date_from) AND DATEPART(YEAR,b.date_to) and b.is_temp = 1
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_project_code
	
) error

--Check for 0 amount to be transferred

IF (SELECT SUM(amount) FROM #temp_budget_dimensions) != 0
BEGIN

	INSERT INTO #error_list
	SELECT 
	fk_tenant_id = @tenant_id
	,export_id = 1
	,batch_id = @batch_id
	,error_type = 'BUDGET_UNBALANCED_CONTROL_SP'
	,dim_1_type = ''
	,dim_2_type = ''
	,dim_1_id = ''
	,dim_2_id = ''	
	,dim_to_change = ''
	,id_new = ''

END

DECLARE @error_count INT
SET @error_count = (select COUNT(*)from #error_list)
select
fk_tenant_id = @tenant_id
,export_id = 1
,batch_id = @batch_id
,erp_type = @erp_type
,error_count = @error_count


SELECT 
fk_tenant_id
,export_id
,batch_id
,error_type
,dim_1_type
,dim_1_id
FROM #error_list
GO