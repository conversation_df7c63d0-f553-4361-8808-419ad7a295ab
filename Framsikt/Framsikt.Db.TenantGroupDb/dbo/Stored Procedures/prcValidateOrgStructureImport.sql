CREATE OR ALTER PROCEDURE [dbo].[prcValidateOrgStructureImport]
(
    @tenant_id INT,
    @user_id INT,
    @job_id BIGINT
)
AS
BEGIN
    -- Constants
    DECLARE @level INT = 8; -- Number of organization levels

    -- Variables
    DECLARE @currentLevel INT;
    DECLARE @sql NVARCHAR(MAX);
	DECLARE @sql1 NVARCHAR(MAX);
	DECLARE @sql2 NVARCHAR(MAX);
	DECLARE @sql3 NVARCHAR(MAX);
    
    -- Initialize error fields to 0
    IF @job_id = -1
    BEGIN
        UPDATE tbu_stage_org_structure_import
        SET
            fk_org_version_error = 0,
            fk_department_code_error = 0,
            error_count = 0,
            org_id_1_error = 0,
            org_name_1_error = 0,
            org_id_2_error = 0,
            org_name_2_error = 0,
            org_id_3_error = 0,
            org_name_3_error = 0,
            org_id_4_error = 0,
            org_name_4_error = 0,
            org_id_5_error = 0,
            org_name_5_error = 0,
            org_id_6_error = 0,
            org_name_6_error = 0,
            org_id_7_error = 0,
            org_name_7_error = 0,
            org_id_8_error = 0,
            org_name_8_error = 0
        WHERE fk_tenant_id = @tenant_id AND user_id = @user_id;
    END
    ELSE
    BEGIN
        UPDATE tbu_stage_org_structure_import
        SET
            fk_org_version_error = 0,
            fk_department_code_error = 0,
            error_count = 0,
            org_id_1_error = 0,
            org_name_1_error = 0,
            org_id_2_error = 0,
            org_name_2_error = 0,
            org_id_3_error = 0,
            org_name_3_error = 0,
            org_id_4_error = 0,
            org_name_4_error = 0,
            org_id_5_error = 0,
            org_name_5_error = 0,
            org_id_6_error = 0,
            org_name_6_error = 0,
            org_id_7_error = 0,
            org_name_7_error = 0,
            org_id_8_error = 0,
            org_name_8_error = 0
        WHERE fk_tenant_id = @tenant_id AND job_Id = @job_id;
    END

    -- Update organization IDs and names to empty strings if NULL
    SET @currentLevel = 1;
    WHILE @currentLevel <= @level
    BEGIN
        SET @sql = N'UPDATE tbu_stage_org_structure_import
                     SET org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' = ''''
                     WHERE fk_tenant_id = @tenant_id AND user_id = @user_id AND org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' IS NULL';

        EXEC sp_executesql @sql, N'@tenant_id INT, @user_id INT', @tenant_id, @user_id;

        SET @sql = N'UPDATE tbu_stage_org_structure_import
                     SET org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + ' = ''''
                     WHERE fk_tenant_id = @tenant_id AND user_id = @user_id AND org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + ' IS NULL';

        EXEC sp_executesql @sql, N'@tenant_id INT, @user_id INT', @tenant_id, @user_id;

        SET @currentLevel = @currentLevel + 1;
    END

    -- Update fk_department_code to empty string if NULL
    UPDATE tbu_stage_org_structure_import
    SET fk_department_code = ''
    WHERE fk_tenant_id = @tenant_id AND user_id = @user_id AND fk_department_code IS NULL;

	CREATE TABLE #TempResults (
						temp_pk_id BIGINT,  
    temp_fk_tenant_id INT NOT NULL,
    temp_fk_org_version VARCHAR(25) NOT NULL,
    temp_org_level INT NULL,
    temp_updated DATETIME NOT NULL,
    temp_updated_by INT NOT NULL,
    temp_org_id_1 VARCHAR(100),
    temp_org_name_1 VARCHAR(500),
    temp_org_id_2 VARCHAR(100),
    temp_org_name_2 VARCHAR(500),
    temp_org_id_3 VARCHAR(100),
    temp_org_name_3 VARCHAR(500),
    temp_org_id_4 VARCHAR(100),
    temp_org_name_4 VARCHAR(500),
    temp_org_id_5 VARCHAR(100),
    temp_org_name_5 VARCHAR(500),
    temp_org_id_6 VARCHAR(100),
    temp_org_name_6 VARCHAR(200),
    temp_org_id_7 VARCHAR(100),
    temp_org_name_7 VARCHAR(200),
    temp_org_id_8 VARCHAR(100),
    temp_org_name_8 VARCHAR(500),
	temp_error_count BIGINT DEFAULT 0,
    temp_fk_department_code VARCHAR(200),
    temp_fk_org_version_error BIT NOT NULL DEFAULT 0,
    temp_fk_department_code_error BIT NOT NULL DEFAULT 0,
    temp_job_Id BIGINT NULL,
    temp_org_id_1_error BIT NOT NULL DEFAULT 0,
    temp_org_name_1_error BIT NOT NULL DEFAULT 0,
    temp_org_id_2_error BIT NOT NULL DEFAULT 0,
    temp_org_name_2_error BIT NOT NULL DEFAULT 0,
    temp_org_id_3_error BIT NOT NULL DEFAULT 0,
    temp_org_name_3_error BIT NOT NULL DEFAULT 0,
    temp_org_id_4_error BIT NOT NULL DEFAULT 0,
    temp_org_name_4_error BIT NOT NULL DEFAULT 0,
    temp_org_id_5_error BIT NOT NULL DEFAULT 0,
    temp_org_name_5_error BIT NOT NULL DEFAULT 0,
    temp_org_id_6_error BIT NOT NULL DEFAULT 0,
    temp_org_name_6_error BIT NOT NULL DEFAULT 0,
    temp_org_id_7_error BIT NOT NULL DEFAULT 0,
    temp_org_name_7_error BIT NOT NULL DEFAULT 0,
    temp_org_id_8_error BIT NOT NULL DEFAULT 0,
    temp_org_name_8_error BIT NOT NULL DEFAULT 0,
	temp_user_id INT );
		SET @sql2 = N'
						INSERT INTO #TempResults( a.temp_pk_id, a.temp_fk_tenant_id, a.temp_fk_org_version, b.temp_org_level, a.temp_updated, 
							   a.temp_updated_by, a.temp_org_id_1, a.temp_org_name_1, a.temp_org_id_2, a.temp_org_name_2, a.temp_org_id_3, 
							   a.temp_org_name_3, a.temp_org_id_4, a.temp_org_name_4, a.temp_org_id_5, a.temp_org_name_5, a.temp_org_id_6, a.temp_org_name_6, 
							   a.temp_org_id_7, a.temp_org_name_7, a.temp_org_id_8, a.temp_org_name_8, a.temp_error_count, a.temp_fk_department_code,
							   a.temp_fk_org_version_error, a.temp_fk_department_code_error, a.temp_job_Id,
							   a.temp_org_id_1_error, a.temp_org_name_1_error, a.temp_org_id_2_error, a.temp_org_name_2_error,
							   a.temp_org_id_3_error, a.temp_org_name_3_error, a.temp_org_id_4_error, a.temp_org_name_4_error,
							   a.temp_org_id_5_error, a.temp_org_name_5_error, a.temp_org_id_6_error, a.temp_org_name_6_error,
							   a.temp_org_id_7_error, a.temp_org_name_7_error, a.temp_org_id_8_error, a.temp_org_name_8_error,a.temp_user_id)
						SELECT a.pk_id, a.fk_tenant_id, a.fk_org_version, b.org_level, a.updated, 
							   a.updated_by, a.org_id_1, a.org_name_1, a.org_id_2, a.org_name_2, a.org_id_3, 
							   a.org_name_3, a.org_id_4, a.org_name_4, a.org_id_5, a.org_name_5, a.org_id_6, a.org_name_6, 
							   a.org_id_7, a.org_name_7, a.org_id_8, a.org_name_8, a.error_count, a.fk_department_code,
							   a.fk_org_version_error, a.fk_department_code_error, a.job_Id,
							   a.org_id_1_error, a.org_name_1_error, a.org_id_2_error, a.org_name_2_error,
							   a.org_id_3_error, a.org_name_3_error, a.org_id_4_error, a.org_name_4_error,
							   a.org_id_5_error, a.org_name_5_error, a.org_id_6_error, a.org_name_6_error,
							   a.org_id_7_error, a.org_name_7_error, a.org_id_8_error, a.org_name_8_error,a.user_id
						FROM tbu_stage_org_structure_import a
						LEFT JOIN tco_org_level b 
						ON a.fk_tenant_id = b.fk_tenant_id 
						AND a.fk_org_version = b.fk_org_version
						WHERE a.fk_tenant_id = @tenant_id AND a.user_id = @user_id
					';
				
					EXEC sp_executesql @sql2, N'@tenant_id INT, @user_id INT', @tenant_id, @user_id;

    -- Add error logic for specific condition based on job_id
    IF(@job_id = -1)
    BEGIN
         --Error handling for job_id = -1
        UPDATE a
        SET fk_org_version_error = 1, error_count = error_count + 1 
        FROM tbu_stage_org_structure_import a
        LEFT JOIN tco_org_version b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_org_version = b.pk_org_version
        WHERE a.fk_tenant_id = @tenant_id AND a.user_id = @user_id AND b.pk_org_version IS NULL;

        UPDATE a
        SET fk_department_code_error = 1, error_count = error_count + 1 
        FROM tbu_stage_org_structure_import a
        LEFT JOIN tco_departments b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_department_code = b.pk_department_code
        WHERE a.fk_tenant_id = @tenant_id AND a.user_id = @user_id AND b.pk_department_code IS NULL;

        SET @currentLevel = 1;
        WHILE @currentLevel <= @level
        BEGIN
            -- Process org_id assuming org_level exists
            SET @sql = N'UPDATE a
                         SET org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 1, error_count = error_count + 1
                         FROM tbu_stage_org_structure_import a
                         LEFT JOIN tco_org_level b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_org_version = b.fk_org_version
                         WHERE a.fk_tenant_id = @tenant_id AND a.user_id = @user_id AND a.org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' = ''''
                         AND b.org_level = ' + CAST(@currentLevel AS NVARCHAR) + '';
            
            EXEC sp_executesql @sql, N'@tenant_id INT, @user_id INT', @tenant_id, @user_id;

            -- Increments org_name error if same org_id has different names
            SET @sql = N'UPDATE a
						SET temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 1, temp_error_count = CASE WHEN temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 0 THEN a.temp_error_count + 1 ELSE a.temp_error_count END
						FROM #TempResults a
						WHERE a.temp_fk_tenant_id = @tenant_id 
						AND a.temp_user_id = @user_id AND a.temp_org_level = ' + CAST(@currentLevel AS NVARCHAR) + '
						AND a.temp_org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' 
						IN (
						SELECT a.org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
						FROM tbu_stage_org_structure_import a
						WHERE a.fk_tenant_id = @tenant_id AND a.user_id = @user_id
						GROUP BY org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
						HAVING COUNT(DISTINCT org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + ') > 1
						) 
						';
						 
						 
						 EXEC sp_executesql @sql, N'@tenant_id INT, @user_id INT', @tenant_id, @user_id;
						 
						 -- Decrements org_name error if same org_id has distinct name
						 SET @sql1 = N'UPDATE a
										SET temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 0, temp_error_count = CASE WHEN temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 1 THEN a.temp_error_count - 1 ELSE a.temp_error_count END
										FROM #TempResults a
										WHERE a.temp_fk_tenant_id = @tenant_id 
										AND a.temp_user_id = @user_id AND a.temp_org_level = ' + CAST(@currentLevel AS NVARCHAR) + '
										AND a.temp_org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' 
										IN (
										SELECT a.org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
										FROM tbu_stage_org_structure_import a
										WHERE a.fk_tenant_id = @tenant_id AND a.user_id = @user_id
										GROUP BY org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
										HAVING COUNT(DISTINCT org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + ') = 1
										) 
										';
EXEC sp_executesql @sql1, N'@tenant_id INT, @user_id INT', @tenant_id, @user_id;

			SET @sql3 = N'UPDATE b
             SET error_count = error_count + tr.temp_error_count,
			 org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error=temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error,
			 org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error=temp_org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error
                 
             FROM tbu_stage_org_structure_import b
             JOIN #TempResults tr ON b.fk_tenant_id = tr.temp_fk_tenant_id
               AND b.user_id = tr.temp_user_id
               AND b.pk_id = tr.temp_pk_id
             WHERE tr.temp_fk_tenant_id = @tenant_id AND tr.temp_user_id = @user_id AND tr.temp_org_level = ' + CAST(@currentLevel AS NVARCHAR(2)) + '';

            EXEC sp_executesql @sql3, N'@tenant_id INT, @user_id BIGINT', @tenant_id, @user_id;

            SET @currentLevel = @currentLevel + 1;
        END

        -- Additional logic for managing duplicate department codes
         UPDATE a
		SET fk_department_code_error = 1, error_count = a.error_count + 1 
		FROM tbu_stage_org_structure_import a
		LEFT JOIN tbu_stage_org_structure_import b on a.pk_id=b.pk_id
		WHERE a.fk_tenant_id = @tenant_id
		AND a.user_id = @user_id 
		AND a.pk_id!=b.pk_id;
    END
    ELSE
    BEGIN
		-- Error handling for specific job_id
        UPDATE a
        SET fk_org_version_error = 1, error_count = error_count + 1 
        FROM tbu_stage_org_structure_import a
        LEFT JOIN tco_org_version b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_org_version = b.pk_org_version
        WHERE a.fk_tenant_id = @tenant_id AND a.job_Id = @job_id AND b.pk_org_version IS NULL;

        UPDATE a
				SET fk_department_code_error = 1, error_count = a.error_count + 1 
				FROM tbu_stage_org_structure_import a
				LEFT JOIN tbu_stage_org_structure_import b on a.fk_tenant_id = b.fk_tenant_id and a.fk_org_version = b.fk_org_version and a.job_Id = b.job_Id	
				WHERE a.fk_tenant_id = @tenant_id
				AND a.job_Id = @job_id
				AND a.fk_department_code = b.fk_department_code
				AND a.pk_id != b.pk_id

        SET @currentLevel = 1;
        WHILE @currentLevel <= @level
        BEGIN
        -- Process org_id assuming org_level exists
            SET @sql = N'UPDATE a
                         SET org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 1, error_count = error_count + 1
                         FROM tbu_stage_org_structure_import a
                         LEFT JOIN tco_org_level b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_org_version = b.fk_org_version
                         WHERE a.fk_tenant_id = @tenant_id AND a.job_Id = @job_id AND a.org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' = ''''
                         AND b.org_level = ' + CAST(@currentLevel AS NVARCHAR) + '';

            
            EXEC sp_executesql @sql, N'@tenant_id INT, @job_id BIGINT', @tenant_id, @job_id;

            -- Increments org_name error if same org_id has different names
            SET @sql = N'UPDATE a
						SET temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 1, temp_error_count = CASE WHEN temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 0 THEN a.temp_error_count + 1 ELSE a.temp_error_count END
						FROM #TempResults a
						WHERE a.temp_fk_tenant_id = @tenant_id 
						AND a.temp_job_Id = @job_id AND a.temp_org_level = ' + CAST(@currentLevel AS NVARCHAR) + '
						AND a.temp_org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' 
						IN (
						SELECT a.org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
						FROM tbu_stage_org_structure_import a
						WHERE a.fk_tenant_id = @tenant_id AND a.job_Id = @job_id
						GROUP BY org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
						HAVING COUNT(DISTINCT org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + ') > 1
						) 
						';
						 
						 
						 EXEC sp_executesql @sql, N'@tenant_id INT, @job_id BIGINT', @tenant_id, @job_id;
						 
						 -- Decrements org_name error if same org_id has distinct name
						 SET @sql1 = N'UPDATE a
										SET temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 0, temp_error_count = CASE WHEN temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error = 1 THEN a.temp_error_count - 1 ELSE a.temp_error_count END
										FROM #TempResults a
										WHERE a.temp_fk_tenant_id = @tenant_id 
										AND a.temp_job_id = @job_Id AND a.temp_org_level = ' + CAST(@currentLevel AS NVARCHAR) + '
										AND a.temp_org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + ' 
										IN (
										SELECT a.org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
										FROM tbu_stage_org_structure_import a
										WHERE a.fk_tenant_id = @tenant_id AND a.job_Id = @job_id
										GROUP BY org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '
										HAVING COUNT(DISTINCT org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + ') = 1
										) 
										';

            
            EXEC sp_executesql @sql, N'@tenant_id INT, @job_id BIGINT', @tenant_id, @job_id;

			SET @sql3 = N'UPDATE b
             SET error_count = error_count + tr.temp_error_count,
			 org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error=temp_org_name_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error,
			 org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error=temp_org_id_' + CAST(@currentLevel AS NVARCHAR(2)) + '_error
                 
             FROM tbu_stage_org_structure_import b
             JOIN #TempResults tr ON b.fk_tenant_id = tr.temp_fk_tenant_id
               AND b.job_Id = tr.temp_job_Id
               AND b.pk_id = tr.temp_pk_id
             WHERE tr.temp_fk_tenant_id = @tenant_id AND tr.temp_job_id = @job_Id AND tr.temp_org_level = ' + CAST(@currentLevel AS NVARCHAR(2)) + '';

            EXEC sp_executesql @sql3, N'@tenant_id INT, @job_id BIGINT', @tenant_id, @job_id;
            
            
            SET @currentLevel = @currentLevel + 1;
        END
		DROP TABLE #TempResults;

		-- Additional logic for managing duplicate department codes
         UPDATE a
		SET fk_department_code_error = 1, error_count = a.error_count + 1 
		FROM tbu_stage_org_structure_import a
		LEFT JOIN tbu_stage_org_structure_import b on a.pk_id=b.pk_id
		WHERE a.fk_tenant_id = @tenant_id
		AND a.job_Id = @job_id 
		AND a.pk_id!=b.pk_id;

    END
END
