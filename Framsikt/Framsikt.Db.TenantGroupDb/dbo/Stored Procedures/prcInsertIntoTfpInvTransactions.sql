CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTfpInvTransactions]
	(
		@udtTfpInvTransactions udtTfpInvTransactions readonly
    )
AS
BEGIN
	insert into tfp_inv_transactions 
	(
	[pk_id] ,
	[fk_inv_action_id] ,
	[fk_investment_id] ,
	[fk_tenant_id] ,
	[budget_year] ,
	[fk_account_code] ,
	[fk_function_code] ,
	[department_code] ,
	[vat_rate] ,
	[vat_refund] ,
	[year_1_amount] ,
	[year_2_amount] ,
	[year_3_amount],
	[year_4_amount] ,
	[updated] ,
	[type] ,
	[updated_by] ,
	[fk_project_code],
	[free_dim_1] ,
	[fk_change_id] ,
	[free_dim_2] ,
	[free_dim_3],
	[free_dim_4] ,
	[fk_inv_details_id] ,
	[fk_alter_code],
	[fk_adjustment_code] ,
	[fk_prog_code]
	)
	select 
	[pk_id] ,
	[fk_inv_action_id] ,
	[fk_investment_id] ,
	[fk_tenant_id] ,
	[budget_year] ,
	[fk_account_code] ,
	[fk_function_code ] ,
	[department_code ] ,
	[vat_rate] ,
	[vat_refund] ,
	[year_1_amount] ,
	[year_2_amount] ,
	[year_3_amount],
	[year_4_amount] ,
	[updated] ,
	[type] ,
	[updated_by ] ,
	[fk_project_code],
	[free_dim_1] ,
	[fk_change_id] ,
	[free_dim_2] ,
	[free_dim_3],
	[free_dim_4] ,
	[fk_inv_details_id] ,
	[fk_alter_code],
	[fk_adjustment_code] ,
	[fk_prog_code]
	from @udtTfpInvTransactions
End

go
