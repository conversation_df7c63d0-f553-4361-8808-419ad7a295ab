CREATE OR ALTER PROCEDURE [dbo].[prc_OrgLevelToLangString]
	@tenant_id int 

AS

/* This procedure is not used by application but is manually run by consultants. 

It will add tenant specific lang strings based on the descriptions in org level table. 
So that labels will show the actual level name instead of org_id_2 etc... 

*/

/* This is sample queryes that is nice to have to genereate the insert script for new lang strings that we might want to add.

SELECT *, '(''' + [id] + '''' + ',''' +context + '''' + ',''' + description + '''' + ',''replace' + '''' + ',''' + Language + '''' + ',' + '5'+'),' 
FROM gco_language_strings where description like 'org%5%' AND language IN ('nb-NO')
AND Description !=	'Org.id.'


SELECT *, '(''' + [id] + '''' + ',''' +context + '''' + ',''' + description + '''' + ',''replace' + '''' + ',''' + Language + '''' + ',' + '5'+'),' 
FROM gco_language_strings where 1=1
--and lang_string_id = ''
and context = 'YearlyBudgetReporting'
AND language IN ('nb-NO')

*/

DECLARE @max_level INT
DECLARE @org_version varchar(25)  

SET @org_version = (SELECT pk_org_version FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND (DATEPART(year, getdate())+1) * 100 +1 between period_from and period_to)
SET @max_level = (
select  max(org_level) from tco_org_level lv where lv.fk_tenant_id = @tenant_id AND fk_org_version = @org_version)



DROP TABLE if exists #lang_table

CREATE TABLE #lang_table (
lang_string_id varchar(100),
context varchar (55),
[lang_description] nvarchar (500),
[postfix] nvarchar (500),
[language] varchar(10),
org_level INT, 
new_description nvarchar(500) NULL
)


INSERT INTO #lang_table (lang_string_id,context,lang_description,postfix, language, org_level)
values 
('BAR_rptcol_orgid1','BudgetAdjustmentsReport','ORG ID 1','(kode)','nb-NO',1),
('BAR_rptcol_orgname1','BudgetAdjustmentsReport','ORG NIVÅ 1','(tekst)','nb-NO',1),
('FPR_reportCol57','FinPlanReporting','Org ID 1','(kode)','nb-NO',1),
('FPR_reportCol62','FinPlanReporting','Org Nivå 1','(tekst)','nb-NO',1),
('FDR_rptcol_orgid1','ForecastDetailsReport','ORG ID 1','(kode)','nb-NO',1),
('FDR_rptcol_orgname1','ForecastDetailsReport','Org nivå 1','(tekst)','nb-NO',1),
('FR_rptcol_orgid1','ForecastReport','Org ID 1','(kode)','nb-NO',1),
('FR_rptcol_orgname1','ForecastReport','Org ID 1 (T)','(tekst)','nb-NO',1),
('INV_reportCol6','InvestmentReporting','Org nivå 1 (kode)','(kode)','nb-NO',1),
('INV_reportCol7','InvestmentReporting','Org nivå 1','(tekst)','nb-NO',1),
('YBR_reportCol20','YearlyBudgetReporting','Org ID 1','(kode)','nb-NO',1),
('YBR_reportCol21','YearlyBudgetReporting','Org ID 1 (T)','(tekst)','nb-NO',1),

('BAR_rptcol_orgid2','BudgetAdjustmentsReport','ORG ID 2','(kode)','nb-NO',2),
('BAR_rptcol_orgname2','BudgetAdjustmentsReport','ORG NIVÅ 2','(tekst)','nb-NO',2),
('FPR_reportCol58','FinPlanReporting','Org ID 2','(kode)','nb-NO',2),
('FPR_reportCol63','FinPlanReporting','Org Nivå 2','(tekst)','nb-NO',2),
('FDR_rptcol_orgid2','ForecastDetailsReport','ORG ID 2','(kode)','nb-NO',2),
('FDR_rptcol_orgname2','ForecastDetailsReport','Org nivå 2','(tekst)','nb-NO',2),
('FR_rptcol_orgid2','ForecastReport','Org ID 2','(kode)','nb-NO',2),
('FR_rptcol_orgname2','ForecastReport','Org ID 2 (T)','(tekst)','nb-NO',2),
('INV_reportCol8','InvestmentReporting','Org nivå 2 (kode)','(kode)','nb-NO',2),
('INV_reportCol9','InvestmentReporting','Org nivå 2','(tekst)','nb-NO',2),
('YBR_reportCol22','YearlyBudgetReporting','Tjenesteområde','(kode)','nb-NO',2),
('YBR_reportCol23','YearlyBudgetReporting','Tjenesteområde (T)','(tekst)','nb-NO',2),

('BAR_rptcol_orgid3','BudgetAdjustmentsReport','ORG ID 3','(kode)','nb-NO',3),
('BAR_rptcol_orgname3','BudgetAdjustmentsReport','ORG NIVÅ 3','(tekst)','nb-NO',3),
('FPR_reportCol59','FinPlanReporting','Org ID 3','(kode)','nb-NO',3),
('FPR_reportCol64','FinPlanReporting','Org Nivå 3','(tekst)','nb-NO',3),
('FDR_rptcol_orgid3','ForecastDetailsReport','ORG ID 3','(kode)','nb-NO',3),
('FDR_rptcol_orgname3','ForecastDetailsReport','Org nivå 3','(tekst)','nb-NO',3),
('FR_rptcol_orgid3','ForecastReport','Org ID 3','(kode)','nb-NO',3),
('FR_rptcol_orgname3','ForecastReport','Org ID 3 (T)','(tekst)','nb-NO',3),
('INV_reportCol10','InvestmentReporting','Org nivå 3 (kode)','(kode)','nb-NO',3),
('INV_reportCol11','InvestmentReporting','Org nivå 3','(tekst)','nb-NO',3),
('YBR_reportCol24','YearlyBudgetReporting','Tjenesteenhet','(kode)','nb-NO',3),
('YBR_reportCol25','YearlyBudgetReporting','Tjenesteenhet (T)','(tekst)','nb-NO',3),

('BAR_rptcol_orgid4','BudgetAdjustmentsReport','ORG ID 4','(kode)','nb-NO',4),
('BAR_rptcol_orgname4','BudgetAdjustmentsReport','ORG NIVÅ 4','(tekst)','nb-NO',4),
('FPR_reportCol60','FinPlanReporting','Org ID 4','(kode)','nb-NO',4),
('FPR_reportCol65','FinPlanReporting','Org Nivå 4','(tekst)','nb-NO',4),
('FDR_rptcol_orgid4','ForecastDetailsReport','ORG ID 4','(kode)','nb-NO',4),
('FDR_rptcol_orgname4','ForecastDetailsReport','Org nivå 4','(tekst)','nb-NO',4),
('FR_rptcol_orgid4','ForecastReport','Org ID 4','(kode)','nb-NO',4),
('FR_rptcol_orgname4','ForecastReport','Org ID 4 (T)','(tekst)','nb-NO',4),
('INV_reportCol12','InvestmentReporting','Orgnivå 4 (kode)','(kode)','nb-NO',4),
('INV_reportCol13','InvestmentReporting','Orgnivå 4 (T)','(tekst)','nb-NO',4),
('YBR_reportCol26','YearlyBudgetReporting','Org ID 4','(kode)','nb-NO',4),
('YBR_reportCol27','YearlyBudgetReporting','Org ID 4 (T)','(tekst)','nb-NO',4),

('BAR_rptcol_orgid5','BudgetAdjustmentsReport','ORG ID 5','(kode)','nb-NO',5),
('BAR_rptcol_orgname5','BudgetAdjustmentsReport','ORG NIVÅ 5','(tekst)','nb-NO',5),
('FPR_reportCol61','FinPlanReporting','Org ID 5','(kode)','nb-NO',5),
('FPR_reportCol66','FinPlanReporting','Org Nivå 5','(tekst)','nb-NO',5),
('FDR_rptcol_orgid5','ForecastDetailsReport','ORG ID 5','(kode)','nb-NO',5),
('FDR_rptcol_orgname5','ForecastDetailsReport','Org nivå 5','(tekst)','nb-NO',5),
('FR_rptcol_orgid5','ForecastReport','Org ID 5','(kode)','nb-NO',5),
('FR_rptcol_orgname5','ForecastReport','Org ID 5 (T)','(tekst)','nb-NO',5),
('INV_reportCol14','InvestmentReporting','Org nivå 5 (kode)','(kode)','nb-NO',5),
('INV_reportCol15','InvestmentReporting','Org nivå 5','(tekst)','nb-NO',5),
('YBR_reportCol28','YearlyBudgetReporting','Org ID 5','(kode)','nb-NO',5),
('YBR_reportCol29','YearlyBudgetReporting','Org ID 5 (T)','(tekst)','nb-NO',5),

('org_structure_org_id_2','StaffPlanning','Org. kode 2','(kode)','nb-NO',2),
('org_structure_org_id_3','StaffPlanning','Org. kode 3','(kode)','nb-NO',3),
('org_structure_org_id_4','StaffPlanning','Org. kode 4','(kode)','nb-NO',4),
('org_structure_org_id_5','StaffPlanning','Org. kode 5','(kode)','nb-NO',5),
('org_structure_org_id_6','StaffPlanning','Org. kode 6','(kode)','nb-NO',6),
('org_structure_org_id_7','StaffPlanning','Org. kode 7','(kode)','nb-NO',7),
('org_structure_org_id_8','StaffPlanning','Org. kode 8','(kode)','nb-NO',8),

('org_structure_org_name_2','StaffPlanning','Org.navn 2','(tekst)','nb-NO',2),
('org_structure_org_name_3','StaffPlanning','Org.navn 3','(tekst)','nb-NO',3),
('org_structure_org_name_4','StaffPlanning','Org.navn 4','(tekst)','nb-NO',4),
('org_structure_org_name_5','StaffPlanning','Org.navn 5','(tekst)','nb-NO',5),
('org_structure_org_name_6','StaffPlanning','Org.navn 6','(tekst)','nb-NO',6),
('org_structure_org_name_7','StaffPlanning','Org.navn 7','(tekst)','nb-NO',7),
('org_structure_org_name_8','StaffPlanning','Org.navn 8','(tekst)','nb-NO',8),
('AdminUser_orgId_1','LangStringAdmin','Org.nivå 1','(kode)','nb-NO',1),
('AdminUser_orgId_1_desc','LangStringAdmin','Org.nivå 1 (T)','(tekst)','nb-NO',1),
('AdminUser_orgId_2','LangStringAdmin','Org.nivå 2','(kode)','nb-NO',2),
('AdminUser_orgId_2_desc','LangStringAdmin','Org.nivå 2 (T)','(tekst)','nb-NO',2),
('AdminUser_orgId_3','LangStringAdmin','Org.nivå 3','(kode)','nb-NO',3),
('AdminUser_orgId_3_desc','LangStringAdmin','Org.nivå 3 (T)','(tekst)','nb-NO',3),
('AdminUser_orgId_4','LangStringAdmin','Org.nivå 4','(kode)','nb-NO',4),
('AdminUser_orgId_4_desc','LangStringAdmin','Org.nivå 4 (T)','(tekst)','nb-NO',4),
('AdminUser_orgId_5','LangStringAdmin','Org.nivå 5','(kode)','nb-NO',5),
('AdminUser_orgId_5_desc','LangStringAdmin','Org.nivå 5 (T)','(tekst)','nb-NO',5)

PRINT 'Inserting the lang strings that should be part of this in helptable'

DELETE FROM #lang_table WHERE org_level > @max_level 

PRINT 'Delete org levels not being used by the tenant'

INSERT INTO #lang_table (lang_string_id,context,lang_description,postfix, language, org_level)
select lang_string_id,context,lang_description,postfix, 'nn-NO' as language, org_level
FROM #lang_table

print 'Insert nynorsk versions of same'


UPDATE l SET new_description = lv.level_name + ' ' + l.postfix 
from #lang_table l
JOIN tco_org_level lv ON lv.fk_tenant_id = @tenant_id AND l.org_level = lv.org_level AND fk_org_version = @org_version

print 'Generating new descriptions'


DELETE ls FROM gco_language_string_overrides_tenant ls
JOIN #lang_table l ON ls.context = l.context AND l.lang_string_id = ls.ID AND l.language = ls.Language
WHERE ls.fk_tenant_id = @tenant_id

print 'Remove old values of tenant specific lang strings'


DELETE l FROM gco_language_string_overrides_tenant ls
JOIN #lang_table l ON ls.context = l.context AND l.new_description = ls.Description AND l.language = ls.Language
WHERE ls.fk_tenant_id = @tenant_id

print 'Remove to avoid duplicate headers in same report'


INSERT INTO gco_language_string_overrides_tenant (id, Description, Language, context,fk_tenant_id)
select l.lang_string_id,l.new_description as description, l.language, l.context, @tenant_id as fk_tenant_id
from #lang_table l
order by l.org_level

print 'Insert into lang strings for tenant ' + convert(varchar(12),@tenant_id)

--SELECT * FROM gco_language_string_overrides_tenant where fk_tenant_id = 487

--SELECT * FROM tco_org_level where fk_tenant_id = 487

PRINT ''
PRINT ''
PRINT '!!!!  Well done. Remember to clear the cache before checking the result !!!!'

RETURN 0
