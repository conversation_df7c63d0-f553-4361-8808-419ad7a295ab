CREATE OR ALTER PROC [dbo].[prcInitializeSAMbase] @tenant_id INT, @budget_year INT, @updated_by INT, @status INT  
  
AS  
  
IF @status = 1 BEGIN  
  
 DELETE FROM [dbo].[tsam_period_status]  
 where fk_tenant_id = @tenant_id  
 and forecast_period = @budget_year*100  
  
 INSERT [dbo].[tsam_period_status]  
 select @tenant_id,@budget_year*100,1,GETDATE(),@updated_by  
  
 --delete existing  
 DELETE from [tsam_base_transactions]  
 WHERE fk_tenant_id = @tenant_id  
 and budget_year = @budget_year  
  
 select top 10 * from tsam_base_transactions  
  
 --insert new data  
 INSERT [dbo].[tsam_base_transactions]  
 select A.fk_tenant_id
    ,a.budget_year
	,a.fk_action_id
	,a.fk_account_code
	,a.fk_department_code
	,a.fk_function_code
	,a.fk_project_code
	,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,a.fk_alter_code, a.fp_amt
   ,fp_description = ''  
   ,central_amt = fp_amt  
   ,central_description = ''  
   ,dep_amt = 0  
   ,dep_description = ''  
   ,final_amt = 0  
   ,updated = GETDATE()  
   ,updated_by = @updated_by  
   ,new_fk_action_id = NULL  
   ,fk_user_adjustment_code = NULL  
   ,fk_adjustment_code_finplan=[fk_adjustment_code_finplan]
 FROM  
 (  
  SELECT a.fk_tenant_id,a.budget_year,fk_action_id,fk_account_code,department_code as fk_department_code,function_code as fk_function_code,project_code as fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,fk_alter_code,SUM(year_1_amount)fp_amt,fk_adjustment_code [fk_adjustment_code_finplan]  
  from tfp_trans_detail a  
  JOIN tco_fp_alter_codes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_alter_code = b.pk_alter_code  
  JOIN [tsam_limit_code_setup] c on a.fk_tenant_id = c.fk_tenant_id and a.budget_year = c.budget_year and b.limit_code = c.limit_code  
  where a.fk_tenant_id = @tenant_id  
  and a.budget_year = @budget_year  
  and c.status = 1  
  group by a.fk_tenant_id,a.budget_year,fk_action_id,fk_account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,fk_alter_code ,fk_adjustment_code 
 ) A where fp_amt <> 0  
END  
  
IF @status = 2 BEGIN  
  
 update [dbo].[tsam_period_status] set status = @status  
 where fk_tenant_id = @tenant_id  
 and forecast_period = @budget_year*100  
  
 update [tsam_base_transactions] set dep_amt = central_amt  
 where fk_tenant_id = @tenant_id  
 and budget_year = @budget_year  
END  
  
IF @status = 3 BEGIN  
  
 update [dbo].[tsam_period_status] set status = @status  
 where fk_tenant_id = @tenant_id  
 and forecast_period = @budget_year*100  
  
 update [tsam_base_transactions] set final_amt = dep_amt  
 where fk_tenant_id = @tenant_id  
 and budget_year = @budget_year  
END  