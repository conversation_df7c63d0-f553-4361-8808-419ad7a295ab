CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTfpTransDetail]
     (
		@udtTfpTransDetail udtTfpTransDetail readonly
     )
AS
BEGIN
	insert into tfp_trans_detail 
	([fk_action_id], [fk_tenant_id],[budget_year], [fk_account_code], [department_code], [function_code], [project_code], [asset_code], [fk_investment_id], [investment_row_id],
	[year_1_amount], [year_2_amount], [year_3_amount], [year_4_amount], [updated], [updated_by], [fk_change_id],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],
	[fk_adjustment_code],[fk_alter_code])
	select
	[fk_action_id] , [fk_tenant_id] , [budget_year] , [fk_account_code] , [department_code] , [function_code] ,[project_code] , [asset_code] ,[fk_investment_id] ,[investment_row_id] ,
	[year_1_amount], [year_2_amount], [year_3_amount], [year_4_amount], [updated], [updated_by], [fk_change_id], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [description],
	[fk_adjustment_code], [fk_alter_code]
	from @udtTfpTransDetail
End