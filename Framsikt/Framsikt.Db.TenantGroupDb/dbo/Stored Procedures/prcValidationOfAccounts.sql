CREATE OR ALTER PROCEDURE [dbo].[prcValidationOfAccounts]
	@tenant_id int
AS
Begin

	/*delete all the existing validations on tenant level*/
	delete from tco_org_validations where fk_tenant_id = @tenant_id and validation_id in (1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013)

	DROP TABLE IF EXISTS #hlp_bud_accounts
	DROP TABLE IF EXISTS #hlp_gl_accounts
	DROP TABLE IF EXISTS #hlp_fp_accounts

	DECLARE @year INT = (SELECT DATEPART(YEAR, GETDATE()))

	SELECT DISTINCT fk_tenant_id, fk_account_code INTO #hlp_bud_accounts FROM tbu_trans_detail a
    WHERE a.fk_tenant_id = @tenant_id

	SELECT DISTINCT fk_tenant_id, fk_account_code INTO #hlp_gl_accounts FROM tfp_accounting_data a
    WHERE a.fk_tenant_id = @tenant_id
	
	SELECT DISTINCT fk_tenant_id, fk_account_code INTO #hlp_fp_accounts FROM tfp_trans_detail a
    WHERE a.fk_tenant_id = @tenant_id

	--'ACCOUNT_MISSING'		'Account used in tfp_trans_detail but not in tco_accounts' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT a.fk_tenant_id, 1001, trim(a.fk_account_code)
	FROM #hlp_fp_accounts a
	WHERE a.fk_tenant_id = @tenant_id
	AND trim(a.fk_account_code) NOT IN 
	(
		SELECT trim(pk_account_code) FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
	) ;

	--'ACCOUNT_MISSING'		'Account used in tbu_trans_detail but not in tco_accounts' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT a.fk_tenant_id, 1002, trim(a.fk_account_code)
	FROM #hlp_bud_accounts a
	WHERE a.fk_tenant_id = @tenant_id
	AND trim(a.fk_account_code) NOT IN 
	(
		SELECT trim(pk_account_code) FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
	) ;

	--'ACCOUNT_MISSING'		'Account used in tfp_accounting_data but not in tco_accounts' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT a.fk_tenant_id, 1003, trim(a.fk_account_code)
	FROM #hlp_gl_accounts a
	WHERE a.fk_tenant_id = @tenant_id
	AND trim(a.fk_account_code) NOT IN 
	(
		SELECT trim(pk_account_code) FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
	) ;

	--'ACCOUNT_MISSING'		'Account used in tco_investment_detail but not in tco_accounts' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT a.fk_tenant_id, 1004, trim(a.fk_account_code) 
	FROM tco_investment_detail a
	WHERE a.fk_tenant_id = @tenant_id
	AND trim(a.fk_account_code) NOT IN 
	(
		SELECT trim(pk_account_code) FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
	) ;

	--'DUPLICATE_ACCOUNT'		'Account might be duplicate in tco_accounts'
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT a.pk_tenant_id, 1005, trim(a.pk_account_code)
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id AND @year between DATEPART(YEAR, a.dateFrom) AND  DATEPART(YEAR, a.dateTo)
	GROUP BY a.pk_tenant_id, trim(a.pk_account_code)
	HAVING COUNT(*) > 1;

	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT a.pk_tenant_id, 1005, trim(a.pk_account_code)
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id AND @year +1 between DATEPART(YEAR, a.dateFrom) AND  DATEPART(YEAR, a.dateTo)
	GROUP BY a.pk_tenant_id, trim(a.pk_account_code)
	HAVING COUNT(*) > 1;

	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT a.pk_tenant_id, 1005, trim(a.pk_account_code)
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id AND @year -1  between DATEPART(YEAR, a.dateFrom) AND  DATEPART(YEAR, a.dateTo)
	GROUP BY a.pk_tenant_id, trim(a.pk_account_code)
	HAVING COUNT(*) > 1;

	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT a.pk_tenant_id, 1005, trim(a.pk_account_code)
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id AND @year - 2  between DATEPART(YEAR, a.dateFrom) AND  DATEPART(YEAR, a.dateTo)
	GROUP BY a.pk_tenant_id, trim(a.pk_account_code)
	HAVING COUNT(*) > 1;

	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT a.pk_tenant_id, 1005, trim(a.pk_account_code)
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id AND @year - 3  between DATEPART(YEAR, a.dateFrom) AND  DATEPART(YEAR, a.dateTo)
	GROUP BY a.pk_tenant_id, trim(a.pk_account_code)
	HAVING COUNT(*) > 1;

	--'KOSTRA_ACCOUNT_MISSING'		'Kostra account code for account in tco_accounts does not exist in gco_kostra_accounts'
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT a.pk_tenant_id, 1006, a.fk_kostra_account_code
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id
	AND a.fk_kostra_account_code NOT IN (SELECT pk_kostra_account_code FROM gco_kostra_accounts);

	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT a.pk_tenant_id, 1013, a.pk_account_code
	FROM tco_accounts a 
	WHERE a.pk_tenant_id = @tenant_id
	AND a.fk_kostra_account_code IN ('0','1');

	--'MONTHREP_ACCOUNT_SETUP_1'		'Account is not defined in tmd_reporting_line type MNDRAPP'  
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT ac.pk_tenant_id, 1007, trim(ac.pk_account_code)
	FROM tco_accounts ac
	JOIN gmd_reporting_line rl ON rl.report = 'YBUD1' AND trim(rl.fk_kostra_account_code) = trim(ac.fk_kostra_account_code) 
	JOIN tco_module_mapping mp ON ac.pk_tenant_id = mp.fk_tenant_id AND mp.fk_module_id= 5
	AND NOT EXISTS (SELECT * FROM tmd_reporting_line df 
	WHERE ac.pk_tenant_id = @tenant_id and trim(ac.pk_account_code) = trim(df.fk_account_code) AND ac.pk_tenant_id = df.fk_tenant_id AND report = 'MNDRAPP')
	AND ac.fk_kostra_account_code not in ('1580','1980')
	AND	ac.pk_tenant_id = @tenant_id
	order BY ac.pk_tenant_id,ac.fk_kostra_account_code

	--'MONTHREP_ACCOUNT_SETUP_2'		'Account is not defined in tmd_reporting_line type INVREPORT'
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT  ac.pk_tenant_id, 1008, trim(ac.pk_account_code) 
	FROM tco_accounts ac
	JOIN gmd_reporting_line rl ON rl.report = 'B2A' AND trim(rl.fk_kostra_account_code) = trim(ac.fk_kostra_account_code) 
	JOIN tco_module_mapping mp ON ac.pk_tenant_id = mp.fk_tenant_id AND mp.fk_module_id= 5
	AND NOT EXISTS (SELECT * FROM tmd_reporting_line df 
	WHERE ac.pk_tenant_id = @tenant_id and trim(ac.pk_account_code) = trim(df.fk_account_code) AND ac.pk_tenant_id = df.fk_tenant_id AND report = 'INVREPORT')
	AND ac.fk_kostra_account_code not in ('1580','1980')
	AND	ac.pk_tenant_id = @tenant_id
	order BY ac.pk_tenant_id,ac.fk_kostra_account_code

	--'SALARY_ACCOUNT_SETUP'		'Account is not defined in tmd_salary_acc_def' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT ac.pk_tenant_id, 1009, trim(ac.pk_account_code)
	FROM tco_accounts ac
	JOIN gmd_reporting_line rl ON rl.report = 'YBUD1' AND trim(rl.fk_kostra_account_code) = trim(ac.fk_kostra_account_code) AND line_group_id = 3000 AND line_item_id between 3000 and 3005
	JOIN tco_module_mapping mp ON ac.pk_tenant_id = mp.fk_tenant_id AND mp.fk_module_id= 4
	AND NOT EXISTS (SELECT * FROM tmd_salary_acc_def df 
	WHERE ac.pk_tenant_id = @tenant_id and trim(ac.pk_account_code) = trim(df.fk_account_code) AND ac.pk_tenant_id = df.fk_tenant_id)
	AND	ac.pk_tenant_id = @tenant_id
	ORDER BY ac.pk_tenant_id,ac.fk_kostra_account_code

	--'INVALID VALUES IN SETUP'		'Account used in tmd_fp_level_defaults is not valid. Budprop will throw an error' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	SELECT DISTINCT df.fk_tenant_id, 1010, trim(df.fk_account_code) 
	FROM tmd_fp_level_defaults df
	WHERE fk_tenant_id = @tenant_id
	AND NOT EXISTS (
	SELECT * FROM tco_accounts ac WHERE trim(df.fk_account_code) = trim(ac.pk_account_code) AND df.fk_tenant_id = ac.pk_tenant_id AND AC.isActive = 1)

	--'INVALID_ACCOUNT_SETUP'		'Account in tmd_acc_defaults does not exist in tco_accounts' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	select DISTINCT d.fk_tenant_id, 1011, d.acc_value 
	from tmd_acc_defaults d
	where d.fk_tenant_id = @tenant_id and acc_type='ACCOUNT'
	and acc_value not in (select trim(pk_account_code) from tco_accounts where pk_tenant_id=d.fk_tenant_id)
	
	--'INVALID_FUNCTION_SETUP'		'Account in tmd_acc_defaults does not exist in tco_accounts' 
	INSERT INTO tco_org_validations (fk_tenant_id, validation_id, error_value)
	select DISTINCT d.pk_tenant_id, 1012, d.pk_Function_code
	from tco_functions d
	where d.pk_tenant_id = @tenant_id AND d.isActive = 1
	and d.fk_kostra_function_code not in (select trim(pk_kostra_function_code) from gmd_kostra_function WHERE active = 1)
	
End