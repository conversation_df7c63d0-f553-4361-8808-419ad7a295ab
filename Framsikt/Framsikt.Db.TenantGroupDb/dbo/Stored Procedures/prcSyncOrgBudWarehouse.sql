
CREATE OR ALTER PROCEDURE [dbo].[prcSyncOrgBudWarehouse]
	@tenant_id int,
	@budget_year int,
    @user_id int
AS
	
DECLARE @tenantid INT = @tenant_id
DECLARE @budgetyear INT = @budget_year
DECLARE @userId INT = @user_id

DECLARE @orgversion VARCHAR(25) 

SET @orgversion = (SELECT pk_org_version FROM tco_org_version ov WHERE ov.fk_tenant_id = @tenantid AND @budgetyear * 100 + 01 BETWEEN ov.period_from AND ov.period_to)

CREATE TABLE #budgetdata
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [sub_tenant_id] INT NOT NULL,
	[finplan_amt_main] DECIMAL(18, 2) NOT NULL,
	[finplan_amt_sub] DECIMAL(18, 2) NOT NULL,
	[org_bud_main] DECIMAL(18,2) NOT NULL,
	[org_bud_sub] DECIMAL(18, 2) NOT NULL
)


DECLARE @subtenants TABLE 
(sub_tenant_id INT)


INSERT INTO @subtenants (sub_tenant_id)
SELECT sub_tenant_id FROM tco_sync_company_setup WHERE fk_tenant_id = @tenantid AND sync_flag != 0 GROUP BY sub_tenant_id

-- start fetching data --

INSERT INTO #budgetdata (
fk_tenant_id
,budget_year
,sub_tenant_id
,finplan_amt_main
,finplan_amt_sub
,org_bud_main
,org_bud_sub
)
SELECT 
fk_tenant_id
,budget_year
,sub_tenant_id
,SUM(finplan_amt_main)
,SUM(finplan_amt_sub)
,SUM(org_bud_main)
,SUM(org_bud_sub)
FROM ( 



-----------



SELECT 
@tenantid as fk_tenant_id
,@budgetyear as budget_year
,s.sub_tenant_id
,sum(d.year_1_amount) as finplan_amt_main
,0 as finplan_amt_sub
,0 as org_bud_main
,0 as org_bud_sub
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN tco_org_hierarchy oh ON d.fk_tenant_id = oh.fk_tenant_id AND d.department_code = oh.fk_department_code AND oh.fk_org_version = @orgversion
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
WHERE d.fk_tenant_id = @tenantid AND d.budget_year = @budgetyear
GROUP BY s.sub_tenant_id

UNION ALL


SELECT 
@tenantid as fk_tenant_id
,@budgetyear as budget_year
,d.fk_tenant_id
,0 as finplan_amt_main
,sum(d.year_1_amount) as finplan_amt_sub
,0 as org_bud_main
,0 as org_bud_sub
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN tco_departments dep ON dep.fk_tenant_id = d.fk_tenant_id AND dep.pk_department_code = d.department_code AND @budget_year BETWEEN dep.year_from AND dep.year_to
JOIN tco_org_version ov ON dep.fk_tenant_id = ov.fk_tenant_id AND @budget_year * 100 + 01 BETWEEN ov.period_from AND ov.period_to
JOIN tco_org_hierarchy oh ON oh.fk_tenant_id = d.fk_tenant_id AND oh.fk_department_code = d.department_code AND oh.fk_org_version = ov.pk_org_version
JOIN tco_relation_values r ON r.fk_tenant_id = d.fk_tenant_id AND d.department_code BETWEEN r.relation_value_from AND r.relation_value_to AND @budget_year BETWEEN r.year_from AND r.year_to
JOIN tco_attribute_values a ON a.fk_tenant_id = r.fk_tenant_id AND a.pk_attribute_id = r.attribute_value AND a.attribute_type = r.attribute_type AND @budget_year BETWEEN a.year_from AND a.year_to
JOIN tco_sync_company_setup cs ON a.fk_tenant_id = cs.sub_tenant_id AND a.pk_attribute_id = cs.sync_value_sub_tenant AND cs.fk_org_version = @orgversion AND cs.sync_type = 'AdminTenantSync_Relation_Value'
WHERE d.budget_year = @budget_year
GROUP BY d.fk_tenant_id

UNION ALL

SELECT 
@tenantid as fk_tenant_id
,@budget_year as budget_year
,d.fk_tenant_id
,0 as finplan_amt_main
,sum(d.year_1_amount) as finplan_amt_sub
,0 as org_bud_main
,0 as org_bud_sub
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN tco_sync_company_setup cs ON d.fk_tenant_id = cs.sub_tenant_id AND cs.fk_org_version = @orgversion AND cs.sync_type != 'AdminTenantSync_Relation_Value'
WHERE d.budget_year = @budget_year 
GROUP BY d.fk_tenant_id

UNION ALL

SELECT 
@tenantid as fk_tenant_id
,@budgetyear as budget_year
,s.sub_tenant_id
,0 as finplan_amt_main
,0 as finplan_amt_sub
,sum(amount_year_1) as org_bud_main
,0 as org_bud_sub
FROM tbu_trans_detail_original t
JOIN tco_org_hierarchy oh ON t.fk_tenant_id = oh.fk_tenant_id AND t.department_code = oh.fk_department_code AND oh.fk_org_version = @orgversion
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
WHERE t.fk_tenant_id = @tenantid AND t.budget_year = @budgetyear
GROUP BY s.sub_tenant_id

UNION ALL


SELECT 
@tenantid as fk_tenant_id
,@budgetyear as budget_year
,d.fk_tenant_id
,0 as finplan_amt_main
,0 as finplan_amt_sub
,0 as org_bud_main
,sum(amount_year_1) as org_bud_sub
FROM tbu_trans_detail_original d
JOIN tco_departments dep ON dep.fk_tenant_id = d.fk_tenant_id AND dep.pk_department_code = d.department_code AND @budget_year BETWEEN dep.year_from AND dep.year_to
JOIN tco_org_version ov ON dep.fk_tenant_id = ov.fk_tenant_id AND @budget_year * 100 + 01 BETWEEN ov.period_from AND ov.period_to
JOIN tco_org_hierarchy oh ON oh.fk_tenant_id = d.fk_tenant_id AND oh.fk_department_code = d.department_code AND oh.fk_org_version = ov.pk_org_version
JOIN tco_relation_values r ON r.fk_tenant_id = d.fk_tenant_id AND d.department_code BETWEEN r.relation_value_from AND r.relation_value_to AND @budget_year BETWEEN r.year_from AND r.year_to
JOIN tco_attribute_values a ON a.fk_tenant_id = r.fk_tenant_id AND a.pk_attribute_id = r.attribute_value AND a.attribute_type = r.attribute_type AND @budget_year BETWEEN a.year_from AND a.year_to
JOIN tco_sync_company_setup cs ON a.fk_tenant_id = cs.sub_tenant_id AND a.pk_attribute_id = cs.sync_value_sub_tenant AND cs.fk_org_version = @orgversion AND cs.sync_type = 'AdminTenantSync_Relation_Value'
WHERE d.budget_year = @budget_year
GROUP BY d.fk_tenant_id

UNION ALL


SELECT 
@tenantid as fk_tenant_id
,@budgetyear as budget_year
,d.fk_tenant_id
,0 as finplan_amt_main
,0 as finplan_amt_sub
,0 as org_bud_main
,sum(amount_year_1) as org_bud_sub
FROM tbu_trans_detail_original d
JOIN tco_sync_company_setup cs ON d.fk_tenant_id = cs.sub_tenant_id AND cs.fk_org_version = @orgversion AND cs.sync_type != 'AdminTenantSync_Relation_Value'
WHERE d.budget_year = @budget_year
GROUP BY d.fk_tenant_id

) S
GROUP BY fk_tenant_id, budget_year, sub_tenant_id



----------------



DELETE FROM [tbu_sync_original_budget] WHERE fk_tenant_id = @tenantid AND budget_year = @budgetyear

INSERT INTO [tbu_sync_original_budget] (
fk_tenant_id
,budget_year
,sub_tenant_id
,finplan_amt_main
,finplan_amt_sub
,org_bud_main
,org_bud_sub
,updated
,updated_by
)
SELECT
fk_tenant_id,
budget_year
,sub_tenant_id
,SUM(finplan_amt_main)
,SUM(finplan_amt_sub)
,SUM(org_bud_main)
,SUM(org_bud_sub)
,getdate()
,@userid
FROM #budgetdata
GROUP BY fk_tenant_id, budget_year, sub_tenant_id

RETURN 0
GO