CREATE OR ALTER PROCEDURE procLockCostEmploymentData
@tenant_id INT,
@budget_year INT,
@udtDepartmentCode udtDepartmentCodes readonly
AS
BEGIN

--Get distinct account code for batch processing
DECLARE @account_codeTable TABLE(
    account_Code varchar(50) NOT NULL,
	department_code varchar(50) NOT NULL
);

INSERT INTO @account_codeTable
SELECT distinct fk_account_code,fk_department_code FROM tbu_employments
where budget_year = @budget_year and fk_tenant_id=@tenant_id and fk_department_code in (select department_code from @udtDepartmentCode);

 --Delete existing rows
 DELETE FROM tbu_employments_cost WHERE budget_year = @budget_year and fk_tenant_id=@tenant_id  
 and fk_department_code in (select department_code from @udtDepartmentCode);


--Create cursor and insert the records
SET NOCOUNT ON
DECLARE @accountCode varchar(50)
DECLARE @departmentCode varchar(50)
DECLARE cur_insert CURSOR
STATIC FOR 
select account_Code,department_code from @account_codeTable
OPEN cur_insert
IF @@CURSOR_ROWS > 0
 BEGIN 
 FETCH NEXT FROM cur_insert INTO @accountCode,@departmentCode
 WHILE @@Fetch_status = 0
 BEGIN
 --Insert new rows
INSERT INTO tbu_employments_cost(
								 pk_employment_id,
								 fk_tenant_id, 
                                 budget_year,
                                 fk_res_id,
                                 res_name,
								 fk_emp_type_id,
								 fk_position_id,
								 hjemmel_id,
								 pension_type,
								 fk_account_code,
								 fk_department_code,
								 fk_function_code,
								 fk_project_code,
								 free_dim_1,
								 free_dim_2,
								 free_dim_3,
								 free_dim_4,
								 amount_existing_salary,
								 amount_salary_month,
								 amount_salary_year,
								 start_period,
								 end_period,
								 position_pct,
								 seniority_date,
								 yearly_budget,
								 amount_pension,
								 amount_holiday,
								 amount_aga_salary,
								 amount_aga_pension,
								 amount_aga_holiday,
								 external_reference,
								 updated,
								 updated_by,
								 fk_holiday_type_id,
								 comments,
								 fk_add_on_comments,
								 change_flag,
								 forecast_change_flag
                                )

						(SELECT	 pk_employment_id,
								 fk_tenant_id, 
                                 budget_year,
                                 fk_res_id,
                                 res_name,
								 fk_emp_type_id,
								 fk_position_id,
								 hjemmel_id,
								 pension_type,
								 fk_account_code,
								 fk_department_code,
								 fk_function_code,
								 fk_project_code,
								 free_dim_1,
								 free_dim_2,
								 free_dim_3,
								 free_dim_4,
								 amount_existing_salary,
								 amount_salary_month,
								 amount_salary_year,
								 start_period,
								 end_period,
								 position_pct,
								 seniority_date,
								 yearly_budget,
								 amount_pension,
								 amount_holiday,
								 amount_aga_salary,
								 amount_aga_pension,
								 amount_aga_holiday,
								 external_reference,
								 updated,
								 updated_by,
								 fk_holiday_type_id,
								 comments,
								 fk_add_on_comments,
								 change_flag,
								 forecast_change_flag
						 FROM tbu_employments WHERE budget_year = @budget_year and fk_tenant_id=@tenant_id and fk_account_code= @accountCode and fk_department_code=@departmentCode)

--Delete from add on original table
  DELETE FROM tbu_employments_add_on_cost WHERE fk_employment_id in (SELECT pk_employment_id FROM 
  tbu_employments_cost WHERE budget_year = @budget_year and fk_tenant_id=@tenant_id and fk_account_code= @accountCode and fk_department_code=@departmentCode)

INSERT INTO tbu_employments_add_on_cost(
								fk_tenant_id,
								fk_employment_id,
								ext_add_code,
								add_on_name,
								fk_account_code,
								fk_department_code,
								fk_function_code,
								fk_project_code,
								free_dim_1,
								free_dim_2,
								free_dim_3,
								free_dim_4,
								original_amount,
								amount_month,
								amount_year,
								start_period,
								end_period,
								pension_flag,
								holiday_flag,
								--social_expense_flag,
								tax_flag,
								updated,
								updated_by
                                )

						(SELECT	fk_tenant_id,
								fk_employment_id,
								ext_add_code,
								add_on_name,
								fk_account_code,
								fk_department_code,
								fk_function_code,
								fk_project_code,
								free_dim_1,
								free_dim_2,
								free_dim_3,
								free_dim_4,
								original_amount,
								amount_month,
								amount_year,
								start_period,
								end_period,
								pension_flag,
								holiday_flag,
								--social_expense_flag,
								tax_flag,
								updated,
								updated_by
						 FROM tbu_employments_add_on WHERE fk_employment_id in (SELECT pk_employment_id FROM 
						 tbu_employments_original WHERE budget_year = @budget_year and fk_tenant_id=@tenant_id and fk_account_code= @accountCode and fk_department_code=@departmentCode))

 FETCH NEXT FROM cur_insert INTO @accountCode,@departmentCode
 END
END
CLOSE cur_insert
DEALLOCATE cur_insert
SET NOCOUNT OFF 

END
