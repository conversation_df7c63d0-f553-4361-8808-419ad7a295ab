CREATE OR ALTER PROCEDURE [dbo].[prcCopyGtiStatusRiskDesc]
    @fk_tenant_id INT, @forecast_period INT
AS 
DECLARE @flag_name varchar(30), @flag_status INT, @flag INT, @last_reported_period INT, @budget_year INT, @last_reported_budget_year INT, @tmp varchar(6), @tmp1 varchar(6)
SET @flag_name = 'MR_COPYREPORT_ORGLEVEL'
SET @flag_status = (SELECT flag_status FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id  AND flag_name = @flag_name)

SET @tmp =  CAST(@forecast_period AS varchar)
SET @tmp1 = SUBSTRING(@tmp, 1, 4)
SET @budget_year = CAST(@tmp1 AS int)

if (@flag_status <> 0 OR @flag_status IS NOT NULL)
BEGIN 

    -- For  goals desc --
    SET @last_reported_period = (select max(t3.forecast_period) from tco_goals t1 
                INNER JOIN tco_goals_distribution t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_goal_id = t2.fk_goal_id 
				LEFT OUTER JOIN tmr_city_goals_status t3 ON t1.fk_tenant_id = t3.fk_tenant_id AND t2.fk_goal_id = t3.fk_goal_id AND t2.pk_goal_distribution_id = t3.fk_goal_distribution_id
				AND t3.forecast_period < @forecast_period AND t3.fk_goal_distribution_id is not null and t3.fk_tenant_id = @fk_tenant_id
				 where t1.fk_tenant_id = @fk_tenant_id and t2.org_level >=@flag_status and t3.status_desc <> '')
	IF (@last_reported_period IS NOT NULL)
    BEGIN

    SET @tmp =  CAST(@last_reported_period AS varchar)
    SET @tmp1 = SUBSTRING(@tmp, 1, 4)
    SET @last_reported_budget_year = CAST(@tmp1 AS int)

    DELETE from tmr_city_goals_status where pk_id in (
                select t3.pk_id from tco_goals t1 
                INNER JOIN tco_goals_distribution t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_goal_id = t2.fk_goal_id 
				LEFT OUTER JOIN tmr_city_goals_status t3 ON t1.fk_tenant_id = t3.fk_tenant_id AND t2.fk_goal_id = t3.fk_goal_id AND t2.pk_goal_distribution_id = t3.fk_goal_distribution_id
				AND t3.forecast_period = @forecast_period AND t3.fk_goal_distribution_id is not null and t3.fk_tenant_id = @fk_tenant_id
				where t1.fk_tenant_id = @fk_tenant_id and t2.org_level >=@flag_status and t1.budget_year = @budget_year
            )

    INSERT INTO tmr_city_goals_status (fk_tenant_id,fk_goal_id, status_desc_id_history,isReported,forecast_period,updated,updated_by,status_desc,fk_goal_distribution_id)
                select distinct t3.fk_tenant_id,t3.fk_goal_id, t3.status_desc_id_history,t3.isReported,@forecast_period,t3.updated,t3.updated_by,t3.status_desc,t3.fk_goal_distribution_id from tco_goals t1 
                INNER JOIN tco_goals_distribution t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_goal_id = t2.fk_goal_id 
				LEFT OUTER JOIN tmr_city_goals_status t3 ON t1.fk_tenant_id = t3.fk_tenant_id AND t2.fk_goal_id = t3.fk_goal_id AND t2.pk_goal_distribution_id = t3.fk_goal_distribution_id
				AND t3.forecast_period = @last_reported_period AND t3.fk_goal_distribution_id is not null and t3.fk_tenant_id = @fk_tenant_id
				where t1.fk_tenant_id = @fk_tenant_id and t1.budget_year = @last_reported_budget_year and t2.org_level >=@flag_status and t3.status_desc <> ''
	 END


     --- for rest ----

     SET  @last_reported_period = (select max(t3.forecast_period) from tco_targets t1 
        INNER JOIN  tco_targets_distribution t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_target_id = t2.fk_target_id
        INNER JOIN tmr_effect_target_status t3 on t1.fk_tenant_id = t3.fk_tenant_id and t1.pk_target_id = t3.fk_target_id 
        where t1.fk_tenant_id = @fk_tenant_id  and t2.org_level >=@flag_status and (t3.status_desc <> '' or t3.target_status_desc <> '' or t3.indicator_value <> '') and t3.forecast_period < @forecast_period )

    IF (@last_reported_period IS NOT NULL)
    BEGIN

        SET @tmp =  CAST(@last_reported_period AS varchar)
        SET @tmp1 = SUBSTRING(@tmp, 1, 4)
        SET @last_reported_budget_year = CAST(@tmp1 AS int)

        delete  from tmr_effect_target_status where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period and pk_id in 
        (
        select t3.pk_id from tco_targets t1 
        INNER JOIN  tco_targets_distribution t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_target_id = t2.fk_target_id
        INNER JOIN tmr_effect_target_status t3 on t1.fk_tenant_id = t3.fk_tenant_id and t1.pk_target_id = t3.fk_target_id 
        where t1.fk_tenant_id = @fk_tenant_id  and t2.org_level >=@flag_status  and t1.budget_year = @budget_year and t3.forecast_period = @forecast_period
        )

    INSERT INTO tmr_effect_target_status (fk_tenant_id,fk_target_id,fk_goal_id,value_type,indicator_value,status_desc_id_history,isReported,forecast_period,updated,updated_by,
            fk_target_detail_id,status_desc,fk_target_ind_id,fk_indicator_code,target_status_desc,target_status_desc_history,is_target_reported,fk_target_distribution_id)

        select distinct t3.fk_tenant_id,t3.fk_target_id,t3.fk_goal_id,t3.value_type,t3.indicator_value,t3.status_desc_id_history,t3.isReported,@forecast_period,t3.updated,t3.updated_by,
                    t3.fk_target_detail_id,t3.status_desc,t3.fk_target_ind_id,t3.fk_indicator_code,t3.target_status_desc,t3.target_status_desc_history,t3.is_target_reported,t3.fk_target_distribution_id
        from tco_targets t1 
        INNER JOIN  tco_targets_distribution t2 ON t1.fk_tenant_id = t2.fk_tenant_id AND t1.pk_target_id = t2.fk_target_id
        INNER JOIN tmr_effect_target_status t3 on t1.fk_tenant_id = t3.fk_tenant_id and t1.pk_target_id = t3.fk_target_id 
        where t1.fk_tenant_id = @fk_tenant_id  and t2.org_level >=@flag_status and t1.budget_year = @last_reported_budget_year and (t3.status_desc <> '' or t3.target_status_desc <> ''  or t3.indicator_value <> ''  ) and t3.forecast_period = @last_reported_period

	END
    


END
RETURN 0