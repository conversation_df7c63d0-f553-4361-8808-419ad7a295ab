CREATE OR ALTER PROCEDURE [dbo].[prcInsertDocVersionContent]
	@fk_tenant_id int,
	@budget_year int,
	@bud_version int
AS
begin
	--Generate header table with unique accounting combinations
    INSERT INTO [dbo].[tbu_budget_version_header]
               ([pk_trans_id]
               ,[fk_budget_version]
               ,[fk_account_code]
               ,[fk_department_code]
               ,[fk_function_code]
               ,[fk_project_code]
               ,[free_dim_1]
               ,[free_dim_2]
               ,[free_dim_3]
               ,[free_dim_4])

    select 
			    [pk_trans_id] = newid()
               ,[fk_budget_version] = @bud_version
               ,[fk_account_code]
               ,[fk_department_code] = department_code
               ,[fk_function_code]
               ,[fk_project_code]
               ,[free_dim_1]
               ,[free_dim_2]
               ,[free_dim_3]
               ,[free_dim_4]
    FROM tbu_trans_detail a
    JOIN tco_user_adjustment_codes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_adjustment_code = b.pk_adj_code and b.status = 1
    WHERE a.fk_tenant_id = @fk_tenant_id
    AND a.budget_year = @budget_year
    GROUP BY [fk_account_code]
               ,department_code
               ,[fk_function_code]
               ,[fk_project_code]
               ,[free_dim_1]
               ,[free_dim_2]
               ,[free_dim_3]
               ,[free_dim_4]


    --Generate trans table with period amounts while fetching trans ID from header table	   
    INSERT INTO [dbo].[tbu_budget_version_trans]
               ([pk_id]
               ,[fk_trans_id]
               ,[period]
               ,[amount])

    select 
			    [pk_id] = newid()
               ,fk_trans_id = c.pk_trans_id
		       ,[period] = a.period
		       ,amount = SUM(amount_year_1)
    FROM tbu_trans_detail a
    JOIN tco_user_adjustment_codes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_adjustment_code = b.pk_adj_code and b.status = 1
    JOIN [dbo].[tbu_budget_version_header] c
    ON	a.fk_account_code			= c.fk_account_code
        AND a.department_code		= c.fk_department_code
        AND a.[fk_function_code]	= c.fk_function_code
        AND a.[fk_project_code]		= c.fk_project_code
        AND a.[free_dim_1]			= c.free_dim_1
        AND a.[free_dim_2]			= c.free_dim_2
        AND a.[free_dim_3]			= c.free_dim_3
        AND a.[free_dim_4]			= c.free_dim_4
    WHERE a.fk_tenant_id = @fk_tenant_id
    AND a.budget_year = @budget_year
    AND c.fk_budget_version = @bud_version
    GROUP BY c.pk_trans_id,a.period
end

GO