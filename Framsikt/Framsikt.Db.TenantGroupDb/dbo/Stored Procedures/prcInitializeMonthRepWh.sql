CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMonthRepWh]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT
AS
	

DECLARE @budget_year INT
DECLARE @periods_remaining INT
DECLARE @periods_ytd INT
DECLARE @month_ytd dec(18,6)
DECLARE @continue INT
DECLARE @rowcount INT
DECLARE @last_period INT
DECLARE @ub_period INT
DECLARE @org_version VARCHAR(25) 
DECLARE @inv_org_level INT
DECLARE @language VARCHAR(10)

DECLARE @report1A varchar(25)
DECLARE @report1B varchar (25)

SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)


SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
SET @budget_year = @forecast_period/100;
SET @periods_ytd= convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))
SET @periods_remaining = 12 - @periods_ytd;
SET @month_ytd = (CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))
SET @last_period = (@budget_year*100)+12
SET @ub_period = (@budget_year*100)+13




SET @report1A = '54_DRIFTA'
SET @report1B = '54_DRIFTB'

IF @forecast_period <= 202113
BEGIN

SET @report1A = '54_DRIFTA_2021'
SET @report1B = '54_DRIFTB_2021'

END



DECLARE @tbl_reporting_line as table (report varchar(50)
, fk_kostra_account_code VARCHAR(25)
, line_group_id INT
, line_group varchar(200)
, line_item_id INT
, line_item varchar(150))


INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)
SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id, 
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),
line_item_id,
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))
FROM gmd_reporting_line rl
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @fk_tenant_id AND tg.Language = @language
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @fk_tenant_id AND tI.Language = @language
WHERE rl.report = @report1A


DECLARE @temp_table_1 TABLE 
	(fk_tenant_id INT NOT NULL, 
    forecast_period INT NOT NULL, 
	period INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
    free_dim_1 NVARCHAR(25) NOT NULL, 
    free_dim_2 NVARCHAR(25) NOT NULL, 
    free_dim_3 NVARCHAR(25) NOT NULL, 
    free_dim_4 NVARCHAR(25) NOT NULL, 
    bud_amt_year DECIMAL(18, 2) NOT NULL, 
    bud_amt_ytd DECIMAL(18, 2) NOT NULL, 
    bud_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_ytd DECIMAL(18, 2) NOT NULL, 
    actual_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_last_ytd DECIMAL(18, 2) NOT NULL,
    tech_amt_budget DECIMAL(18, 2) NOT NULL, 
    tech_amt_running DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	actual_amt_running DECIMAL(18, 2) NOT NULL, 	
	[fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',
	unaprv_bud_change DECIMAL(18, 2) DEFAULT(0) NOT NULL, 
	fk_user_adjustment_code NVARCHAR(25) DEFAULT '' NOT NULL,
	description NVARCHAR(700) DEFAULT '' NOT NULL)
	 ;


CREATE TABLE #temp_table_2
	(fk_tenant_id INT NOT NULL, 
    forecast_period INT NOT NULL, 
	period INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
    free_dim_1 NVARCHAR(25) NOT NULL, 
    free_dim_2 NVARCHAR(25) NOT NULL, 
    free_dim_3 NVARCHAR(25) NOT NULL, 
    free_dim_4 NVARCHAR(25) NOT NULL, 
    bud_amt_year DECIMAL(18, 2) NOT NULL, 
    bud_amt_ytd DECIMAL(18, 2) NOT NULL, 
    bud_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_ytd DECIMAL(18, 2) NOT NULL, 
    actual_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_last_ytd DECIMAL(18, 2) NOT NULL,
    tech_amt_budget DECIMAL(18, 2) NOT NULL, 
    tech_amt_running DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	actual_amt_running DECIMAL(18, 2) NOT NULL, 
	[1A_line] bit DEFAULT ((0)) NOT NULL,
	[line_item_id] INT DEFAULT (0) NOT NULL,
	[line_item] NVARCHAR(200) DEFAULT(''),
	[line_group_id] INT DEFAULT (0) NOT NULL,
	[line_group] NVARCHAR(200) DEFAULT(''),
	[fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',
	unaprv_bud_change DECIMAL(18, 2) DEFAULT(0) NOT NULL, 
	fk_user_adjustment_code NVARCHAR(25) DEFAULT '' NOT NULL,
	description NVARCHAR(700) DEFAULT '' NOT NULL);

DECLARE @calc_table TABLE (pk_id INT IDENTITY NOT NULL,
	forecast_period INT NOT NULL,
    fk_tenant_id INT NOT NULL, 
	fk_account_code NVARCHAR (25) NOT NULL,
	department_code NVARCHAR (25) NOT NULL, 
	fk_function_code NVARCHAR (25) NOT NULL,
    factor float NOT NULL, 
    gl_amount_ytd DECIMAL(18, 2) NOT NULL, 
    budget_amount_ytd DECIMAL(18, 2) NOT NULL,
	budget_amount_period DECIMAL(18, 2)  NULL,
	bud_amt_year DECIMAL(18, 2)  NULL,
	UNIQUE CLUSTERED(fk_tenant_id, forecast_period, fk_account_code,department_code,fk_function_code)
)

DECLARE @period_table TABLE (pk_id INT IDENTITY NOT NULL,
	period INT NOT NULL,
    fk_tenant_id INT NOT NULL) 

	
DECLARE @wh_table as TABLE (
	fk_tenant_id int NOT NULL,
	forecast_period int NOT NULL,
	finished_year int not null,
	fk_investment_id int NOT NULL,
	fk_main_project_code NVARCHAR(25) NOT NULL,
	investment_name nvarchar(100) NOT NULL,
	fk_account_code nvarchar(25) NOT NULL,
	fk_department_code nvarchar(25) NOT NULL,
	fk_function_code nvarchar(25) NOT NULL,
	fk_project_code nvarchar(25) NOT NULL,
	free_dim_1 nvarchar(25) NOT NULL,
	free_dim_2 nvarchar(25) NOT NULL,
	free_dim_3 nvarchar(25) NOT NULL,
	free_dim_4 nvarchar(25) NOT NULL,
	inv_org_planned_amt decimal(18, 2) NOT NULL,
	inv_change_amt decimal(18, 2) NOT NULL,
	inv_total_amt decimal(18, 2) NOT NULL,
	inv_amt_year decimal(18, 2) NOT NULL,
	actual_amt_ytd decimal(18, 2) NOT NULL,
	actual_amt_total decimal(18, 2) NOT NULL,
	actual_amt_last_year decimal(18,2) default 0 NOT NULL,
	updated datetime NOT NULL,
	updated_by int NOT NULL,
	fk_prog_code NVARCHAR(50)  NULL,
	fk_portfolio_code NVARCHAR(25) NULL, 
	type NVARCHAR(2) NOT NULL,
	is_vat_row bit not null default(0),
	approval_cost decimal(18, 2) NOT NULL,
	unaprv_bud_change DECIMAL(18, 2) DEFAULT(0) NOT NULL);

	

DECLARE @inv_owner_table TABLE
(
	[pk_id] BIGINT NOT NULL IDENTITY,
	[fk_investment_id] INT NOT NULL, 
	org_id_header NVARCHAR(25) NOT NULL,
	org_id_detail NVARCHAR(25) NOT NULL
)


SET @inv_org_level = 2

IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1) = 1
begin
SET @inv_org_level = (
 SELECT org_level = 
CASE	WHEN param_value = 'org_id_2' THEN 2 
		WHEN param_value = 'org_id_3' THEN 3
		WHEN param_value = 'org_id_4' THEN 4
		WHEN param_value = 'org_id_5' THEN 5 
ELSE 2 END FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1)
end


SELECT * 
INTO #rep_line_1B
FROM gmd_reporting_line
WHERE report = @report1B

--IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = '1B_EXLC_FINANCE_ROWS' AND active = 1 AND param_value = 'TRUE'
--AND fk_tenant_id = @fk_tenant_id) >=1
--BEGIN

--DELETE FROM #rep_line_1B WHERE line_group_id = 12
--END

BEGIN 

INSERT INTO @period_table (period, fk_tenant_id)
VALUES 
((@budget_year*100)+1, @fk_tenant_id),
((@budget_year*100)+2, @fk_tenant_id),
((@budget_year*100)+3, @fk_tenant_id),
((@budget_year*100)+4, @fk_tenant_id),
((@budget_year*100)+5, @fk_tenant_id),
((@budget_year*100)+6, @fk_tenant_id),
((@budget_year*100)+7, @fk_tenant_id),
((@budget_year*100)+8, @fk_tenant_id),
((@budget_year*100)+9, @fk_tenant_id),
((@budget_year*100)+10, @fk_tenant_id),
((@budget_year*100)+11, @fk_tenant_id),
((@budget_year*100)+12, @fk_tenant_id);

DELETE FROM @period_table WHERE period <= @forecast_period

END


SELECT fk_tenant_id,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year,fk_prog_code,
SUM(amount) AS amount
INTO #TEMP_ACC_DATA
FROM tfp_accounting_data 
WHERE fk_tenant_id = @fk_tenant_id AND gl_year IN  (@budget_year, @budget_year-1)
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
HAVING SUM(amount) != 0
OPTION (RECOMPILE);



INSERT INTO #TEMP_ACC_DATA (fk_tenant_id,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year,fk_prog_code,amount)
SELECT a.fk_tenant_id,a.period, a.fk_account_code, a.department_code, a.fk_function_code,a.fk_project_code,
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, a.gl_year,a.fk_prog_code,
SUM(amount) AS amount
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,dateFrom) AND DATEPART(YEAR,dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id IN (1010,1020,2020)
WHERE fk_tenant_id = @fk_tenant_id AND gl_year < @budget_year-1
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
HAVING SUM(amount) != 0
OPTION (RECOMPILE);

PRINT 'Initilize operations warehouse '  + convert(varchar(400),GETDATE());

-- Bug 97732  blank adjustmentcodes no longer included

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code,
fk_user_adjustment_code, description)
SELECT a.fk_tenant_id, @forecast_period,a.period, a.fk_account_code, a.department_code, a.fk_function_code,a.fk_project_code,
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, SUM(a.amount_year_1) as  bud_amt_year, 
bud_amt_ytd = 
CASE WHEN period <= @forecast_period THEN SUM(amount_year_1) ELSE 0 END,
bud_amt_period = CASE WHEN period = @forecast_period THEN SUM(amount_year_1) ELSE 0 END, 
0 AS actual_amt_year, 0 AS actual_amt_ytd, 0 AS actual_amt_period,0,0, 
tech_amt_budget = 
CASE WHEN period > @forecast_period THEN SUM(amount_year_1) ELSE 0 END,
0 AS tech_amt_running, 0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, 
'' as fk_prog_code, a.fk_adjustment_code, a.description
FROM tbu_trans_detail a
 JOIN tco_accounts b ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.budget_year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART (YEAR, b.dateTo)
 JOIN gco_kostra_accounts k ON b.fk_kostra_account_code = k.pk_kostra_account_code AND k.type = 'operations'
 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
GROUP BY a.fk_tenant_id, a.period, a.fk_account_code, a.department_code, a.fk_function_code ,a.fk_project_code,
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, a.fk_adjustment_code, a.description
HAVING SUM(amount_year_1)!= 0
OPTION (RECOMPILE);


PRINT 'Insert from accounting data 1'  + convert(varchar(400),GETDATE());

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year,actual_amt_running,fk_prog_code)
SELECT fk_tenant_id, @forecast_period,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 as  bud_amt_year, 0 as bud_amt_ytd,
0 as bud_amt_period, 
SUM(amount) AS actual_amt_year, actual_amt_ytd = 
CASE WHEN period <= @forecast_period THEN SUM(amount) ELSE 0 END,
actual_amt_period= 
CASE WHEN period = @forecast_period THEN SUM(amount) ELSE 0 END, 0,0,
tech_amt_budget = 
CASE WHEN period <= @forecast_period THEN SUM(amount) ELSE 0 END,
tech_amt_running = 
CASE WHEN period <= @forecast_period THEN SUM(amount)+(SUM(amount)/@periods_ytd*@periods_remaining) ELSE 0 END, 
0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, fk_prog_code
FROM #TEMP_ACC_DATA 
WHERE fk_tenant_id = @fk_tenant_id AND gl_year = @budget_year
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_prog_code
HAVING SUM(amount) != 0
OPTION (RECOMPILE);


PRINT 'Insert from accounting data 2'  + convert(varchar(400),GETDATE());

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year,actual_amt_running, fk_prog_code)
SELECT fk_tenant_id, @forecast_period,period+100, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 as  bud_amt_year, 0 as bud_amt_ytd,
0 as bud_amt_period, 0 AS actual_amt_year, 0 AS actual_amt_ytd,
0 AS actual_amt_period, 
SUM(amount) AS actual_amt_last_year,
actual_amt_last_ytd = CASE WHEN period <= @forecast_period -100 THEN SUM(amount) ELSE 0 END,
0 AS tech_amt_budget,0 AS tech_amt_running,
0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, fk_prog_code
FROM #TEMP_ACC_DATA 
WHERE fk_tenant_id = @fk_tenant_id AND gl_year = @budget_year -1 
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_prog_code
HAVING SUM(amount) != 0
OPTION (RECOMPILE);

/*
PRINT 'Insert from accounting data 3 - actual amount running'  + convert(varchar(400),GETDATE());

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year,actual_amt_running, fk_prog_code)
SELECT fk_tenant_id, @forecast_period,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 as  bud_amt_year, 0 as bud_amt_ytd,
0 as bud_amt_period, 0 AS actual_amt_year, 0 AS actual_amt_ytd,
0 AS actual_amt_period, 
0 AS actual_amt_last_year,
actual_amt_last_ytd = 0,
0 AS tech_amt_budget,0 AS tech_amt_running,
0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, SUM(amount) as actual_amt_running, fk_prog_code
FROM #TEMP_ACC_DATA 
WHERE fk_tenant_id = @fk_tenant_id AND period between @forecast_period -99 AND @forecast_period 
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_prog_code
HAVING SUM(amount) != 0;
*/

PRINT 'Insert from original 1'  + convert(varchar(400),GETDATE());

WITH cte_trans_detail_original AS (
    SELECT 
        a.fk_tenant_id, 
        a.period, 
        a.fk_account_code, 
        a.department_code, 
        a.fk_function_code, 
        a.fk_project_code,
        a.free_dim_1, 
        a.free_dim_2, 
        a.free_dim_3, 
        a.free_dim_4, 
        a.fk_adjustment_code,
        SUM(a.amount_year_1) AS org_bud_amt_year
    FROM 
        tbu_trans_detail_original a
    WHERE 
        a.fk_tenant_id = @fk_tenant_id 
        AND a.budget_year = @budget_year
    GROUP BY 
        a.fk_tenant_id, 
        a.period, 
        a.fk_account_code, 
        a.department_code, 
        a.fk_function_code, 
        a.fk_project_code, 
        a.free_dim_1, 
        a.free_dim_2, 
        a.free_dim_3, 
        a.free_dim_4, 
        a.fk_adjustment_code
    HAVING 
        SUM(a.amount_year_1) != 0
)

INSERT INTO @temp_table_1 (
    fk_tenant_id, 
    forecast_period, 
    period, 
    fk_account_code, 
    fk_department_code, 
    fk_function_code,
    fk_project_code,
    free_dim_1, 
    free_dim_2, 
    free_dim_3, 
    free_dim_4,  
    bud_amt_year, 
    bud_amt_ytd, 
    bud_amt_period, 
    actual_amt_year, 
    actual_amt_ytd,
    actual_amt_period, 
    actual_amt_last_year, 
    actual_amt_last_ytd,  
    tech_amt_budget, 
    tech_amt_running,
    org_bud_amt_year, 
    org_bud_amt_last_year, 
    actual_amt_running,
    fk_prog_code, 
    fk_user_adjustment_code
)
SELECT 
    cte.fk_tenant_id, 
    @forecast_period,
    cte.period, 
    cte.fk_account_code, 
    cte.department_code, 
    cte.fk_function_code,
    cte.fk_project_code,
    cte.free_dim_1, 
    cte.free_dim_2, 
    cte.free_dim_3, 
    cte.free_dim_4,  
    0 AS bud_amt_year, 
    0 AS bud_amt_ytd,
    0 AS bud_amt_period, 
    0 AS actual_amt_year, 
    0 AS actual_amt_ytd, 
    0 AS actual_amt_period, 
    0 AS actual_amt_last_year,
    0 AS actual_amt_last_ytd,
    0 AS tech_amt_budget,
    0 AS tech_amt_running, 
    cte.org_bud_amt_year, 
    0 AS org_bud_amt_last_year, 
    0 AS actual_amt_running, 
    '' AS fk_prog_code, 
    cte.fk_adjustment_code
FROM 
    cte_trans_detail_original cte
JOIN 
    tco_accounts b 
    ON cte.fk_account_code = b.pk_account_code 
    AND cte.fk_tenant_id = b.pk_tenant_id 
    AND @budget_year BETWEEN DATEPART(YEAR, b.dateFrom) AND DATEPART(YEAR, b.dateTo)
JOIN 
    gco_kostra_accounts k 
    ON b.fk_kostra_account_code = k.pk_kostra_account_code 
    AND k.type = 'operations'
OPTION(RECOMPILE);


PRINT 'Insert from original 2'  + convert(varchar(400),GETDATE());

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period,actual_amt_last_year, actual_amt_last_ytd,  tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code)
SELECT fk_tenant_id, @forecast_period,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 as  bud_amt_year, 0 as bud_amt_ytd,
0, 
0 AS actual_amt_year, 0 AS actual_amt_ytd, 0 AS actual_amt_period, 0,0,
0,
0 AS tech_amt_running, 0 as org_bud_amt_year, SUM(amount_year_1)  AS org_bud_amt_last_year, 0 as actual_amt_running, '' as fk_prog_code
FROM tbu_trans_detail
WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @budget_year -1
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4
HAVING SUM(amount_year_1) != 0
OPTION (RECOMPILE);

PRINT 'Insert unapproved budget changes operations'

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code,
fk_user_adjustment_code, description, unaprv_bud_change)
SELECT d.fk_tenant_id, @forecast_period as forecast_period, @forecast_period as period, 
d.fk_account_code, d.department_code, d.function_code,d.project_code,
d.free_dim_1, d.free_dim_2, d.free_dim_3, d.free_dim_4,  
0 as bud_amt_year, 0 as bud_amt_ytd, 0 as bud_amt_period, 0 as actual_amt_year, 0 as actual_amt_ytd,
0 as actual_amt_period, 0 as actual_amt_last_year, 0 as actual_amt_last_ytd, 0 as tech_amt_budget, 0 as tech_amt_running,0 as org_bud_amt_year, 
0 as org_bud_amt_last_year, 0 as actual_amt_running, '' as fk_prog_code,
'' as fk_user_adjustment_code, '' as description, SUM(d.year_1_amount) as unaprv_bud_change
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.fk_tenant_id = d.fk_tenant_id AND h.pk_action_id = d.fk_action_id
--JOIN tfp_budget_changes ch ON ch.fk_tenant_id = d.fk_tenant_id AND ch.pk_change_id = d.fk_change_id AND ch.budget_year = d.budget_year  AND ch.org_budget_flag = 0 AND ch.not_approved = 1
JOIN tco_user_adjustment_codes uac ON d.fk_tenant_id = uac.fk_tenant_id AND d.fk_adj_code = uac.pk_adj_code AND uac.status = 0 AND uac.include_in_calculation = 1
WHERE d.fk_tenant_id = @fk_tenant_id AND d.budget_year = @budget_year
GROUP BY 
d.fk_tenant_id, 
d.fk_account_code, d.department_code, d.function_code,d.project_code,
d.free_dim_1, d.free_dim_2, d.free_dim_3, d.free_dim_4
OPTION (RECOMPILE);

PRINT 'Insert investments and financing budget '

INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period,actual_amt_last_year, actual_amt_last_ytd,  tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code,
unaprv_bud_change)
SELECT t.fk_tenant_id, @forecast_period forecast_period,@forecast_period as period, t.fk_account_code, 
t.department_code, t.fk_function_code,t.fk_project_code,
t.free_dim_1, t.free_dim_2, t.free_dim_3, t.free_dim_4,  
bud_amt_year = SUM(t.year_1_amount), 
bud_amt_ytd = SUM(t.year_1_amount), 
bud_amt_period = SUM(t.year_1_amount), 
0 AS actual_amt_year, 0 AS actual_amt_ytd, 0 AS actual_amt_period,0 AS actual_amt_last_year, 0 AS actual_amt_last_ytd,  
0 AS tech_amt_budget, 0 AS tech_amt_running,
org_bud_amt_year = CASE WHEN c.org_budget_flag = 1 THEN SUM(t.year_1_amount) 
						ELSE 0 END, 
org_bud_amt_last_year = CASE	WHEN c.org_budget_flag = 1 THEN SUM(t.year_1_amount)  
								ELSE 0 END, 
0 AS actual_amt_running,
t.fk_prog_code, 
unaprv_bud_change = 0 --Have to be fixed when we implement rebudgeting story
FROM tfp_inv_transactions t 
LEFT JOIN tfp_budget_changes c ON t.fk_tenant_id = c.fk_tenant_id AND t.fk_change_id = c.pk_change_id AND t.budget_year = c.budget_year
WHERE t.fk_tenant_id = @fk_tenant_id AND t.budget_year = @budget_year
GROUP BY t.fk_tenant_id, t.fk_account_code, t.department_code, t.fk_function_code ,t.fk_project_code,
t.free_dim_1, t.free_dim_2, t.free_dim_3, t.free_dim_4, t.fk_prog_code, c.org_budget_flag
HAVING SUM(t.year_1_amount) != 0
OPTION (RECOMPILE);

UPDATE @temp_table_1 SET fk_prog_code = 1
FROM @temp_table_1 T
INNER JOIN tco_accounts b ON T.fk_account_code = b.pk_account_code AND T.fk_tenant_id = b.pk_tenant_id
INNER JOIN gco_kostra_accounts k ON b.fk_kostra_account_code = k.pk_kostra_account_code AND k.type = 'investment'
WHERE fk_prog_code = '';




PRINT 'Delete investments from temptable '  + convert(varchar(400),GETDATE());

DELETE T FROM @temp_table_1 T
INNER JOIN tco_accounts b ON T.fk_account_code = b.pk_account_code AND T.fk_tenant_id = b.pk_tenant_id
INNER JOIN gco_kostra_accounts k ON b.fk_kostra_account_code = k.pk_kostra_account_code AND k.type = 'investment'; 



PRINT 'Calculate tech amounts'

INSERT INTO #temp_table_2 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, description, bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, 
fk_prog_code,fk_user_adjustment_code,
unaprv_bud_change)

SELECT fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, description, SUM(bud_amt_year), SUM(bud_amt_ytd), SUM(bud_amt_period), SUM(actual_amt_year), SUM(actual_amt_ytd),
SUM(actual_amt_period), SUM(actual_amt_last_year), SUM(actual_amt_last_ytd), SUM(tech_amt_budget), 0 as tech_amt_running, 
SUM(org_bud_amt_year), SUM(org_bud_amt_last_year), SUM(actual_amt_running),fk_prog_code,fk_user_adjustment_code,
SUM(unaprv_bud_change)
FROM @temp_table_1
GROUP BY fk_tenant_id, forecast_period,period,fk_account_code, fk_department_code, fk_function_code,fk_project_code,
 free_dim_1,  free_dim_2, free_dim_3, free_dim_4, description,fk_prog_code, fk_user_adjustment_code;


INSERT INTO @calc_table (forecast_period,fk_tenant_id,fk_account_code,department_code,fk_function_code,gl_amount_ytd, budget_amount_ytd,factor, bud_amt_year)
SELECT forecast_period,fk_tenant_id,fk_account_code,fk_department_code,fk_function_code,SUM(actual_amt_ytd), SUM(bud_amt_ytd),0 as factor, sum(bud_amt_year)
FROM @temp_table_1
GROUP BY forecast_period,fk_tenant_id,fk_account_code,fk_department_code,fk_function_code;


	UPDATE @calc_table SET factor = 
		CASE WHEN ROUND(budget_amount_ytd,0) = 0 THEN 0
		ELSE gl_amount_ytd/budget_amount_ytd
		END;

		UPDATE @calc_table SET budget_amount_period = gl_amount_ytd  / @month_ytd



UPDATE #temp_table_2 SET tech_amt_running = CASE	
	--WHEN ABS(b.bud_amt_year) >= 100 AND b.gl_amount_ytd = 0 THEN a.bud_amt_year
	WHEN a.period <= @forecast_period THEN a.actual_amt_ytd
	WHEN ABS(b.bud_amt_year) >= 100 THEN ROUND(a.bud_amt_year*b.factor,0)
	ELSE 0 END
FROM #temp_table_2 a, @calc_table b
WHERE a.forecast_period = b.forecast_period
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_account_code = b.fk_account_code
AND a.fk_department_code = b.department_code
AND a.fk_function_code = b.fk_function_code


INSERT INTO #temp_table_2 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running)
SELECT b.fk_tenant_id, b.forecast_period,c.period, b.fk_account_code, b.department_code, b.fk_function_code,'' as fk_project_code,
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4, 0 as bud_amt_year, 0 as bud_amt_ytd, 0 as bud_amt_period, 0 as actual_amt_year, 0 as actual_amt_ytd,
0 as actual_amt_period, 0 as actual_amt_last_year, 0 as actual_amt_last_ytd, 0 as tech_amt_budget, 
tech_amt_running= b.budget_amount_period, 0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS actual_amt_running
FROM @calc_table b, @period_table c
WHERE b.fk_tenant_id = c.fk_tenant_id
AND ABS (b.bud_amt_year) < 100
AND b.budget_amount_period is not null

Print 'Logic for fetching 1A status start ' + convert(varchar(400),GETDATE());

IF @forecast_period >= 202000 AND (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') > 0

BEGIN

PRINT 'Use 1A 2020 -logic'

UPDATE #temp_table_2 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND a.fk_department_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_DEPARTMENTS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = @report1A 

UPDATE #temp_table_2 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND a.fk_function_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = @report1A 


UPDATE #temp_table_2 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND c.line_group_id != 10
WHERE  c.report = @report1A 


UPDATE a SET a.[1A_line] = 0, a.line_group_id = 0, a.line_group = '', a.line_item_id=0, a.line_item = ''
FROM #temp_table_2 a 
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN #rep_line_1B rl ON rl.report = @report1B AND rl.fk_kostra_account_code = ac.fk_kostra_account_code
WHERE a.[1A_line] = 1 
AND a.fk_department_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
AND a.fk_function_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_FUNCTIONS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
AND a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period


-- 1B in budget forms keeps these accounts outside. If change here need to also change budget forms
DELETE a
FROM #temp_table_2 a 
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
WHERE ac.fk_kostra_account_code in ('1580','1980')

END



IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') = 0

BEGIN 

	print 'Use Framsikt 1a'

	UPDATE #temp_table_2
	SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.forecast_period/100 = ls.budget_year
	AND ls.priority = 0;

	UPDATE #temp_table_2
	SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.forecast_period/100 = ls.budget_year
	AND ls.priority = 5;	
	
	UPDATE #temp_table_2
	SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.forecast_period/100 = ls.budget_year
	AND ls.priority = 4;	
	
	UPDATE #temp_table_2
	SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.forecast_period/100 = ls.budget_year	
	AND ls.priority = 3;	
	
	UPDATE #temp_table_2
	SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.forecast_period/100 = ls.budget_year	
	AND ls.priority = 2;	
	
	UPDATE #temp_table_2
	SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.forecast_period/100 = ls.budget_year
	AND ls.priority = 1;


UPDATE #temp_table_2 set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'revenues_text'
AND t.pk_id = @fk_tenant_id)
WHERE [1A_line] = 1 AND line_group_id = 1

UPDATE #temp_table_2 set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'centralexpenses_text'
AND t.pk_id = @fk_tenant_id)
WHERE [1A_line] = 1 AND line_group_id = 100

UPDATE #temp_table_2 set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'financial_income_expenses_text'
AND t.pk_id = @fk_tenant_id)
WHERE [1A_line] = 1 AND line_group_id = 2

UPDATE #temp_table_2 set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'provisions_others_text'
AND t.pk_id = @fk_tenant_id)
WHERE [1A_line] = 1 AND line_group_id = 3

UPDATE #temp_table_2 set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE 
ls.context  = 'BudgetManagement'
AND ls.language = @language
AND ls.Id = 'bm_transfered_to_investments_text'
AND t.pk_id = @fk_tenant_id)
WHERE [1A_line] = 1 AND line_group_id = 4

UPDATE #temp_table_2 SET line_group_id = line_group_id * 1000
WHERE [1A_line] = 1 AND line_group_id IN (2,3,4);

IF (SELECT COUNT(*) FROM #temp_table_2 WHERE [1A_line] = 1 AND line_group_id = 3000) = 0
begin
print 'Insert blank row so at least one with line group id 3000 exists'

INSERT INTO #temp_table_2 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code,
unaprv_bud_change, [1A_line], line_group_id, line_group, line_item_id, line_item)
SELECT @fk_tenant_id as fk_tenant_id, @forecast_period as forecast_period,@forecast_period as period, 
MIN(ac.pk_account_code) as fk_account_code, '' as fk_department_code, '' as fk_function_code ,'' as fk_project_code,
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4,  0 as bud_amt_year, 0 as bud_amt_ytd, 0 as bud_amt_period,
0 as actual_amt_year, 0 as actual_amt_ytd,
0 as actual_amt_period, 0 as actual_amt_last_year, 0 as actual_amt_last_ytd, 0 as tech_amt_budget, 
0 as tech_amt_running, 0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 as actual_amt_running, 1 as fk_prog_code,
0 as unaprv_bud_change, 1 as [1A_line],
 rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item FROM gmd_reporting_line rl
join tco_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code and isActive = 1 and ac.pk_tenant_id = @fk_tenant_id
where report = 'b1a' and line_group_id = 3000 AND line_item_id = 330
GROUP BY rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item

UPDATE #temp_table_2 SET fk_function_code = (SELECT MIN(pk_function_code) FROM tco_functions WHERE pk_tenant_id = @fk_tenant_id AND isActive = 1)
WHERE line_group_id = 3000 AND line_item_id = 330 AND fk_function_code = ''

IF (SELECT COUNT(*)
FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' 
AND pc.active = 1 AND pc.fk_tenant_id = @fk_tenant_id)>=1

begin

UPDATE #temp_table_2 SET fk_department_code = (SELECT MIN(pc.param_value) 
FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' 
AND pc.active = 1 AND pc.fk_tenant_id = @fk_tenant_id)
WHERE line_group_id = 3000 AND line_item_id = 330 AND fk_department_code = ''

end

UPDATE #temp_table_2 SET fk_department_code = (SELECT MIN(pk_department_code) FROM tco_departments WHERE fk_tenant_id = @fk_tenant_id AND status = 1)
WHERE line_group_id = 3000 AND line_item_id = 330 AND fk_department_code = ''



end


END


Print 'Logic for fetching 1A status finish ' + convert(varchar(400),GETDATE());

UPDATE #temp_table_2 SET description = '' WHERE [1A_line] = 0 AND description != ''

PRINT 'Remove user adjustment code where not in use'

UPDATE a SET fk_user_adjustment_code = ''
FROM #temp_table_2 a
LEFT JOIN tco_user_adjustment_codes ad ON a.fk_tenant_id = ad.fk_tenant_id AND a.fk_user_adjustment_code = ad.pk_adj_code AND ad.is_original_flag = 0
WHERE ad.pk_adj_code is null

PRINT 'Delete from wareahouse detail '  + convert(varchar(400),GETDATE());

BEGIN
SET  @continue = 1

WHILE @continue = 1
BEGIN
    PRINT GETDATE()
    SET ROWCOUNT 50000
    BEGIN TRANSACTION
   DELETE FROM tmr_data_warehouse_period WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period;
	SET @rowcount = @@rowcount 
    COMMIT
    PRINT GETDATE()
    IF @rowcount = 0
    BEGIN
        SET @continue = 0
    END
END

END


SET ROWCOUNT 0;

PRINT 'insert into  warehouse detail '  + convert(varchar(400),GETDATE());

INSERT INTO [dbo].[tmr_data_warehouse_period]
           ([fk_tenant_id]
           ,[forecast_period]
           ,[period]
           ,[fk_account_code]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[bud_amt_year]
           ,[bud_amt_ytd]
           ,[bud_amt_period]
           ,[actual_amt_year]
           ,[actual_amt_ytd]
           ,[actual_amt_period]
           ,[tech_amt_budget]
           ,[tech_amt_running]
           ,[org_bud_amt_year]
           ,[org_bud_amt_last_year]
           ,[actual_amt_last_ytd]
           ,[updated]
           ,[updated_by]
           ,[actual_amt_running]
           ,[actual_amt_last_year]
		   ,[unaprv_bud_change])
SELECT fk_tenant_id, forecast_period, period,trim(fk_account_code), trim(fk_department_code), trim(fk_function_code),trim(fk_project_code),
trim(free_dim_1), trim(free_dim_2), trim(free_dim_3), trim(free_dim_4)
,[bud_amt_year]			=SUM([bud_amt_year])
,[bud_amt_ytd]			=SUM([bud_amt_ytd])
,[bud_amt_period]		=SUM([bud_amt_period])
,[actual_amt_year]		=SUM([actual_amt_year])
,[actual_amt_ytd]		=SUM([actual_amt_ytd])
,[actual_amt_period]	=SUM([actual_amt_period])
,[tech_amt_budget]		=SUM([tech_amt_budget])
,[tech_amt_running]		=SUM([tech_amt_running])
,[org_bud_amt_year]		=SUM([org_bud_amt_year])
,[org_bud_amt_last_year]=SUM([org_bud_amt_last_year])
,[actual_amt_last_ytd]	=SUM([actual_amt_last_ytd])
,getdate()
,@user_id
,actual_amt_running = 0
,actual_amt_last_year = SUM(actual_amt_last_year)
,unaprv_bud_change = SUM(unaprv_bud_change)
FROM #temp_table_2
GROUP BY [fk_tenant_id]
      ,[forecast_period]
      ,[period]
      ,[fk_account_code]
      ,[fk_department_code]
      ,[fk_function_code]
      ,[fk_project_code]
      ,[free_dim_1]
      ,[free_dim_2]
      ,[free_dim_3]
      ,[free_dim_4]
HAVING SUM([bud_amt_year]) <>0
OR	   SUM([bud_amt_ytd])			<>0
OR	   SUM([bud_amt_period])		<>0
OR	   SUM([actual_amt_year])		<>0
OR	   SUM([actual_amt_ytd])		<>0
OR	   SUM([actual_amt_period])		<>0
OR	   SUM([tech_amt_budget])		<>0
OR	   SUM([tech_amt_running])		<>0
OR	   SUM([org_bud_amt_year])		<>0
OR	   SUM([org_bud_amt_last_year])	<>0
OR	   SUM([actual_amt_last_ytd])	<>0
OR	   SUM(actual_amt_last_year)	<>0
OR	   SUM(unaprv_bud_change)		<>0


PRINT 'Delete from wareahouse '  + convert(varchar(400),GETDATE());

BEGIN
SET  @continue = 1

WHILE @continue = 1
BEGIN
    PRINT GETDATE()
    SET ROWCOUNT 50000
    BEGIN TRANSACTION
   DELETE FROM tmr_data_warehouse WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period;
	SET @rowcount = @@rowcount 
    COMMIT
    PRINT GETDATE()
    IF @rowcount = 0
    BEGIN
        SET @continue = 0
    END
END

END

SET ROWCOUNT 0;


PRINT 'insert into wareahouse '  + convert(varchar(400),GETDATE());

UPDATE #temp_table_2 SET fk_prog_code = (SELECT MIN(pk_prog_code) FROM tco_inv_program WHERE default_flag = 1 AND fk_tenant_id = @fk_tenant_id) WHERE fk_prog_code is null

PRINT 'update temptable 2 done';

INSERT INTO tmr_data_warehouse (fk_tenant_id, forecast_period,fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_ytd,  tech_amt_budget, tech_amt_running,org_bud_amt_year,org_bud_amt_last_year,updated, updated_by,
[1A_line], line_group_id, line_group, line_item_id, line_item, actual_amt_running, fk_prog_code, unaprv_bud_change, fk_user_adjustment_code,
description)
SELECT fk_tenant_id, forecast_period, trim(fk_account_code), trim(fk_department_code), trim(fk_function_code) ,trim(fk_project_code),
trim(free_dim_1), trim(free_dim_2), trim(free_dim_3), trim(free_dim_4), sum(bud_amt_year), SUM(bud_amt_ytd), SUM(bud_amt_period), SUM(actual_amt_last_year), SUM(actual_amt_ytd),
SUM(actual_amt_period), SUM(actual_amt_last_ytd), SUM(tech_amt_budget), SUM(tech_amt_running),
SUM(org_bud_amt_year),SUM(org_bud_amt_last_year), getdate(), @user_id,[1A_line], line_group_id, line_group, line_item_id, line_item,
sum(actual_amt_running), fk_prog_code, SUM(unaprv_bud_change),fk_user_adjustment_code,description
FROM #temp_table_2
GROUP BY fk_tenant_id, forecast_period,fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,[1A_line], line_group_id, line_group, line_item_id, line_item, fk_prog_code,
fk_user_adjustment_code,description;

PRINT 'Initilize operations warehouse done  '+ convert(varchar(400),GETDATE());


PRINT 'Initialize accounting warehouse ' + convert(varchar(400),GETDATE());

BEGIN
SET  @continue = 1

WHILE @continue = 1
BEGIN
    PRINT GETDATE()
    SET ROWCOUNT 50000
    BEGIN TRANSACTION
	DELETE FROM tmr_accounting_warehouse WHERE fk_tenant_id = @fk_tenant_id AND gl_year = @budget_year;
	SET @rowcount = @@rowcount 
    COMMIT
    PRINT GETDATE()
    IF @rowcount = 0
    BEGIN
        SET @continue = 0
    END
END

END

SET ROWCOUNT 0;

PRINT 'Delete from tmr_accounting warehouse done'  + convert(varchar(400),GETDATE());


INSERT INTO tmr_accounting_warehouse (fk_tenant_id,gl_year,type,monthrep_l1_value,monthrep_l2_value,service_id,service_name,acc_group_code,acc_group_name,amount)
SELECT   a.fk_tenant_id, a.gl_year, 

	type = CASE WHEN rl.line_group_id = 2 THEN 1 else 0 END,
	monthrep_l1_value = CASE
		WHEN p1.param_value = 'service_id_2' THEN ISNULL(sv.service_id_2,'')
		WHEN p1.param_value = 'org_id_2' THEN ISNULL(oh.org_id_2,'')
		WHEN p1.param_value = 'org_id_3' THEN ISNULL (oh.org_id_3, '')
		WHEN p1.param_value = 'service_id_3' THEN ISNULL (sv.service_id_3, '')
		ELSE ISNULL(oh.org_id_2,'') END, 
	monthrep_l2_value = CASE
		WHEN p2.param_value = 'service_id_2' THEN ISNULL(sv.service_id_2, '')
		WHEN p2.param_value = 'org_id_2' THEN ISNULL(oh.org_id_2, '')
		WHEN p2.param_value = 'org_id_3' THEN ISNULL(oh.org_id_3, '')
		WHEN p2.param_value = 'service_id_3' THEN ISNULL(sv.service_id_3, '')
		ELSE ISNULL(oh.org_id_2, '') END,
	service_id = '',
	service_name = '', 
	acc_group_code = convert(nvarchar(25),rl.line_item_id), acc_group_name = rl.line_item,
	SUM(a.amount) AS amount
		FROM #TEMP_ACC_DATA a
	JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = 'L2'
	LEFT JOIN tco_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id AND a.fk_function_code = sv.fk_function_code
	LEFT JOIN tco_org_hierarchy oh ON a.fk_tenant_id = oh.fk_tenant_id AND a.department_code = oh.fk_department_code AND oh.fk_org_version = @org_version 
	LEFT JOIN tco_parameters p1 ON p1.param_name = 'MONTHREP_LEVEL_1' AND p1.active = 1 AND a.fk_tenant_id = p1.fk_tenant_id
	LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_2' AND p2.active = 1 AND a.fk_tenant_id = p2.fk_tenant_id
WHERE a.fk_tenant_id = @fk_tenant_id AND a.gl_year = @budget_year
GROUP BY a.fk_tenant_id,gl_year,sv.service_id_2, sv.service_id_3, oh.org_id_2, oh.org_id_3,
rl.line_item_id,rl.line_item,rl.line_group_id, p1.param_value, p2.param_value;


PRINT 'Initialize accounting warehouse done '+ convert(varchar(400),GETDATE());

PRINT 'Update 1a_flag in lookup warehouse table START' + convert(varchar(400),GETDATE());


SELECT a.fk_tenant_id, a.gl_year, @forecast_period as forecast_period,a.fk_account_code, a.department_code, a.fk_function_code,
a.fk_project_code, a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,
0 as [1A_line]
INTO #accounting_budformdata
FROM tfp_accounting_data_detail a
WHERE a.gl_year = @budget_year AND a.fk_tenant_id = @fk_tenant_id
GROUP BY a.fk_tenant_id, a.fk_account_code, a.department_code, a.fk_function_code, a.gl_year,
a.fk_project_code, a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4
OPTION (RECOMPILE);

IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') > 0

BEGIN 
PRINT 'Update tfp_accounting_detail 1A_line 2020 -logic'

UPDATE #accounting_budformdata SET [1A_line] = 1
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #accounting_budformdata a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.gl_year BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND a.department_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_DEPARTMENTS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = @report1A AND a.gl_year = @budget_year AND a.fk_tenant_id = @fk_tenant_id

UPDATE #accounting_budformdata SET [1A_line] = 1
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #accounting_budformdata a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.gl_year BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND a.fk_function_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = @report1A AND a.gl_year = @budget_year AND a.fk_tenant_id = @fk_tenant_id


UPDATE #accounting_budformdata SET [1A_line] = 1
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #accounting_budformdata a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.gl_year BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND c.line_group_id != 10
WHERE  c.report = @report1A  AND a.gl_year = @budget_year AND a.fk_tenant_id = @fk_tenant_id


UPDATE a SET a.[1A_line] = 0
FROM #accounting_budformdata a 
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN #rep_line_1B rl ON rl.report = @report1B AND rl.fk_kostra_account_code = ac.fk_kostra_account_code
WHERE a.[1A_line] = 1 
AND a.department_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
AND a.fk_function_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_FUNCTIONS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
AND a.fk_tenant_id = @fk_tenant_id AND a.gl_year = @budget_year AND a.fk_tenant_id = @fk_tenant_id

END

IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') = 0
BEGIN 

	print 'Update tfp_accounting_detail - Use Framsikt 1a'

	UPDATE #accounting_budformdata
	SET [1A_line] = 1
	FROM #accounting_budformdata imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.gl_year = @budget_year
	AND imp.fk_tenant_id = @fk_tenant_id
	AND imp.gl_year = ls.budget_year
	AND ls.priority = 0;

	
	UPDATE #accounting_budformdata
	SET [1A_line] = 1
	FROM #accounting_budformdata imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.gl_year = @budget_year
	AND imp.fk_tenant_id = @fk_tenant_id
	AND imp.gl_year = ls.budget_year
	AND ls.priority = 5;
	
	UPDATE #accounting_budformdata
	SET [1A_line] = 1
	FROM #accounting_budformdata imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.gl_year = @budget_year
	AND imp.fk_tenant_id = @fk_tenant_id
	AND imp.gl_year = ls.budget_year
	AND ls.priority = 4;

	
	UPDATE #accounting_budformdata
	SET [1A_line] = 1
	FROM #accounting_budformdata imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.gl_year = @budget_year
	AND imp.fk_tenant_id = @fk_tenant_id
	AND imp.gl_year = ls.budget_year
	AND ls.priority = 3;

	
	UPDATE #accounting_budformdata
	SET [1A_line] = 1
	FROM #accounting_budformdata imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.gl_year = @budget_year
	AND imp.fk_tenant_id = @fk_tenant_id
	AND imp.gl_year = ls.budget_year
	AND ls.priority = 2;

	
	UPDATE #accounting_budformdata
	SET [1A_line] = 1
	FROM #accounting_budformdata imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.gl_year = @budget_year
	AND imp.fk_tenant_id = @fk_tenant_id
	AND imp.gl_year = ls.budget_year
	AND ls.priority = 1;


END

DELETE FROM [tmr_acc_lookup_warehouse] WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period

INSERT INTO [tmr_acc_lookup_warehouse] (
	[fk_tenant_id],
	[gl_year],
	[forecast_period],
	[fk_account_code],
	[department_code],
	[fk_function_code],
	[fk_project_code],
	[free_dim_1],
	[free_dim_2],
	[free_dim_3],
	[free_dim_4],
	[1A_line],
    [updated],
    [updated_by])
SELECT 
	[fk_tenant_id],
	[gl_year],
	[forecast_period],
	[fk_account_code],
	[department_code],
	[fk_function_code],
	[fk_project_code],
	[free_dim_1],
	[free_dim_2],
	[free_dim_3],
	[free_dim_4],
	[1A_line],
    [updated] = getdate(),
    [updated_by] = @user_id
FROM #accounting_budformdata

PRINT 'Update 1a_flag in lookup warehouse table DONE ' + convert(varchar(400),GETDATE());


drop table if exists #reporting_table
drop table if exists #reporting_table_2
drop table if exists #TEMP_wh_period
DROP TABLE if exists #temp_table_2
DROP TABLE if exists #TEMP_ACC_DATA
DROP TABLE IF exists #departments_list
DROP TABLE IF EXISTS #inv_status_header

RETURN 0
GO
