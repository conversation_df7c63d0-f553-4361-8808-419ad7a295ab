CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedDimensionFunctionData] 
        @tenant_id    INT,
        @user_id      INT,
        @dimension_id INT,
        @job_id       BIGINT
AS
    IF( @job_id = -1 )
        BEGIN
            UPDATE tbu_stage_dimensions_import
            SET    function_code_error = 0,
                   function_name_error = 0,
                   year_from_error = 0,
                   year_to_error = 0,
                   function_status_error = 0,
                   kostra_function_code_error = 0,
                   error_count = 0
            WHERE  fk_tenant_id = @tenant_id
                   AND [user_id] = @user_id
                   AND dimension_type = @dimension_id;
        END
    ELSE
        BEGIN
            UPDATE tbu_stage_dimensions_import
            SET    function_code_error = 0,
                   function_name_error = 0,
                   year_from_error = 0,
                   year_to_error = 0,
                   function_status_error = 0,
                   kostra_function_code_error = 0,
                   error_count = 0
            WHERE  fk_tenant_id = @tenant_id
                   AND [job_id] = @job_id
                   AND dimension_type = @dimension_id;
        END

    IF( @job_id = -1)
        BEGIN
            --validate Function Code
            UPDATE tbu_stage_dimensions_import
            SET    function_code_error = function_code_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( function_code IS NULL
                        OR function_code = '' );

            --validate Function Name
            UPDATE tbu_stage_dimensions_import
            SET    function_name_error = function_name_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( function_name IS NULL
                        OR function_name = '' OR LEN(function_name) > 100 );

            --Validate Function Year From NULL CHECK
            UPDATE tbu_stage_dimensions_import
            SET    year_from_error = year_from_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( year_from IS NULL
                        OR year_from = ''
                        OR year_from = 0 );
            
            --Validate Function Year To NULL CHECK
            UPDATE tbu_stage_dimensions_import
            SET    year_to_error = year_to_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( year_to IS NULL
                        OR year_to = ''
                        OR year_to = 0 );

            --Validate length of the function from year
            UPDATE tbu_stage_dimensions_import
            SET    year_from_error = year_from_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_from) < 4
                        OR ( year_from > year_to ) );


            --validate length of the function to year
            UPDATE tbu_stage_dimensions_import
            SET    year_to_error = year_to_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_to) < 4
                        OR ( year_from > year_to ) ); 

            --validate Function Status
            UPDATE tbu_stage_dimensions_import
            SET    function_status_error = function_status_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id
                 AND ( function_status IS NULL
                        OR function_status > 1
                        OR function_status < 0 );
         
            --validate kostra function code
            UPDATE tbu_stage_dimensions_import
	     SET kostra_function_code_error = kostra_function_code_error + 1, error_count = error_count + 1		
	     WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND 
		    LEN(tbu_stage_dimensions_import.year_from) = 4
		    AND  LEN(tbu_stage_dimensions_import.year_to) = 4
		    AND tbu_stage_dimensions_import.kostra_function_code not in (select pk_kostra_function_code from gmd_kostra_function)
        END
    ELSE
        BEGIN
            --validate Function Code
            UPDATE tbu_stage_dimensions_import
            SET    function_code_error = function_code_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( function_code IS NULL
                        OR function_code = '' );

            --validate Function Name
            UPDATE tbu_stage_dimensions_import
            SET    function_name_error = function_name_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( function_name IS NULL
                        OR function_name = '' OR LEN(function_name) > 100);

            --Validate Function Year From NULL CHECK
            UPDATE tbu_stage_dimensions_import
            SET    year_from_error = year_from_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( year_from IS NULL
                        OR year_from = ''
                        OR year_from = 0 );
            
            --Validate Function Year To NULL CHECK
            UPDATE tbu_stage_dimensions_import
            SET    year_to_error = year_to_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( year_to IS NULL
                        OR year_to = ''
                        OR year_to = 0 );

            --Validate length of the function from year
            UPDATE tbu_stage_dimensions_import
            SET    year_from_error = year_from_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_from) < 4
                        OR ( year_from > year_to ) );


            --validate length of the function to year
            UPDATE tbu_stage_dimensions_import
            SET    year_to_error = year_to_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( Len(year_to) < 4
                        OR ( year_from > year_to ) ); 

            --validate Function Status
            UPDATE tbu_stage_dimensions_import
            SET    function_status_error = function_status_error + 1,
                   error_count = error_count + 1
            WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id
                 AND ( function_status IS NULL
                        OR function_status > 1
                        OR function_status < 0 );
         
            --validate kostra function code
            UPDATE tbu_stage_dimensions_import
	     SET kostra_function_code_error = kostra_function_code_error + 1, error_count = error_count + 1		
	     WHERE tbu_stage_dimensions_import.job_id = @job_id AND dimension_type = @dimension_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND 
		    LEN(tbu_stage_dimensions_import.year_from) = 4
		    AND  LEN(tbu_stage_dimensions_import.year_to) = 4
		    AND tbu_stage_dimensions_import.kostra_function_code not in (select pk_kostra_function_code from gmd_kostra_function)
        END
RETURN 0