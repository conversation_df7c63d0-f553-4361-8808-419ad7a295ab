CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportActivityIndicator]
	@tenant_id int,        
	 @user_id int,        
	 @budget_year int,        
	 @job_id bigInt
AS
 
BEGIN
DECLARE @org_version varchar(25)
SET @org_version = (SELECT pk_org_version FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND @budget_year*100+1 BETWEEN period_from AND period_to)


CREATE  TABLE #orgHeirarchyTable(
tenantId int not NULL,
org_id1 NVarchar(50),
org_id2 NVarchar(50),
org_id3 NVarchar(50),
org_id6 NVarchar(50),
org_id7 NVarchar(50),
org_id8 NVarchar(50),
org_id5 NVarchar(50),
org_id4 NVarchar(50)
)

CREATE  TABLE #orgTable (
orgId nvarchar(10),
orgLevel int
)
 
INSERT INTO #orgHeirarchyTable (tenantId,org_id1 ,org_id2 ,org_id3 ,org_id5 ,org_id4 ,org_id6 ,org_id7 ,org_id8 ) 
SELECT fk_tenant_id,org_id_1 ,org_id_2 ,org_id_3 ,org_id_5 ,org_id_4,org_id_6 ,org_id_7 ,org_id_8  
FROM tco_org_hierarchy WHERE fk_tenant_id = @tenant_id AND fk_org_version = @org_version

INSERT INTO #orgTable (orgId,orgLevel) (
SELECT DISTINCT org_id1, 1 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id2, 2 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id3, 3 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id4, 4 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id5, 5 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id6, 6 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id7, 7 FROM #orgHeirarchyTable UNION ALL
SELECT DISTINCT org_id8, 8 FROM #orgHeirarchyTable )


    -- clear all the error information--
	IF( @job_id = -1)
		BEGIN
			UPDATE tco_stage_activity_indicators        
			 SET id_error = 0,        
			 org_id_error = 0,         
			 org_level_error = 0,        
			 service_id_error = 0,        
			 period_error = 0,        
			 error_count = 0
			 WHERE fk_tenant_id = @tenant_id AND         
			  budget_year = @budget_year AND      
			  userId = @user_id;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_activity_indicators        
			 SET id_error = 0,        
			 org_id_error = 0,         
			 org_level_error = 0,        
			 service_id_error = 0,        
			 period_error = 0,        
			 error_count = 0
			 WHERE fk_tenant_id = @tenant_id AND         
			  budget_year = @budget_year AND      
			  fk_job_id = @job_id;
		END
		
        -- INSERT type


	IF @job_id = -1
    BEGIN
       UPDATE tco_stage_activity_indicators SET type = 
		CASE WHEN tco_stage_activity_indicators.id NOT IN (SELECT fk_framsikt_indicator_code FROM tco_indicator_setup WHERE fk_tenant_id = @tenant_id AND ((indicator_type=2 AND (activity_status = 'Active' OR activity_status = '')) or (indicator_type=1) ) ) THEN 0
		ELSE (SELECT indicator_type FROM tco_indicator_setup WHERE fk_tenant_id = @tenant_id AND  fk_framsikt_indicator_code = tco_stage_activity_indicators.id) 
		END,
		Name = CASE WHEN tco_stage_activity_indicators.id NOT IN (SELECT fk_framsikt_indicator_code FROM tco_indicator_setup where fk_tenant_id = @tenant_id ) THEN ''
		ELSE  (SELECT measurment_criteria FROM tco_indicator_setup WHERE fk_tenant_id = @tenant_id AND   fk_framsikt_indicator_code = tco_stage_activity_indicators.id) 
		END 
		WHERE fk_tenant_id = @tenant_id AND         
			  budget_year = @budget_year AND      
			  userId = @user_id; 
		-- validate id 
		UPDATE	tco_stage_activity_indicators
		SET		id_error = 1, error_count = error_count + 1
		FROM	tco_stage_activity_indicators
		WHERE	fk_tenant_id = @tenant_id AND
				userId = @user_id AND budget_year = @budget_year  AND type =0 
		
		-- validate org id AND org level
		UPDATE tco_stage_activity_indicators 
		SET org_id_error=1 , org_level_error=1, error_count=error_count+2 
		FROM tco_stage_activity_indicators a
		LEFT OUTER JOIN #orgTable b ON a.org_id = b.orgId AND a.org_level = b.orgLevel 
		WHERE  a.fk_tenant_id=@tenant_id AND a.budget_year= @budget_year AND a.userId = @user_id  AND (b.orgLevel  IS NULL or b.orgId IS NULL)


		UPDATE tco_stage_activity_indicators SET period_error=1 , error_count=error_count+1
WHERE fk_tenant_id=@tenant_id AND budget_year= @budget_year AND userId = @user_id and ( period NOT BETWEEN @budget_year * 100 + 1 AND @budget_year * 100 + 12 or period is null)

 UPDATE tco_stage_activity_indicators set result_error =1 , error_count = error_count+1 
 WHERE fk_tenant_id=@tenant_id AND budget_year= @budget_year and userId = @user_id AND result IS NULL 

 --//change made as per bug #174944, to show only those rows with id_error =1, which are duplicates with other as all major columns
    ;with DuplicateRows As ( select id, org_id, org_level, period from tco_stage_activity_indicators where fk_tenant_id = @tenant_id and budget_year = @budget_year and userId = @user_id
	                         Group by id, org_id, org_level, period having count(*) > 1)

	update sai set id_error=1, error_count = error_count +1 from tco_stage_activity_indicators sai join DuplicateRows d 
	               on sai.id = d.id and sai.org_id = d.org_id and sai.org_level = d.org_level and sai.period = d.period 
				   where sai.fk_tenant_id = @tenant_id and sai.budget_year = @budget_year and sai.userId = @user_id;
		
	END
IF @job_id <> -1
    BEGIN
        UPDATE tco_stage_activity_indicators SET type = 
		CASE WHEN tco_stage_activity_indicators.id NOT IN (SELECT fk_framsikt_indicator_code FROM tco_indicator_setup WHERE fk_tenant_id = @tenant_id AND ((indicator_type=2 AND (activity_status = 'Active' OR activity_status = '')) or (indicator_type=1) ) ) THEN 0
		ELSE (SELECT indicator_type FROM tco_indicator_setup WHERE fk_tenant_id = @tenant_id AND  fk_framsikt_indicator_code = tco_stage_activity_indicators.id) 
		END,
		Name = CASE WHEN tco_stage_activity_indicators.id NOT IN (SELECT fk_framsikt_indicator_code FROM tco_indicator_setup where fk_tenant_id = @tenant_id ) THEN ''
		ELSE  (SELECT measurment_criteria FROM tco_indicator_setup WHERE fk_tenant_id = @tenant_id AND   fk_framsikt_indicator_code = tco_stage_activity_indicators.id) 
		END 
		WHERE fk_tenant_id = @tenant_id AND         
			  budget_year = @budget_year AND      
			  fk_job_id = @job_id; 
		-- validate id 
		UPDATE	tco_stage_activity_indicators
		SET		id_error = 1, error_count = error_count + 1
		FROM	tco_stage_activity_indicators
		WHERE	fk_tenant_id = @tenant_id AND
				fk_job_id = @job_id AND budget_year = @budget_year  AND type =0 
		
		-- validate org id AND org level
		UPDATE tco_stage_activity_indicators 
		SET org_id_error=1 , org_level_error=1, error_count=error_count+2 
		FROM tco_stage_activity_indicators a
		LEFT OUTER JOIN #orgTable b ON a.org_id = b.orgId AND a.org_level = b.orgLevel 
		WHERE  a.fk_tenant_id=@tenant_id AND a.budget_year= @budget_year AND a.fk_job_id = @job_id  AND (b.orgLevel  IS NULL or b.orgId IS NULL)


		UPDATE tco_stage_activity_indicators SET period_error=1 , error_count=error_count+1
WHERE fk_tenant_id=@tenant_id AND budget_year= @budget_year AND fk_job_id = @job_id and ( period NOT BETWEEN @budget_year * 100 + 1 AND @budget_year * 100 + 12 or period is null)

 UPDATE tco_stage_activity_indicators set result_error =1 , error_count = error_count+1 
 WHERE fk_tenant_id=@tenant_id AND budget_year= @budget_year and fk_job_id = @job_id AND result IS NULL

 UPDATE tco_stage_activity_indicators set id_error =1 , error_count = error_count+1 
 WHERE fk_tenant_id=@tenant_id AND budget_year= @budget_year and fk_job_id = @job_id AND id in (select id from tco_stage_activity_indicators where fk_tenant_id=@tenant_id AND budget_year= @budget_year and fk_job_id = @job_id group by id, org_id, org_level, period having count(id)>1)
    END
        
        


END 




