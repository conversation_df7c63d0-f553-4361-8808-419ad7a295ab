CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRChecklists]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS

	DECLARE @budget_year INT
	DECLARE @prev_forecast_period INT
	DECLARE @org_version VARCHAR(25) 
	DECLARE @language VARCHAR(10)
	
	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
	SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
	SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
	SET @budget_year = @forecast_period/100

	
PRINT 'START: Fetch checklist data'

DECLARE @prev_checklist_period INT = (
SELECT MAX(forecast_period) FROM tmr_calendar
WHERE fk_tenant_id = @fk_tenant_id
AND forecast_period < @forecast_period
AND checklist_flag = 1
AND forecast_period > @budget_year *100)


SELECT i.pk_checklist_item_id,d.pk_checklistitem_detail_id as checklistitem_detail_id,
i.fk_tenant_id,@forecast_period as forecast_Period,
i.budget_year,NEWID() as description_historyid,'' AS checklist_description,
@user_id as updated_by,GETDATE() AS updated,
d.status_inprogress,d.progress_percent,d.status_yes,d.status_no,d.status_noreply, i.repeat_frequency
INTO #checklist_items
 FROM tbi_checklist_items i
JOIN tbi_checklist_items_detail d ON i.fk_tenant_id = d.fk_tenant_id AND i.pk_checklist_item_id = d.fk_checklist_item_id
where 1=2

CREATE UNIQUE INDEX #chk_items_1 ON #checklist_items (checklistitem_detail_id);

INSERT INTO #checklist_items (pk_checklist_item_id,checklistitem_detail_id,fk_tenant_id,forecast_Period,budget_Year,description_historyid,checklist_description,updated_by,updated,status_inprogress,progress_percent,status_yes,status_no,status_noreply, repeat_frequency)
SELECT i.pk_checklist_item_id,d.pk_checklistitem_detail_id as checklistitem_detail_id,
i.fk_tenant_id,@forecast_period as forecast_Period,
i.budget_year,NEWID() as description_historyid,'' AS checklist_description,
@user_id as updated_by,GETDATE() AS updated,
d.status_inprogress,d.progress_percent,d.status_yes,d.status_no,d.status_noreply, i.repeat_frequency
 FROM tbi_checklist_items i
JOIN tbi_checklist_items_detail d ON i.fk_tenant_id = d.fk_tenant_id AND i.pk_checklist_item_id = d.fk_checklist_item_id
where i.fk_tenant_id = @fk_tenant_id 
AND i.budget_year = @budget_year


DELETE FROM #checklist_items WHERE repeat_frequency = 3 AND convert(int,SUBSTRING(CONVERT(VARCHAR(6),@forecast_period),5,2)) NOT IN (3,6,9,12);

DELETE FROM #checklist_items WHERE repeat_frequency = 4 AND convert(int,SUBSTRING(CONVERT(VARCHAR(6),@forecast_period),5,2)) NOT IN (4,8,12);

DELETE FROM #checklist_items WHERE repeat_frequency = 2 AND convert(int,SUBSTRING(CONVERT(VARCHAR(6),@forecast_period),5,2)) NOT IN (12);


UPDATE a SET a.status_inprogress = b.status_inprogress,
a.progress_percent = b.progress_percent,
a.status_yes = b.status_yes,
a.status_no = b.status_no,
a.status_noreply = b.status_noreply
FROM #checklist_items a
JOIN tmr_checklist_descriptions b ON a.fk_tenant_id = b.fk_tenant_id AND b.forecast_period = @prev_checklist_period
AND a.checklistitem_detail_id = b.checklistitem_detail_id
AND a.repeat_frequency NOT IN (1,2,3,4)

UPDATE a SET a.status_inprogress = 0,
a.progress_percent = 0,
a.status_yes = 0,
a.status_no =0,
a.status_noreply = 1
FROM #checklist_items a
WHERE a.repeat_frequency IN (1,2,3,4)

if (SELECT COUNT(*) FROM tmr_checklist_descriptions WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period) = 0
begin

DELETE FROM tmr_checklist_descriptions WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period

INSERT INTO tmr_checklist_descriptions (checklistitem_detail_id,fk_tenant_id,forecast_Period,budget_Year,description_historyid,checklist_description,updated_by,updated,status_inprogress,progress_percent,status_yes,status_no,status_noreply)
SELECT checklistitem_detail_id,fk_tenant_id,forecast_Period,budget_Year,description_historyid,checklist_description,updated_by,updated,status_inprogress,progress_percent,status_yes,status_no,status_noreply
FROM #checklist_items
end

PRINT 'END: Fetch checklist data'

RETURN 0
GO
