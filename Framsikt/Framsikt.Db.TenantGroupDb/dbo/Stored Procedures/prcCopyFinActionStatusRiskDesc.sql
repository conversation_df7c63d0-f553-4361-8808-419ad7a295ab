CREATE OR ALTER PROCEDURE [dbo].[prcCopyFinActionStatusRiskDesc]
    @fk_tenant_id INT, @forecast_period INT
AS 
DECLARE @flag_name varchar(30)
DECLARE @flag_status INT
SET @flag_name = 'MR_COPYREPORT_ORGLEVEL'
SET @flag_status = (SELECT flag_status FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id  AND flag_name = @flag_name)

if (@flag_status <> 0 OR @flag_status IS NOT NULL)
BEGIN
    DELETE FROM tmr_finplan_actions where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period

    INSERT INTO tmr_finplan_actions (fk_tenant_id, forecast_period, fk_action_id, real_amount, status, risk, description,
    updated, updated_by, yearly_forecast, org_id, org_level, service_id, is_reported, limit_code, finplan_year_1_amount, descriptiontxt)
    SELECT fk_tenant_id, @forecast_period, fk_action_id, real_amount, status, risk, description,
    updated, updated_by, yearly_forecast, org_id, org_level, service_id, is_reported, limit_code, finplan_year_1_amount, descriptiontxt  FROM tmr_finplan_actions 
    WHERE fk_tenant_id = @fk_tenant_id AND org_level >= @flag_status AND forecast_period = 
	(select max(forecast_period) FROM tmr_finplan_actions  WHERE fk_tenant_id = @fk_tenant_id AND forecast_period <@forecast_period AND org_level >= @flag_status AND (real_amount <> 0.00 or status <> 0 or risk <> 0 or descriptiontxt <> ''))

END
RETURN 0
GO