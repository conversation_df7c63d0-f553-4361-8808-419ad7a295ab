CREATE OR ALTER PROCEDURE [dbo].[prcValidateMRClimateImportData]
	@tenant_id int,
	@user_id int,
	@budget_year int
AS	

	--Clear all the error information --
	UPDATE tmr_stage_climate_accounting_data
	SET forecast_period_error = 0, 
		category_name_error = 0,
		emission_source_name_error = 0,
		scope_error = 0,
		reported_val_error = 0,
		emission_co2_error = 0,
		error_count = 0
	WHERE tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id AND 
		tmr_stage_climate_accounting_data.budget_year = @budget_year AND 
		tmr_stage_climate_accounting_data.user_id = @user_id;
		
		
	--Validate period_error ---
	
		SELECT a.years  INTO #tempYears FROM (
		SELECT  CAST(@budget_year as nvarchar(10))+'01' as years  UNION  SELECT CAST(@budget_year as nvarchar(10))+'02' 
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'03' UNION  SELECT CAST(@budget_year as nvarchar(10))+'04'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'05' UNION  SELECT CAST(@budget_year as nvarchar(10))+'06'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'07' UNION  SELECT CAST(@budget_year as nvarchar(10))+'08'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'09' UNION  SELECT CAST(@budget_year as nvarchar(10))+'10'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'11' UNION  SELECT CAST(@budget_year as nvarchar(10))+'12' 
		)a

		UPDATE tmr_stage_climate_accounting_data set forecast_period_error = 1, error_count = error_count + 1
		WHERE tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id AND 
				tmr_stage_climate_accounting_data.budget_year = @budget_year AND 
				tmr_stage_climate_accounting_data.user_id = @user_id AND
				(tmr_stage_climate_accounting_data.forecast_period is NULL OR ISNUMERIC(tmr_stage_climate_accounting_data.forecast_period) = 0 OR tmr_stage_climate_accounting_data.forecast_period  NOT IN (SELECT years from #tempYears)) ; 

		DROP TABLE #tempYears


	-- Validate reported value column--

		UPDATE tmr_stage_climate_accounting_data SET reported_val_error = 1, error_count = error_count +1
		WHERE ISNUMERIC(reported_val) = 0
		AND tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id AND 
			tmr_stage_climate_accounting_data.budget_year = @budget_year AND 
			tmr_stage_climate_accounting_data.user_id = @user_id;

	-- Validate scope column--

		UPDATE tmr_stage_climate_accounting_data SET scope_error = 1, error_count = error_count +1
		WHERE scope is NULL OR ISNUMERIC(scope) = 0 OR scope NOT IN (1,2,3)
		AND tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id AND 
			tmr_stage_climate_accounting_data.budget_year = @budget_year AND 
			tmr_stage_climate_accounting_data.user_id = @user_id;

	-- Validate category name column--

		UPDATE tmr_stage_climate_accounting_data SET category_name_error = 1, error_count = error_count +1
		WHERE category_name is NULL OR trim(category_name) =''
		AND tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id AND 
			tmr_stage_climate_accounting_data.budget_year = @budget_year AND 
			tmr_stage_climate_accounting_data.user_id = @user_id;

	-- Validate emission source name column--

		UPDATE tmr_stage_climate_accounting_data SET emission_source_name_error = 1, error_count = error_count +1
		WHERE emission_source_name is NULL OR trim(emission_source_name) =''
		AND tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id AND 
			tmr_stage_climate_accounting_data.budget_year = @budget_year AND 
			tmr_stage_climate_accounting_data.user_id = @user_id;
		
	--UPDATE category id--
		
		UPDATE tmr_stage_climate_accounting_data set fk_category_id = tmr_climate_category.pk_category_id
		from tmr_stage_climate_accounting_data
		inner join tmr_climate_category 
		on 
		tmr_stage_climate_accounting_data.fk_tenant_id = tmr_climate_category.fk_tenant_id and 
		trim(tmr_stage_climate_accounting_data.category_name) = trim(tmr_climate_category.category_name)
		where tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id 

		--set category id to 0(update id to 0 when import with valid category and then change category name to new one)
		UPDATE tmr_stage_climate_accounting_data set fk_category_id = 0
		from tmr_stage_climate_accounting_data a
		where a.fk_tenant_id = @tenant_id and trim(a.category_name) NOT IN(select trim(b.category_name) from tmr_climate_category b
		where b.fk_tenant_id = @tenant_id)

	--UPDATE emission source id--
		
		UPDATE tmr_stage_climate_accounting_data set fk_emission_source_id = tmr_climate_emission_source.pk_emission_source_id
		from tmr_stage_climate_accounting_data
		inner join tmr_climate_emission_source 
		on 
		tmr_stage_climate_accounting_data.fk_tenant_id = tmr_climate_emission_source.fk_tenant_id and 
		trim(tmr_stage_climate_accounting_data.emission_source_name) = trim(tmr_climate_emission_source.emission_source_name)
		where tmr_stage_climate_accounting_data.fk_tenant_id = @tenant_id 

		--set emission source id to 0(update id to 0 when import with valid emission source and then change emission source name to new one)
		UPDATE tmr_stage_climate_accounting_data set fk_emission_source_id = 0
		from tmr_stage_climate_accounting_data a
		where a.fk_tenant_id = @tenant_id and trim(a.emission_source_name) NOT IN(select trim(b.emission_source_name) from tmr_climate_emission_source b
		where b.fk_tenant_id = @tenant_id)
RETURN 0


