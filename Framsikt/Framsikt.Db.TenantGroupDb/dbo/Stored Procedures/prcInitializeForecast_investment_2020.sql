CREATE OR ALTER PROCEDURE [dbo].[prcInitializeForecast_investment_2020]
@forecast_period INT,  @fk_tenant_id INT, @user_id INT, @investment_init_type NVARCHAR(25) = ''

AS

SET NOCOUNT ON;

DECLARE @budget_year INT
DECLARE @last_period INT
DECLARE @ub_period INT
DECLARE @month_remaining dec(18,6) 
DECLARE @month_ytd dec(18,6)
DECLARE @not_use_fr_budget INT
DECLARE @not_use_salary_forecast INT
DECLARE @prev_forecast_period INT
DECLARE @service_level INT
DECLARE @continue INT
DECLARE @rowcount INT
DECLARE @periods_ytd INT
DECLARE @periods_remaining INT
DECLARE @org_version VARCHAR(25) 
DECLARE @inv_org_level INT
DECLARE @unapproved_check INT 
DECLARE @vat_account NVARCHAR(25)

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

DECLARE @change_id_table TABLE (fk_change_id INT)

SET @periods_ytd= convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))
SET @periods_remaining = 12 - @periods_ytd;

DECLARE @acc_table TABLE (fk_account_code NVARCHAR(25))

SET @inv_org_level = 2

IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1) = 1
begin
SET @inv_org_level = (
 SELECT org_level = 
CASE	WHEN param_value = 'org_id_2' THEN 2 
		WHEN param_value = 'org_id_3' THEN 3
		WHEN param_value = 'org_id_4' THEN 4
		WHEN param_value = 'org_id_5' THEN 5 
ELSE 2 END FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1)
end

PRINT 'Start procedure '  + convert(varchar(400),GETDATE());


DECLARE @period_table TABLE (pk_id INT IDENTITY NOT NULL,
	[period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL) 



DECLARE @inv_status_header TABLE
(
[fk_tenant_id] INT NOT NULL, 
   [org_id] NVARCHAR(25) NOT NULL, 
   [org_level] INT NOT NULL,
   [service_id] NVARCHAR(25) NOT NULL,
   [fk_investment_id] INT NOT NULL , 
   [total_change_amount]DECIMAL(18, 2) NOT NULL,
   [yearly_forcast_value] DECIMAL(18, 2) NOT NULL,
   [status] INT NOT NULL,
   [risk] INT NOT NULL,
   [status_desc_id_history] UNIQUEIDENTIFIER NOT NULL, 
   	[forecast_period] INT NOT NULL,
	[est_finish_quarter] INT NOT NULL,
	[is_reported] BIT NOT NULL,
	[fk_prog_code] NVARCHAR(25) NULL, 
	[updated] DATETIME NOT NULL, 
	[updated_by] INT NOT NULL, 
	[fk_alter_code] NVARCHAR(500),
	[status_desc] NVARCHAR(4000) NOT NULL,
	[fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL,
	[approval_cost] DECIMAL(18, 2) DEFAULT 0 NULL)


DECLARE @inv_status_detail TABLE
(
	[pk_id] BIGINT NOT NULL IDENTITY,
	[fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [forecast_period] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
    [vat_refund] DECIMAL(18, 2) NOT NULL,   	
    [year_0_amount] DECIMAL(18, 2) NOT NULL,   
    [year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
    [year_5_amount] DECIMAL(18, 2) NOT NULL, 
    [year_6_amount] DECIMAL(18, 2) NOT NULL, 
    [year_7_amount] DECIMAL(18, 2) NOT NULL, 
    [year_8_amount] DECIMAL(18, 2) NOT NULL,     
    [year_9_amount] DECIMAL(18, 2) NOT NULL, 
    [year_10_amount] DECIMAL(18, 2) NOT NULL, 
	[gl_amount] DECIMAL(18, 2) NOT NULL, 
    [fk_alter_code] nvarchar(50) NOT NULL,
    [fk_adjustment_code] nvarchar(50) NOT NULL,
    [fk_program_code] nvarchar(50) NOT NULL,
	[use_budget] bit default 0 not null,
	[total_amount] DECIMAL(18, 2) default 0 NOT NULL,
	[update_year2] bit default 0 not null,
	[approval_cost] DECIMAL(18, 2) DEFAULT 0 NULL,
	[type] NVARCHAR (2) NOT NULL,	
	[fk_invDetail_id] BIGINT NOT NULL default (0),
	[is_vat_row] bit not null default(0),
	[line_item_id] INT NULL 
)


DECLARE @inv_gl_table TABLE
(
	[pk_id] BIGINT NOT NULL IDENTITY,
	[fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [forecast_period] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL,  
	[gl_amount] DECIMAL(18, 2) NOT NULL, 
    [fk_program_code] nvarchar(50) NOT NULL,
	[type] NVARCHAR (2) NOT NULL
)


DECLARE @inv_owner_table TABLE
(
	[pk_id] BIGINT NOT NULL IDENTITY,
	[fk_investment_id] INT NOT NULL, 
	org_id_header NVARCHAR(25) NOT NULL,
	org_id_detail NVARCHAR(25) NOT NULL
)


SET @budget_year = @forecast_period/100
SET @last_period = (@budget_year*100)+12
SET @ub_period = (@budget_year*100)+13
SET @month_remaining = (12 - CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))/12
SET @month_ytd = (CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))
SET @not_use_fr_budget = (SELECT COUNT(*) FROM tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'DONT_USE_FRAMSIKT_SALARY' AND param_value = 'TRUE')
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
SET @not_use_salary_forecast =  (SELECT COUNT(*) FROM tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'DONT_USE_SALARY_FORECAST' AND param_value = 'TRUE')


IF @forecast_period = @ub_period AND @org_version is null
BEGIN

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period -1 BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

END

SET @vat_account = (
select MIN(acc_value) from  tmd_acc_defaults b 
WHERE  module = 'INV' and link_type = 'VAT_COST'
AND fk_tenant_id = @fk_tenant_id
AND fk_org_version = @org_version)


IF @forecast_period IN (@ub_period)
BEGIN
SET @investment_init_type = ''
--SET @init_type = 'Actual'
END

BEGIN 

INSERT INTO @change_id_table (fk_change_id)
SELECT pk_change_id
FROM tfp_budget_changes 
WHERE fk_tenant_id = @fk_tenant_id
AND budget_year = @budget_year

END

BEGIN 

INSERT INTO @period_table (period, fk_tenant_id)
VALUES 
((@budget_year*100)+1, @fk_tenant_id),
((@budget_year*100)+2, @fk_tenant_id),
((@budget_year*100)+3, @fk_tenant_id),
((@budget_year*100)+4, @fk_tenant_id),
((@budget_year*100)+5, @fk_tenant_id),
((@budget_year*100)+6, @fk_tenant_id),
((@budget_year*100)+7, @fk_tenant_id),
((@budget_year*100)+8, @fk_tenant_id),
((@budget_year*100)+9, @fk_tenant_id),
((@budget_year*100)+10, @fk_tenant_id),
((@budget_year*100)+11, @fk_tenant_id),
((@budget_year*100)+12, @fk_tenant_id);

DELETE FROM @period_table WHERE period <= @forecast_period

END

INSERT INTO @acc_table(fk_account_code)
SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU' AND fk_org_version = @org_version
union 
SELECT fk_account_code FROM tmd_pension_type WHERE fk_tenant_id = @fk_tenant_id 
union 
SELECT fk_account_code_aga FROM tmd_pension_type WHERE fk_tenant_id = @fk_tenant_id;


PRINT 'START: Copy new investments from last budget ' + convert(varchar(400),GETDATE());

SET @service_level = (SELECT fk_service_level_id FROM TCO_ORG_LEVEL l WHERE l.fk_tenant_id = @fk_tenant_id
AND org_level = 2 AND service_level_flag = 1
AND fk_org_version = @org_version)

SELECT * INTO #temp_investment_status
FROM tmr_investment_status
WHERE fk_tenant_id = @fk_tenant_id
AND forecast_period = @forecast_period;

DELETE FROM tmr_investment_status
WHERE fk_tenant_id = @fk_tenant_id
AND forecast_period = @forecast_period;

DELETE FROM tmr_investment_status_detail
WHERE fk_tenant_id = @fk_tenant_id
AND forecast_period = @forecast_period;

INSERT INTO @inv_status_header (fk_tenant_id,org_id,org_level,service_id,fk_investment_id,total_change_amount,yearly_forcast_value,status,risk,status_desc,forecast_period,updated,updated_by,est_finish_quarter,is_reported,fk_prog_code, status_desc_id_history,
fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, approval_cost)
SELECT fk_tenant_id, org_id, org_level,'',pk_investment_id,SUM(total_change_amount),0,status,risk,
'' AS status_desc,forecast_period,getdate() as updated,@user_id as updated_by,est_finish_quarter,is_reported,fk_program_code,'********-0000-0000-0000-********0000',
fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, sum(approved_cost)
FROM (
SELECT  a.fk_tenant_id, yc.fk_org_id AS org_id, @inv_org_level as org_level,service_id = CASE 
	WHEN @service_level = 1 THEN SV.service_id_1
	WHEN @service_level = 2 THEN SV.service_id_2
	WHEN @service_level = 3 THEN SV.service_id_3
	WHEN @service_level = 4 THEN SV.service_id_4
	WHEN @service_level = 5 THEN SV.service_id_5
	ELSE ''
	END, a.pk_investment_id,
total_change_amount = CASE WHEN yc.inv_status = 2 THEN 0 ELSE a.previously_budgeted END,
yearly_forcast_value = 0,
0 as status,0 as risk,newid() as status_desc_id,@forecast_period as forecast_period,getdate() as updated,@user_id as updated_by,convert(int,CONCAT(datepart(year, yc.completion_date), '4')) as est_finish_quarter,1 as is_reported,ISNULL(b.fk_program_code,1) as fk_program_code,
c.fk_account_code, c.department_code, c.fk_function_code, c.fk_project_code, c.free_dim_1, c.free_dim_2, c.free_dim_3, c.free_dim_4,
b.approved_cost
FROM tco_investments a
JOIN tco_investment_detail b ON a.pk_investment_id = b.fk_investment_id AND a.fk_tenant_id = b.fk_tenant_id
LEFT JOIN tfp_inv_transactions c ON a.pk_investment_id = c.fk_investment_id AND a.fk_tenant_id = c.fk_tenant_id AND  b.pk_id = c.fk_inv_details_id
JOIN tco_accounts ac ON ac.pk_tenant_id = c.fk_tenant_id AND ac.pk_account_code = c.fk_account_code
JOIN tco_inv_budgetyear_config yc ON b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV' AND l.line_item_id in (1010,1020,2020)
LEFT JOIN tfp_inv_header d ON a.fk_tenant_id  = d.fk_tenant_id AND c.fk_inv_action_id = d.pk_inv_action_id
LEFT JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_tenant_id AND b.fk_department_code = oh.fk_department_code
LEFT JOIN tco_parameters p1 ON a.fk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'ALTERCODE_AGG_INV' AND p1.active = 1
LEFT JOIN tco_service_values SV ON a.fk_tenant_id = SV.fk_tenant_id AND c.fk_function_code = SV.fk_function_code
WHERE yc.inv_status IN (0,1,2,7,8)
AND b.type IN ('i', 'f')
AND oh.fk_org_version = @org_version
AND c.pk_id IN (SELECT min(it.pk_id) FROM tfp_inv_transactions it, tco_accounts ac2, gmd_reporting_line rl2 
WHERE it.fk_account_code = ac2.pk_account_code
AND it.fk_tenant_id = ac2.pk_tenant_id
AND ac2.fk_kostra_account_code = rl2.fk_kostra_account_code
AND it.type = 'i' 
AND rl2.report = '55_OVINV'
AND rl2.line_item_id IN (1010,1020)
AND it.budget_year = @budget_year
AND it.fk_tenant_id = @fk_tenant_id
GROUP BY fk_investment_id, budget_year)
--AND b.budget_year <= DATEPART (YEAR, a.completion_date)
AND b.budget_year = @budget_year
AND a.fk_tenant_id = @fk_tenant_id
AND a.monthrep_flag = 1
AND b.fk_change_id IN (SELECT fk_change_id FROM @change_id_table)

UNION ALL

SELECT  a.fk_tenant_id, yc.fk_org_id, @inv_org_level as org_level, service_id = CASE 
	WHEN @service_level = 1 THEN SV.service_id_1
	WHEN @service_level = 2 THEN SV.service_id_2
	WHEN @service_level = 3 THEN SV.service_id_3
	WHEN @service_level = 4 THEN SV.service_id_4
	WHEN @service_level = 5 THEN SV.service_id_5
	ELSE ''
	END, -2 AS pk_investment_id,
total_change_amount = CASE WHEN yc.inv_status = 2 THEN 0 ELSE a.previously_budgeted END,
yearly_forcast_value = 0,
0 as status,0 as risk,newid() as status_desc_id,@forecast_period as forecast_period,getdate() as updated,@user_id as updated_by,convert(int,CONCAT(@budget_year+10, '4')) as est_finish_quarter,1 as is_reported,ISNULL(b.fk_program_code,1) AS fk_program_code,
c.fk_account_code, c.department_code, c.fk_function_code, c.fk_project_code, c.free_dim_1, c.free_dim_2, c.free_dim_3, c.free_dim_4,
b.approved_cost as approved_cost
FROM tco_investments a
JOIN tco_investment_detail b ON a.pk_investment_id = b.fk_investment_id AND a.fk_tenant_id = b.fk_tenant_id
JOIN tco_inv_budgetyear_config yc ON b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year
JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV' AND l.line_item_id in (1010,1020,2020)
LEFT JOIN tfp_inv_transactions c ON a.pk_investment_id = c.fk_investment_id AND a.fk_tenant_id = c.fk_tenant_id AND  b.pk_id = c.fk_inv_details_id
LEFT JOIN tfp_inv_header d ON a.fk_tenant_id  = d.fk_tenant_id AND c.fk_inv_action_id = d.pk_inv_action_id
LEFT JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_tenant_id AND b.fk_department_code = oh.fk_department_code
LEFT JOIN tco_parameters p1 ON a.fk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'ALTERCODE_AGG_INV' AND p1.active = 1
LEFT JOIN tco_service_values SV ON a.fk_tenant_id = SV.fk_tenant_id AND c.fk_function_code = SV.fk_function_code
WHERE yc.inv_status IN (0,1,2,7,8)
AND b.type IN ('i', 'f')
AND oh.fk_org_version = @org_version
AND c.pk_id IN (SELECT min(it.pk_id) FROM tfp_inv_transactions it, tco_accounts ac2, gmd_reporting_line rl2 
WHERE it.fk_account_code = ac2.pk_account_code
AND it.fk_tenant_id = ac2.pk_tenant_id
AND ac2.fk_kostra_account_code = rl2.fk_kostra_account_code
AND it.type = 'i' 
AND rl2.report = '55_OVINV'
AND rl2.line_item_id in (1010,1020)
AND it.budget_year = @budget_year
AND it.fk_tenant_id = @fk_tenant_id
GROUP BY fk_investment_id, budget_year)
--AND b.budget_year <= DATEPART (YEAR, a.completion_date)
AND b.budget_year = @budget_year
AND a.fk_tenant_id = @fk_tenant_id
AND a.monthrep_flag = 0
AND b.fk_change_id IN (SELECT fk_change_id FROM @change_id_table)


) T
--WHERE NOT EXISTS  (SELECT * FROM tmr_investment_status s WHERE T.fk_tenant_id = s.fk_tenant_id AND s.fk_investment_id = T.pk_investment_id AND s.forecast_period = @forecast_period)
GROUP BY fk_tenant_id,org_id,org_level,pk_investment_id,status,risk,forecast_period,est_finish_quarter,is_reported,
fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,fk_program_code


IF (SELECT COUNT(*) FROM @inv_status_header WHERE fk_investment_id = -2) = 0
BEGIN

INSERT INTO @inv_status_header (fk_tenant_id,org_id,org_level,service_id,fk_investment_id,total_change_amount,yearly_forcast_value,status,risk,status_desc,forecast_period,updated,updated_by,est_finish_quarter,is_reported,fk_prog_code, status_desc_id_history,
fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, approval_cost)
VALUES  (@fk_tenant_id,'',0,'',-2,0,0,'','','','',getdate(),@user_id,0,0,'', '********-0000-0000-0000-********0000',
'', '', '', '', '', '', '', '', 0)

END


IF @investment_init_type in ('', 'TotalBudget')
BEGIN 

PRINT 'Fetch gl amount ' + convert(varchar(400),GETDATE());

INSERT INTO @inv_gl_table (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,fk_program_code,
gl_amount, type)
SELECT a.fk_tenant_id, @forecast_period, ISNULL(i.pk_investment_id,-2),a.fk_account_code, a.department_code, a.fk_function_code, ISNULL(a.fk_project_code,''), a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,a.fk_prog_code,
SUM(a.amount), type = case when line_item_id in (1010,1020) then 'i' else 'f' end
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id IN (1010,1020,2020)
LEFT JOIN tco_projects p ON p.fk_tenant_id = a.fk_tenant_id AND p.pk_project_code = a.fk_project_code AND a.gl_year BETWEEN datepart(year,p.date_from) AND  datepart(year,p.date_to)
LEFT JOIN tco_main_projects mp ON p.fk_tenant_id = mp.fk_tenant_id AND p.fk_main_project_code = mp.pk_main_project_code AND a.gl_year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART (YEAR,mp.budget_year_to)
LEFT JOIN (SELECT DISTINCT ih.pk_investment_id, ih.fk_tenant_id, ih.fk_main_project_code, id.budget_year, ih.fk_portfolio_code, ih.status, ih.monthrep_flag FROM tco_investment_detail id, tco_investments ih  WHERE ih.fk_tenant_id = id.fk_tenant_id AND ih.pk_investment_id = id.fk_investment_id) i ON mp.fk_tenant_id = i.fk_tenant_id AND mp.pk_main_project_code = i.fk_main_project_code AND i.fk_main_project_code != '' AND i.budget_year = a.gl_year AND i.monthrep_flag =1 AND gl_year = @budget_year
LEFT JOIN tco_inv_budgetyear_config yc ON i.pk_investment_id = yc.fk_investment_id AND i.fk_tenant_id = yc.fk_tenant_id AND i.budget_year = yc.budget_year AND yc.inv_status IN (0,1,2,7,8)
WHERE a.fk_tenant_id = @fk_tenant_id AND a.gl_year = @budget_year
GROUP BY a.fk_tenant_id, i.pk_investment_id,a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, 
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,a.fk_prog_code,line_item_id; 

PRINT 'UPDATE gl table with program code ' + convert(varchar(400),GETDATE());

UPDATE @inv_gl_table SET fk_program_code = b.fk_program_code
FROM @inv_gl_table a
JOIN (SELECT fk_investment_id, MIN(fk_program_code) AS fk_program_code FROM tco_investment_detail WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @forecast_period/100 GROUP BY fk_investment_id) b ON a.fk_investment_id = b.fk_investment_id
WHERE a.forecast_period = @forecast_period AND a.fk_tenant_id = @fk_tenant_id AND a.fk_program_code IS NULL;

UPDATE @inv_gl_table SET fk_program_code = b.fk_program_code
FROM @inv_gl_table a
JOIN (SELECT fk_investment_id, MIN(fk_program_code) AS fk_program_code FROM tco_investment_detail WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @forecast_period/100 GROUP BY fk_investment_id) b ON a.fk_investment_id = b.fk_investment_id
WHERE a.forecast_period = @forecast_period AND a.fk_tenant_id = @fk_tenant_id AND a.fk_program_code = '';

PRINT 'Fetch budget amount ' + convert(varchar(400),GETDATE());


IF @forecast_period NOT IN (@last_period, @ub_period)
BEGIN
PRINT 'Fetch budget amount 1'

INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type, is_vat_row, fk_invDetail_id)

SELECT fk_tenant_id, @forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
SUM(year_0_amount),
SUM(year_1_amount), SUM(year_2_amount), SUM(year_3_amount), SUM(year_4_amount), SUM(year_5_amount), SUM(year_6_amount), SUM(year_7_amount), SUM(year_8_amount), SUM(year_9_amount), SUM(year_10_amount),
0 as gl_amount,0 as vat_refund, 0 as vat_rate, type,is_vat_row, fk_invDetail_id
FROM (
SELECT  a.fk_tenant_id, b.fk_investment_id,b.fk_account_code, b.fk_department_code, b.fk_function_code, b.fk_project_code, b.free_dim_1, b.free_dim_2, b.free_dim_3, b.free_dim_4, b.fk_alter_code, b.fk_adjustment_code, b.fk_program_code,
year_0_amount = case
	WHEN yc.inv_status = 2 THEN 0
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 1  THEN b.year_1_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 2  THEN b.year_1_amount+b.year_2_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 3  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 4  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 5  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 6  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 7  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 8  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 9  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 10 THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount+b.year_10_amount
	ELSE 0 END,
isnull(c.year_1_amount,0) AS year_1_amount, ISNULL(c.year_2_amount,0) AS year_2_amount, ISNULL(c.year_3_amount,0) AS year_3_amount, ISNULL(c.year_4_amount,0) AS year_4_amount, 

year_5_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  5 THEN b.year_10_amount
	ELSE 0 END, 
year_6_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_10_amount
	ELSE 0 END, 
year_7_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_10_amount
	ELSE 0 END,
year_8_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_10_amount
	ELSE 0 END,
year_9_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_10_amount
	ELSE 0 END,
year_10_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -9 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  -1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_10_amount
	ELSE 0 END,
b.vat_refund, b.vat_rate, b.type, b.is_vat_row, b.fk_invDetail_id
FROM tco_investments a
JOIN tco_investment_detail b ON a.pk_investment_id = b.fk_investment_id AND a.fk_tenant_id = b.fk_tenant_id
LEFT JOIN tfp_inv_transactions c ON a.pk_investment_id = c.fk_investment_id AND a.fk_tenant_id = c.fk_tenant_id AND  b.pk_id = c.fk_inv_details_id
JOIN tco_inv_budgetyear_config yc ON b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year
JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV' AND l.line_item_id in (1010,1020,2020)
LEFT JOIN tfp_budget_changes ch ON c.fk_tenant_id = ch.fk_tenant_id AND c.budget_year = ch.budget_year AND c.fk_change_id = ch.pk_change_id
LEFT JOIN tfp_inv_header d ON a.fk_tenant_id  = d.fk_tenant_id AND c.fk_inv_action_id = d.pk_inv_action_id
LEFT JOIN tco_org_hierarchy OH ON a.fk_tenant_id = OH.fk_tenant_id AND OH.fk_org_version = @org_version AND OH.fk_department_code = b.fk_department_code
LEFT JOIN tco_service_values SV ON a.fk_tenant_id = SV.fk_tenant_id AND c.fk_function_code = SV.fk_function_code
WHERE yc.inv_status IN (0,1,2,7,8)
AND b.type IN ('i', 'f')
--AND b.budget_year <= DATEPART (YEAR, a.completion_date)
AND b.budget_year = @budget_year
AND a.fk_tenant_id = @fk_tenant_id
AND a.monthrep_flag = 1
AND b.fk_change_id IN (SELECT fk_change_id FROM @change_id_table)

UNION ALL

SELECT  a.fk_tenant_id, -2 AS fk_investment_id,b.fk_account_code, b.fk_department_code, b.fk_function_code, b.fk_project_code, b.free_dim_1, b.free_dim_2, b.free_dim_3, b.free_dim_4, b.fk_alter_code, b.fk_adjustment_code, b.fk_program_code,
year_0_amount = case
	WHEN yc.inv_status = 2 THEN 0
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 1  THEN b.year_1_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 2  THEN b.year_1_amount+b.year_2_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 3  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 4  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 5  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 6  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 7  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 8  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 9  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 10 THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount+b.year_10_amount
	ELSE 0 END,
isnull(c.year_1_amount,0) AS year_1_amount, ISNULL(c.year_2_amount,0) AS year_2_amount, ISNULL(c.year_3_amount,0) AS year_3_amount, ISNULL(c.year_4_amount,0) AS year_4_amount, 
year_5_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  5 THEN b.year_10_amount
	ELSE 0 END, 
year_6_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_10_amount
	ELSE 0 END, 
year_7_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_10_amount
	ELSE 0 END,
year_8_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_10_amount
	ELSE 0 END,
year_9_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_10_amount
	ELSE 0 END,
year_10_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -9 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  -1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_10_amount
	ELSE 0 END,
b.vat_refund, b.vat_rate, b.type, b.is_vat_row, b.fk_invDetail_id
FROM tco_investments a
JOIN tco_investment_detail b ON a.pk_investment_id = b.fk_investment_id AND a.fk_tenant_id = b.fk_tenant_id
LEFT JOIN tfp_inv_transactions c ON a.pk_investment_id = c.fk_investment_id AND a.fk_tenant_id = c.fk_tenant_id AND  b.pk_id = c.fk_inv_details_id
JOIN tco_inv_budgetyear_config yc ON b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year
JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV' AND l.line_item_id in (1010,1020,2020)
LEFT JOIN tfp_budget_changes ch ON c.fk_tenant_id = ch.fk_tenant_id AND c.budget_year = ch.budget_year AND c.fk_change_id = ch.pk_change_id
LEFT JOIN tfp_inv_header d ON a.fk_tenant_id  = d.fk_tenant_id AND c.fk_inv_action_id = d.pk_inv_action_id
LEFT JOIN tco_org_hierarchy OH ON a.fk_tenant_id = OH.fk_tenant_id AND OH.fk_org_version = @org_version AND OH.fk_department_code = b.fk_department_code
LEFT JOIN tco_service_values SV ON a.fk_tenant_id = SV.fk_tenant_id AND c.fk_function_code = SV.fk_function_code
WHERE yc.inv_status IN (0,1,2,7,8)
AND b.type IN ('i', 'f')
--AND b.budget_year <= DATEPART (YEAR, a.completion_date)
AND b.budget_year = @budget_year
AND a.fk_tenant_id = @fk_tenant_id
AND a.monthrep_flag = 0
AND b.fk_change_id IN (SELECT fk_change_id FROM @change_id_table)


) T
GROUP BY fk_tenant_id, fk_investment_id,fk_account_code, fk_department_code, 
fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code, type,is_vat_row, fk_invDetail_id;

END


IF @forecast_period  IN (@last_period, @ub_period)
BEGIN

INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type, is_vat_row, fk_invDetail_id,line_item_id)

SELECT fk_tenant_id, @forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
SUM(year_0_amount),
SUM(year_1_amount), SUM(year_2_amount), SUM(year_3_amount), SUM(year_4_amount), SUM(year_5_amount), SUM(year_6_amount), SUM(year_7_amount), SUM(year_8_amount), SUM(year_9_amount), SUM(year_10_amount),
0 as gl_amount,0 as vat_refund, 0 as vat_rate, type,0 AS is_vat_row, 0 AS fk_invDetail_id, line_item_id
FROM (
SELECT  a.fk_tenant_id, b.fk_investment_id,b.fk_account_code, b.fk_department_code, b.fk_function_code, b.fk_project_code, b.free_dim_1, b.free_dim_2, b.free_dim_3, b.free_dim_4, b.fk_alter_code, b.fk_adjustment_code, b.fk_program_code,
year_0_amount = case
	WHEN yc.inv_status = 2 THEN 0
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 1  THEN b.year_1_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 2  THEN b.year_1_amount+b.year_2_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 3  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 4  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 5  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 6  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 7  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 8  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 9  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 10 THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount+b.year_10_amount
	ELSE 0 END,
isnull(c.year_1_amount,0) AS year_1_amount, ISNULL(c.year_2_amount,0) AS year_2_amount, ISNULL(c.year_3_amount,0) AS year_3_amount, ISNULL(c.year_4_amount,0) AS year_4_amount, 

year_5_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  5 THEN b.year_10_amount
	ELSE 0 END, 
year_6_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_10_amount
	ELSE 0 END, 
year_7_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_10_amount
	ELSE 0 END,
year_8_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_10_amount
	ELSE 0 END,
year_9_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_10_amount
	ELSE 0 END,
year_10_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -9 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  -1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_10_amount
	ELSE 0 END,
b.vat_refund, b.vat_rate, b.type, b.is_vat_row, b.fk_invDetail_id, l.line_item_id
FROM tco_investments a
JOIN tco_investment_detail b ON a.pk_investment_id = b.fk_investment_id AND a.fk_tenant_id = b.fk_tenant_id
LEFT JOIN tfp_inv_transactions c ON a.pk_investment_id = c.fk_investment_id AND a.fk_tenant_id = c.fk_tenant_id AND  b.pk_id = c.fk_inv_details_id
JOIN tco_inv_budgetyear_config yc ON b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year
JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV' AND l.line_item_id in (1010,1020,2020)
LEFT JOIN tfp_budget_changes ch ON c.fk_tenant_id = ch.fk_tenant_id AND c.budget_year = ch.budget_year AND c.fk_change_id = ch.pk_change_id
LEFT JOIN tfp_inv_header d ON a.fk_tenant_id  = d.fk_tenant_id AND c.fk_inv_action_id = d.pk_inv_action_id
LEFT JOIN tco_org_hierarchy OH ON a.fk_tenant_id = OH.fk_tenant_id AND OH.fk_org_version = @org_version AND OH.fk_department_code = b.fk_department_code
LEFT JOIN tco_service_values SV ON a.fk_tenant_id = SV.fk_tenant_id AND c.fk_function_code = SV.fk_function_code
WHERE yc.inv_status IN (0,1,2,7,8)
AND b.type IN ('i', 'f')
--AND b.budget_year <= DATEPART (YEAR, a.completion_date)
AND b.budget_year = @budget_year
AND a.fk_tenant_id = @fk_tenant_id
AND a.monthrep_flag = 1
AND b.fk_change_id IN (SELECT fk_change_id FROM @change_id_table)

UNION ALL

SELECT  a.fk_tenant_id, b.fk_investment_id,b.fk_account_code, b.fk_department_code, b.fk_function_code, b.fk_project_code, b.free_dim_1, b.free_dim_2, b.free_dim_3, b.free_dim_4, b.fk_alter_code, b.fk_adjustment_code, b.fk_program_code,
year_0_amount = case
	WHEN yc.inv_status = 2 THEN 0
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 1  THEN b.year_1_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 2  THEN b.year_1_amount+b.year_2_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 3  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 4  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 5  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 6  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 7  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 8  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 9  THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount
	WHEN b.type = 'i' AND b.budget_year - a.start_year = 10 THEN b.year_1_amount+b.year_2_amount+b.year_3_amount+b.year_4_amount+b.year_5_amount+b.year_6_amount+b.year_7_amount+b.year_8_amount+b.year_9_amount+b.year_10_amount
	ELSE 0 END,
isnull(c.year_1_amount,0) AS year_1_amount, ISNULL(c.year_2_amount,0) AS year_2_amount, ISNULL(c.year_3_amount,0) AS year_3_amount, ISNULL(c.year_4_amount,0) AS year_4_amount, 
year_5_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  5 THEN b.year_10_amount
	ELSE 0 END, 
year_6_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  4 THEN b.year_10_amount
	ELSE 0 END, 
year_7_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  3 THEN b.year_10_amount
	ELSE 0 END,
year_8_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  2 THEN b.year_10_amount
	ELSE 0 END,
year_9_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -1 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  1 THEN b.year_10_amount
	ELSE 0 END,
year_10_amount = case 
	WHEN yc.inv_status = 2 THEN 0
	WHEN  b.budget_year - a.start_year = -9 THEN b.year_1_amount
	WHEN  b.budget_year - a.start_year = -8 THEN b.year_2_amount
	WHEN  b.budget_year - a.start_year = -7 THEN b.year_3_amount
	WHEN  b.budget_year - a.start_year = -6 THEN b.year_4_amount
	WHEN  b.budget_year - a.start_year = -5 THEN b.year_5_amount
	WHEN  b.budget_year - a.start_year = -4 THEN b.year_6_amount
	WHEN  b.budget_year - a.start_year = -3 THEN b.year_7_amount
	WHEN  b.budget_year - a.start_year = -2 THEN b.year_8_amount
	WHEN  b.budget_year - a.start_year =  -1 THEN b.year_9_amount
	WHEN  b.budget_year - a.start_year =  0 THEN b.year_10_amount
	ELSE 0 END,
b.vat_refund, b.vat_rate, b.type, b.is_vat_row, b.fk_invDetail_id, l.line_item_id
FROM tco_investments a
JOIN tco_investment_detail b ON a.pk_investment_id = b.fk_investment_id AND a.fk_tenant_id = b.fk_tenant_id
LEFT JOIN tfp_inv_transactions c ON a.pk_investment_id = c.fk_investment_id AND a.fk_tenant_id = c.fk_tenant_id AND  b.pk_id = c.fk_inv_details_id
JOIN tco_inv_budgetyear_config yc ON b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year
JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV' AND l.line_item_id in (1010,1020,2020)
LEFT JOIN tfp_budget_changes ch ON c.fk_tenant_id = ch.fk_tenant_id AND c.budget_year = ch.budget_year AND c.fk_change_id = ch.pk_change_id
LEFT JOIN tfp_inv_header d ON a.fk_tenant_id  = d.fk_tenant_id AND c.fk_inv_action_id = d.pk_inv_action_id
LEFT JOIN tco_org_hierarchy OH ON a.fk_tenant_id = OH.fk_tenant_id AND OH.fk_org_version = @org_version AND OH.fk_department_code = b.fk_department_code
LEFT JOIN tco_service_values SV ON a.fk_tenant_id = SV.fk_tenant_id AND c.fk_function_code = SV.fk_function_code
WHERE yc.inv_status IN (0,1,2,7,8)
AND b.type IN ('i', 'f')
--AND b.budget_year <= DATEPART (YEAR, a.completion_date)
AND b.budget_year = @budget_year
AND a.fk_tenant_id = @fk_tenant_id
AND a.monthrep_flag = 0
AND b.fk_change_id IN (SELECT fk_change_id FROM @change_id_table)

) T
GROUP BY fk_tenant_id, fk_investment_id,fk_account_code, fk_department_code, 
fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code, type,line_item_id;

END

print 'Update approval_cost '  + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail set approval_cost = b.approval_cost
FROM @inv_status_detail a, @inv_status_header b
WHERE 
a.fk_tenant_id = b.fk_tenant_id
AND a.fk_investment_id = b.fk_investment_id
AND a.fk_account_code = b.fk_account_code
AND a.fk_department_code = b.fk_department_code
AND a.fk_function_code = b.fk_function_code
AND a.fk_project_code = b.fk_project_code
AND a.free_dim_1 = b.free_dim_1
AND a.free_dim_2 = b.free_dim_2
AND a.free_dim_3 = b.free_dim_3
AND a.free_dim_4 = b.free_dim_4
AND a.fk_program_code = b.fk_prog_code



PRINT 'Update year_0_amount with previously budgeted ' + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET year_0_amount = year_0_amount + h.total_change_amount
FROM @inv_status_detail id, @inv_status_header h
WHERE 
h.fk_tenant_id = id.fk_tenant_id
AND h.fk_investment_id = id.fk_investment_id
AND h.fk_account_code   = id.fk_account_code
AND h.fk_department_code = id.fk_department_code
AND h.fk_function_code = id.fk_function_code
AND h.fk_project_code  = id.fk_project_code
AND h.free_dim_1 = id.free_dim_1
AND h.free_dim_2 = id.free_dim_2
AND h.free_dim_3 = id.free_dim_3
AND h.free_dim_4 = id.free_dim_4
AND h.forecast_period = id.forecast_period
AND id.is_vat_row = 0
AND id.fk_program_code = h.fk_prog_code


print 'Fetch gl amount into inv_status_detail '  + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET gl_amount = gl.gl_amount
FROM @inv_status_detail id, @inv_gl_table gl
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_investment_id = id.fk_investment_id
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.fk_program_code = id.fk_program_code;


PRINT 'Update vat rates '  + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET vat_rate = tid.vat_rate, vat_refund = tid.vat_refund
FROM @inv_status_detail id, tco_investment_detail tid
WHERE 
tid.fk_tenant_id = id.fk_tenant_id
AND tid.fk_investment_id = id.fk_investment_id
AND tid.fk_account_code   = id.fk_account_code
AND tid.fk_department_code = id.fk_department_code
AND tid.fk_function_code = id.fk_function_code
AND tid.fk_project_code  = id.fk_project_code
AND tid.free_dim_1 = id.free_dim_1
AND tid.free_dim_2 = id.free_dim_2
AND tid.free_dim_3 = id.free_dim_3
AND tid.free_dim_4 = id.free_dim_4
AND tid.fk_program_code = id.fk_program_code
AND id.forecast_period = @forecast_period
AND tid.budget_year = @budget_year;

IF @forecast_period IN (@last_period)
BEGIN

Print 'Insert blank gl-lines in detail table  '  + convert(varchar(400),GETDATE());

INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type)
SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
SUM(gl_amount),0 as vat_refund,0 as  vat_rate, gl.type
FROM @inv_gl_table gl
WHERE NOT EXISTS 
(SELECT * FROM @inv_status_detail id 
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_investment_id = id.fk_investment_id
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.fk_program_code = id.fk_program_code
) 
GROUP BY fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,  fk_program_code, type
;

Print 'Mark transactions that will use budget amount '  + convert(varchar(400),GETDATE());

--UPDATE @inv_status_detail SET use_budget = 1
--FROM @inv_status_detail id, (
--SELECT fk_investment_id, fk_program_code, SUM(year_1_amount) AS year_1_amount, SUM(gl_amount) as gl_amount
--FROM @inv_status_detail
--WHERE fk_program_code = 2
--GROUP BY fk_investment_id, fk_program_code
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE id.fk_investment_id = B.fk_investment_id
--AND id.fk_program_code = B.fk_program_code;


UPDATE @inv_status_detail SET use_budget = 1
FROM @inv_status_detail id, tco_inv_budgetyear_config i
WHERE id.fk_investment_id = i.fk_investment_id
AND id.fk_tenant_id = i.fk_tenant_id
AND i.budget_year = @budget_year
AND i.inv_status = 2;

Print 'Mark transactions that will update year 2 amount '  + convert(varchar(400),GETDATE());


--UPDATE @inv_status_detail SET update_year2 = 1
--FROM @inv_status_detail id, (
--SELECT fk_tenant_id, fk_investment_id, SUM(year_1_amount) year_1_amount, SUM(gl_amount) gl_amount
--FROM @inv_status_detail 
--GROUP BY fk_tenant_id, fk_investment_id
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE use_budget = 0 
--AND id.fk_tenant_id = B.fk_tenant_id
--AND id.fk_investment_id = B.fk_investment_id;

UPDATE @inv_status_detail SET update_year2 = 1
WHERE use_budget = 0 



PRINT 'UPDATE with gl_amount ' + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET year_2_amount = year_2_amount+year_1_amount-gl_amount
WHERE update_year2 = 1 AND use_budget = 0;

UPDATE @inv_status_detail SET year_1_amount = gl_amount WHERE use_budget = 0;

END


IF @forecast_period IN (@ub_period)
BEGIN

UPDATE @inv_status_detail SET gl_amount = 0


PRINT 'Calculate the value to spread '  + convert(varchar(400),GETDATE());

SELECT newid() as pk_id,fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
vat_rate, vat_refund,vat_rate/100*vat_refund/100 as vat_pct,'i' as type,SUM(year_1_amount) as year_1_amount, convert(dec(18,2),0) as total_amount, convert(dec(18,6),0) as pct_value,
convert(dec(18,2),0) as gl_amount, convert(dec(18,2),0) as vat_amount, line_item_id
INTO #distribution_table FROM @inv_status_detail
WHERE is_vat_row = 0 AND fk_account_code != @vat_account
GROUP BY fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
vat_rate, vat_refund, line_item_id

UPDATE #distribution_table SET type = 'f' WHERE line_item_id = 2020


UPDATE a SET a.total_amount = S.year_1_amount
FROM #distribution_table a
join 
(SELECT fk_tenant_id, fk_investment_id,line_item_id,SUM(year_1_amount) as year_1_amount
FROM @inv_status_detail
WHERE is_vat_row = 0 AND fk_account_code != @vat_account
GROUP BY fk_tenant_id, fk_investment_id, line_item_id
HAVING sum(year_1_amount) != 0) S
ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.line_item_id = S.line_item_id

UPDATE a SET a.gl_amount = S.gl_amount
FROM #distribution_table a
join 
(SELECT fk_tenant_id, fk_investment_id,type,SUM(gl_amount) as gl_amount
FROM @inv_gl_table 
GROUP BY fk_tenant_id, fk_investment_id, type
HAVING sum(gl_amount) != 0) S
ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.type = S.type


DELETE a
FROM @inv_gl_table a
JOIN 
(SELECT DISTINCT fk_tenant_id, fk_investment_id,type
FROM #distribution_table) S
ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.type = S.type


UPDATE a SET a.pct_value = a.year_1_amount/a.total_amount
FROM #distribution_table a
WHERE total_amount != 0

-- Set one of the lines to 100% when there is no budget.
UPDATE #distribution_table
SET pct_value = 100
WHERE pk_id IN (
SELECT min(pk_id) FROM #distribution_table 
WHERE total_amount = 0 
GROUP BY total_amount, fk_investment_id)

UPDATE a SET a.gl_amount = round(a.gl_amount * a.pct_value,0)
FROM #distribution_table a

UPDATE a SET a.vat_amount = round(a.gl_amount / (1+a.vat_pct) * vat_pct,0)
FROM #distribution_table a

UPDATE @inv_status_detail SET gl_amount = gl.gl_amount, use_budget = 0
FROM @inv_status_detail id, #distribution_table gl
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_investment_id = id.fk_investment_id
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.fk_program_code = id.fk_program_code
AND gl.fk_adjustment_code = id.fk_adjustment_code
AND gl.fk_alter_code = id.fk_alter_code
AND gl.type = id.type



Print 'Insert vat rows'

--INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
--gl_amount,vat_refund, vat_rate,type, is_vat_row, use_budget)
--SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 
--0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
--vat_amount*-1 as  gl_amount, 0 as vat_refund, 0 as vat_rate,type, 1 as is_vat_row,0 as use_budget
--FROM #distribution_table


--INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
--gl_amount,vat_refund, vat_rate,type, is_vat_row,use_budget)
--SELECT fk_tenant_id, forecast_period, fk_investment_id,@vat_account as fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 
--0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
--vat_amount as  gl_amount, 0 as vat_refund, 0 as vat_rate,type, 1 as is_vat_row, 0 as use_budget
--FROM #distribution_table


Print 'Insert  gl-lines without budget match in detail table  '  + convert(varchar(400),GETDATE());

INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type, use_budget)
SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
SUM(gl_amount),0 as vat_refund,0 as  vat_rate, gl.type,0 as use_budget
FROM @inv_gl_table gl
GROUP BY fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,  fk_program_code, type
;


Print 'Mark transactions that will use budget amount '  + convert(varchar(400),GETDATE());

--UPDATE @inv_status_detail SET use_budget = 1
--FROM @inv_status_detail id, (
--SELECT fk_investment_id, fk_program_code, SUM(year_1_amount) AS year_1_amount, SUM(gl_amount) as gl_amount
--FROM @inv_status_detail
--WHERE fk_program_code = 2
--GROUP BY fk_investment_id, fk_program_code
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE id.fk_investment_id = B.fk_investment_id
--AND id.fk_program_code = B.fk_program_code;


UPDATE @inv_status_detail SET use_budget = 1
FROM @inv_status_detail id, tco_inv_budgetyear_config i
WHERE id.fk_investment_id = i.fk_investment_id
AND id.fk_tenant_id = i.fk_tenant_id
AND i.budget_year = @budget_year
AND i.inv_status = 2;

Print 'Mark transactions that will update year 2 amount '  + convert(varchar(400),GETDATE());


--UPDATE @inv_status_detail SET update_year2 = 1
--FROM @inv_status_detail id, (
--SELECT fk_tenant_id, fk_investment_id, SUM(year_1_amount) year_1_amount, SUM(gl_amount) gl_amount
--FROM @inv_status_detail 
--GROUP BY fk_tenant_id, fk_investment_id
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE use_budget = 0 
--AND id.fk_tenant_id = B.fk_tenant_id
--AND id.fk_investment_id = B.fk_investment_id;

UPDATE @inv_status_detail SET update_year2 = 1
WHERE use_budget = 0 



PRINT 'UPDATE with gl_amount ' + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET year_2_amount = year_2_amount+year_1_amount-gl_amount
WHERE update_year2 = 1 AND use_budget = 0;

UPDATE @inv_status_detail SET year_1_amount = gl_amount WHERE use_budget = 0;

DELETE FROM @inv_status_detail 
WHERE  year_0_amount = 0
AND year_1_amount = 0 
AND year_2_amount = 0
AND year_3_amount = 0
AND year_4_amount = 0
AND year_5_amount = 0
AND year_6_amount = 0 
AND year_7_amount = 0 
AND year_8_amount = 0
AND year_9_amount = 0
AND year_10_amount = 0

END

PRINT 'Data ready for insert for option TotalBudget'

END

IF @investment_init_type = 'ActualYtd'
BEGIN
PRINT 'Running ActualYtd'


SELECT fk_tenant_id,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year,fk_prog_code,
SUM(amount) AS amount
INTO #TEMP_ACC_DATA
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id IN (1010,1020,2020)
WHERE fk_tenant_id = @fk_tenant_id 
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
HAVING SUM(amount) != 0;


INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,
fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type)
SELECT a.fk_tenant_id, @forecast_period, ISNULL(i.pk_investment_id,-2), a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, a.fk_prog_code,
year_0_amount = case when gl_year < @budget_year then amount else 0 end,
year_1_amount = case when gl_year = @budget_year then amount else 0 end, 
0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
amount as gl_amount,0 as vat_refund, 0 as vat_rate,type = CASE WHEN l.line_item_id in (1010,1020) THEN 'i' ELSE 'f' END
FROM #TEMP_ACC_DATA a
LEFT JOIN tco_projects p ON p.fk_tenant_id = a.fk_tenant_id AND p.pk_project_code = a.fk_project_code AND a.gl_year BETWEEN datepart(year,p.date_from) AND  datepart(year,p.date_to)
LEFT JOIN tco_main_projects mp ON p.fk_tenant_id = mp.fk_tenant_id AND p.fk_main_project_code = mp.pk_main_project_code AND a.gl_year BETWEEN datepart(year,mp.budget_year_from) AND  datepart(year,mp.budget_year_to)
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id IN (1010,1020,2020)
LEFT JOIN (SELECT DISTINCT ih.original_finish_year,ih.pk_investment_id, ih.fk_tenant_id, ih.fk_main_project_code, id.budget_year, ih.fk_portfolio_code, ih.status, ih.monthrep_flag FROM tco_investment_detail id, tco_investments ih  WHERE ih.fk_tenant_id = id.fk_tenant_id AND ih.pk_investment_id = id.fk_investment_id AND ih.monthrep_flag = 1) i ON mp.fk_tenant_id = i.fk_tenant_id AND mp.pk_main_project_code = i.fk_main_project_code AND i.fk_main_project_code != '' AND i.budget_year = a.gl_year 
LEFT JOIN tco_inv_budgetyear_config yc ON i.pk_investment_id = yc.fk_investment_id AND i.fk_tenant_id = yc.fk_tenant_id AND i.budget_year = yc.budget_year AND yc.inv_status in (0,1,2,7,8) 
WHERE a.fk_tenant_id = @fk_tenant_id AND a.fk_project_code != '' 
;

END


PRINT 'Find owner org of investment ' + convert(varchar(400),GETDATE());

IF @inv_org_level = 2 
BEGIN

INSERT INTO @inv_owner_table (org_id_header, org_id_detail, fk_investment_id)
SELECT DISTINCT a.org_id, d.org_id_2, a.fk_investment_id FROM @inv_status_header a,
(SELECT DISTINCT a.fk_investment_id, oh.org_id_2
FROM @inv_status_detail a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version) d
WHERE a.fk_investment_id = d.fk_investment_id
END


IF @inv_org_level = 3 
BEGIN

INSERT INTO @inv_owner_table (org_id_header, org_id_detail, fk_investment_id)
SELECT DISTINCT a.org_id, d.org_id_3, a.fk_investment_id FROM @inv_status_header a,
(SELECT DISTINCT a.fk_investment_id, oh.org_id_3
FROM @inv_status_detail a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version) d
WHERE a.fk_investment_id = d.fk_investment_id
END


IF @inv_org_level = 4
BEGIN

INSERT INTO @inv_owner_table (org_id_header, org_id_detail, fk_investment_id)
SELECT DISTINCT a.org_id, d.org_id_4, a.fk_investment_id FROM @inv_status_header a,
(SELECT DISTINCT a.fk_investment_id, oh.org_id_4
FROM @inv_status_detail a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version) d
WHERE a.fk_investment_id = d.fk_investment_id
END


IF @inv_org_level = 5
BEGIN

INSERT INTO @inv_owner_table (org_id_header, org_id_detail, fk_investment_id)
SELECT DISTINCT a.org_id, d.org_id_5, a.fk_investment_id FROM @inv_status_header a,
(SELECT DISTINCT a.fk_investment_id, oh.org_id_5
FROM @inv_status_detail a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version) d
WHERE a.fk_investment_id = d.fk_investment_id
END


DELETE FROM @inv_owner_table WHERE fk_investment_id IN (
SELECT DISTINCT fk_investment_id FROM @inv_owner_table WHERE org_id_header = org_id_detail)

IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = 'MR_INV_FILTER_BY_SA' AND param_value = 'TRUE' AND fk_tenant_id = @fk_tenant_id AND active = 1) = 0 
BEGIN

UPDATE @inv_status_header SET org_id = D.org_id_detail
FROM @inv_status_header a,
(SELECT fk_investment_id, MIN(org_id_detail) AS org_id_detail FROM @inv_owner_table GROUP BY fk_investment_id) D
WHERE a.fk_investment_id = D.fk_investment_id

END


UPDATE @inv_status_detail SET fk_program_code = (SELECT MIN(pk_prog_code) FROM tco_inv_program WHERE default_flag = 1 AND status = 1 AND fk_tenant_id = @fk_tenant_id)
WHERE fk_program_code is null or fk_program_code = '';


PRINT 'INSERT INTO tmr_investment_status_detail '  + convert(varchar(400),GETDATE());

INSERT INTO tmr_investment_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code,  fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
vat_refund, vat_rate, updated, updated_by,type, budget_type, is_vat_row, fk_invDetail_id)
SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code,  fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
vat_refund, vat_rate, GETDATE() updated, @user_id AS updated_by, type,1000 as budget_type, is_vat_row,-1
FROM @inv_status_detail;

IF @forecast_period = @ub_period
BEGIN

SELECT fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
vat_rate, vat_refund,vat_rate/100*vat_refund/100 as vat_pct, type,
year_1_amount, year_2_amount,
convert(dec(18,2),0) as vat_amount_1,
convert(dec(18,2),0) as vat_amount_2, pk_id as fk_invDetail_id
INTO #vat_calc_table 
FROM tmr_investment_status_detail
WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period
AND is_vat_row = 0 AND fk_account_code != @vat_account

DELETE FROM #vat_calc_table where vat_pct = 0

DELETE FROM #vat_calc_table WHERE year_1_amount = 0 AND year_2_amount = 0



UPDATE a SET a.vat_amount_1 = round(a.year_1_amount / (1+a.vat_pct) * vat_pct,0),
a.vat_amount_2 = round(a.year_2_amount / (1+a.vat_pct) * vat_pct,0)
FROM #vat_calc_table a

INSERT INTO tmr_investment_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code,  fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
vat_refund, vat_rate, updated, updated_by,type, budget_type, is_vat_row, fk_invDetail_id)
SELECT 
fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code,  fk_adjustment_code, fk_program_code,
0 AS year_0_amount,vat_amount_1 * -1 as year_1_amount, vat_amount_2 * -1 as year_2_amount,
0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
0 as vat_refund, 0 as vat_rate, getdate() as updated,@user_id as updated_by,type,1000 as budget_type, 1 as is_vat_row, fk_invDetail_id
FROM #vat_calc_table

INSERT INTO tmr_investment_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code,  fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
vat_refund, vat_rate, updated, updated_by,type, budget_type, is_vat_row, fk_invDetail_id)
SELECT 
fk_tenant_id, forecast_period, fk_investment_id,@vat_account, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code,  fk_adjustment_code, fk_program_code,
0 AS year_0_amount,vat_amount_1 as year_1_amount, vat_amount_2 as year_2_amount,
0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
0 as vat_refund, 0 as vat_rate, getdate() as updated,@user_id as updated_by,type,1000 as budget_type, 1 as is_vat_row, fk_invDetail_id
FROM #vat_calc_table

END


INSERT INTO tmr_investment_status (fk_tenant_id,org_id,org_level,service_id,fk_investment_id,total_change_amount,yearly_forcast_value,status,risk,status_desc,forecast_period,updated,updated_by,est_finish_quarter,is_reported,fk_prog_code, status_desc_id_history)
SELECT 
fk_tenant_id,org_id,org_level,service_id,fk_investment_id,total_change_amount,yearly_forcast_value,status,risk,status_desc,forecast_period,updated,updated_by,est_finish_quarter,is_reported,fk_prog_code, status_desc_id_history
FROM @inv_status_header

PRINT 'FINISH: Copy new investments from last budget ' + convert(varchar(400),GETDATE());

PRINT 'START: Copy investments from previous forecast ' + convert(varchar(400),GETDATE());



UPDATE tmr_investment_status SET status = b.status, risk = b.risk, is_reported = b.is_reported, est_finish_quarter = b.est_finish_quarter, fin_status = b.fin_status, quality = b.quality
FROM tmr_investment_status a, (SELECT DISTINCT fk_tenant_id,fk_investment_id, org_id, org_level, '' as service_id, status, risk, is_reported, est_finish_quarter, fin_status, quality  
FROM tmr_investment_status b WHERE b.forecast_period = @prev_forecast_period AND b.fk_tenant_id = @fk_tenant_id) b
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.forecast_period = @forecast_period
AND a.fk_investment_id = b.fk_investment_id
AND a.org_id = b.org_id
AND a.org_level = b.org_level
--AND a.service_id = b.service_id





IF @forecast_period IN (@last_period, @ub_period)
BEGIN

PRINT 'UPDATE everythig to be reported when period is 12 ' + convert(varchar(400),GETDATE());

UPDATE tmr_investment_status SET is_reported = 1 WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period;

END


PRINT 'FINISH: Copy investments from previous forecast ' + convert(varchar(400),GETDATE());

PRINT 'START: Copy investments description from already reported when reinitilized '  + convert(varchar(400),GETDATE());

UPDATE tmr_investment_status SET status_desc = b.status_desc, status_desc_id_history = b.status_desc_id_history,
is_reported = b.is_reported, est_finish_quarter = b.est_finish_quarter, fin_status = b.fin_status, quality = b.quality,
status = b.status, risk = b.risk
FROM tmr_investment_status a, #temp_investment_status b
WHERE a.fk_investment_id = b.fk_investment_id
AND a.fk_tenant_id = b.fk_tenant_id
AND a.forecast_period = b.forecast_period
AND a.org_id = b.org_id
AND a.org_level = b.org_level
AND a.service_id = b.service_id

PRINT 'START: Delete from tbu_forecast_transactions '  + convert(varchar(400),GETDATE());

	BEGIN
	SET  @continue = 1

	WHILE @continue = 1
	BEGIN
		PRINT GETDATE()
		SET ROWCOUNT 50000
		BEGIN TRANSACTION
		
		DELETE a FROM tbu_forecast_transactions a
		JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
		JOIN gco_kostra_accounts ka on ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'investment'
		WHERE a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period;
		
		SET @rowcount = @@rowcount 
		COMMIT
		PRINT GETDATE()
		IF @rowcount = 0
		BEGIN
			SET @continue = 0
		END
	END

	END

PRINT 'FINISH: Delete from tbu_forecast_transactions '  + convert(varchar(400),GETDATE());

IF @investment_init_type in ('', 'TotalBudget') AND @forecast_period NOT IN (@ub_period)
	BEGIN
	
	PRINT 'Update financing from finplan'  + convert(varchar(400),GETDATE());

	INSERT INTO tbu_forecast_transactions (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code, fk_alter_code) 
	SELECT NEWID() AS pk_id,@forecast_period as forecast_period,NEWID() AS bu_trans_id,fk_tenant_id,0 AS action_type,0 AS line_order,
	fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,
	0 AS fk_employment_id,'' AS description,budget_year,convert(int,CONCAT(@forecast_period / 100,'12')) AS  period,
	1000 AS budget_type,year_1_amount,year_1_amount,year_1_amount,year_1_amount,0 as fk_key_id,0 as allocation_pct,
	year_1_amount as total_amount,getdate() as updated, @user_id as updated_by,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,
	fk_prog_code,'' as fk_alter_code
	FROM tfp_inv_transactions a
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id NOT IN (1010,1020,2020)
	WHERE budget_year = @budget_year
	AND fk_tenant_id = @fk_tenant_id
	--AND a.fk_investment_id = 0
	--AND type IN ('i', 'f')

	INSERT INTO tbu_forecast_transactions (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code, fk_alter_code) 
	SELECT NEWID() AS pk_id,@forecast_period as forecast_period,NEWID() AS bu_trans_id,fk_tenant_id,0 AS action_type,0 AS line_order,
	fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,
	0 AS fk_employment_id,'' AS description,budget_year,convert(int,CONCAT(@forecast_period / 100,'12')) AS  period,
	1000 AS budget_type,year_1_amount,year_1_amount,year_1_amount,year_1_amount,0 as fk_key_id,0 as allocation_pct,
	year_1_amount as total_amount,getdate() as updated, @user_id as updated_by,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,
	fk_prog_code,'' as fk_alter_code
	FROM tfp_inv_transactions a
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id IN (1010,1020,2020)
	WHERE budget_year = @budget_year
	AND fk_tenant_id = @fk_tenant_id
	AND a.fk_investment_id = 0

	END

IF @investment_init_type in ('ActualYtd') AND @forecast_period NOT IN (@ub_period)


	BEGIN 
	PRINT 'Update financing from accounting'  + convert(varchar(400),GETDATE());

	INSERT INTO tbu_forecast_transactions (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,
	department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,
	description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code, fk_alter_code) 
	SELECT NEWID() AS pk_id,@forecast_period as forecast_period,NEWID() AS bu_trans_id,fk_tenant_id,0 AS action_type,0 AS line_order,
	fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,
	0 AS fk_employment_id,'' AS description,gl_year,convert(int,CONCAT(@forecast_period / 100,'12')) AS  period,
	1000 AS budget_type,amount AS year_1_amount,0,0,0,0 as fk_key_id,0 as allocation_pct,
	amount as total_amount,getdate() as updated, @user_id as updated_by,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,
	fk_prog_code,'' as fk_alter_code
	FROM tfp_accounting_data a
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id NOT IN (1010,1020,2020)
	WHERE gl_year = @budget_year
	AND fk_tenant_id = @fk_tenant_id

END

IF @forecast_period  = @ub_period
	BEGIN
	-- BUG 63443. Use accounting for investment records 630,640 and budget for other financing items 
	PRINT 'update tbu_forecast transactions for UB period'

	INSERT INTO tbu_forecast_transactions (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,
	department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,
	description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code, fk_alter_code) 
	SELECT NEWID() AS pk_id,@forecast_period as forecast_period,NEWID() AS bu_trans_id,fk_tenant_id,0 AS action_type,0 AS line_order,
	fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,
	0 AS fk_employment_id,'' AS description,gl_year,convert(int,CONCAT(@forecast_period / 100,'12')) AS  period,
	1000 AS budget_type,amount AS year_1_amount,0,0,0,0 as fk_key_id,0 as allocation_pct,
	amount as total_amount,getdate() as updated, @user_id as updated_by,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,
	fk_prog_code,'' as fk_alter_code
	FROM tfp_accounting_data a
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id  IN (2010,2060)
	WHERE gl_year = @budget_year
	AND fk_tenant_id = @fk_tenant_id

	INSERT INTO tbu_forecast_transactions (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code, fk_alter_code) 
	SELECT NEWID() AS pk_id,@forecast_period as forecast_period,NEWID() AS bu_trans_id,fk_tenant_id,0 AS action_type,0 AS line_order,
	fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,
	0 AS fk_employment_id,'' AS description,budget_year,convert(int,CONCAT(@forecast_period / 100,'12')) AS  period,
	1000 AS budget_type,year_1_amount,year_1_amount,year_1_amount,year_1_amount,0 as fk_key_id,0 as allocation_pct,
	year_1_amount as total_amount,getdate() as updated, @user_id as updated_by,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,
	fk_prog_code,'' as fk_alter_code
	FROM tfp_inv_transactions a
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'AND rl.line_item_id NOT IN (1010,1020,2020,2010,2060)
	WHERE budget_year = @budget_year
	AND fk_tenant_id = @fk_tenant_id

	END
	
UPDATE tbu_forecast_transactions set fk_prog_code = (SELECT MIN(pk_prog_code) FROM tco_inv_program WHERE default_flag = 1 AND fk_tenant_id = @fk_tenant_id)
FROM tbu_forecast_transactions a
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id NOT IN (1010,1020,2020)
WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period AND fk_prog_code = '' 



go