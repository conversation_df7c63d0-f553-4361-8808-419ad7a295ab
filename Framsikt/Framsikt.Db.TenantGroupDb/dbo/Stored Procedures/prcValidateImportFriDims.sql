CREATE OR ALTER PROCEDURE [dbo].[Prcvalidateimportfridims] @tenant_id      INT,
                                                  @user_id        INT,
                                                  @dimension_id   INT,
                                                  @dimension_type VARCHAR(20),
                                                  @free_dim_1     VARCHAR(25),
                                                  @free_dim_2     VARCHAR(25),
                                                  @free_dim_3     VARCHAR(25),
                                                  @free_dim_4     VARCHAR(25),
                                                  @job_id         BIGINT
AS
    IF( @job_id = -1 )
      BEGIN
          UPDATE tbu_stage_dimensions_import
          SET    project_code_error = 0,
                 project_name_error = 0,
                 year_from_error = 0,
                 year_to_error = 0,
                 free_dim_1_error = 0,
                 free_dim_2_error = 0,
                 free_dim_3_error = 0,
                 free_dim_4_error = 0,
                 free_dim_1_desc_error = 0,
                 free_dim_2_desc_error = 0,
                 free_dim_3_desc_error = 0,
                 free_dim_4_desc_error = 0,
                 error_count = 0
          WHERE  fk_tenant_id = @tenant_id
                 AND [user_id] = @user_id
                 AND dimension_type = @dimension_id;
      END
    ELSE
      BEGIN
          UPDATE tbu_stage_dimensions_import
          SET    project_code_error = 0,
                 project_name_error = 0,
                 year_from_error = 0,
                 year_to_error = 0,
                 free_dim_1_error = 0,
                 free_dim_2_error = 0,
                 free_dim_3_error = 0,
                 free_dim_4_error = 0,
                 free_dim_1_desc_error = 0,
                 free_dim_2_desc_error = 0,
                 free_dim_3_desc_error = 0,
                 free_dim_4_desc_error = 0,
                 error_count = 0
          WHERE  fk_tenant_id = @tenant_id
                 AND [job_id] = @job_id
                 AND dimension_type = @dimension_id;
      END

    IF( Lower(Trim(@free_dim_1)) = Lower(Trim('free_dim_1')) )
      BEGIN
          IF( @job_id = -1 )
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_1_error = free_dim_1_error + 1,
                       error_count = error_count + 1
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_1 IS NULL
                              OR free_dim_1 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_1_desc_error = free_dim_1_desc_error + 1,
                       error_count = error_count + 1
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_1_desc IS NULL
                              OR free_dim_1_desc = '' )
            END
          ELSE
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_1_error = free_dim_1_error + 1,
                       error_count = error_count + 1
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_1 IS NULL
                              OR free_dim_1 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_1_desc_error = free_dim_1_desc_error + 1,
                       error_count = error_count + 1
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_1 IS NULL
                              OR free_dim_1 = '' )
            END
      END

    IF( Lower(Trim(@free_dim_2)) = Lower(Trim('free_dim_2')) )
      BEGIN
          IF( @job_id = -1 )
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_2_error = free_dim_2_error + 2,
                       error_count = error_count + 2
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_2 IS NULL
                              OR free_dim_2 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_2_desc_error = free_dim_2_desc_error + 2,
                       error_count = error_count + 2
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_2_desc IS NULL
                              OR free_dim_2_desc = '' )
            END
          ELSE
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_2_error = free_dim_2_error + 2,
                       error_count = error_count + 2
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_2 IS NULL
                              OR free_dim_2 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_2_desc_error = free_dim_2_desc_error + 2,
                       error_count = error_count + 2
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_2_desc IS NULL
                              OR free_dim_2_desc = '' )
            END
      END

    IF( Lower(Trim(@free_dim_3)) = Lower(Trim('free_dim_3')) )
      BEGIN
          IF( @job_id = -1 )
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_3_error = free_dim_3_error + 3,
                       error_count = error_count + 3
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_3 IS NULL
                              OR free_dim_3 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_3_desc_error = free_dim_3_desc_error + 3,
                       error_count = error_count + 3
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_3_desc IS NULL
                              OR free_dim_3_desc = '' )
            END
          ELSE
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_3_error = free_dim_3_error + 3,
                       error_count = error_count + 3
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_3 IS NULL
                              OR free_dim_3 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_3_desc_error = free_dim_3_desc_error + 3,
                       error_count = error_count + 3
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_3_desc IS NULL
                              OR free_dim_3_desc = '' )
            END
      END

    IF( Lower(Trim(@free_dim_4)) = Lower(Trim('free_dim_4')) )
      BEGIN
          IF( @job_id = -1 )
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_4_error = free_dim_4_error + 4,
                       error_count = error_count + 4
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_4 IS NULL
                              OR free_dim_4 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_4_desc_error = free_dim_4_desc_error + 4,
                       error_count = error_count + 4
                WHERE  fk_tenant_id = @tenant_id
                       AND [user_id] = @user_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_4_desc IS NULL
                              OR free_dim_4_desc = '' )
            END
          ELSE
            BEGIN
                UPDATE tbu_stage_dimensions_import
                SET    free_dim_4_error = free_dim_4_error + 4,
                       error_count = error_count + 4
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_4 IS NULL
                              OR free_dim_4 = '' )

                UPDATE tbu_stage_dimensions_import
                SET    free_dim_4_desc_error = free_dim_4_desc_error + 4,
                       error_count = error_count + 4
                WHERE  fk_tenant_id = @tenant_id
                       AND [job_id] = @job_id
                       AND dimension_type = @dimension_id
                       AND ( free_dim_4_desc IS NULL
                              OR free_dim_4_desc = '' )
            END
      END

    RETURN 0 