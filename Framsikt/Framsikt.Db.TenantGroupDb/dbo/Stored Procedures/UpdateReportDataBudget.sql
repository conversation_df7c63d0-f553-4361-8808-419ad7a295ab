CREATE OR ALTER PROCEDURE [dbo].[UpdateReportDataBudget]
       -- Add the parameters for the stored procedure here
AS

DECLARE @job_running INT
DECLARE @timestamp datetime2
DECLARE @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @RowsUpdated INT

DECLARE @BudstatTbl TABLE(
    fk_tenant_id INT  NOT NULL,
	org_id NVARCHAR(25) NOT NULL,
	budget_year INT  NOT NULL,
	old_status INT NOT NULL
);

DECLARE @temp_change_budget_all TABLE(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[resource_id] [nvarchar](25) NOT NULL
UNIQUE CLUSTERED(fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,fk_tenant_id, budget_year,resource_id )
)


CREATE TABLE #TEMP_BUDGET (
	[fk_tenant_id] [int] NOT NULL DEFAULT 0,
	[budget_year] [int] NOT NULL DEFAULT 0,
	[fk_account_code] [nvarchar](25) NOT NULL DEFAULT '',
	[department_code] [nvarchar](25) NOT NULL DEFAULT '',
	[fk_function_code] [nvarchar](25) NOT NULL DEFAULT '',
	[fk_project_code] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_1] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_2] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_3] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_4] [nvarchar](25) NOT NULL DEFAULT '',
	[resource_id] [nvarchar](25) NOT NULL DEFAULT '',
	[description] [nvarchar](255) NOT NULL DEFAULT '',
	[period] [int] NOT NULL DEFAULT 0,
	[allocation_pct] [decimal](18, 10) NOT NULL DEFAULT 0,
	[fk_key_id] [int] NOT NULL DEFAULT 0,
	[fk_investment_id] [int] NOT NULL DEFAULT 0,
	[fk_portfolio_code] [nvarchar](50) NOT NULL DEFAULT '',
	[fk_employment_id] [bigint] NOT NULL DEFAULT 0,
	[fk_adjustment_code] [nvarchar](50) NOT NULL DEFAULT '',
	[fk_alter_code] [nvarchar](25) NOT NULL DEFAULT '',
	[original_budget] [decimal](38, 2) NOT NULL DEFAULT 0,
	[revised_budget] [decimal](18, 2) NOT NULL DEFAULT 0,
	[cost_calc_budget] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_1_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_2_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_3_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_4_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[revised_budget_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
	[accounting_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
	[finplan_changes_year_1] [decimal](18, 2) NOT NULL DEFAULT 0,
	[type_internal] [int] NOT NULL  DEFAULT 0,
	[fk_action_id] [int] NOT NULL DEFAULT 0,
	[fk_main_project_code] [nvarchar](25) NULL DEFAULT '',
	[ud_status] INT NULL DEFAULT 1
) ;

DECLARE @temp_change_dep_func TABLE(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
UNIQUE CLUSTERED(fk_department_code,fk_function_code,fk_tenant_id, budget_year)
)

DECLARE @stop_tenant TABLE(fk_tenant_id INT);
DECLARE @stop_actions TABLE(fk_action_id INT);


CREATE TABLE #operations_accounts (
fk_account_code varchar(24) NOT NULL,
fk_tenant_id INT NOT NULL)

INSERT INTO #operations_accounts (fk_account_code, fk_tenant_id)
SELECT DISTINCT ac.pk_account_code, ac.pk_tenant_id
FROM tco_accounts ac 
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'


BEGIN
       -- SET NOCOUNT ON added to prevent extra result sets from
       -- interfering with SELECT statements.
SET NOCOUNT ON;
set noexec off;

SET @job_running = (SELECT MAX(run_flag) FROM [twh_report_job_queue] WHERE job_name IN ('TWHBUDGETUPDATE','BUDGETFULL'))

SET @RowsUpdated = @@rowcount;


IF @job_running = 1
	BEGIN
	PRINT 'Job already running. No update is done.'
	RETURN
	END
ELSE 
	BEGIN
	SELECT @StartDate = sysdatetime();
	select @timestamp = sysdatetime();
	PRINT 'Job starting at ' + convert(nvarchar(19),@timestamp)
	--RAISERROR ('Job starting', 0, 1) WITH NOWAIT
	
	UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'TWHBUDGETUPDATE';
	END

	BEGIN 
	INSERT INTO @stop_tenant (fk_tenant_id)
	SELECT fk_tenant_id FROM tco_parameters 
	WHERE param_name = 'STOP_WH_UPDATE'
	AND param_value = 'TRUE'
	AND active = 1;

	PRINT 'Retreving tenants that is not to be updated'

	END

	BEGIN

	DELETE T FROM twh_temp_triggered_budget T INNER JOIN @stop_tenant A ON T.fk_tenant_id = A.fk_tenant_id;

	PRINT 'Delete tenants from trigger tables'

	END


			BEGIN
			INSERT INTO [twh_temp_changed_budget] ([fk_tenant_id], [budget_year],[fk_account_code], [fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[resource_id],[type_internal])
			SELECT DISTINCT a.fk_tenant_id, a.budget_year,a.fk_account_code, a.fk_department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.resource_id, a.type_internal
			FROM [twh_temp_triggered_budget] a
			JOIN #operations_accounts ac ON a.fk_account_code = ac.fk_account_code AND a.fk_tenant_id = ac.fk_tenant_id

			INSERT INTO @temp_change_budget_all ([fk_tenant_id], [budget_year],[fk_account_code], [fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[resource_id])
			SELECT DISTINCT [fk_tenant_id], [budget_year],[fk_account_code], [fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[resource_id]
			FROM
			(SELECT  [fk_tenant_id], [budget_year],[fk_account_code], [fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[resource_id]
			FROM [twh_temp_changed_budget]
			UNION 
			SELECT  [fk_tenant_id], [budget_year]+1,[fk_account_code], [fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[resource_id]
			FROM [twh_temp_changed_budget] WHERE type_internal = 1) S


			DELETE FROM [twh_temp_triggered_budget]
			END

              
BEGIN 
		RAISERROR ('START : UPDATE of budget status', 0, 1) WITH NOWAIT;
	
		INSERT INTO @BudstatTbl (fk_tenant_id, org_id, budget_year, old_status) 
			SELECT DISTINCT a.fk_tenant_id, org_id = CASE 
				WHEN c.org_level = 1 THEN org_id_1
				WHEN c.org_level = 2 THEN org_id_2	
				WHEN c.org_level = 3 THEN org_id_3	
				WHEN c.org_level = 4 THEN org_id_4
				WHEN c.org_level = 5 THEN org_id_5
				else '' 
				END, cb.budget_year, 99 AS old_status
			FROM tco_org_hierarchy a
			JOIN twh_temp_changed_budget cb ON a.fk_tenant_id = cb.fk_tenant_id AND a.fk_department_code = cb.fk_department_code AND type_internal = 1
			LEFT JOIN tco_org_version b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_org_version = b.pk_org_version and (cb.budget_year)*100+1 between b.period_from and b.period_to
			LEFT JOIN tco_org_level c ON a.fk_tenant_id = c.fk_tenant_id 
			WHERE c.su_flag = 1;

		UPDATE @BudstatTbl SET old_status = b.flag_status
		FROM @BudstatTbl a, tco_application_flag b
		WHERE a.fk_tenant_id = b.fk_tenant_id
		AND a.org_id = b.flag_key_id
		AND a.budget_year = b.budget_year
		AND b.flag_name = 'SU_BUDGET_DONE';

		UPDATE tco_application_flag SET flag_status = 2, updated = GETDATE()
		FROM @BudstatTbl a, tco_application_flag b
		WHERE a.fk_tenant_id = b.fk_tenant_id
		AND a.org_id = b.flag_key_id
		AND a.budget_year = b.budget_year
		AND b.flag_name = 'SU_BUDGET_DONE'
		AND b.flag_status = 0
		AND a.old_status = 0;

		INSERT INTO tco_application_flag (flag_name,flag_key_id,flag_status,fk_tenant_id,budget_year,updated,updated_by)
		SELECT 'SU_BUDGET_DONE', org_id, 2, fk_tenant_id, budget_year, getdate(), 1002
		FROM @BudstatTbl WHERE old_status = 99;

		select @timestamp = sysdatetime();
		PRINT 'FINISH: UPDATE of budget status ' + convert(nvarchar(19),@timestamp) 
		  

	END

	/****** UPDATE OF BUDGET TABLE WH BEGINS
			BEGIN
			--PRINT 'UPDATE twh_budget_report FROM budget' 
			RAISERROR ('START : UPDATE twh_budget_report  FROM budget', 0, 1) WITH NOWAIT
			SET @RowsUpdated = 0;



				BEGIN

			INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
			free_dim_1,free_dim_2,free_dim_3,free_dim_4,
			resource_id,description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
			fk_employment_id,fk_adjustment_code,fk_alter_code, original_budget,revised_budget, cost_calc_budget,
			type_internal)
			SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
			a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
			a.resource_id,SUBSTRING(a.description,1,255),a.period,a.allocation_pct,a.fk_key_id, a.fk_investment_id, isnull(a.fk_portfolio_code,''),
			a.fk_employment_id,a.fk_adjustment_code,a.fk_alter_code,
			0, SUM(amount_year_1), 0 as cost_calc_budget,
			1 AS type_internal
			FROM tbu_trans_detail a
			JOIN [twh_temp_changed_budget] TMP ON a.fk_tenant_id = TMP.fk_tenant_id AND a.budget_year = TMP.budget_year AND a.fk_account_code = TMP.fk_account_code AND a.department_code = TMP.fk_department_code AND a.fk_function_code = TMP.fk_function_code AND a.fk_project_code = TMP.fk_project_code AND a.free_dim_1 = TMP.free_dim_1 AND a.resource_id = TMP.resource_id AND TMP.type_internal = 1
			GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
			a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
			a.resource_id,a.description,a.period,a.allocation_pct,a.fk_key_id, a.fk_investment_id, a.fk_portfolio_code,
			a.fk_employment_id, a.fk_adjustment_code, a.fk_alter_code

			end

		
				SET @RowsUpdated = @@rowcount;
		
				RAISERROR ('START : Fetch data for original budget into temp table', 0, 1) WITH NOWAIT

				BEGIN

				INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
				free_dim_1,free_dim_2,free_dim_3,free_dim_4,
				resource_id,description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
				fk_employment_id,fk_adjustment_code, original_budget,revised_budget, cost_calc_budget,
				type_internal)
				SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				a.resource_id,'',a.period,0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id, '' as fk_portfolio_code,a.fk_employment_id,
				'' as fk_adjustment_code,
				SUM(amount_year_1) as original_budget, 
				0 as revised_budget, 0 AS cost_calc_budget,
				2 AS type_internal
				FROM tbu_trans_detail_original a
				JOIN @temp_change_budget_all TMP ON a.fk_tenant_id = TMP.fk_tenant_id AND a.budget_year = TMP.budget_year AND a.fk_account_code = TMP.fk_account_code AND a.department_code = TMP.fk_department_code AND a.fk_function_code = TMP.fk_function_code AND a.fk_project_code = TMP.fk_project_code AND a.free_dim_1 = TMP.free_dim_1 AND a.resource_id = TMP.resource_id
				GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				a.resource_id,a.period,a.fk_employment_id


				END

				select @timestamp = sysdatetime();
			
				
				RAISERROR ('START : Fetch data for revised budget prev year into temp table', 0, 1) WITH NOWAIT

				BEGIN

				INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
				free_dim_1,free_dim_2,free_dim_3,free_dim_4,
				resource_id,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,fk_adjustment_code,
				fk_employment_id,revised_budget_prev_year,
				type_internal, ud_status)
				SELECT a.fk_tenant_id,a.budget_year+1,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				a.resource_id,a.period+100,0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id,  '' as fk_portfolio_code,
				'' as fk_adjustment_code,
				a.fk_employment_id,SUM(amount_year_1), 
				2 AS type_internal, ud.status
				FROM tbu_trans_detail a
				JOIN @temp_change_budget_all TMP ON a.fk_tenant_id = TMP.fk_tenant_id AND a.budget_year+1 = TMP.budget_year AND a.fk_account_code = TMP.fk_account_code AND a.department_code = TMP.fk_department_code AND a.fk_function_code = TMP.fk_function_code AND a.fk_project_code = TMP.fk_project_code AND a.free_dim_1 = TMP.free_dim_1 AND a.resource_id = TMP.resource_id
				LEFT JOIN dbo.tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 0
				GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				a.resource_id,a.period,a.fk_employment_id, ud.status

				
				DELETE FROM #TEMP_BUDGET WHERE ud_status = 0 


				END

				select @timestamp = sysdatetime();
				PRINT 'FINISH: Fetch data for revised budget prev year into temp table at ' + convert(nvarchar(19),@timestamp)



				RAISERROR ('START : Fetch data for cost calc budget into temp table', 0, 1) WITH NOWAIT

				BEGIN

				INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
				free_dim_1,free_dim_2,free_dim_3,free_dim_4,
				resource_id,description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
				fk_employment_id,fk_adjustment_code, original_budget,revised_budget, cost_calc_budget,
				type_internal)
				SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				0 AS resource_id, '' AS description,a.budget_year*100+1 as period, 0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id, '' as fk_portfolio_code,
				0 as fk_employment_id,'' as fk_adjustment_code,
				0, 0, SUM(amount_year_1) as cost_calc_budget,
				2 AS type_internal
				FROM tbu_trans_detail_cost a
				JOIN @temp_change_budget_all TMP ON a.fk_tenant_id = TMP.fk_tenant_id AND a.budget_year = TMP.budget_year AND a.fk_account_code = TMP.fk_account_code AND a.department_code = TMP.fk_department_code AND a.fk_function_code = TMP.fk_function_code AND a.fk_project_code = TMP.fk_project_code AND a.free_dim_1 = TMP.free_dim_1 AND TMP.resource_id = ''
				GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4



				END

				select @timestamp = sysdatetime();
				PRINT 'FINISH: Fetch data for cost calc budget into temp table at ' + convert(nvarchar(19),@timestamp)


				RAISERROR ('START : Fetch data for accounting into temp table', 0, 1) WITH NOWAIT

				BEGIN

				INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
				free_dim_1,free_dim_2,free_dim_3,free_dim_4,
				period,accounting_prev_year,
				type_internal)
				SELECT a.fk_tenant_id, a.gl_year+1,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				a.period+100,
				SUM(amount) as accounting_prev_year,
				2 AS type_internal
				FROM tfp_accounting_data a
				JOIN @temp_change_budget_all TMP ON a.fk_tenant_id = TMP.fk_tenant_id AND a.gl_year = TMP.budget_year-1 AND a.fk_account_code = TMP.fk_account_code AND a.department_code = TMP.fk_department_code AND a.fk_function_code = TMP.fk_function_code AND a.fk_project_code = TMP.fk_project_code AND a.free_dim_1 = TMP.free_dim_1 AND TMP.resource_id = ''
				GROUP BY   a.fk_tenant_id, a.gl_year,a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,
				a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
				a.period

				END

				select @timestamp = sysdatetime();
				PRINT 'FINISH: Fetch data for accounting into temp table at ' + convert(nvarchar(19),@timestamp)

			PRINT 'start: Delete existing rows'

			DELETE  twh_budget_report_v2 FROM twh_budget_report_v2 a, [twh_temp_changed_budget] TMP
			WHERE 
			a.fk_tenant_id = TMP.fk_tenant_id 
			AND a.budget_year = TMP.budget_year 
			AND a.fk_account_code = TMP.fk_account_code 
			AND a.department_code = TMP.fk_department_code 
			AND a.fk_function_code = TMP.fk_function_code 
			AND a.fk_project_code = TMP.fk_project_code 
			AND a.free_dim_1 = TMP.free_dim_1 
			AND a.resource_id = TMP.resource_id
			AND a.type_internal = TMP.type_internal;

			DELETE  twh_budget_report_v2 FROM twh_budget_report_v2 a, @temp_change_budget_all TMP
			WHERE 
			a.fk_tenant_id = TMP.fk_tenant_id 
			AND a.budget_year  = TMP.budget_year
			AND a.fk_account_code = TMP.fk_account_code 
			AND a.department_code = TMP.fk_department_code 
			AND a.fk_function_code = TMP.fk_function_code 
			AND a.fk_project_code = TMP.fk_project_code 
			AND a.free_dim_1 = TMP.free_dim_1 
			AND a.resource_id = TMP.resource_id
			AND a.type_internal = 2
			
			PRINT 'Delete existing rows'
		
				BEGIN

				print 'INSERT into twh_budget_report_v2'

				INSERT INTO twh_budget_report_v2 (fk_tenant_id,budget_year,fk_account_code,department_code,
				fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,
				description,period,allocation_pct,fk_key_id,fk_investment_id,fk_portfolio_code,
				fk_employment_id,fk_adjustment_code,fk_alter_code,original_budget,revised_budget,cost_calc_budget,
				fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,revised_budget_prev_year,
				accounting_prev_year, finplan_changes_year_1,type_internal,fk_action_id,fk_main_project_code)
				SELECT fk_tenant_id,budget_year,fk_account_code,department_code,
				fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,
				description,period,allocation_pct,fk_key_id,fk_investment_id,fk_portfolio_code,
				fk_employment_id,fk_adjustment_code,fk_alter_code,SUM(original_budget),SUM(revised_budget),SUM(cost_calc_budget),
				SUM(fp_year_1_amount),SUM(fp_year_2_amount),SUM(fp_year_3_amount),SUM(fp_year_4_amount),SUM(revised_budget_prev_year),
				SUM(accounting_prev_year), SUM(finplan_changes_year_1),type_internal,fk_action_id,fk_main_project_code
				FROM #TEMP_BUDGET
				GROUP BY fk_tenant_id,budget_year,fk_account_code,department_code,
				fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,
				description,period,allocation_pct,fk_key_id,fk_investment_id,fk_portfolio_code,
				fk_employment_id,fk_adjustment_code,type_internal,fk_action_id,fk_main_project_code,fk_alter_code
				;


				END

			select @timestamp = sysdatetime();
			PRINT 'FINISH: UPDATE twh_budget_report  FROM budget  at ' + convert(nvarchar(19),@timestamp) +'. Rows affected: ' + CONVERT(VARCHAR(20), @RowsUpdated)

			END

UPDATE OF BUDGET TABLE WH ENDS ******/

			INSERT INTO @temp_change_dep_func (fk_tenant_id, budget_year, fk_department_code, fk_function_code)
			SELECT DISTINCT fk_tenant_id, budget_year, fk_department_code, fk_function_code FROM [twh_temp_changed_budget]

			IF EXISTS (SELECT TOP 1 * FROM [twh_temp_changed_budget])
BEGIN
			RAISERROR ('START: Update finplan warehouse', 0, 1) WITH NOWAIT;

			CREATE TABLE #TEMP_wh_upd_1
			(
				[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
				[fk_tenant_id] INT NOT NULL, 
				[budget_year] INT NOT NULL,
				[action_type] [int] NOT NULL,
				[line_order] [int] NOT NULL, 
				[fk_account_code] nvarchar(25) NOT NULL,	
				[fk_department_code] nvarchar(25) NOT NULL,
				[fk_function_code] nvarchar(25) NOT NULL,
				[free_dim_1] [nvarchar](25) NOT NULL,
				[free_dim_2] [nvarchar](25) NOT NULL,
				[free_dim_3] [nvarchar](25) NOT NULL,
				[free_dim_4] [nvarchar](25) NOT NULL,
				[org_budget_prev_year] DECIMAL(18, 2) NOT NULL,
				[org_budget_curr_year] DECIMAL(18, 2) NOT NULL,
				[revised_budget_prev_year] DECIMAL(18, 2) NOT NULL,
				[revised_budget_this_year] DECIMAL(18, 2) NOT NULL,
				[accounting_amount] DECIMAL(18, 2) NOT NULL,
				[forecast_amount] DECIMAL(18, 2) NOT NULL
			)

			CREATE TABLE #TEMP_wh_upd_2
			(
				[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
				[fk_tenant_id] INT NOT NULL, 
				[budget_year] INT NOT NULL,
				[action_type] [int] NOT NULL,
				[line_order] [int] NOT NULL, 
				[line_group_id] [int] NOT NULL, 
				[fk_department_code] nvarchar(25) NOT NULL,
				[fk_function_code] nvarchar(25) NOT NULL,
				[free_dim_2] nvarchar(25) NOT NULL DEFAULT '',
				[org_budget_prev_year] DECIMAL(18, 2) NOT NULL,
				[org_budget_curr_year] DECIMAL(18, 2) NOT NULL,
				[revised_budget_prev_year] DECIMAL(18, 2) NOT NULL,
				[revised_budget_this_year] DECIMAL(18, 2) NOT NULL,
				[accounting_amount] DECIMAL(18, 2) NOT NULL,
				[forecast_amount] DECIMAL(18, 2) NOT NULL
			)

			RAISERROR ('START: Fetch data from tbu_trans_detail', 0, 1) WITH NOWAIT;

			--Fetch if original budget is locked
			INSERT INTO #TEMP_wh_upd_1 (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, 
			free_dim_1, free_dim_2, free_dim_3, free_dim_4,
			org_budget_prev_year, org_budget_curr_year,
			revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount)
			SELECT a.fk_tenant_id, a.budget_year
			,action_type = 0
			,line_order = 0
			, a.fk_account_code, a.department_code, a.fk_function_code,
			free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
			0 revised_budget_prev_year, SUM(amount_year_1) as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount
			FROM tbu_trans_detail a
			JOIN @temp_change_dep_func b ON a.budget_year = b.budget_year AND a.fk_tenant_id = b.fk_tenant_id AND a.fk_function_code = b.fk_function_code AND a.department_code = b.fk_department_code
			LEFT JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND ud.status = 1 --AND a.budget_year = ud.budget_year
			JOIN tco_parameters p ON b.fk_tenant_id = p.fk_tenant_id and b.budget_year = p.param_value and p.param_name = 'LOCK_ORIGINAL_BUDGET' and p.active = 1
			--WHERE (fk_adjustment_code = '' OR ud.status = 1)	
			GROUP BY a.fk_tenant_id, a.budget_year
			--, a.action_type, a.line_order
			, a.department_code, a.fk_function_code, a.fk_account_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4

			--Fetch if original budget is not locked
			INSERT INTO #TEMP_wh_upd_1 (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, 
			free_dim_1, free_dim_2, free_dim_3, free_dim_4,
			org_budget_prev_year, org_budget_curr_year,
			revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount)
			SELECT a.fk_tenant_id, a.budget_year
			,action_type = 0
			,line_order = 0
			, a.fk_account_code, a.department_code, a.fk_function_code,
			free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
			0 revised_budget_prev_year, SUM(amount_year_1) as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount
			FROM tbu_trans_detail a
			JOIN @temp_change_dep_func b ON a.budget_year = b.budget_year AND a.fk_tenant_id = b.fk_tenant_id AND a.fk_function_code = b.fk_function_code AND a.department_code = b.fk_department_code
			LEFT JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND ud.status = 1 --AND a.budget_year = ud.budget_year
			LEFT JOIN tco_parameters p ON b.fk_tenant_id = p.fk_tenant_id and b.budget_year = p.param_value and p.param_name = 'LOCK_ORIGINAL_BUDGET' and p.active = 1
			WHERE p.param_value IS NULL
			GROUP BY a.fk_tenant_id, a.budget_year
			--, a.action_type, a.line_order
			, a.department_code, a.fk_function_code, a.fk_account_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4




			select @timestamp = sysdatetime();
			PRINT 'FINISH: Fetch data from tbu_trans_detail' + convert(nvarchar(19),@timestamp);


			RAISERROR ('START: Fetch line_orders and action type', 0, 1) WITH NOWAIT;


				UPDATE #TEMP_wh_upd_1 SET action_type = 5 WHERE action_type NOT IN (1,2,3,4,100);

				UPDATE #TEMP_wh_upd_1
				SET line_order = ls.line_order, action_type = ls.action_type
				FROM #TEMP_wh_upd_1 imp, tmd_finplan_line_setup ls
				WHERE imp.fk_account_code = ls.fk_account_code
				AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
				AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
				AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
				AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
				AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
				AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
				AND imp.fk_tenant_id = ls.fk_tenant_id
				AND imp.budget_year = ls.budget_year
				AND ls.priority = 0;

					UPDATE #TEMP_wh_upd_1
				SET line_order = ls.line_order, action_type = ls.action_type
				FROM #TEMP_wh_upd_1 imp, tmd_finplan_line_setup ls
				WHERE imp.fk_account_code = ls.fk_account_code
				AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
				AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
				AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
				AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
				AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
				AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
				AND imp.fk_tenant_id = ls.fk_tenant_id
				AND imp.budget_year = ls.budget_year
				AND ls.priority = 5;

				UPDATE #TEMP_wh_upd_1
				SET line_order = ls.line_order, action_type = ls.action_type
				FROM #TEMP_wh_upd_1 imp, tmd_finplan_line_setup ls
				WHERE imp.fk_account_code = ls.fk_account_code
				AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
				AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
				AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
				AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
				AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
				AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
				AND imp.fk_tenant_id = ls.fk_tenant_id
				AND imp.budget_year = ls.budget_year
				AND ls.priority = 4;

				UPDATE #TEMP_wh_upd_1
				SET line_order = ls.line_order, action_type = ls.action_type
				FROM #TEMP_wh_upd_1 imp, tmd_finplan_line_setup ls
				WHERE imp.fk_account_code = ls.fk_account_code
				AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
				AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
				AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
				AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
				AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
				AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
				AND imp.fk_tenant_id = ls.fk_tenant_id
				AND imp.budget_year = ls.budget_year
				AND ls.priority = 3;

				UPDATE #TEMP_wh_upd_1
				SET line_order = ls.line_order, action_type = ls.action_type
				FROM #TEMP_wh_upd_1 imp, tmd_finplan_line_setup ls
				WHERE imp.fk_account_code = ls.fk_account_code
				AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
				AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
				AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
				AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
				AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
				AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
				AND imp.fk_tenant_id = ls.fk_tenant_id
				AND imp.budget_year = ls.budget_year
				AND ls.priority = 2;

				UPDATE #TEMP_wh_upd_1
				SET line_order = ls.line_order, action_type = ls.action_type
				FROM #TEMP_wh_upd_1 imp, tmd_finplan_line_setup ls
				WHERE imp.fk_account_code = ls.fk_account_code
				AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
				AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
				AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
				AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
				AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
				AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
				AND imp.fk_tenant_id = ls.fk_tenant_id
				AND imp.budget_year = ls.budget_year
				AND ls.priority = 1;

			select @timestamp = sysdatetime();
			PRINT 'FINISH: Fetch line_orders and action type' + convert(nvarchar(19),@timestamp);

			RAISERROR ('START: Insert into temp table 2', 0, 1) WITH NOWAIT;

			INSERT INTO #TEMP_wh_upd_2 (fk_tenant_id, budget_year, action_type, line_order,line_group_id, fk_department_code, fk_function_code, free_dim_2,
			org_budget_prev_year,org_budget_curr_year, revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount)
			SELECT fk_tenant_id, budget_year, action_type, line_order,rl.line_group_id, fk_department_code, fk_function_code,free_dim_2,0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
			0 revised_budget_prev_year, SUM(revised_budget_this_year), 0 as accounting_amount, 0 as forecast_amount
			FROM #TEMP_wh_upd_1 a
			JOIN tco_accounts ac ON ac.pk_account_code = a.fk_account_code AND a.fk_tenant_id = ac.pk_tenant_id 
			JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = 'Drift'
			GROUP BY fk_tenant_id, budget_year, action_type, line_order, fk_department_code,fk_function_code,free_dim_2, rl.line_group_id;

			select @timestamp = sysdatetime();
			PRINT 'FINISH: Insert into temp table 2' + convert(nvarchar(19),@timestamp);

			UPDATE #TEMP_wh_upd_2 SET free_dim_2 = '' WHERE free_dim_2 = ' ' OR free_dim_2 IS NULL

			RAISERROR ('START: Updating existing rows', 0, 1) WITH NOWAIT;

			UPDATE [tfp_finplan_warehouse] SET revised_budget_this_year = a.revised_budget_this_year
			FROM [tfp_finplan_warehouse] wh, #TEMP_wh_upd_2 a
			WHERE 
			a.fk_tenant_id = wh.fk_tenant_id
			AND a.budget_year=wh.budget_year
			AND a.action_type=wh.action_type 
			AND a.line_order = wh.line_order
			AND a.fk_department_code = wh.fk_department_code
			AND a.fk_function_code = wh.fk_function_code
			AND a.free_dim_2 = wh.free_dim_2
			AND a.line_group_id = wh.line_group_id;

			select @timestamp = sysdatetime();
			PRINT 'FINISH: Updating existing rows' + convert(nvarchar(19),@timestamp);

			RAISERROR ('START: Insert new rows', 0, 1) WITH NOWAIT;


			INSERT INTO [tfp_finplan_warehouse] (fk_tenant_id, budget_year, action_type, line_order,line_group_id, fk_department_code, fk_function_code, org_budget_prev_year, org_budget_curr_year,
			revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
			SELECT fk_tenant_id, budget_year, action_type, line_order, line_group_id, fk_department_code, fk_function_code, org_budget_prev_year, org_budget_curr_year,
			revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, GETDATE () AS updated, 1002 AS updated_by
			FROM #TEMP_wh_upd_2 a
			WHERE NOT EXISTS (SELECT * FROM [tfp_finplan_warehouse] wh WHERE 
			a.fk_tenant_id = wh.fk_tenant_id
			AND a.budget_year=wh.budget_year
			AND a.action_type=wh.action_type 
			AND a.line_order = wh.line_order
			AND a.fk_department_code = wh.fk_department_code
			AND a.fk_function_code = wh.fk_function_code
			AND a.free_dim_2 = wh.free_dim_2
			AND a.line_group_id = wh.line_group_id);

			select @timestamp = sysdatetime();
			PRINT 'FINISH: Insert new rows' + convert(nvarchar(19),@timestamp);

			DROP TABLE #TEMP_wh_upd_1;
			DROP TABLE #TEMP_wh_upd_2;

						select @timestamp = sysdatetime();
			PRINT 'FINISH: Update finplan warehouse at ' + convert(nvarchar(19),@timestamp);
END

            BEGIN
			PRINT 'Truncate temp tables'
			TRUNCATE TABLE twh_temp_changed_budget;
			UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name = 'TWHBUDGETUPDATE';
			END
			select @timestamp = sysdatetime();
			SELECT @EndDate = sysdatetime();
			SELECT @TimeUsed = (select convert(varchar(5),DateDiff(s, @startDate, @EndDate)/3600)+' hrs '+convert(varchar(5),DateDiff(s, @startDate, @EndDate)%3600/60)+' mins '+convert(varchar(5),(DateDiff(s, @startDate, @EndDate)%60))+' seconds')
			PRINT 'Job finished ok at ' + convert(nvarchar(19),@EndDate) + ' using ' + @TimeUsed
 
END
GO