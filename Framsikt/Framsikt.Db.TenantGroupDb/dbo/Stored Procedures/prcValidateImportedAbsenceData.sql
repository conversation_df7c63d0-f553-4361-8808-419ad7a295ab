CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedAbsenceData]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@isServiceIdSetup bit,
	@org_version NVARCHAR(50),
	@jobId bigint

AS
		--Clear all the error information--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_absence_import
			SET 
			budget_year_error = 0,
			period_error = 0 ,
			department_code_error = 0,
			function_code_error = 0,
			workinghours_error = 0,
			shortabshours_error = 0,
			longabshours_error = 0,
			dateofbirth_error = 0,
			gender_error = 0,
			error_count = 0
			WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.user_id = @user_id;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_import
			SET 
			budget_year_error = 0,
			period_error = 0 ,
			department_code_error = 0,
			function_code_error = 0,
			workinghours_error = 0,
			shortabshours_error = 0,
			longabshours_error = 0,
			dateofbirth_error = 0,
			gender_error = 0,
			error_count = 0
			WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.job_id = @jobId;
		END

	UPDATE tco_stage_absence_import
	SET function_code = ''
	WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND tco_stage_absence_import.function_code IS NULL;

	UPDATE tco_stage_absence_import
	SET dateofbirth = ''
	WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND tco_stage_absence_import.dateofbirth IS NULL;

	UPDATE tco_stage_absence_import
	SET gender = ''
	WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND tco_stage_absence_import.gender IS NULL;

	--Validate department codes --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_absence_import
			SET department_code_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_import 
			LEFT JOIN tco_departments DEPTS ON DEPTS.pk_department_code = tco_stage_absence_import.department_code AND 
				tco_stage_absence_import.fk_tenant_id = DEPTS.fk_tenant_id AND 
				tco_stage_absence_import.fk_tenant_id = @tenant_id AND
				@budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
			WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.user_id = @user_id AND 
				DEPTS.pk_department_code IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_import
			SET department_code_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_import 
			LEFT JOIN tco_departments DEPTS ON DEPTS.pk_department_code = tco_stage_absence_import.department_code AND 
				tco_stage_absence_import.fk_tenant_id = DEPTS.fk_tenant_id AND 
				tco_stage_absence_import.fk_tenant_id = @tenant_id AND
				@budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
				WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.job_id = @jobId AND DEPTS.pk_department_code IS NULL;
		END

	--Validate workinghours codes --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_absence_import
			SET workinghours_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_import 
			WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.user_id = @user_id AND tco_stage_absence_import.workinghours IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_import
			SET workinghours_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_import 
			WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.job_id = @jobId AND tco_stage_absence_import.workinghours IS NULL;
		END	

	if(@isServiceIdSetup = 1)
	begin
		--Validate function code from tco_functions.pk_function_code if @isServiceIdSetup = 1
		IF( @jobID = -1 )
			BEGIN
				update tco_stage_absence_import set function_code_error = 1, 
											error_count = error_count + 1
				from tco_stage_absence_import
				LEFT JOIN tco_functions on 
												tco_stage_absence_import.fk_tenant_id = tco_functions.pk_tenant_id and
												tco_stage_absence_import.function_code = tco_functions.pk_Function_code AND
												tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
												tco_stage_absence_import.budget_year = @budget_year AND 
												tco_stage_absence_import.user_id = @user_id AND
												@budget_year BETWEEN YEAR(tco_functions.dateFrom) AND YEAR(tco_functions.dateTo)
				where tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
					  tco_stage_absence_import.budget_year = @budget_year AND 
					  tco_stage_absence_import.user_id = @user_id AND 
					  tco_functions.pk_Function_code IS NULL;	
			END
		ELSE
			BEGIN
				update tco_stage_absence_import set function_code_error = 1, 
												error_count = error_count + 1
				from tco_stage_absence_import
				LEFT JOIN tco_functions on 
												tco_stage_absence_import.fk_tenant_id = tco_functions.pk_tenant_id and
												tco_stage_absence_import.function_code = tco_functions.pk_Function_code AND
												tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
												tco_stage_absence_import.budget_year = @budget_year AND 
												tco_stage_absence_import.job_id = @jobId AND
												@budget_year BETWEEN YEAR(tco_functions.dateFrom) AND YEAR(tco_functions.dateTo)
				where tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
					  tco_stage_absence_import.budget_year = @budget_year AND 
					  tco_stage_absence_import.job_id = @jobId AND 
					  tco_functions.pk_Function_code IS NULL;	
		END
	end
	else
	begin
		IF( @jobID = -1 )
			BEGIN
				update tco_stage_absence_import set function_code_error = 1, 
													error_count = error_count + 1
				from tco_stage_absence_import
				left outer join tco_functions on 
													tco_stage_absence_import.fk_tenant_id = tco_functions.pk_tenant_id and
													tco_stage_absence_import.function_code = tco_functions.pk_Function_code
				where
				tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.user_id = @user_id and
				tco_functions.pk_Function_code IS NULL AND 
				tco_stage_absence_import.function_code <> '' AND
				tco_stage_absence_import.function_code IS NOT NULL;	
			END
		ELSE
			BEGIN
				update tco_stage_absence_import set function_code_error = 1, 
													error_count = error_count + 1
				from tco_stage_absence_import
				left outer join tco_functions on 
													tco_stage_absence_import.fk_tenant_id = tco_functions.pk_tenant_id and
													tco_stage_absence_import.function_code = tco_functions.pk_Function_code
				where
				tco_stage_absence_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_import.budget_year = @budget_year AND 
				tco_stage_absence_import.job_id = @jobId and
				tco_functions.pk_Function_code IS NULL AND 
				tco_stage_absence_import.function_code <> '' AND
				tco_stage_absence_import.function_code IS NOT NULL;	
		END
	end

	UPDATE tco_stage_absence_import
	SET function_code = ''
	WHERE tco_stage_absence_import.fk_tenant_id = @tenant_id AND tco_stage_absence_import.function_code IS NULL;

RETURN 0