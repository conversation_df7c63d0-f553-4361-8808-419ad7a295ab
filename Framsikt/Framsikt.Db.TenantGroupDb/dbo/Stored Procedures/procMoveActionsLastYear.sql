CREATE OR ALTER PROCEDURE [dbo].[procMoveActionsLastYear]
@tenant_id INT,
@budget_year INT,
@jodId INT,
@batchType VARCHAR(50),
@userId INT,
@changeId INT,
@alterCode VARCHAR(250)
AS
BEGIN

 --Delete existing rows
 DELETE FROM tfp_stage_newyearactions WHERE budget_year = @budget_year and fk_tenant_id=@tenant_id  and batch_type=@batchType

 --Insert new rows
INSERT INTO tfp_stage_newyearactions(batch_id,
									 new_fk_action_id,
									 batch_type,
									 fk_tenant_id,
									 budget_year,
									 previous_pk_action_id,
									 action_name,
									 consequence,
									 action_type,
									 action_source,
									 line_order,
									 isManuallyAdded,
									 tags,
									 long_description,
									 priority,
									 financial_plan_description,
									 fk_account_code,
									 department_code,
									 function_code,
									 project_code,
									 fk_investment_id,
									 investment_row_id,
									 fk_change_id,
									 free_dim_1,
									 free_dim_2,
									 free_dim_3,
									 free_dim_4,
									 fk_adjustment_code,
									 fk_alter_code,
									 year_1_amount,
									 year_2_amount,
									 year_3_amount,
									 year_4_amount,
									 year_5_amount,
									 year_6_amount,
									 year_7_amount,
									 year_8_amount,
									 year_9_amount,
									 year_10_amount,
									 user_id,
									 updated,
									 updated_by
								 )

						(SELECT	@jodId,
								0,
								@batchType,
								td.fk_tenant_id,
								(@budget_year),
								td.fk_action_id,
								th.description,
								th.consequence,
								41 [action_type],
								0,
								0 [line_order],
								0,
								th.tags,
								th.long_description,
								0,
								th.financial_plan_description,
								td.fk_account_code,
								td.department_code,
								td.function_code,
								td.project_code,
								0,
								0,
								@changeId,
								free_dim_1,
								free_dim_2,
								free_dim_3,
								free_dim_4,
								td.fk_adjustment_code,
								@alterCode,
								((SUM(td.year_2_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_3_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								((SUM(td.year_4_amount))-(SUM(td.year_1_amount))),  
								@userId,
								getdate() updated,
								@userId [updated_by]
								FROM tfp_trans_header th
								join tfp_trans_detail td on th.pk_action_id=td.fk_action_id
								join tfp_budget_changes ch on td.fk_change_id = ch.pk_change_id AND td.fk_tenant_id = ch.fk_tenant_id AND ch.org_budget_flag = 1
								where th.fk_tenant_id=@tenant_id and td.budget_year=(@budget_year-1) and th.action_type not in (6,20,30,40,60,90,21)
								and (td.year_2_amount!=td.year_1_amount or td.year_3_amount!=td.year_1_amount or td.year_4_amount!=td.year_1_amount or td.year_5_amount!=td.year_1_amount or td.year_6_amount!=td.year_1_amount or td.year_7_amount!=td.year_1_amount or td.year_8_amount!=td.year_1_amount or td.year_9_amount!=td.year_1_amount or td.year_10_amount!=td.year_1_amount)
								GROUP BY th.financial_plan_description, th.long_description,th.tags,th.consequence,th.description,td.fk_action_id,td.[fk_tenant_id],td.[budget_year], td.[fk_account_code] ,td.[department_code],
								td.[function_code],td.[project_code],td.[free_dim_1] ,td.[free_dim_2] ,td.[free_dim_3],td.[free_dim_4],td.fk_adjustment_code,td.fk_investment_id,td.investment_row_id)



END