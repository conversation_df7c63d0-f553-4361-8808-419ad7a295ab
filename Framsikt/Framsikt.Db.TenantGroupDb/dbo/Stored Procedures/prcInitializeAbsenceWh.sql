

CREATE OR ALTER PROCEDURE [dbo].[prcInitializeAbsenceWh]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT, @update_only INT = 0
AS

DECLARE @budget_year INT = @forecast_period / 100

DECLARE @sick_leave_period INT
SET @sick_leave_period = (SELECT [sickleave_period] FROM [dbo].[tmr_period_setup] WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period)

DELETE FROM [tmr_data_warehouse_absence]
WHERE fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period


INSERT INTO [dbo].[tmr_data_warehouse_absence]
           ([fk_tenant_id]
           ,[budget_year]
           ,[period]
           ,[forecast_period]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[working_hrs]
           ,[absence_hrs]
           ,[working_hrs_long]
           ,[absence_hrs_long]
           ,[working_hrs_short]
           ,[absence_hrs_short]
           ,[updated]
           ,[updated_by]
		   ,[employee_count])
SELECT		a.[fk_tenant_id]
           ,[budget_year]
           ,[period]
           ,[forecast_period] = @forecast_period
           ,[fk_department_code]
           ,[fk_function_code]
           ,[working_hrs]
           ,[absence_hrs]
           ,[working_hrs_long]
           ,[absence_hrs_long]
           ,[working_hrs_short]
           ,[absence_hrs_short]
           ,[updated] = GETDATE()
           ,[updated_by] = @user_id
		   ,[employee_count] = CASE WHEN b.param_value = 'TRUE' AND a.employee_count IS NOT NULL THEN a.employee_count
									ELSE 6 END
FROM [dbo].[tmr_absence_aggregated] a
LEFT JOIN tco_parameters b ON a.fk_tenant_id = b.fk_tenant_id AND b.param_name = 'DISPLAY_HR_MODULE' AND b.active = 1
WHERE a.fk_tenant_id = @fk_tenant_id AND a.period <= @sick_leave_period


DELETE FROM [tmr_absence_employees_processed_data_warehouse]
WHERE fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period

INSERT INTO [dbo].[tmr_absence_employees_processed_data_warehouse]
           ([fk_tenant_id]
           ,[forecast_period]
           ,[budget_year]
           ,[period]
           ,[fk_employee_id]
           ,[fk_position_id]
           ,[public_position_type_value]
           ,[gender]
           ,[birth_date]
           ,[position_pct]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[working_days]
           ,[working_days_adj]
           ,[absence_percentage]
           ,[absence_days_short]
           ,[absence_days_short_adj]
           ,[absence_days_long]
           ,[absence_days_long_adj]
           ,[employee_sick_in_period]
           ,[updated]
           ,[updated_by])

select		[fk_tenant_id]
           ,[forecast_period] = @forecast_period
           ,[budget_year]
           ,[period]
           ,[fk_employee_id]
           ,[fk_position_id]
           ,[public_position_type_value]
           ,[gender]
           ,[birth_date]
           ,[position_pct]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[working_days]				
           ,[working_days_adj]			
           ,[absence_percentage]
           ,[absence_days_short]		
           ,[absence_days_short_adj]	
           ,[absence_days_long]			
           ,[absence_days_long_adj]		
           ,[employee_sick_in_period]
           ,[updated] = GETDATE ()
           ,[updated_by] = @user_id
from tmr_absence_employees_processed
where fk_tenant_id = @fk_tenant_id
and budget_year = @budget_year



DELETE FROM [tmr_absence_employees_data_warehouse]
WHERE fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period

INSERT INTO [dbo].[tmr_absence_employees_data_warehouse]
            ([fk_tenant_id]
           ,[forecast_period]
           ,[budget_year]  
           ,[employee_id]  
           ,[ida] 
           ,[unit_name] 
           ,[unit_code] 
           ,[absence_code] 
           ,[absence_code_name]
           ,[date_from] 
           ,[date_to] 
           ,[absence_percentage] 
           ,[position_id] 
           ,[continued_external_id] 
           ,[work_related] 
           ,[pregnancy_related] 
           ,[sick_leave_id] 
           ,[updated] 
           ,[updated_by])


select		[fk_tenant_id]
           ,[forecast_period] = @forecast_period
           ,[budget_year]  
           ,[employee_id]  
           ,[ida] 
           ,[unit_name] 
           ,[unit_code] 
           ,[absence_code]  
           ,[absence_code_name]
           ,[date_from] 
           ,[date_to] 
           ,[absence_percentage] 
           ,[position_id] 
           ,[continued_external_id] 
           ,[work_related] 
           ,[pregnancy_related] 
           ,[sick_leave_id] 
           ,[updated] = GETDATE ()
           ,[updated_by] = @user_id
from tmr_absence_employees
where fk_tenant_id = @fk_tenant_id
and budget_year = @budget_year
GO
