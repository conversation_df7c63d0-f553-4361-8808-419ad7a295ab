CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMREquality]
	@tenant_id INT,
	@forecast_period INT,
	@user_id INT
WITH RECOMPILE
AS

--DECLARE @tenant_id INT = 7832,
--@forecast_period INT = 202412,
--@user_id INT = 1132

DROP TABLE IF EXISTS #position_details
DROP TABLE IF EXISTS #position_details_2
DROP TABLE IF EXISTS #hlptab_actual
DROP TABLE IF EXISTS #hlptab_actual_display


DECLARE @budget_year INT = @forecast_period / 100
DECLARE @seniority_year dec(18,2) = @budget_year


SELECT 
NEWID() as pk_id
,d.fk_tenant_id
,@forecast_period as forecast_period
,d.budget_year
,e.gender
,d.fk_department_code
,d.fk_function_code
,d.fk_position_id
,d.fk_position_code
,d.seniority_date
,d.fk_employee_id
,d.position_type_value
,d.yearly_salary
,d.position_percentage
,d.presence_percentage
,convert(date,d.position_start_date,103) as position_start_date
,convert(date,d.position_end_date,103) as position_end_date
INTO #position_details  
from tco_position_details d
JOIN tco_employee_data e ON d.fk_tenant_id = e.fk_tenant_id AND d.fk_employee_id = e.pk_employee_id
LEFT JOIN tco_position_category c ON d.fk_tenant_id = c.fk_tenant_id AND d.fk_position_code = c.pk_position_code
WHERE d.fk_tenant_id = @tenant_id  
AND d.budget_year = @budget_year

UPDATE #position_details SET position_end_date = '2999-12-31' 
WHERE position_end_date = '' OR position_end_date is null OR position_end_date = '0001-01-01'

SELECT 
a.pk_id
,A.fk_employee_id
,A.position_start_date
,A.position_end_date
,A.presence_percentage
,A.TheYear
,A.TheMonth
,A.possible_working_days
,B.max_day_month
,(A.possible_working_days * CONVERT(dec(18,2), presence_percentage) / 100) / CONVERT(dec(18,2),B.max_day_month) as Månedsverk
INTO #position_details_2  
FROM
(SELECT 
a.pk_id
,a.fk_employee_id
,a.position_start_date
,a.position_end_date
,a.presence_percentage
,b.TheYear
,b.TheMonth
,COUNT(*) AS possible_working_days 
FROM #position_details a
LEFT JOIN gmd_date_table b ON b.TheDate >= a.position_start_date AND b.TheDate <= a.position_end_date
WHERE a.fk_tenant_id = @tenant_id
AND budget_year = @budget_year
AND CONVERT(dec(18,2),a.presence_percentage) != CONVERT(dec(18, 2), 0)
GROUP BY 
a.pk_id
,a.fk_employee_id
,a.position_start_date
,a.position_end_date
,a.presence_percentage
,b.TheYear
,b.TheMonth) A
JOIN (select TheYear, TheMonth, MAX(TheDay) AS max_day_month 
FROM gmd_date_table
GROUP BY TheYear, TheMonth) B
ON A.TheYear = B.TheYear AND A.TheMonth = B.TheMonth 
WHERE A.TheYear = @budget_year
ORDER BY
A.fk_employee_id
,A.position_start_date
,A.position_end_date
,A.TheYear
,A.TheMonth


INSERT INTO #position_details_2 
SELECT
a.pk_id
,A.fk_employee_id
,A.position_start_date
,A.position_end_date
,A.presence_percentage
,A.TheYear
,A.TheMonth
,A.possible_working_days
,B.max_day_month
,(A.possible_working_days / CONVERT(dec(18,2),B.max_day_month)) as Månedsverk
FROM
(SELECT 
a.pk_id
,a.fk_employee_id
,a.position_start_date
,a.position_end_date
,a.presence_percentage
,b.TheYear
,b.TheMonth
,COUNT(*) AS possible_working_days 
FROM #position_details a
LEFT JOIN gmd_date_table b ON b.TheDate >= a.position_start_date AND b.TheDate <= a.position_end_date
WHERE a.fk_tenant_id = @tenant_id
AND budget_year = @budget_year
AND CONVERT(dec(18,2),a.presence_percentage) = CONVERT(dec(18, 2), 0)
GROUP BY 
a.pk_id
,a.fk_employee_id
,a.position_start_date
,a.position_end_date
,a.presence_percentage
,b.TheYear
,b.TheMonth) A
JOIN (select TheYear, TheMonth, MAX(TheDay) AS max_day_month 
FROM gmd_date_table
GROUP BY TheYear, TheMonth) B
ON A.TheYear = B.TheYear AND A.TheMonth = B.TheMonth 
WHERE A.TheYear = @budget_year
ORDER BY
A.fk_employee_id
,A.position_start_date
,A.position_end_date
,A.TheYear
,A.TheMonth



SELECT 
pk_id = a.pk_id
,a.fk_tenant_id
,forecast_period
,@budget_year as budget_year
,a.gender
,a.fk_department_code
,a.fk_function_code
,a.fk_position_code
,a.fk_employee_id
,a.position_type_value
,a.yearly_salary
,a.position_percentage
,a.presence_percentage
,a.position_start_date
,a.position_end_date
,a.seniority_date
,seniority= @seniority_year-DATEPART(YEAR,seniority_date)
,convert(dec(18,2),0) as jan
,convert(dec(18,2),0) as feb
,convert(dec(18,2),0) as march
,convert(dec(18,2),0) as apr
,convert(dec(18,2),0) as may
,convert(dec(18,2),0) as june
,convert(dec(18,2),0) as july
,convert(dec(18,2),0) as aug
,convert(dec(18,2),0) as sep
,convert(dec(18,2),0) as oct
,convert(dec(18,2),0) as nov
,convert(dec(18,2),0) as dec
,convert(dec(18,3),0) as total
,convert(dec(18,3),0) as total_months
,convert(dec(18,3),0) as total_months_emp
,convert(dec(18,3),0) as position_percentage_adj
,convert(dec(18,2),0) as salary_adj
INTO #hlptab_actual
FROM #position_details a
WHERE @budget_year <= DATEPART (year, a.position_end_date) AND @budget_year >= DATEPART (YEAR, a.position_start_date)
AND budget_year = @budget_year
GROUP BY 
a.pk_id
,a.fk_tenant_id
,forecast_period
,budget_year
,a.gender
,a.fk_department_code
,a.fk_function_code
,a.fk_position_code
,a.fk_employee_id
,a.position_type_value
,a.yearly_salary
,a.position_percentage
,a.presence_percentage
,a.position_start_date
,a.position_end_date
,a.seniority_date

SELECT * INTO #hlptab_actual_display FROM #hlptab_actual ;


--month-wise update logic using dynamic SQL
DECLARE @month_num INT = 1
DECLARE @month_name VARCHAR(10)
DECLARE @sql NVARCHAR(MAX)

-- Create a mapping table for month numbers to column names
DECLARE @month_mapping TABLE (month_num INT, month_name VARCHAR(10))
INSERT INTO @month_mapping VALUES 
(1, 'jan'), (2, 'feb'), (3, 'march'), (4, 'apr'), 
(5, 'may'), (6, 'june'), (7, 'july'), (8, 'aug'), 
(9, 'sep'), (10, 'oct'), (11, 'nov'), (12, 'dec')

-- Loop through each month
WHILE @month_num <= 12
BEGIN
    -- Get the month column name
    SELECT @month_name = month_name 
    FROM @month_mapping 
    WHERE month_num = @month_num
    
    -- Update #hlptab_actual with conditional logic
    SET @sql = N'
    UPDATE #hlptab_actual 
    SET ' + @month_name + ' = CASE WHEN CONVERT(dec(18,2),b.presence_percentage) = CONVERT(dec(18, 2), 0) THEN CONVERT(dec(18, 2), 0) ELSE b.Månedsverk END
    FROM #hlptab_actual a
    JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
    WHERE b.TheMonth = ' + CAST(@month_num AS VARCHAR(2))
    
    EXEC sp_executesql @sql
    
    -- Update #hlptab_actual_display without conditional logic
    SET @sql = N'
    UPDATE #hlptab_actual_display 
    SET ' + @month_name + ' = b.Månedsverk
    FROM #hlptab_actual_display a
    JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
    WHERE b.TheMonth = ' + CAST(@month_num AS VARCHAR(2))
    
    EXEC sp_executesql @sql
    
    SET @month_num = @month_num + 1
END


UPDATE #hlptab_actual SET total = (jan+feb+march+apr+may+june+july+aug+sep+oct+nov+dec)/12

UPDATE #hlptab_actual SET total_months = sub.total_months FROM (SELECT fk_employee_id, fk_position_code, position_start_date, position_end_date,  SUM(jan+feb+march+apr+may+june+july+aug+sep+oct+nov+dec) AS total_months FROM #hlptab_actual_display  GROUP BY fk_employee_id, fk_position_code, position_start_date, position_end_date) AS sub WHERE #hlptab_actual.fk_employee_id = sub.fk_employee_id AND #hlptab_actual.fk_position_code = sub.fk_position_code AND #hlptab_actual.position_start_date = sub.position_start_date AND #hlptab_actual.position_end_date = sub.position_end_date

UPDATE #hlptab_actual SET total_months_emp = sub2.total_months_emp FROM (SELECT fk_employee_id, SUM(jan+feb+march+apr+may+june+july+aug+sep+oct+nov+dec) AS total_months_emp FROM #hlptab_actual_display GROUP BY fk_employee_id) AS sub2 WHERE #hlptab_actual.fk_employee_id = sub2.fk_employee_id

UPDATE #hlptab_actual SET salary_adj = yearly_salary * total_months / total_months_emp

UPDATE #hlptab_actual SET position_percentage_adj = CONVERT(dec(18,2),position_percentage) * total_months / total_months_emp

DELETE FROM [tmr_equality_data_warehouse] WHERE fk_tenant_id = @tenant_id AND forecast_period = @forecast_period

INSERT INTO [tmr_equality_data_warehouse] (
fk_tenant_id
,forecast_period
,fk_employee_id
,fk_department_code
,fk_function_code
,fk_position_code
,position_type_value
,gender
,man_years
,salary
,position_pct
,seniority
,updated
,updated_by
,presence_percentage
,position_pct_adj
,salary_adj)
SELECT 
fk_tenant_id
,forecast_period
,fk_employee_id
,fk_department_code
,fk_function_code
,fk_position_code
,position_type_value
,gender
,total as man_years
,yearly_salary
,CONVERT(dec(18,5),position_percentage)
,seniority = CASE WHEN seniority_date = '0001-01-01' THEN -1 ELSE seniority END
,getdate() as updated
,@user_id as updated_by 
,presence_percentage
,position_percentage_adj
,salary_adj
FROM #hlptab_actual

GO