CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRConsoleBudform]
	@tenant_id int,
	@forecast_period int, 
	@user_id int
AS
	
DROP TABLE IF EXISTS #tcon_mr_hlp1

DECLARE @budget_year INT = @forecast_period /100

CREATE TABLE #tcon_mr_hlp1 (
[pk_id] [int] IDENTITY(1,1) NOT NULL,
[fk_tenant_id] [int] NOT NULL,
[budget_year] [int] NOT NULL,
[consol_level] [int] NOT NULL,
[1A_line] [int] NOT NULL,
[line_group_id] [int]  NULL,
[line_group] NVARCHAR(200)  NULL,
[line_item_id] [int]  NULL,
[line_item] NVARCHAR(150)  NULL,
[fk_kostra_account_code] [varchar](25) NOT NULL,
[fk_kostra_function_code] [nvarchar](25) NOT NULL,
[actual_amt_last_year] DECIMAL(18, 2) NOT NULL,
[actual_amt_year] DECIMAL(18, 2) NOT NULL, 
[org_bud_amt_year] DECIMAL (18, 2) NOT NULL,
[revised_bud_amt_year] DECIMAL (18, 2) NOT NULL,
[acc_type] INT DEFAULT ((0)) NOT NULL
PRIMARY KEY CLUSTERED 
(
[pk_id] ASC
))

INSERT INTO #tcon_mr_hlp1 (fk_tenant_id, budget_year, consol_level, [1A_line], fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year)
SELECT t.fk_tenant_id, t.budget_year, s.consol_level, 0 as [1A_line], t.fk_kostra_account_code, fk_kostra_function_code,
0 AS actual_amt_last_year, 0 AS actual_amt_year, 
SUM(year_1_amount) * s.owner_share/100 AS org_bud_amt_year, 0 as revised_bud_amt_year 
FROM tcon_consol_transactions t
JOIN tcon_consol_company_setup s ON t.fk_company_id = s.pk_company_id AND t.fk_tenant_id = s.fk_tenant_id
AND s.budget_year = t.budget_year AND s.include_consol = 1
WHERE t.fk_tenant_id = @tenant_id
AND t.budget_year = @budget_year
AND t.amount_type = 'Budget'
AND s.consol_level = 1
GROUP BY t.fk_tenant_id, t.budget_year, s.consol_level, t.fk_kostra_account_code, fk_kostra_function_code,s.owner_share


INSERT INTO #tcon_mr_hlp1 (fk_tenant_id, budget_year, consol_level, [1A_line], fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year)
SELECT t.fk_tenant_id, t.budget_year, s.consol_level, 0 as [1A_line], t.fk_kostra_account_code, fk_kostra_function_code,
0 AS actual_amt_last_year, 0 AS actual_amt_year, 
0 AS org_bud_amt_year, SUM(year_1_amount) * s.owner_share/100 as revised_bud_amt_year 
FROM tcon_consol_transactions t
JOIN tcon_consol_company_setup s ON t.fk_company_id = s.pk_company_id AND t.fk_tenant_id = s.fk_tenant_id
AND s.budget_year = t.budget_year AND s.include_consol = 1
WHERE t.fk_tenant_id = @tenant_id
AND t.budget_year = @budget_year
AND t.amount_type = 'RevisedBudget'
AND s.consol_level = 1
GROUP BY t.fk_tenant_id, t.budget_year, s.consol_level, t.fk_kostra_account_code, fk_kostra_function_code,s.owner_share


INSERT INTO #tcon_mr_hlp1 (fk_tenant_id, budget_year, consol_level, [1A_line], fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year)
SELECT t.fk_tenant_id, t.budget_year, s.consol_level, 0 as [1A_line], t.fk_kostra_account_code, fk_kostra_function_code,
0 AS actual_amt_last_year, SUM(year_1_amount) * s.owner_share/100 AS actual_amt_year, 
0 AS org_bud_amt_year, 0 as revised_bud_amt_year 
FROM tcon_consol_transactions t
JOIN tcon_consol_company_setup s ON t.fk_company_id = s.pk_company_id AND t.fk_tenant_id = s.fk_tenant_id
AND s.budget_year = t.budget_year AND s.include_consol = 1
WHERE t.fk_tenant_id = @tenant_id
AND t.budget_year = @budget_year
AND t.amount_type = 'Accounting'
AND s.consol_level = 1
GROUP BY t.fk_tenant_id, t.budget_year, s.consol_level, t.fk_kostra_account_code, fk_kostra_function_code,s.owner_share


INSERT INTO #tcon_mr_hlp1 (fk_tenant_id, budget_year, consol_level, [1A_line], fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year)
SELECT t.fk_tenant_id, t.budget_year+1, s.consol_level, 0 as [1A_line], t.fk_kostra_account_code, fk_kostra_function_code,
 SUM(year_1_amount) * s.owner_share/100 AS actual_amt_last_year,0 AS actual_amt_year, 
0 AS org_bud_amt_year, 0 as revised_bud_amt_year 
FROM tcon_consol_transactions t
JOIN tcon_consol_company_setup s ON t.fk_company_id = s.pk_company_id AND t.fk_tenant_id = s.fk_tenant_id
AND s.budget_year = t.budget_year AND s.include_consol = 1
WHERE t.fk_tenant_id = @tenant_id
AND t.budget_year = @budget_year-1
AND t.amount_type = 'Accounting'
GROUP BY t.fk_tenant_id, t.budget_year, s.consol_level, t.fk_kostra_account_code, fk_kostra_function_code, s.owner_share


UPDATE a SET a.fk_kostra_account_code = r.elimination_diff_account
FROM #tcon_mr_hlp1 a
JOIN gco_elimination_rules r ON a.fk_kostra_account_code = r.account_code_1

UPDATE a SET a.fk_kostra_account_code = r.elimination_diff_account
FROM #tcon_mr_hlp1 a
JOIN gco_elimination_rules r ON a.fk_kostra_account_code = r.account_code_2



UPDATE #tcon_mr_hlp1 SET acc_type = 1 
FROM #tcon_mr_hlp1 a, gco_kostra_accounts c
WHERE  a.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'operations'


UPDATE #tcon_mr_hlp1 SET acc_type = 2 
FROM #tcon_mr_hlp1 a, gco_kostra_accounts c
WHERE  a.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'investment'


UPDATE #tcon_mr_hlp1 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  gmd_reporting_line c 
LEFT OUTER JOIN  #tcon_mr_hlp1 a ON  a.fk_kostra_account_code = c.fk_kostra_account_code
--AND a.fk_kostra_function_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = '54_DRIFTA' 


UPDATE #tcon_mr_hlp1 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  gmd_reporting_line c 
LEFT OUTER JOIN  #tcon_mr_hlp1 a ON a.fk_kostra_account_code = c.fk_kostra_account_code
AND c.line_group_id != 10
WHERE  c.report = '54_DRIFTA' 


DELETE FROM [tmr_cons_budform_1A] WHERE forecast_period = @forecast_period AND fk_tenant_id = @tenant_id
DELETE FROM [tmr_cons_budform_B3] WHERE forecast_period = @forecast_period AND fk_tenant_id = @tenant_id
DELETE FROM [tmr_cons_budform_2A] WHERE forecast_period = @forecast_period AND fk_tenant_id = @tenant_id


INSERT INTO [tmr_cons_budform_1A] (fk_tenant_id, forecast_period, consol_level, [1A_line], line_group_id, line_group, line_item_id, line_item,
fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year, updated_by, updated)
SELECT fk_tenant_id, @forecast_period, consol_level, [1A_line], 
ISNULL(line_group_id,0), 
ISNULL(line_group,''), 
ISNULL(line_item_id,0), 
ISNULL(line_item,''),
fk_kostra_account_code, fk_kostra_function_code,
SUM(actual_amt_last_year), SUM(actual_amt_year), SUM(org_bud_amt_year), SUM(revised_bud_amt_year), 
@user_id as updated_by, getdate() as updated
FROM #tcon_mr_hlp1
WHERE acc_type = 1
GROUP BY fk_tenant_id, budget_year, consol_level, [1A_line], line_group_id, line_group, line_item_id, line_item,
fk_kostra_account_code, fk_kostra_function_code

INSERT INTO [tmr_cons_budform_B3] (fk_tenant_id, forecast_period, consol_level,line_group_id, line_group, line_item_id, line_item,
fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year, updated_by, updated)
SELECT h.fk_tenant_id, @forecast_period, h.consol_level,
rl.line_group_id, rl.line_group,rl.line_item_id, rl.line_item,
h.fk_kostra_account_code, h.fk_kostra_function_code,
SUM(h.actual_amt_last_year), SUM(h.actual_amt_year), SUM(h.org_bud_amt_year), SUM(h.revised_bud_amt_year), 
@user_id as updated_by, getdate() as updated
FROM #tcon_mr_hlp1 h
JOIN gmd_reporting_line rl ON h.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '54_OVDRIFT' 
GROUP BY h.fk_tenant_id, h.budget_year, h.consol_level, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,
h.fk_kostra_account_code, h.fk_kostra_function_code


INSERT INTO [tmr_cons_budform_2A] (fk_tenant_id, forecast_period, consol_level,line_group_id, line_group, line_item_id, line_item,
fk_kostra_account_code, fk_kostra_function_code,
actual_amt_last_year, actual_amt_year, org_bud_amt_year, revised_bud_amt_year, updated_by, updated)
SELECT h.fk_tenant_id, @forecast_period, h.consol_level,
rl.line_group_id, rl.line_group,rl.line_item_id, rl.line_item,
h.fk_kostra_account_code, h.fk_kostra_function_code,
SUM(h.actual_amt_last_year), SUM(h.actual_amt_year), SUM(h.org_bud_amt_year), SUM(h.revised_bud_amt_year), 
@user_id as updated_by, getdate() as updated
FROM #tcon_mr_hlp1 h
JOIN gmd_reporting_line rl ON h.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' 
GROUP BY h.fk_tenant_id, h.budget_year, h.consol_level, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,
h.fk_kostra_account_code, h.fk_kostra_function_code





RETURN 0

