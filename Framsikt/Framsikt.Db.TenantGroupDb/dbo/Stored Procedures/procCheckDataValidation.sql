CREATE OR ALTER PROCEDURE [dbo].[procCheckDataValidation]
	@budget_year int,
	@tenant_id int = 0

AS

CREATE TABLE #tenant_check_depts
(fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
forecast_period INT NOT NULL,
fk_department_code NVARCHAR(25) NOT NULL,
tbu_revised_budget DECIMAL (18,2) NOT NULL,
tbu_original_budget DECIMAL (18,2) NOT NULL,
tfp_finplan_amount DECIMAL (18,2) NOT NULL,
tbu_forecast_amount DECIMAL (18,2) NOT NULL,
twh_revised_budget DECIMAL (18,2) NOT NULL,
twh_original_budget DECIMAL (18,2) NOT NULL,
twh_report_finplan_amount DECIMAL (18,2) NOT NULL,
twh_forecast_amount DECIMAL (18,2) NOT NULL,
twh_doc_finplan_amount DECIMAL (18,2) NOT NULL,
cons_parent_finplan_amount DECIMAL (18,2) DEFAULT 0 NOT NULL,
cons_child_doc_finplan_amount DECIMAL (18,2) DEFAULT 0 NOT NULL,
);

CREATE TABLE #error_table (
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
forecast_period INT NOT NULL,
org_unit NVARCHAR (255) NOT NULL,
error_code NVARCHAR (25) NOT NULL,
error_description NVARCHAR (500) NOT NULL,
transaction_value  DECIMAL (18,2) NOT NULL,
warehouse_value DECIMAL (18,2) NOT NULL,
deviation DECIMAL (18,2) NOT NULL)



PRINT 'pleace check the result in table tco_error_data_check'

PRINT 'Fetch from tbu_trans_detail '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id,budget_year, 0 as forecast_period, department_code, sum(amount_year_1) as tbu_revised_budget, 0 as  tbu_original_budget, 0 as  tfp_finplan_amount, 
0 as tbu_forecast_amount, 0 as twh_revised_budget, 0 as twh_original_budget, 0 as twh_finplan_amount, 0 as twh_forecast_amount, 0 as twh_doc_finplan_amount
FROM tbu_trans_detail
WHERE budget_year = @budget_year
GROUP BY fk_tenant_id, department_code, budget_year;

PRINT 'Fetch from tbu_trans_detail_original '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id, budget_year, 0 as forecast_period, department_code, 0 as tbu_revised_budget, 
sum(amount_year_1) as tbu_original_budget, 0 as tfp_finplan_amount, 
0 as tbu_forecast_amount, 0 as twh_revised_budget, 0 as twh_original_budget, 0 as twh_finplan_amount, 0 as twh_forecast_amount, 0 as twh_doc_finplan_amount
FROM tbu_trans_detail_original
WHERE budget_year = @budget_year
GROUP BY fk_tenant_id, department_code, budget_year

PRINT 'Fetch from tfp_trans_detail '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id, budget_year, 0 as forecast_period, department_code, 0 as tbu_revised_budget, 0 as tbu_original_budget, sum(year_1_amount) as tfp_finplan_amount,
0 as tbu_forecast_amount, 0 as twh_revised_budget, 0 as twh_original_budget, 0 as twh_finplan_amount, 0 as twh_forecast_amount, 0 as twh_doc_finplan_amount
FROM tfp_trans_detail
WHERE budget_year = @budget_year
GROUP BY fk_tenant_id, department_code, budget_year

PRINT 'Fetch from tbu_forecast_transactions '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id, budget_year, forecast_period, department_code, 0 as tbu_revised_budget, 0 as tbu_original_budget, 0 as tfp_finplan_amount, sum(amount_year_1) as tbu_forecast_amount,
0 as twh_revised_budget, 0 as twh_original_budget, 0 as twh_finplan_amount, 0 as twh_forecast_amount, 0 as twh_doc_finplan_amount
FROM tbu_forecast_transactions
WHERE budget_year = @budget_year
GROUP BY fk_tenant_id, forecast_period,budget_year, department_code

PRINT 'Fetch from twh_budget_report '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id, budget_year, 0 as forecast_period, department_code, 0 as tbu_revised_budget, 0 as tbu_original_budget, 0 as tfp_finplan_amount, 
0 as tbu_forecast_amount, sum(revised_budget) AS twh_revised_budget, SUM(original_budget) AS twh_original_budget, 0 as twh_finplan_amount, 0 as twh_forecast_amount,0 as twh_doc_finplan_amount
FROM twh_budget_report 
WHERE budget_year = @budget_year
GROUP BY fk_tenant_id, department_code, budget_year

PRINT 'Fetch from twh_buddoc_reports '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id, budget_year, 0 as forecast_period, fk_department_code, 0 as tbu_revised_budget, 0 as tbu_original_budget, 0 as tfp_finplan_amount, 
0 as tbu_forecast_amount, 0 as twh_revised_budget, 0 as twh_original_budget, 0 as twh_finplan_amount, 0 as twh_forecast_amount, 
sum(year_1_amount) as twh_doc_finplan_amount
FROM twh_buddoc_reports
WHERE budget_year = @budget_year
GROUP BY fk_tenant_id, fk_department_code, budget_year

PRINT 'Fetch from twh_budget_report '  + convert(varchar(400),GETDATE());

INSERT INTO #tenant_check_depts (fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,twh_doc_finplan_amount)
SELECT fk_tenant_id, budget_year, 0 as forecast_period, fk_department_code, 0 as tbu_revised_budget, 0 as tbu_original_budget, 0 as tfp_finplan_amount, 
0 as tbu_forecast_amount, 0 as twh_revised_budget, 0 as twh_original_budget, SUM(fp_year_1_amount) as twh_finplan_amount, 0 as twh_forecast_amount, 
0 as twh_doc_finplan_amount
FROM twh_finplan_report 
WHERE budget_year = @budget_year AND data_type = 'F'
GROUP BY fk_tenant_id, fk_department_code, budget_year


INSERT INTO #tenant_check_depts 
(fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,
twh_doc_finplan_amount, cons_parent_finplan_amount, cons_child_doc_finplan_amount)
select hq.tenant_id_child, d.budget_year, 0 as forecast_period, '' fk_department_code, 0 as tbu_revised_budget,0 as  tbu_original_budget, 0 AS tfp_finplan_amount, 
0 as tbu_forecast_amount,0 as twh_revised_budget,0 as  twh_original_budget, 0 as twh_report_finplan_amount, 0 as twh_forecast_amount,
0 as twh_doc_finplan_amount, sum(d.year_1_amount) as cons_parent_finplan_amount, 0 as cons_child_doc_finplan_amount
from tmd_hq_tenant_definition hq
JOIN tfp_trans_detail d ON hq.fk_tenant_id = d.fk_tenant_id AND hq.budget_year = d.budget_year AND hq.default_change_id = d.fk_change_id
WHERE d.budget_year = @budget_year
GROUP BY hq.tenant_id_child, d.budget_year

INSERT INTO #tenant_check_depts 
(fk_tenant_id, budget_year, forecast_period, fk_department_code, tbu_revised_budget, tbu_original_budget, tfp_finplan_amount, 
tbu_forecast_amount, twh_revised_budget, twh_original_budget, twh_report_finplan_amount, twh_forecast_amount,
twh_doc_finplan_amount, cons_parent_finplan_amount, cons_child_doc_finplan_amount)
select hq.tenant_id_child, d.budget_year, 0 as forecast_period, '' fk_department_code, 0 as tbu_revised_budget,0 as  tbu_original_budget, 0 AS tfp_finplan_amount, 
0 as tbu_forecast_amount,0 as twh_revised_budget,0 as  twh_original_budget, 0 as twh_report_finplan_amount, 0 as twh_forecast_amount,
0 as twh_doc_finplan_amount, 0 as cons_parent_finplan_amount, sum(d.year_1_amount) as cons_child_doc_finplan_amount  
from tmd_hq_tenant_definition hq
JOIN tfp_trans_detail d ON hq.tenant_id_child = d.fk_tenant_id AND hq.budget_year = d.budget_year 
WHERE d.budget_year = @budget_year
GROUP BY hq.tenant_id_child, d.budget_year



INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'BUDGETREPORT' error_code, 'Mismatch on revised budget' error_description, 
sum(tbu_revised_budget), sum(twh_revised_budget),sum(tbu_revised_budget)- sum(twh_revised_budget) 
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  abs(sum(tbu_revised_budget)- sum(twh_revised_budget)) > 1;

INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'BUDGETREPORT' error_code, 'Mismatch on original budget' error_description, 
sum(tbu_original_budget), sum(twh_original_budget),sum(tbu_original_budget)- sum(twh_original_budget) 
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  abs(sum(tbu_original_budget)- sum(twh_original_budget)) > 1


INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'FINPLANREPORT' error_code, 'Mismatch on finplan amount year 1' error_description, 
sum(tfp_finplan_amount), sum(twh_report_finplan_amount),sum(tfp_finplan_amount)- sum(twh_report_finplan_amount) 
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  abs(sum(tfp_finplan_amount)- sum(twh_report_finplan_amount)) > 1


INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'FINPLANDOC' error_code, 'Mismatch on finplan document warehouse amount year 1' error_description, 
sum(tfp_finplan_amount), sum(twh_doc_finplan_amount),sum(tfp_finplan_amount)- sum(twh_doc_finplan_amount) 
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  abs(sum(tfp_finplan_amount)- sum(twh_doc_finplan_amount)) > 1

INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'FORECASTREPORT' error_code, 'Mismatch on forecast amount in forecast report' error_description, 
sum(tbu_forecast_amount), sum(twh_forecast_amount),sum(tbu_forecast_amount)- sum(twh_forecast_amount) 
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  abs(sum(tbu_forecast_amount)- sum(twh_forecast_amount)) > 1

INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'CONSOLIDATEERROR' error_code, 'Mismatch on finplan amount in the consolidated tenant' error_description, 
sum(cons_child_doc_finplan_amount), sum(cons_parent_finplan_amount),sum(cons_child_doc_finplan_amount)- sum(cons_parent_finplan_amount) 
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  abs(sum(cons_child_doc_finplan_amount)- sum(cons_parent_finplan_amount)) > 1



INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, 
transaction_value, warehouse_value, deviation)
SELECT fk_tenant_id, budget_year, forecast_period,'' as org_unit, 
'FINPLANTOTAL' error_code, 'Finplan in total is not zero' error_description, 
sum(tfp_finplan_amount), 0,sum(tfp_finplan_amount)
FROM #tenant_check_depts
GROUP BY fk_tenant_id, budget_year,forecast_period
HAVING  ABS(sum(tfp_finplan_amount)) > 1000


INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, 
transaction_value, warehouse_value, deviation)
SELECT t.fk_tenant_id, t.budget_year, t.forecast_period, oh.org_id_2 + ' - ' + oh.org_name_2,
'RAMMECTRL' error_code, 'Discrepencey between budget and finplan' as error_description,
SUM(tfp_finplan_amount), SUM(tbu_revised_budget), SUM(tfp_finplan_amount)-SUM(tbu_revised_budget)
FROM #tenant_check_depts t
JOIN tco_org_version ov ON t.fk_tenant_id = ov.fk_Tenant_id and (t.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on t.fk_tenant_id = oh.fk_Tenant_id and t.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
GROUP BY t.fk_tenant_id, t.budget_year, t.forecast_period, oh.org_id_2, oh.org_name_2
HAVING ABS(SUM(tfp_finplan_amount)-SUM(tbu_revised_budget)) > 1


INSERT INTO #error_table (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, 
transaction_value, warehouse_value, deviation)
SELECT DISTINCT oh.fk_tenant_id, @budget_year,0 as forecast_period, oh.org_id_2 + ' - ' + oh.org_name_2,
'ORGBUDGETLOCK' error_code, 'Original budet is not locked for these units',
0 as transaction_value, 0 as warehouse_value, 0 as deviation
FROM tco_org_hierarchy oh
JOIN tco_org_version ov ON oh.fk_tenant_id = ov.fk_Tenant_id and (@budget_year)*100+1 between ov.period_from and ov.period_to and ov.pk_org_version = oh.fk_org_version
WHERE oh.fk_tenant_id = 1
AND NOT EXISTS (
SELECT * FROM tco_application_flag f 
WHERE flag_name = 'LOCK_ORIGINAL_BUDGET'
AND budget_year = @budget_year
AND ov.fk_tenant_id = f.fk_tenant_id
)  

DELETE FROM tco_error_data_check WHERE budget_year = @budget_year;

INSERT INTO tco_error_data_check (fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, 
transaction_value, warehouse_value, deviation, updated)
SELECT fk_tenant_id, budget_year, forecast_period,org_unit, error_code, error_description, 
transaction_value, warehouse_value, deviation, GETDATE() AS updated
FROM #error_table

PRINT 'pleace check the result in table tco_error_data_check'

RETURN 0
