CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportInvestmentStatus]
	@tenant_id int,        
	 @user_id int,        
	 @budget_year int,        
	 @forecast_period int,
	 @job_id int
AS
IF(@job_id = -1)
	BEGIN
		 UPDATE tmr_stage_status_ISY_import        
			 SET status_level_error = 0,        
				 ID_error = 0,        
			  est_finish_quarter_error = 0,         
			  status_error = 0,        
			  risk_error = 0,        
			  quality_error = 0,        
			  fin_status_error = 0,        
			  status_desc_error = 0,        
			  error_count = 0,
			  ISY_update_date_error=0
			 WHERE fk_tenant_id = @tenant_id AND         
			  forecast_period = @forecast_period AND      
			  updated_by = @user_id;

		-- validate status level
		UPDATE	tmr_stage_status_ISY_import
		SET		status_level_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period  AND (tmr_stage_status_ISY_import.status_level IS NULL OR tmr_stage_status_ISY_import.status_level = ''
				OR status_level NOT IN ('P','MP','1','2','3','4','5'));
		-- validate ID

			UPDATE	tmr_stage_status_ISY_import
			SET		ID_error = 1, error_count = error_count + 1
			FROM	tmr_stage_status_ISY_import
			WHERE	fk_tenant_id = @tenant_id AND updated_by = @user_id AND forecast_period = @forecast_period AND (tmr_stage_status_ISY_import.ID IS NULL OR tmr_stage_status_ISY_import.ID = '' OR 
					((status_level = 'P' AND 
					(select count(pk_id) from tco_projects where fk_tenant_id = @tenant_id AND active = 1 AND LEFT(date_from,4) <= @budget_year AND LEFT(date_to,4) >= @budget_year) != 0) 
					AND ID NOT IN (select pk_project_code from tco_projects where fk_tenant_id = @tenant_id AND active = 1 AND LEFT(date_from,4) <= @budget_year AND LEFT(date_to,4) >= @budget_year)) OR
					((status_level = 'MP' AND  (select count(pk_id) from tco_main_projects where fk_tenant_id = @tenant_id AND status = 1 AND LEFT(budget_year_from,4) <= @budget_year AND LEFT(budget_year_to,4) >= @budget_year) != 0)
					AND ID NOT IN (select pk_main_project_code from tco_main_projects where fk_tenant_id = @tenant_id AND status = 1 AND LEFT(budget_year_from,4) <= @budget_year AND LEFT(budget_year_to,4) >= @budget_year)) OR 
					((status_level = '1' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 1) != 0) AND 
										  ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 1)) OR
					((status_level = '2' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 2) != 0) AND 
										  ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 2)) OR
					((status_level = '3' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 3) != 0) AND 
										ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 3)) OR
					((status_level = '4' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 4) != 0) AND 
										ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 4)) OR
					((status_level = '5' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 5) != 0) AND 
										ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 5)));
		-- validate status 
		UPDATE	tmr_stage_status_ISY_import
		SET		status_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND status = '' OR status  = NULL OR ISNUMERIC(status) = 0 OR
				(status!= 0 AND status NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV'));
		-- validate fin status
		UPDATE	tmr_stage_status_ISY_import
		SET		fin_status_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND fin_status = '' OR fin_status  = NULL OR ISNUMERIC(fin_status) = 0 OR
				(fin_status != 0 AND fin_status NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV_FIN_STATUS'));
	-- validate quality
		UPDATE	tmr_stage_status_ISY_import
		SET		quality_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND quality = '' OR quality  = NULL OR ISNUMERIC(quality) = 0 OR
				(quality != 0 AND quality NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV_QUALITY'));
		-- validate risk
		--UPDATE	tmr_stage_status_ISY_import
		--SET		risk_error = 1, error_count = error_count + 1
		--FROM	tmr_stage_status_ISY_import
		--WHERE	fk_tenant_id = @tenant_id AND
		--		updated_by = @user_id AND forecast_period = @forecast_period AND risk = '' OR risk  = NULL OR ISNUMERIC(risk) = 0 OR
		--		(risk != 0 AND (risk NOT IN (0,1,2,3)));

		UPDATE	tmr_stage_status_ISY_import
		SET		risk_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND risk = '' OR risk  = NULL OR ISNUMERIC(risk) = 0 OR
				(risk!= 0 AND risk NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV_RISK'));
		-- validate est finish quarter 

		UPDATE	tmr_stage_status_ISY_import
		SET		est_finish_quarter_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND (est_finish_quarter!= null AND est_finish_quarter != '') AND ((ISNUMERIC(est_finish_quarter) = 0  OR LEN(est_finish_quarter) != 5 OR (est_finish_quarter / 10 ) < 2000 OR (est_finish_quarter / 10 ) > 2099
				OR RIGHT(est_finish_quarter,1) NOT IN (1,2,3,4)))

	  -- validate isy update date

	   update tmr_stage_status_ISY_import set isy_update_date_error =1, error_count = error_count + 1
	   where fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND TRY_CONVERT(DATETIME, ISY_update_date) IS NULL
END
ELSE
	BEGIN
		 UPDATE tmr_stage_status_ISY_import        
				 SET status_level_error = 0,        
					 ID_error = 0,        
				  est_finish_quarter_error = 0,         
				  status_error = 0,        
				  risk_error = 0,        
				  quality_error = 0,        
				  fin_status_error = 0,        
				  status_desc_error = 0,        
				  error_count = 0
				 WHERE fk_tenant_id = @tenant_id AND         
				  forecast_period = @forecast_period AND      
				  fk_job_id = @job_id;

			-- validate status level
			UPDATE	tmr_stage_status_ISY_import
			SET		status_level_error = 1, error_count = error_count + 1
			FROM	tmr_stage_status_ISY_import
			WHERE	fk_tenant_id = @tenant_id AND
					fk_job_id = @job_id AND forecast_period = @forecast_period  AND (tmr_stage_status_ISY_import.status_level IS NULL OR tmr_stage_status_ISY_import.status_level = ''
					OR status_level NOT IN ('P','MP','1','2','3','4','5'));
			-- validate ID

				UPDATE	tmr_stage_status_ISY_import
				SET		ID_error = 1, error_count = error_count + 1
				FROM	tmr_stage_status_ISY_import
				WHERE	fk_tenant_id = @tenant_id AND fk_job_id = @job_id AND forecast_period = @forecast_period AND (tmr_stage_status_ISY_import.ID IS NULL OR tmr_stage_status_ISY_import.ID = '' OR 
						((status_level = 'P' AND 
						(select count(pk_id) from tco_projects where fk_tenant_id = @tenant_id AND active = 1 AND LEFT(date_from,4) <= @budget_year AND LEFT(date_to,4) >= @budget_year) != 0) 
						AND ID NOT IN (select pk_project_code from tco_projects where fk_tenant_id = @tenant_id AND active = 1 AND LEFT(date_from,4) <= @budget_year AND LEFT(date_to,4) >= @budget_year)) OR
						((status_level = 'MP' AND  (select count(pk_id) from tco_main_projects where fk_tenant_id = @tenant_id AND status = 1 AND LEFT(budget_year_from,4) <= @budget_year AND LEFT(budget_year_to,4) >= @budget_year) != 0)
						AND ID NOT IN (select pk_main_project_code from tco_main_projects where fk_tenant_id = @tenant_id AND status = 1 AND LEFT(budget_year_from,4) <= @budget_year AND LEFT(budget_year_to,4) >= @budget_year)) OR 
						((status_level = '1' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 1) != 0) AND 
											  ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 1)) OR
						((status_level = '2' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 2) != 0) AND 
											  ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 2)) OR
						((status_level = '3' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 3) != 0) AND 
											ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 3)) OR
						((status_level = '4' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 4) != 0) AND 
											ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 4)) OR
						((status_level = '5' AND (select count(pk_id) from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 5) != 0) AND 
											ID NOT IN (select proj_gr_id from tco_proj_level_data where fk_tenant_id = @tenant_id AND level = 5)));
			-- validate status 
			UPDATE	tmr_stage_status_ISY_import
			SET		status_error = 1, error_count = error_count + 1
			FROM	tmr_stage_status_ISY_import
			WHERE	fk_tenant_id = @tenant_id AND
					fk_job_id = @job_id AND forecast_period = @forecast_period AND status = '' OR status  = NULL OR ISNUMERIC(status) = 0 OR
					(status!= 0 AND status NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV'));
			-- validate fin status
			UPDATE	tmr_stage_status_ISY_import
			SET		fin_status_error = 1, error_count = error_count + 1
			FROM	tmr_stage_status_ISY_import
			WHERE	fk_tenant_id = @tenant_id AND
					fk_job_id = @job_id AND forecast_period = @forecast_period AND fin_status = '' OR fin_status  = NULL OR ISNUMERIC(fin_status) = 0 OR
					(fin_status != 0 AND fin_status NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV_FIN_STATUS'));
		-- validate quality
			UPDATE	tmr_stage_status_ISY_import
			SET		quality_error = 1, error_count = error_count + 1
			FROM	tmr_stage_status_ISY_import
			WHERE	fk_tenant_id = @tenant_id AND
					fk_job_id = @job_id AND forecast_period = @forecast_period AND quality = '' OR quality  = NULL OR ISNUMERIC(quality) = 0 OR
					(quality != 0 AND quality NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV_QUALITY'));
			-- validate risk
			--UPDATE	tmr_stage_status_ISY_import
			--SET		risk_error = 1, error_count = error_count + 1
			--FROM	tmr_stage_status_ISY_import
			--WHERE	fk_tenant_id = @tenant_id AND
			--		fk_job_id = @job_id AND forecast_period = @forecast_period AND risk = '' OR risk  = NULL OR ISNUMERIC(risk) = 0 OR
			--		(risk != 0 AND (risk NOT IN (0,1,2,3)));
			
		UPDATE	tmr_stage_status_ISY_import
		SET		risk_error = 1, error_count = error_count + 1
		FROM	tmr_stage_status_ISY_import
		WHERE	fk_tenant_id = @tenant_id AND
				updated_by = @user_id AND forecast_period = @forecast_period AND risk = '' OR risk  = NULL OR ISNUMERIC(risk) = 0 OR
				(risk!= 0 AND risk NOT IN (SELECT status_id from tco_progress_status where fk_tenant_id = @tenant_id AND type='MONTHREP_INV_RISK'));
			-- validate est finish quarter 

			UPDATE	tmr_stage_status_ISY_import
			SET		est_finish_quarter_error = 1, error_count = error_count + 1
			FROM	tmr_stage_status_ISY_import
			WHERE	fk_tenant_id = @tenant_id AND
					fk_job_id = @job_id AND forecast_period = @forecast_period AND (est_finish_quarter!= null AND est_finish_quarter != '') AND  ((ISNUMERIC(est_finish_quarter) = 0  OR LEN(est_finish_quarter) != 5 OR (est_finish_quarter / 10 ) < 2000 OR (est_finish_quarter / 10 ) > 2099
					OR RIGHT(est_finish_quarter,1) NOT IN (1,2,3,4)))
		    -- validate isy update date

	   update tmr_stage_status_ISY_import set isy_update_date_error =1, error_count = error_count + 1
	   where fk_tenant_id = @tenant_id AND
				fk_job_id = @job_id AND forecast_period = @forecast_period AND TRY_CONVERT(DATETIME, ISY_update_date) IS NULL
END
RETURN 0