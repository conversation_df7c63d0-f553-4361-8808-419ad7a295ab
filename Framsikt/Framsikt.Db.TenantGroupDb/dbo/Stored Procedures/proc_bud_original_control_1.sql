CREATE PROC [dbo].[proc_bud_original_control]
@tenant_id INT,
@budget_year INT,
@batch_id varchar (12) = ''
AS

--DECLARE
--@tenant_id INT = 2092,
--@budget_year INT = 2023,
--@batch_id varchar (12) = '230207150734'

--Find erp type and check if valid
DECLARE @erp_type NVARCHAR(20) = (select erp_type from gco_tenants where pk_id = @tenant_id)

IF @erp_type NOT IN ('Agresso', 'Visma')
BEGIN
	PRINT 'ERP type not supported'
	RETURN
END

--Find existing export id corresponding to batch id if exists
DECLARE @export_id INT
DECLARE @replace_existing_batch BIT = 1

IF @batch_id = ''
BEGIN
	SET @replace_existing_batch = 0
END


IF @replace_existing_batch = 1
BEGIN
	IF @erp_type = 'Visma'
	BEGIN
		SET @export_id = (
		SELECT MAX(export_id) FROM [dbo].[integnew_export_visma_posted_origbud]
		WHERE fk_tenant_id = @tenant_id
		AND batch_id = @batch_id)
	END
	IF @erp_type = 'Agresso'
	BEGIN
		SET @export_id = (
		SELECT MAX(export_id) FROM [dbo].[integ_agrpostback_origbud]
		WHERE fk_tenant_id = @tenant_id
		AND [Batchid] = @batch_id)
	END
END

--If no export_id found then generate new even if batch_id is supplied
IF @export_id IS NULL
BEGIN
	SET @replace_existing_batch = 0
END



--Define batch_id - should ideally been checked against batch ID table in INTEG DB for uniqueness, but not implemented here.
IF @replace_existing_batch = 0
BEGIN
	DECLARE @now datetime;
	SET @now = GETDATE();
	DECLARE @rolling_id nvarchar(2);
	SET @rolling_id = (SELECT CAST(RIGHT(ABS(CAST(CAST(NEWID() AS VARBINARY) AS INT)),2) AS VARCHAR(2)))
	SET @batch_id = CONCAT(CONVERT(VARCHAR,  @now, 12), RIGHT('000'+CAST(datepart(hour, @now) AS VARCHAR(2)),2), RIGHT('000'+CAST(datepart(minute, @now) AS VARCHAR(2)),2), @rolling_id)
END

DROP TABLE IF EXISTS #temp_budget_dimensions
CREATE TABLE #temp_budget_dimensions(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[export_id] [bigint] NOT NULL,
	[batch_id] [nvarchar](14) NOT NULL,
	[budget_type] [nvarchar](25) NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[amount] [decimal](18, 2) NOT NULL)

DROP TABLE IF EXISTS #account_list_1
CREATE TABLE #account_list_1 (pk_tenant_id INT, pk_Account_code NVARCHAR(25),[type] varchar(50))
--Fetch accounts to be included
INSERT INTO #account_list_1
SELECT b.pk_tenant_id, b.pk_account_code, c.[type]
FROM tco_accounts b
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code
WHERE @budget_year between DATEPART(YEAR,b.dateFrom) and DATEPART(YEAR,b.dateTo)
AND b.pk_tenant_id = @tenant_id
GROUP BY b.pk_tenant_id, b.pk_account_code, c.[type]


IF @erp_type = 'Visma'
BEGIN
	IF @replace_existing_batch = 0
	BEGIN
		INSERT INTO [dbo].[tfp_erp_exports]
				   ([date]
				   ,[exported_by]
				   ,[export_type]
				   ,[tenant_id]
				   ,[is_delta]
				   ,[budget_Year])

		SELECT		[date] = getdate()
				   ,[exported_by] = 1001
				   ,[export_type] = 'Visma'
				   ,[tenant_id] = @tenant_id
				   ,[is_delta] = 0
				   ,[budget_Year] = @budget_year

		SET @export_id = (SELECT SCOPE_IDENTITY())
	END

	IF @replace_existing_batch = 1
	BEGIN
		UPDATE [dbo].[tfp_erp_exports] SET [date] = getdate(), exported_by = 1001 WHERE tenant_id = @tenant_id and [id] = @export_id
	END

	DROP TABLE IF EXISTS #export_dump_visma
	CREATE TABLE #export_dump_visma(
	[export_id] [bigint] NOT NULL,
	[fk_action_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[description] [nvarchar](700) NOT NULL,
	[company] [nvarchar](100) NOT NULL,
	[region] [nvarchar](100) NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[free_dim_5] [nvarchar](25) NOT NULL,
	[free_dim_6] [nvarchar](25) NOT NULL,
	[fk_adjustment_code] [nvarchar](25) NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[revised_budget] [decimal](18, 2) NOT NULL,
	[jan] [decimal](18, 2) NOT NULL,
	[feb] [decimal](18, 2) NOT NULL,
	[mar] [decimal](18, 2) NOT NULL,
	[apr] [decimal](18, 2) NOT NULL,
	[may] [decimal](18, 2) NOT NULL,
	[jun] [decimal](18, 2) NOT NULL,
	[jul] [decimal](18, 2) NOT NULL,
	[aug] [decimal](18, 2) NOT NULL,
	[sep] [decimal](18, 2) NOT NULL,
	[oct] [decimal](18, 2) NOT NULL,
	[nov] [decimal](18, 2) NOT NULL,
	[dec] [decimal](18, 2) NOT NULL)

	insert into #export_dump_visma
	EXEC [dbo].[proc_bud_visma_original]
	@tenant_id = @tenant_id,
	@budget_year = @budget_year,
	@export_id = @export_id

	--Insert into erp log for logging purposes
	IF @replace_existing_batch = 1
	BEGIN
		DELETE FROM [dbo].[tfp_erp_export_log_visma]
		WHERE fk_tenant_id = @tenant_id
		AND export_id = @export_id
	END

	INSERT INTO [dbo].[tfp_erp_export_log_visma]
           ([export_id],[fk_tenant_id],[company],[region],[budget_year],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[free_dim_5],[free_dim_6]
           ,[revised_budget],[jan],[feb],[mar],[apr],[may],[jun],[jul],[aug],[sep],[oct],[nov],[dec],[fk_action_id],[description],[fk_adjustment_code],[fk_alter_code])
	SELECT [export_id],[fk_tenant_id],[company],[region],[budget_year],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[free_dim_5],[free_dim_6]
           ,[revised_budget],[jan],[feb],[mar],[apr],[may],[jun],[jul],[aug],[sep],[oct],[nov],[dec],[fk_action_id],[description],[fk_adjustment_code],[fk_alter_code]
	FROM #export_dump_visma
	
	IF @replace_existing_batch = 1
	BEGIN
		DELETE FROM [dbo].[integnew_export_visma_postbatches]
		WHERE fk_tenant_id = @tenant_id
		AND batch_id = @batch_id
	END

	INSERT INTO [dbo].[integnew_export_visma_postbatches]
           ([fk_tenant_id]
           ,[batch_id]
           ,[is_orig_bud]
           ,[transferred]
           ,[updated]
           ,[budget_year]
           ,[error_fixed]
           ,[response_message]
           ,[response_status]
		   ,[control_batch])
	select
			[fk_tenant_id] = @tenant_id
           ,[batch_id] = @batch_id
           ,[is_orig_bud] = 1
           ,[transferred] = 0
           ,[updated] = getdate()
           ,[budget_year] = @budget_year
           ,[error_fixed] = 0
           ,[response_message] = 'Generated by orig bud control SP'
           ,[response_status] = 0
		   ,control_batch = 1

	IF @replace_existing_batch = 1
	BEGIN
		DELETE FROM [dbo].[integnew_export_visma_posted_origbud]
		WHERE fk_tenant_id = @tenant_id
		AND export_id = @export_id
		AND batch_id = @batch_id
	END

	INSERT INTO [dbo].[integnew_export_visma_posted_origbud]
	([fk_tenant_id]
	,[export_id]
	,[batch_id]
	,[fk_action_id]
	,[fk_account_code]
	,[fk_department_code]
	,[fk_function_code]
	,[fk_project_code]
	,[fk_alter_code]
	,[fk_adjustment_code]
	,[free_dim_1]
	,[free_dim_2]
	,[free_dim_3]
	,[free_dim_4]
	,[description]
	,[jan]
	,[feb]
	,[mar]
	,[apr]
	,[may]
	,[jun]
	,[jul]
	,[aug]
	,[sep]
	,[oct]
	,[nov]
	,[dec]
	,[year_total])
	SELECT 
	[fk_tenant_id]
	,[export_id]
	,[batch_id] = @batch_id
	,[fk_action_id]
	,[fk_account_code]
	,[fk_department_code]
	,[fk_function_code]
	,[fk_project_code]
	,[fk_alter_code]
	,[fk_adjustment_code]
	,[free_dim_1]
	,[free_dim_2]
	,[free_dim_3]
	,[free_dim_4]
	,[description] = LEFT([description],255)
	,[jan]
	,[feb]
	,[mar]
	,[apr]
	,[may]
	,[jun]
	,[jul]
	,[aug]
	,[sep]
	,[oct]
	,[nov]
	,[dec]
	,[year_total] = revised_budget
	FROM #export_dump_visma

	--Fetch grouped dimensions to be used in further controls
	INSERT INTO #temp_budget_dimensions
	([fk_tenant_id],export_id,batch_id,[budget_year],[budget_type],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code]
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,[amount])
	select [fk_tenant_id]
	,export_id
	,batch_id = @batch_id
	,[budget_year]
	,[budget_type] = b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,amount = SUM([jan]+[feb]+[mar]+[apr]+[may]+[jun]+[jul]+[aug]+[sep]+[oct]+[nov]+[dec])
	FROM #export_dump_visma a
	JOIN #account_list_1 b on a.fk_tenant_id = b.pk_tenant_id  AND a.fk_account_code = b.pk_account_code
	GROUP BY [fk_tenant_id],export_id,[budget_year]
	,b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]

END

IF @erp_type = 'Agresso'
BEGIN
	IF @replace_existing_batch = 0
	BEGIN
	INSERT INTO [dbo].[tfp_erp_exports]
			   ([date]
			   ,[exported_by]
			   ,[export_type]
			   ,[tenant_id]
			   ,[is_delta]
			   ,[budget_Year])

	SELECT		[date] = getdate()
			   ,[exported_by] = 1001
			   ,[export_type] = 'Agresso'
			   ,[tenant_id] = @tenant_id
			   ,[is_delta] = 0
			   ,[budget_Year] = @budget_year

	SET @export_id = (SELECT SCOPE_IDENTITY())
	END

	IF @replace_existing_batch = 1
	BEGIN
		UPDATE [dbo].[tfp_erp_exports] SET [date] = getdate(), exported_by = 1001 WHERE tenant_id = @tenant_id and [id] = @export_id
	END

	DROP TABLE IF EXISTS #export_dump_agresso
	CREATE TABLE #export_dump_agresso(
	[export_id] [bigint] NOT NULL,
	[fk_action_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[description] [nvarchar](700) NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[fk_adjustment_code] [nvarchar](25) NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[jan] [decimal](18, 2) NOT NULL,
	[feb] [decimal](18, 2) NOT NULL,
	[mar] [decimal](18, 2) NOT NULL,
	[apr] [decimal](18, 2) NOT NULL,
	[may] [decimal](18, 2) NOT NULL,
	[jun] [decimal](18, 2) NOT NULL,
	[jul] [decimal](18, 2) NOT NULL,
	[aug] [decimal](18, 2) NOT NULL,
	[sep] [decimal](18, 2) NOT NULL,
	[oct] [decimal](18, 2) NOT NULL,
	[nov] [decimal](18, 2) NOT NULL,
	[dec] [decimal](18, 2) NOT NULL)

	INSERT INTO #export_dump_agresso
	EXEC [dbo].[proc_bud_agresso_original]
	@tenant_id = @tenant_id,
	@budget_year = @budget_year,
	@export_id = @export_id

	--Insert into erp log for logging purposes
	IF @replace_existing_batch = 1
	BEGIN
		DELETE FROM [dbo].[tfp_erp_export_log_agresso]
		WHERE fk_tenant_id = @tenant_id
		AND export_id = @export_id
	END

	INSERT INTO [dbo].[tfp_erp_export_log_agresso]
           ([export_id],[fk_tenant_id],[budget_year],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
           ,[jan],[feb],[mar],[apr],[may],[jun],[jul],[aug],[sep],[oct],[nov],[dec],[fk_action_id],[description],[fk_adjustment_code],[fk_alter_code])
	SELECT [export_id],[fk_tenant_id],[budget_year],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
           ,[jan],[feb],[mar],[apr],[may],[jun],[jul],[aug],[sep],[oct],[nov],[dec],[fk_action_id],[description],[fk_adjustment_code],[fk_alter_code]
	FROM #export_dump_agresso
	
	IF @replace_existing_batch = 1
	BEGIN
		DELETE FROM [dbo].[integ_agrpostback_batches]
		WHERE fk_tenant_id = @tenant_id
		AND [Batchid] = @batch_id
	END

	INSERT INTO [dbo].[integ_agrpostback_batches]
           ([Batchid]
           ,[fk_tenant_id]
           ,[IsOrig]
           ,[transferred]
           ,[updated]
           ,[budget_year]
           ,[error_fixed]
           ,[export_type]
           ,[response_message]
		   ,[control_batch])
	SELECT [Batchid] = @batch_id
           ,[fk_tenant_id] = @tenant_id
           ,[IsOrig] = 1
           ,[transferred] = 0
           ,[updated] = getdate()
           ,[budget_year] = @budget_year
           ,[error_fixed] = 0
           ,[export_type] = 1
           ,[response_message] = 'Generated by orig bud control SP'
		   ,control_batch = 1


	DROP TABLE IF EXISTS #period_list
	CREATE TABLE #period_list (period_name NVARCHAR(10), period_num NVARCHAR(10))
	INSERT INTO #period_list (period_name,period_num) 
	VALUES
	('jan','01'),
	('feb','02'),
	('mar','03'),
	('apr','04'),
	('may','05'),
	('jun','06'),
	('jul','07'),
	('aug','08'),
	('sep','09'),
	('oct','10'),
	('nov','11'),
	('dec','12')

	IF @replace_existing_batch = 1
	BEGIN
		DELETE FROM [dbo].[integ_agrpostback_origbud]
		WHERE fk_tenant_id = @tenant_id
		AND [Batchid] = @batch_id
		AND export_id = @export_id
	END

	INSERT INTO [dbo].[integ_agrpostback_origbud]
           ([fk_action_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_adjustment_code]
           ,[fk_alter_code]
           ,[amount]
           ,[period]
           ,[description]
           ,[fk_tenant_id]
           ,[export_id]
           ,[Batchid])
	select	[fk_action_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_adjustment_code]
           ,[fk_alter_code]
           ,[amount]
           ,[period] = CONVERT(NVARCHAR(20),@budget_year)+period_num
           ,[description] = LEFT([description],250)
           ,[fk_tenant_id]
           ,[export_id]
		   ,[Batchid] = @batch_id
	FROM
		(SELECT 
			[fk_action_id]
           ,[fk_account_code]
           ,[department_code] = fk_department_code
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_adjustment_code]
           ,[fk_alter_code]
           ,[description]
           ,[fk_tenant_id]
           ,[export_id] 
		   ,jan,feb,mar,apr,may,jun,jul,aug,sep,oct,nov,dec
		from #export_dump_agresso) p
	UNPIVOT
		(amount FOR period IN
			(jan,feb,mar,apr,may,jun,jul,aug,sep,oct,nov,dec)
	) AS unpvt
	JOIN #period_list pl ON unpvt.period = pl.period_name

	--Fetch grouped dimensions to be used in further controls
	INSERT INTO #temp_budget_dimensions
	([fk_tenant_id],export_id,batch_id,[budget_year],[budget_type],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code]
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,[amount])
	select [fk_tenant_id]
	,export_id
	,batch_id = @batch_id
	,[budget_year]
	,[budget_type] = b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
	,amount = SUM([jan]+[feb]+[mar]+[apr]+[may]+[jun]+[jul]+[aug]+[sep]+[oct]+[nov]+[dec])
	FROM #export_dump_agresso a
	JOIN #account_list_1 b on a.fk_tenant_id = b.pk_tenant_id  AND a.fk_account_code = b.pk_account_code
	GROUP BY [fk_tenant_id],export_id,[budget_year]
	,b.[type]
	,[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]

END

/* DIMENSION CHECKS START - account check not needed because it's checked for in the SP that generates the data to send  */
DROP TABLE IF EXISTS #error_list
CREATE TABLE #error_list
(fk_tenant_id INT,
export_id INT,
batch_id VARCHAR(14),
error_type NVARCHAR(100),
dim_1_type NVARCHAR(50),
dim_2_type NVARCHAR(50),
dim_1_id NVARCHAR(50),
dim_2_id NVARCHAR(50),	
dim_to_change NVARCHAR(50),
[id_new] NVARCHAR(50))

INSERT INTO #error_list
SELECT 
fk_tenant_id
,export_id
,batch_id
,error_type
,dim_1_type
,dim_2_type
,dim_1_id
,dim_2_id	
,dim_to_change
,id_new = ''
FROM
(

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'fk_account_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_account_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_account_code'
	from #temp_budget_dimensions a
	JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code and a.budget_year BETWEEN DATEPART(YEAR,dateFrom) AND DATEPART(YEAR,dateTo) and b.isActive = 0
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_account_code

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'fk_department_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_department_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_department_code'
	from #temp_budget_dimensions a
	LEFT JOIN tco_departments b on a.fk_tenant_id = b.fk_tenant_id and a.fk_department_code = b.pk_department_code and a.budget_year BETWEEN b.year_from and b.year_to
	where b.pk_department_code IS NULL
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_department_code

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'fk_department_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_department_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_department_code'
	from #temp_budget_dimensions a
	JOIN tco_departments b on a.fk_tenant_id = b.fk_tenant_id and a.fk_department_code = b.pk_department_code and a.budget_year BETWEEN b.year_from and b.year_to AND b.[status] = 0
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_department_code

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'fk_function_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_function_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_function_code'
	from #temp_budget_dimensions a
	LEFT JOIN tco_functions b on a.fk_tenant_id = b.pk_tenant_id and a.fk_function_code = b.pk_Function_code and a.budget_year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)
	where b.pk_Function_code IS NULL
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_function_code
	
	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'fk_function_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_function_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_function_code'
	from #temp_budget_dimensions a
	JOIN tco_functions b on a.fk_tenant_id = b.pk_tenant_id and a.fk_function_code = b.pk_Function_code and a.budget_year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo) AND b.isActive = 0
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_function_code
	
	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'fk_project_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_project_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_project_code'
	from #temp_budget_dimensions a
	LEFT JOIN tco_projects b on a.fk_tenant_id = b.fk_tenant_id and a.fk_project_code = b.pk_project_code and a.budget_year BETWEEN DATEPART(YEAR,b.date_from) AND DATEPART(YEAR,b.date_to)
	where b.pk_project_code IS NULL
	and a.fk_project_code != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_project_code
	
	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'fk_project_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_project_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_project_code'
	from #temp_budget_dimensions a
	JOIN tco_projects b on a.fk_tenant_id = b.fk_tenant_id and a.fk_project_code = b.pk_project_code and a.budget_year BETWEEN DATEPART(YEAR,b.date_from) AND DATEPART(YEAR,b.date_to) AND b.active = 0
	where a.fk_project_code != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_project_code
	
	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_TEMPORARY_CONTROL_SP'
	,dim_1_type = 'fk_project_code'
	,dim_2_type = ''
	,dim_1_id = a.fk_project_code 
	,dim_2_id = ''
	,dim_to_change = 'fk_project_code'
	from #temp_budget_dimensions a
	JOIN tco_projects b on a.fk_tenant_id = b.fk_tenant_id and a.fk_project_code = b.pk_project_code and a.budget_year BETWEEN DATEPART(YEAR,b.date_from) AND DATEPART(YEAR,b.date_to) and b.is_temp = 1
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.fk_project_code
	

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'free_dim_1'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_1 
	,dim_2_id = ''
	,dim_to_change = 'free_dim_1'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_1'
	LEFT JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_1 = b.free_dim_code and b.free_dim_column = 'free_dim_1'
	where b.free_dim_code IS NULL
	and a.free_dim_1 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_1
	
	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'free_dim_1'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_1 
	,dim_2_id = ''
	,dim_to_change = 'free_dim_1'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_1'
	JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_1 = b.free_dim_code and b.free_dim_column = 'free_dim_1' AND b.status = 0
	where a.free_dim_1 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_1
	
	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'free_dim_2'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_2 
	,dim_2_id = ''
	,dim_to_change = 'free_dim_2'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_2'
	LEFT JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_2 = b.free_dim_code and b.free_dim_column = 'free_dim_2'
	where b.free_dim_code IS NULL
	and a.free_dim_2 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_2

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'free_dim_2'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_2 
	,dim_2_id = ''
	,dim_to_change = 'free_dim_2'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_2'
	JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_2 = b.free_dim_code and b.free_dim_column = 'free_dim_2' AND b.status = 0
	where a.free_dim_2 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_2

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'free_dim_3'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_3
	,dim_2_id = ''
	,dim_to_change = 'free_dim_3'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_3'
	LEFT JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_3 = b.free_dim_code and b.free_dim_column = 'free_dim_3'
	where b.free_dim_code IS NULL
	and a.free_dim_3 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_3

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'free_dim_3'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_3
	,dim_2_id = ''
	,dim_to_change = 'free_dim_3'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_3'
	JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_3 = b.free_dim_code and b.free_dim_column = 'free_dim_3' AND b.status = 0
	where a.free_dim_3 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_3

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_INVALID_CONTROL_SP'
	,dim_1_type = 'free_dim_4'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_4
	,dim_2_id = ''
	,dim_to_change = 'free_dim_4'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_4'
	LEFT JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_4 = b.free_dim_code and b.free_dim_column = 'free_dim_4'
	where b.free_dim_code IS NULL
	and a.free_dim_4 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_4

	UNION ALL

	select a.fk_tenant_id,
	export_id,batch_id,
	error_type = 'DIM_NOT_ACTIVE_CONTROL_SP'
	,dim_1_type = 'free_dim_4'
	,dim_2_type = ''
	,dim_1_id = a.free_dim_4
	,dim_2_id = ''
	,dim_to_change = 'free_dim_4'
	from #temp_budget_dimensions a
	JOIN tmd_free_dim_definition fdd ON a.fk_tenant_id = fdd.fk_tenant_id and fdd.free_dim_column = 'free_dim_4'
	JOIN tco_free_dim_values b on a.fk_tenant_id = b.fk_tenant_id and a.free_dim_4 = b.free_dim_code and b.free_dim_column = 'free_dim_4' AND b.status = 0
	where a.free_dim_4 != ''
	GROUP BY a.fk_tenant_id,export_id,batch_id,a.free_dim_4

) error

--Check for 0 amount to be transferred

IF (SELECT SUM(amount) FROM #temp_budget_dimensions) != 0
BEGIN

	INSERT INTO #error_list
	SELECT 
	fk_tenant_id = @tenant_id
	,export_id = @export_id
	,batch_id = @batch_id
	,error_type = 'BUDGET_UNBALANCED_CONTROL_SP'
	,dim_1_type = ''
	,dim_2_type = ''
	,dim_1_id = ''
	,dim_2_id = ''	
	,dim_to_change = ''
	,id_new = ''

END

--Section for relation errors starts
DROP TABLE IF EXISTS #column_names
CREATE TABLE #column_names (relation_desc NVARCHAR(50), column_name NVARCHAR(50))
INSERT INTO #column_names (relation_desc,column_name) VALUES
('PROJECTS','fk_project_code'),
('DEPARTMENTS','fk_department_code'),
('ACCOUNTS','fk_account_code'),
('FUNCTIONS','fk_function_code'),
('PROJECT','fk_project_code'),
('DEPARTMENT','fk_department_code'),
('ACCOUNT','fk_account_code'),
('FUNCTION','fk_function_code')

DROP TABLE IF EXISTS #rules_list
select
row_id = row_number()  OVER(order by rv.pk_id)
,rv.fk_tenant_id
,rule_type = def.attribute_type+'-'+def.relation_type
,attribute_column = att.column_name 
,rv.attribute_value
,relation_column = rel.column_name
,rv.relation_value_from
,rv.relation_value_to
INTO #rules_list
FROM
(
select fk_tenant_id,attribute_type,relation_type
from tco_relation_definition
where fk_tenant_id = @tenant_id
AND flag = 1
AND rule_type = 0
GROUP BY fk_tenant_id,attribute_type,relation_type
) def
JOIN tco_relation_values rv ON def.fk_tenant_id = rv.fk_tenant_id and def.attribute_type = rv.attribute_type and def.relation_type = rv.relation_type
JOIN #column_names att ON rv.attribute_type = att.relation_desc
JOIN #column_names rel ON rv.relation_type = rel.relation_desc



DROP TABLE IF EXISTS #temp_relation_errors
CREATE TABLE #temp_relation_errors(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[export_id] [bigint] NOT NULL,
	[batch_id] [nvarchar](14) NOT NULL,
	[budget_type] [nvarchar](25) NOT NULL,
	[rule_type] [nvarchar](100) NOT NULL,
	[attribute_column] [nvarchar](100) NOT NULL,
	[relation_column] [nvarchar](100) NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL)


DECLARE @CursorID INT = 1;
DECLARE @RowCnt BIGINT = 0;
DECLARE @attribute_column NVARCHAR(50);
DECLARE @attribute_value NVARCHAR(50);
DECLARE @relation_column NVARCHAR(50);
DECLARE @relation_value_from NVARCHAR(50);
DECLARE @relation_value_to NVARCHAR(50);
DECLARE @rule_type NVARCHAR(100);
DECLARE @update_string NVARCHAR(MAX)

SELECT @RowCnt = COUNT(0) FROM #rules_list

--First fetch all rows that does not correspond to each individual rule
WHILE @CursorID <= @RowCnt
BEGIN
	SET @attribute_column =		(select attribute_column	from #rules_list where row_id = @CursorID)
	SET @attribute_value =		(select attribute_value		from #rules_list where row_id = @CursorID)
	SET @relation_column =		(select relation_column		from #rules_list where row_id = @CursorID)
	SET @relation_value_from =	(select relation_value_from	from #rules_list where row_id = @CursorID)
	SET @relation_value_to =	(select relation_value_to	from #rules_list where row_id = @CursorID)
	SET @rule_type =			(select rule_type			from #rules_list where row_id = @CursorID)

	SET @update_string =	'INSERT INTO #temp_relation_errors
							select 
							[fk_tenant_id],
							[budget_year],
							[export_id],
							[batch_id],
							[budget_type],
							rule_type = '''+@rule_type +''' ,
							attribute_column = '''+@attribute_column +''' ,
							relation_column = '''+@relation_column +''' ,
							[fk_account_code],
							[fk_department_code],
							[fk_function_code],
							[fk_project_code]
							from #temp_budget_dimensions where '+ @attribute_column + '= '''+ @attribute_value +''' 
							AND ' + @relation_column + ' NOT BETWEEN '''+ @relation_value_from + ''' AND ''' + @relation_value_to +''';'
	
	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END


--Then delete from the error list to cover scenario where there's multiple rules for a given dimension and rule type
SET @CursorID = 1 
WHILE @CursorID <= @RowCnt
BEGIN

	SET @attribute_column =		(select attribute_column	from #rules_list where row_id = @CursorID)
	SET @attribute_value =		(select attribute_value		from #rules_list where row_id = @CursorID)
	SET @relation_column =		(select relation_column		from #rules_list where row_id = @CursorID)
	SET @relation_value_from =	(select relation_value_from	from #rules_list where row_id = @CursorID)
	SET @relation_value_to =	(select relation_value_to	from #rules_list where row_id = @CursorID)
	SET @rule_type =			(select rule_type			from #rules_list where row_id = @CursorID)

	SET @update_string =	'DELETE #temp_relation_errors
							WHERE rule_type = '''+ @rule_type +''' 
							AND ' + @attribute_column + '= '''+ @attribute_value +''' 
							AND ' + @relation_column + ' BETWEEN '''+ @relation_value_from + ''' AND ''' + @relation_value_to +''';'
	
	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END



--Finally group the errors
DROP TABLE IF EXISTS #relation_errors_groups
select row_id = row_number()  OVER(order by rule_type)
,rule_type, attribute_column,relation_column
INTO #relation_errors_groups
from #temp_relation_errors
GROUP BY rule_type, attribute_column,relation_column


SET @CursorID = 1 
SELECT @RowCnt = COUNT(0) FROM #relation_errors_groups

WHILE @CursorID <= @RowCnt
BEGIN

	SET @attribute_column =		(select attribute_column	from #rules_list where row_id = @CursorID)
	SET @relation_column =		(select relation_column		from #rules_list where row_id = @CursorID)
	SET @rule_type =			(select rule_type			from #rules_list where row_id = @CursorID)

	SET @update_string =	'	INSERT INTO #error_list
								SELECT 
								fk_tenant_id
								,export_id
								,batch_id
								,error_type = ''RELATION_INVALID_CONTROL_SP''
								,dim_1_type = ''' + @attribute_column + '''
								,dim_2_type = ''' + @relation_column + '''
								,dim_1_id = '+ @attribute_column + '
								,dim_2_id = ' + @relation_column + '
								,dim_to_change = ''' + @relation_column + '''
								,id_new = ''''
								from #temp_relation_errors
								WHERE rule_type = ''' + @rule_type + '''
								GROUP BY fk_tenant_id,export_id,batch_id,budget_type, '+@attribute_column+','+@relation_column+';'
	
	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END

--Begin control of defaults

DROP TABLE IF EXISTS #rules_list_defaults
select
row_id = row_number()  OVER(order by rv.pk_id)
,rv.fk_tenant_id
,rule_type = def.attribute_type+'-'+def.relation_type
,attribute_column = att.column_name 
,rv.attribute_value
,relation_column = rel.column_name
,rv.relation_value_from
,rv.relation_value_to
INTO #rules_list_defaults
FROM
(
select fk_tenant_id,attribute_type,relation_type
from tco_relation_definition
where fk_tenant_id = @tenant_id
AND flag = 1
AND rule_type = 1
GROUP BY fk_tenant_id,attribute_type,relation_type
) def
JOIN tco_relation_values rv ON def.fk_tenant_id = rv.fk_tenant_id and def.attribute_type = rv.attribute_type and def.relation_type = rv.relation_type
JOIN #column_names att ON rv.attribute_type = att.relation_desc
JOIN #column_names rel ON rv.relation_type = rel.relation_desc



DROP TABLE IF EXISTS #temp_default_errors
CREATE TABLE #temp_default_errors(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[export_id] [bigint] NOT NULL,
	[batch_id] [nvarchar](14) NOT NULL,
	[budget_type] [nvarchar](25) NOT NULL,
	[rule_type] [nvarchar](100) NOT NULL,
	[attribute_column] [nvarchar](100) NOT NULL,
	[default_value] [nvarchar](100) NOT NULL,
	[relation_column] [nvarchar](100) NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL)


SET @CursorID = 1;
SET @RowCnt = 0;
SET @attribute_column = '';
SET @attribute_value = '';
SET @relation_column = '';
SET @relation_value_from = '';
SET @relation_value_to = '';
SET @rule_type ='';
SET @update_string = '';

SELECT @RowCnt = COUNT(0) FROM #rules_list_defaults

--First fetch all rows that does not correspond to each individual rule
WHILE @CursorID <= @RowCnt
BEGIN
	SET @attribute_column =		(select attribute_column	from #rules_list_defaults where row_id = @CursorID)
	SET @attribute_value =		(select attribute_value		from #rules_list_defaults where row_id = @CursorID)
	SET @relation_column =		(select relation_column		from #rules_list_defaults where row_id = @CursorID)
	SET @relation_value_from =	(select relation_value_from	from #rules_list_defaults where row_id = @CursorID)
	SET @relation_value_to =	(select relation_value_to	from #rules_list_defaults where row_id = @CursorID)
	SET @rule_type =			(select rule_type			from #rules_list_defaults where row_id = @CursorID)

	SET @update_string =	'INSERT INTO #temp_default_errors
							select 
							[fk_tenant_id],
							[budget_year],
							[export_id],
							[batch_id],
							[budget_type],
							rule_type = '''+@rule_type +''' ,
							attribute_column = '''+@attribute_column +''' ,
							default_value = '''+@attribute_value +''' ,
							relation_column = '''+@relation_column +''' ,
							[fk_account_code],
							[fk_department_code],
							[fk_function_code],
							[fk_project_code],
							[free_dim_1],
							[free_dim_2],
							[free_dim_3],
							[free_dim_4]
							from #temp_budget_dimensions WHERE ' + @relation_column + ' BETWEEN '''+ @relation_value_from + ''' AND ''' + @relation_value_to +'''
							AND '+ @attribute_column + ' = '''' ;'
	
	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END


--Group default errors
DROP TABLE IF EXISTS #defaults_errors_groups
select row_id = row_number()  OVER(order by rule_type)
,rule_type, attribute_column,relation_column
INTO #defaults_errors_groups
from #temp_default_errors
GROUP BY rule_type, attribute_column,relation_column


SET @CursorID = 1 
SELECT @RowCnt = COUNT(0) FROM #defaults_errors_groups

WHILE @CursorID <= @RowCnt
BEGIN

	SET @attribute_column =		(select attribute_column	from #rules_list_defaults where row_id = @CursorID)
	SET @relation_column =		(select relation_column		from #rules_list_defaults where row_id = @CursorID)
	SET @rule_type =			(select rule_type			from #rules_list_defaults where row_id = @CursorID)

	SET @update_string =	'	INSERT INTO #error_list
								SELECT 
								fk_tenant_id
								,export_id
								,batch_id
								,error_type = ''DEFAULT_REQUIRED_CONTROL_SP''
								,dim_1_type = ''' + @relation_column + '''
								,dim_2_type = ''' + @attribute_column + '''
								,dim_1_id = '+ @relation_column + '
								,dim_2_id = ' + @attribute_column + '
								,dim_to_change = ''' + @attribute_column + '''
								,id_new = default_value
								from #temp_default_errors
								WHERE rule_type = ''' + @rule_type + '''
								GROUP BY fk_tenant_id,export_id,batch_id,budget_type,default_value, '+@attribute_column+','+@relation_column+';'
	
	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END


--Insert errors if any
IF @replace_existing_batch = 1
BEGIN
	DELETE FROM [dbo].[tbu_integ_error_handling]
	WHERE fk_tenant_id = @tenant_id
	and export_id = @export_id
	and batch_id = @batch_id
END

INSERT INTO [dbo].[tbu_integ_error_handling]
           ([fk_tenant_id]
           ,[export_id]
           ,[batch_id]
           ,[fk_error_type]
           ,[dim_1_type]
           ,[dim_2_type]
           ,[dim_1_id]
           ,[dim_2_id]
           ,[dim_to_change]
           ,[id_new]
           ,[keep]
           ,[fixed])
SELECT		[fk_tenant_id]
           ,[export_id]
           ,[batch_id]
           ,[fk_error_type] = error_type
           ,[dim_1_type]
           ,[dim_2_type]
           ,[dim_1_id]
           ,[dim_2_id]
           ,[dim_to_change]
           ,[id_new]
           ,[keep] = 0
           ,[fixed] = 0
FROM #error_list


DECLARE @error_count INT
SET @error_count = (select COUNT(*)from #error_list)
select
fk_tenant_id = @tenant_id
,export_id = @export_id
,batch_id = @batch_id
,erp_type = @erp_type
,error_count = @error_count
GO

