
CREATE OR ALTER PROCEDURE [dbo].[prcUpdateTcoManYears]
		
@tenant_id INT,
@budget_year INT,
@user_id INT
WITH RECOMPILE
AS



DECLARE @this_year INT = @budget_year
DECLARE @prev_year INT = @budget_year - 1


SELECT 
NEWID() as pk_id
,a.fk_tenant_id
,budget_year
,fk_employee_id
,(@budget_year - DATEPART(year, b.birth_date)) as age
,fk_department_code
,fk_function_code
,position_percentage
,presence_percentage
,fk_position_code
,position_type_value
,position_type_name
,convert(date,position_start_date,103) as position_start_date
,convert(date,position_end_date,103) as position_end_date
,res_name=''
INTO #position_details_1  
FROM  tco_position_details a
LEFT JOIN tco_employee_data b ON a.fk_employee_id = b.pk_employee_id AND a.fk_tenant_id = b.fk_tenant_id
WHERE a.fk_tenant_id = @tenant_id  AND budget_year IN (@prev_year, @this_year)


UPDATE #position_details_1 SET position_end_date = '2999-12-31' 
WHERE position_end_date = '' OR position_end_date is null OR position_end_date = '0001-01-01'


SELECT 
a.pk_id
,A.fk_employee_id
,A.position_start_date
,A.position_end_date
,A.presence_percentage
,A.TheYear
,A.TheMonth
,A.possible_working_days
,B.max_day_month
,(A.possible_working_days * CONVERT(dec(18,2), presence_percentage) / 100) / CONVERT(dec(18,2),B.max_day_month) as Månedsverk
INTO #position_details_2  
FROM
(SELECT 
a.pk_id
,a.fk_employee_id
,a.position_start_date
,a.position_end_date
,a.presence_percentage
,b.TheYear
,b.TheMonth
,COUNT(*) AS possible_working_days 
FROM #position_details_1 a
LEFT JOIN gmd_date_table b ON b.TheDate >= a.position_start_date AND b.TheDate <= a.position_end_date
WHERE a.fk_tenant_id = @tenant_id
AND budget_year = @budget_year
GROUP BY 
a.pk_id
,a.fk_employee_id
,a.position_start_date
,a.position_end_date
,a.presence_percentage
,b.TheYear
,b.TheMonth) A
JOIN (select TheYear, TheMonth, MAX(TheDay) AS max_day_month 
FROM gmd_date_table
GROUP BY TheYear, TheMonth) B
ON A.TheYear = B.TheYear AND A.TheMonth = B.TheMonth 
WHERE A.TheYear = @budget_year
ORDER BY
A.fk_employee_id
,A.position_start_date
,A.position_end_date
,A.TheYear
,A.TheMonth


SELECT 
pk_id = a.pk_id
,a.fk_tenant_id
,@budget_year as budget_year
,a.fk_employee_id
,(@budget_year - DATEPART(year, b.birth_date)) as age
,fk_department_code
,fk_function_code
,position_percentage
,a.presence_percentage
,fk_position_code
,position_type_value
,position_type_name
,a.position_start_date
,a.position_end_date
,0 as month_start
,0 as month_end
,convert(dec(18,2),0) as jan
,convert(dec(18,2),0) as feb
,convert(dec(18,2),0) as march
,convert(dec(18,2),0) as apr
,convert(dec(18,2),0) as may
,convert(dec(18,2),0) as june
,convert(dec(18,2),0) as july
,convert(dec(18,2),0) as aug
,convert(dec(18,2),0) as sep
,convert(dec(18,2),0) as oct
,convert(dec(18,2),0) as nov
,convert(dec(18,2),0) as dec
,convert(dec(18,2),0) as total
,a.res_name
INTO #hlptab_actual
FROM #position_details_1 a
LEFT JOIN tco_employee_data b ON a.fk_employee_id = b.pk_employee_id AND a.fk_tenant_id = b.fk_tenant_id
WHERE @this_year <= DATEPART (year, a.position_end_date) AND @this_year >= DATEPART (YEAR, a.position_start_date)
AND budget_year = @this_year
GROUP BY a.pk_id, a.fk_tenant_id, budget_year, a.fk_employee_id, b.birth_date, age, fk_department_code, fk_function_code, position_percentage, a.presence_percentage, fk_position_code, position_type_value, position_type_name, a.position_start_date, a.position_end_date, res_name


UPDATE #hlptab_actual SET month_start = 1 WHERE DATEPART (YEAR, position_start_date)  < @this_year
UPDATE #hlptab_actual SET month_start = DATEPART(MONTH, position_start_date)  WHERE DATEPART (YEAR, position_start_date)  = @this_year

UPDATE #hlptab_actual SET month_end = 12 WHERE DATEPART (YEAR, position_end_date)  > @this_year
UPDATE #hlptab_actual SET month_end = DATEPART(MONTH, position_end_date)  WHERE DATEPART (YEAR, position_end_date)  = @this_year


UPDATE #hlptab_actual SET jan = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 1

UPDATE #hlptab_actual SET feb = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 2

UPDATE #hlptab_actual SET march = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 3

UPDATE #hlptab_actual SET apr = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 4

UPDATE #hlptab_actual SET may = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 5

UPDATE #hlptab_actual SET june = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 6

UPDATE #hlptab_actual SET july = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 7

UPDATE #hlptab_actual SET aug = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 8

UPDATE #hlptab_actual SET sep = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 9

UPDATE #hlptab_actual SET oct = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 10

UPDATE #hlptab_actual SET nov = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 11

UPDATE #hlptab_actual SET dec = b.Månedsverk
FROM #hlptab_actual a
JOIN #position_details_2 b ON a.pk_id = b.pk_id AND a.fk_employee_id = b.fk_employee_id
WHERE b.TheMonth = 12

UPDATE #hlptab_actual SET total = (jan+feb+march+apr+may+june+july+aug+sep+oct+nov+dec)/12


SELECT
a.[fk_tenant_id]
,[budget_year]
,[fk_res_id] AS [fk_employee_id]
,[age] = CASE WHEN b.birth_date IS NULL THEN -1
		 ELSE (@budget_year - DATEPART(year, b.birth_date))
		 END
,[fk_department_code]
,[fk_function_code]
,[position_pct] AS [position_percentage]
,'' AS [presence_percentage]
,[start_period] AS [month_start]
,[end_period] AS [month_end]
,[fk_position_id] AS [fk_position_code]
,CONVERT(VARCHAR(25), [fk_emp_type_id]) AS [position_type_value] 
,c.[description] AS [position_type_name]
,convert(dec(18,2),0) as [jan]
,convert(dec(18,2),0) as [feb]
,convert(dec(18,2),0) as [march]
,convert(dec(18,2),0) as [apr]
,convert(dec(18,2),0) as [may]
,convert(dec(18,2),0) as [june]
,convert(dec(18,2),0) as [july]
,convert(dec(18,2),0) as [aug]
,convert(dec(18,2),0) as [sep]
,convert(dec(18,2),0) as [oct]
,convert(dec(18,2),0) as [nov]
,convert(dec(18,2),0) as [dec]
,convert(dec(18,2),0) as [total]
,a.res_name
INTO #hlptab_tbuemp_org
FROM tbu_employments_original a
LEFT JOIN tco_resources b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_res_id = b.pk_res_id
JOIN gmd_emp_type c ON a.fk_emp_type_id = c.pk_emp_type_id
WHERE a.fk_tenant_id = @tenant_id  AND a.budget_year = @this_year


UPDATE #hlptab_tbuemp_org SET jan = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 1 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET feb = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 2 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET march = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 3 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET apr = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 4 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET may = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 5 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET june = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 6 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET july = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 7 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET aug = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 8 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET sep = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 9 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET oct = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 10 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET nov = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 11 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET dec = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 12 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_org SET total = (jan+feb+march+apr+may+june+july+aug+sep+oct+nov+dec)/12


SELECT
a.[fk_tenant_id]
,[budget_year]
,[fk_res_id] AS [fk_employee_id]
,[age] = CASE WHEN b.birth_date IS NULL THEN -1
		 ELSE (@budget_year - DATEPART(year, b.birth_date))
		 END
,[fk_department_code]
,[fk_function_code]
,[position_pct] AS [position_percentage]
,'' AS [presence_percentage]
,[start_period] AS [month_start]
,[end_period] AS [month_end]
,[fk_position_id] AS [fk_position_code]
,CONVERT(VARCHAR(25), [fk_emp_type_id]) AS [position_type_value] 
,c.[description] AS [position_type_name]
,convert(dec(18,2),0) as [jan]
,convert(dec(18,2),0) as [feb]
,convert(dec(18,2),0) as [march]
,convert(dec(18,2),0) as [apr]
,convert(dec(18,2),0) as [may]
,convert(dec(18,2),0) as [june]
,convert(dec(18,2),0) as [july]
,convert(dec(18,2),0) as [aug]
,convert(dec(18,2),0) as [sep]
,convert(dec(18,2),0) as [oct]
,convert(dec(18,2),0) as [nov]
,convert(dec(18,2),0) as [dec]
,convert(dec(18,2),0) as [total]
,a.res_name
INTO #hlptab_tbuemp_rev
FROM tbu_employments a
LEFT JOIN tco_resources b ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_res_id = b.pk_res_id
JOIN gmd_emp_type c ON a.fk_emp_type_id = c.pk_emp_type_id
WHERE a.fk_tenant_id = @tenant_id  AND a.budget_year = @this_year AND a.delete_flag = 0


UPDATE #hlptab_tbuemp_rev SET jan = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 1 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET feb = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 2 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET march = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 3 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET apr = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 4 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET may = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 5 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET june = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 6 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET july = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 7 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET aug = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 8 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET sep = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 9 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET oct = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 10 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET nov = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 11 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET dec = (1 * CONVERT(decimal, position_percentage) / 100) WHERE 12 BETWEEN month_start AND month_end
UPDATE #hlptab_tbuemp_rev SET total = (jan+feb+march+apr+may+june+july+aug+sep+oct+nov+dec)/12



delete from [tco_man_years] where fk_tenant_id = @tenant_id AND budget_year = @budget_year


INSERT INTO [tco_man_years]
([fk_tenant_id]
,[budget_year]
,[fk_employee_id]
,[age]
,[fk_department_code]
,[fk_function_code]
,[position_percentage]
,[month_start]
,[month_end]
,[fk_position_code]
,[position_type_value]
,[position_type_name]
,[jan]
,[feb]
,[march]
,[apr]
,[may]
,[june]
,[july]
,[aug]
,[sep]
,[oct]
,[nov]
,[dec]
,[total]
,[updated]
,[updated_by]
,[budget_type]
,[presence_percentage]
,[res_name])

SELECT 
[fk_tenant_id]
,[budget_year]
,[fk_employee_id]
,[age]
,[fk_department_code]
,[fk_function_code]
,[position_percentage]
,[month_start]
,[month_end]
,[fk_position_code]
,[position_type_value] = CONVERT(VARCHAR(25), [position_type_value])
,[position_type_name] = CONVERT(VARCHAR(25), [position_type_name])
,[jan]
,[feb]
,[march]
,[apr]
,[may]
,[june]
,[july]
,[aug]
,[sep]
,[oct]
,[nov]
,[dec]
,[total]
,GETDATE() AS [updated]
,@user_id AS [updated_by]
,[budget_type] = 0
,presence_percentage
,res_name
FROM #hlptab_actual

UNION ALL 

SELECT 
[fk_tenant_id]
,[budget_year]
,[fk_employee_id]
,[age]
,[fk_department_code]
,[fk_function_code]
,[position_percentage]
,[month_start]
,[month_end]
,[fk_position_code]
,[position_type_value] = CONVERT(VARCHAR(25), [position_type_value])
,[position_type_name] = CONVERT(VARCHAR(25), [position_type_name])
,[jan]
,[feb]
,[march]
,[apr]
,[may]
,[june]
,[july]
,[aug]
,[sep]
,[oct]
,[nov]
,[dec]
,[total]
,GETDATE() AS [updated]
,@user_id AS [updated_by]
,[budget_type] = 1
,[presence_percentage]
,res_name
FROM #hlptab_tbuemp_org

UNION ALL 

SELECT 
[fk_tenant_id]
,[budget_year]
,[fk_employee_id]
,[age]
,[fk_department_code]
,[fk_function_code]
,[position_percentage]
,[month_start]
,[month_end]
,[fk_position_code]
,[position_type_value] = CONVERT(VARCHAR(25), [position_type_value])
,[position_type_name] = CONVERT(VARCHAR(25), [position_type_name])
,[jan]
,[feb]
,[march]
,[apr]
,[may]
,[june]
,[july]
,[aug]
,[sep]
,[oct]
,[nov]
,[dec]
,[total]
,GETDATE() AS [updated]
,@user_id AS [updated_by]
,[budget_type] = 2
,[presence_percentage]
,res_name
FROM #hlptab_tbuemp_rev


DROP TABLE IF EXISTS #position_details_1
DROP TABLE IF EXISTS #position_details_2
DROP TABLE IF EXISTS #hlptab_actual
DROP TABLE IF EXISTS #hlptab_tbuemp_org
DROP TABLE IF EXISTS #hlptab_tbuemp_rev
GO
