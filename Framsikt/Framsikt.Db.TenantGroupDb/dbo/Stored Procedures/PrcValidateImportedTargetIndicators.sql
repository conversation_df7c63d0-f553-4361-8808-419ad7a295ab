CREATE OR ALTER PROCEDURE [dbo].[PrcValidateImportedTargetIndicators]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@isServiceIdSetup bit,
	@serviceLevel int,
	@org_version NVARCHAR(50)

AS
		--Clear all the error information--
	UPDATE tco_stage_target_indicators_import
	SET 
	framsikt_reference_error = 0,
	external_reference_error = 0 ,
	measure_finance_plan_error = 0,
	measurment_criteria_error = 0,
	org_level_error = 0,
	org_level_str_error = 0,
	org_id_error = 0,
	service_id_error = 0,
	indicator_value_error = 0,
	status_desc_error = 0,
	error_count = 0
	WHERE tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
		tco_stage_target_indicators_import.budget_year = @budget_year AND 
		tco_stage_target_indicators_import.user_id = @user_id;

	--update org_level from org_level_str
	update tco_stage_target_indicators_import set org_level = org_level_str
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	TRY_PARSE(tco_stage_target_indicators_import.org_level_str as int) is not null;

	--Validate org_level_str
	update tco_stage_target_indicators_import set org_level_str_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	(
		(TRY_PARSE(tco_stage_target_indicators_import.org_level_str as int) is null)
	);

	--Validate OrgLevel
	update tco_stage_target_indicators_import set org_level_str_error = 1, 
													error_count = error_count + 1
	from tco_stage_target_indicators_import
	left outer join tco_org_level on 
								tco_stage_target_indicators_import.fk_tenant_id = tco_org_level.fk_tenant_id and
								tco_stage_target_indicators_import.org_level = tco_org_level.org_level  
	left join tco_org_version on
								tco_org_level.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_org_level.org_level is null and
	tco_stage_target_indicators_import.org_level_str_error = 0;

	--Validate OrgId from tco_org_hierarchy.org_id_1 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_target_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_target_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_target_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_target_indicators_import.org_id = tco_org_hierarchy.org_id_1 and 
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_level = 1 and
	tco_stage_target_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_1  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_2 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_target_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_target_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_target_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_target_indicators_import.org_id = tco_org_hierarchy.org_id_2 and 
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_level = 2 and
	tco_stage_target_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_2  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_3 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_target_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_target_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_target_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_target_indicators_import.org_id = tco_org_hierarchy.org_id_3 and 
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_level = 3 and
	tco_stage_target_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_3  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_4 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_target_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_target_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_target_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_target_indicators_import.org_id = tco_org_hierarchy.org_id_4 and 
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_level = 4 and
	tco_stage_target_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_4  is null;

	--Validate OrgId from tco_org_hierarchy.org_id_5 if org_level = 1, this should be validated only if there is no error in org_level, meaning org_level_error = 0
	update tco_stage_target_indicators_import set org_id_error = 1, 
													error_count = error_count + 1
	from tco_stage_target_indicators_import
	left outer join tco_org_hierarchy on 
										tco_stage_target_indicators_import.fk_tenant_id = tco_org_hierarchy.fk_tenant_id and
										tco_stage_target_indicators_import.org_id = tco_org_hierarchy.org_id_5 and 
										tco_org_hierarchy.fk_org_version = @org_version
	left join tco_org_version on
								tco_org_hierarchy.fk_tenant_id = tco_org_version.fk_tenant_id and tco_org_version.pk_org_version=@org_version
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_level = 5 and
	tco_stage_target_indicators_import.org_level_error = 0 and
	tco_org_hierarchy.org_id_5  is null;

	if(@isServiceIdSetup = 1)
	begin
		if(@serviceLevel = 1)
		begin
			--Validate service_id from tco_service_values.service_id_1 if @serviceLevel = 1
			update tco_stage_target_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_target_indicators_import
			left outer join tco_service_values on 
												tco_stage_target_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_target_indicators_import.service_id = tco_service_values.service_id_1
			where
			tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_target_indicators_import.budget_year = @budget_year AND 
			tco_stage_target_indicators_import.user_id = @user_id and
			tco_stage_target_indicators_import.service_id <> '' and
			tco_service_values.service_id_1  is null;
		end
		else if(@serviceLevel = 2)
		begin
			--Validate service_id from tco_service_values.service_id_2 if @serviceLevel = 2
			update tco_stage_target_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_target_indicators_import
			left outer join tco_service_values on 
												tco_stage_target_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_target_indicators_import.service_id = tco_service_values.service_id_2
			where
			tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_target_indicators_import.budget_year = @budget_year AND 
			tco_stage_target_indicators_import.user_id = @user_id and
			tco_stage_target_indicators_import.service_id <> '' and
			tco_service_values.service_id_2  is null;
		end
		else if(@serviceLevel = 3)
		begin
			--Validate service_id from tco_service_values.service_id_1 if @serviceLevel = 1
			update tco_stage_target_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_target_indicators_import
			left outer join tco_service_values on 
												tco_stage_target_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_target_indicators_import.service_id = tco_service_values.service_id_3
			where
			tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_target_indicators_import.budget_year = @budget_year AND 
			tco_stage_target_indicators_import.user_id = @user_id and
			tco_stage_target_indicators_import.service_id <> '' and
			tco_service_values.service_id_3  is null;
		end
		else if(@serviceLevel = 4)
		begin
			--Validate service_id from tco_service_values.service_id_4
			update tco_stage_target_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_target_indicators_import
			left outer join tco_service_values on 
												tco_stage_target_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_target_indicators_import.service_id = tco_service_values.service_id_4
			where
			tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_target_indicators_import.budget_year = @budget_year AND 
			tco_stage_target_indicators_import.user_id = @user_id and
			tco_stage_target_indicators_import.service_id <> '' and
			tco_service_values.service_id_4  is null;
		end
		else if(@serviceLevel = 5)
		begin
			--Validate service_id from tco_service_values.service_id_5
			update tco_stage_target_indicators_import set service_id_error = 1, 
															error_count = error_count + 1
			from tco_stage_target_indicators_import
			left outer join tco_service_values on 
												tco_stage_target_indicators_import.fk_tenant_id = tco_service_values.fk_tenant_id and
												tco_stage_target_indicators_import.service_id = tco_service_values.service_id_5
			where
			tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
			tco_stage_target_indicators_import.budget_year = @budget_year AND 
			tco_stage_target_indicators_import.user_id = @user_id and
			tco_stage_target_indicators_import.service_id <> '' and
			tco_service_values.service_id_5  is null;
		end
		
	end
	else
	begin
		update tco_stage_target_indicators_import set service_id = ''
		from tco_stage_target_indicators_import
		where
		tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
		tco_stage_target_indicators_import.budget_year = @budget_year AND 
		tco_stage_target_indicators_import.user_id = @user_id;
	end

	--Validate target_distribution_id is not null for org_id and org_level
	update tco_stage_target_indicators_import set org_id_error = 1,
													error_count = error_count + 1
    from tco_stage_target_indicators_import
	inner join tco_targets_distribution on tco_targets_distribution.fk_tenant_id = tco_stage_target_indicators_import.fk_tenant_id and
									tco_targets_distribution.org_id = tco_stage_target_indicators_import.org_id and
									tco_targets_distribution.org_level = tco_stage_target_indicators_import.org_level and
									tco_targets_distribution.service_id = tco_stage_target_indicators_import.service_id 
	inner join tco_targets on tco_targets.pk_target_id = tco_targets_distribution.fk_target_id and
								tco_targets.fk_tenant_id = tco_targets_distribution.fk_tenant_id	
    inner join tfp_effect_target_detail on tco_targets_distribution.fk_tenant_id = tfp_effect_target_detail.fk_tenant_id
									   and tco_targets_distribution.fk_target_id = tfp_effect_target_detail.fk_target_id
									   and tco_targets_distribution.pk_target_distribution_id = tfp_effect_target_detail.fk_target_distribution_id
                                       and tco_stage_target_indicators_import.fk_indicator_code = tfp_effect_target_detail.fk_indicator_code
    inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
                                  and tco_stage_target_indicators_import.fk_indicator_code = tco_indicator_setup.pk_indicator_code
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_targets_distribution.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_targets.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.fk_target_distribution_id is null

RETURN 0
