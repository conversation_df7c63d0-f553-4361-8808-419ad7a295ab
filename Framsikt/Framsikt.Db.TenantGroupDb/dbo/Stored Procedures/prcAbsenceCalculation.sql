
CREATE OR ALTER PROCEDURE [dbo].[prcAbsenceCalculation] 

@fk_tenant_id INT,
@budget_year INT,
@fk_user_id INT

AS

DECLARE @start_date date
DECLARE @end_date date

--Need to fetch 16 days before current period so that we get correct classification of short/long
SET @start_date = DATEADD(day,-16,DATEFROMPARTS(@budget_year, 1, 1))
SET @end_date = DATEFROMPARTS(@budget_year, 12, 31)


DROP TABLE IF EXISTS #emp_sick_calendar

CREATE TABLE #emp_sick_calendar (
[pk_id] [int] IDENTITY(1,1) NOT NULL,
fk_tenant_id INT,	
budget_year	INT,
period INT,
TheDate	date,
employee_id	INT,
is_sick	bit,
SeqDiff	INT,
FinalSeq	INT,
sick_instance_id INT,	
sick_days_running INT,
short_long VARCHAR(20)
)

INSERT INTO #emp_sick_calendar (fk_tenant_id,budget_year,period,TheDate,employee_id,is_sick,SeqDiff,FinalSeq)
select 
fk_tenant_id,budget_year,period,TheDate,employee_id,is_sick
,SeqDiff = MasterSeq-SickSeq
,FinalSeq = ROW_NUMBER() OVER (PARTITION BY employee_id,is_sick,MasterSeq-SickSeq ORDER BY TheDate)
FROM
(
select 
dt.fk_tenant_id
,dt.budget_year
,dt.TheDate
,dt.period
,dt.employee_id
,is_sick    = CASE WHEN sl.employee_id IS NULL THEN 0 ELSE 1 END
--,is_working = CASE WHEN sl.employee_id IS NOT NULL THEN 0 ELSE 1 END
--,"counter"	= CASE WHEN sl.employee_id IS NULL THEN -1 ELSE 1 END
,MasterSeq	= ROW_NUMBER() OVER (PARTITION BY dt.employee_id ORDER BY dt.TheDate)
,SickSeq	= ROW_NUMBER() OVER (PARTITION BY dt.employee_id, sl.employee_id ORDER BY dt.TheDate)
from 
(
	--Generate a calendar for each employee
	SELECT fk_tenant_id,budget_year,employee_id,TheDate,period 
	FROM gmd_date_table a
	LEFT JOIN
	(
		SELECT fk_tenant_id,budget_year,employee_id 
		FROM tmr_absence_employees
		where date_from >= @start_date AND date_to <= @end_date
		GROUP BY fk_tenant_id,budget_year,employee_id
	) e ON  1=1
	WHERE a.TheDate >= @start_date AND a.TheDate <= @end_date
)dt
LEFT JOIN
(
	--Fetches the days a given employee has a sick leave, shared across all positions that employee might have
	select fk_tenant_id,employee_id, TheDate
	from tmr_absence_employees a
	JOIN gmd_date_table b on b.TheDate >= a.date_from and b.TheDate <= a.date_to
	GROUP BY fk_tenant_id,employee_id, TheDate
) sl ON  dt.TheDate = sl.TheDate AND dt.fk_tenant_id = sl.fk_tenant_id AND dt.employee_id = sl.employee_id
where  dt.fk_tenant_id = @fk_tenant_id
) A



DROP TABLE IF EXISTS #sick_ranges
select 
*
,Sick_seq_order = ROW_NUMBER() OVER (PARTITION BY fk_tenant_id,budget_year,employee_id ORDER BY start_range)
INTO #sick_ranges
FROM
(
select fk_tenant_id,budget_year, employee_id,is_sick,SeqDiff
,days_in_range = MAX(FinalSeq)
,start_range = MIN(TheDate)
,end_range= MAX(TheDate)
from #emp_sick_calendar
--WHERE is_sick = 1
GROUP BY fk_tenant_id,budget_year, employee_id,is_sick,SeqDiff
--ORDER BY MIN(TheDate)
) A




DROP TABLE IF EXISTS #sick_instancing
select a.fk_tenant_id,a.budget_year,a.employee_id, a.SeqDiff,a.days_in_range,a.start_range,a.end_range, a.Sick_seq_order
,continue_from_prev_sick_period = CASE WHEN b.days_in_range <= 16 THEN 1 ELSE 0 END
,sick_instance_id = 1 --Will connect those periods that are within 16 days of eachother on a single ID
,row_number = ROW_NUMBER() OVER(order by a.fk_tenant_id,a.budget_year,a.employee_id,a.Sick_seq_order)
INTO #sick_instancing
from #sick_ranges a
LEFT JOIN #sick_ranges b on a.fk_tenant_id = b.fk_tenant_id and a.employee_id= b.employee_id and a.Sick_seq_order = b.Sick_seq_order +1
WHERE a.is_sick = 1;
--order by fk_tenant_id,budget_year,employee_id,Sick_seq_order

WITH CTE AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (ORDER BY row_number) AS rn
    FROM 
        #sick_instancing
),
RecursiveCTE AS (
    SELECT 
        row_number,
        sick_instance_id = 1,
        rn,
        start_range,
        end_range
    FROM 
        CTE
    WHERE 
        rn = 1
    UNION ALL
    SELECT 
        C.row_number,
        CASE 
            WHEN DATEDIFF(DAY, RC.end_range, C.start_range) <= 16 THEN RC.sick_instance_id 
            ELSE RC.sick_instance_id + 1 
        END,
        C.rn,
        C.start_range,
        C.end_range
    FROM 
        CTE C
    INNER JOIN 
        RecursiveCTE RC ON C.rn = RC.rn + 1
)
UPDATE SI
SET 
    sick_instance_id = RC.sick_instance_id
FROM 
    #sick_instancing SI
JOIN 
    RecursiveCTE RC ON SI.row_number = RC.row_number
OPTION (MAXRECURSION 0); -- Increase the maximum recursion limit to unlimited (use with caution)

--Update the main calendar table with the sick instances
update a set sick_instance_id = b.sick_instance_id
from #emp_sick_calendar a
LEFT JOIN #sick_instancing b ON a.fk_tenant_id = b.fk_tenant_id and a.budget_year = b.budget_year and a.employee_id = b.employee_id and a.SeqDiff = b.SeqDiff
where b.sick_instance_id IS NOT NULL

--Create a running count of sick days pr sick instance
update a set sick_days_running = b.sick_days
from #emp_sick_calendar a
JOIN
(
select pk_id
,sick_days = ROW_NUMBER() OVER (PARTITION BY fk_tenant_id,budget_year,employee_id,sick_instance_id ORDER BY TheDate)
from #emp_sick_calendar
where is_sick = 1
) b on a.pk_id = b.pk_id


--Set the classification of short / long for the sick instances
UPDATE #emp_sick_calendar SET short_long = CASE WHEN sick_days_running > 16 THEN 'LONG' ELSE 'SHORT' END
where is_sick = 1


--Generate calendar for work days pr position
	DROP TABLE IF EXISTS #workdays
	select a.fk_tenant_id,a.budget_year, b.TheDate,period,a.fk_employee_id,a.fk_position_id,gender,birth_date,a.fk_department_code,a.fk_function_code,public_position_type_value,position_percentage = CASE WHEN tpd.leave_percentage is not NULL THEN (1-(tpd.leave_percentage/100))*a.position_percentage ELSE a.position_percentage END
	INTO #workdays
	from tco_position_details a
	JOIN gmd_date_table b on b.TheDate >= a.position_start_date and b.TheDate <= a.position_end_date
    LEFT JOIN gmd_holiday_table c ON b.TheYear = C.TheYear AND b.TheDate = c.TheDate
	JOIN tco_employee_data d ON a.fk_tenant_id = d.fk_tenant_id and a.fk_employee_id = d.pk_employee_id
	JOIN tco_position_category e ON a.fk_tenant_id = e.fk_tenant_id and a.fk_position_code = e.pk_position_code
	LEFT JOIN tco_position_details tpd ON a.pk_id = tpd.pk_id AND b.TheDate >= tpd.leave_from_date and b.TheDate <= tpd.leave_to_date
	WHERE b.IsWeekend = 0 and c.TheDate IS NULL AND a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
	AND b.TheDate >= @start_date AND b.TheDate <= @end_date

--Fetch sick days - if multiple absences for a given position at a given date the highest absence percentage is used
    DROP TABLE IF EXISTS #sickdays;

    DROP TABLE IF EXISTS #FilteredAbsences;

    WITH EmployeesWith1050 AS (
     -- This CTE have distinct employee_id from tmr_absence_employees only with absence code 1050
    SELECT DISTINCT employee_id
    FROM tmr_absence_employees
    WHERE absence_code = 1050
        AND fk_tenant_id = @fk_tenant_id
        AND budget_year = @budget_year
)

   SELECT *
   INTO #FilteredAbsences
   FROM tmr_absence_employees a
   WHERE 
    a.fk_tenant_id = @fk_tenant_id
    AND a.budget_year = @budget_year
    AND (
        a.absence_code <> 1040
        OR (
            a.absence_code = 1040
            AND NOT EXISTS (
                SELECT 1 
                FROM EmployeesWith1050 e
                WHERE e.employee_id = a.employee_id
            )
        )
    );

    WITH ExpandedAbsences AS (
        -- This CTE expands absence periods into daily records, including absence_code
        SELECT
            a.fk_tenant_id,
            a.budget_year,
            a.employee_id,
            a.position_id,
            b.TheDate,
            a.absence_code,
            a.absence_percentage,
            a.date_from,          -- Keep for potential debugging or further logic if needed
            a.date_to             -- Keep for potential debugging
        FROM
            #FilteredAbsences a
        JOIN
            gmd_date_table b ON b.TheDate >= a.date_from AND b.TheDate <= a.date_to
        WHERE
            b.TheDate >= @start_date 
            AND b.TheDate <= @end_date 
            AND a.fk_tenant_id = @fk_tenant_id
            AND a.budget_year = @budget_year
    ),
    AggregatedAbsences AS (
        -- This CTE groups by the required fields and prepares the conditional percentages
        SELECT
            ea.fk_tenant_id,
            ea.budget_year,
            ea.employee_id,
            ea.position_id,
            ea.TheDate,
            MAX(CASE WHEN ea.absence_code = 1050 THEN ea.absence_percentage ELSE NULL END) AS percentage_for_code_1050, -- Calculate MAX percentage specifically for code 1050
            MAX(CASE WHEN ea.absence_code = 1050 THEN 1 ELSE 0 END) AS has_code_1050, -- Check if code 1050 exists in the group
            MAX(ea.absence_percentage) AS max_overall_percentage -- Calculate overall MAX percentage for the group (the original behavior)
        FROM
            ExpandedAbsences ea
        GROUP BY
            ea.fk_tenant_id,
            ea.budget_year,
            ea.employee_id,
            ea.position_id,
            ea.TheDate
    )
    -- Final selection into the temporary table using the new logic
    SELECT
        aa.fk_tenant_id,
        aa.budget_year,
        aa.employee_id,
        aa.position_id,
        aa.TheDate,
        CASE
            WHEN aa.has_code_1050 = 1 THEN aa.percentage_for_code_1050 -- Priority 1: Use percentage from code 1050
            ELSE aa.max_overall_percentage                             -- Fallback: Use overall max percentage
        END AS absence_percentage
    INTO #sickdays
    FROM AggregatedAbsences aa;

	--Fetch periods the employee has been sick - is used in screen to filter if the employee should be shown. If not sick for the period we group all together as "healthy"
	DROP TABLE IF EXISTS #sick_periods
	SELECT fk_tenant_id,budget_year,period,employee_id
	INTO #sick_periods
	FROM #emp_sick_calendar
	WHERE is_sick = 1
	GROUP BY fk_tenant_id,budget_year,period,employee_id

--Create final dataset before insert
DROP TABLE IF EXISTS #final_temp
select 
WD.fk_tenant_id
,WD.budget_year
,WD.TheDate	
,WD.period	
,WD.fk_employee_id	
,WD.fk_position_id
,position_pct = WD.position_percentage
,WD.gender	
,WD.birth_date	
,WD.fk_department_code	
,WD.fk_function_code	
,WD.public_position_type_value
,working_days				= 1
,working_days_adj			= CONVERT(DECIMAL (18,9),position_percentage)/100
,absence_percentage			= ISNULL(sd.absence_percentage,0)
,absence_days_short			= CASE WHEN sc.short_long = 'SHORT' AND SD.absence_percentage IS NOT NULL THEN 1 ELSE 0 END
,absence_days_short_adj		= CASE WHEN sc.short_long = 'SHORT' AND SD.absence_percentage IS NOT NULL THEN (CONVERT(DECIMAL (18,9),SD.absence_percentage)/100) * (CONVERT(DECIMAL (18,9),WD.position_percentage)/100) ELSE 0 END
,absence_days_long			= CASE WHEN sc.short_long = 'LONG' AND SD.absence_percentage IS NOT NULL THEN 1 ELSE 0 END
,absence_days_long_adj		= CASE WHEN sc.short_long = 'LONG' AND SD.absence_percentage IS NOT NULL THEN (CONVERT(DECIMAL (18,9),SD.absence_percentage)/100) * (CONVERT(DECIMAL (18,9),WD.position_percentage)/100) ELSE 0 END
,employee_sick_in_period	= CASE WHEN SP.employee_id IS NULL THEN 0 ELSE 1 END
INTO #final_temp
FROM #workdays WD
LEFT JOIN #sickdays SD ON WD.fk_tenant_id = SD.fk_tenant_id AND wd.budget_year = sd.budget_year AND WD.fk_employee_id = SD.employee_id AND WD.fk_position_id = SD.position_id AND WD.TheDate = SD.TheDate
--Fetch short/long definition
LEFT JOIN #emp_sick_calendar sc ON wd.fk_tenant_id = sc.fk_tenant_id AND WD.budget_year = sc.budget_year AND wd.fk_employee_id = sc.employee_id AND wd.TheDate = sc.TheDate
LEFT JOIN #sick_periods SP ON WD.fk_tenant_id = SP.fk_tenant_id AND WD.budget_year = SP.budget_year AND WD.period = SP.period AND WD.fk_employee_id = SP.employee_id
WHERE WD.fk_tenant_id = @fk_tenant_id
AND WD.budget_year = @budget_year


--Delete before insert
DELETE FROM [tmr_absence_employees_processed] 
WHERE fk_tenant_id = @fk_tenant_id
AND budget_year = @budget_year

--Insert fresh data
INSERT INTO [dbo].[tmr_absence_employees_processed]
           ([fk_tenant_id]
           ,[budget_year]
           ,[period]
           ,[fk_employee_id]
           ,[fk_position_id]
           ,[public_position_type_value]
           ,[gender]
           ,[birth_date]
           ,[position_pct]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[working_days]
           ,[working_days_adj]
           ,[absence_percentage]
           ,[absence_days_short]
           ,[absence_days_short_adj]
           ,[absence_days_long]
           ,[absence_days_long_adj]
           ,[employee_sick_in_period])

select		[fk_tenant_id]
           ,[budget_year]
           ,[period]
           ,[fk_employee_id]
           ,[fk_position_id]
           ,[public_position_type_value]
           ,[gender]
           ,[birth_date]
           ,[position_pct]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[working_days]				= SUM([working_days]			)
           ,[working_days_adj]			= SUM([working_days_adj]		)
           ,[absence_percentage]
           ,[absence_days_short]		= SUM([absence_days_short]	)
           ,[absence_days_short_adj]	= SUM([absence_days_short_adj])
           ,[absence_days_long]			= SUM([absence_days_long]		)
           ,[absence_days_long_adj]		= SUM([absence_days_long_adj]	)
           ,[employee_sick_in_period]
from #final_temp
WHERE LEFT(period,4) = @budget_year
GROUP BY [fk_tenant_id]
           ,[budget_year]
           ,[period]
           ,[fk_employee_id]
           ,[fk_position_id]
           ,[public_position_type_value]
           ,[gender]
           ,[birth_date]
           ,[position_pct]
           ,[fk_department_code]
           ,[fk_function_code]
		   ,[employee_sick_in_period]
		   ,[absence_percentage]




SELECT fk_tenant_id, budget_year, period, fk_department_code, fk_function_code, employee_count = COUNT(*) 
INTO #employee_count
FROM (
SELECT fk_tenant_id, budget_year, period, fk_department_code, fk_function_code, fk_employee_id 
FROM tmr_absence_employees_processed
GROUP BY fk_tenant_id, budget_year, period, fk_department_code, fk_function_code, fk_employee_id) a
GROUP BY fk_tenant_id, budget_year, period, fk_department_code, fk_function_code


SELECT	    [fk_tenant_id]
		   ,[budget_year]
		   ,[period]
		   ,[fk_department_code]
		   ,[fk_function_code]
		   ,[gender]
		   ,[birth_date]
		   ,[working_hrs] = SUM([working_days_adj])
		   ,[absence_hrs] = SUM([absence_days_short_adj] + [absence_days_long_adj])
		   ,[working_hrs_long] = SUM([working_days_adj])
		   ,[absence_hrs_long] = SUM([absence_days_long_adj])
		   ,[working_hrs_short] = SUM([working_days_adj])
		   ,[absence_hrs_short] = SUM([absence_days_short_adj])
		   ,[updated] = GETDATE()
		   ,[updated_by] = @fk_user_id
INTO #absence_grouped
FROM tmr_absence_employees_processed
WHERE fk_tenant_id = @fk_tenant_id
AND budget_year = @budget_year
GROUP BY fk_tenant_id, budget_year, period, fk_department_code, fk_function_code, gender, birth_date


--Delete before insert
DELETE FROM [tmr_absence_aggregated] 
WHERE fk_tenant_id = @fk_tenant_id
AND budget_year = @budget_year

--Insert data to tmr_absence_aggregated
INSERT INTO [dbo].[tmr_absence_aggregated]
		   ([fk_tenant_id]
		   ,[budget_year]
		   ,[period]
		   ,[fk_department_code]
		   ,[fk_function_code]
		   ,[working_hrs]
		   ,[absence_hrs]
		   ,[working_hrs_long]
		   ,[absence_hrs_long]
		   ,[working_hrs_short]
		   ,[absence_hrs_short]
		   ,[updated]
		   ,[updated_by]
		   ,[employee_count])

SELECT	    a.[fk_tenant_id]
		   ,a.[budget_year]
		   ,a.[period]
		   ,a.[fk_department_code]
		   ,a.[fk_function_code]
		   ,a.[working_hrs]
		   ,a.[absence_hrs]
		   ,a.[working_hrs_long]
		   ,a.[absence_hrs_long]
		   ,a.[working_hrs_short]
		   ,a.[absence_hrs_short]
		   ,[updated] = GETDATE()
		   ,[updated_by] = @fk_user_id
		   ,[employee_count]
FROM #absence_grouped a
JOIN #employee_count b ON a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year AND a.period = b.period AND a.fk_department_code = b.fk_department_code AND a.fk_function_code = b.fk_function_code
WHERE a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year






--Delete before insert
DELETE FROM [tmr_absence_details] 
WHERE fk_tenant_id = @fk_tenant_id
AND budget_year = @budget_year

--Insert data to tmr_absence_details
INSERT INTO [dbo].[tmr_absence_details]
		   ([fk_tenant_id]
		   ,[budget_year]
		   ,[period]
		   ,[fk_department_code]
		   ,[fk_function_code]
		   ,[fk_position_id]
		   ,[education_code]
		   ,[education]
		   ,[age]
		   ,[gender]
		   ,[seniority]
		   ,[working_hrs]
		   ,[absence_hrs_short]
		   ,[absence_hrs_long]
		   ,[updated]
		   ,[updated_by]
		   ,[age_group_id])

SELECT	    a.[fk_tenant_id]
		   ,a.[budget_year]
		   ,a.[period]
		   ,a.[fk_department_code]
		   ,a.[fk_function_code]
		   ,[fk_position_id] = ''
		   ,[education_code] = ''
		   ,[education] = ''
		   ,[age] = @budget_year - DATEPART(year, a.birth_date)
		   ,a.[gender]
		   ,[seniority] = 0
		   ,a.[working_hrs]
		   ,a.[absence_hrs_short]
		   ,a.[absence_hrs_long]
		   ,[updated] = GETDATE()
		   ,[updated_by] = @fk_user_id
		   ,[age_group_id] = '' 
FROM #absence_grouped a
JOIN #employee_count b ON a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year AND a.period = b.period AND a.fk_department_code = b.fk_department_code AND a.fk_function_code = b.fk_function_code
WHERE a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year


UPDATE tmr_absence_details
SET age_group_id = b.pk_id
FROM tmr_absence_details a
JOIN tmr_absence_age_groups b ON a.fk_tenant_id = b.fk_tenant_id and a.age BETWEEN b.age_from and b.age_to
WHERE a.fk_tenant_id = @fk_tenant_id
AND a.budget_year = @budget_year


DROP TABLE IF EXISTS #emp_sick_calendar
DROP TABLE IF EXISTS #sick_ranges
DROP TABLE IF EXISTS #sick_instancing
DROP TABLE IF EXISTS #final_temp
DROP TABLE IF EXISTS #employee_count
DROP TABLE IF EXISTS #absence_grouped
DROP TABLE IF EXISTS #FilteredAbsences

GO