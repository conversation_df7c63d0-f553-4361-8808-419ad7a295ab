CREATE OR ALTER   PROCEDURE [dbo].[prcValidateImportedActions]  
 @tenant_id int,  
 @user_id int,  
 @budget_year int,  
 @change_id int,  
 @isbudget_change bit,  
 @isblist bit  
  
AS  
  
 DECLARE @mandatory_adj_code AS INT = 0  
 DECLARE @mandatory_proj_code AS INT = 0  
 DECLARE @mandatory_freedim1 AS INT = 0  
 DECLARE @mandatory_freedim2 AS INT = 0  
 DECLARE @mandatory_freedim3 AS INT = 0  
 DECLARE @mandatory_freedim4 AS INT = 0  
 Declare @actionType int = 41   
  
 DECLARE @alter_code_table as TABLE (  
 fk_tenant_id INT NOT NULL,  
 fk_alter_code NVARCHAR(24) NOT NULL,  
 action_type nvarchar(24) NOT NULL)  
  
 if (SELECT COUNT(*) FROM tfp_budget_changes   
 WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year AND pk_change_id = @change_id AND org_budget_flag = 0) = 1  
 BEGIN   
 SET @isbudget_change = 1  
 END  
  
  
 IF (SELECT COUNT(*) FROM tco_adjustment_codes WHERE fk_tenant_id = @tenant_id AND status = 1) > 0  
 BEGIN  
  SET @mandatory_adj_code = 1  
 END  
  
 IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_MANDATORY_PROJECT' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @tenant_id) > 0  
 BEGIN  
  SET @mandatory_proj_code = 1  
 END  
  
 IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_MANDATORY_FREEDIM1' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @tenant_id) > 0  
 BEGIN  
  SET @mandatory_freedim1 = 1  
 END  
  
 IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_MANDATORY_FREEDIM2' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @tenant_id) > 0  
 BEGIN  
  SET @mandatory_freedim2 = 1  
 END  
  
 IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_MANDATORY_FREEDIM3' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @tenant_id) > 0  
 BEGIN  
  SET @mandatory_freedim3 = 1  
 END  
  
 IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_MANDATORY_FREEDIM4' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @tenant_id) > 0  
 BEGIN  
  SET @mandatory_freedim4 = 1  
 END  
  
   
 IF(@isbudget_change = 1)  
 BEGIN  
  SET @actionType = 60  
 END  
  
 --Clear all the error information--  
 UPDATE tfp_stage_action_import  
 SET action_id_error = 0,  
     action_title_error = 0,  
  account_code_error = 0,   
  department_code_error = 0,  
  function_code_error = 0,  
  project_code_error = 0,  
  free_dim_1_error = 0,  
  free_dim_2_error = 0,  
  free_dim_3_error = 0,  
  free_dim_4_error = 0,  
  adjustment_code_error = 0,  
  alter_code_error = 0,   
  error_count = 0,  
  action_type = @actionType,   
  isManuallyAdded = 0,  
  periodicKey_error = 0,
    year_1_amount_error=0   
 WHERE tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year AND   
  tfp_stage_action_import.user_id = @user_id AND  
  tfp_stage_action_import.change_id = @change_id;  
  
  --Trim accounting data--  
 UPDATE tfp_stage_action_import  
 SET account_code = RTRIM(LTRIM(account_code)),  
 department_code = RTRIM(LTRIM(department_code)),  
 function_code = RTRIM(LTRIM(function_code)),  
 project_code = RTRIM(LTRIM(project_code)),  
 free_dim_1 = RTRIM(LTRIM(free_dim_1)),  
 free_dim_2 = RTRIM(LTRIM(free_dim_2)),  
 free_dim_3 = RTRIM(LTRIM(free_dim_3)),  
 free_dim_4 = RTRIM(LTRIM(free_dim_4))  
 WHERE tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year AND   
  tfp_stage_action_import.user_id = @user_id AND  
  tfp_stage_action_import.change_id = @change_id;  
  
  
 IF(@isbudget_change != 1)  
  
 BEGIN  
   
 INSERT INTO @alter_code_table (fk_tenant_id, fk_alter_code, action_type)  
 SELECT ac.fk_tenant_id, ac.pk_alter_code, MIN(rv.attribute_value) as action_type    
 FROM tco_relation_values rv, tco_fp_alter_codes ac  
 WHERE rv.fk_tenant_id = @tenant_id  
 AND rv.attribute_type = 'ACTIONTYPE'  
 AND rv.relation_type = 'ALTERCODE'  
 AND rv.attribute_value IN ('5','7','41')  
 AND ac.pk_alter_code BETWEEN rv.relation_value_from AND rv.relation_value_to  
 AND ac.fk_tenant_id = rv.fk_tenant_id  
 GROUP BY ac.fk_tenant_id, ac.pk_alter_code  
  
 UPDATE tfp_stage_action_import SET action_type = ac.action_type  
 FROM tfp_stage_action_import imp, @alter_code_table ac  
 WHERE   
 imp.tenant_id = @tenant_id AND   
 imp.budget_year = @budget_year AND   
 imp.user_id = @user_id AND  
 imp.change_id = @change_id  
 AND imp.alter_code = ac.fk_alter_code  
 AND imp.tenant_id = ac.fk_tenant_id;  
  
 UPDATE tfp_stage_action_import SET line_order = 9000, isManuallyAdded = 1  
 WHERE tenant_id = @tenant_id AND   
 budget_year = @budget_year AND   
 user_id = @user_id AND  
 change_id = @change_id  
 AND action_type = 5  
  
 END  
 -- Validate action _id --  
  
 UPDATE tfp_stage_action_import  
 SET action_id_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 LEFT JOIN tfp_trans_header th ON imp.action_id = th.pk_action_id AND imp.tenant_id = th.fk_tenant_id  
 left join tfp_trans_detail td on th.fk_tenant_id = td.fk_tenant_id and th.pk_action_id = td.fk_action_id  
 WHERE th.pk_action_id IS NULL   
 and td.budget_year is null  
 AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND  
  imp.user_id = @user_id AND  
  imp.action_id >= 1 AND @isblist = 0;  
  
-- Validate action_id against BList (while importing blist actions)--  
  UPDATE tfp_stage_action_import  
 SET action_id_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 LEFT JOIN tfp_temp_header th ON imp.action_id = th.pk_temp_id AND imp.tenant_id = th.fk_tenant_id  
 left join tfp_temp_detail td on th.fk_tenant_id = td.fk_tenant_id and th.pk_temp_id = td.fk_temp_id  
 WHERE th.pk_temp_id IS NULL   
 and td.budget_year is null  
 AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND  
  imp.user_id = @user_id AND  
  imp.action_id >= 1 AND @isblist = 1;  
  
-- Validate action_id against Deleted List--  
  
 UPDATE tfp_stage_action_import  
 SET action_id_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 WHERE imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND  
  imp.action_id in (select fk_action_id from tfp_delete_header where fk_Tenant_id = @tenant_id) AND  
  action_id_error <> 1 AND  
  imp.user_id = @user_id AND  
  imp.action_id >= 1 AND @isblist = 0;  
  
-- Validate action_id against B List--  
  
 UPDATE tfp_stage_action_import  
 SET action_id_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 WHERE imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND   
  imp.action_id in (select fk_action_id from tfp_temp_header where fk_Tenant_id = @tenant_id AND is_parked_action = 0) AND  
  action_id_error <> 1 AND  
  imp.user_id = @user_id AND  
  imp.action_id >= 1 AND @isblist = 0;  
  
  
 --Validate action_id againts current year   
   UPDATE tfp_stage_action_import  
 SET action_id_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 WHERE imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND  
  imp.action_id NOT IN (select fk_action_id from tfp_trans_detail where fk_Tenant_id = @tenant_id and budget_year = @budget_year and fk_action_id = imp.action_id) AND  
  action_id_error <> 1 AND  
  imp.user_id = @user_id AND  
  imp.action_id >= 1 AND @isblist = 0;  
  
  
 -- Validate action name --  
  
 UPDATE tfp_stage_action_import  
 SET action_title_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 WHERE (imp.action_id = 0  
 AND imp.action_title = '' AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id AND  
  imp.change_id = @change_id) OR  
 (imp.action_id = 0  
 AND imp.action_title IS NULL AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id AND  
  imp.change_id = @change_id) OR  
 (imp.action_id IS NULL  
 AND imp.action_title IS NULL AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id AND  
  imp.change_id = @change_id) OR  
 (imp.action_id = 0  
 AND imp.action_title IS NULL AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id AND  
  imp.change_id = @change_id) OR  
  (imp.action_id != 0  
 AND DATALENGTH(imp.action_title) > 301  AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id AND  
  imp.change_id = @change_id) OR  
  (imp.action_id = 0  
 AND DATALENGTH(imp.action_title) > 301  AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id AND  
  imp.change_id = @change_id);  
  
 --Validate account codes--  
 UPDATE tfp_stage_action_import  
 SET account_code_error = 1, error_count = error_count + 1   
 FROM tfp_stage_action_import   
 LEFT JOIN tco_accounts acc on   
  tfp_stage_action_import.tenant_id = acc.pk_tenant_id AND  
  tfp_stage_action_import.account_code = acc.pk_account_code AND     
  tfp_stage_action_import.budget_year BETWEEN datepart (year, acc.dateFrom) AND datepart (year, acc.dateTo) AND  
  acc.isActive = 1 and acc.fk_kostra_account_code not in (select pk_kostra_account_code from gco_kostra_accounts where type='investment')  
 WHERE acc.pk_account_code IS NULL AND   
  tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year AND   
  tfp_stage_action_import.user_id = @user_id AND  
  tfp_stage_action_import.change_id = @change_id ;  
  
 --Validate department codes --  
 UPDATE tfp_stage_action_import  
 SET department_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_departments DEPTS ON   
  tfp_stage_action_import.tenant_id = DEPTS.fk_tenant_id AND  
  tfp_stage_action_import.department_code COLLATE Latin1_General_CS_AS = DEPTS.pk_department_code COLLATE Latin1_General_CS_AS AND   
  tfp_stage_action_import.budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND  
  DEPTS.[status] = 1 AND  
  @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to  
 WHERE DEPTS.pk_department_code IS NULL AND   
  tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year AND   
  tfp_stage_action_import.user_id = @user_id AND  
  tfp_stage_action_import.change_id = @change_id ;  
  
/* --Validate department codes against user access --  
 UPDATE tfp_stage_action_import  
 SET department_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import imp  
 LEFT JOIN (SELECT DISTINCT fk_department_code, fk_user_id FROM (  
SELECT toh.fk_department_code, r.fk_user_id FROM tco_org_hierarchy toh, tco_user_orgrole r  
WHERE toh.fk_tenant_id = @tenant_id AND r.fk_user_id = @user_id  
AND r.fk_tenant_id = toh.fk_tenant_id AND r.fk_org_version = toh.fk_org_version AND r.fk_org_id = toh.org_id_1 AND r.hierarchy_level = 1  
UNION  
SELECT toh.fk_department_code, r.fk_user_id FROM tco_org_hierarchy toh, tco_user_orgrole r  
WHERE toh.fk_tenant_id = @tenant_id AND r.fk_user_id = @user_id  
AND r.fk_tenant_id = toh.fk_tenant_id AND r.fk_org_version = toh.fk_org_version AND r.fk_org_id = toh.org_id_2 AND r.hierarchy_level = 2  
UNION  
SELECT toh.fk_department_code, r.fk_user_id FROM tco_org_hierarchy toh, tco_user_orgrole r  
WHERE toh.fk_tenant_id = @tenant_id AND r.fk_user_id = @user_id  
AND r.fk_tenant_id = toh.fk_tenant_id AND r.fk_org_version = toh.fk_org_version AND r.fk_org_id = toh.org_id_3 AND r.hierarchy_level = 3  
UNION  
SELECT toh.fk_department_code, r.fk_user_id FROM tco_org_hierarchy toh, tco_user_orgrole r  
WHERE toh.fk_tenant_id = @tenant_id AND r.fk_user_id = @user_id  
AND r.fk_tenant_id = toh.fk_tenant_id AND r.fk_org_version = toh.fk_org_version AND r.fk_org_id = toh.org_id_4 AND r.hierarchy_level = 4  
UNION  
SELECT toh.fk_department_code, r.fk_user_id FROM tco_org_hierarchy toh, tco_user_orgrole r  
WHERE toh.fk_tenant_id = @tenant_id AND r.fk_user_id = @user_id  
AND r.fk_tenant_id = toh.fk_tenant_id AND r.fk_org_version = toh.fk_org_version AND r.fk_org_id = toh.org_id_5 AND r.hierarchy_level = 5) A) B  
ON b.fk_department_code = imp.department_code AND imp.user_id = B.fk_user_id  
WHERE B.fk_department_code IS NULL  
and imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year AND   
  imp.user_id = @user_id;  
*/  
 --Validate Functions--  
 UPDATE tfp_stage_action_import  
 SET function_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_functions fn on   
  tfp_stage_action_import.tenant_id = fn.pk_tenant_id AND  
  tfp_stage_action_import.function_code = fn.pk_Function_code  AND     
  tfp_stage_action_import.budget_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND  
  fn.isActive = 1  
 WHERE fn.pk_Function_code IS NULL AND   
  tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year AND   
  tfp_stage_action_import.user_id = @user_id AND  
  tfp_stage_action_import.change_id = @change_id   
 ;  
  
 --Validate Projects--  
BEGIN  
IF @mandatory_proj_code = 0  
 BEGIN  
  UPDATE tfp_stage_action_import  
  SET project_code_error = 1, error_count = error_count + 1  
  FROM tfp_stage_action_import   
  LEFT JOIN tco_projects prj on   
   tfp_stage_action_import.tenant_id = prj.fk_tenant_id AND  
   tfp_stage_action_import.project_code = prj.pk_project_code AND      
   tfp_stage_action_import.budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND  
   prj.active = 1  
  WHERE prj.pk_project_code IS NULL  
  AND tfp_stage_action_import.tenant_id = @tenant_id AND   
   tfp_stage_action_import.budget_year = @budget_year  AND  
   tfp_stage_action_import.user_id = @user_id  
   AND tfp_stage_action_import.project_code != ''   
   AND tfp_stage_action_import.project_code IS NOT NULL    
 END  
ELSE  
 BEGIN  
  UPDATE tfp_stage_action_import  
  SET project_code_error = 1, error_count = error_count + 1  
  FROM tfp_stage_action_import   
  LEFT JOIN tco_projects prj on prj.pk_project_code = tfp_stage_action_import.project_code AND   
   tfp_stage_action_import.tenant_id = prj.fk_tenant_id    
  WHERE prj.pk_project_code IS NULL  
  AND tfp_stage_action_import.tenant_id = @tenant_id AND   
   tfp_stage_action_import.budget_year = @budget_year  AND  
   tfp_stage_action_import.user_id = @user_id  
 END  
END  
  
  
-- Validate free dim 1 --  
  
BEGIN  
IF @mandatory_freedim1 = 0   
  
 BEGIN  
  
 UPDATE tfp_stage_action_import  
 SET free_dim_1_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_1 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  AND tfp_stage_action_import.free_dim_1 != ''   
  AND tfp_stage_action_import.free_dim_1 IS NOT NULL;  
    
 END  
ELSE  
 BEGIN  
   
  UPDATE tfp_stage_action_import  
 SET free_dim_1_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_1 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  
 END  
END    
  
-- Validate free dim 2 --  
  
BEGIN   
 IF @mandatory_freedim2 = 0  
 BEGIN  
  
 UPDATE tfp_stage_action_import  
 SET free_dim_2_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_2 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  AND tfp_stage_action_import.free_dim_2 != ''   
  AND tfp_stage_action_import.free_dim_2 IS NOT NULL;   
   
 END  
ELSE  
  
 BEGIN  
   
 UPDATE tfp_stage_action_import  
 SET free_dim_2_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_2 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  
 END  
   
END  
   
  
-- Validate free dim 3 --  
BEGIN   
 IF @mandatory_freedim3 = 0  
 BEGIN  
   
 UPDATE tfp_stage_action_import  
 SET free_dim_3_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_3 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  AND tfp_stage_action_import.free_dim_3 != ''   
  AND tfp_stage_action_import.free_dim_3 IS NOT NULL;  
    
 END  
  
ELSE  
 BEGIN  
  
 UPDATE tfp_stage_action_import  
 SET free_dim_3_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_3 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
   
 END  
   
END  
    
  
-- Validate free dim 4 --  
BEGIN  
IF @mandatory_freedim4 = 0  
  
 BEGIN  
  
 UPDATE tfp_stage_action_import  
 SET free_dim_4_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_4 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  AND tfp_stage_action_import.free_dim_4 != ''   
  AND tfp_stage_action_import.free_dim_4 IS NOT NULL;   
  
  END  
  
ELSE  
  
 BEGIN  
  
 UPDATE tfp_stage_action_import  
 SET free_dim_4_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_action_import.free_dim_4 AND   
  tfp_stage_action_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id  
  
 END  
  
END  
  
-- Validate adjustment code --  
  
--BEGIN  
--IF @mandatory_adj_code = 0   
-- BEGIN  
  
-- UPDATE tfp_stage_action_import  
-- SET adjustment_code_error = 1, error_count = error_count + 1  
-- FROM tfp_stage_action_import   
-- LEFT JOIN tco_adjustment_codes adj on adj.pk_adjustment_code = tfp_stage_action_import.adjustment_code AND   
--  tfp_stage_action_import.tenant_id = adj.fk_tenant_id   
-- WHERE adj.pk_adjustment_code IS NULL  
-- AND tfp_stage_action_import.tenant_id = @tenant_id AND   
--  tfp_stage_action_import.budget_year = @budget_year  AND  
--  tfp_stage_action_import.user_id = @user_id  
--  AND tfp_stage_action_import.adjustment_code != ''   
--  AND tfp_stage_action_import.adjustment_code IS NOT NULL;   
  
-- END  
  
--ELSE   
  
-- BEGIN  
  
--  UPDATE tfp_stage_action_import  
-- SET adjustment_code_error = 1, error_count = error_count + 1  
-- FROM tfp_stage_action_import   
-- LEFT JOIN tco_adjustment_codes adj on adj.pk_adjustment_code = tfp_stage_action_import.adjustment_code AND   
--  tfp_stage_action_import.tenant_id = adj.fk_tenant_id and adj.status <> 0  
-- WHERE adj.pk_adjustment_code IS NULL  
-- AND tfp_stage_action_import.tenant_id = @tenant_id AND   
--  tfp_stage_action_import.budget_year = @budget_year  AND  
--  tfp_stage_action_import.user_id = @user_id  
  
-- END  
--END  
  
-- Validate alter code --  
  
 UPDATE tfp_stage_action_import  
 SET alter_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_action_import   
 LEFT JOIN tco_fp_alter_codes alt on alt.pk_alter_code = tfp_stage_action_import.alter_code AND   
  tfp_stage_action_import.tenant_id = alt.fk_tenant_id   
 WHERE alt.pk_alter_code IS NULL  
 AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year  AND  
  tfp_stage_action_import.user_id = @user_id;  
  
   --Do not allow alter code 41 for budget change  
 IF(@isbudget_change = 1)  
  BEGIN  
    UPDATE tfp_stage_action_import  
    SET alter_code_error = 1, error_count = error_count + 1  
    FROM tfp_stage_action_import  stg  
    left join tco_relation_values rel on stg.tenant_id = rel.fk_tenant_id and stg.alter_code between rel.relation_value_from and rel.relation_value_to and rel.attribute_type = 'ACTIONTYPE' and rel.relation_type = 'ALTERCODE' and rel.attribute_value in ('1

','2','3','4','5','7','60')  
    WHERE rel.attribute_value IS NULL  
    AND stg.tenant_id = @tenant_id AND   
     stg.budget_year = @budget_year  AND  
     stg.user_id = @user_id;  
  END  
 ELSE  
  BEGIN  
   UPDATE tfp_stage_action_import  
   SET alter_code_error = 1, error_count = error_count + 1  
   FROM tfp_stage_action_import  stg  
   left join tco_relation_values rel on stg.tenant_id = rel.fk_tenant_id and stg.alter_code between rel.relation_value_from and rel.relation_value_to and rel.attribute_type = 'ACTIONTYPE' and rel.relation_type = 'ALTERCODE' and rel.attribute_value in ('1'

,'2','3','4','5','7','41','60')  
   WHERE rel.attribute_value IS NULL  
   AND stg.tenant_id = @tenant_id AND   
    stg.budget_year = @budget_year  AND  
    stg.user_id = @user_id;  
  END  
  
-- Fetch line order and override action type, Fetch action_id on existing actions on central actions  
  
 UPDATE tfp_stage_action_import  
       SET line_order = A.line_order, action_title = A.description, action_type = A.action_type, action_id = A.pk_action_id  
       FROM tfp_stage_action_import imp  
       JOIN tmd_finplan_line_setup ls  
       ON imp.account_code = ls.fk_account_code  
       AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to  
       AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to  
       AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to  
       AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to  
       AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to  
       AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to  
       AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to  
       AND imp.tenant_id = ls.fk_tenant_id  
       AND imp.budget_year = ls.budget_year  
       JOIN (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year  
                                               FROM tfp_trans_header th, tfp_trans_detail td  
                                               WHERE th.pk_action_id = td.fk_action_id  
                                               AND th.fk_tenant_id = td.fk_tenant_id  
                                               AND th.action_type IN (1,2,3,4,100)  
                                               AND th.line_order != 0)  A  
       ON ls.line_order = A.line_order  
       AND ls.fk_tenant_id = A.fk_tenant_id  
       AND ls.budget_year = A.budget_year  
       AND ls.action_type = A.action_type  
       WHERE imp.tenant_id = @tenant_id    
       AND imp.budget_year = @budget_year   
       AND imp.user_id = @user_id  
       AND ls.priority = 0;  
  
 UPDATE tfp_stage_action_import  
       SET line_order = A.line_order, action_title = A.description, action_type = A.action_type, action_id = A.pk_action_id  
       FROM tfp_stage_action_import imp  
       JOIN tmd_finplan_line_setup ls  
       ON imp.account_code = ls.fk_account_code  
       AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to  
       AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to  
       AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to  
       AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to  
       AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to  
       AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to  
       AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to  
       AND imp.tenant_id = ls.fk_tenant_id  
       AND imp.budget_year = ls.budget_year  
       JOIN (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year  
                                               FROM tfp_trans_header th, tfp_trans_detail td  
                                               WHERE th.pk_action_id = td.fk_action_id  
                                               AND th.fk_tenant_id = td.fk_tenant_id  
                                               AND th.action_type IN (1,2,3,4,100)  
                                               AND th.line_order != 0)  A  
       ON ls.line_order = A.line_order  
       AND ls.fk_tenant_id = A.fk_tenant_id  
       AND ls.budget_year = A.budget_year  
       AND ls.action_type = A.action_type  
       WHERE imp.tenant_id = @tenant_id    
       AND imp.budget_year = @budget_year   
       AND imp.user_id = @user_id  
       AND ls.priority = 5;  
  
  
 UPDATE tfp_stage_action_import  
       SET line_order = A.line_order, action_title = A.description, action_type = A.action_type, action_id = A.pk_action_id  
       FROM tfp_stage_action_import imp  
       JOIN tmd_finplan_line_setup ls  
       ON imp.account_code = ls.fk_account_code  
       AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to  
       AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to  
       AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to  
       AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to  
       AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to  
       AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to  
       AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to  
       AND imp.tenant_id = ls.fk_tenant_id  
       AND imp.budget_year = ls.budget_year  
       JOIN (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year  
                                               FROM tfp_trans_header th, tfp_trans_detail td  
                                               WHERE th.pk_action_id = td.fk_action_id  
                                               AND th.fk_tenant_id = td.fk_tenant_id  
                                               AND th.action_type IN (1,2,3,4,100)  
                                               AND th.line_order != 0)  A  
       ON ls.line_order = A.line_order  
       AND ls.fk_tenant_id = A.fk_tenant_id  
       AND ls.budget_year = A.budget_year  
       AND ls.action_type = A.action_type  
       WHERE imp.tenant_id = @tenant_id    
       AND imp.budget_year = @budget_year   
       AND imp.user_id = @user_id  
       AND ls.priority = 4;  
  
  UPDATE tfp_stage_action_import  
       SET line_order = A.line_order, action_title = A.description, action_type = A.action_type, action_id = A.pk_action_id  
       FROM tfp_stage_action_import imp  
       JOIN tmd_finplan_line_setup ls  
       ON imp.account_code = ls.fk_account_code  
       AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to  
       AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to  
       AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to  
       AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to  
       AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to  
       AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to  
       AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to  
       AND imp.tenant_id = ls.fk_tenant_id  
       AND imp.budget_year = ls.budget_year  
       JOIN (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year  
                                               FROM tfp_trans_header th, tfp_trans_detail td  
                                               WHERE th.pk_action_id = td.fk_action_id  
                                               AND th.fk_tenant_id = td.fk_tenant_id  
                                               AND th.action_type IN (1,2,3,4,100)  
                                               AND th.line_order != 0)  A  
       ON ls.line_order = A.line_order  
       AND ls.fk_tenant_id = A.fk_tenant_id  
       AND ls.budget_year = A.budget_year  
       AND ls.action_type = A.action_type  
       WHERE imp.tenant_id = @tenant_id    
       AND imp.budget_year = @budget_year   
       AND imp.user_id = @user_id  
       AND ls.priority = 3;  
  
  UPDATE tfp_stage_action_import  
       SET line_order = A.line_order, action_title = A.description, action_type = A.action_type, action_id = A.pk_action_id  
       FROM tfp_stage_action_import imp  
       JOIN tmd_finplan_line_setup ls  
       ON imp.account_code = ls.fk_account_code  
       AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to  
       AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to  
       AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to  
       AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to  
       AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to  
       AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to  
       AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to  
       AND imp.tenant_id = ls.fk_tenant_id  
       AND imp.budget_year = ls.budget_year  
       JOIN (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year  
                                               FROM tfp_trans_header th, tfp_trans_detail td  
                                               WHERE th.pk_action_id = td.fk_action_id  
                                               AND th.fk_tenant_id = td.fk_tenant_id  
                                               AND th.action_type IN (1,2,3,4,100)  
                                               AND th.line_order != 0)  A  
       ON ls.line_order = A.line_order  
       AND ls.fk_tenant_id = A.fk_tenant_id  
       AND ls.budget_year = A.budget_year  
       AND ls.action_type = A.action_type  
       WHERE imp.tenant_id = @tenant_id    
       AND imp.budget_year = @budget_year   
       AND imp.user_id = @user_id  
       AND ls.priority = 2;  
  
  UPDATE tfp_stage_action_import  
       SET line_order = A.line_order, action_title = A.description, action_type = A.action_type, action_id = A.pk_action_id  
       FROM tfp_stage_action_import imp  
       JOIN tmd_finplan_line_setup ls  
       ON imp.account_code = ls.fk_account_code  
       AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to  
       AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to  
       AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to  
       AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to  
       AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to  
       AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to  
       AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to  
       AND imp.tenant_id = ls.fk_tenant_id  
       AND imp.budget_year = ls.budget_year  
       JOIN (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year  
                                               FROM tfp_trans_header th, tfp_trans_detail td  
                                               WHERE th.pk_action_id = td.fk_action_id  
                                               AND th.fk_tenant_id = td.fk_tenant_id  
                                               AND th.action_type IN (1,2,3,4,100)  
                                               AND th.line_order != 0)  A  
       ON ls.line_order = A.line_order  
       AND ls.fk_tenant_id = A.fk_tenant_id  
       AND ls.budget_year = A.budget_year  
       AND ls.action_type = A.action_type  
       WHERE imp.tenant_id = @tenant_id    
       AND imp.budget_year = @budget_year   
       AND imp.user_id = @user_id  
       AND ls.priority = 1;  
  
-- Check that not all amounts is 0 --  
  
UPDATE tfp_stage_action_import SET year_1_amount_error = 1, error_count = error_count +1  
WHERE year_1_amount = 0  
AND year_2_amount = 0   
AND year_3_amount = 0   
AND year_4_amount = 0   
AND year_5_amount = 0  
AND year_6_amount = 0  
AND year_7_amount = 0  
AND year_8_amount = 0  
AND year_9_amount = 0  
AND year_10_amount = 0   
AND tfp_stage_action_import.tenant_id = @tenant_id AND   
  tfp_stage_action_import.budget_year = @budget_year AND   
  tfp_stage_action_import.user_id = @user_id AND  
  tfp_stage_action_import.change_id = @change_id;  
  

-- Check that amounts doesn't have decimals --   
   
UPDATE tfp_stage_action_import
SET year_1_amount_error = 1,
    error_count = error_count + 1
WHERE year_1_amount != CAST(year_1_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;

UPDATE tfp_stage_action_import
SET year_2_amount_error = 1,
    error_count = error_count + 1
WHERE year_2_amount != CAST(year_2_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;

UPDATE tfp_stage_action_import
SET year_3_amount_error = 1,
    error_count = error_count + 1
WHERE year_3_amount != CAST(year_3_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;

UPDATE tfp_stage_action_import
SET year_4_amount_error = 1,
    error_count = error_count + 1
WHERE year_4_amount != CAST(year_4_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;

UPDATE tfp_stage_action_import
SET year_5_amount_error = 1,
    error_count = error_count + 1
WHERE year_5_amount != CAST(year_5_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;


UPDATE tfp_stage_action_import
SET year_6_amount_error = 1,
    error_count = error_count + 1
WHERE year_6_amount != CAST(year_6_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;

UPDATE tfp_stage_action_import
SET year_7_amount_error = 1,
    error_count = error_count + 1
WHERE year_7_amount != CAST(year_7_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;


UPDATE tfp_stage_action_import
SET year_8_amount_error = 1,
    error_count = error_count + 1
WHERE year_8_amount != CAST(year_8_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;


UPDATE tfp_stage_action_import
SET year_9_amount_error = 1,
    error_count = error_count + 1
WHERE year_9_amount != CAST(year_9_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;

UPDATE tfp_stage_action_import
SET year_10_amount_error = 1,
    error_count = error_count + 1
WHERE year_10_amount != CAST(year_10_amount AS INT)
  AND tfp_stage_action_import.tenant_id = @tenant_id
  AND tfp_stage_action_import.budget_year = @budget_year
  AND tfp_stage_action_import.user_id = @user_id
  AND tfp_stage_action_import.change_id = @change_id;


-- Check that user have access to the departments --  
  
------ Update action title if the action Id is present -------  
UPDATE tfp_stage_action_import  
SET action_title = (SELECT description FROM tfp_trans_header where fk_tenant_id = tenant_id and pk_action_id = action_id)  
WHERE action_id != 0 AND action_title IS NULL  
  
------ Update action title if the action Id is present -------  
  
  
 -- UPDATING WITH DEFAULT PeriodicKey VALUES FOR NULL/BLANK ENTRIES   
  
  UPDATE tfp_stage_action_import set periodicKey = (Select  param_value FROM vw_tco_parameters with(nolock) where param_name='DEFAULT_BUDGET_PER_KEY' and fk_tenant_id=@tenant_id)  
  WHERE tfp_stage_action_import.tenant_id = @tenant_id AND   
    tfp_stage_action_import.budget_year = @budget_year AND   
    tfp_stage_action_import.user_id = @user_id AND  
    (tfp_stage_action_import.periodicKey is NULL OR tfp_stage_action_import.periodicKey ='' ) ;  
  
  -- Validate PeriodicKey If Periodic Key is not zero  
  
  Select PeriodKeys.ID into #tempPeriodicKeys from   
  (select distinct key_id as ID,key_description from tco_periodic_key   where      fixed_flag  = 1 and     fk_tenant_id=0  
  UNION ALL  
  select distinct key_id,key_description from tco_periodic_key   where      fixed_flag  = 1 and     fk_tenant_id=@tenant_id  
  UNION ALL  
  select distinct key_id,key_description from tco_periodic_key   where   fixed_flag  <>  1 and     fk_tenant_id=0  
  UNION ALL  
  select distinct key_id,key_description from tco_periodic_key   where   fixed_flag  <>  1 and     fk_tenant_id=@tenant_id) PeriodKeys  
  
     
  UPDATE tfp_stage_action_import  
  SET periodicKey_error = 1, error_count = error_count + 1  
  FROM tfp_stage_action_import   
  LEFT JOIN #tempPeriodicKeys frdm on CAST (frdm.ID as varchar(10)) = tfp_stage_action_import.periodicKey   
  where frdm.ID IS NULL and tfp_stage_action_import.tenant_id = @tenant_id   and tfp_stage_action_import.budget_year = @budget_year   and tfp_stage_action_import.user_id =  @user_id AND  
  tfp_stage_action_import.change_id = @change_id and tfp_stage_action_import.periodicKey != '0';  
  
  DROP table #tempPeriodicKeys  
    
  
RETURN 0  

