CREATE OR ALTER PROC [dbo].[prcInitializeSAMtransactions] @tenant_id INT, @forecast_period INT, @updated_by INT, @status INT

AS

DECLARE @year INT = @forecast_period/100*100

DECLARE @prev_period INT = ISNULL((select MAX(forecast_period) from [dbo].[tsam_period_transactions] where fk_tenant_id = @tenant_id AND (forecast_period > @year) and (forecast_period < @forecast_period)),@forecast_period/100*100)

IF @status = 1 BEGIN

	DELETE FROM [dbo].[tsam_period_status]
	where fk_tenant_id = @tenant_id
	and forecast_period = @forecast_period

	INSERT [dbo].[tsam_period_status]
	select @tenant_id,@forecast_period,1,GETDATE(),@updated_by

	--delete existing
	DELETE from [tsam_period_transactions]
	WHERE fk_tenant_id = @tenant_id
	and forecast_period = @forecast_period

	--insert new data from base if no existing previous forecast
	IF @prev_period = @forecast_period/100*100 BEGIN
		
			INSERT INTO [dbo].[tsam_period_transactions]
           ([fk_tenant_id]
           ,[forecast_period]
           ,[budget_year]
           ,[fk_action_id]
           ,[new_fk_action_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_alter_code]
           ,[fp_amt]
           ,[final_amt]
           ,[final_description]
           ,[Prev_reversal]
           ,[real_amount]
           ,[yearly_forecast]
           ,[status]
           ,[risk]
           ,[description_department]
           ,[reversal_amt]
           ,[partial_reversal_amt]
           ,[fund_reversal_amt]
           ,[partial_reversal_2_amt]
           ,[withdrawal_amt]
           ,[reversal_amt_f]
           ,[partial_reversal_amt_f]
           ,[fund_reversal_amt_f]
           ,[partial_reversal_2_amt_f]
           ,[withdrawal_amt_f]
           ,[description_final]
           ,[updated]
           ,[updated_by]
		   ,[prev_withdrawal_amt]
           ,[prev_fund_reversal_amt])

			select		
			[fk_tenant_id]
           ,[forecast_period] = @forecast_period
           ,[budget_year]
           ,[fk_action_id]
           ,[new_fk_action_id] = 0
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_alter_code]
           ,[fp_amt]
           ,[final_amt]
           ,[final_description]
           ,[Prev_reversal] = 0
           ,[real_amount] = 0
           ,[yearly_forecast] = 0
           ,[status] = 0
           ,[risk] = 0
           ,[description_department] = ''
           ,[reversal_amt] = 0
           ,[partial_reversal_amt] = 0
           ,[fund_reversal_amt] = 0
           ,[partial_reversal_2_amt] = 0
           ,[withdrawal_amt] = 0
           ,[reversal_amt_f] = 0
           ,[partial_reversal_amt_f] = 0
           ,[fund_reversal_amt_f] = 0
           ,[partial_reversal_2_amt_f] = 0
           ,[withdrawal_amt_f] = 0
           ,[description_final] = ''
           ,[updated] = GETDATE()
           ,[updated_by] = @updated_by
		   ,[prev_withdrawal_amt] = 0
           ,[prev_fund_reversal_amt] = 0
			from [dbo].[tsam_base_transactions]
			where fk_tenant_id = @tenant_id
			and budget_year = @forecast_period / 100
	END

	--Insert from previous period if it exists
	IF @prev_period > @forecast_period /100 * 100 BEGIN

			INSERT INTO [dbo].[tsam_period_transactions]
           ([fk_tenant_id]
           ,[forecast_period]
           ,[budget_year]
           ,[fk_action_id]
           ,[new_fk_action_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_alter_code]
           ,[fp_amt]
           ,[final_amt]
           ,[final_description]
           ,[Prev_reversal]
           ,[real_amount]
           ,[yearly_forecast]
           ,[status]
           ,[risk]
           ,[description_department]
           ,[reversal_amt]
           ,[partial_reversal_amt]
           ,[fund_reversal_amt]
           ,[partial_reversal_2_amt]
           ,[withdrawal_amt]
           ,[reversal_amt_f]
           ,[partial_reversal_amt_f]
           ,[fund_reversal_amt_f]
           ,[partial_reversal_2_amt_f]
           ,[withdrawal_amt_f]
           ,[description_final]
           ,[updated]
           ,[updated_by]
		   ,[prev_withdrawal_amt]
           ,[prev_fund_reversal_amt])

			select		
			[fk_tenant_id]
           ,[forecast_period] = @forecast_period
           ,[budget_year]
           ,[fk_action_id]
           ,[new_fk_action_id] = 0
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[fk_alter_code]
           ,[fp_amt]
           ,[final_amt]
           ,[final_description]
           ,[Prev_reversal] = [Prev_reversal] + [reversal_amt_f]
           ,[real_amount]
           ,[yearly_forecast]
           ,[status]
           ,[risk]
           ,[description_department] = ''
           ,[reversal_amt] = 0
           ,[partial_reversal_amt] = 0
           ,[fund_reversal_amt] = 0
           ,[partial_reversal_2_amt] = 0
           ,[withdrawal_amt] = 0
           ,[reversal_amt_f] = 0
           ,[partial_reversal_amt_f] = 0
           ,[fund_reversal_amt_f] = 0
           ,[partial_reversal_2_amt_f] = 0
           ,[withdrawal_amt_f] = 0
           ,[description_final] = ''
           ,[updated] = getdate()
           ,[updated_by] = @updated_by
		   ,[prev_withdrawal_amt] = [prev_withdrawal_amt] + [withdrawal_amt_f]
           ,[prev_fund_reversal_amt] = [prev_fund_reversal_amt] + [fund_reversal_amt_f]
			from [tsam_period_transactions]
			where fk_tenant_id = @tenant_id
			and forecast_period = @prev_period

	END

END

IF @status = 2 BEGIN

	update [dbo].[tsam_period_status] set status = @status
	where fk_tenant_id = @tenant_id
	and forecast_period = @forecast_period

	update [tsam_period_transactions] set reversal_amt_f = reversal_amt, partial_reversal_amt_f = partial_reversal_amt, fund_reversal_amt_f = fund_reversal_amt, partial_reversal_2_amt_f = partial_reversal_2_amt, withdrawal_amt_f = withdrawal_amt
	where fk_tenant_id = @tenant_id
	and forecast_period = @forecast_period
	   
END
