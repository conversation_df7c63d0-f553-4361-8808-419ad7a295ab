CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMrPlanTask]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS
	
	DECLARE @budget_year INT
	DECLARE @prev_forecast_period INT
	DECLARE @org_version VARCHAR(25) 
	DECLARE @language VARCHAR(10)
	DECLARE @prev_plan_period INT


	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
	SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
	SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
	SET @budget_year = @forecast_period/100


	SET @prev_plan_period = (
	SELECT MAX(forecast_period) FROM tmr_planstratask_setup
	WHERE fk_Tenant_id = @fk_tenant_id
	AND forecast_period < @forecast_period
	AND forecast_period > @budget_year * 100
	)

	IF @prev_plan_period IS NULL
	begin
	Return
	end

PRINT 'START: Fetch Plan Processing/Tasks'


IF (select count(*) from tmr_planstratask_setup WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period) = 0
BEGIN

	INSERT INTO tmr_planstratask_setup (fk_plan_id, fk_plstratask_id, fk_tenant_id, forecast_period, is_reported, updated_by, updated)
	SELECT fk_plan_id, fk_plstratask_id, fk_tenant_id, @forecast_period AS forecast_period, is_reported, @user_id as updated_by, getdate() as updated
	FROM tmr_planstratask_setup
	WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @prev_plan_period

END

PRINT 'END: Fetch Plan Processing/Tasks'


DROP TABLE IF EXISTS #hlptab_linkage

CREATE TABLE #hlptab_linkage (fk_plan_id uniqueidentifier NOT NULL, fk_plstratask_id uniqueidentifier NOT NULL)

INSERT INTO #hlptab_linkage 
-- Planoppgaver uten plan
SELECT '00000000-0000-0000-0000-000000000000', t.pk_strategy_task_id FROM tpl_planningstrategy_tasks t
LEFT JOIN tpl_plan p ON t.fk_tenant_id = p.fk_tenant_id AND t.pk_strategy_task_id = p.fk_strategy_task_id
WHERE t.fk_tenant_id = @fk_tenant_id AND p.pk_plan_id is null
union all
-- Planer uten planoppgaver
SELECT p.pk_plan_id, '00000000-0000-0000-0000-000000000000' 
FROM  tpl_plan p
LEFT JOIN tpl_planningstrategy_tasks t ON t.fk_tenant_id = p.fk_tenant_id AND t.pk_strategy_task_id = p.fk_strategy_task_id
WHERE p.fk_tenant_id = @fk_tenant_id AND t.pk_strategy_task_id is null AND p.is_soft_delete = 0
AND exists (select * from tpl_plan_distribution d WHERE p.pk_plan_id = d.fk_plan_id AND p.fk_tenant_id = d.fk_tenant_id)
union all 
-- Plan med planoppgaver
SELECT p.pk_plan_id, t.pk_strategy_task_id FROM tpl_planningstrategy_tasks t
JOIN tpl_plan p ON t.fk_tenant_id = p.fk_tenant_id AND t.pk_strategy_task_id = p.fk_strategy_task_id AND p.is_soft_delete = 0
WHERE t.fk_tenant_id = @fk_tenant_id AND exists (select * from tpl_plan_distribution d WHERE p.pk_plan_id = d.fk_plan_id AND p.fk_tenant_id = d.fk_tenant_id)


UPDATE mr SET mr.fk_plstratask_id = h.fk_plstratask_id, mr.updated_by = @user_id, mr.updated = getdate()
FROM tmr_planstratask_setup mr
JOIN #hlptab_linkage h ON mr.fk_plan_id = h.fk_plan_id AND h.fk_plan_id != '00000000-0000-0000-0000-000000000000'
WHERE mr.fk_tenant_id = @fk_tenant_id AND mr.forecast_period = @forecast_period
AND not exists (SELECT * FROM #hlptab_linkage h2 WHERE mr.fk_plan_id = h2.fk_plan_id AND mr.fk_plstratask_id = h2.fk_plstratask_id)

UPDATE mr SET mr.fk_plan_id = h.fk_plan_id, mr.updated_by = @user_id, mr.updated = getdate()
FROM tmr_planstratask_setup mr
JOIN #hlptab_linkage h ON mr.fk_plstratask_id = h.fk_plstratask_id AND h.fk_plan_id = '00000000-0000-0000-0000-000000000000'
WHERE mr.fk_tenant_id = @fk_tenant_id AND mr.forecast_period = @forecast_period
AND not exists (SELECT * FROM #hlptab_linkage h2 WHERE mr.fk_plan_id = h2.fk_plan_id AND mr.fk_plstratask_id = h2.fk_plstratask_id)


RETURN 0
