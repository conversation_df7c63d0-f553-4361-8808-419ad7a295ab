CREATE OR ALTER PROCEDURE [dbo].[prcUpdateInvestmentBudgetFromForecast]
 @user_id INT, 
 @tenant_id INT, 
 @forecast_period INT, 
 @change_id_budget_year INT,
 @change_id_next_year INT,
 @alter_code NVARCHAR(25),
 @adjustment_code NVARCHAR(25)=''

AS

DECLARE @budget_year INT = @forecast_period / 100
DECLARE @report NVARCHAR(20)= 'B2A'

DECLARE @fk_inv_action_id_investment INT = (SELECT max(pk_inv_action_id) FROM tfp_inv_header WHERE budget_year = @budget_year AND fk_tenant_id = @tenant_id AND line_order = 1 AND isManuallyAdded = 0 AND action_type = 1)
DECLARE @fk_inv_action_id_investment_next_year INT = (SELECT max(pk_inv_action_id) FROM tfp_inv_header WHERE budget_year = @budget_year+1 AND fk_tenant_id = @tenant_id AND line_order = 1 AND isManuallyAdded = 0 AND action_type = 1)
DECLARE @fk_inv_action_id_vat INT = (SELECT max(pk_inv_action_id) FROM tfp_inv_header WHERE budget_year = @budget_year AND fk_tenant_id = @tenant_id AND line_order = 80 AND isManuallyAdded = 0 AND action_type = 2)
DECLARE @fk_inv_action_id_vat_next_year INT = (SELECT max(pk_inv_action_id) FROM tfp_inv_header WHERE budget_year = @budget_year+1 AND fk_tenant_id = @tenant_id AND line_order = 80 AND isManuallyAdded = 0 AND action_type = 2)

if @fk_inv_action_id_investment_next_year is null
begin

INSERT INTO tfp_inv_header (fk_tenant_id,action_name,budget_year,description,action_type,line_order,isManuallyAdded,updated,updated_by,pk_inv_action_id)
SELECT @tenant_id,'Investeringer' as action_name,@budget_year + 1 as budget_year,newid() as description,
1 as action_type,1 as line_order,0 as isManuallyAdded,getdate() as updated,@user_id as updated_by,
(SELECT MAX(pk_inv_action_id) + 1 from tfp_inv_header);

SET @fk_inv_action_id_investment_next_year  = (SELECT max(pk_inv_action_id) FROM tfp_inv_header WHERE budget_year = @budget_year+1 AND fk_tenant_id = @tenant_id AND line_order = 1 AND isManuallyAdded = 0 AND action_type = 1)

end


IF (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header WHERE fk_tenant_id = @tenant_id 
AND budget_year = @budget_year+1 AND action_type = 2 AND line_order = 1) IS NULL
begin


INSERT INTO tfp_inv_header (fk_tenant_id,action_name,budget_year,description,action_type,line_order,isManuallyAdded,updated,updated_by,pk_inv_action_id)
SELECT @tenant_id,'Låneopptak' as action_name,@budget_year + 1 as budget_year,newid() as description,
2 as action_type,1 as line_order,0 as isManuallyAdded,getdate() as updated,@user_id as updated_by,
(SELECT MAX(pk_inv_action_id) + 1 from tfp_inv_header);


end

IF (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year+1 AND action_type = 3 AND line_order = 10) IS NULL
BEGIN

INSERT INTO tfp_inv_header (fk_tenant_id,action_name,budget_year,description,action_type,line_order,isManuallyAdded,updated,updated_by,pk_inv_action_id)
SELECT @tenant_id,'Overført fra drift' as action_name,@budget_year + 1 as budget_year,newid() as description,
3 as action_type,10 as line_order,0 as isManuallyAdded,getdate() as updated,@user_id as updated_by,
(SELECT MAX(pk_inv_action_id) + 1 from tfp_inv_header);

END

IF (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year+1 AND action_type = 2 AND line_order = 80) IS NULL
BEGIN

INSERT INTO tfp_inv_header (fk_tenant_id,action_name,budget_year,description,action_type,line_order,isManuallyAdded,updated,updated_by,pk_inv_action_id)
SELECT @tenant_id,'Mva kompensasjon' as action_name,@budget_year + 1 as budget_year,newid() as description,
2 as action_type,80 as line_order,0 as isManuallyAdded,getdate() as updated,@user_id as updated_by,
(SELECT MAX(pk_inv_action_id) + 1 from tfp_inv_header);

END


DECLARE @use_net_amount INT = (SELECT count(*) FROM tco_parameters WHERE fk_tenant_id = @tenant_id AND param_name = 'FP_INV_NET_AMOUNTS' AND active = 1 AND param_value = 'TRUE')

DECLARE @next_inv_details_id INT
DECLARE @next_pk_id INT
DECLARE @period INT = CONVERT(INT,CONVERT(VARCHAR(4), @budget_year)+'01')

DECLARE @last_period INT= (@budget_year*100)+12
DECLARE @ub_period INT = (@budget_year*100)+13

DECLARE @last_header_actionid INT

DECLARE @bud_adjustment_code NVARCHAR(50)


DECLARE @forecast_data  TABLE (
	[row_id] BIGINT NOT NULL IDENTITY,
	[pk_id] BIGINT NOT NULL ,
	[fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [forecast_period] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
    [vat_refund] DECIMAL(18, 2) NOT NULL, 
	[year_0_amount] DECIMAL(18, 2) NOT NULL,     
    [year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
    [year_5_amount] DECIMAL(18, 2) NOT NULL, 
    [year_6_amount] DECIMAL(18, 2) NOT NULL, 
    [year_7_amount] DECIMAL(18, 2) NOT NULL, 
    [year_8_amount] DECIMAL(18, 2) NOT NULL,     
    [year_9_amount] DECIMAL(18, 2) NOT NULL, 
    [year_10_amount] DECIMAL(18, 2) NOT NULL, 
    [fk_alter_code] nvarchar(50) NOT NULL,
    [fk_adjustment_code] nvarchar(50) NOT NULL,
    [fk_program_code] nvarchar(50) NOT NULL,
	[type] NVARCHAR(1) NOT NULL,
	[fin_forecast] DECIMAL(18, 2) NOT NULL, 
	[internal_remarks] nvarchar(50)  NULL DEFAULT (''),
	[budget_type] int DEFAULT (0),
	[fk_invDetail_id] BIGINT NOT NULL default (0),
	[is_vat_row] bit not null default(0))


DECLARE @maindata  TABLE (
	[pk_id] INT NOT NULL IDENTITY, 
	[old_pk_id] int NOT NULL,
	[old_fk_invDetail_id] int NOT NULL,
    [fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
	[start_year] INT NOT NULL, 
	[budget_year] INT NOT NULL, 
    [description] NVARCHAR(MAX) NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	[vat_refund] DECIMAL(18, 2) NOT NULL,  
	[year_0_amount] DECIMAL(18, 2) NOT NULL,	
	[year_1_amount] DECIMAL(18, 2) NOT NULL,	 
	[year_2_amount] DECIMAL(18, 2) NOT NULL,
	[type] NVARCHAR(2) NOT NULL, 
    [existing_flag] INT NULL, 
	[fk_change_id] int not null,
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
	[fk_prog_code] nvarchar(50) NOT NULL,
	[fk_portfolio_code] NVARCHAR(25) NOT NULL,
	[fk_invDetail_id] int NOT NULL default (0),
	[is_vat_row] bit not null default(0),
	[fk_inv_action_id] INT NULL
)

CREATE TABLE #compare_table
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	[vat_refund] DECIMAL(18, 2) NOT NULL,  
	[forecast_amount] DECIMAL(18, 2) NOT NULL,	
	[year_1_amount] DECIMAL(18, 2) NOT NULL,	 
	[year_2_amount] DECIMAL(18, 2) NOT NULL,
	[total_forecast_amount] DECIMAL(18, 2) NOT NULL,
	[budget_amount_year] DECIMAL(18, 2) NOT NULL,
	[total_budget_amount] DECIMAL(18, 2) NOT NULL,
	[type] NVARCHAR(2) NOT NULL, 
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
	[fk_prog_code] nvarchar(50) NOT NULL,
	[fk_invDetail_id] BIGINT NOT NULL default (0),
	[is_vat_row] bit not null default(0),
	[fk_inv_action_id] INT NULL
)



CREATE TABLE #temp_financing (
	[fk_tenant_id] INT NOT NULL, 
	[budget_year] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
	[fk_prog_code] nvarchar(50) NOT NULL,
	line_group_id INT NOT NULL, 
	line_group varchar(200) NOT NULL, 
	line_item_id INT NOT NULL, 
	line_item varchar(200) NOT NULL, 
	forecast_amount DECIMAL(18, 2) NOT NULL,  
	budget_amount DECIMAL(18, 2) NOT NULL,
	[fk_inv_action_id] INT DEFAULT 0 NOT NULL)

	CREATE TABLE #TEMP_header_new
(	[pk_id] INT NOT NULL IDENTITY,
	line_group_id INT NOT NULL,
	line_item_id INT NOT NULL,
	[fk_tenant_id] INT NOT NULL, 
	[pk_inv_action_id] INT NOT NULL, 
	action_name nvarchar(150) NOT NULL,
	budget_year INT NOT NULL,
    [description] UNIQUEIDENTIFIER NOT NULL, 
	[action_type] [int] NOT NULL,
	[line_order] [int] NOT NULL,
	[isManuallyAdded] [int] NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] INT NOT NULL
)



IF (SELECT COUNT(*) FROM tco_application_flag WHERE flag_name = 'MR_BUDREG_FROM_INV' AND fk_tenant_id  = @tenant_id AND period = @forecast_period) > 0
BEGIN
PRINT 'Already run'
return
END


INSERT INTO #compare_table (
[fk_investment_id],[fk_tenant_id],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],
[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[vat_rate],[vat_refund],[forecast_amount],[year_1_amount],[year_2_amount],
[total_forecast_amount],[budget_amount_year],[total_budget_amount],[type],[fk_alter_code],
[fk_adjustment_code],[fk_prog_code],[fk_invDetail_id],[is_vat_row])
SELECT [fk_investment_id],[fk_tenant_id],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],
[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[vat_rate],[vat_refund],[year_1_amount] as [forecast_amount],
0 as [year_1_amount],[year_2_amount],
[total_forecast_amount] = year_0_amount + year_1_amount + year_2_amount + year_3_amount + year_4_amount+ year_5_amount + year_6_amount + year_7_amount + year_8_amount + year_9_amount + year_10_amount, 
0 AS [budget_amount_year],0 AS [total_budget_amount],[type],[fk_alter_code],
[fk_adjustment_code], [fk_program_code], [fk_invDetail_id], [is_vat_row]
FROM tmr_investment_status_detail d 
JOIN tco_accounts ac ON d.fk_tenant_id = ac.pk_tenant_id AND d.fk_account_code = ac.pk_account_code AND d.forecast_period/100 BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART(YEAR, ac.dateto)
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = 'B2A' --AND rl.line_item_id = 500
WHERE fk_tenant_id = @tenant_id AND forecast_period = @forecast_period

INSERT INTO #compare_table (
[fk_investment_id],[fk_tenant_id],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],
[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[vat_rate],[vat_refund],[forecast_amount],[year_1_amount],[year_2_amount],
[total_forecast_amount],[budget_amount_year],[total_budget_amount],[type],[fk_alter_code],
[fk_adjustment_code],[fk_prog_code],[fk_invDetail_id],[is_vat_row])
SELECT [fk_investment_id],[fk_tenant_id],[fk_account_code],[fk_department_code],[fk_function_code],[fk_project_code],
[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4], 0 as [vat_rate],0 as [vat_refund],0 as [forecast_amount],0 as [year_1_amount],0 as [year_2_amount],
[total_forecast_amount] =0, 
inv_amt_year+inv_change_amt+unaprv_bud_change AS [budget_amount_year],inv_total_amt AS [total_budget_amount],[type],'' AS [fk_alter_code],
'' AS [fk_adjustment_code], [fk_prog_code], -1 [fk_invDetail_id], 0 [is_vat_row]
FROM tmr_inv_data_warehouse d 
JOIN tco_accounts ac ON d.fk_tenant_id = ac.pk_tenant_id AND d.fk_account_code = ac.pk_account_code AND d.forecast_period/100 BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART(YEAR, ac.dateto)
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = 'B2A' --AND rl.line_item_id = 500
WHERE fk_tenant_id = @tenant_id AND forecast_period = @forecast_period



UPDATE #compare_table SET year_1_amount =  forecast_amount - budget_amount_year

-- this old logic was commented out in release 105 (bug 61865) UPDATE #compare_table set year_2_amount = total_forecast_amount - total_budget_amount - year_1_amount

UPDATE #compare_table set year_2_amount = year_1_amount * -1


IF @forecast_period IN (@last_period, @ub_period)
BEGIN

INSERT INTO @forecast_data (pk_id, fk_investment_id, fk_tenant_id, forecast_period, fk_account_code, fk_department_code,
 fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, vat_rate, vat_refund, 
 year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,
 year_9_amount,year_10_amount,fk_alter_code,fk_adjustment_code,fk_program_code, year_0_amount, type,
 fin_forecast,internal_remarks,budget_type, fk_invDetail_id, is_vat_row)
SELECT 0 AS pk_id,fk_investment_id,fk_tenant_id,@forecast_period forecast_period,fk_account_code,fk_department_code,fk_function_code,
fk_project_code,'' as free_dim_1,'' as free_dim_2,'' as free_dim_3,'' as free_dim_4,0 as vat_rate,0 as vat_refund,sum(year_1_amount),sum(year_2_amount),
0 AS year_3_amount,0 AS year_4_amount,0 AS year_5_amount,0 AS year_6_amount,0 AS year_7_amount,0 AS year_8_amount,0 AS year_9_amount,0 AS year_10_amount,
'' AS fk_alter_code,'' AS fk_adjustment_code,fk_prog_code,0 AS year_0_amount,type, 
0 AS fin_forecast,'' AS internal_remarks,0 AS budget_type, -1 AS fk_invDetail_id, 0 AS is_vat_row
FROM #compare_table
WHERE fk_investment_id > 0
GROUP BY fk_investment_id, fk_tenant_id, fk_account_code, fk_department_code,
 fk_function_code, fk_project_code, fk_prog_code,type

 END

IF @forecast_period NOT IN (@last_period, @ub_period)
BEGIN

INSERT INTO @forecast_data (pk_id, fk_investment_id, fk_tenant_id, forecast_period, fk_account_code, fk_department_code,
 fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, vat_rate, vat_refund, 
 year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,
 year_9_amount,year_10_amount,fk_alter_code,fk_adjustment_code,fk_program_code, year_0_amount, type,
 fin_forecast,internal_remarks,budget_type, fk_invDetail_id, is_vat_row)
SELECT pk_id,fk_investment_id,fk_tenant_id,forecast_period,fk_account_code,fk_department_code,fk_function_code,
fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,year_1_amount,year_2_amount,
year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,
fk_alter_code,fk_adjustment_code,fk_program_code,year_0_amount,type, 
fin_forecast,'' as internal_remarks,budget_type, fk_invDetail_id, is_vat_row
FROM tmr_investment_status_detail d WHERE fk_tenant_id = @tenant_id AND forecast_period = @forecast_period
AND budget_type != 1000

END

DELETE FROM @forecast_data WHERE year_1_amount = 0 AND year_2_amount = 0


PRINT 'insert into maindata'
IF @forecast_period != @ub_period
BEGIN

INSERT INTO @maindata (old_pk_id, old_fk_invDetail_id,fk_investment_id, fk_tenant_id, start_year, budget_year,description,fk_account_code, 
    fk_department_code, fk_function_code, fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,
	year_0_amount,year_1_amount,year_2_amount,
	type,existing_flag,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,fk_portfolio_code, is_vat_row, fk_invDetail_id)

SELECT f.pk_id, f.fk_invDetail_id,f.fk_investment_id, f.fk_tenant_id, i.start_year, @budget_year, '' as description, f.fk_account_code, 
f.fk_department_code, f.fk_function_code, f.fk_project_code,f.free_dim_1,f.free_dim_2,f.free_dim_3,f.free_dim_4,f.vat_rate,f.vat_refund,
	0 as year_0_amount, 
	f.year_1_amount,
	year_2_amount = f.year_0_amount + f.year_2_amount + f.year_3_amount + f.year_4_amount + f.year_5_amount + f.year_6_amount + f.year_7_amount + f.year_8_amount + f.year_9_amount + f.year_10_amount,
	f.type, 0 as existing_flag, @change_id_budget_year,f.fk_alter_code,f.fk_adjustment_code, f.fk_program_code, i.fk_portfolio_code,
	f.is_vat_row, f.fk_invDetail_id
FROM @forecast_data f
JOIN tco_investments i ON f.fk_tenant_id = i.fk_tenant_id AND f.fk_investment_id = i.pk_investment_id

END

IF @change_id_next_year != 0 
BEGIN

INSERT INTO @maindata (old_pk_id, old_fk_invDetail_id,fk_investment_id, fk_tenant_id, start_year, budget_year,description,fk_account_code, 
    fk_department_code, fk_function_code, fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,
	year_0_amount, year_1_amount,year_2_amount,
	type,existing_flag,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,fk_portfolio_code,is_vat_row, fk_invDetail_id)

SELECT f.pk_id, f.fk_invDetail_id,f.fk_investment_id, f.fk_tenant_id, i.start_year, @budget_year+1, '' as description, f.fk_account_code, 
f.fk_department_code, f.fk_function_code, f.fk_project_code,f.free_dim_1,f.free_dim_2,f.free_dim_3,f.free_dim_4,f.vat_rate,f.vat_refund,
	year_0_amount = f.year_1_amount,
	year_1_amount = f.year_0_amount + f.year_2_amount + f.year_3_amount + f.year_4_amount + f.year_5_amount + f.year_6_amount + f.year_7_amount + f.year_8_amount + f.year_9_amount + f.year_10_amount,
	year_2_amount = 0,	
	f.type, 0 as existing_flag, @change_id_next_year,f.fk_alter_code,f.fk_adjustment_code, f.fk_program_code, i.fk_portfolio_code, f.is_vat_row, f.fk_invDetail_id
FROM @forecast_data f
JOIN tco_investments i ON f.fk_tenant_id = i.fk_tenant_id AND f.fk_investment_id = i.pk_investment_id

END

UPDATE @maindata SET fk_alter_code = @alter_code, fk_adjustment_code = @adjustment_code
WHERE fk_alter_code = '' 

UPDATE b SET fk_invDetail_id = a.pk_id
FROM @maindata a, @maindata b
WHERE a.budget_year = b.budget_year
AND a.old_pk_id = b.old_fk_invDetail_id
AND b.is_vat_row = 1

UPDATE a SET fk_invDetail_id = 0
FROM @maindata a
WHERE a.is_vat_row = 0

UPDATE @maindata SET fk_inv_action_id = @fk_inv_action_id_investment WHERE budget_year = @budget_year AND fk_investment_id > 0

UPDATE @maindata SET fk_inv_action_id = @fk_inv_action_id_investment_next_year WHERE budget_year = @budget_year +1 AND fk_investment_id > 0


UPDATE @maindata SET type = 'i'
FROM @maindata a, gmd_reporting_line b, tco_accounts c
WHERE a.fk_account_code = c.pk_account_code
AND a.fk_tenant_id = c.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND b.report = @report
AND b.line_item_id IN (500, 630)
AND a.type = ''


UPDATE @maindata SET type = 'f' WHERE type = ''

IF @use_net_amount = 1 
BEGIN

UPDATE @maindata SET fk_inv_action_id = @fk_inv_action_id_vat
FROM @maindata a, gmd_reporting_line b, tco_accounts c
WHERE a.fk_account_code = c.pk_account_code
AND a.fk_tenant_id = c.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND b.report = @report
AND b.line_item_id = 630
AND a.budget_year = @budget_year

UPDATE @maindata SET fk_inv_action_id = @fk_inv_action_id_vat_next_year
FROM @maindata a, gmd_reporting_line b, tco_accounts c
WHERE a.fk_account_code = c.pk_account_code
AND a.fk_tenant_id = c.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND b.report = @report
AND b.line_item_id = 630
AND a.budget_year = @budget_year+1

END

BEGIN
PRINT 'Start financing transactions'

IF @forecast_period != @ub_period
begin

INSERT INTO #temp_financing (fk_tenant_id, budget_year, fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, forecast_amount,  budget_amount)

SELECT a.fk_tenant_id, @budget_year,a.fk_account_code, a.fk_department_code, a.fk_function_code, ISNULL(a.fk_prog_code,'1'),
rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, 0 AS forecast_amount,SUM(bud_amt_year)amount 
from tmr_data_warehouse a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code AND a.forecast_period/100 BETWEEN datepart(year,b.datefrom) and datepart(year, b.dateTo)
JOIN gmd_reporting_line rl on b.fk_kostra_account_code = rl.fk_kostra_account_code and rl.report = @report
where a.fk_tenant_id = @tenant_id
and forecast_period = @forecast_period
and rl.line_item_id NOT IN ('500')
GROUP BY a.fk_tenant_id, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.fk_prog_code
HAVING sum(bud_amt_year)!=0

INSERT INTO #temp_financing (fk_tenant_id, budget_year,fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, forecast_amount,  budget_amount)

select a.fk_tenant_id,@budget_year,a.fk_account_code, a.department_code, a.fk_function_code, ISNULL(a.fk_prog_code,'1'),
rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,  SUM(amount_year_1) forecast_amount, convert(dec(18,2),0) budget_amount
from tbu_forecast_transactions a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code AND a.forecast_period/100 BETWEEN datepart(year,b.datefrom) and datepart(year, b.dateTo)
JOIN gmd_reporting_line rl on b.fk_kostra_account_code = rl.fk_kostra_account_code and rl.report = @report
where a.fk_tenant_id = @tenant_id
and forecast_period = @forecast_period
and rl.line_item_id != '500'
group by a.fk_tenant_id,rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, a.fk_account_code, a.department_code, a.fk_function_code, a.fk_prog_code
having sum(amount_year_1) != 0
order by rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item

INSERT INTO #temp_financing (fk_tenant_id, budget_year,fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, forecast_amount,  budget_amount)

select a.fk_tenant_id,@budget_year,a.fk_account_code, a.fk_department_code, a.fk_function_code, ISNULL(a.fk_program_code,'1'),
rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,  SUM(a.year_1_amount) forecast_amount, convert(dec(18,2),0) budget_amount
from tmr_investment_status_detail a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code AND a.forecast_period/100 BETWEEN datepart(year,b.datefrom) and datepart(year, b.dateTo)
JOIN gmd_reporting_line rl on b.fk_kostra_account_code = rl.fk_kostra_account_code and rl.report = @report
where a.fk_tenant_id = @tenant_id
and forecast_period = @forecast_period
and rl.line_item_id != '500'
and a.budget_type = 1000
group by a.fk_tenant_id,rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.fk_program_code
having sum(a.year_1_amount) != 0
order by rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item


UPDATE #temp_financing 
SET fk_inv_action_id = (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year AND action_type = 2 AND line_order = 1)
WHERE line_group_id = 6000 AND line_item_id = 600 AND budget_year = @budget_year

UPDATE #temp_financing 
SET fk_inv_action_id = (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year AND action_type = 3 AND line_order = 10)
WHERE line_group_id = 7000 AND line_item_id = 710 AND budget_year = @budget_year

UPDATE #temp_financing 
SET fk_inv_action_id = (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year AND action_type = 2 AND line_order = 80)
WHERE line_group_id = 6000 AND line_item_id = 630 AND budget_year = @budget_year

END 


IF @change_id_next_year != 0 
BEGIN


INSERT INTO #temp_financing (fk_tenant_id, budget_year, fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, forecast_amount,  budget_amount)

SELECT a.fk_tenant_id, @budget_year+1,a.fk_account_code, a.fk_department_code, a.fk_function_code, ISNULL(a.fk_prog_code,'1'),
rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, 0 AS forecast_amount,SUM(bud_amt_year)amount 
from tmr_data_warehouse a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code AND a.forecast_period/100 BETWEEN datepart(year,b.datefrom) and datepart(year, b.dateTo)
JOIN gmd_reporting_line rl on b.fk_kostra_account_code = rl.fk_kostra_account_code and rl.report = 'b2A'
where a.fk_tenant_id = @tenant_id
and forecast_period = @forecast_period
and rl.line_item_id != '500'
GROUP BY a.fk_tenant_id, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.fk_prog_code
HAVING sum(bud_amt_year)!=0

INSERT INTO #temp_financing (fk_tenant_id, budget_year,fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, forecast_amount,  budget_amount)

select a.fk_tenant_id,@budget_year+1,a.fk_account_code, a.department_code, a.fk_function_code, ISNULL(a.fk_prog_code,'1'),
rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,  SUM(amount_year_1) forecast_amount, convert(dec(18,2),0) budget_amount
from tbu_forecast_transactions a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code AND a.forecast_period/100 BETWEEN datepart(year,b.datefrom) and datepart(year, b.dateTo)
JOIN gmd_reporting_line rl on b.fk_kostra_account_code = rl.fk_kostra_account_code and rl.report = 'b2A'
where a.fk_tenant_id = @tenant_id
and forecast_period = @forecast_period
and rl.line_item_id != '500'
group by a.fk_tenant_id,rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, a.fk_account_code, a.department_code, a.fk_function_code, a.fk_prog_code
having sum(amount_year_1) != 0
order by rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item

INSERT INTO #temp_financing (fk_tenant_id, budget_year,fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, forecast_amount,  budget_amount)

select a.fk_tenant_id,@budget_year+1,a.fk_account_code, a.fk_department_code, a.fk_function_code, ISNULL(a.fk_program_code,'1'),
rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,  SUM(a.year_1_amount) forecast_amount, convert(dec(18,2),0) budget_amount
from tmr_investment_status_detail a
JOIN tco_accounts b on a.fk_tenant_id = b.pk_tenant_id and a.fk_account_code = b.pk_account_code AND a.forecast_period/100 BETWEEN datepart(year,b.datefrom) and datepart(year, b.dateTo)
JOIN gmd_reporting_line rl on b.fk_kostra_account_code = rl.fk_kostra_account_code and rl.report = 'b2A'
where a.fk_tenant_id = @tenant_id
and forecast_period = @forecast_period
and rl.line_item_id != '500' and a.budget_type = 1000
group by a.fk_tenant_id,rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.fk_program_code
having sum(a.year_1_amount) != 0
order by rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item


UPDATE #temp_financing 
SET fk_inv_action_id = (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year+1 AND action_type = 2 AND line_order = 1)
WHERE line_group_id = 6000 AND line_item_id = 600 AND budget_year = @budget_year+1

UPDATE #temp_financing 
SET fk_inv_action_id = (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year+1 AND action_type = 3 AND line_order = 10)
WHERE line_group_id = 7000 AND line_item_id = 710 AND budget_year = @budget_year+1

UPDATE #temp_financing 
SET fk_inv_action_id = (SELECT MIN(pk_inv_action_id) FROM tfp_inv_header 
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year+1 AND action_type = 2 AND line_order = 80)
WHERE line_group_id = 6000 AND line_item_id = 630 AND budget_year = @budget_year+1

END


UPDATE #temp_financing SET fk_prog_code = (SELECT MIN(pk_prog_code) FROM tco_inv_program WHERE default_flag = 1 AND fk_tenant_id = @tenant_id) WHERE fk_prog_code is null

UPDATE #temp_financing SET fk_prog_code = (SELECT MIN(pk_prog_code) FROM tco_inv_program WHERE default_flag = 1 AND fk_tenant_id = @tenant_id) WHERE fk_prog_code = ''




INSERT INTO #TEMP_header_new (fk_tenant_id,line_group_id, line_item_id, pk_inv_action_id, action_name,budget_year,description, action_type, line_order, isManuallyAdded, updated, updated_by)
SELECT DISTINCT fk_tenant_id,line_group_id, line_item_id, 0, line_item, budget_year, '00000000-0000-0000-0000-000000000000', 2, 0,1,getdate(), @user_id
FROM #temp_financing
WHERE fk_inv_action_id = 0 AND line_group_id IN (6000,7000)

INSERT INTO #TEMP_header_new (fk_tenant_id,line_group_id, line_item_id, pk_inv_action_id, action_name,budget_year,description, action_type, line_order, isManuallyAdded, updated, updated_by)
SELECT DISTINCT fk_tenant_id,line_group_id, line_item_id, 0, line_item, budget_year, '00000000-0000-0000-0000-000000000000', 1, 0,1,getdate(), @user_id
FROM #temp_financing
WHERE fk_inv_action_id = 0 AND line_group_id = 5000

BEGIN TRANSACTION

SET @last_header_actionid = (SELECT max(pk_inv_action_id) FROM tfp_inv_header)

UPDATE #TEMP_header_new SET pk_inv_action_id = @last_header_actionid + pk_id

PRINT 'Insert into tfp_inv_header'

INSERT INTO tfp_inv_header (fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by,pk_inv_action_id)
SELECT fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by, pk_inv_action_id
FROM #TEMP_header_new

UPDATE #temp_financing SET fk_inv_action_id = b.pk_inv_action_id
FROM #temp_financing a, #TEMP_header_new b
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.line_group_id = b.line_group_id
AND a.line_item_id = b.line_item_id
AND a.budget_year = b.budget_year

COMMIT



INSERT INTO @maindata (old_pk_id, old_fk_invDetail_id,fk_investment_id, fk_tenant_id, start_year, budget_year,description,fk_account_code, 
    fk_department_code, fk_function_code, fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,
	year_0_amount, year_1_amount,year_2_amount,
	type,existing_flag,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,fk_portfolio_code,is_vat_row, fk_invDetail_id, fk_inv_action_id)
SELECT 0 AS old_pk_id, 0 AS old_fk_invDetail_id,0 AS fk_investment_id, fk_tenant_id,0 AS  start_year, budget_year,'' AS description,fk_account_code, 
    fk_department_code, fk_function_code, '' as fk_project_code,'' AS free_dim_1,'' AS free_dim_2,'' AS free_dim_3,'' AS free_dim_4,0 vat_rate,0 AS vat_refund,
	0 AS year_0_amount, sum(forecast_amount-  budget_amount) AS year_1_amount,
	year_2_amount =  sum(budget_amount - forecast_amount),
	'f' AS type,0 AS existing_flag,@change_id_budget_year AS  fk_change_id, @alter_code AS  fk_alter_code,@adjustment_code AS  fk_adjustment_code,fk_prog_code,'' AS fk_portfolio_code,0 AS is_vat_row, 0 AS fk_invDetail_id,
	fk_inv_action_id
FROM #temp_financing WHERE budget_year = @budget_year
group by fk_tenant_id,fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, fk_inv_action_id, budget_year


INSERT INTO @maindata (old_pk_id, old_fk_invDetail_id,fk_investment_id, fk_tenant_id, start_year, budget_year,description,fk_account_code, 
    fk_department_code, fk_function_code, fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,
	year_0_amount, year_1_amount,year_2_amount,
	type,existing_flag,fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,fk_portfolio_code,is_vat_row, fk_invDetail_id, fk_inv_action_id)
SELECT 0 AS old_pk_id, 0 AS old_fk_invDetail_id,0 AS fk_investment_id, fk_tenant_id,0 AS  start_year, budget_year,'' AS description,fk_account_code, 
    fk_department_code, fk_function_code, '' as fk_project_code,'' AS free_dim_1,'' AS free_dim_2,'' AS free_dim_3,'' AS free_dim_4,0 vat_rate,0 AS vat_refund,
	sum(forecast_amount - budget_amount) AS year_0_amount,  sum(budget_amount - forecast_amount) AS year_1_amount,
	year_2_amount = 0,
	'f' AS type,0 AS existing_flag,@change_id_next_year AS  fk_change_id, @alter_code AS  fk_alter_code,@adjustment_code AS  fk_adjustment_code,fk_prog_code,'' AS fk_portfolio_code,0 AS is_vat_row, 0 AS fk_invDetail_id,
	fk_inv_action_id
FROM #temp_financing WHERE budget_year = @budget_year + 1
group by fk_tenant_id,fk_account_code, fk_department_code, fk_function_code, fk_prog_code,
line_group_id, line_group, line_item_id, line_item, fk_inv_action_id, budget_year

DELETE FROM @maindata WHERE year_0_amount = 0 AND year_1_amount = 0 AND year_2_amount = 0

PRINT 'End financing transactions'

END


BEGIN

BEGIN TRANSACTION

SET @bud_adjustment_code = (SELECT prefix + '-' + CONVERT(VARCHAR(15), next_value) FROM tco_counters WHERE fk_tenant_id = @tenant_id AND counter_id = 'BUDADJCODE')

UPDATE tco_counters SET next_value = next_value + 1
WHERE fk_tenant_id = @tenant_id
AND counter_id = 'BUDADJCODE'

COMMIT

INSERT INTO tco_user_adjustment_codes (fk_tenant_id,pk_adj_code,org_level_value,description,description_id,status,fk_user_id,updated,budget_year,org_level)
SELECT @tenant_id,@bud_adjustment_code ,0 AS org_level_value,'Oppdatering fra prognose finansiering' as description,
NEWID() AS description_id,1 AS status,@user_id,getdate() as updated,@budget_year,0 as org_level

BEGIN TRANSACTION

--INSERT INTO tco_fin_forecast_transferlog (pk_id, fk_tenant_id, updated, updated_by)
--SELECT DISTINCT old_pk_id, fk_tenant_id, getdate(), @user_id
--FROM @maindata


INSERT INTO tco_application_flag (flag_name,flag_key_id,flag_status,fk_tenant_id,budget_year,period,updated,updated_by)
VALUES ('MR_BUDREG_FROM_INV',0,0,@tenant_id,0,@forecast_period,GETDATE(),@user_id)


IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END 


SET @next_inv_details_id = (SELECT MAX(pk_id)+100 FROM tco_investment_detail)

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END 

SET IDENTITY_INSERT tco_investment_detail ON 

INSERT INTO tco_investment_detail (pk_id,fk_investment_id,fk_tenant_id,description,fk_account_code,fk_department_code,fk_function_code,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,type,updated,updated_by,existing_flag,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_year,fk_alter_code,fk_adjustment_code,fk_change_id,fk_program_code, is_vat_row, fk_invDetail_id, approved_cost)
SELECT pk_id + @next_inv_details_id,fk_investment_id,fk_tenant_id,description,fk_account_code,fk_department_code,fk_function_code,vat_rate,vat_refund,
year_1_amount = CASE WHEN budget_year - start_year = 0 THEN year_1_amount 
					 WHEN budget_year - start_year = 1 THEN year_0_amount
ELSE 0 END,
year_2_amount = CASE WHEN budget_year - start_year = 0 THEN year_2_amount
					 WHEN budget_year - start_year = 1 THEN year_1_amount
					 WHEN budget_year - start_year = 2 THEN year_0_amount
					 ELSE 0 END,
year_3_amount = CASE WHEN budget_year - start_year = 1 THEN year_2_amount
					 WHEN budget_year - start_year = 2 THEN year_1_amount
					 WHEN budget_year - start_year = 3 THEN year_0_amount
					 ELSE 0 END,
year_4_amount = CASE WHEN budget_year - start_year = 2 THEN year_2_amount
					 WHEN budget_year - start_year = 3 THEN year_1_amount
					 WHEN budget_year - start_year = 4 THEN year_0_amount
					 ELSE 0 END,
year_5_amount = CASE WHEN budget_year - start_year = 3 THEN year_2_amount
					 WHEN budget_year - start_year = 4 THEN year_1_amount
					 WHEN budget_year - start_year = 5 THEN year_0_amount
					 ELSE 0 END,
year_6_amount = CASE WHEN budget_year - start_year = 4 THEN year_2_amount
					 WHEN budget_year - start_year = 5 THEN year_1_amount
					 WHEN budget_year - start_year = 6 THEN year_0_amount
					 ELSE 0 END,
year_7_amount = CASE WHEN budget_year - start_year = 5 THEN year_2_amount
					 WHEN budget_year - start_year = 6 THEN year_1_amount
					 WHEN budget_year - start_year = 7 THEN year_0_amount
					 ELSE 0 END,
year_8_amount = CASE WHEN budget_year - start_year = 6 THEN year_2_amount
					 WHEN budget_year - start_year = 7 THEN year_1_amount
					 WHEN budget_year - start_year = 8 THEN year_0_amount
					 ELSE 0 END,
year_9_amount = CASE WHEN budget_year - start_year = 7 THEN year_2_amount
					 WHEN budget_year - start_year = 8 THEN year_1_amount
					 WHEN budget_year - start_year = 9 THEN year_0_amount
					 ELSE 0 END,
year_10_amount= CASE WHEN budget_year - start_year = 8 THEN year_2_amount
					 WHEN budget_year - start_year = 9 THEN year_1_amount
					 WHEN budget_year - start_year = 10 THEN year_0_amount
					 ELSE 0 END,
type,GETDATE() as updated, @user_id AS updated_by,
existing_flag,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_year,fk_alter_code,
fk_adjustment_code,fk_change_id,fk_prog_code, is_vat_row, 
fk_invDetail_id = case when fk_invDetail_id =0 then 0 else fk_invDetail_id+@next_inv_details_id end, 
0 as approved_cost
FROM @maindata
WHERE fk_investment_id > 0

SET IDENTITY_INSERT tco_investment_detail OFF

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

SET @next_pk_id = (SELECT MAX(pk_id) FROM tfp_inv_transactions)

INSERT INTO tfp_inv_transactions (pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code)
SELECT pk_id +@next_pk_id, fk_inv_action_id, fk_investment_id, fk_tenant_id, budget_year,fk_account_code,fk_function_code,fk_department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount as year_2_amount,0 AS year_3_amount,0 AS year_4_amount,getdate(),type,@user_id ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,pk_id + @next_inv_details_id as fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code
FROM @maindata WHERE fk_investment_id != 0

INSERT INTO tfp_inv_transactions (pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code)
SELECT pk_id +@next_pk_id, fk_inv_action_id, fk_investment_id, fk_tenant_id, budget_year,fk_account_code,fk_function_code,fk_department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount as year_2_amount,0 AS year_3_amount,0 AS year_4_amount,getdate(),type,@user_id ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,0 as fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code
FROM @maindata WHERE fk_investment_id = 0


IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

UPDATE a SET a.inv_status = 0
FROM tco_inv_budgetyear_config a
JOIN (SELECT DISTINCT fk_tenant_id, budget_year, fk_investment_id FROM @maindata) b
ON a.fk_tenant_id = b.fk_tenant_id AND a.fk_investment_id = b.fk_investment_id AND a.budget_year = b.budget_year
WHERE a.inv_status = 3


IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

PRINT 'Insert into tbu_trans_detail'


IF (SELECT count(*) FROM tfp_budget_changes WHERE fk_tenant_id = @tenant_id AND pk_change_id = @change_id_budget_year) = 1

BEGIN

INSERT INTO tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,total_amount,tax_flag,holiday_flag,fk_pension_type,fk_action_id,fk_investment_id,fk_portfolio_code,fk_prog_code, fk_adjustment_code)
SELECT NEWID() AS pk_id,NEWID() AS bu_trans_id,fk_tenant_id,0 AS action_type,0 AS line_order,fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' as resource_id,0 as fk_employment_id,'' as description,budget_year,@period AS period,0 AS budget_type,year_1_amount,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,GETDATE(),@user_id as updated_by,0 as allocation_pct,year_1_amount as total_amount,0 as tax_flag,0 as holiday_flag,'' as fk_pension_type,0 as fk_action_id,fk_investment_id,fk_portfolio_code,fk_prog_code,@bud_adjustment_code
FROM @maindata
WHERE budget_year = @budget_year

END

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

COMMIT
END
GO


