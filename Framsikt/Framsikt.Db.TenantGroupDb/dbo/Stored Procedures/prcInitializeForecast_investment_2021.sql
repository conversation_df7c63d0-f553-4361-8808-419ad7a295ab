
CREATE OR ALTER PROCEDURE [dbo].[prcInitializeForecast_investment_2021]
@forecast_period INT,  @fk_tenant_id INT, @user_id INT, @investment_init_type NVARCHAR(25) = ''

AS

SET NOCOUNT ON;

DECLARE @budget_year INT
DECLARE @last_period INT
DECLARE @ub_period INT
DECLARE @month_remaining dec(18,6) 
DECLARE @month_ytd dec(18,6)
DECLARE @prev_forecast_period INT
DECLARE @service_level INT
DECLARE @periods_ytd INT
DECLARE @periods_remaining INT
DECLARE @org_version VARCHAR(25) 
DECLARE @inv_org_level INT
DECLARE @unapproved_check INT 
DECLARE @vat_account NVARCHAR(25)

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

DECLARE @change_id_table TABLE (fk_change_id INT, budget_year INT)

SET @periods_ytd= convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))
SET @periods_remaining = 12 - @periods_ytd;

SET @inv_org_level = 2

IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1) = 1
begin
SET @inv_org_level = (
 SELECT org_level = 
CASE	WHEN param_value = 'org_id_2' THEN 2 
		WHEN param_value = 'org_id_3' THEN 3
		WHEN param_value = 'org_id_4' THEN 4
		WHEN param_value = 'org_id_5' THEN 5 
ELSE 2 END FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1)
end

PRINT 'Start procedure '  + convert(varchar(400),GETDATE());


DECLARE @period_table TABLE (pk_id INT IDENTITY NOT NULL,
	[period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL) 


CREATE TABLE #temp_proj_transactions
(
	   [pk_id] [uniqueidentifier] NOT NULL,
       [trans_id] [uniqueidentifier] NOT NULL,
       [fk_tenant_id] [int] NOT NULL,
       [forecast_period] [int] NOT NULL,
       [fk_account_code] [nvarchar](25) NOT NULL,
       [fk_function_code] [nvarchar](25) NOT NULL,
       [fk_department_code] [nvarchar](25) NOT NULL,
       [fk_main_project_code] [nvarchar](25) NULL,
       [fk_project_code] [nvarchar](25) NOT NULL,
       [free_dim_1] [nvarchar](25) NOT NULL,
       [free_dim_2] [nvarchar](25) NOT NULL,
       [free_dim_3] [nvarchar](25) NOT NULL,
       [free_dim_4] [nvarchar](25) NOT NULL,
       [vat_rate] [decimal](18, 2) NOT NULL,
       [vat_refund] [decimal](18, 2) NOT NULL,
       [year] [int] NOT NULL,
       [amount] [decimal](18, 2) NOT NULL,
       [updated] [datetime] NOT NULL,
       [updated_by] [int] NOT NULL,
       [fk_alter_code] [nvarchar](50) NOT NULL,
       [fk_adjustment_code] [nvarchar](50) NOT NULL,
       [is_vat_row] [bit] NOT NULL DEFAULT ((0)),
       [fk_proj_trans_id] [uniqueidentifier] NULL,
       [description] [nvarchar](max) NULL,
       [is_change] [int] NULL,
	   [inv_status] INT NULL,
       [gl_amount] [decimal](18, 2) NOT NULL,
       [use_budget] INT NOT NULL DEFAULT 0)




DECLARE @inv_gl_table TABLE
(
	[pk_id] BIGINT NOT NULL IDENTITY,
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [gl_year] INT NOT NULL,
    [forecast_period] INT NOT NULL, 
	[fk_main_project_code] NVARCHAR(25) NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL,  
	[gl_amount] DECIMAL(18, 2) NOT NULL, 
	[type] NVARCHAR (2) NOT NULL,
    [inv_status] INT NULL
)



SET @budget_year = @forecast_period/100
SET @last_period = (@budget_year*100)+12
SET @ub_period = (@budget_year*100)+13
SET @month_remaining = (12 - CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))/12
SET @month_ytd = (CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)


IF @forecast_period = @ub_period AND @org_version is null
BEGIN

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period -1 BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

END

SET @vat_account = (
select MIN(acc_value) from  tmd_acc_defaults b 
WHERE  module = 'INV' and link_type = 'VAT_COST'
AND fk_tenant_id = @fk_tenant_id
AND fk_org_version = @org_version)


/* removed as part of conversion to user adjustment codes on inv model 
SET @unapproved_check = (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = 'MRI_INCL_UNAPPR_BUDCH' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @fk_tenant_id)

IF @unapproved_check = 0 
BEGIN 

INSERT INTO @change_id_table (fk_change_id)
		select pk_change_id from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year < @budget_year
        UNION
        select pk_change_id from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year = @budget_year
        --and org_budget_flag = 1
		AND rebudget_approved = 1

END

ELSE
*/

BEGIN 

INSERT INTO @change_id_table (fk_change_id,budget_year)
		select pk_change_id,budget_year from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year <= @budget_year
        --UNION
        --select pk_change_id from tfp_budget_changes
        --where fk_tenant_id = @fk_tenant_id
        --and budget_year = @budget_year
        --and org_budget_flag = 1

END

BEGIN 

INSERT INTO @period_table (period, fk_tenant_id)
VALUES 
((@budget_year*100)+1, @fk_tenant_id),
((@budget_year*100)+2, @fk_tenant_id),
((@budget_year*100)+3, @fk_tenant_id),
((@budget_year*100)+4, @fk_tenant_id),
((@budget_year*100)+5, @fk_tenant_id),
((@budget_year*100)+6, @fk_tenant_id),
((@budget_year*100)+7, @fk_tenant_id),
((@budget_year*100)+8, @fk_tenant_id),
((@budget_year*100)+9, @fk_tenant_id),
((@budget_year*100)+10, @fk_tenant_id),
((@budget_year*100)+11, @fk_tenant_id),
((@budget_year*100)+12, @fk_tenant_id);

DELETE FROM @period_table WHERE period <= @forecast_period

END


PRINT 'Fetch budget amount ' + convert(varchar(400),GETDATE());


INSERT INTO #temp_proj_transactions (pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,
fk_function_code,fk_department_code, fk_main_project_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
vat_rate,vat_refund,year,amount,updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,
fk_proj_trans_id,description,is_change, inv_status, gl_amount)

SELECT newid() as pk_id, newid() as trans_id, pt.fk_tenant_id,@forecast_period as forecast_period,
pt.fk_account_code,
pt.fk_function_code,pt.fk_department_code, mp.pk_main_project_code as fk_main_project_code,
pt.fk_project_code,pt.free_dim_1,pt.free_dim_2,pt.free_dim_3,pt.free_dim_4,
0 AS vat_rate,0 AS vat_refund,
pt.year
,amount = CASE  WHEN UAD.status = 1 THEN PT.amount
                WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND ch.budget_year < @budget_year THEN PT.amount
                ELSE 0
                END
, getdate() as updated, @user_id as updated_by,'' as fk_alter_code,'' as fk_adjustment_code,
is_vat_row,
NULL AS fk_proj_trans_id,'' AS description,0 AS is_change, mp.inv_status, 0 as gl_amount
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN @change_id_table ch ON pt.fk_change_id = ch.fk_change_id
JOIN tco_accounts ac ON ac.pk_tenant_id = pt.fk_tenant_id AND ac.pk_account_code = pt.fk_account_code AND @budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV'
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code-- and UAD.status = 1
WHERE PT.fk_tenant_id = @fk_tenant_id 
AND (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and PT.year > 0
--GROUP BY  pt.fk_tenant_id, pt.fk_account_code,
--pt.fk_function_code,pt.fk_department_code, mp.pk_main_project_code,
--pt.fk_project_code,pt.free_dim_1,pt.free_dim_2,pt.free_dim_3,pt.free_dim_4,
--pt.year, mp.inv_status, is_vat_row



IF @forecast_period  IN (@last_period, @ub_period) AND @investment_init_type in ('', 'TotalBudget')
BEGIN 

PRINT 'Kommer tilbake til denne logikken'

PRINT 'Fetch gl amount ' + convert(varchar(400),GETDATE());

INSERT INTO @inv_gl_table (fk_tenant_id, gl_year,forecast_period, fk_main_project_code,
fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
gl_amount, type, inv_status)
SELECT a.fk_tenant_id, a.gl_year,@forecast_period, mp.pk_main_project_code,a.fk_account_code, 
a.department_code, a.fk_function_code, ISNULL(a.fk_project_code,''), 
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,
SUM(a.amount), type = case when l.line_item_id in (1010,1020) then 'i' else 'f' end, mp.inv_status
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV'
LEFT JOIN tco_projects p ON p.fk_tenant_id = a.fk_tenant_id AND p.pk_project_code = a.fk_project_code AND a.gl_year BETWEEN datepart(year,p.date_from) AND  datepart(year,p.date_to)
LEFT JOIN tco_main_projects mp ON p.fk_tenant_id = mp.fk_tenant_id AND p.fk_main_project_code = mp.pk_main_project_code AND a.gl_year BETWEEN datepart(year,mp.budget_year_from) AND  datepart(year,mp.budget_year_to)
WHERE a.fk_tenant_id = @fk_tenant_id AND a.gl_year = @budget_year
GROUP BY a.fk_tenant_id, p.fk_main_project_code,a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, 
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,l.line_item_id, a.gl_year, mp.inv_status, mp.pk_main_project_code; 

print 'Fetch gl amount into inv_status_detail '  + convert(varchar(400),GETDATE());

UPDATE #temp_proj_transactions SET gl_amount = gl.gl_amount
FROM #temp_proj_transactions id, @inv_gl_table gl
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_main_project_code = id.fk_main_project_code
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.gl_year = id.year
AND gl.inv_status = id.inv_status;



IF @forecast_period IN (@last_period, @ub_period)
BEGIN

Print 'Insert blank gl-lines in detail table  '  + convert(varchar(400),GETDATE());

INSERT INTO #temp_proj_transactions (pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,
fk_function_code,fk_department_code, fk_main_project_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
vat_rate,vat_refund,year,amount,updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,
fk_proj_trans_id,description,is_change, inv_status,  gl_amount)
SELECT NEWID() AS  pk_id,NEWID() AS trans_id, gl.fk_tenant_id, gl.forecast_period, gl.fk_account_code,
gl.fk_function_code,gl.fk_department_code, gl.fk_main_project_code,gl.fk_project_code,
gl.free_dim_1,gl.free_dim_2,gl.free_dim_3,gl.free_dim_4,
0 as vat_rate,0 as vat_refund, gl.gl_year, 0 AS amount,
getdate() as updated,@user_id as updated_by,'' as fk_alter_code,'' as fk_adjustment_code,0 as is_vat_row,
NULL AS fk_proj_trans_id,'' AS description,0 AS is_change, gl.inv_status,  SUM(gl_amount)
FROM @inv_gl_table gl
WHERE NOT EXISTS 
(SELECT * FROM #temp_proj_transactions id 
WHERE gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_main_project_code = id.fk_main_project_code
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.gl_year = id.year
AND gl.inv_status = id.inv_status
) 
GROUP BY gl.fk_tenant_id, gl.forecast_period, gl.fk_account_code,
gl.fk_function_code,gl.fk_department_code, gl.fk_main_project_code,gl.fk_project_code,
gl.free_dim_1,gl.free_dim_2,gl.free_dim_3,gl.free_dim_4,
gl.gl_year,gl.inv_status

--UPDATE #temp_proj_transactions SET inv_status = 1000 WHERE inv_status = ''
UPDATE #temp_proj_transactions SET inv_status = 1000 WHERE inv_status IS NULL

print 'Insert year_1_amount-gl_amount in year 2'

INSERT INTO #temp_proj_transactions (pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,
fk_function_code,fk_department_code, fk_main_project_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
vat_rate,vat_refund,year,amount,updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,
fk_proj_trans_id,description,is_change, inv_status,  gl_amount)
SELECT NEWID() pk_id, NEWID() trans_id,a.fk_tenant_id,a.forecast_period,a.fk_account_code,
a.fk_function_code,a.fk_department_code, a.fk_main_project_code,a.fk_project_code,
a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
a.vat_rate,a.vat_refund,@budget_year + 1 as year,
SUM(a.amount) - SUM(a.gl_amount),GETDATE() AS updated,@user_id as updated_by,
a.fk_alter_code,a.fk_adjustment_code,a.is_vat_row,
a.fk_proj_trans_id,a.description,a.is_change, a.inv_status,
0 as  gl_amount
FROM #temp_proj_transactions a
WHERE a.year = @budget_year
AND a.inv_status NOT IN (2,1000)
GROUP BY a.fk_tenant_id,a.forecast_period,a.fk_account_code,
a.fk_function_code,a.fk_department_code, a.fk_main_project_code,a.fk_project_code,
a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
a.vat_rate,a.vat_refund,
a.fk_alter_code,a.fk_adjustment_code,a.is_vat_row,
a.fk_proj_trans_id,a.description,a.is_change, a.inv_status


PRINT 'UPDATE with gl_amount ' + convert(varchar(400),GETDATE());

UPDATE #temp_proj_transactions SET amount = gl_amount WHERE year = @budget_year
AND inv_status != 2


END


--IF @forecast_period IN (@ub_period)
--BEGIN

--UPDATE @inv_status_detail SET gl_amount = 0


--PRINT 'Calculate the value to spread '  + convert(varchar(400),GETDATE());

--SELECT newid() as pk_id,fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
--free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
--vat_rate, vat_refund,vat_rate/100*vat_refund/100 as vat_pct,'i' as type,SUM(year_1_amount) as year_1_amount, convert(dec(18,2),0) as total_amount, convert(dec(18,6),0) as pct_value,
--convert(dec(18,2),0) as gl_amount, convert(dec(18,2),0) as vat_amount, line_item_id
--INTO #distribution_table FROM @inv_status_detail
--WHERE is_vat_row = 0 AND fk_account_code != @vat_account
--GROUP BY fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
--free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
--vat_rate, vat_refund, line_item_id

--UPDATE #distribution_table SET type = 'f' WHERE line_item_id = 2020


--UPDATE a SET a.total_amount = S.year_1_amount
--FROM #distribution_table a
--join 
--(SELECT fk_tenant_id, fk_investment_id,line_item_id,SUM(year_1_amount) as year_1_amount
--FROM @inv_status_detail
--WHERE is_vat_row = 0 AND fk_account_code != @vat_account
--GROUP BY fk_tenant_id, fk_investment_id, line_item_id
--HAVING sum(year_1_amount) != 0) S
--ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.line_item_id = S.line_item_id

--UPDATE a SET a.gl_amount = S.gl_amount
--FROM #distribution_table a
--join 
--(SELECT fk_tenant_id, fk_investment_id,type,SUM(gl_amount) as gl_amount
--FROM @inv_gl_table 
--GROUP BY fk_tenant_id, fk_investment_id, type
--HAVING sum(gl_amount) != 0) S
--ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.type = S.type


--DELETE a
--FROM @inv_gl_table a
--JOIN 
--(SELECT DISTINCT fk_tenant_id, fk_investment_id,type
--FROM #distribution_table) S
--ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.type = S.type


--UPDATE a SET a.pct_value = a.year_1_amount/a.total_amount
--FROM #distribution_table a
--WHERE total_amount != 0

---- Set one of the lines to 100% when there is no budget.
--UPDATE #distribution_table
--SET pct_value = 100
--WHERE pk_id IN (
--SELECT min(pk_id) FROM #distribution_table 
--WHERE total_amount = 0 
--GROUP BY total_amount, fk_investment_id)

--UPDATE a SET a.gl_amount = round(a.gl_amount * a.pct_value,0)
--FROM #distribution_table a

--UPDATE a SET a.vat_amount = round(a.gl_amount / (1+a.vat_pct) * vat_pct,0)
--FROM #distribution_table a

--UPDATE @inv_status_detail SET gl_amount = gl.gl_amount, use_budget = 0
--FROM @inv_status_detail id, #distribution_table gl
--WHERE 
--gl.fk_tenant_id = id.fk_tenant_id
--AND gl.forecast_period = id.forecast_period
--AND gl.fk_investment_id = id.fk_investment_id
--AND gl.fk_account_code   = id.fk_account_code
--AND gl.fk_department_code = id.fk_department_code
--AND gl.fk_function_code = id.fk_function_code
--AND gl.fk_project_code  = id.fk_project_code
--AND gl.free_dim_1 = id.free_dim_1
--AND gl.free_dim_2 = id.free_dim_2
--AND gl.free_dim_3 = id.free_dim_3
--AND gl.free_dim_4 = id.free_dim_4
--AND gl.fk_program_code = id.fk_program_code
--AND gl.fk_adjustment_code = id.fk_adjustment_code
--AND gl.fk_alter_code = id.fk_alter_code
--AND gl.type = id.type



--Print 'Insert vat rows'

----INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
----year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
----gl_amount,vat_refund, vat_rate,type, is_vat_row, use_budget)
----SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
----0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 
----0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
----vat_amount*-1 as  gl_amount, 0 as vat_refund, 0 as vat_rate,type, 1 as is_vat_row,0 as use_budget
----FROM #distribution_table


----INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
----year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
----gl_amount,vat_refund, vat_rate,type, is_vat_row,use_budget)
----SELECT fk_tenant_id, forecast_period, fk_investment_id,@vat_account as fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
----0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 
----0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
----vat_amount as  gl_amount, 0 as vat_refund, 0 as vat_rate,type, 1 as is_vat_row, 0 as use_budget
----FROM #distribution_table


--Print 'Insert  gl-lines without budget match in detail table  '  + convert(varchar(400),GETDATE());

--INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
--gl_amount,vat_refund, vat_rate,type, use_budget)
--SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
--0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
--SUM(gl_amount),0 as vat_refund,0 as  vat_rate, gl.type,0 as use_budget
--FROM @inv_gl_table gl
--GROUP BY fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,  fk_program_code, type
--;


--Print 'Mark transactions that will use budget amount '  + convert(varchar(400),GETDATE());

----UPDATE @inv_status_detail SET use_budget = 1
----FROM @inv_status_detail id, (
----SELECT fk_investment_id, fk_program_code, SUM(year_1_amount) AS year_1_amount, SUM(gl_amount) as gl_amount
----FROM @inv_status_detail
----WHERE fk_program_code = 2
----GROUP BY fk_investment_id, fk_program_code
----HAVING SUM(year_1_amount) > SUM(gl_amount)) B
----WHERE id.fk_investment_id = B.fk_investment_id
----AND id.fk_program_code = B.fk_program_code;


--UPDATE @inv_status_detail SET use_budget = 1
--FROM @inv_status_detail id, tco_inv_budgetyear_config i
--WHERE id.fk_investment_id = i.fk_investment_id
--AND id.fk_tenant_id = i.fk_tenant_id
--AND i.budget_year = @budget_year
--AND i.inv_status = 2;

--Print 'Mark transactions that will update year 2 amount '  + convert(varchar(400),GETDATE());


----UPDATE @inv_status_detail SET update_year2 = 1
----FROM @inv_status_detail id, (
----SELECT fk_tenant_id, fk_investment_id, SUM(year_1_amount) year_1_amount, SUM(gl_amount) gl_amount
----FROM @inv_status_detail 
----GROUP BY fk_tenant_id, fk_investment_id
----HAVING SUM(year_1_amount) > SUM(gl_amount)) B
----WHERE use_budget = 0 
----AND id.fk_tenant_id = B.fk_tenant_id
----AND id.fk_investment_id = B.fk_investment_id;

--UPDATE @inv_status_detail SET update_year2 = 1
--WHERE use_budget = 0 



--PRINT 'UPDATE with gl_amount ' + convert(varchar(400),GETDATE());

--UPDATE @inv_status_detail SET year_2_amount = year_2_amount+year_1_amount-gl_amount
--WHERE update_year2 = 1 AND use_budget = 0;

--UPDATE @inv_status_detail SET year_1_amount = gl_amount WHERE use_budget = 0;

--DELETE FROM @inv_status_detail 
--WHERE  year_0_amount = 0
--AND year_1_amount = 0 
--AND year_2_amount = 0
--AND year_3_amount = 0
--AND year_4_amount = 0
--AND year_5_amount = 0
--AND year_6_amount = 0 
--AND year_7_amount = 0 
--AND year_8_amount = 0
--AND year_9_amount = 0
--AND year_10_amount = 0

--END

PRINT 'Data ready for insert for option TotalBudget'

END

IF @investment_init_type = 'ActualYtd'
BEGIN
PRINT 'Running ActualYtd'

DELETE FROM #temp_proj_transactions;

SELECT fk_tenant_id,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year,fk_prog_code,
SUM(amount) AS amount
INTO #TEMP_ACC_DATA
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV'
WHERE fk_tenant_id = @fk_tenant_id 
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
HAVING SUM(amount) != 0;


INSERT INTO #temp_proj_transactions (pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,
fk_function_code,fk_department_code, fk_main_project_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
vat_rate,vat_refund,year,amount,updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,
fk_proj_trans_id,description,is_change, inv_status,  gl_amount)
SELECT newid() as pk_id, newid() as trans_id, a.fk_tenant_id,@forecast_period as forecast_period,a.fk_account_code,
a.fk_function_code, a.department_code, '' as fk_main_project_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
0 as vat_rate,0 as vat_refund,gl_year as year,a.amount,
getdate() as updated,@user_id as updated_by,'' as fk_alter_code,'' as fk_adjustment_code,0 as is_vat_row,
NULL AS fk_proj_trans_id,'' AS description,0 as is_change, 0 as inv_status,  amount as gl_amount
FROM #TEMP_ACC_DATA a

END



PRINT 'INSERT INTO tmr_proj_transactions '  + convert(varchar(400),GETDATE());

DELETE FROM tmr_proj_transactions WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period;

INSERT INTO tmr_proj_transactions (pk_id, trans_id, fk_tenant_id, forecast_period, fk_account_code, fk_function_code,
fk_department_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3,free_dim_4,vat_rate,vat_refund,year,amount,
updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,fk_proj_trans_id,description,is_change)
SELECT pk_id = newiD(),trans_id = NEWID(),fk_tenant_id,forecast_period,fk_account_code,fk_function_code,fk_department_code,
fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,year,amount = SUM(amount)
,updated = getdate() ,updated_by = @user_id
,fk_alter_code,fk_adjustment_code,is_vat_row,fk_proj_trans_id = NULL,description,is_change
FROM #temp_proj_transactions
GROUP BY fk_tenant_id,forecast_period,fk_account_code,fk_function_code,fk_department_code,
fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,year
,fk_alter_code,fk_adjustment_code,is_vat_row,description,is_change;






PRINT 'FINISH: Copy new investments from last budget ' + convert(varchar(400),GETDATE());
