
CREATE OR ALTER PROCEDURE [dbo].[prcUpdateFlatTables] @fk_tenant_id INT, @user_id INT

AS


CREATE TABLE #prev_dep
(
[pk_id] INT NOT NULL IDENTITY(1,1),
[pk_department_code] nvarchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[department_name] nvarchar(125) NOT NULL
)

IF @fk_tenant_id = 0
BEGIN


INSERT INTO #prev_dep
SELECT 
pk_department_code
,fk_tenant_id
,budget_year
,department_name = MAX (department_name) 
FROM tco_departments a
JOIN gco_year_list b ON b.budget_year-1 BETWEEN a.year_from and a.year_to
GROUP BY pk_department_code, fk_tenant_id, budget_year


DELETE h1 from #prev_dep h1
JOIN (
select fk_tenant_id, pk_department_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #prev_dep
group by fk_tenant_id, pk_department_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h1.fk_tenant_id = t.fk_tenant_id AND h1.pk_department_code = t.pk_department_code AND h1.budget_year = t.budget_year AND h1.pk_id != min_pk


CREATE TABLE #hlptab1
(
[pk_id] INT NOT NULL IDENTITY(1,1),
[pk_department_code] nvarchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[department_name] nvarchar(125) NOT NULL
)


DELETE h1 from #hlptab1 h1
JOIN (
select fk_tenant_id, pk_department_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #hlptab1
group by fk_tenant_id, pk_department_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h1.fk_tenant_id = t.fk_tenant_id AND h1.pk_department_code = t.pk_department_code AND h1.budget_year = t.budget_year AND h1.pk_id != min_pk



CREATE TABLE #hlptab2
(
[pk_id] INT NOT NULL IDENTITY(1,1),
[pk_function_code] nvarchar(25) NOT NULL,
[pk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[fk_kostra_function_code] nvarchar(25) NOT NULL,
[display_name] nvarchar(100) NOT NULL
)

CREATE TABLE #hlptab3
(
[pk_id] INT NOT NULL IDENTITY(1,1),
[pk_account_code] nvarchar(25) NOT NULL,
[pk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[fk_kostra_account_code] nvarchar(25) NOT NULL,
[display_name] nvarchar(255) NOT NULL
)

CREATE TABLE #hlptab4
(
[pk_id] INT NOT NULL IDENTITY(1,1),
[pk_project_code] nvarchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[project_name] nvarchar(250) NOT NULL
)


CREATE TABLE #hlptab5
(
[pk_org_version] varchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[org_id_1] nvarchar(25) NOT NULL,
[org_name_1] nvarchar(125) NOT NULL,
[org_id_2] nvarchar(25) NOT NULL,
[org_name_2] nvarchar(125) NOT NULL,
[org_id_3] nvarchar(25) NOT NULL,
[org_name_3] nvarchar(125) NOT NULL,
[org_id_4] nvarchar(25) NOT NULL,
[org_name_4] nvarchar(125) NOT NULL,
[org_id_5] nvarchar(25) NOT NULL,
[org_name_5] nvarchar(125) NOT NULL,
[fk_department_code] nvarchar(25) NOT NULL,
[department_name] nvarchar(125) NOT NULL,
[org_id_6] nvarchar(50) NULL,
[org_name_6] nvarchar(200) NULL,
[org_shortname_6] nvarchar(50) NULL,
[org_id_7] nvarchar(50) NULL,
[org_name_7] nvarchar(200) NULL,
[org_shortname_7] nvarchar(50) NULL,
[org_id_8] nvarchar(50) NULL,
[org_name_8] nvarchar(200) NULL,
[org_shortname_8] nvarchar(50) NULL,
[department_setup_missing] INT NOT NULL, 
[updated] DATETIME NOT NULL, 
[updated_by] INT NOT NULL
)


CREATE TABLE #hlptab6
(
[fk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[fk_department_code] nvarchar(25) NOT NULL,
[attribute_id] nvarchar(25) NOT NULL,
[attribute_name] nvarchar(225) NOT NULL, 
[updated] DATETIME NOT NULL, 
[updated_by] INT NOT NULL
)



CREATE TABLE #hlptab7
(	 
[pk_Function_code] nvarchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[function_name] nvarchar(100) NOT NULL,
[fk_kostra_function_code] nvarchar(25) NOT NULL,
[kostra_function_name] nvarchar(100) NOT NULL,
[budget_year] int NOT NULL,
[service_id_1] nvarchar(25) NOT NULL,
[service_name_1] nvarchar(100) NOT NULL,
[service_id_2] nvarchar(25) NOT NULL,
[service_name_2] nvarchar(100) NOT NULL,
[service_id_3] nvarchar(25) NOT NULL,
[service_name_3] nvarchar(100) NOT NULL,
[service_id_4] nvarchar(25) NOT NULL,
[service_name_4] nvarchar(100) NOT NULL,
[service_id_5] nvarchar(25) NOT NULL,
[service_name_5] nvarchar(100) NOT NULL, 
[updated] DATETIME NOT NULL, 
[updated_by] INT NOT NULL
)



CREATE TABLE #hlptab8
(	 
[fk_tenant_id] int NOT NULL,
[pk_account_code] nvarchar(25) NOT NULL,
[account_name] nvarchar(255) NOT NULL,
[fk_kostra_account_code] varchar(25) NOT NULL,
[kostra_account_name] varchar(100) NOT NULL,
[budget_year] int NOT NULL,
[account_type] varchar(50) NOT NULL,
[income_flag] int NOT NULL, 
[updated] DATETIME NOT NULL, 
[updated_by] INT NOT NULL
)


CREATE TABLE #hlptab9
(
[pk_project_code] nvarchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[project_name] nvarchar(250) NOT NULL,
[budget_year] int NOT NULL, 
[updated] DATETIME NOT NULL, 
[updated_by] INT NOT NULL
)




INSERT INTO #hlptab1
SELECT 
pk_department_code
,fk_tenant_id
,budget_year
,department_name = MAX (department_name) 
FROM tco_departments a
JOIN gco_year_list b ON b.budget_year BETWEEN a.year_from and a.year_to
GROUP BY pk_department_code, fk_tenant_id, budget_year

DELETE h1 from #hlptab1 h1
JOIN (
select fk_tenant_id, pk_department_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #hlptab1
group by fk_tenant_id, pk_department_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h1.fk_tenant_id = t.fk_tenant_id AND h1.pk_department_code = t.pk_department_code AND h1.budget_year = t.budget_year AND h1.pk_id != min_pk






INSERT INTO #hlptab2
SELECT 
pk_function_code
,pk_tenant_id
,budget_year
,fk_kostra_function_code
,display_name = MAX (display_name) 
FROM tco_functions a
JOIN gco_year_list b ON b.budget_year BETWEEN DATEPART(year,a.dateFrom) and DATEPART(year, a.dateTo)
GROUP BY pk_function_code, pk_tenant_id, budget_year, fk_kostra_function_code

DELETE h2 from #hlptab2 h2
JOIN (
select pk_tenant_id, pk_function_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #hlptab2
group by pk_tenant_id, pk_function_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h2.pk_tenant_id = t.pk_tenant_id AND h2.pk_function_code = t.pk_function_code AND h2.budget_year = t.budget_year AND h2.pk_id != min_pk






INSERT INTO #hlptab3
SELECT 
pk_account_code
,pk_tenant_id
,budget_year
,fk_kostra_account_code
,display_name = MAX (display_name) 
FROM tco_accounts a
JOIN gco_year_list b ON b.budget_year BETWEEN DATEPART(year,a.dateFrom) and DATEPART(year, a.dateTo)
GROUP BY pk_account_code, pk_tenant_id, budget_year, fk_kostra_account_code

DELETE h3 from #hlptab3 h3
JOIN (
select pk_tenant_id, pk_account_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #hlptab3
group by pk_tenant_id, pk_account_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h3.pk_tenant_id = t.pk_tenant_id AND h3.pk_account_code = t.pk_account_code AND h3.budget_year = t.budget_year AND h3.pk_id != min_pk



INSERT INTO #hlptab4
SELECT 
pk_project_code
,fk_tenant_id
,budget_year
,project_name = MAX (project_name) 
FROM tco_projects a
JOIN gco_year_list b ON b.budget_year BETWEEN DATEPART (year,a.date_from) and DATEPART(year, a.date_to)
GROUP BY pk_project_code, fk_tenant_id, budget_year


DELETE h4 from #hlptab4 h4
JOIN (
select fk_tenant_id, pk_project_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #hlptab4
group by fk_tenant_id, pk_project_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h4.fk_tenant_id = t.fk_tenant_id AND h4.pk_project_code = t.pk_project_code AND h4.budget_year = t.budget_year AND h4.pk_id != min_pk






INSERT INTO #hlptab5
select       ov.pk_org_version
             ,ov.fk_tenant_id
             ,y.budget_year
             ,[org_id_1]
             ,[org_name_1]
             ,[org_id_2]
             ,[org_name_2]
             ,[org_id_3]
             ,[org_name_3]
             ,[org_id_4]
             ,[org_name_4]
             ,[org_id_5]
             ,[org_name_5]
             ,[fk_department_code]
             ,[department_name] = ISNULL(dep.department_name,'Oppsett ansvar mangler')
             ,[org_id_6]
             ,[org_name_6]
             ,[org_shortname_6] 
             ,[org_id_7]
             ,[org_name_7]
             ,[org_shortname_7]
             ,[org_id_8]
             ,[org_name_8]
             ,[org_shortname_8]
			 ,department_setup_missing = CASE WHEN dep.pk_department_code IS NULL THEN 1 ELSE 0 END
			 ,updated = GETDATE()
			 ,updated_by = @user_id
from [tco_org_version] ov
JOIN [gco_year_list] y ON 1=1
JOIN [tco_org_hierarchy] oh	ON  ov.fk_tenant_id = oh.fk_tenant_id	AND ov.pk_org_version = oh.fk_org_version
LEFT JOIN #hlptab1 dep	ON  oh.fk_tenant_id = dep.fk_tenant_id	AND oh.fk_department_code = dep.pk_department_code AND y.budget_year = dep.budget_year
where y.budget_year*100+1 BETWEEN ov.period_from and ov.period_to

UPDATE h SET h.department_name = dep.department_name, h.department_setup_missing = 0
FROM #hlptab5 h 
JOIN #prev_dep dep ON h.fk_tenant_id = dep.fk_tenant_id AND h.budget_year = dep.budget_year AND h.fk_department_code = dep.pk_department_code
WHERE h.department_setup_missing = 1

INSERT INTO #hlptab6
select       dep.fk_tenant_id
             ,y.budget_year
             ,dep.pk_department_code
			 ,av.pk_attribute_id
			 ,av.attribute_name
			 ,updated = GETDATE()
			 ,updated_by = @user_id
from [tco_departments] dep
JOIN [gco_year_list] y ON 1=1
JOIN [tco_relation_values] rv ON dep.fk_Tenant_id = rv.fk_tenant_id AND dep.pk_department_code BETWEEN rv.relation_value_from and rv. relation_value_to AND rv.attribute_type = 'CHAPTER' AND rv. relation_type = 'DEPARTMENTS' AND y.budget_year BETWEEN rv. year_from and rv. year_to
JOIN [tco_attribute_values] av on rv.fk_tenant_id = av. fk_tenant_id AND rv. attribute_value = av. pk_attribute_id
where y.budget_year BETWEEN dep.year_from and dep.year_to 



INSERT INTO #hlptab7
select 
fc.pk_Function_code	
,fc.pk_tenant_id	
,fc.display_name	
,fc.fk_kostra_function_code	
,kostra_function_name = ISNULL(kf.kostra_function_name,'')
,y.budget_year	
,service_id_1	= ISNULL(sv.service_id_1	,'')
,service_name_1 = ISNULL(sv.service_name_1	,'')
,service_id_2	= ISNULL(sv.service_id_2	,'')
,service_name_2 = ISNULL(sv.service_name_2	,'')
,service_id_3	= ISNULL(sv.service_id_3	,'')
,service_name_3 = ISNULL(sv.service_name_3	,'')
,service_id_4	= ISNULL(sv.service_id_4	,'')
,service_name_4 = ISNULL(sv.service_name_4	,'')
,service_id_5	= ISNULL(sv.service_id_5	,'')
,service_name_5	= ISNULL(sv.service_name_5	,'')
,updated = GETDATE()
,updated_by = @user_id
from #hlptab2 fc
JOIN [gco_year_list] y ON 1=1
LEFT JOIN [tco_service_values] sv ON fc.pk_function_code = sv.fk_function_code AND fc.pk_tenant_id = sv.fk_tenant_id
LEFT JOIN [gco_tenants] gt ON fc.pk_tenant_id = gt.pk_id
LEFT JOIN [gmd_kostra_function] kf ON kf.pk_kostra_function_code = fc.fk_kostra_function_code AND gt.tenant_type_id = kf.tenant_type
where y.budget_year = fc.budget_year

INSERT INTO #hlptab8
select
src.pk_tenant_id
,pk_account_code = src.pk_account_code
,src.display_name
,fk_kostra_account_code = src.fk_kostra_account_code
,kostra_account_name = ISNULL(b.display_name,'''')
,y.budget_year
,account_type = ISNULL(b.type,'''')
,income_flag = ISNULL(b.income_flag,0)
,updated = GETDATE()
,updated_by = @user_id
FROM #hlptab3 src
LEFT JOIN  [gco_kostra_accounts] b on src.fk_kostra_account_code = b.pk_kostra_account_code
JOIN [gco_year_list] y ON 1=1 AND y.budget_year = src.budget_year


INSERT INTO #hlptab9
select 
pk_project_code	
,fk_tenant_id	
,project_name	
,p.budget_year
,updated = GETDATE()
,updated_by = @user_id
from #hlptab4 p
JOIN [gco_year_list] y ON 1=1
where y.budget_year = p.budget_year


--MERGE FLAT_ORG_HIERARCHY_DEP

MERGE flat_org_hierarchy_dep AS TARGET
USING #hlptab5 AS SOURCE
ON SOURCE.fk_tenant_id = TARGET.fk_tenant_id AND SOURCE.budget_year = TARGET.budget_year AND SOURCE.pk_org_version = TARGET.pk_org_version AND SOURCE.fk_department_code = TARGET.fk_department_code
WHEN NOT MATCHED BY TARGET THEN
    INSERT (
	pk_org_version
	,fk_tenant_id
	,budget_year
	,org_id_1
	,org_name_1
	,org_id_2
	,org_name_2
	,org_id_3
	,org_name_3
	,org_id_4
	,org_name_4
	,org_id_5
	,org_name_5
	,fk_department_code
	,department_name
	,org_id_6
	,org_name_6
	,org_shortname_6
	,org_id_7
	,org_name_7
	,org_shortname_7
	,org_id_8
	,org_name_8
	,org_shortname_8
	,department_setup_missing
	,updated
	,updated_by)
	VALUES
	(SOURCE.pk_org_version
    ,SOURCE.fk_tenant_id
    ,SOURCE.budget_year
    ,SOURCE.org_id_1
    ,SOURCE.org_name_1
    ,SOURCE.org_id_2
    ,SOURCE.org_name_2
    ,SOURCE.org_id_3
    ,SOURCE.org_name_3
    ,SOURCE.org_id_4
    ,SOURCE.org_name_4
    ,SOURCE.org_id_5
    ,SOURCE.org_name_5
    ,SOURCE.fk_department_code
    ,SOURCE.department_name
    ,SOURCE.org_id_6
    ,SOURCE.org_name_6
    ,SOURCE.org_shortname_6 
    ,SOURCE.org_id_7
    ,SOURCE.org_name_7
    ,SOURCE.org_shortname_7
    ,SOURCE.org_id_8
    ,SOURCE.org_name_8
    ,SOURCE.org_shortname_8
	,SOURCE.department_setup_missing
	,SOURCE.updated
	,SOURCE.updated_by	
	)
	
	WHEN MATCHED THEN UPDATE SET
			
	 TARGET.org_id_1					= SOURCE.org_id_1				
	,TARGET.org_name_1					= SOURCE.org_name_1				
	,TARGET.org_id_2					= SOURCE.org_id_2				
	,TARGET.org_name_2					= SOURCE.org_name_2				
	,TARGET.org_id_3					= SOURCE.org_id_3				
	,TARGET.org_name_3					= SOURCE.org_name_3				
	,TARGET.org_id_4					= SOURCE.org_id_4				
	,TARGET.org_name_4					= SOURCE.org_name_4				
	,TARGET.org_id_5					= SOURCE.org_id_5				
	,TARGET.org_name_5					= SOURCE.org_name_5				
	,TARGET.department_name				= SOURCE.department_name			
	,TARGET.org_id_6					= SOURCE.org_id_6				
	,TARGET.org_name_6					= SOURCE.org_name_6				
	,TARGET.org_shortname_6				= SOURCE.org_shortname_6			
	,TARGET.org_id_7					= SOURCE.org_id_7				
	,TARGET.org_name_7					= SOURCE.org_name_7				
	,TARGET.org_shortname_7				= SOURCE.org_shortname_7			
	,TARGET.org_id_8					= SOURCE.org_id_8				
	,TARGET.org_name_8					= SOURCE.org_name_8				
	,TARGET.org_shortname_8				= SOURCE.org_shortname_8			
	,TARGET.department_setup_missing	= SOURCE.department_setup_missing
	,TARGET.updated						= SOURCE.updated					
	,TARGET.updated_by					= SOURCE.updated_by				
	
	WHEN NOT MATCHED BY SOURCE THEN
	DELETE;

--	-- Checking the actions by MERGE statement
--OUTPUT $action, 
-- DELETED.pk_org_version				AS TARGETpk_org_version
--,DELETED.fk_tenant_id				AS TARGETfk_tenant_id
--,DELETED.budget_year				AS TARGETbudget_year
--,DELETED.org_id_1					AS TARGETorg_id_1					
--,DELETED.org_name_1					AS TARGETorg_name_1				
--,DELETED.org_id_2					AS TARGETorg_id_2					
--,DELETED.org_name_2					AS TARGETorg_name_2				
--,DELETED.org_id_3					AS TARGETorg_id_3					
--,DELETED.org_name_3					AS TARGETorg_name_3				
--,DELETED.org_id_4					AS TARGETorg_id_4					
--,DELETED.org_name_4					AS TARGETorg_name_4				
--,DELETED.org_id_5					AS TARGETorg_id_5					
--,DELETED.org_name_5					AS TARGETorg_name_5				
--,DELETED.fk_department_code			AS TARGETfk_department_code
--,DELETED.department_name			AS TARGETdepartment_name			
--,DELETED.org_id_6					AS TARGETorg_id_6					
--,DELETED.org_name_6					AS TARGETorg_name_6				
--,DELETED.org_shortname_6			AS TARGETorg_shortname_6			
--,DELETED.org_id_7					AS TARGETorg_id_7					
--,DELETED.org_name_7					AS TARGETorg_name_7				
--,DELETED.org_shortname_7			AS TARGETorg_shortname_7			
--,DELETED.org_id_8					AS TARGETorg_id_8					
--,DELETED.org_name_8					AS TARGETorg_name_8				
--,DELETED.org_shortname_8			AS TARGETorg_shortname_8			
--,DELETED.department_setup_missing	AS TARGETdepartment_setup_missing	
--,DELETED.updated					AS TARGETupdated					
--,DELETED.updated_by					AS TARGETupdated_by				
--,INSERTED.pk_org_version			AS TARGETpk_org_version
--,INSERTED.fk_tenant_id				AS TARGETfk_tenant_id
--,INSERTED.budget_year				AS TARGETbudget_year
--,INSERTED.org_id_1					AS TARGETorg_id_1					
--,INSERTED.org_name_1				AS TARGETorg_name_1				
--,INSERTED.org_id_2					AS TARGETorg_id_2					
--,INSERTED.org_name_2				AS TARGETorg_name_2				
--,INSERTED.org_id_3					AS TARGETorg_id_3					
--,INSERTED.org_name_3				AS TARGETorg_name_3				
--,INSERTED.org_id_4					AS TARGETorg_id_4					
--,INSERTED.org_name_4				AS TARGETorg_name_4				
--,INSERTED.org_id_5					AS TARGETorg_id_5					
--,INSERTED.org_name_5				AS TARGETorg_name_5				
--,INSERTED.fk_department_code		AS TARGETfk_department_code
--,INSERTED.department_name			AS TARGETdepartment_name			
--,INSERTED.org_id_6					AS TARGETorg_id_6					
--,INSERTED.org_name_6				AS TARGETorg_name_6				
--,INSERTED.org_shortname_6			AS TARGETorg_shortname_6			
--,INSERTED.org_id_7					AS TARGETorg_id_7					
--,INSERTED.org_name_7				AS TARGETorg_name_7				
--,INSERTED.org_shortname_7			AS TARGETorg_shortname_7			
--,INSERTED.org_id_8					AS TARGETorg_id_8					
--,INSERTED.org_name_8				AS TARGETorg_name_8				
--,INSERTED.org_shortname_8			AS TARGETorg_shortname_8			
--,INSERTED.department_setup_missing	AS TARGETdepartment_setup_missing	
--,INSERTED.updated					AS TARGETupdated					
--,INSERTED.updated_by				AS TARGETupdated_by			
--;






--MERGE FLAT_ATTRIBUTE_VALUES

MERGE flat_attribute_values AS TARGET
USING #hlptab6 AS SOURCE
ON SOURCE.fk_tenant_id = TARGET.fk_tenant_id AND SOURCE.budget_year = TARGET.budget_year AND SOURCE.fk_department_code = TARGET.fk_department_code AND SOURCE.attribute_id = TARGET.attribute_id
WHEN NOT MATCHED BY TARGET THEN
    INSERT (
	 fk_tenant_id
	,budget_year
	,fk_department_code
	,attribute_id
	,attribute_name
	,updated
	,updated_by
)
	VALUES
	(SOURCE.fk_tenant_id
    ,SOURCE.budget_year
    ,SOURCE.fk_department_code
    ,SOURCE.attribute_id
    ,SOURCE.attribute_name
    ,SOURCE.updated
    ,SOURCE.updated_by
	)
	
	WHEN MATCHED THEN UPDATE SET
			
	 TARGET.fk_tenant_id		= SOURCE.fk_tenant_id				
	,TARGET.budget_year			= SOURCE.budget_year					
	,TARGET.fk_department_code	= SOURCE.fk_department_code			
	,TARGET.attribute_id		= SOURCE.attribute_id				
	,TARGET.attribute_name		= SOURCE.attribute_name				
	,TARGET.updated				= SOURCE.updated						
	,TARGET.updated_by			= SOURCE.updated_by					

	
	WHEN NOT MATCHED BY SOURCE THEN
	DELETE;






--MERGE FLAT_FUNCTION_SERVICE_VALUES
	

MERGE flat_function_service_values AS TARGET
USING #hlptab7 AS SOURCE
ON SOURCE.fk_tenant_id = TARGET.fk_tenant_id AND SOURCE.budget_year = TARGET.budget_year AND SOURCE.pk_function_code = TARGET.pk_function_code
WHEN NOT MATCHED BY TARGET THEN
    INSERT (
	 pk_function_code
	,fk_tenant_id
	,function_name
	,fk_kostra_function_code
	,kostra_function_name
	,budget_year
	,service_id_1
	,service_name_1
	,service_id_2
	,service_name_2
	,service_id_3
	,service_name_3
	,service_id_4
	,service_name_4
	,service_id_5
	,service_name_5
	,updated
	,updated_by
)
	VALUES (
	 SOURCE.pk_function_code
    ,SOURCE.fk_tenant_id
    ,SOURCE.function_name
    ,SOURCE.fk_kostra_function_code
    ,SOURCE.kostra_function_name
    ,SOURCE.budget_year
    ,SOURCE.service_id_1
	,SOURCE.service_name_1
	,SOURCE.service_id_2
	,SOURCE.service_name_2
	,SOURCE.service_id_3
	,SOURCE.service_name_3
	,SOURCE.service_id_4
	,SOURCE.service_name_4
	,SOURCE.service_id_5
	,SOURCE.service_name_5
	,SOURCE.updated
	,SOURCE.updated_by
)
	
	WHEN MATCHED THEN UPDATE SET
			
	 TARGET.pk_function_code			= SOURCE.pk_function_code		
	,TARGET.fk_tenant_id				= SOURCE.fk_tenant_id			
	,TARGET.function_name				= SOURCE.function_name		
	,TARGET.fk_kostra_function_code		= SOURCE.fk_kostra_function_code		
	,TARGET.kostra_function_name		= SOURCE.kostra_function_name		
	,TARGET.budget_year					= SOURCE.budget_year			
	,TARGET.service_id_1				= SOURCE.service_id_1		
	,TARGET.service_name_1				= SOURCE.service_name_1
	,TARGET.service_id_2				= SOURCE.service_id_2
	,TARGET.service_name_2				= SOURCE.service_name_2
	,TARGET.service_id_3				= SOURCE.service_id_3
	,TARGET.service_name_3				= SOURCE.service_name_3
	,TARGET.service_id_4				= SOURCE.service_id_4
	,TARGET.service_name_4				= SOURCE.service_name_4
	,TARGET.service_id_5				= SOURCE.service_id_5
	,TARGET.service_name_5				= SOURCE.service_name_5
	,TARGET.updated						= SOURCE.updated
	,TARGET.updated_by					= SOURCE.updated_by
	
	WHEN NOT MATCHED BY SOURCE THEN
	DELETE;



--MERGE FLAT_ACCOUNTS
	
MERGE flat_accounts AS TARGET
USING #hlptab8 AS SOURCE
ON SOURCE.fk_tenant_id = TARGET.fk_tenant_id AND SOURCE.budget_year = TARGET.budget_year AND SOURCE.pk_account_code = TARGET.pk_account_code
WHEN NOT MATCHED BY TARGET THEN
    INSERT (
	  fk_tenant_id
	 ,pk_account_code
	 ,account_name
	 ,fk_kostra_account_code
	 ,kostra_account_name
	 ,budget_year
	 ,account_type
	 ,income_flag
	 ,updated
	 ,updated_by
)
	VALUES (
	 SOURCE.fk_tenant_id
    ,SOURCE.pk_account_code
    ,SOURCE.account_name
    ,SOURCE.fk_kostra_account_code
    ,SOURCE.kostra_account_name
    ,SOURCE.budget_year
    ,SOURCE.account_type
	,SOURCE.income_flag
	,SOURCE.updated
	,SOURCE.updated_by
)
	
	WHEN MATCHED THEN UPDATE SET
			
	 TARGET.fk_tenant_id			= SOURCE.fk_tenant_id
	,TARGET.pk_account_code			= SOURCE.pk_account_code
	,TARGET.account_name			= SOURCE.account_name
	,TARGET.fk_kostra_account_code	= SOURCE.fk_kostra_account_code		
	,TARGET.kostra_account_name		= SOURCE.kostra_account_name	
	,TARGET.budget_year				= SOURCE.budget_year
	,TARGET.account_type			= SOURCE.account_type
	,TARGET.income_flag				= SOURCE.income_flag
	,TARGET.updated					= SOURCE.updated
	,TARGET.updated_by				= SOURCE.updated_by

	
	WHEN NOT MATCHED BY SOURCE THEN
	DELETE;



--MERGE FLAT_PROJECTS
	
MERGE flat_projects AS TARGET
USING #hlptab9 AS SOURCE
ON SOURCE.fk_tenant_id = TARGET.fk_tenant_id AND SOURCE.budget_year = TARGET.budget_year AND SOURCE.pk_project_code = TARGET.pk_project_code
WHEN NOT MATCHED BY TARGET THEN
    INSERT (
	  pk_project_code
	 ,fk_tenant_id
	 ,project_name
	 ,budget_year
	 ,updated
	 ,updated_by
)
	VALUES (
	 SOURCE.pk_project_code
    ,SOURCE.fk_tenant_id
    ,SOURCE.project_name
    ,SOURCE.budget_year
    ,SOURCE.updated
    ,SOURCE.updated_by
)
	
	WHEN MATCHED THEN UPDATE SET
			
	 TARGET.pk_project_code			= SOURCE.pk_project_code
	,TARGET.fk_tenant_id			= SOURCE.fk_tenant_id
	,TARGET.project_name			= SOURCE.project_name
	,TARGET.budget_year				= SOURCE.budget_year	
	,TARGET.updated					= SOURCE.updated
	,TARGET.updated_by				= SOURCE.updated_by



	WHEN NOT MATCHED BY SOURCE THEN
	DELETE;

	END
-------------------------------------------------------------------------------------------------------------------------------------------------------------


IF @fk_tenant_id != 0
BEGIN


INSERT INTO #prev_dep
SELECT 
pk_department_code
,fk_tenant_id
,budget_year
,department_name = MAX (department_name) 
FROM tco_departments a
JOIN gco_year_list b ON b.budget_year-1 BETWEEN a.year_from and a.year_to
WHERE a.fk_tenant_id = @fk_tenant_id
GROUP BY pk_department_code, fk_tenant_id, budget_year


DELETE h1 from #prev_dep h1
JOIN (
select fk_tenant_id, pk_department_code, budget_year, COUNT(*) as antall, MIN(pk_id) as min_pk 
from #prev_dep
group by fk_tenant_id, pk_department_code, budget_year
HAVING COUNT(*) > 1 ) t
ON h1.fk_tenant_id = t.fk_tenant_id AND h1.pk_department_code = t.pk_department_code AND h1.budget_year = t.budget_year AND h1.pk_id != min_pk


DELETE FROM flat_org_hierarchy_dep where fk_tenant_id = @fk_tenant_id

INSERT INTO flat_org_hierarchy_dep
select       ov.pk_org_version
             ,ov.fk_tenant_id
             ,y.budget_year
             ,[org_id_1]
             ,[org_name_1]
             ,[org_id_2]
             ,[org_name_2]
             ,[org_id_3]
             ,[org_name_3]
             ,[org_id_4]
             ,[org_name_4]
             ,[org_id_5]
             ,[org_name_5]
             ,[fk_department_code]
             ,[department_name] = ISNULL(dep.department_name,'Oppsett ansvar mangler')
             ,[org_id_6]
             ,[org_name_6]
             ,[org_shortname_6] 
             ,[org_id_7]
             ,[org_name_7]
             ,[org_shortname_7]
             ,[org_id_8]
             ,[org_name_8]
             ,[org_shortname_8]
			 ,department_setup_missing = CASE WHEN dep.pk_department_code IS NULL THEN 1 ELSE 0 END
			 ,updated = GETDATE()
			 ,updated_by = @user_id
from [tco_org_version] ov
JOIN [gco_year_list] y ON 1=1
JOIN [tco_org_hierarchy] oh	ON  ov.fk_tenant_id = oh.fk_tenant_id	AND ov.pk_org_version = oh.fk_org_version
LEFT JOIN [tco_departments] dep	ON  oh.fk_tenant_id = dep.fk_tenant_id	AND oh.fk_department_code = dep.pk_department_code AND y.budget_year BETWEEN dep.year_from and dep.year_to
where y.budget_year*100+1 BETWEEN ov.period_from and ov.period_to 
AND ov.fk_tenant_id = @fk_tenant_id 

UPDATE h SET h.department_name = dep.department_name, h.department_setup_missing = 0
FROM flat_org_hierarchy_dep h 
JOIN #prev_dep dep ON h.fk_tenant_id = dep.fk_tenant_id AND h.budget_year = dep.budget_year AND h.fk_department_code = dep.pk_department_code
WHERE h.department_setup_missing = 1 AND h.fk_tenant_id = @fk_tenant_id

DELETE FROM flat_attribute_values where fk_tenant_id = @fk_tenant_id

INSERT INTO flat_attribute_values
select       dep.fk_tenant_id
             ,y.budget_year
             ,dep.pk_department_code
			 ,av.pk_attribute_id
			 ,av.attribute_name
			 ,updated = GETDATE()
			 ,updated_by = @user_id
from [tco_departments] dep
JOIN [gco_year_list] y ON 1=1
JOIN [tco_relation_values] rv ON dep.fk_Tenant_id = rv.fk_tenant_id AND dep.pk_department_code BETWEEN rv.relation_value_from and rv. relation_value_to AND rv.attribute_type = 'CHAPTER' AND rv. relation_type = 'DEPARTMENTS' AND y.budget_year BETWEEN rv. year_from and rv. year_to
JOIN [tco_attribute_values] av on rv.fk_tenant_id = av. fk_tenant_id AND rv. attribute_value = av. pk_attribute_id
where y.budget_year BETWEEN dep.year_from and dep.year_to 
AND dep.fk_tenant_id = @fk_tenant_id 



DELETE FROM flat_function_service_values where fk_tenant_id = @fk_tenant_id

INSERT INTO flat_function_service_values
select 
fc.pk_Function_code	
,fc.pk_tenant_id	
,fc.display_name	
,fc.fk_kostra_function_code	
,kostra_function_name = ISNULL(kf.kostra_function_name,'''')
,y.budget_year	
,service_id_1	= ISNULL(sv.service_id_1	,'')
,service_name_1 = ISNULL(sv.service_name_1	,'')
,service_id_2	= ISNULL(sv.service_id_2	,'')
,service_name_2 = ISNULL(sv.service_name_2	,'')
,service_id_3	= ISNULL(sv.service_id_3	,'')
,service_name_3 = ISNULL(sv.service_name_3	,'')
,service_id_4	= ISNULL(sv.service_id_4	,'')
,service_name_4 = ISNULL(sv.service_name_4	,'')
,service_id_5	= ISNULL(sv.service_id_5	,'')
,service_name_5	= ISNULL(sv.service_name_5	,'')
,updated = GETDATE()
,updated_by = @user_id
from [tco_functions] fc
JOIN [gco_year_list] y ON 1=1
LEFT JOIN [tco_service_values] sv ON fc.pk_function_code = sv.fk_function_code AND fc.pk_tenant_id = sv.fk_tenant_id
LEFT JOIN [gco_tenants] gt ON fc.pk_tenant_id = gt.pk_id
LEFT JOIN [gmd_kostra_function] kf ON kf.pk_kostra_function_code = fc.fk_kostra_function_code AND gt.tenant_type_id = kf.tenant_type
where y.budget_year BETWEEN DATEPART(YEAR,fc.dateFrom) AND DATEPART(YEAR, fc.dateTo)
AND fc.pk_tenant_id = @fk_tenant_id 



DELETE FROM flat_accounts where fk_tenant_id = @fk_tenant_id

INSERT INTO flat_accounts 
select
src.pk_tenant_id
,pk_account_code = src.pk_account_code
,src.display_name
,fk_kostra_account_code = src.fk_kostra_account_code
,kostra_account_name = ISNULL(b.display_name,'''')
,y.budget_year
,account_type = ISNULL(b.type,'''')
,income_flag = ISNULL(b.income_flag,0)
,updated = GETDATE()
,updated_by = @user_id
FROM [tco_accounts] src
LEFT JOIN  [gco_kostra_accounts] b on src.fk_kostra_account_code = b.pk_kostra_account_code
JOIN [gco_year_list] y ON 1=1 AND y.budget_year BETWEEN DATEPART(year,src.dateFrom) and DATEPART(year, src.dateTo)
WHERE src.pk_tenant_id = @fk_tenant_id 



DELETE FROM flat_projects where fk_tenant_id = @fk_tenant_id

INSERT INTO flat_projects
select 
pk_project_code	
,fk_tenant_id	
,project_name	
,budget_year
,updated = GETDATE()
,updated_by = @user_id
from [tco_projects] p
JOIN [gco_year_list] y ON 1=1
WHERE p.fk_tenant_id = @fk_tenant_id 
AND y.budget_year BETWEEN DATEPART(year, p.date_from) AND DATEPART(year, p.date_to)

END



DROP TABLE IF EXISTS #hlptab1
DROP TABLE IF EXISTS #hlptab2
DROP TABLE IF EXISTS #hlptab3
DROP TABLE IF EXISTS #hlptab4
DROP TABLE IF EXISTS #hlptab5
DROP TABLE IF EXISTS #hlptab6
DROP TABLE IF EXISTS #hlptab7
DROP TABLE IF EXISTS #hlptab8
DROP TABLE IF EXISTS #hlptab9
DROP TABLE IF EXISTS #prev_dep



GO