CREATE OR ALTER PROCEDURE [dbo].[prcDeleteAccountingData]  
 @TenantID int,  
 @UserID int ,  
 @jobID bigint,  
 @importintoAccoutingData bit,
 @importintoAccoutingDataDetail bit,
 @budgetYear int,
 @deleteStagedData bit
AS  
BEGIN   

	IF(@deleteStagedData = 0)
	BEGIN
			--This Stored Procedure Delete Records in Batches in order to prevent locks on transaction tables tfp_accounting_data and tfp_accounting_data_detail
			DECLARE @TenantID_1 int,@importintoAccoutingData_1 bit,@importintoAccoutingDataDetail_1 int 
			SET @TenantID_1 = @TenantID
			SET @importintoAccoutingData_1 = @importintoAccoutingData
			SET @importintoAccoutingDataDetail_1 = @importintoAccoutingDataDetail

			DECLARE @pkidcountAccData INT ,@pkidcountAccDataDetail INT , @batchtoProcess INT
			CREATE TABLE #recordstodelete (pk_id BIGINT)
			CREATE TABLE #recordstodelete_detail (pk_id BIGINT)

			SELECT DISTINCT T2.pk_id into #pkid FROM tfp_accounting_data as T2 JOIN tbu_stage_accounting_import as T1  
			ON T2.fk_tenant_id = T1.fk_tenant_id AND T2.[period]= T1.[period] 
			WHERE T1.fk_tenant_id = @TenantID AND T1.[user_id]=@UserID and T1.gl_year = @budgetYear;

			SELECT DISTINCT T2.pk_id into #pkidDetail FROM tfp_accounting_data_detail as T2 JOIN tbu_stage_accounting_import as T1  
			ON T2.fk_tenant_id = T1.fk_tenant_id  AND T2.[period]= T1.[period] 
			WHERE T1.fk_tenant_id = @TenantID AND  T1.[user_id]=@UserID and T1.gl_year = @budgetYear;

			IF (@importintoAccoutingData = 0 AND @importintoAccoutingDataDetail = 0)
			BEGIN
				SET @pkidcountAccData = (SELECT count (1) FROM #pkid)
				UPDATE tco_job_status Set  total_steps= total_steps + @pkidcountAccData  where pk_id= @jobID   and fk_tenant_id = @TenantID  
		  
				SET @pkidcountAccDataDetail = (SELECT count (1) FROM #pkidDetail)
				UPDATE tco_job_status Set  total_steps= total_steps + @pkidcountAccDataDetail  where pk_id= @jobID   and fk_tenant_id = @TenantID  

				WHILE @pkidcountAccData != 0
				BEGIN
					INSERT #recordstodelete
					SELECT TOP 2000  pk_id from #pkid
					SET @batchtoProcess = 	(SELECT count (1) FROM #recordstodelete)
				 
					DELETE  FROM tfp_accounting_data   where pk_id  in (select  pk_id from #recordstodelete) 
					DELETE  FROM #pkid   where pk_id  in (select  pk_id from #recordstodelete) 
					TRUNCATE TABLE #recordstodelete
					SET @pkidcountAccData= (SELECT count (1) FROM #pkid)
					UPDATE tco_job_status Set  steps_completed= steps_completed + @batchtoProcess  where pk_id= @jobID   and fk_tenant_id = @TenantID  
				END

				WHILE @pkidcountAccDataDetail != 0
				BEGIN
					INSERT #recordstodelete_detail
					SELECT TOP 2000  pk_id from #pkidDetail		 
					SET @batchtoProcess = 	(SELECT count (1) FROM #recordstodelete_detail)
			
					DELETE  FROM tfp_accounting_data_detail where pk_id  in (select  pk_id from #recordstodelete_detail) 
					DELETE  FROM #pkidDetail   where pk_id  in (select  pk_id from #recordstodelete_detail) 
					TRUNCATE TABLE #recordstodelete_detail
					SET @pkidcountAccDataDetail= (SELECT count (1) FROM #pkidDetail)
					UPDATE tco_job_status Set  steps_completed= steps_completed + @batchtoProcess  where pk_id= @jobID   and fk_tenant_id = @TenantID  
				END
			END

			IF (@importintoAccoutingData = 1 AND @importintoAccoutingDataDetail = 0)
			BEGIN
				SET @pkidcountAccData = (SELECT count (1) FROM #pkid)
				UPDATE tco_job_status Set  total_steps= total_steps + @pkidcountAccData  where pk_id= @jobID   and fk_tenant_id = @TenantID  
		
				WHILE @pkidcountAccData != 0
				BEGIN
					INSERT #recordstodelete
					SELECT TOP 2000  pk_id from #pkid		 
					SET @batchtoProcess = 	(SELECT count (1) FROM #recordstodelete)
			
					DELETE  FROM tfp_accounting_data   where pk_id  in (select  pk_id from #recordstodelete) 
					DELETE  FROM #pkid   where pk_id  in (select  pk_id from #recordstodelete) 
					TRUNCATE TABLE #recordstodelete
					SET @pkidcountAccData= (SELECT count (1) FROM #pkid)
					UPDATE tco_job_status Set  steps_completed= steps_completed + @batchtoProcess  where pk_id= @jobID   and fk_tenant_id = @TenantID  
				END 
			END

			IF (@importintoAccoutingData = 0 AND @importintoAccoutingDataDetail = 1)
			BEGIN
				SET @pkidcountAccDataDetail = (SELECT count (1) FROM #pkidDetail)
				UPDATE tco_job_status Set  total_steps= total_steps + @pkidcountAccDataDetail  where pk_id= @jobID   and fk_tenant_id = @TenantID  
		
				WHILE @pkidcountAccDataDetail != 0
				BEGIN
					INSERT #recordstodelete_detail
					SELECT TOP 2000  pk_id from #pkidDetail		 
					SET @batchtoProcess = 	(SELECT count (1) FROM #recordstodelete_detail)
			
					DELETE  FROM tfp_accounting_data_detail where pk_id  in (select  pk_id from #recordstodelete_detail) 
					DELETE  FROM #pkidDetail   where pk_id  in (select  pk_id from #recordstodelete_detail) 
					TRUNCATE TABLE #recordstodelete_detail
					SET @pkidcountAccDataDetail= (SELECT count (1) FROM #pkidDetail)
					UPDATE tco_job_status Set  steps_completed= steps_completed + @batchtoProcess  where pk_id= @jobID   and fk_tenant_id = @TenantID  
				END
			END

			DROP TABLE IF EXISTS #pkid 
			DROP TABLE IF EXISTS #pkidDetail 
			DROP TABLE IF EXISTS #recordstodelete 
			DROP TABLE IF EXISTS #recordstodelete_detail 
	END
	ELSE
	BEGIN
		DELETE from tbu_stage_accounting_import WHERE fk_tenant_id = @TenantID AND  [user_id]=@UserID and gl_year = @budgetYear AND job_id = @jobID; 
	END

END  

 
