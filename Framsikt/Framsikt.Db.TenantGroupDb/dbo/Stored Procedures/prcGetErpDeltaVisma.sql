CREATE OR ALTER PROCEDURE [dbo].[prcGetErpDeltaVisma]
(
	@v1_export_id	BIGINT,
	@v2_export_id	BIGINT
)
AS
BEGIN

--Get defaults and set the value  instead of empty 
DECLARE @defProject VARCHAR(50) 
DECLARE @defFreeDim1 VARCHAR(50) 
DECLARE @defFreeDim2 VARCHAR(50) 
DECLARE @defFreeDim3 VARCHAR(50) 
DECLARE @defFreeDim4 VARCHAR(50) 
DECLARE @defAlterCode VARCHAR(50) 
DECLARE @tenantId INT 
DECLARE @orgVersion VARCHAR(50) 
DECLARE @budgetYear VARCHAR(10) 
 
select top 1 @tenantId=fk_tenant_id,@budgetYear=budget_year from tfp_erp_export_log_visma WHERE export_id = @v1_export_id 
 
select @orgVersion=pk_org_version from tco_org_version where fk_tenant_id=@tenantId and @budgetYear+'01' between period_from and period_to 
 
select @defProject= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='PROJECT' and fk_org_version= @orgVersion 
 
select @defFreeDim1= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_1' and fk_org_version= @orgVersion 
 
select @defFreeDim2= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_2' and fk_org_version= @orgVersion 
 
select @defFreeDim3= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_3' and fk_org_version= @orgVersion 
 
select @defFreeDim4= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_4' and fk_org_version= @orgVersion 
 
select @defAlterCode= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='ALTER_CODE' and fk_org_version= @orgVersion 

 
DECLARE @Param_Value NVARCHAR(10) 
SET @Param_Value='false' 
select @Param_Value=lower(param_value) from tco_parameters where param_name='ERP_SHOW_ADJ_ALTER_CODES' and fk_tenant_id = @tenantId and active = 1 
 
--To do : add budget year check 
	IF(@Param_Value ='true') 
	 BEGIN 
		SELECT * FROM 
		(SELECT f.description,Company,Region,FkAccountCode,DepartmentCode,FkFunctionCode, 
		CASE WHEN FkProjectCode='' or FkProjectCode is null THEN isnull(@defProject,'') 
		ELSE isnull(FkProjectCode,'') 
		END [FkProjectCode], 
		CASE WHEN FreeDim1='' or FreeDim1 is null THEN isnull(@defFreeDim1,'') 
		ELSE isnull(FreeDim1,'') 
		END [FreeDim1], 
		CASE WHEN FreeDim2='' or FreeDim2 is null THEN isnull(@defFreeDim2,'') 
		ELSE isnull(FreeDim2,'') 
		END [FreeDim2], 
		CASE WHEN FreeDim3='' or FreeDim3 is null THEN isnull(@defFreeDim3,'') 
		ELSE isnull(FreeDim3,'') 
		END [FreeDim3], 
		CASE WHEN FreeDim4='' or FreeDim4 is null THEN isnull(@defFreeDim4,'') 
		ELSE isnull(FreeDim4,'') 
		END [FreeDim4], 
		FkAdjustmentCode = CASE WHEN adj.prefix_adjCode = '' or adj.prefix_adjCode IS NULL THEN ISNULL(f.FkAdjustmentCode,'') ELSE adj.prefix_adjCode END, 
		CASE WHEN FkAlterCode='' or FkAlterCode is null THEN isnull(@defAlterCode,'') 
		ELSE isnull(FkAlterCode,'') 
		END [FkAlterCode], 
		SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec  
		FROM( 
				SELECT			description 
							,Company 
							,Region 
							,FkAccountCode 
							,DepartmentCode 
							,FkFunctionCode 
							,FkProjectCode 
							,FreeDim1 
							,FreeDim2 
							,FreeDim3 
							,FreeDim4 
							,FreeDim5 
							,FreeDim6 
							,FkAdjustmentCode 
							,FkAlterCode 
							,RevisedBudget = SUM(v2_RevisedBudget - v1_RevisedBudget) 
							,Jan = SUM(v2_Jan - v1_Jan) 
							,Feb = SUM(v2_Feb - v1_Feb) 
							,Mar = SUM(v2_Mar - v1_Mar) 
							,Apr = SUM(v2_Apr - v1_Apr) 
							,May = SUM(v2_May - v1_May) 
							,Jun = SUM(v2_Jun - v1_Jun) 
							,Jul = SUM(v2_Jul - v1_Jul) 
							,Aug = SUM(v2_Aug - v1_Aug) 
							,Sep = SUM(v2_Sep - v1_Sep) 
							,Oct = SUM(v2_Oct - v1_Oct) 
							,Nov = SUM(v2_Nov - v1_Nov) 
							,Dec = SUM(v2_Dec - v1_Dec)
			FROM ( 
					SELECT	description = description 
							,Company = company 
							,Region = region 
							,FkAccountCode = fk_account_code 
							,DepartmentCode = department_code 
							,FkFunctionCode = fk_function_code 
							,FkProjectCode = fk_project_code 
							,FreeDim1 = free_dim_1 
							,FreeDim2 = free_dim_2 
							,FreeDim3 = free_dim_3 
							,FreeDim4 = free_dim_4 
							,FreeDim5 = free_dim_5 
							,FreeDim6 = free_dim_6 
							,FkAdjustmentCode = fk_adjustment_code 
							,FkAlterCode = '' 
							,v1_RevisedBudget = revised_budget 
							,v1_Jan = jan 
							,v1_Feb = feb 
							,v1_Mar = mar 
							,v1_Apr = apr 
							,v1_May = may 
							,v1_Jun = jun 
							,v1_Jul = jul 
							,v1_Aug = aug 
							,v1_Sep = sep 
							,v1_Oct = oct 
							,v1_Nov = nov 
							,v1_Dec = dec 
							,v2_RevisedBudget = 0 
							,v2_Jan			  = 0 
							,v2_Feb			  = 0 
							,v2_Mar			  = 0 
							,v2_Apr			  = 0 
							,v2_May			  = 0 
							,v2_Jun			  = 0 
							,v2_Jul			  = 0 
							,v2_Aug			  = 0 
							,v2_Sep			  = 0 
							,v2_Oct			  = 0 
							,v2_Nov			  = 0 
							,v2_Dec			  = 0 
					FROM tfp_erp_export_log_visma WHERE export_id = @v1_export_id 
					UNION ALL 
					SELECT description = v2.description 
							,Company = company 
							,Region = region 
							,FkAccountCode = fk_account_code 
							,DepartmentCode = department_code 
							,FkFunctionCode = fk_function_code 
							,FkProjectCode = fk_project_code 
							,FreeDim1 = free_dim_1 
							,FreeDim2 = free_dim_2 
							,FreeDim3 = free_dim_3 
							,FreeDim4 = free_dim_4 
							,FreeDim5 = free_dim_5 
							,FreeDim6 = free_dim_6 
							,FkAdjustmentCode = fk_adjustment_code 
							,FkAlterCode = '' 
							,v1_RevisedBudget = 0 
							,v1_Jan = 0 
							,v1_Feb = 0 
							,v1_Mar = 0 
							,v1_Apr = 0 
							,v1_May = 0 
							,v1_Jun = 0 
							,v1_Jul = 0 
							,v1_Aug = 0 
							,v1_Sep = 0 
							,v1_Oct = 0 
							,v1_Nov = 0 
							,v1_Dec = 0 
							,v2_RevisedBudget = revised_budget 
							,v2_Jan			  = jan 
							,v2_Feb			  = feb 
							,v2_Mar			  = mar 
							,v2_Apr			  = apr 
							,v2_May			  = may 
							,v2_Jun			  = jun 
							,v2_Jul			  = jul 
							,v2_Aug			  = aug 
							,v2_Sep			  = sep 
							,v2_Oct			  = oct 
							,v2_Nov			  = nov 
							,v2_Dec			  = dec 
					FROM tfp_erp_export_log_visma v2 
					LEFT JOIN tco_user_adjustment_codes adj ON fk_adjustment_code = adj.pk_adj_code AND adj.fk_tenant_id = @tenantId AND adj.budget_year = @budgetYear 
					WHERE export_id = @v2_export_id 
			) TRANS 
			GROUP BY description,Company,Region,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FreeDim5,FreeDim6,FkAdjustmentCode,FkAlterCode
			HAVING 	SUM(v2_RevisedBudget - v1_RevisedBudget) <> 0 OR 
					SUM(v2_Jan - v1_Jan) <> 0 OR 
					SUM(v2_Feb - v1_Feb) <> 0 OR 
					SUM(v2_Mar - v1_Mar) <> 0 OR 
					SUM(v2_Apr - v1_Apr) <> 0 OR 
					SUM(v2_May - v1_May) <> 0 OR 
					SUM(v2_Jun - v1_Jun) <> 0 OR 
					SUM(v2_Jul - v1_Jul) <> 0 OR 
					SUM(v2_Aug - v1_Aug) <> 0 OR 
					SUM(v2_Sep - v1_Sep) <> 0 OR 
					SUM(v2_Oct - v1_Oct) <> 0 OR 
					SUM(v2_Nov - v1_Nov) <> 0 OR 
					SUM(v2_Dec - v1_Dec) <> 0 
		) f  
		LEFT JOIN tco_user_adjustment_codes adj ON f.FkAdjustmentCode = adj.pk_adj_code AND adj.fk_tenant_id = @tenantId AND adj.budget_year = @budgetYear 
		WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
		OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 

		GROUP BY f.description,Company,Region,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode,adj.prefix_adjCode)  a 
		WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
		OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
	END 
	ELSE 
	 BEGIN 
		SELECT * FROM 
		(SELECT description,Company,Region,FkAccountCode,DepartmentCode,FkFunctionCode, 
		CASE WHEN FkProjectCode='' or FkProjectCode is null THEN isnull(@defProject,'') 
		ELSE isnull(FkProjectCode,'') 
		END [FkProjectCode], 
		CASE WHEN FreeDim1='' or FreeDim1 is null THEN isnull(@defFreeDim1,'') 
		ELSE isnull(FreeDim1,'') 
		END [FreeDim1], 
		CASE WHEN FreeDim2='' or FreeDim2 is null THEN isnull(@defFreeDim2,'') 
		ELSE isnull( FreeDim2,'') 
		END [FreeDim2], 
		CASE WHEN FreeDim3='' or FreeDim3 is null THEN isnull(@defFreeDim3,'') 
		ELSE isnull(FreeDim3,'') 
		END [FreeDim3], 
		CASE WHEN FreeDim4='' or FreeDim4 is null THEN isnull(@defFreeDim4,'') 
		ELSE isnull( FreeDim4,'') 
		END [FreeDim4], 
		'' FkAdjustmentCode, 
		'' [FkAlterCode], 
		SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec  
		FROM( 
					SELECT	description 
							,Company 
							,Region 
							,FkAccountCode 
							,DepartmentCode 
							,FkFunctionCode 
							,FkProjectCode 
							,FreeDim1 
							,FreeDim2 
							,FreeDim3 
							,FreeDim4 
							,FreeDim5 
							,FreeDim6 
							,FkAdjustmentCode 
							,FkAlterCode 
							,RevisedBudget = SUM(v2_RevisedBudget - v1_RevisedBudget) 
							,Jan = SUM(v2_Jan - v1_Jan) 
							,Feb = SUM(v2_Feb - v1_Feb) 
							,Mar = SUM(v2_Mar - v1_Mar) 
							,Apr = SUM(v2_Apr - v1_Apr) 
							,May = SUM(v2_May - v1_May) 
							,Jun = SUM(v2_Jun - v1_Jun) 
							,Jul = SUM(v2_Jul - v1_Jul) 
							,Aug = SUM(v2_Aug - v1_Aug) 
							,Sep = SUM(v2_Sep - v1_Sep) 
							,Oct = SUM(v2_Oct - v1_Oct) 
							,Nov = SUM(v2_Nov - v1_Nov) 
							,Dec = SUM(v2_Dec - v1_Dec) 
			FROM ( 
					SELECT	description = description 
							,Company = company 
							,Region = region 
							,FkAccountCode = fk_account_code 
							,DepartmentCode = department_code 
							,FkFunctionCode = fk_function_code 
							,FkProjectCode = fk_project_code 
							,FreeDim1 = free_dim_1 
							,FreeDim2 = free_dim_2 
							,FreeDim3 = free_dim_3 
							,FreeDim4 = free_dim_4 
							,FreeDim5 = free_dim_5 
							,FreeDim6 = free_dim_6 
							,FkAdjustmentCode = fk_adjustment_code 
							,FkAlterCode = fk_alter_code 
							,v1_RevisedBudget = revised_budget 
							,v1_Jan = jan 
							,v1_Feb = feb 
							,v1_Mar = mar 
							,v1_Apr = apr 
							,v1_May = may 
							,v1_Jun = jun 
							,v1_Jul = jul 
							,v1_Aug = aug 
							,v1_Sep = sep 
							,v1_Oct = oct 
							,v1_Nov = nov 
							,v1_Dec = dec 
							,v2_RevisedBudget = 0 
							,v2_Jan			  = 0 
							,v2_Feb			  = 0 
							,v2_Mar			  = 0 
							,v2_Apr			  = 0 
							,v2_May			  = 0 
							,v2_Jun			  = 0 
							,v2_Jul			  = 0 
							,v2_Aug			  = 0 
							,v2_Sep			  = 0 
							,v2_Oct			  = 0 
							,v2_Nov			  = 0 
							,v2_Dec			  = 0
					FROM tfp_erp_export_log_visma WHERE export_id = @v1_export_id 
					UNION ALL 
					SELECT description = v2.description 
							,Company = company 
							,Region = region 
							,FkAccountCode = fk_account_code 
							,DepartmentCode = department_code 
							,FkFunctionCode = fk_function_code 
							,FkProjectCode = fk_project_code 
							,FreeDim1 = free_dim_1 
							,FreeDim2 = free_dim_2 
							,FreeDim3 = free_dim_3 
							,FreeDim4 = free_dim_4 
							,FreeDim5 = free_dim_5 
							,FreeDim6 = free_dim_6 
							,FkAdjustmentCode = fk_adjustment_code 
							,FkAlterCode = fk_alter_code 
							,v1_RevisedBudget = 0 
							,v1_Jan = 0 
							,v1_Feb = 0 
							,v1_Mar = 0 
							,v1_Apr = 0 
							,v1_May = 0 
							,v1_Jun = 0 
							,v1_Jul = 0 
							,v1_Aug = 0 
							,v1_Sep = 0 
							,v1_Oct = 0 
							,v1_Nov = 0 
							,v1_Dec = 0 
							,v2_RevisedBudget = revised_budget 
							,v2_Jan			  = jan 
							,v2_Feb			  = feb 
							,v2_Mar			  = mar 
							,v2_Apr			  = apr 
							,v2_May			  = may 
							,v2_Jun			  = jun 
							,v2_Jul			  = jul 
							,v2_Aug			  = aug 
							,v2_Sep			  = sep 
							,v2_Oct			  = oct 
							,v2_Nov			  = nov 
							,v2_Dec			  = dec
					FROM tfp_erp_export_log_visma v2 
                    LEFT JOIN tco_user_adjustment_codes adj ON fk_adjustment_code = adj.pk_adj_code AND adj.fk_tenant_id = @tenantId AND adj.budget_year = @budgetYear  
                    WHERE export_id = @v2_export_id 
			) TRANS             
			GROUP BY description,Company,Region,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FreeDim5,FreeDim6,FkAdjustmentCode,FkAlterCode 
			HAVING 	SUM(v2_RevisedBudget - v1_RevisedBudget) <> 0 OR 
					SUM(v2_Jan - v1_Jan) <> 0 OR 
					SUM(v2_Feb - v1_Feb) <> 0 OR 
					SUM(v2_Mar - v1_Mar) <> 0 OR 
					SUM(v2_Apr - v1_Apr) <> 0 OR 
					SUM(v2_May - v1_May) <> 0 OR 
					SUM(v2_Jun - v1_Jun) <> 0 OR 
					SUM(v2_Jul - v1_Jul) <> 0 OR 
					SUM(v2_Aug - v1_Aug) <> 0 OR 
					SUM(v2_Sep - v1_Sep) <> 0 OR 
					SUM(v2_Oct - v1_Oct) <> 0 OR 
					SUM(v2_Nov - v1_Nov) <> 0 OR 
					SUM(v2_Dec - v1_Dec) <> 0 
		) f      
		WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
		OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
		GROUP BY description,Company,Region,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4)  a 
		WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
		OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
 
	END 
END 
	 

GO