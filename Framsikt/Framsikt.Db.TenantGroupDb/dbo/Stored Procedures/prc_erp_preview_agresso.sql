CREATE OR ALTER PROCEDURE [dbo].[prc_erp_preview_agresso]

@tenant_id INT,
@budget_year INT

AS

DROP TABLE IF EXISTS #account_list
 
--Fetch accounts to be included
SELECT b.pk_tenant_id, b.pk_account_code 
INTO #account_list
FROM tco_accounts b
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code and c.type = 'investment'
WHERE @budget_year between DATEPART(YEAR,b.dateFrom) and DATEPART(YEAR,b.dateTo)
AND b.pk_tenant_id = @tenant_id
GROUP BY b.pk_tenant_id, b.pk_account_code

DROP TABLE IF EXISTS #erp_temp

SELECT *
INTO #erp_temp
FROM (

	--Fetch operations budget
	SELECT fk_tenant_id[FkTenantId],description [description], budget_year[BudgetYear], fk_account_code[FkAccountCode],department_code[DepartmentCode],fk_function_code[FkFunctionCode],fk_project_code[FkProjectCode],free_dim_1[FreeDim1],free_dim_2[FreeDim2],free_dim_3[FreeDim3],free_dim_4[FreeDim4],
	isnull(fk_adjustment_code,'') [FkAdjustmentCode],isnull(fk_alter_code ,'')[FkAlterCode],
	SUM(jan) Jan, SUM(feb) Feb,SUM(mar) Mar,SUM(apr) Apr,SUM(may) May,SUM(jun) Jun,SUM(jul) Jul,SUM(aug) Aug,SUM(sep) Sep,SUM(oct) Oct,SUM(nov) Nov,SUM(dec) Dec,fk_action_id[FkActionId]
	FROM
	(SELECT a.fk_tenant_id, a.budget_year,a.description [description], fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
	 a.fk_adjustment_code,
	 a.fk_alter_code,
	period,amount_year_1, round(amount_year_1, -3) as belop_i_1000,
	jan =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '01' THEN amount_year_1 ELSE 0 END,
	feb =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '02' THEN amount_year_1 ELSE 0 END,
	mar =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '03' THEN amount_year_1 ELSE 0 END,
	apr =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '04' THEN amount_year_1 ELSE 0 END,
	may =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '05' THEN amount_year_1 ELSE 0 END,
	jun =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '06' THEN amount_year_1 ELSE 0 END,
	jul =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '07' THEN amount_year_1 ELSE 0 END,
	aug =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '08' THEN amount_year_1 ELSE 0 END,
	sep =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '09' THEN amount_year_1 ELSE 0 END,
	oct =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '10' THEN amount_year_1 ELSE 0 END,
	nov =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '11' THEN amount_year_1 ELSE 0 END,
	dec =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '12' THEN amount_year_1 ELSE 0 END,
	fk_action_id
	FROM tbu_trans_detail a
	JOIN tco_user_adjustment_codes uad on a.fk_adjustment_code=uad.pk_adj_code and a.budget_year=uad.budget_year and a.fk_tenant_id=uad.fk_tenant_id and uad.status=1
	JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND @budget_year BETWEEN datepart(year, datefrom) AND datepart(year, dateTo)
	JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = 'YBUD1'
	WHERE amount_year_1 != 0 --and department_code NOT IN(SELECT department_code FROM @departmentList) AND fk_action_id=0
	AND a.fk_tenant_id=@tenant_id and a.budget_year=@budget_year
	) A
	GROUP BY fk_action_id,fk_tenant_id, budget_year,fk_account_code,department_code,fk_function_code, fk_project_code, free_dim_1, free_dim_2,free_dim_3,free_dim_4,description,fk_adjustment_code,fk_alter_code
 

	UNION ALL

	--Fetch investment budget pre 2020
	SELECT  fk_tenant_id [FkTenantId],description [description], budget_year[BudgetYear], fk_account_code[FkAccountCode],department_code[DepartmentCode],fk_function_code[FkFunctionCode],fk_project_code[FkProjectCode],free_dim_1[FreeDim1],free_dim_2[FreeDim2],free_dim_3[FreeDim3],free_dim_4[FreeDim4],
	isnull(fk_adjustment_code,'') [FkAdjustmentCode],isnull(fk_alter_code,'') [FkAlterCode],
	SUM(jan) Jan, SUM(feb) Feb,SUM(mar) Mar,SUM(apr) Apr,SUM(may) May,SUM(jun) Jun,SUM(jul) Jul,SUM(aug) Aug,SUM(sep) Sep,SUM(oct) Oct,SUM(nov) Nov,SUM(dec) Dec,fk_action_id[FkActionId]
	FROM
	(SELECT a.fk_tenant_id,a. budget_year,a.description [description], fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
	a.fk_adjustment_code,fk_alter_code,
	period,amount_year_1, round(amount_year_1, -3) as belop_i_1000,
	jan =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '01' THEN amount_year_1 ELSE 0 END,
	feb =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '02' THEN amount_year_1 ELSE 0 END,
	mar =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '03' THEN amount_year_1 ELSE 0 END,
	apr =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '04' THEN amount_year_1 ELSE 0 END,
	may =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '05' THEN amount_year_1 ELSE 0 END,
	jun =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '06' THEN amount_year_1 ELSE 0 END,
	jul =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '07' THEN amount_year_1 ELSE 0 END,
	aug =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '08' THEN amount_year_1 ELSE 0 END,
	sep =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '09' THEN amount_year_1 ELSE 0 END,
	oct =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '10' THEN amount_year_1 ELSE 0 END,
	nov =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '11' THEN amount_year_1 ELSE 0 END,
	dec =  CASE WHEN SUBSTRING(convert(char(6),period),5,2) = '12' THEN amount_year_1 ELSE 0 END,
	fk_action_id
	FROM tbu_trans_detail a
	JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND @budget_year BETWEEN datepart(year, datefrom) AND datepart(year, dateTo)
	JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = 'B2A'
	LEFT JOIN tco_user_adjustment_codes uad on a.fk_adjustment_code=uad.pk_adj_code and a.budget_year=uad.budget_year and a.fk_tenant_id=uad.fk_tenant_id
	WHERE amount_year_1 != 0
	--AND fk_action_id=0 
	AND a.fk_tenant_id=@tenant_id 
	and @budget_year < 2020
	and a.budget_year=@budget_year
 
	) C
	GROUP BY fk_action_id,fk_tenant_id, budget_year,fk_account_code,department_code,fk_function_code, fk_project_code, free_dim_1, free_dim_2,free_dim_3,free_dim_4,[description],fk_adjustment_code,fk_alter_code
 
	UNION ALL

	--Fetch investment budget for 2020
	SELECT  
	fk_tenant_id [FkTenantId]
	,ISNULL(description,'') [description]
	, budget_year[BudgetYear]
	, fk_account_code[FkAccountCode]
	,department_code[DepartmentCode]
	,fk_function_code[FkFunctionCode]
	,fk_project_code[FkProjectCode]
	,free_dim_1[FreeDim1]
	,free_dim_2[FreeDim2]
	,free_dim_3[FreeDim3]
	,free_dim_4[FreeDim4]
	,isnull(fk_adjustment_code,'') [FkAdjustmentCode]
	,isnull(fk_alter_code,'') [FkAlterCode]
	,SUM(new_model_inv) Jan
	, 0 as Feb,0 as Mar,0 as Apr,0 as May,0 as Jun,0 as Jul,0 as Aug,0 as Sep,0 as Oct,0 as Nov,0 as Dec
	,0 as [FkActionId]
	FROM
	(
	select PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, SUM(pt.amount) as new_model_inv
	, PT.description
	, @budget_year as budget_year
	, fk_adjustment_code = PT.fk_user_adjustment_code
	, PT.fk_alter_code
	from tfp_proj_transactions PT
	LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	JOIN #account_list ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
	select fk_tenant_id, pk_change_id from tfp_budget_changes
	where budget_year <= @budget_year
	)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
	where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
	AND year = @budget_year
	AND @budget_year = 2020
	AND pt.fk_tenant_id = @tenant_id
	GROUP BY PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, PT.description
	, PT.fk_user_adjustment_code
	, PT.fk_alter_code
	HAVING ABS(SUM(amount))>0
	) INV_new
	GROUP BY fk_tenant_id ,description, budget_year , fk_account_code,department_code ,fk_function_code,fk_project_code ,free_dim_1 ,free_dim_2 ,free_dim_3 ,free_dim_4 ,fk_adjustment_code,fk_alter_code

	UNION ALL

	--Fetch investment budget for 2021 onwards. Added to change logic with inner join in tco_projects but not disturb the 2020 budgets
	SELECT  
	fk_tenant_id [FkTenantId]
	,ISNULL(description,'') [description]
	, budget_year[BudgetYear]
	, fk_account_code[FkAccountCode]
	,department_code[DepartmentCode]
	,fk_function_code[FkFunctionCode]
	,fk_project_code[FkProjectCode]
	,free_dim_1[FreeDim1]
	,free_dim_2[FreeDim2]
	,free_dim_3[FreeDim3]
	,free_dim_4[FreeDim4]
	,isnull(fk_adjustment_code,'') [FkAdjustmentCode]
	,isnull(fk_alter_code,'') [FkAlterCode]
	,SUM(new_model_inv) Jan
	, 0 as Feb,0 as Mar,0 as Apr,0 as May,0 as Jun,0 as Jul,0 as Aug,0 as Sep,0 as Oct,0 as Nov,0 as Dec
	,0 as [FkActionId]
	FROM
	(
	select PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, SUM(pt.amount) as new_model_inv
	, PT.description
	, @budget_year as budget_year
	, fk_adjustment_code = CONVERT(VARCHAR(4),@budget_year)+'VBUD'
	, fk_alter_code = ''
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	JOIN #account_list ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id from tfp_budget_changes
			where fk_tenant_id = @tenant_id
			and budget_year < @budget_year
			UNION
			select fk_tenant_id, pk_change_id from tfp_budget_changes
			where fk_tenant_id = @tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 1
	)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
	where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
	AND year = @budget_year
	AND @budget_year > 2020
	AND pt.fk_tenant_id = @tenant_id
	GROUP BY PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, PT.description, PT.fk_user_adjustment_code, PT.fk_alter_code
	HAVING ABS(SUM(amount))>0

	UNION ALL

	select PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, SUM(pt.amount) as new_model_inv
	, PT.description
	, @budget_year as budget_year
	, fk_adjustment_code = pt.fk_user_adjustment_code
	, PT.fk_alter_code
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	JOIN #account_list ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id from tfp_budget_changes
			where fk_tenant_id = @tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 0
	)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
	where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
	AND year = @budget_year
	AND @budget_year > 2020
	AND pt.fk_tenant_id = @tenant_id
	GROUP BY PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	, PT.description, PT.fk_user_adjustment_code, PT.fk_alter_code
	HAVING ABS(SUM(amount))>0
	) INV_new
	GROUP BY fk_tenant_id ,description, budget_year , fk_account_code,department_code ,fk_function_code,fk_project_code ,free_dim_1 ,free_dim_2 ,free_dim_3 ,free_dim_4 ,fk_adjustment_code,fk_alter_code
) A



/*************  START CALCULATION OF DELTA   *************/

DECLARE @v1_export_id	BIGINT
SET @v1_export_id = (select MAX(id) from tfp_erp_exports where tenant_id = @tenant_id and budget_Year = @budget_year and is_delta = 1)

BEGIN

--Get defaults and set the value  instead of empty
DECLARE @defProject VARCHAR(50)
DECLARE @defFreeDim1 VARCHAR(50)
DECLARE @defFreeDim2 VARCHAR(50)
DECLARE @defFreeDim3 VARCHAR(50)
DECLARE @defFreeDim4 VARCHAR(50)
DECLARE @defAlterCode VARCHAR(50)
DECLARE @tenantId INT
DECLARE @orgVersion VARCHAR(50)
DECLARE @budgetYear VARCHAR(10)


set @tenantId = @tenant_id
SET @budgetYear = @budget_year

select @orgVersion=pk_org_version from tco_org_version where fk_tenant_id=@tenantId and @budgetYear+'01' between period_from and period_to

select @defProject= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='PROJECT' and fk_org_version= @orgVersion

select @defFreeDim1= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_1' and fk_org_version= @orgVersion

select @defFreeDim2= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_2' and fk_org_version= @orgVersion

select @defFreeDim3= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_3' and fk_org_version= @orgVersion

select @defFreeDim4= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_4' and fk_org_version= @orgVersion

select @defAlterCode= acc_value from tmd_acc_defaults
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='ALTER_CODE' and fk_org_version= @orgVersion

DECLARE @Param_Value NVARCHAR(10)
SET @Param_Value='false'
select @Param_Value=lower(param_value) from tco_parameters where param_name='ERP_SHOW_ADJ_ALTER_CODES' and fk_tenant_id = @tenantId and active = 1

--To do : add budget year check

	IF(@Param_Value ='true')
	 BEGIN
        SELECT * FROM
        (SELECT f.description,FkAccountCode,DepartmentCode,FkFunctionCode,
        CASE WHEN FkProjectCode ='' or FkProjectCode IS NULL THEN isnull(@defProject,'')
        ELSE isnull(FkProjectCode,'')
        END [FkProjectCode],
        CASE WHEN ISNULL(FreeDim1,'')='' or FreeDim1 is null  THEN isnull(@defFreeDim1,'')
        ELSE isnull(FreeDim1,'')
        END [FreeDim1],
        CASE WHEN ISNULL(FreeDim2,'')='' or FreeDim2 is null  THEN isnull(@defFreeDim2,'')
        ELSE isnull(FreeDim2,'')
        END [FreeDim2],
        CASE WHEN ISNULL(FreeDim3,'')='' or FreeDim3 is null  THEN isnull(@defFreeDim3,'')
        ELSE isnull(FreeDim3,'')
        END [FreeDim3],
        CASE WHEN ISNULL(FreeDim4,'')='' or FreeDim4 is null  THEN isnull(@defFreeDim4,'')
        ELSE isnull(FreeDim4,'')
        END [FreeDim4],
        FkAdjustmentCode = CASE WHEN adj.prefix_adjCode = '' or adj.prefix_adjCode IS NULL THEN ISNULL(f.FkAdjustmentCode,'') ELSE adj.prefix_adjCode END
		,CASE WHEN FkAlterCode='' or FkAlterCode is null THEN isnull(@defAlterCode,'')
        ELSE isnull(FkAlterCode,'')
        END [FkAlterCode],
        SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec 
		FROM (
               SELECT	description
						,FkAccountCode
						,DepartmentCode
						,FkFunctionCode
						,FkProjectCode
						,FreeDim1
						,FreeDim2
						,FreeDim3
						,FreeDim4
						,FkAdjustmentCode
						,FkAlterCode
						,Jan = SUM(v2_Jan-v1_Jan)
						,Feb = SUM(v2_Feb-v1_Feb)
						,Mar = SUM(v2_Mar-v1_Mar)
						,Apr = SUM(v2_Apr-v1_Apr)
						,May = SUM(v2_May-v1_May)
						,Jun = SUM(v2_Jun-v1_Jun)
						,Jul = SUM(v2_Jul-v1_Jul)
						,Aug = SUM(v2_Aug-v1_Aug)
						,Sep = SUM(v2_Sep-v1_Sep)
						,Oct = SUM(v2_Oct-v1_Oct)
						,Nov = SUM(v2_Nov-v1_Nov)
						,Dec = SUM(v2_Dec-v1_Dec)
				FROM ( 
						SELECT	description = description
								,FkAccountCode = fk_account_code
								,DepartmentCode = department_code
								,FkFunctionCode = fk_function_code
								,FkProjectCode = fk_project_code
								,FreeDim1 = free_dim_1
								,FreeDim2 = free_dim_2
								,FreeDim3 = free_dim_3
								,FreeDim4 = free_dim_4
								,FkAdjustmentCode = fk_adjustment_code
								,FkAlterCode = fk_alter_code
								,v1_Jan = jan
								,v1_Feb = feb
								,v1_Mar = mar
								,v1_Apr = apr
								,v1_May = may
								,v1_Jun = jun
								,v1_Jul = jul
								,v1_Aug = aug
								,v1_Sep = sep
								,v1_Oct = oct
								,v1_Nov = nov
								,v1_Dec = dec
								,v2_Jan = 0
								,v2_Feb	= 0
								,v2_Mar	= 0
								,v2_Apr	= 0
								,v2_May	= 0
								,v2_Jun	= 0
								,v2_Jul	= 0
								,v2_Aug	= 0
								,v2_Sep	= 0
								,v2_Oct	= 0
								,v2_Nov	= 0
								,v2_Dec	= 0
						FROM  tfp_erp_export_log_agresso WHERE export_id = @v1_export_id
						UNION ALL
						SELECT	description = description
								,FkAccountCode 
								,DepartmentCode
								,FkFunctionCode
								,FkProjectCode 
								,FreeDim1
								,FreeDim2
								,FreeDim3
								,FreeDim4
								,FkAdjustmentCode
								,FkAlterCode
								,v1_Jan = 0
								,v1_Feb	= 0
								,v1_Mar	= 0
								,v1_Apr	= 0
								,v1_May	= 0
								,v1_Jun	= 0
								,v1_Jul	= 0
								,v1_Aug	= 0
								,v1_Sep	= 0
								,v1_Oct	= 0
								,v1_Nov	= 0
								,v1_Dec	= 0
								,v2_Jan = jan
								,v2_Feb = feb
								,v2_Mar = mar
								,v2_Apr = apr
								,v2_May = may
								,v2_Jun = jun
								,v2_Jul = jul
								,v2_Aug = aug
								,v2_Sep = sep
								,v2_Oct = oct
								,v2_Nov = nov
								,v2_Dec = dec
						FROM #erp_temp
				) TRANS
				GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode
				HAVING	SUM(v2_Jan-v1_Jan) <> 0 OR
						SUM(v2_Feb-v1_Feb) <> 0 OR
						SUM(v2_Mar-v1_Mar) <> 0 OR
						SUM(v2_Apr-v1_Apr) <> 0 OR
						SUM(v2_May-v1_May) <> 0 OR
						SUM(v2_Jun-v1_Jun) <> 0 OR
						SUM(v2_Jul-v1_Jul) <> 0 OR
						SUM(v2_Aug-v1_Aug) <> 0 OR
						SUM(v2_Sep-v1_Sep) <> 0 OR
						SUM(v2_Oct-v1_Oct) <> 0 OR
						SUM(v2_Nov-v1_Nov) <> 0 OR
						SUM(v2_Dec-v1_Dec) <> 0
        ) f
		LEFT JOIN tco_user_adjustment_codes adj ON f.FkAdjustmentCode = adj.pk_adj_code AND adj.fk_tenant_id = @tenantId AND adj.budget_year = @budgetYear
		
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0 
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0
        GROUP BY f.description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode,adj.prefix_adjCode)  a
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0 
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0
    END
    ELSE
     BEGIN

     SELECT * FROM
        (SELECT description,FkAccountCode,DepartmentCode,FkFunctionCode,
        CASE WHEN FkProjectCode ='' or FkProjectCode IS NULL THEN isnull(@defProject,'')
        ELSE isnull(FkProjectCode,'')
        END [FkProjectCode],
        CASE WHEN ISNULL(FreeDim1,'')='' or FreeDim1 is null  THEN isnull( @defFreeDim1,'')
        ELSE isnull( FreeDim1,'')
        END [FreeDim1],
        CASE WHEN ISNULL(FreeDim2,'')='' or FreeDim2 is null  THEN isnull( @defFreeDim2,'')
        ELSE isnull(FreeDim2,'')
        END [FreeDim2],
        CASE WHEN ISNULL(FreeDim3,'')='' or FreeDim3 is null  THEN isnull(@defFreeDim3,'')
        ELSE isnull(FreeDim3,'')
        END [FreeDim3],
        CASE WHEN ISNULL(FreeDim4,'')='' or FreeDim4 is null  THEN isnull(@defFreeDim4,'')
        ELSE isnull(FreeDim4,'')
        END [FreeDim4],
        '' FkAdjustmentCode,
        '' FkAlterCode,
        SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec 
		FROM (
               SELECT	description
						,FkAccountCode
						,DepartmentCode
						,FkFunctionCode
						,FkProjectCode
						,FreeDim1
						,FreeDim2
						,FreeDim3
						,FreeDim4
						,FkAdjustmentCode
						,FkAlterCode
						,Jan = SUM(v2_Jan-v1_Jan)
						,Feb = SUM(v2_Feb-v1_Feb)
						,Mar = SUM(v2_Mar-v1_Mar)
						,Apr = SUM(v2_Apr-v1_Apr)
						,May = SUM(v2_May-v1_May)
						,Jun = SUM(v2_Jun-v1_Jun)
						,Jul = SUM(v2_Jul-v1_Jul)
						,Aug = SUM(v2_Aug-v1_Aug)
						,Sep = SUM(v2_Sep-v1_Sep)
						,Oct = SUM(v2_Oct-v1_Oct)
						,Nov = SUM(v2_Nov-v1_Nov)
						,Dec = SUM(v2_Dec-v1_Dec)
				FROM (
						SELECT	description = description
								,FkAccountCode = fk_account_code
								,DepartmentCode = department_code
								,FkFunctionCode = fk_function_code
								,FkProjectCode = fk_project_code
								,FreeDim1 = free_dim_1
								,FreeDim2 = free_dim_2
								,FreeDim3 = free_dim_3
								,FreeDim4 = free_dim_4
								,FkAdjustmentCode = fk_adjustment_code
								,FkAlterCode = fk_alter_code	
								,v1_Jan = jan
								,v1_Feb = feb
								,v1_Mar = mar
								,v1_Apr = apr
								,v1_May = may
								,v1_Jun = jun
								,v1_Jul = jul
								,v1_Aug = aug
								,v1_Sep = sep
								,v1_Oct = oct
								,v1_Nov = nov
								,v1_Dec = dec
								,v2_Jan = 0
								,v2_Feb	= 0
								,v2_Mar	= 0
								,v2_Apr	= 0
								,v2_May	= 0
								,v2_Jun	= 0
								,v2_Jul	= 0
								,v2_Aug	= 0
								,v2_Sep	= 0
								,v2_Oct	= 0
								,v2_Nov	= 0
								,v2_Dec	= 0
						FROM  tfp_erp_export_log_agresso WHERE export_id = @v1_export_id
						UNION ALL
						SELECT	description = description
								,FkAccountCode
								,DepartmentCode
								,FkFunctionCode
								,FkProjectCode
								,FreeDim1
								,FreeDim2
								,FreeDim3
								,FreeDim4
								,FkAdjustmentCode
								,FkAlterCode
								,v1_Jan = 0
								,v1_Feb	= 0
								,v1_Mar	= 0
								,v1_Apr	= 0
								,v1_May	= 0
								,v1_Jun	= 0
								,v1_Jul	= 0
								,v1_Aug	= 0
								,v1_Sep	= 0
								,v1_Oct	= 0
								,v1_Nov	= 0
								,v1_Dec	= 0
								,v2_Jan = jan
								,v2_Feb = feb
								,v2_Mar = mar
								,v2_Apr = apr
								,v2_May = may
								,v2_Jun = jun
								,v2_Jul = jul
								,v2_Aug = aug
								,v2_Sep = sep
								,v2_Oct = oct
								,v2_Nov = nov
								,v2_Dec = dec
						FROM #erp_temp 
				) TRANS
				GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode
				HAVING	SUM(v2_Jan-v1_Jan) <> 0 OR
						SUM(v2_Feb-v1_Feb) <> 0 OR
						SUM(v2_Mar-v1_Mar) <> 0 OR
						SUM(v2_Apr-v1_Apr) <> 0 OR
						SUM(v2_May-v1_May) <> 0 OR
						SUM(v2_Jun-v1_Jun) <> 0 OR
						SUM(v2_Jul-v1_Jul) <> 0 OR
						SUM(v2_Aug-v1_Aug) <> 0 OR
						SUM(v2_Sep-v1_Sep) <> 0 OR
						SUM(v2_Oct-v1_Oct) <> 0 OR
						SUM(v2_Nov-v1_Nov) <> 0 OR
						SUM(v2_Dec-v1_Dec) <> 0
        ) f     

        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0 
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0
        GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4)  a
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0 
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0

    END
END

GO