CREATE OR ALTER PROCEDURE [dbo].[prcUpdateFinplanData]

AS

DECLARE @job_running INT
DECLARE @timestamp datetime2
DECLARE @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @RowsUpdated INT
DECLARE @Update_flag INT



DECLARE @temp_change_budget TABLE(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[resource_id] [nvarchar](25) NOT NULL,
	[type_internal] [int] NOT NULL,
UNIQUE CLUSTERED(fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,fk_tenant_id, budget_year,resource_id,type_internal )
)


CREATE TABLE #TEMP_BUDGET (
	[fk_tenant_id] [int] NOT NULL DEFAULT 0,
	[budget_year] [int] NOT NULL DEFAULT 0,
	[fk_account_code] [nvarchar](25) NOT NULL DEFAULT '',
	[department_code] [nvarchar](25) NOT NULL DEFAULT '',
	[fk_function_code] [nvarchar](25) NOT NULL DEFAULT '',
	[fk_project_code] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_1] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_2] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_3] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_4] [nvarchar](25) NOT NULL DEFAULT '',
	[resource_id] [nvarchar](25) NOT NULL DEFAULT '',
	[description] [nvarchar](255) NOT NULL DEFAULT '',
	[period] [int] NOT NULL DEFAULT 0,
	[allocation_pct] [decimal](18, 10) NOT NULL DEFAULT 0,
	[fk_key_id] [int] NOT NULL DEFAULT 0,
	[fk_investment_id] [int] NOT NULL DEFAULT 0,
	[fk_portfolio_code] [nvarchar](50) NOT NULL DEFAULT '',
	[fk_employment_id] [bigint] NOT NULL DEFAULT 0,
	[fk_adjustment_code] [nvarchar](50) NOT NULL DEFAULT '',
	[fk_alter_code] [nvarchar](25) NOT NULL DEFAULT '',
	[original_budget] [decimal](38, 2) NOT NULL DEFAULT 0,
	[revised_budget] [decimal](18, 2) NOT NULL DEFAULT 0,
	[cost_calc_budget] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_1_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_2_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_3_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_4_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[revised_budget_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
	[accounting_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
	[finplan_changes_year_1] [decimal](18, 2) NOT NULL DEFAULT 0,
	[type_internal] [int] NOT NULL  DEFAULT 0,
	[fk_action_id] [int] NOT NULL DEFAULT 0,
	[fk_main_project_code] [nvarchar](25) NULL DEFAULT ''
) ;

DECLARE @stop_tenant TABLE(fk_tenant_id INT);
DECLARE @stop_actions TABLE(fk_action_id INT);


declare @updated datetime = (
SELECT updated FROM twh_report_job_queue
WHERE job_name = 'TWHUPDATE')


set @updated = (select DATEADD(second, - 15,@updated ))
INSERT INTO twh_temp_triggered_actions (fk_action_id)
SELECT DISTINCT fk_action_id FROM tfp_trans_detail where UPDATED  >= @updated


BEGIN
       -- SET NOCOUNT ON added to prevent extra result sets from
       -- interfering with SELECT statements.
SET NOCOUNT ON;
set noexec off;

SET @job_running = (SELECT MAX(run_flag) FROM [twh_report_job_queue] WHERE job_name IN ('TWHUPDATE', 'FINPLANFULL', 'BUDDOCFULL'))

SET @RowsUpdated = @@rowcount;


IF @job_running = 1
	BEGIN
	PRINT 'Job already running. No update is done.'
	RETURN
	END
ELSE 
	BEGIN
	SELECT @StartDate = sysdatetime();
	select @timestamp = sysdatetime();
	PRINT 'Job starting at ' + convert(nvarchar(19),@timestamp)
	--RAISERROR ('Job starting', 0, 1) WITH NOWAIT
	
	UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'TWHUPDATE';
	END

	BEGIN 


	INSERT INTO @stop_actions (fk_action_id)
	SELECT b.pk_action_id FROM tco_parameters a, tfp_trans_header b
	WHERE a.param_name = 'STOP_WH_UPDATE'
	AND a.param_value = 'TRUE'
	AND a.active = 1
	AND a.fk_tenant_id = b.fk_tenant_id;

	PRINT 'Retreving tenants that is not to be updated'

	END

	BEGIN
	
	DELETE T FROM twh_temp_triggered_actions T INNER JOIN @stop_actions A ON T.fk_action_id = A.fk_action_id;
	
	PRINT 'Delete tenants from trigger tables'

	END
              
            BEGIN TRANSACTION
            INSERT INTO twh_temp_changed_actions (fk_action_id) SELECT distinct b.fk_action_id FROM twh_temp_triggered_actions b WHERE NOT EXISTS (SELECT * FROM twh_temp_changed_actions a WHERE a.fk_action_id = b.fk_action_id) ;
			DELETE T FROM twh_temp_triggered_actions T INNER JOIN twh_temp_changed_actions A ON T.fk_action_id = A.fk_action_id;
            COMMIT


			BEGIN TRANSACTION
			INSERT INTO thq_temp_changed_actions (fk_action_id, fk_tenant_id)
			SELECT DISTINCT a.fk_action_id, h.fk_tenant_id
			FROM twh_temp_changed_actions a
			INNER JOIN tfp_trans_header h ON h.pk_action_id = a.fk_action_id
			INNER JOIN tmd_hq_tenant_definition d ON h.fk_tenant_id = d.tenant_id_child
			COMMIT


	

               BEGIN

			   --PRINT 'UPDATE twh_buddoc_reports FROM finplan'
			   RAISERROR ('START : UPDATE twh_buddoc_reports FROM finplan', 0, 1) WITH NOWAIT

               DELETE FROM [twh_buddoc_reports] WHERE pk_action_id IN (SELECT fk_action_id FROM twh_temp_changed_actions);

            INSERT INTO [twh_buddoc_reports] (pk_id, fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
			fk_alter_code,pk_action_id,fk_account_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
			service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
			service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
			org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
			fk_function_code,budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,[description], updated, updated_by)
			SELECT newid(), th.fk_tenant_id, td.budget_year, td.fk_change_id,fp_level_1_value =
            CASE   WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_1' THEN oh.org_id_1
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_2' THEN oh.org_id_2
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_3' THEN oh.org_id_3
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_4' THEN oh.org_id_4
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_5' THEN oh.org_id_5
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_1' THEN sv.service_id_1           
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_2' THEN sv.service_id_2
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_3' THEN sv.service_id_3
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_4' THEN sv.service_id_4
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_1' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_5' THEN sv.service_id_5           
                    ELSE ''
            END, fp_level_2_value =
            CASE   WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_1' THEN oh.org_id_1
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_2' THEN oh.org_id_2
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_3' THEN oh.org_id_3
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_4' THEN oh.org_id_4
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'org_id_5' THEN oh.org_id_5
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_1' THEN sv.service_id_1           
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_2' THEN sv.service_id_2
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_3' THEN sv.service_id_3
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_4' THEN sv.service_id_4
                        WHEN (SELECT param_value FROM tco_parameters P WHERE P.param_name = 'FINPLAN_LEVEL_2' AND P.fk_tenant_id = th.fk_tenant_id) = 'service_id_5' THEN sv.service_id_5           
                    ELSE '' END,
            th.action_type, td.fk_alter_code, th.pk_action_id, td.fk_account_code,td.project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
            sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
           oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,
			oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,
			oh.org_id_5,oh.org_name_5,
			oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,td.department_code,
			td.function_code as fk_function_code,
            0 AS budget_amount, SUM(td.year_1_amount) AS year_1_amount,SUM(td.year_2_amount) AS year_2_amount,SUM(td.year_3_amount) AS year_3_amount,SUM(td.year_4_amount) AS year_4_amount,
            td.description, getdate(), 1002
            from tfp_trans_header th
             join tfp_trans_detail td on th.fk_tenant_id = td.fk_tenant_id and th.pk_action_id = td .fk_action_id
            left  join tco_service_values sv on th.fk_tenant_id = sv.fk_tenant_id AND td.function_code = sv.fk_function_code
			 JOIN tco_org_version ov ON td.fk_tenant_id = ov.fk_Tenant_id and (td.budget_year)*100+1 between ov.period_from and ov.period_to
			 JOIN tco_org_hierarchy oh on td.fk_tenant_id = oh.fk_Tenant_id and td.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
            --left outer join tco_functions tf on th.fk_tenant_id = tf.pk_tenant_id AND td.function_code = tf.pk_function_code
			WHERE  th.pk_action_id IN (SELECT fk_action_id FROM twh_temp_changed_actions)
            GROUP BY th.fk_tenant_id, td.budget_year,td.fk_change_id,th.action_type, td.fk_alter_code, th.pk_action_id, td.fk_account_code,
            td.project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
            sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
            oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
			oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,td.department_code,
			td.function_code,td.description
			
			SET @RowsUpdated = @@rowcount;

			IF @RowsUpdated > 0
			begin
			set @Update_flag = 1
			end
 
			select @timestamp = sysdatetime();
			PRINT 'FINISH: UPDATE twh_buddoc_reports FROM finplan at ' + convert(nvarchar(19),@timestamp)+'. Rows affected: ' + CONVERT(VARCHAR(20), @RowsUpdated)

            END


            BEGIN
			PRINT 'Truncate temp tables'

			IF @Update_flag = 1
			begin
            TRUNCATE TABLE twh_temp_changed_actions;
			end

			END



			UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name = 'TWHUPDATE';

			select @timestamp = sysdatetime();
			SELECT @EndDate = sysdatetime();
			SELECT @TimeUsed = (select convert(varchar(5),DateDiff(s, @startDate, @EndDate)/3600)+' hrs '+convert(varchar(5),DateDiff(s, @startDate, @EndDate)%3600/60)+' mins '+convert(varchar(5),(DateDiff(s, @startDate, @EndDate)%60))+' seconds')
			PRINT 'Job finished ok at ' + convert(nvarchar(19),@EndDate) + ' using ' + @TimeUsed
 
END
GO

