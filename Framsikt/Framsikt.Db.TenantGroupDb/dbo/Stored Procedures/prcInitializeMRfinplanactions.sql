CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRfinplanactions]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS
	
	DECLARE @budget_year INT
	DECLARE @prev_forecast_period INT
	DECLARE @org_version VARCHAR(25) 
	DECLARE @language VARCHAR(10)
	DECLARE @flag_name varchar(30)
	DECLARE @flag_status INT
	
	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
	SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
	SET @budget_year = @forecast_period/100

	
	SET @prev_forecast_period = (
	SELECT MAX(forecast_period) FROM tmr_finplan_reporting
	WHERE fk_Tenant_id = @fk_tenant_id
	AND forecast_period < @forecast_period
	AND forecast_period > @budget_year * 100
	)

	PRINT 'START: copying finplan actions setup'
	BEGIN

	INSERT INTO tmr_finplan_reporting (fk_action_id,fk_tenant_id,forecast_period,description,fk_adjustment_code,fk_alter_code,monthly_report_flag,updated,updated_by,org_id_2,service_id_2,limit_code)
	SELECT fk_action_id,fk_tenant_id,@forecast_period, description, fk_adjustment_code,fk_alter_code,monthly_report_flag,GETDATE() AS updated,updated_by,org_id_2,service_id_2,limit_code
	FROM tmr_finplan_reporting rp1 WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @prev_forecast_period
	AND NOT EXISTS (SELECT * FROM tmr_finplan_reporting rp2 
	WHERE rp2.fk_tenant_id = rp1.fk_tenant_id AND rp2.forecast_period = @forecast_period
	AND rp2.fk_action_id = rp1.fk_action_id
	AND rp2.fk_adjustment_code = rp1.fk_adjustment_code
	AND rp2.fk_alter_code = rp1.fk_alter_code)

	END

	PRINT 'FINISH: copying finplan actions setup'	

SET @flag_name = 'MR_COPYREPORT_ORGLEVEL'
SET @flag_status = (SELECT flag_status FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id  AND flag_name = @flag_name AND fk_org_version = @org_version)

	DELETE FROM tmr_finplan_actions 
	WHERE fk_tenant_id = @fk_tenant_id  AND forecast_period = @forecast_period AND status = 3


    INSERT INTO tmr_finplan_actions (fk_tenant_id, forecast_period, fk_action_id, real_amount, status, risk, description,
    updated, updated_by, yearly_forecast, org_id, org_level, service_id, is_reported, limit_code, finplan_year_1_amount, descriptiontxt)
    SELECT fk_tenant_id, @forecast_period, fk_action_id, real_amount, status, risk, NEWID() as description,
    updated, updated_by, yearly_forecast, org_id, org_level, service_id, is_reported, limit_code, finplan_year_1_amount, descriptiontxt  
	FROM tmr_finplan_actions rp1
	WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @prev_forecast_period AND status = 3

	

if (@flag_status <> 0 AND @flag_status IS NOT NULL)
BEGIN
    DELETE FROM tmr_finplan_actions where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period and org_level >= @flag_status AND status != 3

    INSERT INTO tmr_finplan_actions (fk_tenant_id, forecast_period, fk_action_id, real_amount, status, risk, description,
    updated, updated_by, yearly_forecast, org_id, org_level, service_id, is_reported, limit_code, finplan_year_1_amount, descriptiontxt)
    SELECT fk_tenant_id, @forecast_period, fk_action_id, real_amount, status, risk, NEWID() as description,
    updated, updated_by, yearly_forecast, org_id, org_level, service_id, is_reported, limit_code, finplan_year_1_amount, descriptiontxt  
	FROM tmr_finplan_actions rp1
    WHERE fk_tenant_id = @fk_tenant_id AND org_level >= @flag_status AND forecast_period = @prev_forecast_period AND status != 3
	
END


PRINT 'FINISH: copying finplan actions reporting from prev period'


PRINT 'START: Fetch action data from SAM'

--fetch data from SAM
DROP TABLE IF EXISTS #temp_sam_to_mr

select a.fk_tenant_id,a.forecast_period,a.fk_action_id,SUM(a.real_amount)real_amount,a.status,a.risk, updated = getdate(), updated_by = @user_id,SUM(a.yearly_forecast)yearly_forecast
, org_id = b.org_id_2, org_level = 2, service_id = c.service_id_2
, is_reported = 1, d.limit_code,SUM(a.fp_amt)finplan_year_1_amount
, descriptiontxt = a.description_department
INTO #temp_sam_to_mr
from tsam_period_transactions a
JOIN tco_org_hierarchy b on a.fk_tenant_id = b.fk_tenant_id and a.department_code = b.fk_department_code and b.fk_org_version = @org_version
LEFT JOIN tco_service_values c on a.fk_tenant_id = c.fk_tenant_id and a.fk_function_code = c.fk_function_code
JOIN tco_fp_alter_codes d on a.fk_tenant_id = d.fk_tenant_id and a.fk_alter_code = d.pk_alter_code
where a.fk_tenant_id = @fk_tenant_id
and forecast_period = @forecast_period
GROUP BY a.fk_tenant_id,a.forecast_period,a.fk_action_id,a.status,a.risk, b.org_id_2,c.service_id_2, d.limit_code,a.description_department

--Update for existing records
update b set real_amount = a.real_amount, status = a.status, risk = a.risk, descriptiontxt = a.descriptiontxt, yearly_forecast = a.yearly_forecast
from #temp_sam_to_mr a
LEFT JOIN tmr_finplan_actions b ON
		a.fk_tenant_id		= b.fk_tenant_id
	AND a.forecast_period	= b.forecast_period
	AND a.fk_action_id 		= b.fk_action_id
	AND a.org_id			= b.org_id
	AND a.org_level			= b.org_level
	AND a.service_id 		= b.service_id
	AND a.limit_code		= b.limit_code
WHERE b.pk_id IS NOT NULL

--INSERT FOR ROWS THAT DOES NOT EXIST
INSERT INTO [dbo].[tmr_finplan_actions]
           ([fk_tenant_id]
           ,[forecast_period]
           ,[fk_action_id]
           ,[real_amount]
           ,[status]
           ,[risk]
           ,[description]
           ,[updated]
           ,[updated_by]
           ,[yearly_forecast]
           ,[org_id]
           ,[org_level]
           ,[service_id]
           ,[is_reported]
           ,[limit_code]
           ,[finplan_year_1_amount]
           ,[descriptiontxt])

select		a.[fk_tenant_id]
           ,a.[forecast_period]
           ,a.[fk_action_id]
           ,a.[real_amount]
           ,a.[status]
           ,a.[risk]
           ,[description] = newid()
           ,a.[updated]
           ,a.[updated_by]
           ,a.[yearly_forecast]
           ,a.[org_id]
           ,a.[org_level]
           ,a.[service_id]
           ,a.[is_reported]
           ,a.[limit_code]
           ,a.[finplan_year_1_amount]
           ,a.[descriptiontxt]
from #temp_sam_to_mr a
LEFT JOIN tmr_finplan_actions b ON
		a.fk_tenant_id		= b.fk_tenant_id
	AND a.forecast_period	= b.forecast_period
	AND a.fk_action_id 		= b.fk_action_id
	AND a.org_id			= b.org_id
	AND a.org_level			= b.org_level
	AND a.service_id 		= b.service_id
	AND a.limit_code		= b.limit_code
WHERE b.pk_id IS NULL;

PRINT 'FINISH: Fetch action data from SAM';

PRINT 'START: Update year1 to year4 amounts from trans_detail';

WITH dataset AS (SELECT th.pk_action_id                                         AS actionId,
                        tfr2.org_id_2                                           AS orgId,
                        ISNULL(srg1.service_id_2, '')                           AS serviceId,
                        ISNULL(g3.limit_code, '')                               AS limitCode,
                        tfr2.forecast_period                                    AS forecastPeriod,
                        SUM(ISNULL(g.year_1_amount, 0))                         AS year1amount,
                        SUM(ISNULL(g.year_2_amount, 0))                         AS year2amount,
                        SUM(ISNULL(g.year_3_amount, 0))                         AS year3amount,
                        SUM(ISNULL(g.year_4_amount, 0))                         AS year4amount
                 FROM dbo.tfp_trans_header th
                          INNER JOIN
                      dbo.tfp_trans_detail g ON th.fk_tenant_id = g.fk_tenant_id AND th.pk_action_id = g.fk_action_id
                          LEFT JOIN
                      dbo.tco_fp_alter_codes g3
                      ON g.fk_alter_code = g3.pk_alter_code AND g.fk_tenant_id = g3.fk_tenant_id
                          LEFT JOIN
                      dbo.tco_service_values srg1
                      ON g.fk_tenant_id = srg1.fk_tenant_id AND g.function_code = srg1.fk_function_code
                          LEFT JOIN
                      dbo.tmr_finplan_reporting tfr2
                      ON th.fk_tenant_id = tfr2.fk_tenant_id AND th.pk_action_id = tfr2.fk_action_id
                          AND g.fk_adjustment_code = tfr2.fk_adjustment_code AND g.fk_alter_code = tfr2.fk_alter_code
                          AND ISNULL(g3.limit_code, '') = ISNULL(tfr2.limit_code, '')
                 WHERE th.fk_tenant_id = @fk_tenant_id
                   AND g.budget_year = @budget_year
                   AND tfr2.forecast_period = @forecast_period
                 GROUP BY tfr2.forecast_period,
                          th.pk_action_id,
                          srg1.service_id_2,
                          tfr2.org_id_2,
                          g3.limit_code)
UPDATE tfa
SET tfa.finplan_year_1_amount = dataset.year1amount,
    tfa.finplan_year_2_amount = dataset.year2amount,
    tfa.finplan_year_3_amount = dataset.year3amount,
    tfa.finplan_year_4_amount = dataset.year4amount
FROM dbo.tmr_finplan_actions tfa
         INNER JOIN dataset ON tfa.fk_tenant_id = @fk_tenant_id
    AND tfa.forecast_period = @forecast_period
    AND tfa.forecast_period = dataset.forecastPeriod
    AND tfa.fk_action_id = dataset.actionId
    AND ISNULL(tfa.limit_code, '') = ISNULL(dataset.limitCode, '')
    AND tfa.org_id = dataset.orgId
    AND tfa.service_id = dataset.serviceId

PRINT 'FINISH: Update year1 to year4 amounts from trans_detail'

RETURN 0
