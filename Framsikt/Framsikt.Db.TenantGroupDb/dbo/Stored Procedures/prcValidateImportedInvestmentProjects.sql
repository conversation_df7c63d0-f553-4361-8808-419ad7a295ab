CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedInvestmentProjects]  
 @tenant_id int,  
 @user_id int,  
 @budget_year int,  
 @change_id int,  
 @is_super_user bit,  
 @adj_code_count int = 0 output  
AS  
  
 declare @forecast_period INT  
 DECLARE @Include_20_year VARCHAR(25)  
 DECLARE @org_version VARCHAR(25)   
 DECLARE @finplan_investment_level VARCHAR(25)   
  
 set @forecast_period = (@budget_year * 100) + 1;  
 SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @tenant_id)  
  
 SET @finplan_investment_level =  (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id and param_name = 'FINPLAN_INVESTMENT_LEVEL')  
  
  
 SET @Include_20_year =  (SELECT param_value FROM vw_tco_parameters WHERE param_name='Enable_INV_20_Year_column' AND fk_tenant_id = @tenant_id)  
  
 --Clear all the error information--  
 UPDATE tfp_stage_investment_project_import  
 SET   
 [account_code_error] = 0,  
 [department_code_error] = 0,  
 [function_code_error] = 0,  
 [project_code_error] = 0,  
 [free_dim_1_error] = 0,  
 [free_dim_2_error] = 0,  
 [free_dim_3_error] = 0,  
 [free_dim_4_error] = 0,  
 [approved_cost_error]  = 0, 
 [cost_estimate_error]  = 0,
 [adjustment_code_error]  = 0,  
 [alter_code_error]  = 0,  
 [Year1_error] = 0,  
 [Year2_error] = 0,  
 [Year3_error] = 0,  
 [Year4_error] = 0,  
 [Year5_error] = 0,  
 [Year6_error] = 0,  
 [Year7_error] = 0,  
 [Year8_error] = 0,  
 [Year9_error] = 0,  
 [Year10_error] = 0,  
 [Year11_error] = 0,  
 [Year12_error] = 0,  
 [Year13_error] = 0,  
 [Year14_error] = 0,  
 [Year15_error] = 0,  
 [Year16_error] = 0,  
 [Year17_error] = 0,  
 [Year18_error] = 0,  
 [Year19_error] = 0,  
 [Year20_error] = 0,  
 [PreviousYear1_error] = 0,  
 [PreviousYear2_error] = 0,  
 [PreviousYear3_error] = 0,  
 [PreviousYear4_error] = 0,  
 [PreviousYear5_error] = 0,  
 [error_count] = 0,  
 inv_description = SUBSTRING(inv_description, 1, 500),  
 [vat_rate_per_error]=0,  
 [vat_refund_per_error]=0  
 WHERE tfp_stage_investment_project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_project_import.budget_year = @budget_year AND   
  tfp_stage_investment_project_import.user_id = @user_id AND  
  tfp_stage_investment_project_import.change_id = @change_id;  
  
 -- Set alter code if it does not exist  
  
 UPDATE tfp_stage_investment_Project_import   
 SET alter_code = b.pk_alter_code  
 FROM tfp_stage_investment_Project_import imp, tco_fp_alter_codes b  
 WHERE imp.tenant_id = b.fk_tenant_id  
 AND imp.alter_code = ''  
 AND imp.budget_year = @budget_year   
 AND imp.user_id = @user_id   
	AND	imp.change_id = @change_id
 AND b.default_action_type = 1000;  
  
  
 UPDATE tfp_stage_investment_Project_import  
 SET alter_code = '41'  
 WHERE alter_code = ''  
 AND budget_year = @budget_year   
 AND user_id = @user_id   
	AND	change_id = @change_id;
  
  
  
 --Validate account codes--  
 UPDATE tfp_stage_investment_Project_import  
 SET account_code_error = 1, error_count = error_count + 1   
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_accounts acc on   
  tfp_stage_investment_Project_import.tenant_id = acc.pk_tenant_id AND   
  tfp_stage_investment_Project_import.account_code = acc.pk_account_code AND    
  tfp_stage_investment_Project_import.budget_year BETWEEN datepart (year, acc.dateFrom) AND datepart (year, acc.dateTo) AND  
  acc.isActive = 1 AND  
  tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year AND   
  tfp_stage_investment_Project_import.user_id = @user_id AND  
  tfp_stage_investment_Project_import.change_id = @change_id  
 LEFT JOIN gco_kostra_accounts ka ON acc.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'investment'   
 WHERE ka.pk_kostra_account_code IS NULL;  
  
 --Validate department codes --  
 UPDATE tfp_stage_investment_Project_import  
 SET department_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_departments DEPTS ON   
  tfp_stage_investment_Project_import.tenant_id = DEPTS.fk_tenant_id AND  
  tfp_stage_investment_Project_import.department_code = DEPTS.pk_department_code AND  
  tfp_stage_investment_Project_import.budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND  
  DEPTS.[status] = 1 AND  
  @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to  
 WHERE DEPTS.pk_department_code IS NULL AND   
  tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year AND   
  tfp_stage_investment_Project_import.user_id = @user_id AND  
  tfp_stage_investment_Project_import.change_id = @change_id;  
  
 --Validate Functions--  
 UPDATE tfp_stage_investment_Project_import  
 SET function_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_functions fn on   
  tfp_stage_investment_Project_import.tenant_id = fn.pk_tenant_id AND  
  tfp_stage_investment_Project_import.function_code = fn.pk_Function_code AND     
  tfp_stage_investment_Project_import.budget_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND  
  fn.isActive = 1  
 WHERE fn.pk_Function_code IS NULL AND   
  tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year AND   
  tfp_stage_investment_Project_import.user_id = @user_id AND  
  tfp_stage_investment_Project_import.change_id = @change_id ;  
  
  --Validate Projects--  
  
  UPDATE tfp_stage_investment_Project_import  
  SET project_code_error = 1, error_count = error_count + 1  
  FROM tfp_stage_investment_Project_import   
  LEFT JOIN tco_projects prj on   
   tfp_stage_investment_Project_import.tenant_id = prj.fk_tenant_id  AND  
   tfp_stage_investment_Project_import.project_code = prj.pk_project_code AND      
   tfp_stage_investment_Project_import.budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND  
   prj.active = 1  
  WHERE prj.pk_project_code IS NULL  
  AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
  tfp_stage_investment_Project_import.user_id = @user_id  
  --AND tfp_stage_investment_Project_import.project_code != ''   
  --AND tfp_stage_investment_Project_import.project_code IS NOT NULL  ;  
  
  
 -- Validate free dim 1 --  
  
 UPDATE tfp_stage_investment_Project_import  
 SET free_dim_1_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_investment_Project_import.free_dim_1 AND   
  tfp_stage_investment_Project_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'  
  AND frdm.status = 1  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
  tfp_stage_investment_Project_import.user_id = @user_id  
  AND tfp_stage_investment_Project_import.free_dim_1 != ''   
  AND tfp_stage_investment_Project_import.free_dim_1 IS NOT NULL;    
  
-- Validate free dim 2 --  
  
 UPDATE tfp_stage_investment_Project_import  
 SET free_dim_2_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_investment_Project_import.free_dim_2 AND   
  tfp_stage_investment_Project_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'  
  AND frdm.status = 1  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
  tfp_stage_investment_Project_import.user_id = @user_id  
  AND tfp_stage_investment_Project_import.free_dim_2 != ''   
  AND tfp_stage_investment_Project_import.free_dim_2 IS NOT NULL;    
  
 -- Validate free dim 3 --  
  
 UPDATE tfp_stage_investment_Project_import  
 SET free_dim_3_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_investment_Project_import.free_dim_3 AND   
  tfp_stage_investment_Project_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'  
  AND frdm.status = 1  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
  tfp_stage_investment_Project_import.user_id = @user_id  
  AND tfp_stage_investment_Project_import.free_dim_3 != ''   
  AND tfp_stage_investment_Project_import.free_dim_3 IS NOT NULL;    
  
 -- Validate free dim 4 --  
  
 UPDATE tfp_stage_investment_Project_import  
 SET free_dim_4_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tfp_stage_investment_Project_import.free_dim_4 AND   
  tfp_stage_investment_Project_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'  
  AND frdm.status = 1  
 WHERE frdm.free_dim_column IS NULL  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
  tfp_stage_investment_Project_import.user_id = @user_id  
  AND tfp_stage_investment_Project_import.free_dim_4 != ''   
  AND tfp_stage_investment_Project_import.free_dim_4 IS NOT NULL;   
  
 -- Validate adjustment code --  
  
 SET @adj_code_count = (select count(pk_adjustment_code) from tco_adjustment_codes where fk_tenant_id=@tenant_id and status =1 )  
  if(@adj_code_count > 0)  
  begin  
      UPDATE tfp_stage_investment_Project_import  
  SET adjustment_code_error = 1, error_count = error_count + 1  
  FROM tfp_stage_investment_Project_import   
  LEFT JOIN tco_adjustment_codes adj on adj.pk_adjustment_code = tfp_stage_investment_Project_import.adjustment_code AND   
   tfp_stage_investment_Project_import.tenant_id = adj.fk_tenant_id   
   AND adj.status = 1  
  WHERE adj.pk_adjustment_code IS NULL  
  AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
   tfp_stage_investment_Project_import.user_id = @user_id   
   AND tfp_stage_investment_Project_import.adjustment_code != ''   
  AND tfp_stage_investment_Project_import.adjustment_code IS NOT NULL;    
   End  
  
    
  
 -- Validate alter code --  
  
 UPDATE tfp_stage_investment_Project_import  
 SET alter_code_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import   
 LEFT JOIN tco_fp_alter_codes alt on alt.pk_alter_code = tfp_stage_investment_Project_import.alter_code AND   
  tfp_stage_investment_Project_import.tenant_id = alt.fk_tenant_id AND alt.status = 1  
 WHERE alt.pk_alter_code IS NULL  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
  tfp_stage_investment_Project_import.budget_year = @budget_year  AND  
  tfp_stage_investment_Project_import.user_id = @user_id;  
  
 --check estimate cost is a number  
  UPDATE tfp_stage_investment_Project_import SET cost_estimate_error = 1, error_count = error_count +1  
 WHERE ISNUMERIC(cost_estimate)=0  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id AND cost_estimate IS NOT NULL AND cost_estimate!='';  
 
 --check approve cost is a number  
  UPDATE tfp_stage_investment_Project_import SET approved_cost_error = 1, error_count = error_count +1  
 WHERE ISNUMERIC(approved_cost)=0  
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id AND approved_cost IS NOT NULL AND approved_cost!='';  
 -- Check that not all amounts is 0 --  
  
 if(@Include_20_year ='' or LOWER(@Include_20_year) ='false')  
 Begin  
 UPDATE tfp_stage_investment_Project_import SET Year1_error = 1, error_count = error_count +1  
 WHERE Year1 = 0  
 AND year2 = 0   
 AND Year3 = 0   
 AND year4 = 0  
 AND Year5 = 0    
 AND year6 = 0   
 AND Year7 = 0   
 AND Year8 = 0  
 AND Year9 = 0  
 AND Year10 = 0   
 AND PreviousYear1 = 0    
 AND PreviousYear2 = 0   
 AND PreviousYear3 = 0  
 AND PreviousYear4 = 0  
 AND PreviousYear5 = 0    
 AND approved_cost=0  
 AND cost_estimate=0   
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;  
 End  
 Else  
  Begin   
   UPDATE tfp_stage_investment_Project_import SET Year1_error = 1, error_count = error_count +1  
 WHERE Year1 = 0  
 AND year2 = 0   
 AND Year3 = 0   
 AND year4 = 0  
 AND Year5 = 0    
 AND year6 = 0   
 AND Year7 = 0   
 AND Year8 = 0  
 AND Year9 = 0  
 AND Year10 = 0   
 AND Year11 = 0  
 AND year12 = 0   
 AND Year13 = 0   
 AND year14 = 0  
 AND Year15 = 0    
 AND year16 = 0   
 AND Year17 = 0   
 AND Year18 = 0  
 AND Year19 = 0  
 AND Year20 = 0   
 AND PreviousYear1 = 0   
 AND PreviousYear2 = 0   
 AND PreviousYear3 = 0  
 AND PreviousYear4 = 0  
 AND PreviousYear5 = 0    
 AND approved_cost=0    
 AND cost_estimate=0
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;     
   End  
  
  
 -- Update vat_refund_per & vat_rate_per if the excel sheets passes percentage values multipled by 100  
  
 UPDATE tfp_stage_investment_Project_import    
 SET tfp_stage_investment_Project_import.vat_refund_per =  CONVERT (decimal(18,2) , (CONVERT(decimal(18,2), tfp_stage_investment_Project_import.vat_refund_per) / CONVERT(decimal(18,2), 100)))  
 WHERE tfp_stage_investment_Project_import.tenant_id = @tenant_id AND  tfp_stage_investment_Project_import.budget_year = @budget_year  AND tfp_stage_investment_Project_import.user_id = @user_id AND  tfp_stage_investment_Project_import.vat_refund_per > 100 ;   
      
 UPDATE tfp_stage_investment_Project_import  
 SET tfp_stage_investment_Project_import.vat_rate_per =  CONVERT (decimal(18,2) , (CONVERT(decimal(18,2), tfp_stage_investment_Project_import.vat_rate_per) / CONVERT(decimal(18,2), 100)))  
 WHERE tfp_stage_investment_Project_import.tenant_id = @tenant_id AND  tfp_stage_investment_Project_import.budget_year = @budget_year  AND tfp_stage_investment_Project_import.user_id = @user_id AND  tfp_stage_investment_Project_import.vat_rate_per > 100 ;
      
   -- Validate vat_refund_per  
  
 UPDATE tfp_stage_investment_Project_import  
 SET vat_refund_per_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import imp   
 WHERE imp.vat_refund_per>100   
 AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND  
  imp.user_id = @user_id ;  
    
  
   -- Validate vat_rate_per_error  
  
 UPDATE tfp_stage_investment_Project_import  
 SET vat_rate_per_error = 1, error_count = error_count + 1  
 FROM tfp_stage_investment_Project_import imp   
 WHERE imp.vat_rate_per>25   
 AND imp.tenant_id = @tenant_id AND   
  imp.budget_year = @budget_year  AND  
  imp.user_id = @user_id ;  
  
--validate prevous years  
DECLARE @prevYear1Count INT=0  
DECLARE @prevYear2Count INT=0  
DECLARE @prevYear3Count INT=0  
DECLARE @prevYear4Count INT=0  
DECLARE @prevYear5Count INT=0  
DECLARE @rebudgetApproved BIT=0  
  
select @prevYear5Count=COUNT(a.amount) from tfp_proj_transactions a  
JOIN tfp_budget_changes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_change_id = b.pk_change_id  
where a.fk_tenant_id = @tenant_id  
and b.budget_year = @budget_year-1 and a.amount!=0  
  
select @rebudgetApproved=rebudget_approved from tfp_budget_changes where pk_change_id=@change_id and fk_tenant_id=@tenant_id  
  
IF(@prevYear5Count>0 and @rebudgetApproved=0)  
BEGIN  
UPDATE tfp_stage_investment_Project_import SET PreviousYear5_error = 1, error_count = error_count +1  
 WHERE   
  PreviousYear5 != 0    
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;  
  
END  
  
select @prevYear4Count=COUNT(a.amount) from tfp_proj_transactions a  
JOIN tfp_budget_changes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_change_id = b.pk_change_id  
where a.fk_tenant_id = @tenant_id  
and b.budget_year = @budget_year-2 and a.amount!=0  
  
IF(@prevYear4Count>0 and @is_super_user=0)  
BEGIN  
UPDATE tfp_stage_investment_Project_import SET PreviousYear4_error = 1, error_count = error_count +1  
 WHERE   
  PreviousYear4 != 0    
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;  
END  
  
select @prevYear3Count=COUNT(a.amount) from tfp_proj_transactions a  
JOIN tfp_budget_changes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_change_id = b.pk_change_id  
where a.fk_tenant_id = @tenant_id  
and b.budget_year = @budget_year-3 and a.amount!=0  
  
IF(@prevYear3Count>0 and @is_super_user=0)  
BEGIN  
UPDATE tfp_stage_investment_Project_import SET PreviousYear3_error = 1, error_count = error_count +1  
 WHERE   
  PreviousYear3 != 0    
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;  
END  
  
select @prevYear2Count=COUNT(a.amount) from tfp_proj_transactions a  
JOIN tfp_budget_changes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_change_id = b.pk_change_id  
where a.fk_tenant_id = @tenant_id  
and b.budget_year = @budget_year-4 and a.amount!=0  
IF(@prevYear2Count>0 and @is_super_user=0)  
BEGIN  
UPDATE tfp_stage_investment_Project_import SET PreviousYear2_error = 1, error_count = error_count +1  
 WHERE   
  PreviousYear2 != 0    
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;  
END  
  
select @prevYear1Count=COUNT(a.amount) from tfp_proj_transactions a  
JOIN tfp_budget_changes b on a.fk_tenant_id = b.fk_tenant_id and a.fk_change_id = b.pk_change_id  
where a.fk_tenant_id = @tenant_id  
and b.budget_year = @budget_year-5 and a.amount!=0  
IF(@prevYear1Count>0 and @is_super_user=0)  
BEGIN  
UPDATE tfp_stage_investment_Project_import SET PreviousYear1_error = 1, error_count = error_count +1  
 WHERE   
  PreviousYear1 != 0    
 AND tfp_stage_investment_Project_import.tenant_id = @tenant_id AND   
   tfp_stage_investment_Project_import.budget_year = @budget_year AND   
   tfp_stage_investment_Project_import.user_id = @user_id AND  
   tfp_stage_investment_Project_import.change_id = @change_id;  
END  
RETURN 0  
GO


