CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTbuEmploymentForecast]
     (
		@udtTbuEmpForecast udtTbuEmploymentsForecast readonly
     )
AS
BEGIN
	insert into tbu_employments_forecast 
	select [pk_employment_id] , [forecast_period] , [fk_tenant_id] , [budget_year] , [fk_res_id], [res_name], [fk_emp_type_id], [fk_position_id], [hjemmel_id], [pension_type] , 
	[fk_account_code] ,[fk_department_code] , [fk_function_code] ,[fk_project_code] ,free_dim_1 ,
	free_dim_2 ,free_dim_3 ,free_dim_4 ,[amount_existing_salary] ,[amount_salary_month] ,[amount_salary_year] ,[start_period] , [end_period] , [position_pct] , [seniority_date] , 
	[yearly_budget] , 
    [amount_pension] , [amount_holiday] , [amount_aga_salary],[amount_aga_pension] , [amount_aga_holiday], external_reference , [updated] , [updated_by] , [fk_holiday_type_id] , comments, [fk_add_on_comments],
    [change_flag], [forecast_change_flag]
	from @udtTbuEmpForecast
End