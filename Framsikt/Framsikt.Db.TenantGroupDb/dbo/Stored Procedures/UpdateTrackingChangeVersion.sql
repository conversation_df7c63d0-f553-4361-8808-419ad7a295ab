CREATE OR ALTER PROCEDURE [dbo].[UpdateTrackingChangeVersion]
    @CurrentTrackingVersion BIGINT, 
    @TrackerName VARCHAR(50)
AS

BEGIN
    IF EXISTS (SELECT * FROM [dbo].[gco_dw_load_version_tracking] WITH (updlock,serializable) WHERE [tracker_name] = @TrackerName)
    BEGIN
        UPDATE [dbo].[gco_dw_load_version_tracking]
        SET [sys_change_version] = @CurrentTrackingVersion
        WHERE [tracker_name] = @TrackerName
    END
    ELSE
    BEGIN
        INSERT INTO [dbo].[gco_dw_load_version_tracking] ([tracker_name], [sys_change_version])
        VALUES (@TrackerName, @CurrentTrackingVersion) 
    END
END  