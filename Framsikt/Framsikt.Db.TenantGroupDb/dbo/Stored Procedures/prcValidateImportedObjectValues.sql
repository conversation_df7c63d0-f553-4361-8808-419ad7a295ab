CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedObjectValues]
	@tenant_id int,
	@user_id int,
	@jobId bigint

AS
		--Clear all the error information--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_attribute_values
			SET 
			attribute_id_error = 0,
			attribute_name_error = 0 ,
			year_from_error = 0,
			year_to_error = 0,
			status_error = 0,
			error_count = 0
			WHERE tco_stage_attribute_values.fk_tenant_id = @tenant_id AND 
				tco_stage_attribute_values.user_id = @user_id;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_attribute_values
			SET 
			attribute_id_error = 0,
			attribute_name_error = 0 ,
			year_from_error = 0,
			year_to_error = 0,
			status_error = 0,
			error_count = 0
			WHERE tco_stage_attribute_values.fk_tenant_id = @tenant_id AND 
				tco_stage_attribute_values.job_id = @jobId;
		END


	UPDATE tco_stage_attribute_values
	SET 
	attribute_id = ''
	WHERE tco_stage_attribute_values.fk_tenant_id = @tenant_id AND tco_stage_attribute_values.attribute_id IS NULL;

	UPDATE tco_stage_attribute_values
	SET 
	attribute_name = ''
	WHERE tco_stage_attribute_values.fk_tenant_id = @tenant_id AND tco_stage_attribute_values.attribute_name IS NULL;

	UPDATE tco_stage_attribute_values
	SET 
	year_from = 0
	WHERE tco_stage_attribute_values.fk_tenant_id = @tenant_id AND tco_stage_attribute_values.year_from IS NULL;

	UPDATE tco_stage_attribute_values
	SET 
	year_to = 0
	WHERE tco_stage_attribute_values.fk_tenant_id = @tenant_id AND tco_stage_attribute_values.year_to IS NULL;

	--Validate attribute name --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_attribute_values
			SET attribute_name_error = 1, error_count = error_count + 1
			FROM tco_stage_attribute_values 
			WHERE fk_tenant_id = @tenant_id
			AND user_id = @user_id  
			AND attribute_name = '';
		END
	ELSE
		BEGIN
			UPDATE tco_stage_attribute_values
			SET attribute_name_error = 1, error_count = error_count + 1
			FROM tco_stage_attribute_values 
			WHERE fk_tenant_id = @tenant_id
			AND job_id = @jobId  
			AND attribute_name = '';
		END

	--Validate year from --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_attribute_values
			SET year_from_error = 1, error_count = error_count + 1
			FROM tco_stage_attribute_values 
			WHERE fk_tenant_id = @tenant_id
			AND user_id = @user_id  
			AND (year_from = 0 OR LEN(CAST(year_from AS CHAR)) != 4 OR year_from > year_to);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_attribute_values
			SET year_from_error = 1, error_count = error_count + 1
			FROM tco_stage_attribute_values 
			WHERE fk_tenant_id = @tenant_id
			AND job_id = @jobId  
			AND (year_from = 0 OR LEN(CAST(year_from AS CHAR)) != 4 OR year_from > year_to);
		END

	--Validate year to --
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_attribute_values
			SET year_to_error = 1, error_count = error_count + 1
			FROM tco_stage_attribute_values 
			WHERE fk_tenant_id = @tenant_id
			AND user_id = @user_id  
			AND (year_to = 0 OR LEN(CAST(year_to AS CHAR)) != 4 OR year_to < year_from);
		END
	ELSE
		BEGIN
			UPDATE tco_stage_attribute_values
			SET year_to_error = 1, error_count = error_count + 1
			FROM tco_stage_attribute_values 
			WHERE fk_tenant_id = @tenant_id
			AND job_id = @jobId  
			AND (year_to = 0 OR LEN(CAST(year_to AS CHAR)) != 4 OR year_to < year_from);
		END

	--Validate attribute id --
	IF( @jobID = -1 )
		BEGIN
			UPDATE a
			SET a.attribute_id_error = 1, a.error_count = a.error_count + 1
			FROM tco_stage_attribute_values a
			LEFT JOIN tco_stage_attribute_values b ON a.attribute_id = b.attribute_id
            AND a.pk_id != b.pk_id
            AND a.fk_tenant_id = b.fk_tenant_id
            AND a.user_id = b.user_id
            AND b.year_from_error = 0
            AND b.year_to_error = 0
            AND ((a.year_from BETWEEN b.year_from AND b.year_to)
            OR (a.year_to BETWEEN b.year_from AND b.year_to))
			WHERE a.fk_tenant_id = @tenant_id
			AND a.user_id = @jobId
			AND (a.year_to = 0 OR a.year_from = 0
			OR (a.attribute_id = ''
			OR b.attribute_id IS NOT NULL))
		END
	ELSE
		BEGIN
			UPDATE a 
			SET a.attribute_id_error = 1, a.error_count = a.error_count + 1
			FROM tco_stage_attribute_values a
			INNER JOIN tco_stage_attribute_values b ON a.attribute_id = b.attribute_id
            AND a.pk_id != b.pk_id
            AND a.fk_tenant_id = b.fk_tenant_id
            AND a.job_id = b.job_id
            AND b.year_from_error = 0 
            AND b.year_to_error = 0
            AND (
			(a.year_from BETWEEN b.year_from AND b.year_to)
			OR (a.year_to BETWEEN b.year_from AND b.year_to ))
			WHERE a.fk_tenant_id = @tenant_id
			AND a.job_id = @jobId 
			AND (
			a.year_to = 0 
			OR a.year_from = 0
			OR a.attribute_id = ''
			OR b.attribute_id IS NOT NULL)
		END

	--Validate status --
	IF( @jobID = -1 )
		BEGIN
			UPDATE a 
			SET a.status_error = 1,a.error_count = error_count + 1
			FROM tco_stage_attribute_values a
			WHERE fk_tenant_id = @tenant_id
			AND user_id = @user_id  
			AND [status] IS NOT NULL
			AND [status] > 1
			
		END
	ELSE
		BEGIN
			UPDATE a 
			SET a.status_error = 1,a.error_count = error_count + 1
			FROM tco_stage_attribute_values a
			WHERE fk_tenant_id = @tenant_id
			AND job_id = @jobId 
			AND [status] IS NOT NULL
			AND [status] > 1
		END

RETURN 0