
CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedAssignmentData]
	@tenant_id int,
	@user_id int,
	@jobId bigint,
	@budgetYear int

AS

	--Clear all the error information--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_assignment_import
			SET 
			assignment_name_error = 0,
			category_error = 0 ,
			case_external_reference_error = 0,
			start_year_bp_error = 0,
			end_year_error = 0,
			start_date_error = 0,
			end_date_error = 0,
			status_error = 0,
			url_prefix_error = 0,
			url_error = 0,
			url_title_error = 0,
			org_id_error = 0,
			org_level_error = 0,
			service_id_error = 0,
			assignment_owner_error = 0,
			responsible_user_error = 0,
			error_count = 0
			WHERE tco_stage_assignment_import.fk_tenant_id = @tenant_id AND 
				tco_stage_assignment_import.user_id = @user_id;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_assignment_import
			SET 
			assignment_name_error = 0,
			category_error = 0 ,
			case_external_reference_error = 0,
			start_year_bp_error = 0,
			end_year_error = 0,
			start_date_error = 0,
			end_date_error = 0,
			status_error = 0,
			url_prefix_error = 0,
			url_error = 0,
			url_title_error = 0,
			org_id_error = 0,
			org_level_error = 0,
			service_id_error = 0,
			assignment_owner_error = 0,
			responsible_user_error = 0,
			error_count = 0
			WHERE tco_stage_assignment_import.fk_tenant_id = @tenant_id AND 
				tco_stage_assignment_import.job_id = @jobId;
		END

	-- set defaul start date
	UPDATE tco_stage_assignment_import
	SET 
	start_date = CONCAT(SUBSTRING(CONVERT(varchar, FORMAT(GETDATE(), 'd', 'no')), 0, 7), CONVERT(varchar, @budgetYear))
	WHERE tco_stage_assignment_import.fk_tenant_id = @tenant_id AND (tco_stage_assignment_import.start_date IS NULL OR tco_stage_assignment_import.start_date = '');

	-- set defaul end date
	UPDATE tco_stage_assignment_import
	SET 
	end_date = CONCAT('31.12.', CONVERT(varchar, @budgetYear))
	WHERE tco_stage_assignment_import.fk_tenant_id = @tenant_id AND (tco_stage_assignment_import.end_date IS NULL OR tco_stage_assignment_import.end_date = '');

	-- set default protocol
	UPDATE tco_stage_assignment_import
	SET
	url_prefix = 'https://'
	WHERE tco_stage_assignment_import.fk_tenant_id = @tenant_id AND (tco_stage_assignment_import.url_prefix IS NULL OR tco_stage_assignment_import.url_prefix = '');

	-- set default status
	UPDATE tco_stage_assignment_import
	SET
	status = 'Ikke startet'
	WHERE tco_stage_assignment_import.fk_tenant_id = @tenant_id AND (tco_stage_assignment_import.status IS NULL OR tco_stage_assignment_import.status = '');

	IF(@jobId = -1)
		BEGIN
		-- validate assignemnt name
			UPDATE	tco_stage_assignment_import
			SET		assignment_name_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					(assignment_name IS NULL OR assignment_name = '');

		-- validate assignment category
			UPDATE	tco_stage_assignment_import
			SET		category_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					(category IS NULL OR category = '' OR category
					NOT IN (SELECT description FROM tco_category WHERE fk_tenant_id = @tenant_id and type = 'SUNIT_BPLAN'));
		
		-- validate start date
			UPDATE	tco_stage_assignment_import
			SET		start_date_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					((start_date IS NOT NULL AND start_date != '') AND (LEN(start_date) < 10 OR LEN(start_date) > 10 OR PATINDEX('[0-9][0-9].[0-9][0-9].[0-9][0-9][0-9][0-9]', start_date) = 0));

		-- validate end data (deadline)
			UPDATE	tco_stage_assignment_import
			SET		end_date_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					((end_date IS NOT NULL AND end_date != '') AND (LEN(end_date) < 10 OR LEN(end_date) > 10 OR PATINDEX('[0-9][0-9].[0-9][0-9].[0-9][0-9][0-9][0-9]', end_date) = 0));

		-- validate start date should be less than end date
			UPDATE tco_stage_assignment_import
			SET    start_date_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import 
			WHERE  fk_tenant_id = @tenant_id AND
					user_id = @user_id AND 
					start_date_error = 0 AND
					ISDATE(end_date) = 0 AND
					((end_date_error = 0) AND CONVERT(date, start_date, 104) > CONVERT(date, end_date, 104));

		--Validate end date should be greater then start date --
			UPDATE tco_stage_assignment_import
			SET    end_date_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import 
			WHERE  fk_tenant_id = @tenant_id AND
					user_id = @user_id AND 
					end_date_error = 0 AND
					ISDATE(start_date) = 0 AND
					((start_date_error = 0) AND CONVERT(date, start_date, 104) > CONVERT(date, end_date, 104));

		-- validate status
			UPDATE	tco_stage_assignment_import
			SET		status_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					(status IS NOT NULL AND status NOT IN
					(SELECT status_description from tco_progress_status WHERE fk_tenant_id = @tenant_id and type = 'sunit_bplan'));

		-- validate protocol (url_prefix)
			UPDATE	tco_stage_assignment_import
			SET		url_prefix_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					(url_prefix IS NOT NULL AND url_prefix != '' AND url_prefix != 'https://' AND url_prefix != 'http://');

		-- validate url
			UPDATE	tco_stage_assignment_import
			SET		url_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					(url IS NOT NULL AND url != '' AND url NOT LIKE '%[^a-zA-Z0-9]%.[a-z][a-z]%');

		-- validae url_title
			UPDATE	tco_stage_assignment_import
			SET		url_title_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					((url IS NOT NULL AND url != '') AND (url_title IS NULL OR url_title = ''));

		-- validate org level
			UPDATE	tco_stage_assignment_import
			SET		org_level_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND 
					(org_level IS NULL OR org_level = '' OR ISNUMERIC(org_level) = 0 OR CAST(org_level AS int) < 1 OR CAST(org_level AS int) > 9)

		-- validate org id
			UPDATE	tco_stage_assignment_import
			SET		org_id_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					((org_level = '1' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_1 = org_id) = 0) OR
					 (org_level = '2' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_2 = org_id) = 0) OR 
					 (org_level = '3' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_3 = org_id) = 0) OR
					 (org_level = '4' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_4 = org_id) = 0) OR
					 (org_level = '5' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_5 = org_id) = 0) OR
					 (org_level = '6' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_6 = org_id) = 0) OR
					 (org_level = '7' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_7 = org_id) = 0) OR
					 (org_level = '8' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_8 = org_id) = 0) OR
					 org_level_error = 1 );

		-- validate service id
			UPDATE	tco_stage_assignment_import
			SET		service_id_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					user_id = @user_id AND
					service_id <> '' AND
					(((select param_value from vw_tco_parameters where fk_tenant_id = @tenant_id AND param_name like 'MONTHREP_LEVEL_1' AND param_value IS NOT NULL) = 'service_id_1' AND (select count(pk_id) from tco_service_values where fk_tenant_id = @tenant_id AND service_id_1 = service_id) = 0) OR
					((select param_value from vw_tco_parameters where fk_tenant_id = @tenant_id AND param_name like 'MONTHREP_LEVEL_1' AND param_value IS NOT NULL) = 'service_id_2' AND (select count(pk_id) from tco_service_values where fk_tenant_id = @tenant_id AND service_id_2 = service_id) = 0) OR
					((select param_value from vw_tco_parameters where fk_tenant_id = @tenant_id AND param_name like 'MONTHREP_LEVEL_1' AND param_value IS NOT NULL) = 'service_id_3' AND (select count(pk_id) from tco_service_values where fk_tenant_id = @tenant_id AND service_id_3 = service_id) = 0));
		
		-- validate assignment owner
			UPDATE tco_stage_assignment_import
			SET	   assignment_owner_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   user_id = @user_id AND
				   assignment_owner IS NOT NULL AND assignment_owner <> '' AND
				   assignment_owner NOT IN (select first_name + ' ' + last_name from tco_users WHERE IsActive = 1 and first_name IS NOT NULL and last_name IS NOT NULL)

		-- validate assignment owner
			UPDATE tco_stage_assignment_import
			SET	   responsible_user_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   user_id = @user_id AND
				   responsible_user IS NOT NULL AND responsible_user <> '' AND
				   responsible_user NOT IN (select first_name + ' ' + last_name from tco_users WHERE IsActive = 1 and first_name IS NOT NULL and last_name IS NOT NULL)

		-- validate start year business plan
			UPDATE tco_stage_assignment_import
			SET	   start_year_bp_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   user_id = @user_id AND
				   start_year_bp IS NOT NULL AND start_year_bp <> '' AND (ISNUMERIC(start_year_bp) = 0 OR CAST(start_year_bp AS int) < @budgetYear)

		-- validate end year
			UPDATE tco_stage_assignment_import
			SET	   end_year_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   user_id = @user_id AND
				   end_year IS NOT NULL AND end_year <> '' AND (ISNUMERIC(end_year) = 0 OR CAST(end_year AS int) < @budgetYear OR (ISNUMERIC(start_year_bp) = 1 AND CAST(end_year AS int) < CAST(start_year_bp AS int)))

		END

	ELSE
		BEGIN
			-- validate assignemnt name
			UPDATE	tco_stage_assignment_import
			SET		assignment_name_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					(assignment_name IS NULL OR assignment_name = '');

		-- validate assignment category
			UPDATE	tco_stage_assignment_import
			SET		category_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					(category IS NULL OR category = '' OR category
					NOT IN (SELECT description FROM tco_category WHERE fk_tenant_id = @tenant_id and type = 'SUNIT_BPLAN'));
		
		-- validate start date
			UPDATE	tco_stage_assignment_import
			SET		start_date_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					((start_date IS NOT NULL AND start_date != '') AND (LEN(start_date) < 10 OR LEN(start_date) > 10 OR PATINDEX('[0-9][0-9].[0-9][0-9].[0-9][0-9][0-9][0-9]', start_date) = 0));

		-- validate end data (deadline)
			UPDATE	tco_stage_assignment_import
			SET		end_date_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					((end_date IS NOT NULL AND end_date != '') AND (LEN(end_date) < 10 OR LEN(end_date) > 10 OR PATINDEX('[0-9][0-9].[0-9][0-9].[0-9][0-9][0-9][0-9]', end_date) = 0));

		-- validate start date should be less than end date
			UPDATE tco_stage_assignment_import
			SET    start_date_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import 
			WHERE  fk_tenant_id = @tenant_id AND
					job_id = @jobID AND 
					start_date_error = 0 AND
					ISDATE(end_date) = 0 AND
					((end_date_error = 0) AND CONVERT(date, start_date, 104) > CONVERT(date, end_date, 104));

		--Validate end date should be greater then start date --
			UPDATE tco_stage_assignment_import
			SET    end_date_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import 
			WHERE  fk_tenant_id = @tenant_id AND
					job_id = @jobID AND 
					end_date_error = 0 AND
					ISDATE(start_date) = 0 AND
					((start_date_error = 0) AND CONVERT(date, start_date, 104) > CONVERT(date, end_date, 104));

		-- validate status
			UPDATE	tco_stage_assignment_import
			SET		status_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					(status IS NOT NULL AND status NOT IN
					(SELECT status_description from tco_progress_status WHERE fk_tenant_id = @tenant_id and type = 'sunit_bplan'));

		-- validate protocol (url_prefix)
			UPDATE	tco_stage_assignment_import
			SET		url_prefix_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					(url_prefix IS NOT NULL AND url_prefix != '' AND url_prefix != 'https://' AND url_prefix != 'http://');

		-- validate url
			UPDATE	tco_stage_assignment_import
			SET		url_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					(url IS NOT NULL AND url != '' AND url NOT LIKE '%[^a-zA-Z0-9]%.[a-z][a-z]%');

		-- validae url_title
			UPDATE	tco_stage_assignment_import
			SET		url_title_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					((url IS NOT NULL AND url != '') AND (url_title IS NULL OR url_title = ''));

		-- validate org level
			UPDATE	tco_stage_assignment_import
			SET		org_level_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND 
					(org_level IS NULL OR org_level = '' OR ISNUMERIC(org_level) = 0 OR CAST(org_level AS int) < 1 OR CAST(org_level AS int) > 9)

		-- validate org id
			UPDATE	tco_stage_assignment_import
			SET		org_id_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					((org_level = '1' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_1 = org_id) = 0) OR
					 (org_level = '2' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_2 = org_id) = 0) OR 
					 (org_level = '3' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_3 = org_id) = 0) OR
					 (org_level = '4' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_4 = org_id) = 0) OR
					 (org_level = '5' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_5 = org_id) = 0) OR
					 (org_level = '6' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_6 = org_id) = 0) OR
					 (org_level = '7' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_7 = org_id) = 0) OR
					 (org_level = '8' AND (select count(pk_id) from tco_org_hierarchy where fk_tenant_id = @tenant_id AND org_id_8 = org_id) = 0) OR
					 org_level_error = 1 );

		-- validate service id
			UPDATE	tco_stage_assignment_import
			SET		service_id_error = 1, error_count = error_count + 1
			FROM	tco_stage_assignment_import
			WHERE	fk_tenant_id = @tenant_id AND
					job_id = @jobID AND
					service_id <> ''AND
					(((select param_value from vw_tco_parameters where fk_tenant_id = @tenant_id AND param_name like 'MONTHREP_LEVEL_1' AND param_value IS NOT NULL) = 'service_id_1' AND (select count(pk_id) from tco_service_values where fk_tenant_id = @tenant_id AND service_id_1 = service_id) = 0) OR
					((select param_value from vw_tco_parameters where fk_tenant_id = @tenant_id AND param_name like 'MONTHREP_LEVEL_1' AND param_value IS NOT NULL) = 'service_id_2' AND (select count(pk_id) from tco_service_values where fk_tenant_id = @tenant_id AND service_id_2 = service_id) = 0) OR
					((select param_value from vw_tco_parameters where fk_tenant_id = @tenant_id AND param_name like 'MONTHREP_LEVEL_1' AND param_value IS NOT NULL) = 'service_id_3' AND (select count(pk_id) from tco_service_values where fk_tenant_id = @tenant_id AND service_id_3 = service_id) = 0));
		
		-- validate assignment owner
			UPDATE tco_stage_assignment_import
			SET	   assignment_owner_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   job_id = @jobID AND
				   assignment_owner IS NOT NULL AND assignment_owner <> '' AND
				   assignment_owner NOT IN (select first_name + ' ' + last_name from tco_users WHERE IsActive = 1 and first_name IS NOT NULL and last_name IS NOT NULL)

		-- validate assignment owner
			UPDATE tco_stage_assignment_import
			SET	   responsible_user_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   job_id = @jobID AND
				   responsible_user IS NOT NULL AND responsible_user <> '' AND
				   responsible_user NOT IN (select first_name + ' ' + last_name from tco_users WHERE IsActive = 1 and first_name IS NOT NULL and last_name IS NOT NULL)

		-- validate start year business plan
			UPDATE tco_stage_assignment_import
			SET	   start_year_bp_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   job_id = @jobID AND
					start_year_bp IS NOT NULL AND start_year_bp <> '' AND (ISNUMERIC(start_year_bp) = 0 OR CAST(start_year_bp AS int) < @budgetYear)

		-- validate end year
			UPDATE tco_stage_assignment_import
			SET	   end_year_error = 1, error_count = error_count + 1
			FROM   tco_stage_assignment_import
			WHERE  fk_tenant_id = @tenant_id AND
				   job_id = @jobID AND
				   end_year IS NOT NULL AND end_year <> '' AND (ISNUMERIC(end_year) = 0 OR CAST(end_year AS int) < @budgetYear OR (ISNUMERIC(start_year_bp) = 1 AND CAST(end_year AS int) < CAST(start_year_bp AS int)))

		END
