CREATE OR ALTER PROCEDURE [dbo].[prcPoliticalSimulation2A_2021]          
 @tenant_id int,           
 @budget_year int,          
 @user_id int,          
 @proposal_id UNIQUEIDENTIFIER           
          
          
AS          
          
--DECLARE @tenant_id int = 4097, @budget_year int = 2021, @user_id int = 4097999, @proposal_id UNIQUEIDENTIFIER = newid()    
--SELECT @proposal_id          
DECLARE @org_version VARCHAR(24)= (select pk_org_version FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND @budget_year * 100 +1 BETWEEN period_from AND period_to)          
DECLARE @langstring_project NVARCHAR(255)           
DECLARE @param_inv_grouping VARCHAR(255)          
DECLARE @default_change_id INT          
DECLARE @param_value_18 nvarchar(500)          
DECLARE @year INT = @budget_year          
          
DECLARE @language VARCHAR(10)          
          
SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @tenant_id)          
          
DECLARE @tbl_reporting_line as table (report varchar(50)          
, fk_kostra_account_code VARCHAR(25)          
, line_group_id INT          
, line_group varchar(200)          
, line_item_id INT          
, line_item varchar(150))          
          
          
INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)          
SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id,           
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),          
line_item_id,          
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))          
FROM gmd_reporting_line rl          
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language          
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language          
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @tenant_id AND tg.Language = @language          
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @tenant_id AND tI.Language = @language          
          
          
SET @param_value_18 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id AND active = 1 AND param_name = 'FINPLAN_INVESTMENT_LEVEL')          
          
          
SET @langstring_project =           
(          
SELECT isnull(s3.description, ISNULL(s2.description, s.description)) FROM gco_language_strings s          
LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language          
LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @tenant_id          
WHERE s.id = 'BudForm_2B_projectmissing' AND s.language = @language)          
          
SET @param_inv_grouping = (SELECT param_value FROM vw_tco_parameters WHERE param_name = '2B_TABS_GROUPING' AND fk_tenant_id = @tenant_id)          
          
IF @param_inv_grouping IS NULL           
BEGIN          
SET @param_inv_grouping = 'project_code'          
END          
          
          
SELECT sv.service_id_1, sv.service_id_2,sv.service_id_3, sv.service_id_4, sv.service_id_5,          
sv.service_name_1, sv.service_name_2, sv.service_name_3, sv.service_name_4, sv.service_name_5,          
sv.fk_function_code, p.pk_project_code          
INTO #service_data          
FROM tco_service_values sv           
LEFT JOIN tco_main_projects mp ON sv.fk_tenant_id = mp.fk_tenant_id AND sv.fk_function_code = mp.fk_function_code AND @year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)           
LEFT JOIN tco_projects p ON mp.fk_tenant_id = p.fk_tenant_id  AND p.fk_main_project_code = mp.pk_main_project_code           
WHERE sv.fk_tenant_id = @tenant_id          
          
          
SELECT oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,          
oh.org_name_1, oh.org_name_2, oh.org_name_3, oh.org_name_4, oh.org_name_5,          
oh.fk_department_code, p.pk_project_code          
INTO #org_data          
FROM tco_org_hierarchy oh           
LEFT JOIN tco_main_projects mp ON oh.fk_tenant_id = mp.fk_tenant_id AND oh.fk_department_code = mp.fk_department_code AND @year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)           
LEFT JOIN tco_projects p ON mp.fk_tenant_id = p.fk_tenant_id  AND p.fk_main_project_code = mp.pk_main_project_code           
WHERE oh.fk_tenant_id = @tenant_id AND oh.fk_org_version = @org_version          
          
          
CREATE TABLE #temp_table_1          
 (fk_tenant_id INT NOT NULL,           
    fk_account_code NVARCHAR(25) NOT NULL,           
 forecast_period INT NULL DEFAULT 0,          
    fk_department_code NVARCHAR(25) NOT NULL,           
    fk_function_code NVARCHAR(25) NOT NULL,           
    fk_project_code NVARCHAR(25) NOT NULL,           
 free_dim_1 NVARCHAR(25) NOT NULL,           
 free_dim_2 NVARCHAR(25) NOT NULL,           
 free_dim_3 NVARCHAR(25) NOT NULL,           
 free_dim_4 NVARCHAR(25) NOT NULL,           
    actual_amt_year DECIMAL(18, 2) NOT NULL,           
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,          
    actual_amt_year_minus2 DECIMAL(18, 2) NOT NULL,          
 org_bud_amt_year DECIMAL (18, 2) NOT NULL,          
 org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,          
 revised_bud_amt_year DECIMAL (18, 2) NOT NULL,          
 revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,          
 finplan_year_1_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_2_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_3_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_4_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_5_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_6_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_7_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_8_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_9_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_10_amount DECIMAL (18, 2) NOT NULL,          
 forecast_amount DECIMAL (18, 2) NOT NULL,          
 budget_period DECIMAL (18, 2) NULL DEFAULT 0,          
 accounting_period DECIMAL (18, 2) NULL DEFAULT 0,          
 accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,          
 budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,          
 accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,          
 budget_change DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_action DECIMAL (18, 2) NULL DEFAULT 0,          
 forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,          
 [fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',          
 action_name NVARCHAR(150) NULL,          
 action_type INT NULL,          
 line_order INT NULL,          
 org_budget_flag INT NOT NULL DEFAULT 1,          
 inv_fk_org_id NVARCHAR(25)  NULL,           
 inv_org_name NVARCHAR(255)  NULL,          
 fk_investment_id INT NULL,           
 fk_portfolio_code NVARCHAR(25) NULL,          
 inv_line_group INT NULL,          
 [fk_adjustment_code] NVARCHAR(25) DEFAULT '' NOT NULL,          
 [fk_change_id] INT NOT NULL DEFAULT 0          
 )          
          
          
CREATE TABLE #temp_table_2          
 (fk_tenant_id INT NOT NULL,           
 year INT NOT NULL,          
 forecast_period INT NULL DEFAULT 0,          
    fk_account_code NVARCHAR(25) NOT NULL,           
    fk_department_code NVARCHAR(25) NOT NULL,           
    fk_function_code NVARCHAR(25) NOT NULL,           
 fk_project_code NVARCHAR(25) DEFAULT '' NOT NULL,           
 free_dim_1 NVARCHAR(25) NOT NULL,           
 free_dim_2 NVARCHAR(25) NOT NULL,           
 free_dim_3 NVARCHAR(25) NOT NULL,           
 free_dim_4 NVARCHAR(25) NOT NULL,           
    actual_amt_year DECIMAL(18, 2) NOT NULL,           
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,          
    actual_amt_year_minus2 DECIMAL(18, 2) NOT NULL,          
 org_bud_amt_year DECIMAL (18, 2) NOT NULL,          
 org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,          
 revised_bud_amt_year DECIMAL (18, 2) NOT NULL,          
 revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,          
 finplan_year_1_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_2_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_3_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_4_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_5_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_6_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_7_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_8_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_9_amount DECIMAL (18, 2) NOT NULL,          
 finplan_year_10_amount DECIMAL (18, 2) NOT NULL,          
 forecast_amount DECIMAL (18, 2) NOT NULL,          
 budget_period DECIMAL (18, 2) NULL DEFAULT 0,          
 accounting_period DECIMAL (18, 2) NULL DEFAULT 0,          
 accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,          
 budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,          
 accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,          
 budget_change DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_action DECIMAL (18, 2) NULL DEFAULT 0,          
 forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,          
 deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,          
 [1A_line] bit DEFAULT ((0)) NOT NULL,          
 [line_item_id] INT DEFAULT (0) NOT NULL,          
 [line_item] NVARCHAR(200) DEFAULT(''),          
 [line_group_id] INT DEFAULT (0) NOT NULL,          
 [line_group] NVARCHAR(200) DEFAULT(''),          
 [fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',          
 [acc_type] INT DEFAULT ((0)) NOT NULL,          
 [fr_1A_line] bit DEFAULT ((0)) NOT NULL,          
 [fr_line_item_id] INT DEFAULT (0) NOT NULL,          
 [fr_line_item] NVARCHAR(200) DEFAULT(''),          
 [fr_line_group_id] INT DEFAULT (0) NOT NULL,          
 [fr_line_group] NVARCHAR(200) DEFAULT(''),          
 org_budget_flag INT NOT NULL DEFAULT 1,          
 [fk_adjustment_code] NVARCHAR(25) DEFAULT '' NOT NULL,          
 [fk_change_id] INT NOT NULL DEFAULT 0          
 )          
          
           
           
CREATE INDEX #ind_1_temp_table_2 ON #temp_table_2 (fk_account_code, fk_department_code, fk_function_code, fk_tenant_id);          
          
CREATE INDEX #ind_2_temp_table_2 ON #temp_table_2 ([1A_line], line_group_id);          
          
CREATE INDEX #ind_3_temp_table_2 ON #temp_table_2 (acc_type);          
          
CREATE INDEX #ind_4_temp_table_2 ON #temp_table_2 ([fr_1A_line], fr_line_group_id);          
          
CREATE INDEX #ind_5_temp_table_2 ON #temp_table_2 (fk_account_code, fk_tenant_id, year);          
          
CREATE TABLE #valid_accounts(           
 [pk_id] INT NOT NULL IDENTITY,           
 pk_tenant_id INT NOT NULL,          
    pk_account_code NVARCHAR(25) NOT NULL,          
    fk_kostra_account_code NVARCHAR(25) NOT NULL)          
          
INSERT INTO #valid_accounts (pk_tenant_id, pk_account_code, fk_kostra_account_code)          
          
          
SELECT DISTINCT pk_tenant_id, pk_account_code, fk_kostra_account_code FROM           
(SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code          
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @year BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)          
UNION          
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code          
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @year-1 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)          
UNION           
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code          
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @year-2 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)       
) s          
          
          
CREATE TABLE #valid_mainprojects(           
 [pk_id] INT NOT NULL IDENTITY,           
 fk_tenant_id INT NOT NULL,          
    pk_main_project_code NVARCHAR(25) NOT NULL,          
    main_project_name NVARCHAR(100) NOT NULL)          
          
CREATE UNIQUE INDEX #_IND_valid_mainprojects ON #valid_mainprojects (pk_main_project_code, fk_tenant_id)          
          
INSERT INTO #valid_mainprojects ( fk_tenant_id, pk_main_project_code, main_project_name)          
SELECT DISTINCT fk_tenant_id, pk_main_project_code, '' as main_project_name FROM           
(SELECT fk_tenant_id,pk_main_project_code          
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @year BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)          
UNION          
SELECT fk_tenant_id,pk_main_project_code          
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @year-1 BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)          
UNION           
SELECT fk_tenant_id,pk_main_project_code          
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @year-2 BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)          
) s          
          
UPDATE a SET a.main_project_name = b.main_project_name          
from #valid_mainprojects a          
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @year BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)          
WHERE a.main_project_name = ''          
          
UPDATE a SET a.main_project_name = b.main_project_name          
from #valid_mainprojects a          
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @year-1 BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)          
WHERE a.main_project_name = ''          
          
UPDATE a SET a.main_project_name = b.main_project_name          
from #valid_mainprojects a          
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @year-2 BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)          
WHERE a.main_project_name = ''          
          
          
RAISERROR ('START : Fetch data', 0, 1) WITH NOWAIT          
PRINT convert(nvarchar(19),SYSDATETIME())          
          
INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code,           
free_dim_1, free_dim_2, free_dim_3, free_dim_4,          
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,          
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,          
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount, finplan_year_5_amount, finplan_year_6_amount, finplan_year_7_amount,          
finplan_year_8_amount, finplan_year_9_amount, finplan_year_10_amount, forecast_amount, fk_prog_code)          
SELECT fk_tenant_id, fk_account_code, department_code, fk_function_code,          
free_dim_1, free_dim_2, free_dim_3, free_dim_4,          
fk_project_code, 0 as actual_amt_year,0 as actual_amt_last_year,sum(amount) AS actual_amt_year_minus2,          
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS  revised_bud_amt_year, 0 AS revised_bud_amt_last_year,          
0 AS finplan_year_1_amount, 0 AS finplan_year_2_amount, 0 AS  finplan_year_3_amount, 0 AS finplan_year_4_amount, 0 AS finplan_year_5_amount,          
0 AS finplan_year_6_amount, 0 AS finplan_year_7_amount, 0 AS finplan_year_8_amount, 0 AS finplan_year_9_amount, 0 AS finplan_year_10_amount, 0 AS forecast_amount,          
a.fk_prog_code          
FROM tfp_accounting_data a          
 JOIN #valid_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code           
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'          
WHERE fk_tenant_id = @tenant_id          
AND gl_year = @year-2          
GROUP BY  fk_tenant_id, fk_account_code, department_code, fk_function_code,fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.fk_prog_code          
          
RAISERROR ('FINISH: Fetch data tfp_accounting_data 3', 0, 1) WITH NOWAIT          
PRINT convert(nvarchar(19),SYSDATETIME())          
          
          
          
          
UPDATE #temp_table_1 SET  org_bud_amt_year = 0, org_bud_amt_last_year = 0, revised_bud_amt_year = 0,revised_bud_amt_last_year=0          
FROM #temp_table_1 a, #valid_accounts b, gco_kostra_accounts c          
WHERE  a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --AND a.year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)          
AND b.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'investment'          
          
          
          
select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,           
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,ISNULL(pt.fk_change_id,0) AS fk_change_id,           
pt.fk_alter_code, pt.fk_adjustment_code,isnull(p.fk_prog_code,'1') AS fk_prog_code,          
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function,           
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,          
adjustment_code_status = CASE              
WHEN UAD.status = 1 THEN 1          
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @year THEN 1          
ELSE 0          
END          
INTO #inv_hlptab          
FROM tfp_proj_transactions PT          
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)          
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)          
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code          
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1          
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)          
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year from tfp_budget_changes          
        where fk_tenant_id = @tenant_id          
        and budget_year < @year          
        UNION            
      ((SELECT tf.fk_tenant_id, tf.pk_change_id, tf.org_budget_flag, tf.budget_year        
          FROM tfp_budget_changes tf        
          JOIN tco_budget_phase tc         
            ON   tf.fk_tenant_id = tc.fk_tenant_id      
            AND  tf.fk_budget_phase_id = tc.pk_budget_phase_id     
          WHERE  tf.fk_tenant_id = @tenant_id        
            AND  tf.budget_year = @year and         
          tc.sort_order <= (        
           SELECT sort_order        
           FROM tco_budget_phase tp    
          JOIN  tps_admin_config ta         
           ON   ta.fk_tenant_id = tp.fk_tenant_id     
           AND  ta.budgetphase_id = tp.pk_budget_phase_id    
           WHERE ta.fk_tenant_id = @tenant_id and ta.budget_year = @year) )        
      ))  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id          
where PT.fk_tenant_id = @tenant_id          
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,           
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,          
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,          
UAD.status, UAD.include_in_calculation,BC.budget_year          
          
DELETE FROM #inv_hlptab WHERE adjustment_code_status = 0          
          
 SET @default_change_id = (          
 SELECT top 1 pk_change_id FROM tfp_budget_changes ch          
 JOIN tco_budget_phase ph ON ch.fk_tenant_id = ph.fk_tenant_id AND ch.fk_budget_phase_id = ph.pk_budget_phase_id          
 WHERE ch.fk_tenant_id = @tenant_id AND ch.budget_year = @year AND ch.org_budget_flag = 1          
 ORDER BY ph.sort_order, ch.pk_change_id)          
          
    if @default_change_id is null          
    begin          
    set @default_change_id = 0          
    end          
          
          
 UPDATE a SET a.fk_change_id = @default_change_id          
 FROM #inv_hlptab a          
 JOIN tfp_budget_changes ch ON ch.fk_tenant_id = a.fk_tenant_id AND a.fk_change_id = ch.pk_change_id and ch.budget_year < @year          
 ;           
          
select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,           
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,@default_change_id as fk_change_id,           
pt.fk_alter_code, pt.fk_adjustment_code,isnull(p.fk_prog_code,'1') AS fk_prog_code,          
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function,           
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,          
adjustment_code_status = CASE              
WHEN UAD.status = 1 THEN 1          
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @year THEN 1          
ELSE 0          
END          
INTO #inv_hlptab_prev_year          
FROM tfp_proj_transactions PT          
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @year-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)          
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)          
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code          
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1          
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)          
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year from tfp_budget_changes          
        where fk_tenant_id = @tenant_id          
        and budget_year < @year-1          
        UNION          
        select fk_tenant_id, pk_change_id, org_budget_flag, budget_year from tfp_budget_changes          
        where fk_tenant_id = @tenant_id          
        and budget_year = @year-1          
    )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id          
where PT.fk_tenant_id = @tenant_id          
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,           
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,          
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,          
UAD.status, UAD.include_in_calculation, BC.budget_year          
          
DELETE FROM #inv_hlptab_prev_year WHERE adjustment_code_status = 0;          
          
SELECT fk_tenant_id,@year AS budget_year,pk_main_project_code,main_project_name,inv_status,          
is_temp,completion_date,fk_account_code,fk_department_code,          
fk_function_code,fk_project_code,project_name,fk_change_id,          
fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept, header_function,approval_reference,           
approval_ref_url,original_finish_year,          
org_bud_last_year = convert(dec(18,2),0),          
rev_bud_last_year = convert(dec(18,2),0),          
year_1_amount = CASE WHEN year = @year AND org_budget_flag = 1 THEN amount else 0 end,          
year_2_amount = CASE WHEN year = @year+1 AND org_budget_flag = 1  THEN amount else 0 end,          
year_3_amount = CASE WHEN year = @year+2 AND org_budget_flag = 1  THEN amount else 0 end,          
year_4_amount = CASE WHEN year = @year+3 AND org_budget_flag = 1  THEN amount else 0 end,          
year_5_amount = CASE WHEN year = @year+4 AND org_budget_flag = 1  THEN amount else 0 end,          
year_6_amount = CASE WHEN year = @year+5 AND org_budget_flag = 1  THEN amount else 0 end,          
year_7_amount = CASE WHEN year = @year+6 AND org_budget_flag = 1  THEN amount else 0 end,          
year_8_amount = CASE WHEN year = @year+7 AND org_budget_flag = 1 THEN amount else 0 end,          
year_9_amount = CASE WHEN year = @year+8 AND org_budget_flag = 1 THEN amount else 0 end,          
year_10_amount = CASE WHEN year = @year+9 AND org_budget_flag = 1 THEN amount else 0 end,          
net_cost = CASE WHEN inv_status = 2 OR year = -1 THEN 0 else amount END,          
previously_budgeted = CASE WHEN year < @year AND year != -1 THEN amount ELSE 0 END,          
approved_cost = CASE WHEN year = -1 THEN amount else 0 end          
INTO #inv_hlptab2          
 FROM #inv_hlptab          
          
           
 INSERT INTO #inv_hlptab2 (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,inv_status,is_temp,          
 completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,          
 fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function,          
 approval_reference,approval_ref_url,original_finish_year,org_bud_last_year,rev_bud_last_year,          
 year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,          
 year_7_amount,year_8_amount,year_9_amount,year_10_amount,net_cost,previously_budgeted,approved_cost)          
SELECT fk_tenant_id,@year AS budget_year,pk_main_project_code,main_project_name,inv_status,          
is_temp,completion_date,fk_account_code,fk_department_code,          
fk_function_code,fk_project_code,project_name,fk_change_id,          
fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept, header_function,approval_reference,           
approval_ref_url,original_finish_year,          
org_bud_last_year = CASE WHEN year = @year-1 AND org_budget_flag = 1 THEN amount else 0 end,          
rev_bud_last_year = CASE WHEN year = @year-1 THEN amount else 0 end,          
year_1_amount = 0,          
year_2_amount = 0,          
year_3_amount = 0,          
year_4_amount = 0,          
year_5_amount = 0,          
year_6_amount = 0,          
year_7_amount = 0,          
year_8_amount = 0,          
year_9_amount = 0,          
year_10_amount = 0,          
net_cost = 0,          
previously_budgeted = 0,          
approved_cost = 0          
FROM #inv_hlptab_prev_year          
-- Set original budget last year          
          
--UPDATE a SET a.org_bud_last_year = 0          
--FROM #inv_hlptab2 a          
--JOIN tfp_budget_changes ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.fk_change_id = ch.pk_change_id           
--AND ch.budget_year = @year-1 AND ch.org_budget_flag = 0          
          
          
--UPDATE a SET a.org_bud_last_year = 0, rev_bud_last_year = 0          
--FROM #inv_hlptab2 a          
--JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id AND a.pk_main_project_code = mp.pk_main_project_code          
--AND @year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)          
--AND mp.inv_status IN (3,4,5)          
          
UPDATE #inv_hlptab2 SET org_bud_last_year = 0, rev_bud_last_year = 0, year_1_amount = 0, year_2_amount = 0, year_3_amount = 0, year_4_amount = 0, year_5_amount = 0,          
year_6_amount = 0, year_7_amount = 0, year_8_amount = 0, year_9_amount = 0, year_10_amount = 0          
WHERE inv_status IN (3,4,5)          
          
 SELECT a.fk_tenant_id,a.budget_year,fp_level_1_value = CONVERT(NVARCHAR(25),''),fp_level_1_name= CONVERT(NVARCHAR(100),''),          
 fp_level_2_value = CONVERT(NVARCHAR(25),''), fp_level_2_name = CONVERT(NVARCHAR(100),''),          
 ISNULL(a.pk_main_project_code,'') AS pk_main_project_code,          
 ISNULL(a.main_project_name,'') AS main_project_name,          
 ISNULL(a.inv_status,100) AS inv_status,          
 ISNULL(a.is_temp,0) AS is_temp,          
 ISNULL(a.completion_date,'1900-01-01') AS completion_date,          
 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,          
 a.fk_prog_code,a.header_dept, a.header_function,          
 oh.org_id_1 as header_org_id_1, oh.org_name_1 as header_org_name_1, oh.org_id_2  as header_org_id_2, oh.org_name_2 as header_org_name_2,          
 oh.org_id_3  as header_org_id_3, oh.org_name_3 as header_org_name_3,oh.org_id_4  as header_org_id_4, oh.org_name_4 as header_org_name_4,          
 oh.org_id_5  as header_org_id_5, oh.org_name_5 as header_org_name_5,          
 od.org_id_1 as detail_org_id_1, od.org_name_1 as detail_org_name_1, od.org_id_2  as detail_org_id_2, od.org_name_2 as detail_org_name_2,          
 od.org_id_3  as detail_org_id_3, od.org_name_3 as detail_org_name_3,od.org_id_4  as detail_org_id_4, od.org_name_4 as detail_org_name_4,          
 od.org_id_5  as detail_org_id_5, od.org_name_5 as detail_org_name_5,          
 ISNULL(sh.service_id_1,'')  as header_service_id_1, ISNULL(sh.service_name_1,'') as header_service_name_1, ISNULL(sh.service_id_2,'')  as header_service_id_2, ISNULL(sh.service_name_2,'') as header_service_name_2,          
 ISNULL(sh.service_id_3,'')  as header_service_id_3, ISNULL(sh.service_name_3,'') as header_service_name_3, ISNULL(sh.service_id_4,'')  as header_service_id_4, ISNULL(sh.service_name_4,'') as header_service_name_4,          
 ISNULL(sh.service_id_5,'')  as header_service_id_5, ISNULL(sh.service_name_5,'') as header_service_name_5,                        
 ISNULL(sd.service_id_1,'')  as detail_service_id_1, ISNULL(sd.service_name_1,'') as detail_service_name_1, ISNULL(sd.service_id_2,'')  as detail_service_id_2, ISNULL(sd.service_name_2,'') as detail_service_name_2,          
 ISNULL(sd.service_id_3,'')  as detail_service_id_3, ISNULL(sd.service_name_3,'') as detail_service_name_3, ISNULL(sd.service_id_4,'')  as detail_service_id_4, ISNULL(sd.service_name_4,'') as detail_service_name_4,          
 ISNULL(sd.service_id_5,'')  as detail_service_id_5, ISNULL(sd.service_name_5,'') as detail_service_name_5,          
 ISNULL(ip.description,'') as program_code_description,rl.line_group_id, rl.line_item_id,          
 sum(org_bud_last_year) as org_bud_last_year, sum(rev_bud_last_year) as rev_bud_last_year,          
 sum(year_1_amount) as year_1_amount,sum(year_2_amount) as year_2_amount,          
 sum(year_3_amount) as year_3_amount,          
 sum(year_4_amount) as year_4_amount,sum(year_5_amount) as year_5_amount,          
 sum(year_6_amount) as year_6_amount,sum(year_7_amount) as year_7_amount,          
 sum(year_8_amount) as year_8_amount,sum(year_9_amount) as year_9_amount,          
 sum(year_10_amount) as year_10_amount,            
 sum_finplan = convert(dec(18,2),0),          
finished_year = isnull(DATEPART(YEAR, completion_date),@year),           
SUM(approved_cost) as approved_cost,           
ISNULL(a.approval_reference,'') AS approval_reference,          
ISNULL(a.approval_ref_url,'') AS approval_ref_url,          
ISNULL(a.original_finish_year,0) AS original_finish_year,          
inv_year_1_amount = convert(dec(18,2),0),           
inv_year_2_amount = convert(dec(18,2),0),           
inv_year_3_amount = convert(dec(18,2),0),           
inv_year_4_amount = convert(dec(18,2),0),           
inv_year_5_amount = convert(dec(18,2),0),           
inv_year_6_amount = convert(dec(18,2),0),           
inv_year_7_amount = convert(dec(18,2),0),           
inv_year_8_amount = convert(dec(18,2),0),          
inv_year_9_amount = convert(dec(18,2),0),           
inv_year_10_amount = convert(dec(18,2),0),          
fin_year_1_amount = convert(dec(18,2),0),           
fin_year_2_amount = convert(dec(18,2),0),           
fin_year_3_amount = convert(dec(18,2),0),           
fin_year_4_amount = convert(dec(18,2),0),           
fin_total_amount = convert(dec(18,2),0),          
SUM(previously_budgeted) as previously_budgeted,          
gross_cost = convert(dec(18,2),0),          
financed_amount = convert(dec(18,2),0),          
SUM(net_cost) AS net_cost,          
type = CASE WHEN rl.line_item_id = 1010 THEN 'i' ELSE 'f' END          
 INTO #inv_hlptab3          
 FROM #inv_hlptab2 a          
 LEFT JOIN tco_org_hierarchy oh ON a.header_dept = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version          
 JOIN tco_org_hierarchy od ON a.fk_department_code = od.fk_department_code AND a.fk_tenant_id = od.fk_tenant_id AND od.fk_org_version = @org_version          
 LEFT JOIN tco_service_values sh ON a.header_function = sh.fk_function_code AND a.fk_tenant_id = sh.fk_tenant_id          
 LEFT JOIN tco_service_values sd ON a.fk_function_code = sd.fk_function_code AND a.fk_tenant_id = sd.fk_tenant_id          
 LEFT JOIN tco_inv_program ip ON a.fk_prog_code = ip.pk_prog_code AND a.fk_tenant_id = ip.fk_tenant_id          
 LEFT JOIN #valid_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code --AND a.budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)          
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'          
 group by a.fk_tenant_id,a.budget_year,a.pk_main_project_code,a.main_project_name,a.inv_status,a.is_temp,completion_date,          
 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,          
 a.fk_prog_code,a.header_dept, a.header_function,          
 oh.org_id_1, oh.org_name_1, oh.org_id_2, oh.org_name_2,          
 oh.org_id_3, oh.org_name_3,oh.org_id_4, oh.org_name_4,          
 oh.org_id_5, oh.org_name_5,          
 od.org_id_1, od.org_name_1, od.org_id_2, od.org_name_2,          
 od.org_id_3, od.org_name_3, od.org_id_4, od.org_name_4,          
 od.org_id_5, od.org_name_5,  sh.service_id_1, sh.service_name_1, sh.service_id_2, sh.service_name_2,          
 sh.service_id_3, sh.service_name_3,sh.service_id_4, sh.service_name_4,          
 sh.service_id_5, sh.service_name_5,          
 sd.service_id_1, sd.service_name_1, sd.service_id_2, sd.service_name_2,          
 sd.service_id_3, sd.service_name_3,sd.service_id_4, sd.service_name_4,          
 sd.service_id_5, sd.service_name_5,          
ip.description,rl.line_group_id, rl.line_item_id,a.approval_reference,a.approval_ref_url, a.original_finish_year;          
          
          
UPDATE #inv_hlptab3 SET           
header_org_id_1 = detail_org_id_1,          
header_org_id_2 = detail_org_id_2,          
header_org_id_3 = detail_org_id_3,          
header_org_id_4 = detail_org_id_4,          
header_org_id_5 = detail_org_id_5,          
header_org_name_1 = detail_org_name_1,          
header_org_name_2 = detail_org_name_2,          
header_org_name_3 = detail_org_name_3,          
header_org_name_4 = detail_org_name_4,          
header_org_name_5 = detail_org_name_5,          
header_dept = fk_department_code         
WHERE inv_status = 100 or header_org_id_1 is null;          
          
          
PRINT 'Get investment finplan data'          
          
INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code,           
free_dim_1, free_dim_2, free_dim_3, free_dim_4,          
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,          
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,          
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount, finplan_year_5_amount,          
finplan_year_6_amount, finplan_year_7_amount, finplan_year_8_amount, finplan_year_9_amount, finplan_year_10_amount,          
forecast_amount,          
fk_investment_id, fk_portfolio_code, fk_prog_code, fk_change_id)          
SELECT a.fk_tenant_id, a.fk_account_code, a.fk_department_code, a.fk_function_code,           
'' AS free_dim_1, '' AS free_dim_2, '' AS free_dim_3, '' AS free_dim_4,          
a.fk_project_code,0 as actual_amt_year,0 as actual_amt_last_year,0 as actual_amt_year_minus2,          
SUM(a.year_1_amount) as org_bud_amt_year,SUM(a.org_bud_last_year) as  org_bud_amt_last_year,          
SUM(a.year_1_amount) as  revised_bud_amt_year,SUM(a.rev_bud_last_year)as revised_bud_amt_last_year,          
SUM(a.year_1_amount) as finplan_year_1_amount, SUM(a.year_2_amount)  as finplan_year_2_amount,           
SUM(a.year_3_amount) as finplan_year_3_amount, SUM(a.year_4_amount) as finplan_year_4_amount,          
SUM(a.year_5_amount) as finplan_year_5_amount, SUM(a.year_6_amount) as finplan_year_6_amount,          
SUM(a.year_7_amount) as finplan_year_7_amount, SUM(a.year_8_amount) as finplan_year_8_amount,          
SUM(a.year_9_amount) as finplan_year_9_amount, SUM(a.year_10_amount) as finplan_year_10_amount,          
0 as forecast_amount,          
0 as fk_investment_id, '' as fk_portfolio_code, a.fk_prog_code, fk_change_id          
FROM #inv_hlptab3 a          
GROUP BY a.fk_tenant_id, a.fk_account_code, a.fk_department_code, a.fk_function_code,           
a.fk_project_code,a.fk_prog_code, fk_change_id          
          
          
PRINT 'Fecth investment header org/service'          
          
UPDATE a          
SET a.inv_fk_org_id = CASE          
WHEN @param_value_18 = 'org_id_1' THEN b.org_id_1          
WHEN @param_value_18 = 'org_id_2' THEN b.org_id_2          
WHEN @param_value_18 = 'org_id_3' THEN b.org_id_3          
WHEN @param_value_18 = 'org_id_4' THEN b.org_id_4          
WHEN @param_value_18 = 'org_id_5' THEN b.org_id_5          
ELSE b.org_id_2          
END,          
inv_org_name = CASE           
WHEN @param_value_18 = 'org_id_1' THEN b.org_name_1          
WHEN @param_value_18 = 'org_id_2' THEN b.org_name_2          
WHEN @param_value_18 = 'org_id_3' THEN b.org_name_3          
WHEN @param_value_18 = 'org_id_4' THEN b.org_name_4          
WHEN @param_value_18 = 'org_id_5' THEN b.org_name_5          
ELSE b.org_name_2          
END          
FROM #temp_table_1 a          
JOIN #org_data b ON a.fk_project_code = b.pk_project_code           
WHERE @param_value_18 like 'org%'          
          
UPDATE a          
SET a.inv_fk_org_id = CASE          
WHEN @param_value_18 = 'org_id_1' THEN b.org_id_1          
WHEN @param_value_18 = 'org_id_2' THEN b.org_id_2          
WHEN @param_value_18 = 'org_id_3' THEN b.org_id_3          
WHEN @param_value_18 = 'org_id_4' THEN b.org_id_4          
WHEN @param_value_18 = 'org_id_5' THEN b.org_id_5          
ELSE b.org_id_2          
END,          
inv_org_name = CASE           
WHEN @param_value_18 = 'org_id_1' THEN b.org_name_1          
WHEN @param_value_18 = 'org_id_2' THEN b.org_name_2          
WHEN @param_value_18 = 'org_id_3' THEN b.org_name_3          
WHEN @param_value_18 = 'org_id_4' THEN b.org_name_4          
WHEN @param_value_18 = 'org_id_5' THEN b.org_name_5          
ELSE b.org_name_2          
END          
FROM #temp_table_1 a          
JOIN #org_data b ON a.fk_department_code = b.fk_department_code          
WHERE @param_value_18 like 'org%' AND a.inv_fk_org_id is null          
          
          
          
UPDATE a          
SET a.inv_fk_org_id = CASE          
WHEN @param_value_18 = 'service_id_1' THEN b.service_id_1          
WHEN @param_value_18 = 'service_id_2' THEN b.service_id_2          
WHEN @param_value_18 = 'service_id_3' THEN b.service_id_3          
WHEN @param_value_18 = 'service_id_4' THEN b.service_id_4          
WHEN @param_value_18 = 'service_id_5' THEN b.service_id_5          
ELSE b.service_id_2          
END,          
inv_org_name = CASE           
WHEN @param_value_18 = 'service_id_1' THEN b.service_name_1          
WHEN @param_value_18 = 'service_id_2' THEN b.service_name_2          
WHEN @param_value_18 = 'service_id_3' THEN b.service_name_3          
WHEN @param_value_18 = 'service_id_4' THEN b.service_name_4          
WHEN @param_value_18 = 'service_id_5' THEN b.service_name_5          
ELSE b.service_name_2          
END          
FROM #temp_table_1 a          
JOIN #service_data b ON a.fk_project_code = b.pk_project_code           
WHERE @param_value_18 like 'service%'          
          
          
UPDATE a          
SET a.inv_fk_org_id = CASE          
WHEN @param_value_18 = 'service_id_1' THEN b.service_id_1          
WHEN @param_value_18 = 'service_id_2' THEN b.service_id_2          
WHEN @param_value_18 = 'service_id_3' THEN b.service_id_3          
WHEN @param_value_18 = 'service_id_4' THEN b.service_id_4          
WHEN @param_value_18 = 'service_id_5' THEN b.service_id_5          
ELSE b.service_id_2          
END,          
inv_org_name = CASE           
WHEN @param_value_18 = 'service_id_1' THEN b.service_name_1          
WHEN @param_value_18 = 'service_id_2' THEN b.service_name_2          
WHEN @param_value_18 = 'service_id_3' THEN b.service_name_3          
WHEN @param_value_18 = 'service_id_4' THEN b.service_name_4          
WHEN @param_value_18 = 'service_id_5' THEN b.service_name_5          
ELSE b.service_name_2          
END          
FROM #temp_table_1 a          
JOIN #service_data b ON a.fk_function_code = b.fk_function_code          
WHERE @param_value_18 like 'service%' AND a.inv_fk_org_id is null          
          
          
UPDATE a          
SET a.inv_fk_org_id = 'ZZ',          
inv_org_name = 'Oppsett mangler'          
FROM #temp_table_1 a WHERE a.inv_fk_org_id is null          
          
          
RAISERROR ('FINISH: Fetch data from investments', 0, 1) WITH NOWAIT          
PRINT convert(nvarchar(19),SYSDATETIME())          
          
          
INSERT INTO #temp_table_2 (fk_tenant_id,year, fk_account_code, fk_department_code, fk_function_code,           
fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4, actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,          
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,          
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount, finplan_year_5_amount, finplan_year_6_amount,          
finplan_year_7_amount, finplan_year_8_amount, finplan_year_9_amount, finplan_year_10_amount, forecast_amount, org_budget_flag,          
fk_adjustment_code, fk_change_id)          
SELECT fk_tenant_id, @year, fk_account_code, fk_department_code, fk_function_code,           
fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4,          
sum(actual_amt_year),sum(actual_amt_last_year),SUM(actual_amt_year_minus2),          
sum(org_bud_amt_year), sum(org_bud_amt_last_year), sum(revised_bud_amt_year),sum(revised_bud_amt_last_year),          
sum(finplan_year_1_amount), sum(finplan_year_2_amount), sum(finplan_year_3_amount), sum(finplan_year_4_amount), sum(finplan_year_5_amount),          
sum(finplan_year_6_amount), sum(finplan_year_7_amount), sum(finplan_year_8_amount), sum(finplan_year_9_amount), sum(finplan_year_10_amount), sum(forecast_amount),          
org_budget_flag,fk_adjustment_code, fk_change_id          
FROM  #temp_table_1           
GROUP BY fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, org_budget_flag,         
free_dim_1, free_dim_2, free_dim_3, free_dim_4,fk_project_code,fk_adjustment_code, fk_change_id;          
          
          
-------------------------------------------------- INSERT LOGIC STARTS HERE FOR INVESTMENT 2A (tps_fin_inv_propopsal) ----------------------------------------------------------------          
          
    SELECT           
    a.fk_tenant_id,year as [Year],fk_account_code,  sum(actual_amt_year_minus2)actual_amt_year_minus2, sum(org_bud_amt_last_year)org_bud_amt_last_year,           
    sum(finplan_year_1_amount)year_1_amount, sum(finplan_year_2_amount)year_2_amount, sum(finplan_year_3_amount)year_3_amount, sum(finplan_year_4_amount)year_4_amount, sum(finplan_year_5_amount)year_5_amount,          
    sum(finplan_year_6_amount)year_6_amount, sum(finplan_year_7_amount)year_7_amount, sum(finplan_year_8_amount)year_8_amount, sum(finplan_year_9_amount)year_9_amount, sum(finplan_year_10_amount)year_10_amount,          
 rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item into #tps_fin_inv_propopsal2A          
    FROM #temp_table_2 a             
    JOIN #valid_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id --AND a.year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)          
    JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id not in ('1010','1020','1030','1040')          
    GROUP BY fk_tenant_id,year,rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item,fk_account_code ;          
            
    INSERT tps_fin_inv_propopsal          
    (fk_tenant_id,fk_proposal_id,budget_year,org_id,org_name,line_group_id,line_group,line_item_id,line_item,fk_investment_id,investment_name,year_1_amount,year_2_amount,          
    year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,gl_amount,budget_amount,          
 change_1_amount,change_2_amount,change_3_amount,change_4_amount,change_5_amount,change_6_amount,change_7_amount,change_8_amount,change_9_amount,change_10_amount,          
    updated,updated_by,status,comments,new_investment_description,isEditable,fk_fdv_codes,year_1_pct,year_2_pct,year_3_pct,year_4_pct,year_5_pct,year_6_pct,year_7_pct,year_8_pct,year_9_pct,year_10_pct,account_code)           
    SELECT fk_tenant_id,@proposal_id,Year,0,0,line_group_id,line_group,line_item_id,line_item,0,0,          
    sum(year_1_amount)year_1_amount,sum(year_2_amount)year_2_amount,sum(year_3_amount)year_3_amount,sum(year_4_amount)year_4_amount,sum(year_5_amount)year_5_amount,sum(year_6_amount)year_6_amount,          
 sum(year_7_amount)year_7_amount,sum(year_8_amount)year_8_amount,sum(year_9_amount)year_9_amount,sum(year_10_amount)year_10_amount,sum(actual_amt_year_minus2)gl_amount,sum(org_bud_amt_last_year)budget_amount          
    ,0,0,0,0,0,0,0,0,0,0,getutcdate(),@user_id,1,'' as comments,'',           
    CASE WHEN line_item_id = 550 Then 1          
     WHEN line_item_id = 610 Then 1          
     WHEN line_item_id = 620 Then 1          
     WHEN line_item_id = 730 Then 1          
     ELSE 0 END,'',0,0,0,0,0,0,0,0,0,0,fk_account_code   from #tps_fin_inv_propopsal2A          
    group by fk_tenant_id,Year, fk_account_code,line_group_id,line_group,          
    line_item_id,line_item          
          
          
 -- LOGIC TO INSERT RECORDS WITH LINE GROUP/ITEMS WHICH DOESN'T HAVE TRANSACTIONS STARTS HERE  --          
          
 SELECT distinct rl.line_group_id, rl.line_group, rl.line_item , rl.line_item_id , ac.pk_account_code          
 into #linegroupSet from  #valid_accounts ac            
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id not in ('1010','1020','1030','1040')          
           
 INSERT tps_fin_inv_propopsal          
    (fk_tenant_id,fk_proposal_id,budget_year,org_id,org_name,line_group_id,line_group,line_item_id,line_item,fk_investment_id,investment_name,year_1_amount,year_2_amount,          
    year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,gl_amount,budget_amount,          
 change_1_amount,change_2_amount,change_3_amount,change_4_amount,change_5_amount,change_6_amount,change_7_amount,change_8_amount,change_9_amount,change_10_amount,          
    updated,updated_by,status,comments,new_investment_description,isEditable,fk_fdv_codes,year_1_pct,year_2_pct,year_3_pct,year_4_pct,year_5_pct,year_6_pct,year_7_pct,year_8_pct,year_9_pct,year_10_pct,account_code)           
 SELECT distinct @tenant_id,@proposal_id,@budget_year,0,0,rl.line_group_id,rl.line_group,rl.line_item_id,rl.line_item,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,getutcdate(),@user_id,1,'' as comments,'',           
    CASE WHEN rl.line_item_id = 550 Then 1          
     WHEN rl.line_item_id = 610 Then 1          
     WHEN rl.line_item_id = 620 Then 1          
     WHEN rl.line_item_id = 730 Then 1          
     ELSE 0 END,'',0,0,0,0,0,0,0,0,0,0,accountCode = (select min(pk_account_code) from #linegroupSet a           
 where a.line_group_id = rl.line_group_id and a.line_item_id = rl.line_item_id )          
 from #linegroupSet rl           
 left join #tps_fin_inv_propopsal2A invdata  on  rl.line_group_id  =invdata.line_group_id    and   rl.line_item_id = invdata.line_item_id           
 where invdata.line_group_id  IS NULL           
            
 DROP TABLE #linegroupSet          
          
 -- LOGIC TO INSERT RECORDS WITH LINE GROUP/ITEMS WHICH DOESN'T HAVE TRANSACTIONS ENDS HERE --          
          
    DROP TABLE #tps_fin_inv_propopsal2A           
          
-------------------------------------------------- INSERT LOGIC ENDS HERE FOR INVESTMENT 2A (tps_fin_inv_propopsal)  ----------------------------------------------------------------          
          
DROP TABLE #service_data          
DROP TABLE #org_data          
DROP TABLE #temp_table_1          
DROP TABLE #valid_accounts          
DROP TABLE #valid_mainprojects          
DROP TABLE #temp_table_2          
DROP TABLE #inv_hlptab          
DROP TABLE #inv_hlptab2          
DROP TABLE #inv_hlptab3            
DROP TABLE #inv_hlptab_prev_year          
          
RETURN 0 
