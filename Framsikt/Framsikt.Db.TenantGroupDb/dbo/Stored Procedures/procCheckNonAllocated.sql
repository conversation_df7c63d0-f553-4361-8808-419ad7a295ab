CREATE OR ALTER PROCEDURE [dbo].[procC<PERSON><PERSON><PERSON>on<PERSON>llocated]
	@tenant_id int = 0, 
	@budget_year INT = 0,
	@debug_details INT = 0

AS


IF @budget_year = 0 
BEGIN
	SET @budget_year = (SELECT DATEPART( YEAR, GETDATE())+1)
END

DECLARE @tenant_table AS table (
	    [fk_tenant_id] INT NOT NULL)


CREATE TABLE #temp_budget_check
(
	[pk_id] INT NOT NULL  IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fp_level_1_value] NVARCHAR(25) NOT NULL, 
    [fp_level_2_value] NVARCHAR(25) NOT NULL,  
	[action_type] INT NOT NULL,
    [year_1_finplan] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_2_finplan] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_3_finplan] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_4_finplan] DECIMAL(18, 2) DEFAULT 0 NOT NULL,
	[year_1_limit] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_2_limit] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_3_limit] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_4_limit] DECIMAL(18, 2) DEFAULT 0 NOT NULL,
	[year_1_notallocated] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_2_notallocated] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_3_notallocated] DECIMAL(18, 2) DEFAULT 0 NOT NULL, 
    [year_4_notallocated] DECIMAL(18, 2) DEFAULT 0 NOT NULL
)


CREATE TABLE #temp_budget_limits
(
	[pk_id] INT NOT NULL  IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fp_level_1_value] NVARCHAR(25) NOT NULL, 
    [fp_level_2_value] NVARCHAR(25) NOT NULL,  
	[action_type] INT NOT NULL,
	[year_1_limit] DECIMAL(18, 2) NOT NULL, 
    [year_2_limit] DECIMAL(18, 2) NOT NULL, 
    [year_3_limit] DECIMAL(18, 2) NOT NULL, 
    [year_4_limit] DECIMAL(18, 2) NOT NULL
)

CREATE UNIQUE INDEX #IND_temp_budget_limits_1 ON #temp_budget_limits (action_type, [fp_level_1_value], [fp_level_2_value], budget_year, [fk_tenant_id]);

IF @tenant_id != 0
BEGIN 
	INSERT INTO @tenant_table (fk_tenant_id)
	VALUES (@tenant_id)
END
ELSE
BEGIN
	INSERT INTO @tenant_table (fk_tenant_id)
	SELECT DISTINCT fk_tenant_id FROM tco_module_mapping WHERE fk_module_id = 3
	AND fk_tenant_id NOT IN 
	(SELECT fk_tenant_id FROM tco_application_flag WHERE fk_tenant_id = @tenant_id and budget_year = @budget_year and flag_name = 'Dont_Update_Non_Allocated_Actions' and flag_status = 1)
END

INSERT INTO #temp_budget_check (fk_tenant_id,budget_year,action_type,year_1_notallocated, year_2_notallocated, year_3_notallocated,year_4_notallocated,
fp_level_1_value,fp_level_2_value) 
SELECT a.fk_tenant_id, b.budget_year, a.action_type,
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount, 
fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END 
FROM tfp_trans_header a 
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and b.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
JOIN @tenant_table t on a.fk_tenant_id = t.fk_tenant_id
WHERE b.budget_year = @budget_year
AND a.action_type IN (90, 20, 30, 40)


INSERT INTO #temp_budget_check (fk_tenant_id,budget_year,action_type,year_1_limit,year_2_limit,year_3_limit,year_4_limit,fp_level_1_value,fp_level_2_value)
SELECT l.fk_tenant_id,l.budget_year,l.action_type,l.year_1_limit,l.year_2_limit,l.year_3_limit,l.year_4_limit,l.fp_level_1_value,l.fp_level_2_value
FROM tfp_budget_limits l
JOIN @tenant_table t ON l.fk_tenant_id = t.fk_tenant_id
WHERE l.budget_year = @budget_year;


INSERT INTO #temp_budget_check (fk_tenant_id,budget_year,action_type,year_1_finplan,year_2_finplan,year_3_finplan,year_4_finplan,fp_level_1_value,fp_level_2_value) 
SELECT  S.fk_tenant_id, S.budget_year, S.action_type, SUM(year_1_amount) as year_1_finplan, SUM(year_2_amount) as year_2_finplan, 
SUM(year_3_amount) as year_3_finplan, SUM(year_4_amount) as year_4_finplan,S.fp_level_1_value,S.fp_level_2_value
FROM 
(SELECT a.fk_tenant_id, b.budget_year, action_type = 
    CASE WHEN action_type = 9 THEN 90
        WHEN action_type = 21 THEN 20
        WHEN action_type = 31 THEN 30
        WHEN action_type = 41 THEN 40
        end,
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount, 
fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END 
FROM tfp_trans_header a 
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and b.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
JOIN @tenant_table t on a.fk_tenant_id = t.fk_tenant_id
WHERE b.budget_year = @budget_year
AND a.action_type IN (9, 21, 31, 41)) S

GROUP BY 
S.fk_tenant_id, S.budget_year, S.action_type, S.fp_level_1_value,S.fp_level_2_value;


--UPDATE #temp_budget_check SET year_1_limit = b.year_1_limit, year_2_limit = b.year_2_limit, year_3_limit = b.year_3_limit, year_4_limit = b.year_4_limit
--FROM #temp_budget_check a, #temp_budget_limits b
--WHERE a.fk_tenant_id = b.fk_tenant_id
--AND a.fp_level_1_value = b.fp_level_1_value
--AND a.fp_level_2_value = b.fp_level_2_value
--AND a.action_type = b.action_type
--AND a.budget_year = b.budget_year


--INSERT INTO #temp_budget_check (fk_tenant_id,budget_year,action_type,year_1_finplan,year_2_finplan,year_3_finplan,year_4_finplan,fp_level_1_value,fp_level_2_value,
--year_1_limit, year_2_limit, year_3_limit, year_4_limit) 
--SELECT b.fk_tenant_id,b.budget_year,b.action_type,0 as year_1_finplan,0 as year_2_finplan,0 as year_3_finplan,0 as year_4_finplan,
--b.fp_level_1_value,b.fp_level_2_value,
--b.year_1_limit, b.year_2_limit, b.year_3_limit, b.year_4_limit 
--FROM #temp_budget_limits b
--WHERE NOT EXISTS (SELECT * FROM #temp_budget_check a
--WHERE a.fk_tenant_id = b.fk_tenant_id
--AND a.fp_level_1_value = b.fp_level_1_value
--AND a.fp_level_2_value = b.fp_level_2_value
--AND a.action_type = b.action_type
--AND a.budget_year = b.budget_year
--)

IF @debug_details = 1 
BEGIN

SELECT fk_tenant_id,budget_year,fp_level_1_value,fp_level_2_value,action_type,
SUM(year_1_finplan),
SUM(year_2_finplan),
SUM(year_3_finplan),
SUM(year_4_finplan),
SUM(year_1_limit),
SUM(year_2_limit),
SUM(year_3_limit),
SUM(year_4_limit),
SUM(year_1_notallocated),
SUM(year_2_notallocated),
SUM(year_3_notallocated),
SUM(year_4_notallocated),
year_1_discrepency = SUM(year_1_finplan)- SUM(year_1_limit)+SUM(year_1_notallocated),
year_2_discrepency = SUM(year_2_finplan)- SUM(year_2_limit)+SUM(year_2_notallocated),
year_3_discrepency = SUM(year_3_finplan)- SUM(year_3_limit)+SUM(year_3_notallocated),
year_4_discrepency = SUM(year_4_finplan)- SUM(year_4_limit)+SUM(year_4_notallocated)
FROM #temp_budget_check 
GROUP BY fk_tenant_id,budget_year,fp_level_1_value,fp_level_2_value,action_type
HAVING SUM(year_1_finplan+year_2_finplan+year_3_finplan+year_4_finplan)-SUM(year_1_limit+year_2_limit+year_3_limit+year_4_limit)+ SUM(year_1_notallocated+year_2_notallocated+year_3_notallocated+year_4_notallocated) 
 != 0;

END

INSERT INTO tco_error_in_not_allocated (fk_tenant_id,budget_year,fp_level_1_value,fp_level_2_value,
action_type,year_1_discrepency,year_2_discrepency,year_3_discrepency,year_4_discrepency, updated)
SELECT fk_tenant_id,budget_year,fp_level_1_value,fp_level_2_value,action_type,
year_1_discrepency = SUM(year_1_finplan)- SUM(year_1_limit)+SUM(year_1_notallocated),
year_2_discrepency = SUM(year_2_finplan)- SUM(year_2_limit)+SUM(year_2_notallocated),
year_3_discrepency = SUM(year_3_finplan)- SUM(year_3_limit)+SUM(year_3_notallocated),
year_4_discrepency = SUM(year_4_finplan)- SUM(year_4_limit)+SUM(year_4_notallocated), getdate()
FROM #temp_budget_check 
GROUP BY fk_tenant_id,budget_year,fp_level_1_value,fp_level_2_value,action_type
HAVING SUM(year_1_finplan+year_2_finplan+year_3_finplan+year_4_finplan)-SUM(year_1_limit+year_2_limit+year_3_limit+year_4_limit)+ SUM(year_1_notallocated+year_2_notallocated+year_3_notallocated+year_4_notallocated) 
 != 0;

DROP TABLE #temp_budget_check;
DROP TABLE #temp_budget_limits;

RETURN 0

GO
