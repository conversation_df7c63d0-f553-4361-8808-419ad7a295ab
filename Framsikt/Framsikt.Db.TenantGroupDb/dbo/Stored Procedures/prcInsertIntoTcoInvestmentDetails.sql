CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTcoInvestmentDetails]
	(
		@udtTcoInvestmentDetail udtTcoInvestmentDetail readonly
    )
AS
BEGIN
	insert into tco_investment_detail 
	(
			[fk_investment_id] ,
			[fk_tenant_id] ,
			[description],
			[fk_account_code],
			[fk_department_code],
			[fk_function_code],
			[vat_rate],
			[vat_refund],
			[year_1_amount],
			[year_2_amount],
			[year_3_amount],
			[year_4_amount],
			[year_5_amount],
			[year_6_amount],
			[year_7_amount],
			[year_8_amount],
			[year_9_amount],
			[year_10_amount],
			[type] ,
			[updated],
			[updated_by],
			[existing_flag],
			[fk_project_code],
			[free_dim_1],
			[free_dim_2],
			[free_dim_3] ,
			[free_dim_4] ,
			[budget_year] ,
			[fk_alter_code] ,
			[fk_adjustment_code] ,
			[fk_change_id],
			[fk_program_code],
			[approved_cost]
	)
	select 			
	
			[fk_investment_id] ,
			[fk_tenant_id] ,
			[description],
			[fk_account_code],
			[fk_department_code],
			[fk_function_code],
			[vat_rate],
			[vat_refund],
			[year_1_amount],
			[year_2_amount],
			[year_3_amount],
			[year_4_amount],
			[year_5_amount],
			[year_6_amount],
			[year_7_amount],
			[year_8_amount],
			[year_9_amount],
			[year_10_amount],
			[type] ,
			[updated],
			[updated_by],
			[existing_flag],
			[fk_project_code],
			[free_dim_1],
			[free_dim_2],
			[free_dim_3] ,
			[free_dim_4] ,
			[budget_year] ,
			[fk_alter_code] ,
			[fk_adjustment_code] ,
			[fk_change_id],
			[inv_program],
			[approved_cost]
	from @udtTcoInvestmentDetail
End

