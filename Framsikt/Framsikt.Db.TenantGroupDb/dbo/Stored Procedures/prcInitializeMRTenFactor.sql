


CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRTenFactor]
	@fk_tenant_id int,
	@forecast_period int,
	@fk_user_id int
AS

DECLARE @municipality_id varchar(25)
DECLARE @budget_year INT
DECLARE @org_version varchar(25)
DECLARE @org_level INT

SET @municipality_id = (SELECT municipality_id FROM gco_tenants WHERE pk_id = @fk_tenant_id)
SET @budget_year = @forecast_period/100
SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
SET @org_level = (SELECT MAX(org_level) FROM tco_org_level WHERE fk_org_version = @org_version and fk_tenant_id = @fk_tenant_id)



CREATE TABLE #hlptab_data_1
(
    [fk_tenant_id] INT NOT NULL,
    [fk_department_code] NVARCHAR(25) NOT NULL,
    [budget_year] INT NOT NULL,
    [fk_factor_id] UNIQUEIDENTIFIER NOT NULL,
    [fk_question_id] UNIQUEIDENTIFIER NOT NULL,
    [org_id_1] VARCHAR(25) NOT NULL,
    [org_id_2] VARCHAR(25) NOT NULL,
    [org_id_3] VARCHAR(25) NOT NULL,
    [org_id_4] VARCHAR(25) NOT NULL,
    [org_id_5] VARCHAR(25) NOT NULL,
    [org_id_6] VARCHAR(25) NULL,
    [org_id_7] VARCHAR(25) NULL,
    [org_id_8] VARCHAR(25) NULL,
    [result] DECIMAL(18, 2) NOT NULL,
    [counter] INT NOT NULL DEFAULT (0)
)

 
CREATE TABLE #hlptab_data_2
(
                [fk_factor_id] UNIQUEIDENTIFIER NOT NULL,
                [fk_question_id] UNIQUEIDENTIFIER NOT NULL,
                [number_of_questions] INT NOT NULL
)



 
CREATE TABLE #hlp_department_check
(
                [budget_year] INT NOT NULL,
                [fk_factor_id] UNIQUEIDENTIFIER NOT NULL,
                [fk_question_id] UNIQUEIDENTIFIER NOT NULL,
                [fk_department_code] NVARCHAR(25) NOT NULL,
                [counter] DECIMAL(18,10) NOT NULL
)



CREATE TABLE #hlp_department_check_org
(
                [org_level] int not null,
                [org_id] NVARCHAR (24) NOT NULL,
                [budget_year] INT NOT NULL,
                [fk_factor_id] UNIQUEIDENTIFIER NOT NULL,
                [fk_question_id] UNIQUEIDENTIFIER NOT NULL,
                [counter] DECIMAL(18,10) NOT NULL
)



CREATE TABLE #hlptab_ten_factor_warehouse
(
    [fk_tenant_id] INT NOT NULL,
    [forecast_period] INT NOT NULL,
    [fk_factor_id] UNIQUEIDENTIFIER NOT NULL,
    [org_level] INT NOT NULL,
    [org_id] NVARCHAR(25) NOT NULL,
    [result] DECIMAL(18, 2) NOT NULL,
    [average_org_level] DECIMAL(18, 2) NOT NULL,
    [average_municipality] DECIMAL(18, 2) NOT NULL,
    [average_country] DECIMAL(18, 2) NOT NULL,
    [result_year_minus_one] DECIMAL(18, 2) NOT NULL,
    [result_year_minus_two] DECIMAL(18, 2) NOT NULL
)

CREATE TABLE #hlptab_ten_factor_warehouse_2
(
    [fk_tenant_id] INT NOT NULL,
    [forecast_period] INT NOT NULL,
    [fk_factor_id] UNIQUEIDENTIFIER NOT NULL,
    [org_level] INT NOT NULL,
    [org_id] NVARCHAR(25) NOT NULL,
    [result] DECIMAL(18, 2) NOT NULL,
    [average_org_level] DECIMAL(18, 2) NOT NULL,
    [average_municipality] DECIMAL(18, 2) NOT NULL,
    [average_country] DECIMAL(18, 2) NOT NULL,
    [result_year_minus_one] DECIMAL(18, 2) NOT NULL,
    [result_year_minus_two] DECIMAL(18, 2) NOT NULL
)

INSERT INTO #hlptab_data_1 (fk_tenant_id, fk_department_code, budget_year, fk_factor_id,fk_question_id,
org_id_1,org_id_2, org_id_3, org_id_4, org_id_5, org_id_6, org_id_7, org_id_8,
result, counter)
SELECT oh.fk_tenant_id, d.fk_department_code, d.budget_year, q.fk_factor_id,d.fk_question_id,
oh.org_id_1,oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5, oh.org_id_6, oh.org_id_7, oh.org_id_8,
d.result, counter = 0
FROM gco_ten_factor_data d
JOIN tco_org_hierarchy oh ON d.fk_department_code = oh.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
JOIN gco_ten_factor_questions q ON d.fk_question_id = q.pk_question_id
WHERE fk_municipality_id = @municipality_id AND budget_year IN (@budget_year-2, @budget_year-1, @budget_year)

 
INSERT INTO #hlptab_data_2 (fk_factor_id, fk_question_id, number_of_questions)
SELECT fk_factor_id, MIN(pk_question_id),COUNT(*)
FROM gco_ten_factor_questions
GROUP BY fk_factor_id

-- Henter først ut antall per år, spørsmål, faktor og ansvar

INSERT INTO #hlp_department_check (budget_year, fk_factor_id, fk_question_id, fk_department_code, counter)
SELECT b.budget_year, a.fk_factor_id, a.fk_question_id, b.fk_department_code, COUNT(*)
FROM #hlptab_data_2 a
JOIN #hlptab_data_1 b ON a.fk_question_id = b.fk_question_id
GROUP BY b.budget_year, a.fk_factor_id, a.fk_question_id, b.fk_department_code

-- Lager så en hjelpetabell som inneholder antall per org_id, orgnivå, år, faktor og spørsmål.

INSERT INTO #hlp_department_check_org (org_level, org_id, budget_year, fk_factor_id, fk_question_id, counter)
select 1 as org_level,oh.org_id_1, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
GROUP BY oh.org_id_1, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 2 as org_level,oh.org_id_2, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=2
GROUP BY oh.org_id_2, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 3 as org_level,oh.org_id_3, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=3
GROUP BY oh.org_id_3, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 4 as org_level,oh.org_id_4, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=4
GROUP BY oh.org_id_4, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 5 as org_level,oh.org_id_5, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=5
GROUP BY oh.org_id_5, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 6 as org_level,oh.org_id_6, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=6
GROUP BY oh.org_id_6, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 7 as org_level,oh.org_id_7, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=7
GROUP BY oh.org_id_7, c.budget_year, c.fk_factor_id,c.fk_question_id
UNION ALL
select 8 as org_level,oh.org_id_8, c.budget_year, c.fk_factor_id,c.fk_question_id,  sum(c.counter) as counter
from #hlp_department_check c
JOIN tco_org_hierarchy oh ON oh.fk_department_code = c.fk_department_code AND oh.fk_tenant_id = @fk_tenant_id AND oh.fk_org_version = @org_version
WHERE @org_level>=8
GROUP BY oh.org_id_8, c.budget_year, c.fk_factor_id,c.fk_question_id

-- SLETTER DE ORG/spørsmål SOM HAR MER ENN 5 antall slik at tabellen bare står igjen med det som skal gjemmes

DELETE FROM #hlp_department_check_org WHERE counter > 5


-- Legger inn i en egen tabell de som skal gjemmes for å fjerne duplikater pga spørsmåls-id

SELECT DISTINCT org_level, org_id, budget_year, fk_factor_id INTO #hlp_org_hide
FROM #hlp_department_check_org


-- ORG LEVEL 1 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 1, org_id_1
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 1
GROUP BY fk_tenant_id, fk_factor_id, org_id_1

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 1, org_id_1
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 1
GROUP BY fk_tenant_id, fk_factor_id, org_id_1

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 1, org_id_1
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 1
GROUP BY fk_tenant_id, fk_factor_id, org_id_1

-- ORG LEVEL 2 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 2, org_id_2
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 2
GROUP BY fk_tenant_id, fk_factor_id, org_id_2

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 2, org_id_2
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 2
GROUP BY fk_tenant_id, fk_factor_id, org_id_2

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 2, org_id_2
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 2
GROUP BY fk_tenant_id, fk_factor_id, org_id_2

 
-- ORG LEVEL 3 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 3, org_id_3
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 3
GROUP BY fk_tenant_id, fk_factor_id, org_id_3

 
INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 3, org_id_3
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 3
GROUP BY fk_tenant_id, fk_factor_id, org_id_3

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 3, org_id_3
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 3
GROUP BY fk_tenant_id, fk_factor_id, org_id_3

 
-- ORG LEVEL 4 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 4, org_id_4
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 4
GROUP BY fk_tenant_id, fk_factor_id, org_id_4

 
INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 4, org_id_4
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 4
GROUP BY fk_tenant_id, fk_factor_id, org_id_4

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 4, org_id_4
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 4
GROUP BY fk_tenant_id, fk_factor_id, org_id_4

 
-- ORG LEVEL 5 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 5, org_id_5
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 5
GROUP BY fk_tenant_id, fk_factor_id, org_id_5

 
INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 5, org_id_5
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 5
GROUP BY fk_tenant_id, fk_factor_id, org_id_5

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 5, org_id_5
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 5
GROUP BY fk_tenant_id, fk_factor_id, org_id_5

 
-- ORG LEVEL 6 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 6, org_id_6
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 6
GROUP BY fk_tenant_id, fk_factor_id, org_id_6

 
INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 6, org_id_6
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 6
GROUP BY fk_tenant_id, fk_factor_id, org_id_6

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 6, org_id_6
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 6
GROUP BY fk_tenant_id, fk_factor_id, org_id_6

 
-- ORG LEVEL 7 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 7, org_id_7
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 7
GROUP BY fk_tenant_id, fk_factor_id, org_id_7

 
INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 7, org_id_7
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 7
GROUP BY fk_tenant_id, fk_factor_id, org_id_7

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 7, org_id_7
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 7
GROUP BY fk_tenant_id, fk_factor_id, org_id_7

 
-- ORG LEVEL 8 --

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 8, org_id_8
,sum(result)/count(*)
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year AND @org_level >= 8
GROUP BY fk_tenant_id, fk_factor_id, org_id_8

 
INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 8, org_id_8
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = sum(result)/count(*)
,result_year_minus_two = 0
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 1 AND @org_level >= 8
GROUP BY fk_tenant_id, fk_factor_id, org_id_8

INSERT INTO #hlptab_ten_factor_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, @forecast_period, fk_factor_id, org_level = 8, org_id_8
,result = 0
,average_org_level = 0
,average_municipality = 0
,average_country = 0
,result_year_minus_one = 0
,result_year_minus_two = sum(result)/count(*)
FROM #hlptab_data_1
WHERE budget_year = @budget_year - 2 AND @org_level >= 8
GROUP BY fk_tenant_id, fk_factor_id, org_id_8

--- Setter resultat lik 0 på de som har færre enn 5 svar

UPDATE w SET w.result = 0
FROM #hlptab_ten_factor_warehouse w
JOIN #hlp_org_hide h ON w.fk_factor_id = h.fk_factor_id AND h.org_level = w.org_level AND h.org_id = w.org_id AND h.budget_year = @budget_year

UPDATE w SET w.result_year_minus_one = 0
FROM #hlptab_ten_factor_warehouse w
JOIN #hlp_org_hide h ON w.fk_factor_id = h.fk_factor_id AND h.org_level = w.org_level AND h.org_id = w.org_id AND h.budget_year = @budget_year-1

UPDATE w SET w.result_year_minus_two = 0
FROM #hlptab_ten_factor_warehouse w
JOIN #hlp_org_hide h ON w.fk_factor_id = h.fk_factor_id AND h.org_level = w.org_level AND h.org_id = w.org_id AND h.budget_year = @budget_year-2

 
 
INSERT INTO #hlptab_ten_factor_warehouse_2 (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two)
SELECT fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, SUM(result), SUM(average_org_level), SUM(average_municipality), SUM(average_country),
SUM(result_year_minus_one), SUM(result_year_minus_two)
FROM #hlptab_ten_factor_warehouse
GROUP BY fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id

 
UPDATE a SET a.average_municipality = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 1

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 1 and a.org_level = 2
JOIN (SELECT DISTINCT fk_tenant_id, org_id_1, org_id_2 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_2 AND b.org_id = c.org_id_1 AND b.fk_tenant_id = c.fk_tenant_id

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 2 and a.org_level = 3
JOIN (SELECT DISTINCT fk_tenant_id, org_id_2, org_id_3 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_3 AND b.org_id = c.org_id_2 AND b.fk_tenant_id = c.fk_tenant_id

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 3 and a.org_level = 4
JOIN (SELECT DISTINCT fk_tenant_id, org_id_3, org_id_4 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_4 AND b.org_id = c.org_id_3 AND b.fk_tenant_id = c.fk_tenant_id

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 4 and a.org_level = 5
JOIN (SELECT DISTINCT fk_tenant_id, org_id_4, org_id_5 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_5 AND b.org_id = c.org_id_4 AND b.fk_tenant_id = c.fk_tenant_id

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 5 and a.org_level = 6
JOIN (SELECT DISTINCT fk_tenant_id, org_id_5, org_id_6 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_6 AND b.org_id = c.org_id_5 AND b.fk_tenant_id = c.fk_tenant_id

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 6 and a.org_level = 7
JOIN (SELECT DISTINCT fk_tenant_id, org_id_6, org_id_7 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_7 AND b.org_id = c.org_id_6 AND b.fk_tenant_id = c.fk_tenant_id

 
UPDATE a SET a.average_org_level = b.result
FROM #hlptab_ten_factor_warehouse_2 a
JOIN #hlptab_ten_factor_warehouse_2 b ON a.fk_tenant_id = b.fk_tenant_id AND a.forecast_period = b.forecast_period AND a.fk_factor_id = b.fk_factor_id AND b.org_level = 7 and a.org_level = 8
JOIN (SELECT DISTINCT fk_tenant_id, org_id_7, org_id_8 FROM tco_org_hierarchy WHERE fk_org_version = @org_version) c ON a.fk_tenant_id = c.fk_tenant_id AND a.org_id = c.org_id_8 AND b.org_id = c.org_id_7 AND b.fk_tenant_id = c.fk_tenant_id

 
DELETE FROM tmr_ten_factor_data_warehouse WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period

INSERT INTO tmr_ten_factor_data_warehouse (fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, result, average_org_level, average_municipality, average_country,
result_year_minus_one, result_year_minus_two, updated, updated_by)
SELECT fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id, SUM(result), SUM(average_org_level), SUM(average_municipality), SUM(average_country),
SUM(result_year_minus_one), SUM(result_year_minus_two), getdate(), @fk_user_id
FROM #hlptab_ten_factor_warehouse_2
GROUP BY fk_tenant_id, forecast_period,fk_factor_id, org_level, org_id

GO
