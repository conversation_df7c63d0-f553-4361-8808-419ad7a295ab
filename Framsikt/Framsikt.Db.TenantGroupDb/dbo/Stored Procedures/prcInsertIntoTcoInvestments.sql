CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTcoInvestments]
	(
		@udtTcoInvestments udtTcoInvestments readonly,
		@budget_year int
    )
AS

BEGIN
DECLARE c1 CURSOR READ_ONLY
FOR select caption_name from @udtTcoInvestments
OPEN c1
DECLARE @invName VARCHAR(500) 
FETCH NEXT FROM c1 INTO @invName  

WHILE @@FETCH_STATUS = 0
BEGIN
	insert into tco_investments 
	(
			[fk_tenant_id],
			[status],
			[self_cost_flag] ,
			[budget_year_established] ,
			[start_year] ,
			[completion_pct],
			[approval] ,
			[approval_date] ,
			[responsible],
			--[fk_org_id] ,
			--[org_name] ,
			[fk_project_id],
			[project_phase] ,
			[updated] ,
			[updated_by] ,
			[fk_fdv_codes] ,
			[unit_value] ,
			[previously_budgeted] ,
			[previously_financing] ,
			[monthrep_flag],
			[fk_main_project_code],
			[fk_portfolio_code],
			[tags],
			[goalId],
			[original_finish_year],
			[approval_ref_url]
	)
	select [fk_tenant_id],
			[status],
			[self_cost_flag] ,
			[budget_year_established] ,
			[start_year] ,
			[completion_pct ],
			[approval] ,
			[approval_date] ,
			[responsible],
			--[fk_org_id] ,
			--[org_name] ,
			[fk_project_id ],
			[project_phase] ,
			[updated] ,
			[updated_by] ,
			[fk_fdv_codes] ,
			[unit_value] ,
			[previously_budgeted] ,
			[previously_financing] ,
			[monthrep_flag] ,
			[fk_main_project_code],
			[fk_portfolio_code],
			[tags],
			[goalId],
			[original_finish_year],
			''
	from @udtTcoInvestments where caption_name=@invName

	DECLARE @pk_investment_id INT
	select @pk_investment_id= SCOPE_IDENTITY()

	insert into tco_inv_budgetyear_config(fk_tenant_id,fk_investment_id,budget_year,inv_status,priority,updated,updated_by,investment_name,completion_date,fk_org_id,org_name)
	select [fk_tenant_id],@pk_investment_id,@budget_year,0,0,[updated],[updated_by],[caption_name],[completion_date],[fk_org_id],[org_name]from @udtTcoInvestments where caption_name=@invName

FETCH NEXT FROM c1 INTO @invName  

END
CLOSE c1
DEALLOCATE c1

End

