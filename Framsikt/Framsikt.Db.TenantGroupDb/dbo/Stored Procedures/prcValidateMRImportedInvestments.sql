CREATE OR ALTER PROCEDURE [dbo].[prcValidateMRImportedInvestments]
	@tenant_id int,
	@user_id int,
	@budget_year int ,
	@forecast_period int ,
	@orgId varchar(10),
	@org_version NVARCHAR(50)
AS	 

	SELECT  investment_id,investment_name,program_code,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,
	internal_remarks,approvedCost,est_finish_quarter,status,risk,status_desc,user_id,tenant_id,forecast_period,sum(totalForecastChange)totalForecastChange ,sum(annualForecastChange)annualForecastChange,
	annualForecastChange_str,totalForecastChange_str
	into #temp   from tmr_stage_investmentforecast_import where tenant_id=@tenant_id AND [user_id] = @user_id AND forecast_period = @forecast_period
	group by investment_id,investment_name,program_code,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,
	internal_remarks,approvedCost,est_finish_quarter,status,risk,status_desc,user_id,tenant_id,forecast_period,annualForecastChange_str,totalForecastChange_str

	DELETE FROM tmr_stage_investmentforecast_import where tenant_id=@tenant_id AND [user_id] = @user_id AND forecast_period = @forecast_period

	INSERT tmr_stage_investmentforecast_import (investment_id,investment_name,program_code,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,
	internal_remarks,approvedCost,est_finish_quarter,status,risk,status_desc,user_id,tenant_id,forecast_period,totalForecastChange,annualForecastChange,annualForecastChange_str,totalForecastChange_str)
	SELECT  investment_id,investment_name,program_code,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,alter_code,
	internal_remarks,approvedCost,est_finish_quarter,status,risk,status_desc,user_id,tenant_id,forecast_period,totalForecastChange,annualForecastChange,annualForecastChange_str,totalForecastChange_str
	from #temp

	DROP TABLE #temp


	--Clear all the error information 
	UPDATE tmr_stage_investmentforecast_import
	SET investment_id_error=0,
	program_code_error=0,
	account_code_error=0,
	department_code_error=0,
	function_code_error=0,
	project_code_error=0,
	free_dim_1_error=0,
	free_dim_2_error=0,
	free_dim_3_error=0,
	free_dim_4_error=0,
	est_finish_quarter_error=0,
	status_error=0,
	statusDesc_error=0,
	risk_error=0,
	error_count=0,
	altercode_error=0,
	total_forecast_change_error = 0, 
	annual_forecast_change_error = 0
	WHERE tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.[user_id] = @user_id;

	DECLARE  @Risks Table( RiskID int)
	INSERT @Risks(RiskID)
	SELECT 1 UNION  SELECT 2 UNION SELECT 3 


	-- Validate Alter Code
	UPDATE tmr_stage_investmentforecast_import
	SET altercode_error = 1, error_count = error_count + 1   FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (select distinct fk_tenant_id, pk_alter_code,alter_description,[status] from tco_fp_alter_codes where fk_tenant_id =@tenant_id and [status]=1) acc 
	on acc.fk_tenant_id = tmr_stage_investmentforecast_import.tenant_id 
	AND   acc.pk_alter_code = tmr_stage_investmentforecast_import.alter_code
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.[user_id] = @user_id  AND acc.[pk_alter_code] is NULL AND (tmr_stage_investmentforecast_import.alter_code is null OR tmr_stage_investmentforecast_import.alter_code ='');

	-- Validate Status
	UPDATE tmr_stage_investmentforecast_import
	SET status_error = 1, error_count = error_count + 1 
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_progress_status acc on acc.fk_tenant_id = tmr_stage_investmentforecast_import.tenant_id    AND acc.status_id = tmr_stage_investmentforecast_import.[status]  AND acc.[type]='MONTHREP_INV'
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.[user_id] = @user_id AND acc.[status_id] is NULL AND tmr_stage_investmentforecast_import.[status] <> 0;

	-- Validate Investment Programs
	UPDATE tmr_stage_investmentforecast_import
	SET program_code_error = 1, error_count = error_count + 1 
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_inv_program acc on acc.fk_tenant_id = tmr_stage_investmentforecast_import.tenant_id   AND acc.pk_prog_code = tmr_stage_investmentforecast_import.[program_code]
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.[user_id] = @user_id AND acc.[pk_prog_code] is NULL;

	-- Validate RISKS
	UPDATE tmr_stage_investmentforecast_import
	SET risk_error = 1, error_count = error_count + 1 
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN @Risks acc on acc.RiskID = tmr_stage_investmentforecast_import.risk   
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.[user_id] = @user_id AND acc.[RiskID] is NULL;


	--Validate account codes--
	UPDATE tmr_stage_investmentforecast_import
	SET account_code_error = 1, error_count = error_count + 1 
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_accounts acc on acc.pk_account_code = tmr_stage_investmentforecast_import.account_code AND 
		tmr_stage_investmentforecast_import.tenant_id = acc.pk_tenant_id AND 
		tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.[user_id] = @user_id 
	WHERE acc.pk_account_code IS NULL;


	--Update inv_type from account codes 
	select distinct  a.pk_account_code,  b.line_item_id, Case When  b.line_item_id = 500 then 'i'  else 'f' end as invType into #tempAccounts from tco_accounts a
	JOIN gmd_reporting_line b on a.fk_kostra_account_code = b.fk_kostra_account_code and b.report = 'B2A'
	where a.pk_Tenant_id = @tenant_id

	UPDATE tmr_stage_investmentforecast_import
	SET inv_type = frdm.invType
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT   pk_account_code,invType  from #tempAccounts) frdm on frdm.pk_account_code = tmr_stage_investmentforecast_import.account_code  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.account_code_error = 0; 

	DROP TABLE #tempAccounts
	 

	--Validate department codes --

	

	IF (@orgId = '')
	BEGIN
		UPDATE tmr_stage_investmentforecast_import
		SET department_code_error = 1, error_count = error_count + 1
		FROM tmr_stage_investmentforecast_import 
		LEFT JOIN (select distinct fk_department_code as department_code from tco_org_hierarchy where fk_tenant_id = @tenant_id and  fk_org_version in (@org_version))
	    DEPTS ON DEPTS.department_code = tmr_stage_investmentforecast_import.department_code WHERE 
			tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
			tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
			tmr_stage_investmentforecast_import.[user_id] = @user_id 
		AND DEPTS.department_code IS NULL;
	END
	ELSE
	BEGIN
		select distinct fk_department_code from tco_org_hierarchy where fk_tenant_id = @tenant_id and  fk_org_version in (@org_version)
		and org_id_2 =@orgId

		UPDATE tmr_stage_investmentforecast_import
		SET department_code_error = 1, error_count = error_count + 1
		FROM tmr_stage_investmentforecast_import 
		LEFT JOIN (select distinct fk_department_code as department_code from tco_org_hierarchy where fk_tenant_id = @tenant_id and  fk_org_version in (@org_version)
		and org_id_2 =@orgId) DEPTS ON DEPTS.department_code = tmr_stage_investmentforecast_import.department_code WHERE 
			tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
			tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
			tmr_stage_investmentforecast_import.[user_id] = @user_id 
		AND DEPTS.department_code IS NULL;
	END 
 
	--Validate Functions--
	UPDATE tmr_stage_investmentforecast_import
	SET function_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_functions fn on fn.pk_Function_code = tmr_stage_investmentforecast_import.function_code AND 
		tmr_stage_investmentforecast_import.tenant_id = fn.pk_tenant_id AND 
		tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.[user_id] = @user_id 
	WHERE fn.pk_Function_code IS NULL;

	--Validate Projects--

	UPDATE tmr_stage_investmentforecast_import
	SET project_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_projects prj on prj.pk_project_code = tmr_stage_investmentforecast_import.project_code AND 
		tmr_stage_investmentforecast_import.tenant_id = prj.fk_tenant_id  
	WHERE prj.pk_project_code IS NULL
	AND tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.user_id = @user_id  
		AND tmr_stage_investmentforecast_import.project_code != '' 
		AND tmr_stage_investmentforecast_import.project_code IS NULL  


	-- Validate free dim 1 --
	UPDATE tmr_stage_investmentforecast_import
	SET free_dim_1_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_investmentforecast_import.free_dim_1 AND 
		tmr_stage_investmentforecast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.user_id = @user_id  
		AND tmr_stage_investmentforecast_import.free_dim_1 != '' 
		AND tmr_stage_investmentforecast_import.free_dim_1 IS NOT NULL;  

	-- Validate free dim 2 --
	UPDATE tmr_stage_investmentforecast_import
	SET free_dim_2_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_investmentforecast_import.free_dim_2 AND 
		tmr_stage_investmentforecast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.user_id = @user_id   
		AND tmr_stage_investmentforecast_import.free_dim_2 != '' 
		AND tmr_stage_investmentforecast_import.free_dim_2 IS NOT NULL;  

	-- Validate free dim 3 --
	UPDATE tmr_stage_investmentforecast_import
	SET free_dim_3_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_investmentforecast_import.free_dim_3 AND 
		tmr_stage_investmentforecast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.user_id = @user_id   
		AND tmr_stage_investmentforecast_import.free_dim_3 != '' 
		AND tmr_stage_investmentforecast_import.free_dim_3 IS NOT NULL;  

	-- Validate free dim 4 --
	UPDATE tmr_stage_investmentforecast_import
	SET free_dim_4_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_investmentforecast_import.free_dim_4 AND 
		tmr_stage_investmentforecast_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
		tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
		tmr_stage_investmentforecast_import.user_id = @user_id   
		AND tmr_stage_investmentforecast_import.free_dim_4 != '' 
		AND tmr_stage_investmentforecast_import.free_dim_4 IS NOT NULL; 

	-- Insert missing investments in budget_year_config (hotfix - BUG 49291)

	DROP TABLE IF EXISTS #missing_budget_year_config
	DROP TABLE IF EXISTS #last_budget_year

	SELECT DISTINCT tc.fk_tenant_id,tc.fk_investment_id,max(tc.budget_year) as budget_year
	into #last_budget_year from tco_investments i
	LEFT join tco_inv_budgetyear_config tc on i.pk_investment_id=tc.fk_investment_id and i.fk_tenant_id=tc.fk_tenant_id
	JOIN tmr_stage_investmentforecast_import imp ON imp.investment_id = i.pk_investment_id AND imp.tenant_id = i.fk_tenant_id AND imp.tenant_id = @tenant_id AND 	imp.forecast_period = @forecast_period AND 	imp.[user_id] = @user_id 
	WHERE i.fk_tenant_id =@tenant_id and  tc.budget_year < @budget_year 
	GROUP BY tc.fk_tenant_id,tc.fk_investment_id


	SELECT DISTINCT tc.fk_tenant_id,tc.fk_investment_id,@budget_year as budget_year,tc.inv_status,tc.priority,getdate() as updated,@user_id as updated_by,tc.investment_name,tc.completion_date,tc.fk_investment_phase_id,tc.fk_org_id,tc.org_name 
	into #missing_budget_year_config from tco_investments i
	JOIN tco_inv_budgetyear_config tc on i.pk_investment_id=tc.fk_investment_id and i.fk_tenant_id=tc.fk_tenant_id
	JOIN #last_budget_year ly ON ly.fk_investment_id = i.pk_investment_id AND ly.fk_tenant_id = i.fk_tenant_id AND ly.budget_year = tc.budget_year

	DELETE tmp FROM #missing_budget_year_config tmp
	JOIN tco_inv_budgetyear_config tc ON tmp.budget_year = tc.budget_year AND tmp.fk_investment_id = tc.fk_investment_id AND tmp.fk_tenant_id = tc.fk_tenant_id
	
	INSERT INTO tco_inv_budgetyear_config (fk_tenant_id,fk_investment_id,budget_year,inv_status,priority,updated,updated_by,investment_name,completion_date,fk_investment_phase_id,fk_org_id,org_name)
	SELECT fk_tenant_id,fk_investment_id,budget_year,inv_status,priority,updated,updated_by,investment_name,completion_date,fk_investment_phase_id,fk_org_id,org_name
	FROM #missing_budget_year_config tmp

	-- Validate InvestmentID's 

	SELECT DISTINCT i.pk_investment_id,tc.investment_name into #masterInvestments from tco_investments i
	LEFT join tco_inv_budgetyear_config tc on i.pk_investment_id=tc.fk_investment_id and i.fk_tenant_id=tc.fk_tenant_id
	WHERE i.fk_tenant_id =@tenant_id and  tc.budget_year =@budget_year 

	UPDATE tmr_stage_investmentforecast_import
	SET investment_id_error = 1, error_count = error_count + 1
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.investment_id  = '' 
	AND tmr_stage_investmentforecast_import.investment_id IS NULL; 

	UPDATE tmr_stage_investmentforecast_import
	SET investment_id_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT DISTINCT pk_investment_id from #masterInvestments ) frdm on frdm.pk_investment_id = tmr_stage_investmentforecast_import.investment_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id AND frdm.pk_investment_id is NULL;

	UPDATE tmr_stage_investmentforecast_import
	SET investment_name = frdm.investment_name
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT   pk_investment_id,investment_name  from #masterInvestments) frdm on frdm.pk_investment_id = tmr_stage_investmentforecast_import.investment_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.investment_id != '' 
	AND tmr_stage_investmentforecast_import.investment_id IS NOT NULL; 

	DROP TABLE #masterInvestments;
	 

	 -- Validate Investment Programs

	 UPDATE tmr_stage_investmentforecast_import
	SET program_code_error = 1, error_count = error_count + 1
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.program_code  = '' 
	AND tmr_stage_investmentforecast_import.program_code IS NULL; 

	UPDATE tmr_stage_investmentforecast_import
	SET program_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN tco_inv_program frdm on frdm.pk_prog_code = tmr_stage_investmentforecast_import.program_code   AND frdm.fk_tenant_id = tmr_stage_investmentforecast_import.tenant_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND  frdm.[status] =1 AND
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.program_code != '' 
	AND tmr_stage_investmentforecast_import.program_code IS NULL; 


	-- Validate annual and total forecast

	UPDATE tmr_stage_investmentforecast_import
	SET totalForecastChange = 0 
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.totalForecastChange  = '' 
	AND tmr_stage_investmentforecast_import.totalForecastChange IS NULL; 

	UPDATE tmr_stage_investmentforecast_import
	SET annualForecastChange = 0 
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND tmr_stage_investmentforecast_import.annualForecastChange  = '' 
	AND tmr_stage_investmentforecast_import.annualForecastChange IS NULL; 
	 

	 -- Validate Estimated finished date

	SELECT  investment_id,Count(Distinct est_finish_quarter) estimatedquarter  into #estimatedquarter  from tmr_stage_investmentforecast_import WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id  AND tmr_stage_investmentforecast_import.est_finish_quarter <> '' AND tmr_stage_investmentforecast_import.est_finish_quarter IS NOT NULL
	GROUP by investment_id
	having Count(Distinct est_finish_quarter) > 1


	UPDATE tmr_stage_investmentforecast_import
	SET est_finish_quarter_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT DISTINCT investment_id from #estimatedquarter ) frdm on frdm.investment_id = tmr_stage_investmentforecast_import.investment_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id	AND frdm.investment_id IS NOT NULL; 

	DROP TABLE #estimatedquarter
	
	SET DATEFORMAT dmy
	UPDATE tmr_stage_investmentforecast_import set error_count = 
	CASE WHEN ISDATE(est_finish_quarter) = 0 THEN error_count +1
			WHEN ISNUMERIC(est_finish_quarter) = 1 THEN error_count +1
			--WHEN datepart(year,est_finish_quarter) < start_year THEN error_count+ 1
			ELSE error_count + 0
		END, est_finish_quarter_error = 
		CASE WHEN ISDATE(est_finish_quarter) = 0 THEN 1
			WHEN ISNUMERIC(est_finish_quarter) = 1 THEN 1
			--WHEN datepart(year,est_finish_quarter) < start_year THEN 1
			ELSE 0
			END
		WHERE tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
			tmr_stage_investmentforecast_import.user_id = @user_id AND
			tmr_stage_investmentforecast_import.forecast_period =@forecast_period

	 -- Validate Status

	SELECT  investment_id,Count(Distinct [status]) [status] into #Status  from tmr_stage_investmentforecast_import WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id  
	GROUP by investment_id
	having Count(Distinct [status]) > 1


	UPDATE tmr_stage_investmentforecast_import
	SET status_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT DISTINCT investment_id from #Status ) frdm on frdm.investment_id = tmr_stage_investmentforecast_import.investment_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND frdm.investment_id IS NOT NULL; ; 


	DROP TABLE #Status

	 -- Validate Risks

	SELECT  investment_id,Count(Distinct risk)risk into #risk  from tmr_stage_investmentforecast_import WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id  
	GROUP by investment_id
	having Count(Distinct risk) > 1


	UPDATE tmr_stage_investmentforecast_import
	SET risk_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT DISTINCT investment_id from #risk ) frdm on frdm.investment_id = tmr_stage_investmentforecast_import.investment_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND frdm.investment_id IS NOT NULL; 


	DROP TABLE #risk 


	-- Validate Description

	SELECT  investment_id,Count(Distinct status_desc)statusDesc into #statusDesc  from tmr_stage_investmentforecast_import WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id  AND   tmr_stage_investmentforecast_import.status_desc IS NOT NULL AND tmr_stage_investmentforecast_import.status_desc <> '' 
	GROUP by investment_id
	having Count(Distinct status_desc) > 1


	UPDATE tmr_stage_investmentforecast_import
	SET statusDesc_error = 1, error_count = error_count + 1
	FROM tmr_stage_investmentforecast_import 
	LEFT JOIN (SELECT DISTINCT investment_id from #statusDesc ) frdm on frdm.investment_id = tmr_stage_investmentforecast_import.investment_id  
	WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
	tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
	tmr_stage_investmentforecast_import.user_id = @user_id   
	AND frdm.investment_id IS NOT NULL; ; 


	DROP TABLE #statusDesc 



	IF(@orgId <> '')
	BEGIN
		-- Validate finished date, status,risk and status description if the investments id doesn't belong to the initial org id at which they were created
			SELECT DISTINCT i.pk_investment_id,tc.fk_org_id as [OrgID] into #InvOrgData from tco_investments i
			LEFT join tco_inv_budgetyear_config tc on i.pk_investment_id=tc.fk_investment_id and i.fk_tenant_id=tc.fk_tenant_id
			WHERE i.fk_tenant_id =@tenant_id and  tc.budget_year =@budget_year 

			UPDATE tmr_stage_investmentforecast_import
			SET orgID = frdm.OrgID
			FROM tmr_stage_investmentforecast_import 
			LEFT JOIN #InvOrgData frdm on frdm.pk_investment_id = tmr_stage_investmentforecast_import.investment_id  
			WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
			tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
			tmr_stage_investmentforecast_import.user_id = @user_id; 

			UPDATE tmr_stage_investmentforecast_import
			SET est_finish_quarter_error = 1,status_error = 1,statusDesc_error = 1,risk_error = 1, error_count = error_count + 4
			FROM tmr_stage_investmentforecast_import 
			LEFT JOIN (SELECT DISTINCT pk_investment_id from #InvOrgData where OrgID <> @orgId ) frdm on frdm.pk_investment_id = tmr_stage_investmentforecast_import.investment_id  
			WHERE  tmr_stage_investmentforecast_import.tenant_id = @tenant_id AND 
			tmr_stage_investmentforecast_import.forecast_period = @forecast_period AND 
			tmr_stage_investmentforecast_import.user_id = @user_id AND frdm.pk_investment_id IS NOT NULL; 

			DROP TABLE #InvOrgData

	END


RETURN 0