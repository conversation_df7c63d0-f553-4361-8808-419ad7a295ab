CREATE OR ALTER PROCEDURE [dbo].[prcValidateNewYearActions]
    @tenant_id int,
    @user_id int,
    @budget_year int,
    @batch_id nvarchar(100),
    @org_version nvarchar(50)
AS

-- Set default action type for batch type 1

UPDATE tfp_stage_newyearactions SET  action_type = 5, action_name = '<PERSON><PERSON>rin<PERSON>ig budsjett',
                                     fk_alter_code = (SELECT top 1 pk_alter_code FROM tco_fp_alter_codes
                                                      WHERE fk_tenant_id = @tenant_id
                                                        AND cab_flag = 1
                                                      ORDER BY sum_code,limit_code, pk_alter_code )
WHERE  fk_tenant_id = @tenant_id
  AND budget_year = @budget_year
  AND user_id = @user_id
  AND batch_id = @batch_id
  AND batch_type = 'original';

-- Set action type 9 for last years actions

UPDATE tfp_stage_newyearactions SET action_type = 9
WHERE  fk_tenant_id = @tenant_id
  AND action_type !=21
  AND budget_year = @budget_year
  AND user_id = @user_id
  AND batch_id = @batch_id
  AND batch_type = 'ActionsLastYear';


-- Fetch line order and override action type for batch type 1 AND 3 (data from budget and budget changes) 

UPDATE tfp_stage_newyearactions
SET line_order = ls.line_order, action_name = ls.action_name, action_type = ls.action_type,
    long_description = '', financial_plan_description = '', consequence = ''
FROM tfp_stage_newyearactions imp, tmd_finplan_line_setup ls
WHERE imp.fk_account_code = ls.fk_account_code
  AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
  AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
  AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
  AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
  AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
  AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
  AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
  AND imp.fk_tenant_id = ls.fk_tenant_id
  AND imp.fk_tenant_id = @tenant_id
  AND imp.budget_year = @budget_year
  AND imp.user_id = @user_id
  AND imp.batch_id = @batch_id
  AND imp.batch_type in ('original','BudgetChanges', 'ActionsLastYear')
  AND ls.priority = 5
  AND ls.budget_year=@budget_year;

UPDATE tfp_stage_newyearactions
SET line_order = ls.line_order, action_name = ls.action_name, action_type = ls.action_type,
    long_description = '', financial_plan_description = '', consequence = ''
FROM tfp_stage_newyearactions imp, tmd_finplan_line_setup ls
WHERE imp.fk_account_code = ls.fk_account_code
  AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
  AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
  AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
  AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
  AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
  AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
  AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
  AND imp.fk_tenant_id = ls.fk_tenant_id
  AND imp.fk_tenant_id = @tenant_id
  AND imp.budget_year = @budget_year
  AND imp.user_id = @user_id
  AND imp.batch_id = @batch_id
  AND imp.batch_type in ('original','BudgetChanges', 'ActionsLastYear')
  AND ls.priority = 4
  AND ls.budget_year=@budget_year;

UPDATE tfp_stage_newyearactions
SET line_order = ls.line_order, action_name = ls.action_name, action_type = ls.action_type,
    long_description = '', financial_plan_description = '', consequence = ''
FROM tfp_stage_newyearactions imp, tmd_finplan_line_setup ls
WHERE imp.fk_account_code = ls.fk_account_code
  AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
  AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
  AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
  AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
  AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
  AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
  AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
  AND imp.fk_tenant_id = ls.fk_tenant_id
  AND imp.fk_tenant_id = @tenant_id
  AND imp.budget_year = @budget_year
  AND imp.user_id = @user_id
  AND imp.batch_id = @batch_id
  AND imp.batch_type in ('original','BudgetChanges', 'ActionsLastYear')
  AND ls.priority = 3
  AND ls.budget_year=@budget_year;

UPDATE tfp_stage_newyearactions
SET line_order = ls.line_order, action_name = ls.action_name, action_type = ls.action_type,
    long_description = '', financial_plan_description = '', consequence = ''
FROM tfp_stage_newyearactions imp, tmd_finplan_line_setup ls
WHERE imp.fk_account_code = ls.fk_account_code
  AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
  AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
  AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
  AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
  AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
  AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
  AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
  AND imp.fk_tenant_id = ls.fk_tenant_id
  AND imp.fk_tenant_id = @tenant_id
  AND imp.budget_year = @budget_year
  AND imp.user_id = @user_id
  AND imp.batch_id = @batch_id
  AND imp.batch_type in ('original','BudgetChanges', 'ActionsLastYear')
  AND ls.priority = 2
  AND ls.budget_year=@budget_year;

UPDATE tfp_stage_newyearactions
SET line_order = ls.line_order, action_name = ls.action_name, action_type = ls.action_type,
    long_description = '', financial_plan_description = '', consequence = ''
FROM tfp_stage_newyearactions imp, tmd_finplan_line_setup ls
WHERE imp.fk_account_code = ls.fk_account_code
  AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
  AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
  AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
  AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
  AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
  AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
  AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
  AND imp.fk_tenant_id = ls.fk_tenant_id
  AND imp.fk_tenant_id = @tenant_id
  AND imp.budget_year = @budget_year
  AND imp.user_id = @user_id
  AND imp.batch_id = @batch_id
  AND imp.batch_type in ('original','BudgetChanges', 'ActionsLastYear')
  AND ls.priority = 1
  AND ls.budget_year=@budget_year;

-- Fetch action_id on existing actions on ActionsLastYear  --

    if (SELECT count(*) FROM tfp_stage_newyearactions imp
        WHERE imp.fk_tenant_id = @tenant_id
          AND imp.budget_year = @budget_year
          AND imp.user_id = @user_id
          AND imp.batch_id = @batch_id
          AND imp.batch_type ='original') = 0

        BEGIN
            UPDATE tfp_stage_newyearactions
            SET new_fk_action_id = A.pk_action_id, action_name = A.description
            FROM tfp_stage_newyearactions imp, (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year
                                                FROM tfp_trans_header th, tfp_trans_detail td
                                                WHERE th.pk_action_id = td.fk_action_id
                                                  AND th.fk_tenant_id = td.fk_tenant_id
                                                  AND th.action_type IN (1,2,3,4,100)
                                                  AND th.line_order != 0
                                                  AND td.budget_year = @budget_year
                                                  AND th.fk_tenant_id = @tenant_id)  A
            WHERE imp.line_order = A.line_order
              AND imp.fk_tenant_id = A.fk_tenant_id
              AND imp.budget_year = A.budget_year
              AND imp.fk_tenant_id = @tenant_id
              AND imp.budget_year = @budget_year
              AND imp.action_type = A.action_type
              AND imp.batch_id = @batch_id
              AND imp.user_id = @user_id
              AND imp.batch_type in ('ActionsLastYear','BudgetChanges');
        END

    if (SELECT count(*) FROM tfp_stage_newyearactions imp
        WHERE imp.fk_tenant_id = @tenant_id
          AND imp.budget_year = @budget_year
          AND imp.user_id = @user_id
          AND imp.batch_id = @batch_id
          AND imp.batch_type ='ActionsLastYear') = 0

        BEGIN
            UPDATE tfp_stage_newyearactions
            SET new_fk_action_id = A.pk_action_id, action_name = A.description
            FROM tfp_stage_newyearactions imp, (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year
                                                FROM tfp_trans_header th, tfp_trans_detail td
                                                WHERE th.pk_action_id = td.fk_action_id
                                                  AND th.fk_tenant_id = td.fk_tenant_id
                                                  AND th.action_type IN (1,2,3,4,100)
                                                  AND th.line_order != 0
                                                  AND td.budget_year = @budget_year
                                                  AND th.fk_tenant_id = @tenant_id)  A
            WHERE imp.line_order = A.line_order
              AND imp.fk_tenant_id = A.fk_tenant_id
              AND imp.budget_year = A.budget_year
              AND imp.fk_tenant_id = @tenant_id
              AND imp.budget_year = @budget_year
              AND imp.action_type = A.action_type
              AND imp.batch_id = @batch_id
              AND imp.user_id = @user_id
              AND imp.batch_type = 'original';
        END

UPDATE tfp_stage_newyearactions
SET previous_pk_action_id = A.pk_action_id
    FROM tfp_stage_newyearactions imp, (SELECT DISTINCT th.pk_action_id, th.fk_tenant_id, th.line_order, th.action_type, th.description, td.budget_year
                                                    FROM tfp_trans_header th, tfp_trans_detail td
                                                    WHERE th.pk_action_id = td.fk_action_id
                                                      AND th.fk_tenant_id = td.fk_tenant_id
                                                      AND th.action_type IN (1,2,3,4,100)
                                                      AND th.line_order != 0
                                                      AND td.budget_year = (@budget_year - 1)
                                                      AND th.fk_tenant_id = @tenant_id)  A
WHERE imp.line_order = A.line_order
  AND imp.fk_tenant_id = A.fk_tenant_id
  AND imp.fk_tenant_id = @tenant_id
  AND imp.budget_year = @budget_year
  AND imp.action_type = A.action_type
  AND imp.batch_id = @batch_id
  AND imp.user_id = @user_id
  AND imp.batch_type in ('original','BudgetChanges', 'ActionsLastYear');

-- Change action type on action type 60 for batch_type BudgetChanges

UPDATE tfp_stage_newyearactions SET action_type = 7
WHERE  fk_tenant_id = @tenant_id
  AND budget_year = @budget_year
  AND user_id = @user_id
  AND batch_id = @batch_id
  AND action_type = 60
  AND batch_type = 'BudgetChanges';

-- Set default alter code for batch_type ReverseDemo

UPDATE tfp_stage_newyearactions SET fk_alter_code = ac.pk_alter_code
FROM tfp_stage_newyearactions nw, tco_fp_alter_codes ac
WHERE  nw.fk_tenant_id = @tenant_id
  AND nw.budget_year = @budget_year
  AND nw.user_id = @user_id
  AND nw.batch_id = @batch_id
  AND nw.action_type = 6
  AND nw.batch_type = 'ReverseDemo'
  AND nw.fk_tenant_id = ac.fk_tenant_id
  AND pk_alter_code = 5;

-- Change action type for batch_type ReverseDemo

UPDATE tfp_stage_newyearactions SET action_type = 5,isManuallyAdded=1
WHERE  fk_tenant_id = @tenant_id
  AND budget_year = @budget_year
  AND user_id = @user_id
  AND batch_id = @batch_id
  AND action_type = 6
  AND batch_type = 'ReverseDemo';


-- DELETE zero rows

DELETE FROM  [dbo].[tfp_stage_newyearactions]
WHERE  fk_tenant_id = @tenant_id
  AND budget_year = @budget_year
  AND user_id = @user_id
  AND batch_id = @batch_id
  AND year_1_amount = 0
  AND year_2_amount = 0
  AND year_3_amount = 0
  AND year_4_amount = 0;


    RETURN 0


-- Insert rows in budget limit for new year. 


INSERT INTO tfp_budget_limits (fk_tenant_id,budget_year,action_type,year_1_limit,year_2_limit,year_3_limit,year_4_limit,updated,updated_by,fp_level_1_value,fp_level_2_value)
SELECT DISTINCT fk_tenant_id, budget_year, action_type, 0 as year_1_limit, 0 as year_2_limit, 0 as year_3_limit, 0 as year_4_limit,
                GETDATE() AS updated, @user_id as updated_by, fp_level_1_value, fp_level_2_value
FROM
    (SELECT oh.fk_tenant_id, @budget_year as budget_year,20 as action_type, fp_level_1_value =
            CASE	WHEN p1.param_value = 'org_id_1' THEN org_id_1
                    WHEN p1.param_value = 'org_id_2' THEN org_id_2
                    WHEN p1.param_value = 'org_id_3' THEN org_id_3
                    WHEN p1.param_value = 'org_id_4' THEN org_id_4
                    WHEN p1.param_value = 'org_id_5' THEN org_id_5
                    WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END, fp_level_2_value =
            CASE	WHEN p2.param_value = 'org_id_1' THEN org_id_1
                    WHEN p2.param_value = 'org_id_2' THEN org_id_2
                    WHEN p2.param_value = 'org_id_3' THEN org_id_3
                    WHEN p2.param_value = 'org_id_4' THEN org_id_4
                    WHEN p2.param_value = 'org_id_5' THEN org_id_5
                    WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END FROM tco_org_hierarchy oh
                             INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version
                             JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
                             LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
                             LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
     WHERE oh.fk_tenant_id = @tenant_id

     union

     SELECT oh.fk_tenant_id, @budget_year as budget_year,30 as action_type, fp_level_1_value =
            CASE	WHEN p1.param_value = 'org_id_1' THEN org_id_1
                    WHEN p1.param_value = 'org_id_2' THEN org_id_2
                    WHEN p1.param_value = 'org_id_3' THEN org_id_3
                    WHEN p1.param_value = 'org_id_4' THEN org_id_4
                    WHEN p1.param_value = 'org_id_5' THEN org_id_5
                    WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END, fp_level_2_value =
            CASE	WHEN p2.param_value = 'org_id_1' THEN org_id_1
                    WHEN p2.param_value = 'org_id_2' THEN org_id_2
                    WHEN p2.param_value = 'org_id_3' THEN org_id_3
                    WHEN p2.param_value = 'org_id_4' THEN org_id_4
                    WHEN p2.param_value = 'org_id_5' THEN org_id_5
                    WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END FROM tco_org_hierarchy oh
                             INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version
                             JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
                             LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
                             LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
     WHERE oh.fk_tenant_id = @tenant_id

     union

     SELECT oh.fk_tenant_id, @budget_year as budget_year,40 as action_type, fp_level_1_value =
            CASE	WHEN p1.param_value = 'org_id_1' THEN org_id_1
                    WHEN p1.param_value = 'org_id_2' THEN org_id_2
                    WHEN p1.param_value = 'org_id_3' THEN org_id_3
                    WHEN p1.param_value = 'org_id_4' THEN org_id_4
                    WHEN p1.param_value = 'org_id_5' THEN org_id_5
                    WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END, fp_level_2_value =
            CASE	WHEN p2.param_value = 'org_id_1' THEN org_id_1
                    WHEN p2.param_value = 'org_id_2' THEN org_id_2
                    WHEN p2.param_value = 'org_id_3' THEN org_id_3
                    WHEN p2.param_value = 'org_id_4' THEN org_id_4
                    WHEN p2.param_value = 'org_id_5' THEN org_id_5
                    WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END FROM tco_org_hierarchy oh
                             INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version
                             JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
                             LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
                             LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
     WHERE oh.fk_tenant_id = @tenant_id

     union

     SELECT oh.fk_tenant_id, @budget_year as budget_year,90 as action_type, fp_level_1_value =
            CASE	WHEN p1.param_value = 'org_id_1' THEN org_id_1
                    WHEN p1.param_value = 'org_id_2' THEN org_id_2
                    WHEN p1.param_value = 'org_id_3' THEN org_id_3
                    WHEN p1.param_value = 'org_id_4' THEN org_id_4
                    WHEN p1.param_value = 'org_id_5' THEN org_id_5
                    WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END, fp_level_2_value =
            CASE	WHEN p2.param_value = 'org_id_1' THEN org_id_1
                    WHEN p2.param_value = 'org_id_2' THEN org_id_2
                    WHEN p2.param_value = 'org_id_3' THEN org_id_3
                    WHEN p2.param_value = 'org_id_4' THEN org_id_4
                    WHEN p2.param_value = 'org_id_5' THEN org_id_5
                    WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
                    WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
                    WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
                    WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
                    WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
                    ELSE ''
                END FROM tco_org_hierarchy oh
                             INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version
                             JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
                             LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
                             LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
     WHERE oh.fk_tenant_id = @tenant_id

    ) A
WHERE NOT EXISTS (SELECT * FROM tfp_budget_limits b WHERE A.fk_tenant_id = b.fk_tenant_id AND a.action_type = b.action_type
                                                      AND a.budget_year = b.budget_year AND a.fp_level_1_value = b.fp_level_1_value AND a.fp_level_2_value = b.fp_level_2_value)
GROUP BY fk_tenant_id, budget_year, action_type, fp_level_1_value, fp_level_2_value
ORDER BY fk_tenant_id, budget_year, action_type, fp_level_1_value, fp_level_2_value

GO



