
CREATE PROC [dbo].[proc_bud_changes_update]
@tenant_id INT,
@budget_year INT,
@batch_id varchar (32) = ''

AS

--DECLARE @tenant_id INT = 37
--DECLARE @budget_year INT = 2022
--DECLARE @batch_id NVARCHAR(32) = '21421072925441'

DECLARE @statement NVARCHAR(MAX) --Used for dynamic sql queries

DROP TABLE IF EXISTS #error_types
CREATE TABLE #error_types (
fk_error_type NVARCHAR(50),
case_id INT)


--Define the error types supported in the SP.
--1		just 1 dimension
--2		Relation error
--3		Required default value - replace blank field with some value
--4		Field required to be blank - not implemented support for as of yet
--5		Other with no handling - support needs to handle

INSERT INTO #error_types VALUES
('dim_not_valid'				, 1	),
('dim_locked'					, 1	),
('invalid_account'				, 1	),
('DIM_INVALID_CONTROL_SP'		, 1	),
('DIM_TEMPORARY_CONTROL_SP'		, 1	),
('DIM_NOT_ACTIVE_CONTROL_SP'	, 1	),
('dim_does_not_exist'			, 1	),
('default_value'				, 1	),
('dim_combination_not_valid'	, 2	),
('RELATION_INVALID_CONTROL_SP'	, 2	),
('dim_required'					, 3	),
('dim_not_allowed'				, 4	),
('dim_automatic'				, 4	),
('dim_prohibited'				, 4	),
('budget_unbalanced'			, 5	),
('dim_unknown_error'			, 5	),
('costcentre_control_violation'	, 5	)

--Check if there's any unsupported error types in the batch, if there is then give warning.
IF	(
	select COUNT(*) from tbu_integ_error_handling a
	LEFT JOIN #error_types b on a.fk_error_type = b.fk_error_type
	where fk_tenant_id = @tenant_id
	and batch_id = @batch_id
	and b.fk_error_type IS NULL
	) >0
BEGIN
	PRINT 'WARNING: Unsupported error type in log - contact support'
END

--Find tenant type (agresso vs Visma AND try to figure out the export ids we are working with
DECLARE @tenant_type NVARCHAR(30) = (select erp_type from gco_tenants where pk_id = @tenant_id)
DECLARE @v2_export_id INT = (select MAX(export_id)export_id from tbu_integ_error_handling where fk_tenant_id = @tenant_id and batch_id = @batch_id)
DECLARE @v1_export_id INT = (select MAX(id)export_id from tfp_erp_exports where tenant_id = @tenant_id and budget_Year = @budget_year AND is_delta = 1 AND id < @v2_export_id)

IF @v1_export_id IS NULL
BEGIN
	SET @v1_export_id = (select MAX(id)export_id from tfp_erp_exports where tenant_id = @tenant_id and budget_Year = @budget_year AND id < @v2_export_id)
END

IF @v1_export_id IS NULL
BEGIN
	PRINT 'Could not find previous export ID - contact support'
	RETURN;
END

--Find log table name based on ERP type
DECLARE @log_table_name NVARCHAR(50)
DECLARE @postback_table_name NVARCHAR(50)
DECLARE @deltas_table_name NVARCHAR(50)

IF @tenant_type = 'Agresso'
BEGIN
	SET @log_table_name = 'tfp_erp_export_log_agresso'
	SET @postback_table_name = 'integ_agrpostback_bud'
	SET @deltas_table_name = 'integ_tfp_erp_export_log_agresso_deltas'
END

IF @tenant_type = 'Visma'
BEGIN
	SET @log_table_name = 'tfp_erp_export_log_visma'
	SET @postback_table_name = 'integnew_export_visma_posted_bud'
	SET @deltas_table_name = 'integ_tfp_erp_export_log_visma_deltas'
END

IF @log_table_name = ''
BEGIN
	PRINT 'Problem with finding tenant ERP type - Contact support'
	RETURN;
END


--Fetch accounts to be included
DROP TABLE IF EXISTS #account_list_invest
SELECT b.pk_tenant_id, b.pk_account_code 
INTO #account_list_invest
FROM tco_accounts b
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code and c.type = 'investment'
WHERE @budget_year between DATEPART(YEAR,b.dateFrom) and DATEPART(YEAR,b.dateTo)
AND b.pk_tenant_id = @tenant_id
GROUP BY b.pk_tenant_id, b.pk_account_code 

DROP TABLE IF EXISTS #account_list_oper
SELECT ac.pk_tenant_id, ac.pk_account_code 
INTO #account_list_oper
FROM tco_accounts ac
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = 'YBUD1'
WHERE @budget_year between DATEPART(YEAR,ac.dateFrom) and DATEPART(YEAR,ac.dateTo)
AND ac.pk_tenant_id = @tenant_id
GROUP BY ac.pk_tenant_id, ac.pk_account_code


DROP TABLE IF EXISTS #temp_delta
CREATE TABLE #temp_delta (
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[description] [nvarchar](700) NOT NULL,
	[fk_adjustment_code] [nvarchar](25) NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[jan] [decimal](18, 2) NOT NULL,
	[feb] [decimal](18, 2) NOT NULL,
	[mar] [decimal](18, 2) NOT NULL,
	[apr] [decimal](18, 2) NOT NULL,
	[may] [decimal](18, 2) NOT NULL,
	[jun] [decimal](18, 2) NOT NULL,
	[jul] [decimal](18, 2) NOT NULL,
	[aug] [decimal](18, 2) NOT NULL,
	[sep] [decimal](18, 2) NOT NULL,
	[oct] [decimal](18, 2) NOT NULL,
	[nov] [decimal](18, 2) NOT NULL,
	[dec] [decimal](18, 2) NOT NULL
	)

DROP TABLE IF EXISTS #transactions_to_update
CREATE TABLE #transactions_to_update(
	[table_name] NVARCHAR(50) NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[pk_id] NVARCHAR(300) NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[description] [nvarchar](700) NOT NULL,
	[fk_adjustment_code] [nvarchar](25) NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	--[period] [int] NOT NULL,
	--[amount_year_1] [decimal](18, 2) NOT NULL,
	[new_fk_account_code] [nvarchar](25) NOT NULL,
	[new_fk_department_code] [nvarchar](25) NOT NULL,
	[new_fk_function_code] [nvarchar](25) NOT NULL,
	[new_fk_project_code] [nvarchar](25) NOT NULL,
	[new_free_dim_1] [nvarchar](25) NOT NULL,
	[new_free_dim_2] [nvarchar](25) NOT NULL,
	[new_free_dim_3] [nvarchar](25) NOT NULL,
	[new_free_dim_4] [nvarchar](25) NOT NULL,
	to_update BIT NULL)

DROP TABLE IF EXISTS #temp_inv_budget
CREATE TABLE #temp_inv_budget(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[pk_id] [uniqueidentifier] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[fk_adjustment_code] [nvarchar](50) NOT NULL,
	[fk_alter_code] [nvarchar](50) NOT NULL,
	[description] [nvarchar](max) NULL,
	[amount] [decimal](18, 2) NOT NULL)

--Fetch delta directly from erp log tables to use against tbu_trans since these don't have the default values set for project, etc.
SET @statement = '
INSERT INTO #temp_delta (	[fk_tenant_id],[budget_year],[fk_account_code],[department_code],[fk_function_code],[fk_project_code]
							,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code],
							[jan],[feb],[mar],[apr],[may],[jun],[jul],[aug],[sep],[oct],[nov],[dec])
SELECT
	 [fk_tenant_id] = '+CONVERT(VARCHAR(20),@tenant_id)+'
	,[budget_year] = '+CONVERT(VARCHAR(5),@budget_year)+'
	,[fk_account_code] = FkAccountCode
	,[department_code] = DepartmentCode
	,[fk_function_code] = FkFunctionCode
	,[fk_project_code] = FkProjectCode
	,[free_dim_1] = FreeDim1
	,[free_dim_2] = FreeDim2
	,[free_dim_3] = FreeDim3
	,[free_dim_4] = FreeDim4
	,[description] = [description]
	,[fk_adjustment_code] = FkAdjustmentCode
	,[fk_alter_code] = FkAlterCode
	,Jan = SUM(v2_Jan-v1_Jan)
	,Feb = SUM(v2_Feb-v1_Feb)
	,Mar = SUM(v2_Mar-v1_Mar)
	,Apr = SUM(v2_Apr-v1_Apr)
	,May = SUM(v2_May-v1_May)
	,Jun = SUM(v2_Jun-v1_Jun)
	,Jul = SUM(v2_Jul-v1_Jul)
	,Aug = SUM(v2_Aug-v1_Aug)
	,Sep = SUM(v2_Sep-v1_Sep)
	,Oct = SUM(v2_Oct-v1_Oct)
	,Nov = SUM(v2_Nov-v1_Nov)
	,Dec = SUM(v2_Dec-v1_Dec)
FROM (
			SELECT	
					description = description
					,FkAccountCode = fk_account_code
					,DepartmentCode = department_code
					,FkFunctionCode = fk_function_code
					,FkProjectCode = fk_project_code
					,FreeDim1 = free_dim_1
					,FreeDim2 = free_dim_2
					,FreeDim3 = free_dim_3
					,FreeDim4 = free_dim_4
					,FkAdjustmentCode = fk_adjustment_code
					,FkAlterCode = fk_alter_code
					,v1_Jan = jan
					,v1_Feb = feb
					,v1_Mar = mar
					,v1_Apr = apr
					,v1_May = may
					,v1_Jun = jun
					,v1_Jul = jul
					,v1_Aug = aug
					,v1_Sep = sep
					,v1_Oct = oct
					,v1_Nov = nov
					,v1_Dec = dec
					,v2_Jan = 0
					,v2_Feb	= 0
					,v2_Mar	= 0
					,v2_Apr	= 0
					,v2_May	= 0
					,v2_Jun	= 0
					,v2_Jul	= 0
					,v2_Aug	= 0
					,v2_Sep	= 0
					,v2_Oct	= 0
					,v2_Nov	= 0
					,v2_Dec	= 0
			FROM '+ @log_table_name +' WHERE export_id = '+CONVERT(VARCHAR(20),@v1_export_id)+'
			UNION ALL
			SELECT	description = description
					,FkAccountCode = fk_account_code
					,DepartmentCode = department_code
					,FkFunctionCode = fk_function_code
					,FkProjectCode = fk_project_code
					,FreeDim1 = free_dim_1
					,FreeDim2 = free_dim_2
					,FreeDim3 = free_dim_3
					,FreeDim4 = free_dim_4
					,FkAdjustmentCode = fk_adjustment_code
					,FkAlterCode = fk_alter_code
					,v1_Jan = 0
					,v1_Feb	= 0
					,v1_Mar	= 0
					,v1_Apr	= 0
					,v1_May	= 0
					,v1_Jun	= 0
					,v1_Jul	= 0
					,v1_Aug	= 0
					,v1_Sep	= 0
					,v1_Oct	= 0
					,v1_Nov	= 0
					,v1_Dec	= 0
					,v2_Jan = jan
					,v2_Feb = feb
					,v2_Mar = mar
					,v2_Apr = apr
					,v2_May = may
					,v2_Jun = jun
					,v2_Jul = jul
					,v2_Aug = aug
					,v2_Sep = sep
					,v2_Oct = oct
					,v2_Nov = nov
					,v2_Dec = dec
			FROM '+ @log_table_name +' WHERE export_id = '+CONVERT(VARCHAR(20),@v2_export_id)+'
	) TRANS
	GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode
	HAVING	SUM(v2_Jan-v1_Jan) <> 0 OR
			SUM(v2_Feb-v1_Feb) <> 0 OR
			SUM(v2_Mar-v1_Mar) <> 0 OR
			SUM(v2_Apr-v1_Apr) <> 0 OR
			SUM(v2_May-v1_May) <> 0 OR
			SUM(v2_Jun-v1_Jun) <> 0 OR
			SUM(v2_Jul-v1_Jul) <> 0 OR
			SUM(v2_Aug-v1_Aug) <> 0 OR
			SUM(v2_Sep-v1_Sep) <> 0 OR
			SUM(v2_Oct-v1_Oct) <> 0 OR
			SUM(v2_Nov-v1_Nov) <> 0 OR
			SUM(v2_Dec-v1_Dec) <> 0
'
EXEC sp_executesql @statement



--Use calculated delta to fetch the rows in tbu_trans that is related to the change
INSERT INTO #transactions_to_update(
	[table_name],[fk_tenant_id],[budget_year],[pk_id],[fk_account_code] ,[fk_department_code] ,[fk_function_code],[fk_project_code] 
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code]
	--,[period],[amount_year_1]
	,[new_fk_account_code] ,[new_fk_department_code] ,[new_fk_function_code],[new_fk_project_code] 
	,[new_free_dim_1],[new_free_dim_2],[new_free_dim_3],[new_free_dim_4])
select 
[table_name] = 'tbu_trans_detail'
,c.fk_tenant_id	
,c.budget_year
,c.pk_id
,c.fk_account_code	
,c.department_code	
,c.fk_function_code	
,c.fk_project_code	
,c.free_dim_1	
,c.free_dim_2	
,c.free_dim_3	
,c.free_dim_4	
,c.[description]	
,c.fk_adjustment_code
,c.fk_alter_code
--,c.period
--,c.amount_year_1
,new_fk_account_code	=c.fk_account_code	
,new_fk_department_code	=c.department_code	
,new_fk_function_code	=c.fk_function_code	
,new_fk_project_code	=c.fk_project_code	
,new_free_dim_1			=c.free_dim_1	
,new_free_dim_2			=c.free_dim_2	
,new_free_dim_3			=c.free_dim_3	
,new_free_dim_4			=c.free_dim_4	
from #temp_delta a
JOIN #account_list_oper b on a.fk_account_code = b.pk_account_code
JOIN tbu_trans_detail c ON 
    a.fk_tenant_id			= c.fk_tenant_id	
AND a.budget_year			= c.budget_year	
AND a.fk_account_code		= c.fk_account_code	
AND a.department_code		= c.department_code	
AND a.fk_function_code		= c.fk_function_code	
AND a.fk_project_code		= c.fk_project_code	
AND a.free_dim_1			= c.free_dim_1	
AND a.free_dim_2			= c.free_dim_2	
AND a.free_dim_3			= c.free_dim_3	
AND a.free_dim_4			= c.free_dim_4	
AND a.[description]			= c.[description]	
AND a.fk_adjustment_code	= c.fk_adjustment_code
AND a.fk_alter_code			= c.fk_alter_code

--Fetch the rows from the log tables to be updated. Have to fetch all logs equal or higher than the export we are working on since there could have been sent more batches in the interim
--Here we use the deltas table since these tables have defaults set
SET @statement = '
INSERT INTO #transactions_to_update(
	[table_name],[fk_tenant_id],[budget_year],[pk_id],[fk_account_code] ,[fk_department_code] ,[fk_function_code],[fk_project_code] 
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code]
	,[new_fk_account_code] ,[new_fk_department_code] ,[new_fk_function_code],[new_fk_project_code] 
	,[new_free_dim_1],[new_free_dim_2],[new_free_dim_3],[new_free_dim_4])
select 
[table_name] = '''+@log_table_name+'''
,c.fk_tenant_id	
,c.budget_year
,c.pk_id
,c.fk_account_code	
,c.department_code	
,c.fk_function_code	
,c.fk_project_code	
,c.free_dim_1	
,c.free_dim_2	
,c.free_dim_3	
,c.free_dim_4	
,c.[description]	
,c.fk_adjustment_code
,c.fk_alter_code
,new_fk_account_code	=c.fk_account_code	
,new_fk_department_code	=c.department_code	
,new_fk_function_code	=c.fk_function_code	
,new_fk_project_code	=c.fk_project_code	
,new_free_dim_1			=c.free_dim_1	
,new_free_dim_2			=c.free_dim_2	
,new_free_dim_3			=c.free_dim_3	
,new_free_dim_4			=c.free_dim_4	
from ' + @deltas_table_name +' a
JOIN ' + @log_table_name +' c ON 
    a.fk_tenant_id			= c.fk_tenant_id	
AND a.fk_account_code		= c.fk_account_code	
AND a.department_code		= c.department_code	
AND a.fk_function_code		= c.fk_function_code	
AND a.fk_project_code		= c.fk_project_code	
AND a.free_dim_1			= c.free_dim_1	
AND a.free_dim_2			= c.free_dim_2	
AND a.free_dim_3			= c.free_dim_3	
AND a.free_dim_4			= c.free_dim_4	
AND a.[description]			= c.[description]	
AND a.fk_adjustment_code	= c.fk_adjustment_code
AND a.fk_alter_code			= c.fk_alter_code
JOIN tfp_erp_exports d ON c.export_id = d.id and c.fk_tenant_id = d.tenant_id and d.budget_Year = a.budget_year and d.is_delta = 1
WHERE a.export_id = '+CONVERT(VARCHAR(20),@v2_export_id)+'
AND c.export_id >= '+CONVERT(VARCHAR(20),@v2_export_id)+'
and a.fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
and a.budget_year = '+CONVERT(VARCHAR(20),@budget_year)

EXEC sp_executesql @statement


--Fetch full investment budget into temp table
INSERT INTO #temp_inv_budget
SELECT  
 fk_tenant_id [FkTenantId]
,budget_year[BudgetYear]
,pk_id
,fk_account_code[FkAccountCode]
,department_code[DepartmentCode]
,fk_function_code[FkFunctionCode]
,fk_project_code[FkProjectCode]
,free_dim_1[FreeDim1]
,free_dim_2[FreeDim2]
,free_dim_3[FreeDim3]
,free_dim_4[FreeDim4]
,isnull(fk_adjustment_code,'') [FkAdjustmentCode]
,isnull(fk_alter_code,'') [FkAlterCode]
,ISNULL(description,'') [description]
,amount
FROM
(
select 
 PT.fk_tenant_id
,budget_year = @budget_year
,pt.pk_id
,pt.fk_account_code
,pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, fk_adjustment_code = CONVERT(VARCHAR(4),@budget_year)+'VBUD'
, fk_alter_code = ''
, PT.description
,pt.amount
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN #account_list_invest ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @tenant_id
		and budget_year < @budget_year
		UNION
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @tenant_id
		and budget_year = @budget_year
		and org_budget_flag = 1
)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
AND year = @budget_year
AND pt.fk_tenant_id = @tenant_id
) INV_new

UNION ALL

SELECT  
 fk_tenant_id [FkTenantId]
,budget_year[BudgetYear]
,pk_id
,fk_account_code[FkAccountCode]
,department_code[DepartmentCode]
,fk_function_code[FkFunctionCode]
,fk_project_code[FkProjectCode]
,free_dim_1[FreeDim1]
,free_dim_2[FreeDim2]
,free_dim_3[FreeDim3]
,free_dim_4[FreeDim4]
,isnull(fk_adjustment_code,'') [FkAdjustmentCode]
,isnull(fk_alter_code,'') [FkAlterCode]
,ISNULL(description,'') [description]
,amount
FROM
(
select 
 PT.fk_tenant_id
,@budget_year as budget_year
,pt.pk_id
,pt.fk_account_code
,pt.fk_department_code as department_code
,pt.fk_function_code
,pt.fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,fk_adjustment_code = pt.fk_user_adjustment_code
,PT.fk_alter_code
,PT.description
,pt.amount
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN #account_list_invest ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @tenant_id
		and budget_year = @budget_year
		and org_budget_flag = 0
)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
AND year = @budget_year
AND pt.fk_tenant_id = @tenant_id
) INV_new



--Identify the transactions from investment budget that have changed based on the deltas calculated earlier
INSERT INTO #transactions_to_update(
	[table_name],[fk_tenant_id],[budget_year],[pk_id],[fk_account_code] ,[fk_department_code] ,[fk_function_code],[fk_project_code] 
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code]
	--,[period],[amount_year_1]
	,[new_fk_account_code] ,[new_fk_department_code] ,[new_fk_function_code],[new_fk_project_code] 
	,[new_free_dim_1],[new_free_dim_2],[new_free_dim_3],[new_free_dim_4])
select 
[table_name] = 'tfp_proj_transactions'
,c.fk_tenant_id	
,c.budget_year
,c.pk_id
,c.fk_account_code	
,c.fk_department_code	
,c.fk_function_code	
,c.fk_project_code	
,c.free_dim_1	
,c.free_dim_2	
,c.free_dim_3	
,c.free_dim_4	
,c.[description]	
,c.fk_adjustment_code
,c.fk_alter_code
--,[period] = @budget_year*100+1
--,[amount_year_1] = c.amount
,new_fk_account_code	=c.fk_account_code	
,new_fk_department_code	=c.fk_department_code	
,new_fk_function_code	=c.fk_function_code	
,new_fk_project_code	=c.fk_project_code	
,new_free_dim_1			=c.free_dim_1	
,new_free_dim_2			=c.free_dim_2	
,new_free_dim_3			=c.free_dim_3	
,new_free_dim_4			=c.free_dim_4	
from #temp_delta a
JOIN #account_list_invest b on a.fk_account_code = b.pk_account_code
JOIN #temp_inv_budget c ON 
    a.fk_tenant_id			= c.fk_tenant_id	
AND a.budget_year			= c.budget_year	
AND a.fk_account_code		= c.fk_account_code	
AND a.department_code		= c.fk_department_code	
AND a.fk_function_code		= c.fk_function_code	
AND a.fk_project_code		= c.fk_project_code	
AND a.free_dim_1			= c.free_dim_1	
AND a.free_dim_2			= c.free_dim_2	
AND a.free_dim_3			= c.free_dim_3	
AND a.free_dim_4			= c.free_dim_4	
AND a.[description]			= c.[description]	
AND a.fk_adjustment_code	= c.fk_adjustment_code
AND a.fk_alter_code			= c.fk_alter_code

--Fetch the rows from the postback / error tables for the integration which should be updated
SET @statement = '
INSERT INTO #transactions_to_update(
	[table_name],[fk_tenant_id],[budget_year],[pk_id],[fk_account_code] ,[fk_department_code] ,[fk_function_code],[fk_project_code] 
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code]
	,[new_fk_account_code] ,[new_fk_department_code] ,[new_fk_function_code],[new_fk_project_code] 
	,[new_free_dim_1],[new_free_dim_2],[new_free_dim_3],[new_free_dim_4])
select 
[table_name] = '''+ @postback_table_name + '''
,c.fk_tenant_id	
,budget_year = '+ CONVERT(VARCHAR(10),@budget_year) +'
,c.pk_id
,c.fk_account_code	
,c.'+ CASE WHEN @tenant_type = 'Visma' THEN 'fk_department_code' ELSE 'department_code' END  +'	
,c.fk_function_code	
,c.fk_project_code	
,c.free_dim_1	
,c.free_dim_2	
,c.free_dim_3	
,c.free_dim_4	
,c.[description]	
,c.fk_adjustment_code
,c.fk_alter_code
,new_fk_account_code	=c.fk_account_code	
,new_fk_department_code	=c.'+ CASE WHEN @tenant_type = 'Visma' THEN 'fk_department_code' ELSE 'department_code' END  +'	
,new_fk_function_code	=c.fk_function_code	
,new_fk_project_code	=c.fk_project_code	
,new_free_dim_1			=c.free_dim_1	
,new_free_dim_2			=c.free_dim_2	
,new_free_dim_3			=c.free_dim_3	
,new_free_dim_4			=c.free_dim_4	
from '+@postback_table_name+' c
where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) + '
and export_id = '+ CONVERT(VARCHAR(20),@v2_export_id)

EXEC sp_executesql @statement

--Fetch the rows from the deltas tables for the integration which should be updated
SET @statement = '
INSERT INTO #transactions_to_update(
	[table_name],[fk_tenant_id],[budget_year],[pk_id],[fk_account_code] ,[fk_department_code] ,[fk_function_code],[fk_project_code] 
	,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code]
	,[new_fk_account_code] ,[new_fk_department_code] ,[new_fk_function_code],[new_fk_project_code] 
	,[new_free_dim_1],[new_free_dim_2],[new_free_dim_3],[new_free_dim_4])
select 
[table_name] = '''+ @deltas_table_name + '''
,c.fk_tenant_id	
,budget_year = '+ CONVERT(VARCHAR(10),@budget_year) +'
,c.pk_id
,c.fk_account_code	
,c.department_code
,c.fk_function_code	
,c.fk_project_code	
,c.free_dim_1	
,c.free_dim_2	
,c.free_dim_3	
,c.free_dim_4	
,c.[description]	
,c.fk_adjustment_code
,c.fk_alter_code
,new_fk_account_code	=c.fk_account_code	
,new_fk_department_code	=c.department_code
,new_fk_function_code	=c.fk_function_code	
,new_fk_project_code	=c.fk_project_code	
,new_free_dim_1			=c.free_dim_1	
,new_free_dim_2			=c.free_dim_2	
,new_free_dim_3			=c.free_dim_3	
,new_free_dim_4			=c.free_dim_4	
from '+@deltas_table_name+' c
where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) + '
and export_id = '+ CONVERT(VARCHAR(20),@v2_export_id)

EXEC sp_executesql @statement


/******* WE NOW HAVE A SINGLE TEMP TABLE WITH ALL RELEVANT TRANSACTIONS AND WE'LL FIRST DO ALL UPDATE AGAINST THIS TEMP TABLE *******/ 

/******* CASE 1 - ERRORS WHERE THERE SOMETHING WRONG WITH A SINGLE DIMENSION *******/ 

DROP TABLE IF EXISTS #dim_singleton_list
select row_id = row_number()  OVER(order by pk_id)
,pk_id
,dim_1_type
,dim_id_old = dim_1_id 
,dim_id_new = id_new
,a.fk_error_type
INTO #dim_singleton_list
from tbu_integ_error_handling a
JOIN #error_types b on a.fk_error_type = b.fk_error_type AND b.case_id = 1
where [keep] = 0
AND fixed = 0
and id_new IS NOT NULL
AND id_new != ''
AND dim_1_type IS NOT NULL
AND dim_1_type != ''
AND dim_1_id IS NOT NULL
AND dim_1_id != ''
AND fk_tenant_id = @tenant_id
and export_id = @v2_export_id


DECLARE @CursorID INT = 1;
DECLARE @RowCnt BIGINT = 0;

SELECT @RowCnt = COUNT(0) FROM #dim_singleton_list

WHILE @CursorID <= @RowCnt
BEGIN
	DECLARE @dim_type NVARCHAR(50);
	DECLARE @dim_id_old NVARCHAR(50);
	DECLARE @dim_id_new NVARCHAR(50);
	DECLARE @pk_id_error_table bigint;
	DECLARE @error_type NVARCHAR(300);

	SET @dim_type = (select dim_1_type from #dim_singleton_list where row_id = @CursorID)
	SET @dim_id_old = (select dim_id_old from #dim_singleton_list where row_id = @CursorID)
	SET @dim_id_new = (select dim_id_new from #dim_singleton_list where row_id = @CursorID)
	SET @pk_id_error_table = (select pk_id from #dim_singleton_list where row_id = @CursorID)
	SET @error_type = (select fk_error_type from #dim_singleton_list where row_id = @CursorID)

	DECLARE @update_string NVARCHAR(MAX)

	SET @update_string = '	UPDATE #transactions_to_update SET to_update = 1, new_' + @dim_type + ' = ''' + @dim_id_new + ''' WHERE ' + @dim_type + ' = ''' +  @dim_id_old + ''';
							UPDATE tbu_integ_error_handling SET fixed = 1 where	pk_id = '+ CONVERT(VARCHAR(100),@pk_id_error_table) +';
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
						   ([fk_tenant_id]
						   ,[export_id]
						   ,[batch_id]
						   ,[table_name]
						   ,[table_pk_id]
						   ,[fk_error_type]
						   ,[dim_1_type]
						   ,[dim_2_type]
						   ,[dim_1_id]
						   ,[dim_2_id]
						   ,[dim_to_change]
						   ,[id_new]
						   ,[update_time])
						   SELECT
						   [fk_tenant_id]
						   ,[export_id] = '+CONVERT(varchar(20),@v2_export_id)+'
						   ,[batch_id] = '''+@batch_id+'''
						   ,[table_name]
						   ,[table_pk_id] = pk_id
						   ,[fk_error_type] = '''+@error_type+'''
						   ,[dim_1_type] = '''+@dim_type+'''
						   ,[dim_2_type] = ''''
						   ,[dim_1_id] = '''+@dim_id_old+'''
						   ,[dim_2_id] = ''''
						   ,[dim_to_change] = '''+@dim_type+'''
						   ,[id_new] = '''+@dim_id_new+'''
						   ,[update_time] = getdate()
						   FROM #transactions_to_update
						   WHERE ' + @dim_type + ' = ''' +  @dim_id_old + ''';'
							
	

	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END


/******* CASE 2 - ERRORS WHERE THERE SOMETHING WRONG WITH A COMBINATION OF DIMENSIONS *******/ 

DROP TABLE IF EXISTS #dim_combination_list
select row_id = row_number()  OVER(order by pk_id)
,pk_id
,dim_1_type
,dim_2_type
,dim_1_id_old = dim_1_id 
,dim_2_id_old = dim_2_id 
,dim_type_to_change = dim_to_change
,dim_id_new = id_new
,a.fk_error_type
INTO #dim_combination_list
from tbu_integ_error_handling a
JOIN #error_types b on a.fk_error_type = b.fk_error_type AND b.case_id = 2
where [keep] = 0
and id_new IS NOT NULL
and id_new != ''
AND dim_1_type IS NOT NULL
AND dim_1_type != ''
AND dim_1_id IS NOT NULL
AND dim_1_id != ''
AND dim_2_type IS NOT NULL
AND dim_2_type != ''
AND dim_2_id IS NOT NULL
AND dim_2_id != ''
AND dim_to_change IS NOT NULL
AND dim_to_change != ''
AND fk_tenant_id = @tenant_id
and export_id = @v2_export_id



DECLARE @CursorID_2 INT = 1;
DECLARE @RowCnt_2 BIGINT = 0;

SELECT @RowCnt_2 = COUNT(0) FROM #dim_combination_list

WHILE @CursorID_2 <= @RowCnt_2
BEGIN
	DECLARE @2_dim_1_type NVARCHAR(50);
	DECLARE @2_dim_2_type NVARCHAR(50);
	DECLARE @2_dim_id_1_old NVARCHAR(50);
	DECLARE @2_dim_id_2_old NVARCHAR(50);
	DECLARE @2_dim_type_to_change NVARCHAR(50);
	DECLARE @2_dim_id_new NVARCHAR(50);
	DECLARE @2_pk_id_error_table bigint;
	DECLARE @2_error_type NVARCHAR(300);

	SET @2_dim_1_type =			(select dim_1_type			from #dim_combination_list where row_id = @CursorID_2)
	SET @2_dim_2_type =			(select dim_2_type			from #dim_combination_list where row_id = @CursorID_2)
	SET @2_dim_id_1_old =		(select dim_1_id_old		from #dim_combination_list where row_id = @CursorID_2)
	SET @2_dim_id_2_old =		(select dim_2_id_old		from #dim_combination_list where row_id = @CursorID_2)
	SET @2_dim_type_to_change =	(select dim_type_to_change	from #dim_combination_list where row_id = @CursorID_2)
	SET @2_dim_id_new =			(select dim_id_new			from #dim_combination_list where row_id = @CursorID_2)
	SET @2_pk_id_error_table =	(select pk_id				from #dim_combination_list where row_id = @CursorID_2)
	SET @2_error_type =			(select fk_error_type		from #dim_combination_list where row_id = @CursorID_2)

	SET @update_string = ''

	SET @update_string = '	UPDATE #transactions_to_update SET to_update = 1, new_' + @2_dim_type_to_change + ' = ''' + @2_dim_id_new +''' WHERE ' + @2_dim_1_type + ' = ''' +  @2_dim_id_1_old + ''' AND ' + @2_dim_2_type + ' = ''' +  @2_dim_id_2_old + ''';
							UPDATE tbu_integ_error_handling SET fixed = 1 where	pk_id = '+ CONVERT(VARCHAR(100),@2_pk_id_error_table) +';
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
						   ([fk_tenant_id]
						   ,[export_id]
						   ,[batch_id]
						   ,[table_name]
						   ,[table_pk_id]
						   ,[fk_error_type]
						   ,[dim_1_type]
						   ,[dim_2_type]
						   ,[dim_1_id]
						   ,[dim_2_id]
						   ,[dim_to_change]
						   ,[id_new]
						   ,[update_time])
						   SELECT
						   [fk_tenant_id]
						   ,[export_id] = '+CONVERT(varchar(20),@v2_export_id)+'
						   ,[batch_id] = '''+@batch_id+'''
						   ,[table_name]
						   ,[table_pk_id] = pk_id
						   ,[fk_error_type] = '''+@2_error_type+'''
						   ,[dim_1_type] = '''+@2_dim_1_type+'''
						   ,[dim_2_type] = '''+@2_dim_2_type+'''
						   ,[dim_1_id] = '''+@2_dim_id_1_old+'''
						   ,[dim_2_id] = '''+@2_dim_id_2_old+'''
						   ,[dim_to_change] = '''+@2_dim_type_to_change+'''
						   ,[id_new] = '''+@2_dim_id_new+'''
						   ,[update_time] = getdate()
						   FROM #transactions_to_update
						   WHERE ' + @2_dim_1_type + ' = ''' +  @2_dim_id_1_old + ''' AND ' + @2_dim_2_type + ' = ''' +  @2_dim_id_2_old + ''';'
							
	

	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID_2 = @CursorID_2 + 1 
 
END


/******* BEGIN ACTUAL UPDATES *******/ 

DROP TABLE IF EXISTS #table_updates_list
select 
row_id = row_number()  OVER(order by table_name)
,table_name
INTO #table_updates_list
from #transactions_to_update
group by table_name

--DECLARE @CursorID INT
--DECLARE @RowCnt BIGINT
--DECLARE @update_string VARCHAR(MAX)

SET @CursorID = 1;
SET @RowCnt = 0;

SELECT @RowCnt = COUNT(0) FROM #table_updates_list

WHILE @CursorID <= @RowCnt
BEGIN
	DECLARE @table_name NVARCHAR(100);
	

	SET @table_name = (select table_name from #table_updates_list where row_id = @CursorID)


	SET @update_string = ''

	SET @update_string = '	UPDATE a SET 
							fk_account_code		= b.new_fk_account_code
						,	'+CASE WHEN @table_name = 'tfp_proj_transactions' OR @table_name = 'integnew_export_visma_posted_bud' THEN 'fk_department_code' ELSE 'department_code' END +' = b.new_fk_department_code	
						,	fk_function_code	= b.new_fk_function_code
						,	fk_project_code		= b.new_fk_project_code	
						,	free_dim_1			= b.new_free_dim_1		
						,	free_dim_2			= b.new_free_dim_2		
						,	free_dim_3			= b.new_free_dim_3		
						,	free_dim_4 			= b.new_free_dim_4 		
						FROM '+@table_name+' a
						JOIN #transactions_to_update b ON a.pk_id = b.pk_id
						WHERE b.table_name = '''+@table_name+''';'
							
	

	--PRINT @update_string
	EXEC sp_executesql @update_string
   
	SET @CursorID = @CursorID + 1 
 
END

/******* SET ERROR FIXED FOR BATCH IF ALL ERRORS FIXED *******/ 

DECLARE @total_rows INT
DECLARE @fixed_rows INT

SET @total_rows =	(
					select COUNT(*) from tbu_integ_error_handling
					where batch_id = @batch_id
					)

SET @fixed_rows =	(
					select COUNT(*) from tbu_integ_error_handling
					where batch_id = @batch_id
					AND ([keep] = 1 OR [fixed] = 1)
					)

IF @total_rows = @fixed_rows
BEGIN
	IF @tenant_type = 'Visma'
	BEGIN
		UPDATE integnew_export_visma_postbatches SET error_fixed = 1
		where batch_id = @batch_id
		AND fk_tenant_id = @tenant_id
	END

	IF @tenant_type = 'Agresso'
	BEGIN
		UPDATE integ_agrpostback_batches SET error_fixed = 1
		where Batchid = @batch_id
		AND fk_tenant_id = @tenant_id
	END
END
GO

