
CREATE OR ALTER PROC [dbo].[prcInitializeProjBudChangeToTFP] @forecast_period INT,  @fk_tenant_id INT, @user_id INT, @fk_user_adjustment_code [nvarchar](50),@fk_change_id [int],@fk_adjustment_code [nvarchar](50)

AS

DELETE FROM tfp_proj_transactions where fk_tenant_id = @fk_tenant_id and fk_user_adjustment_code = @fk_user_adjustment_code


select		[pk_id]
          ,pk_id_new = NEWID()
INTO #pk_mapping
FROM [tmr_proj_bud_changes]
where fk_tenant_id = @fk_tenant_id
and forecast_period = @forecast_period


INSERT INTO [dbo].[tfp_proj_transactions]
           ([pk_id]
           ,[trans_id]
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[fk_function_code]
           ,[fk_department_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[vat_rate]
           ,[vat_refund]
           ,[year]
           ,[amount]
           ,[updated]
           ,[updated_by]
           ,[fk_change_id]
           ,[fk_alter_code]
           ,[fk_adjustment_code]
           ,[is_vat_row]
           ,[fk_proj_trans_id]
           ,[description]
           ,[fk_user_adjustment_code])

select		[pk_id] = b.pk_id_new
           ,[trans_id]
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[fk_function_code]
           ,[fk_department_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[vat_rate]
           ,[vat_refund]
           ,[year]
           ,[amount]
           ,[updated] = getdate()
           ,[updated_by] = @user_id
           ,[fk_change_id] = @fk_change_id
           ,[fk_alter_code]
           ,[fk_adjustment_code] = @fk_adjustment_code
           ,[is_vat_row]
           ,[fk_proj_trans_id] = c.pk_id_new
           ,[description]
           ,[fk_user_adjustment_code] = @fk_user_adjustment_code
FROM [tmr_proj_bud_changes] a
LEFT JOIN #pk_mapping b on a.pk_id = b.pk_id
LEFT JOIN #pk_mapping c ON a.fk_proj_trans_id = c.pk_id
where fk_tenant_id = @fk_tenant_id
and forecast_period = @forecast_period
GO



