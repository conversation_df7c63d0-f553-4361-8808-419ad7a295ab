CREATE OR ALTER PROCEDURE [dbo].[prcValidateMrAction]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@forecast_period int
AS
--Clear all the error information and set default action type --
	UPDATE tmr_stage_action_import
	SET action_id_error = 0,
	    action_title_error = 0,
		account_code_error = 0, 
		department_code_error = 0,
		function_code_error = 0,
		project_code_error = 0,
		free_dim_1_error = 0,
		free_dim_2_error = 0,
		free_dim_3_error = 0,
		free_dim_4_error = 0,
	    adjustment_code_error = 0,
		alter_code_error = 0, 
		error_count = 0,
		periodicKey_error = 0,
		action_type = 60, 
		isManuallyAdded = 0
	WHERE tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period AND 
		tmr_stage_action_import.user_id = @user_id

	DECLARE @alter_code_table as TABLE (
	fk_tenant_id INT NOT NULL,
	fk_alter_code NVARCHAR(24) NOT NULL,
	action_type nvarchar(24) NOT NULL)

	INSERT INTO @alter_code_table (fk_tenant_id, fk_alter_code, action_type)
	SELECT ac.fk_tenant_id, ac.pk_alter_code, MIN(rv.attribute_value) as action_type  
	FROM tco_relation_values rv, tco_fp_alter_codes ac
	WHERE rv.fk_tenant_id = @tenant_id
	AND rv.attribute_type = 'ACTIONTYPE'
	AND rv.relation_type = 'ALTERCODE'
	AND rv.attribute_value = '60'
	AND ac.pk_alter_code BETWEEN rv.relation_value_from AND rv.relation_value_to
	AND ac.fk_tenant_id = rv.fk_tenant_id
	GROUP BY ac.fk_tenant_id, ac.pk_alter_code

	DECLARE @report1B varchar (25)
	SET @report1B = '54_DRIFTB'

	SELECT * 
	INTO #rep_line_1B
	FROM gmd_reporting_line
	WHERE report = @report1B

	DECLARE @report1A varchar(25)
	SET @report1A = '54_DRIFTA'


	
	UPDATE tmr_stage_action_import SET action_type = ac.action_type
	FROM tmr_stage_action_import imp, @alter_code_table ac
	WHERE 
	imp.fk_tenant_id = @tenant_id AND 
	imp.forecast_period = @forecast_period AND 
	imp.user_id = @user_id
	AND imp.alter_code = ac.fk_alter_code
	AND imp.fk_tenant_id = ac.fk_tenant_id;

	if (SELECT COUNT(*) FROM @alter_code_table WHERE fk_tenant_id = @tenant_id) = 1
	BEGIN 
	UPDATE tmr_stage_action_import SET alter_code = ac.fk_alter_code
	FROM tmr_stage_action_import imp, @alter_code_table ac
	WHERE 
	imp.fk_tenant_id = @tenant_id AND 
	imp.forecast_period = @forecast_period AND 
	imp.user_id = @user_id
	AND imp.fk_tenant_id = ac.fk_tenant_id;
	END

	-- Validate action _id --

	UPDATE tmr_stage_action_import
	SET action_id_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import imp
	LEFT JOIN tfp_temp_header th ON imp.action_id = th.pk_temp_id AND imp.fk_tenant_id = th.fk_tenant_id
	left join tfp_temp_detail td on th.fk_tenant_id = td.fk_tenant_id and th.pk_temp_id = td.fk_temp_id
	WHERE th.pk_temp_id IS NULL 
	and th.is_parked_action = 0
	AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period  AND
		imp.user_id = @user_id AND
		imp.action_id >= 1 

	-- Validate action_id against B List--

	UPDATE tmr_stage_action_import
	SET action_id_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import imp
	WHERE imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period  AND 
		imp.action_id not in (select pk_temp_id from tfp_temp_header where fk_Tenant_id = @tenant_id AND is_parked_action = 0) AND
		action_id_error <> 1 AND
		imp.user_id = @user_id AND
		imp.action_id >= 1 


 --Validate action_id againts current year 
   UPDATE tmr_stage_action_import
	SET action_id_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import imp
	WHERE imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period  AND
		imp.action_id NOT IN (select fk_temp_id from tfp_temp_detail where fk_Tenant_id = @tenant_id and budget_year = @budget_year and fk_temp_id = imp.action_id and forecast_period = @forecast_period) AND
		action_id_error <> 1 AND
		imp.user_id = @user_id AND
		imp.action_id >= 1 

	-- Validate action name --

	UPDATE tmr_stage_action_import
	SET action_title_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import imp
	WHERE (imp.action_id = 0
	AND imp.action_title = '' AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period AND 
		imp.user_id = @user_id)  OR
	(imp.action_id = 0
	AND imp.action_title IS NULL AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period AND 
		imp.user_id = @user_id)  OR
	(imp.action_id IS NULL
	AND imp.action_title IS NULL AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period AND 
		imp.user_id = @user_id)  OR
	(imp.action_id = 0
	AND imp.action_title IS NULL AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period AND 
		imp.user_id = @user_id)  OR
		(imp.action_id != 0
	AND DATALENGTH(imp.action_title) > 301  AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period AND 
		imp.user_id = @user_id)  OR
		(imp.action_id = 0
	AND DATALENGTH(imp.action_title) > 301  AND imp.fk_tenant_id = @tenant_id AND 
		imp.forecast_period = @forecast_period AND
		imp.user_id = @user_id);

	--Validate account codes--
	UPDATE tmr_stage_action_import
	SET account_code_error = 1, error_count = error_count + 1 
	FROM tmr_stage_action_import 
	LEFT JOIN tco_accounts acc on 
		tmr_stage_action_import.fk_tenant_id = acc.pk_tenant_id AND
		tmr_stage_action_import.account_code = acc.pk_account_code AND 		
		tmr_stage_action_import.budget_year BETWEEN datepart (year, acc.dateFrom) AND datepart (year, acc.dateTo) AND
		acc.isActive = 1 and acc.fk_kostra_account_code not in (select pk_kostra_account_code from gco_kostra_accounts where type='investment')
	WHERE acc.pk_account_code IS NULL AND 
		tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period AND 
		tmr_stage_action_import.user_id = @user_id

	--Validate department codes --
	UPDATE tmr_stage_action_import
	SET department_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_departments DEPTS ON 
		tmr_stage_action_import.fk_tenant_id = DEPTS.fk_tenant_id AND
		tmr_stage_action_import.department_code = DEPTS.pk_department_code AND 
		tmr_stage_action_import.budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND
		DEPTS.[status] = 1 AND
		@budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
	WHERE DEPTS.pk_department_code IS NULL AND 
		tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period AND 
		tmr_stage_action_import.user_id = @user_id;

    	--Validate Functions--
	UPDATE tmr_stage_action_import
	SET function_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_functions fn on 
		tmr_stage_action_import.fk_tenant_id = fn.pk_tenant_id AND
		tmr_stage_action_import.function_code = fn.pk_Function_code  AND 		
		tmr_stage_action_import.budget_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND
		fn.isActive = 1
	WHERE fn.pk_Function_code IS NULL AND 
		tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period AND  
		tmr_stage_action_import.user_id = @user_id

	--Validate Projects--
	UPDATE tmr_stage_action_import
		SET project_code_error = 1, error_count = error_count + 1
		FROM tmr_stage_action_import 
		LEFT JOIN tco_projects prj on 
			tmr_stage_action_import.fk_tenant_id = prj.fk_tenant_id AND
			tmr_stage_action_import.project_code = prj.pk_project_code AND 			
			tmr_stage_action_import.budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND
			prj.active = 1
		WHERE prj.pk_project_code IS NULL
		AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
			tmr_stage_action_import.forecast_period = @forecast_period AND 
			tmr_stage_action_import.user_id = @user_id
			AND tmr_stage_action_import.project_code != '' 
			AND tmr_stage_action_import.project_code IS NOT NULL 
			
	-- Validate alter code --

	UPDATE tmr_stage_action_import
	SET alter_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_fp_alter_codes alt on alt.pk_alter_code = tmr_stage_action_import.alter_code AND 
		tmr_stage_action_import.fk_tenant_id = alt.fk_tenant_id 
	WHERE alt.pk_alter_code IS NULL
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period  AND
		tmr_stage_action_import.user_id = @user_id;

	-- Validate free dim 1 --
	UPDATE tmr_stage_action_import
	SET free_dim_1_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_action_import.free_dim_1 AND 
		tmr_stage_action_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period AND 
		tmr_stage_action_import.user_id = @user_id
		AND tmr_stage_action_import.free_dim_1 != '' 
		AND tmr_stage_action_import.free_dim_1 IS NOT NULL;

	-- Validate free dim 2 --
	UPDATE tmr_stage_action_import
	SET free_dim_2_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_action_import.free_dim_2 AND 
		tmr_stage_action_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period AND 
		tmr_stage_action_import.user_id = @user_id
		AND tmr_stage_action_import.free_dim_2 != '' 
		AND tmr_stage_action_import.free_dim_2 IS NOT NULL; 

	-- Validate free dim 3 --
	UPDATE tmr_stage_action_import
	SET free_dim_3_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_action_import.free_dim_3 AND 
		tmr_stage_action_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period  AND
		tmr_stage_action_import.user_id = @user_id
		AND tmr_stage_action_import.free_dim_3 != '' 
		AND tmr_stage_action_import.free_dim_3 IS NOT NULL;

	-- Validate free dim 4 --

	UPDATE tmr_stage_action_import
	SET free_dim_4_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_action_import.free_dim_4 AND 
		tmr_stage_action_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
	WHERE frdm.free_dim_column IS NULL
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period  AND
		tmr_stage_action_import.user_id = @user_id AND
		tmr_stage_action_import.free_dim_4 != '' AND
		tmr_stage_action_import.free_dim_4 IS NOT NULL; 

	-- Validate adjustment code --
	UPDATE tmr_stage_action_import
	SET adjustment_code_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN tco_adjustment_codes adj on adj.pk_adjustment_code = tmr_stage_action_import.adjustment_code AND 
		tmr_stage_action_import.fk_tenant_id = adj.fk_tenant_id 
	WHERE adj.pk_adjustment_code IS NULL
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
		tmr_stage_action_import.forecast_period = @forecast_period  AND
		tmr_stage_action_import.user_id = @user_id
		AND tmr_stage_action_import.adjustment_code != '' 
		AND tmr_stage_action_import.adjustment_code IS NOT NULL; 

	--Validate if account is Central--
	-- If the parameter is set
	IF @forecast_period >= 202000 AND (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') > 0

	BEGIN
	print 'New logic for 1a 2020'

	UPDATE tmr_stage_action_import SET isCentralAccount = 1
	FROM  gmd_reporting_line c 
	LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
	LEFT OUTER JOIN  tmr_stage_action_import a ON a.account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
	AND a.fk_tenant_id = b.pk_tenant_id
	AND b.fk_kostra_account_code = c.fk_kostra_account_code
	AND a.department_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_DEPARTMENTS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
	AND c.line_group_id = 10
	WHERE  c.report = @report1A 

	UPDATE tmr_stage_action_import SET isCentralAccount = 1
	FROM  gmd_reporting_line c 
	LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
	LEFT OUTER JOIN  tmr_stage_action_import a ON a.account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
	AND a.fk_tenant_id = b.pk_tenant_id
	AND b.fk_kostra_account_code = c.fk_kostra_account_code
	AND a.function_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
	AND c.line_group_id = 10
	WHERE  c.report = @report1A 


	UPDATE tmr_stage_action_import SET isCentralAccount = 1
	FROM  gmd_reporting_line c 
	LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
	LEFT OUTER JOIN  tmr_stage_action_import a ON a.account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
	AND a.fk_tenant_id = b.pk_tenant_id
	AND b.fk_kostra_account_code = c.fk_kostra_account_code
	AND c.line_group_id != 10
	WHERE  c.report = @report1A 

	
	UPDATE a SET a.isCentralAccount = 0
	FROM tmr_stage_action_import a 
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.account_code = ac.pk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
	JOIN #rep_line_1B rl ON rl.report = @report1B AND rl.fk_kostra_account_code = ac.fk_kostra_account_code
	WHERE a.isCentralAccount = 1 
	AND a.department_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
	AND a.function_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_FUNCTIONS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
	AND a.fk_tenant_id = @tenant_id AND a.forecast_period = @forecast_period

	END

	-- If the parameter is not set
	IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') = 0 

	BEGIN 

		UPDATE tmr_stage_action_import
		SET isCentralAccount = 1
		FROM tmr_stage_action_import imp, tmd_finplan_line_setup ls
		WHERE imp.account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 0;

		UPDATE tmr_stage_action_import
		SET isCentralAccount = 1
		FROM tmr_stage_action_import imp, tmd_finplan_line_setup ls
		WHERE imp.account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 5;	
	
		UPDATE tmr_stage_action_import
		SET isCentralAccount = 1
		FROM tmr_stage_action_import imp, tmd_finplan_line_setup ls
		WHERE imp.account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 4;	
	
		UPDATE tmr_stage_action_import
		SET isCentralAccount = 1
		FROM tmr_stage_action_import imp, tmd_finplan_line_setup ls
		WHERE imp.account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 3;	
	
		UPDATE tmr_stage_action_import
		SET isCentralAccount = 1
		FROM tmr_stage_action_import imp, tmd_finplan_line_setup ls
		WHERE imp.account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 2;	
	
		UPDATE tmr_stage_action_import
		SET isCentralAccount = 1
		FROM tmr_stage_action_import imp, tmd_finplan_line_setup ls
		WHERE imp.account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 1;

	END


	-- Check that not all amounts is 0 --

	UPDATE tmr_stage_action_import SET year_1_amount_error = 1, error_count = error_count +1
	WHERE year_1_amount = 0
	AND year_2_amount = 0 
	AND year_3_amount = 0 
	AND year_4_amount = 0 
	AND tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
			tmr_stage_action_import.forecast_period = @forecast_period AND 
			tmr_stage_action_import.user_id = @user_id

	------ Update action title if the action Id is present -------
	UPDATE tmr_stage_action_import
	SET action_title = (SELECT description FROM tfp_temp_header where fk_tenant_id = fk_tenant_id and pk_temp_id = action_id)
	WHERE action_id != 0 AND action_title IS NULL

	-- UPDATING WITH DEFAULT PeriodicKey VALUES FOR NULL/BLANK ENTRIES 
	UPDATE tmr_stage_action_import set periodicKey = (Select  param_value FROM vw_tco_parameters with(nolock) where param_name='DEFAULT_BUDGET_PER_KEY' and fk_tenant_id=@tenant_id)
	WHERE tmr_stage_action_import.fk_tenant_id = @tenant_id AND 
			tmr_stage_action_import.forecast_period = @forecast_period AND 
			tmr_stage_action_import.user_id = @user_id AND
			(tmr_stage_action_import.periodicKey is NULL OR tmr_stage_action_import.periodicKey ='' ) ;

	-- Validate PeriodicKey If Periodic Key is not zero

	Select PeriodKeys.ID into #tempPeriodicKeys from 
	(select distinct key_id as ID,key_description from tco_periodic_key   where      fixed_flag  = 1 and     fk_tenant_id=0
	UNION ALL
	select distinct key_id,key_description from tco_periodic_key   where      fixed_flag  = 1 and     fk_tenant_id=@tenant_id
	UNION ALL
	select distinct key_id,key_description from tco_periodic_key   where   fixed_flag  <>  1 and     fk_tenant_id=0
	UNION ALL
	select distinct key_id,key_description from tco_periodic_key   where   fixed_flag  <>  1 and     fk_tenant_id=@tenant_id) PeriodKeys

			
	UPDATE tmr_stage_action_import
	SET periodicKey_error = 1, error_count = error_count + 1
	FROM tmr_stage_action_import 
	LEFT JOIN #tempPeriodicKeys frdm on CAST (frdm.ID as varchar(10)) = tmr_stage_action_import.periodicKey 
	where frdm.ID IS NULL and tmr_stage_action_import.fk_tenant_id = @tenant_id   and tmr_stage_action_import.budget_year = @budget_year   and tmr_stage_action_import.user_id =  @user_id  and tmr_stage_action_import.periodicKey != '0';

	DROP table #tempPeriodicKeys
RETURN 0
