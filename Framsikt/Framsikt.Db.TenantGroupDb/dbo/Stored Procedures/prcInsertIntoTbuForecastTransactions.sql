CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTbuForecastTransactions]
     (
		@udtForecastTran udtTbuForecastTransactionsType readonly
     )
AS
BEGIN
	insert into tbu_forecast_transactions 
	(
		   [pk_id] , [forecast_period] , [bu_trans_id] , [fk_tenant_id] , [action_type] , [line_order] , [fk_account_code] , [department_code] , [fk_function_code] , [fk_project_code] , [free_dim_1] , 
		   [free_dim_2] , [free_dim_3] , [free_dim_4] , [resource_id] , [fk_employment_id] , [description] , [budget_year] , [period] , [budget_type] , [amount_year_1] , [amount_year_2] , 
		   [amount_year_3] , [amount_year_4] , [fk_key_id] , [allocation_pct] , [total_amount] , [updated] , [updated_by] , [tax_flag] , [holiday_flag] , [fk_pension_type] , [fk_prog_code]
	)
	select [pk_id] , [forecast_period] , [bu_trans_id] , [fk_tenant_id] , [action_type] , [line_order] , fk_account_code ,department_code , fk_function_code ,fk_project_code ,free_dim_1 ,
		   free_dim_2 ,free_dim_3 ,free_dim_4 ,resource_id ,fk_employment_id ,description ,[budget_year] , [period] , [budget_type] , [amount_year_1] , [amount_year_2] , 
           [amount_year_3] , [amount_year_4] , fk_key_id,[allocation_pct] , [total_amount], [updated] , [updated_by] , [tax_flag] , [holiday_flag] , [fk_pension_type], [fk_prog_code]
	from @udtForecastTran
End