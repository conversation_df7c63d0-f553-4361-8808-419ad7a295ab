CREATE OR ALTER  PROCEDURE [dbo].[prcimportBudgetsFromStaging]
	@TenantID int,
	@isOriginal bit,
	@UserID int ,
	@budgetYear int,
	@jobID int,
	@isPeriodicKey bit,
	@userAdjCode  nvarchar(30)
AS
BEGIN	


		DECLARE @TenantID_1 int,@isOriginal_1 bit,@UserID_1 int,@budgetYear_1 int,@jobID_1 int,@deleteExistingRecords_1 bit,@isPeriodicKey_1 bit,@startId_1 bigint, @endId_1 bigint, @userAdjCode_1 nvarchar(30) 
		SET @TenantID_1 = @TenantID
		SET @isOriginal_1 = @isOriginal
		SET @UserID_1 = @UserID
		SET @budgetYear_1 = @budgetYear
		SET @jobID_1 = @jobID
		SET @isPeriodicKey_1 = @isPeriodicKey
		SET @userAdjCode_1 = @userAdjCode

		DECLARE @periods as table (period INT NOT NULL)

		INSERT INTO @periods (period)
		VALUES 
		(@budgetYear_1 * 100 + 1),
		(@budgetYear_1 * 100 + 2),
		(@budgetYear_1 * 100 + 3),
		(@budgetYear_1 * 100 + 4),
		(@budgetYear_1 * 100 + 5),
		(@budgetYear_1 * 100 + 6),
		(@budgetYear_1 * 100 + 7),
		(@budgetYear_1 * 100 + 8),
		(@budgetYear_1 * 100 + 9),
		(@budgetYear_1 * 100 + 10),
		(@budgetYear_1 * 100 + 11),
		(@budgetYear_1 * 100 + 12)

		declare	@timestamp datetime2

		select @timestamp = sysdatetime();
		PRINT 'START PROCESSING at ' + convert(nvarchar(19),@timestamp)

		declare @last_period int = convert(varchar(4), @budgetYear) + '12'

		DECLARE @defaultperiodicKey INT

		SET @defaultperiodicKey = (SELECT param_value FROM vw_tco_parameters where param_name='DEFAULT_BUDGET_PER_KEY' and fk_tenant_id=@TenantID);

		DECLARE @defaultinvProgram INT = (select top 1 pk_prog_code from [dbo].[tco_inv_program] where fk_tenant_id =@TenantID_1 and default_flag = 1)
		CREATE TABLE  #tbu_stage_budget_import (
		[rowNo] [bigint] IDENTITY(1,1) NOT NULL, [pk_id] [bigint] ,[user_id] [int] NOT NULL,[tenant_id] [int] NOT NULL,[budget_year] [int] NOT NULL,
		[account_code] [nvarchar](25) NULL DEFAULT (''),[department_code] [nvarchar](25) NULL DEFAULT (''),[function_code] [nvarchar](25) NULL DEFAULT (''),[project_code] [nvarchar](25) NULL DEFAULT (''),	
		[free_dim_1] [nvarchar](25) NULL DEFAULT (''),[free_dim_2] [nvarchar](25) NULL DEFAULT (''),[free_dim_3] [nvarchar](25) NULL DEFAULT (''),[free_dim_4] [nvarchar](25) NULL DEFAULT (''),
		[totalbudgetamount] [decimal](18, 2) NULL DEFAULT ((0)),[comments] [nvarchar](max) NULL DEFAULT (''),[period] [nvarchar](10) NULL DEFAULT (''),[periodicKey] [nvarchar](25) NULL DEFAULT (''),[account_code_error] [bit] NULL DEFAULT ((0)),
		[department_code_error] [bit] NULL DEFAULT ((0)),[function_code_error] [bit] NULL DEFAULT ((0)),[project_code_error] [bit] NULL DEFAULT ((0)),[free_dim_1_error] [bit] NULL DEFAULT ((0)),
		[free_dim_2_error] [bit] NULL DEFAULT ((0)),[free_dim_3_error] [bit] NULL DEFAULT ((0)),[free_dim_4_error] [bit] NULL DEFAULT ((0)),[periodicKey_error] [bit] NULL DEFAULT ((0)),[period_error] [bit] NULL DEFAULT ((0)),		
		[totalbudgetamount_error] [bit] NULL DEFAULT ((0)),[adjustment_code] [nvarchar](25) NULL DEFAULT (''),[alter_code] [nvarchar](25) NULL DEFAULT (''),[adjustment_code_error] [bit] NULL DEFAULT ((0)),
		[alter_code_error] [bit] NULL DEFAULT ((0)),[error_count] [int] NULL DEFAULT ((0)), [fk_investment_id] [int] DEFAULT ((0)),
		[fk_prog_code] NVARCHAR(25), [bu_trans_id] [uniqueidentifier] NULL CONSTRAINT [PK_tbu_stage_budget_importtemp] PRIMARY KEY ([rowNo])); 
		
		
			INSERT #tbu_stage_budget_import (pk_id,user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,
			free_dim_4,totalbudgetamount,comments,periodicKey,account_code_error,department_code_error,function_code_error,project_code_error,free_dim_1_error,
			free_dim_2_error,free_dim_3_error,free_dim_4_error,periodicKey_error,totalbudgetamount_error,error_count,period,period_error,adjustment_code,alter_code,
			adjustment_code_error,alter_code_error,fk_investment_id,fk_prog_code, [bu_trans_id])  
			SELECT   pk_id,user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,
			free_dim_4,totalbudgetamount,SUBSTRING (comments,1,700),periodicKey,account_code_error,department_code_error,function_code_error,project_code_error,free_dim_1_error,
			free_dim_2_error,free_dim_3_error,free_dim_4_error,periodicKey_error,totalbudgetamount_error,error_count,period,period_error,adjustment_code,alter_code,
			adjustment_code_error,alter_code_error,fk_investment_id ,NULL, newid() as [bu_trans_id]
			FROM  tbu_stage_budget_import 
			WHERE  tenant_id= @TenantID_1  AND [user_id]=@UserID_1 AND budget_year= @budgetYear_1 AND adjustment_code = @userAdjCode_1
			order by pk_id 
	
		UPDATE tco_job_status Set  steps_completed= 1  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1

		CREATE  TABLE #tbu_trans_detail(
		row_id_num int identity(1,1) not null,[pk_id] [uniqueidentifier] , [bu_trans_id] [uniqueidentifier] NULL, [fk_tenant_id] [int] NULL,[action_type] [int] NULL,[line_order] [int] NULL,[fk_account_code] [nvarchar](25) NULL,
		[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,
		[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[resource_id] [nvarchar](25) NULL,[fk_employment_id] [bigint] NULL,[description] [nvarchar](255) NULL,
		[budget_year] [int] NULL,[period] [int] NULL,[budget_type] [int] NULL,[amount_year_1] [decimal](18, 2) NULL,[amount_year_2] [decimal](18, 2) NULL,[amount_year_3] [decimal](18, 2) NULL,
		[amount_year_4] [decimal](18, 2) NULL,[fk_key_id] [int] NULL,[updated] [datetime] NULL,[updated_by] [int] NULL,[allocation_pct] [decimal](18, 10) NULL,[total_amount] [decimal](18, 2) NULL,
		[tax_flag] [int] NULL,[holiday_flag] [int] NULL,[fk_pension_type] [nvarchar](12) NULL,[fk_adjustment_code] [nvarchar](25) NULL,[fk_alter_code] [nvarchar](25) NULL,[fk_investment_id]  [int] NOT NULL DEFAULT ((0)),
		[fk_prog_code] NVARCHAR(25) NULL CONSTRAINT [PK_tbu_trans_detail_temp] PRIMARY KEY ([pk_id])  ) 

		CREATE  TABLE #tbu_trans_detail_original (
		row_id_num int identity(1,1) not null,[pk_id] [uniqueidentifier] , [bu_trans_id] [uniqueidentifier] NULL, [fk_tenant_id] [int] NULL,[action_type] [int] NULL,[line_order] [int] NULL,[fk_account_code] [nvarchar](25) NULL,
		[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,
		[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[resource_id] [nvarchar](25) NULL,[fk_employment_id] [bigint] NULL,[description] [nvarchar](255) NULL,
		[budget_year] [int] NULL,[period] [int] NULL,[budget_type] [int] NULL,[amount_year_1] [decimal](18, 2) NULL, [fk_key_id] [int] NULL,[updated] [datetime] NULL,[updated_by] [int] NULL,[allocation_pct] [decimal](18, 10) NULL
		,[total_amount] [decimal](18, 2) NULL,[tax_flag] [int] NULL,[holiday_flag] [int] NULL,[fk_pension_type] [nvarchar](12) NULL,[fk_adjustment_code] [nvarchar](25) NULL,[fk_alter_code] [nvarchar](25) NULL ,
		[fk_investment_id]  [int] NOT NULL DEFAULT ((0)), [fk_prog_code] NVARCHAR(25) NULL
		CONSTRAINT [PK_tbu_trans_detail_original_temp] PRIMARY KEY ([pk_id])) 

			

		update  #tbu_stage_budget_import set fk_investment_id = 0   where fk_investment_id is NULL;  
		update  #tbu_stage_budget_import set fk_prog_code = ''   where fk_prog_code is NULL;   
		 
		----------------end update investement id in staging tble ---------------------
		
		update #tbu_stage_budget_import set account_code = '' where account_code is null;
		update #tbu_stage_budget_import set department_code = '' where department_code is null;
		update #tbu_stage_budget_import set function_code = '' where function_code is null;
		update #tbu_stage_budget_import set project_code = '' where project_code is null;
		update #tbu_stage_budget_import set free_dim_1 = '' where free_dim_1 is null;
		update #tbu_stage_budget_import set free_dim_2 = '' where free_dim_2 is null;
		update #tbu_stage_budget_import set free_dim_3 = '' where free_dim_3 is null;
		update #tbu_stage_budget_import set free_dim_4 = '' where free_dim_4 is null;
		update #tbu_stage_budget_import set comments = '' where comments is null;
		update #tbu_stage_budget_import set adjustment_code = '' where adjustment_code is null;
		update #tbu_stage_budget_import set alter_code = '' where alter_code is null;

		update #tbu_stage_budget_import set account_code = TRIM(account_code);
		update #tbu_stage_budget_import set department_code = TRIM(department_code);
		update #tbu_stage_budget_import set function_code = TRIM(function_code);
		update #tbu_stage_budget_import set project_code = TRIM(project_code);
		update #tbu_stage_budget_import set free_dim_1 = TRIM(free_dim_1);
		update #tbu_stage_budget_import set free_dim_2 = TRIM(free_dim_2);
		update #tbu_stage_budget_import set free_dim_3 = TRIM(free_dim_3);
		update #tbu_stage_budget_import set free_dim_4 = TRIM(free_dim_4);
		update #tbu_stage_budget_import set comments = TRIM(comments);
		update #tbu_stage_budget_import set adjustment_code = TRIM(adjustment_code);
		update #tbu_stage_budget_import set alter_code = TRIM(alter_code);


	IF (@isPeriodicKey_1 = 1)
		begin
		print 'FINN allocation pct for de som ikke har nøkkel ' + convert (varchar(200),getdate())

		drop table if exists #acc_details

		 SELECT DISTINCT tenant_id as fk_tenant_id, budget_year,
		account_code as  [fk_account_code],[department_code], function_code as [fk_function_code], project_code as [fk_project_code],
		[free_dim_1],[free_dim_2],
				[free_dim_3],[free_dim_4]
		INTO #acc_details
		from #tbu_stage_budget_import

		--SELECT * FROM #acc_details

		DROP TABLE IF EXISTS #tbu_trans_detailForZero

			CREATE TABLE  #tbu_trans_detailForZero ([fk_account_code] [nvarchar](25) NULL,[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,
				[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[period] [int] NULL,[amount_year_1] [decimal](18, 2) NULL,
				[total_amount] [decimal](18, 2) NULL,[allocation_pct] [decimal](18, 3) NULL)

		INSERT INTO #tbu_trans_detailForZero (fk_account_code, department_code, fk_function_code, fk_project_code,
		free_dim_1, free_dim_2, free_dim_3, free_dim_4, period, amount_year_1, total_amount, allocation_pct)
		SELECT a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code,
		a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, a.period, SUM(a.amount_year_1),0,0
		FROM tbu_trans_detail a
		JOIN #acc_details b ON 
		a.fk_account_code = b.fk_account_code
		AND a.department_code = b.department_code
		AND a.fk_function_code = b.fk_function_code
		AND a.fk_project_code = b.fk_project_code
		AND a.free_dim_1 = b.free_dim_1
		AND a.free_dim_2 = b.free_dim_2
		AND a.free_dim_3 = b.free_dim_3 
		AND a.free_dim_4 = b.free_dim_4
		AND a.fk_tenant_id = b.fk_tenant_id
		AND a.budget_year = b.budget_year
		GROUP BY a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code,
		a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, a.period


		UPDATE a SET a.total_amount = b.total_amount
		FROM #tbu_trans_detailForZero a
		JOIN (SELECT fk_account_code, department_code, fk_function_code, fk_project_code,
		free_dim_1, free_dim_2, free_dim_3, free_dim_4, SUM (amount_year_1) as total_amount
		FROM #tbu_trans_detailForZero
		GROUP BY fk_account_code, department_code, fk_function_code, fk_project_code,
		free_dim_1, free_dim_2, free_dim_3, free_dim_4) b
		ON a.fk_account_code = b.fk_account_code
		AND a.department_code = b.department_code
		AND a.fk_function_code = b.fk_function_code
		AND a.fk_project_code = b.fk_project_code
		AND a.free_dim_1 = b.free_dim_1
		AND a.free_dim_2 = b.free_dim_2
		AND a.free_dim_3 = b.free_dim_3 
		AND a.free_dim_4 = b.free_dim_4

		DELETE FROM #tbu_trans_detailForZero WHERE total_amount BETWEEN -0.********* AND  0.*********

		UPDATE #tbu_trans_detailForZero SET allocation_pct = amount_year_1 / total_amount * 100

		UPDATE a SET periodicKey = @defaultperiodicKey
		FROM #tbu_stage_budget_import a
		WHERE NOT EXISTS (SELECT * FROM  #tbu_trans_detailForZero b
		where a.account_code = b.fk_account_code
		AND a.department_code = b.department_code
		AND a.function_code = b.fk_function_code
		AND a.project_code = b.fk_project_code
		AND a.free_dim_1 = b.free_dim_1
		AND a.free_dim_2 = b.free_dim_2
		AND a.free_dim_3 = b.free_dim_3 
		AND a.free_dim_4 = b.free_dim_4)
		AND a.periodicKey = 0

		
		print 'Ferdig: FINN allocation pct for de som ikke har nøkkel ' + convert (varchar(200),getdate())
		UPDATE tco_job_status Set  steps_completed= 5  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1

end



		IF (@isPeriodicKey_1 = 1)
			BEGIN
			
		print 'Begin get data for periodic key ' + convert (varchar(200),getdate())

					DROP TABLE if exists #hlp_tbu_trans_detail

					CREATE TABLE  #hlp_tbu_trans_detail  (
					[pk_id] [uniqueidentifier] NULL, [bu_trans_id] [uniqueidentifier] NOT NULL, [fk_tenant_id] [int] NULL,[action_type] [int] NULL,[line_order] [int] NULL,[fk_account_code] [nvarchar](25) NULL,
					[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,
					[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[resource_id] [nvarchar](25) NULL,[fk_employment_id] [bigint] NULL,[description] [nvarchar](max) NULL,
					[budget_year] [int] NULL,[period] [int] NULL,[budget_type] [int] NULL,[amount_year_1] [decimal](18, 2) NULL,[amount_year_2] [decimal](18, 2) NULL,[amount_year_3] [decimal](18, 2) NULL,
					[amount_year_4] [decimal](18, 2) NULL,[fk_key_id] [int] NULL,[updated] [datetime] NULL,[updated_by] [int] NULL,[allocation_pct] [decimal](18, 3) NULL,[total_amount] [decimal](18, 2) NULL,
					[tax_flag] [int] NULL,[holiday_flag] [int] NULL,[fk_pension_type] [nvarchar](12) NULL,[fk_adjustment_code] NVARCHAR(25) NULL,[alter_code] NVARCHAR (25) NULL, prefix_adjCode NVARCHAR (25) NULL)  


					DROP table if exists #per_key

					CREATE TABLE #per_key (key_id INT NOT  NULL,
					allocation_pct dec(18,3) NOT NULL, 
					period INT NOT NULL
					)

					CREATE unique index #ind_per_key_1 ON #per_key ( key_id, period)

					INSERT INTO #per_key (key_id, allocation_pct, period)
					select key_id, allocation_pct, 
					period = CASE 
						WHEN len(period) = 1 THEN convert(varchar(4), @budgetYear) + '0' + convert(varchar(2),period)
						ELSE convert(varchar(4), @budgetYear) + convert(varchar(2),period) END
					from tco_periodic_key
					WHERE fk_tenant_id IN (0,@TenantID)
					AND key_id IN (SELECT DISTINCT periodicKey FROM #tbu_stage_budget_import)


					INSERT INTO #per_key (key_id, allocation_pct, period)
					VALUES 
					(0, 0, @last_period-11),
					(0, 0, @last_period-10),
					(0, 0, @last_period-9),
					(0, 0, @last_period-8),
					(0, 0, @last_period-7),
					(0, 0, @last_period-6),
					(0, 0, @last_period-5),
					(0, 0, @last_period-4),
					(0, 0, @last_period-3),
					(0, 0, @last_period-2),
					(0, 0, @last_period-1),
					(0, 0, @last_period)

					INSERT #hlp_tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
									description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
									total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,alter_code )
									SELECT NEWID() AS pk_id,i.bu_trans_id,i.tenant_id, 5 as action_type, 0 as line_order,i.account_code, i.department_code, i.function_code, i.project_code, i.free_dim_1, i.free_dim_2, i.free_dim_3, i.free_dim_4,
									i.comments as description,'' as resource_id,0 as fk_employment_id,i.budget_year as budget_year,k.period as period,1 as budget_type,
									0 as amount_year_1,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,periodicKey as fk_key_id,GETUTCDATE() as updated,@UserID as updated_by,
									k.allocation_pct as allocation_pct,i.totalbudgetamount as total_amount,0 as tax_flag
									,0 as holiday_flag,'' as fk_pension_type,@userAdjCode_1 as fk_adjustment_code,alter_code 
					FROM #tbu_stage_budget_import i
					JOIN #per_key k ON i.periodicKey = k.key_id


					PRINT 'Set allocation pct where key is not used  ' + convert (varchar(200),getdate())

					UPDATE a SET a.allocation_pct = b.allocation_pct
					FROM #hlp_tbu_trans_detail a
					JOIN  #tbu_trans_detailForZero b
					ON a.fk_account_code = b.fk_account_code
					AND a.department_code = b.department_code
					AND a.fk_function_code = b.fk_function_code
					AND a.fk_project_code = b.fk_project_code
					AND a.free_dim_1 = b.free_dim_1
					AND a.free_dim_2 = b.free_dim_2
					AND a.free_dim_3 = b.free_dim_3 
					AND a.free_dim_4 = b.free_dim_4
					AND a.period = b.period
					WHERE a.fk_key_id = 0

					UPDATE #hlp_tbu_trans_detail SET amount_year_1 = ROUND(total_amount * allocation_pct/100,0)

					DROP TABLE IF EXISTS #temp_diff

					select A.bu_trans_id,A.amount_year_1-B.totalbudgetamount as diff
					INTO #temp_diff
					FROM
					(select bu_trans_id,SUM(amount_year_1)amount_year_1 from #hlp_tbu_trans_detail
					group by bu_trans_id) A
					JOIN
					(select bu_trans_id, totalbudgetamount  from #tbu_stage_budget_import) B ON A.bu_trans_id = B.bu_trans_id

					update #hlp_tbu_trans_detail set amount_year_1 = amount_year_1 - diff
					from #hlp_tbu_trans_detail A
					JOIN #temp_diff B on a.bu_Trans_id = b.bu_Trans_id
					where a.period = @last_period


					select @timestamp = sysdatetime();
					PRINT 'Periodic allocation part done '  + convert(nvarchar(19),@timestamp)
					UPDATE tco_job_status Set  steps_completed= 7  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1

						IF(@isOriginal_1 = 0)
							BEGIN
								INSERT #tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
								total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
								select pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
								total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,'' AS fk_alter_code,0 AS fk_investment_id,'' AS fk_prog_code 
								FROM #hlp_tbu_trans_detail

							END
						ELSE
							BEGIN 

								INSERT #tbu_trans_detail_original (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
								total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code)
								SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
								total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,'' AS fk_alter_code,0 AS fk_investment_id,'' AS fk_prog_code
								FROM #hlp_tbu_trans_detail
							END	
					
					select @timestamp = sysdatetime();
					PRINT 'insert into #tbu part done '  + convert(nvarchar(19),@timestamp)

					 
					UPDATE tco_job_status Set  steps_completed= 10  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1

					DROP TABLE IF EXISTS #PeriodicAllocation	


				IF(@isOriginal_1 = 0)
					BEGIN			
							begin
							INSERT tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code, fk_investment_id,fk_prog_code )
							SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1, 2),amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
							ROUND(total_amount,2),tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code  from #tbu_trans_detail
	
							UPDATE tco_job_status Set  steps_completed= 70  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1
						end
						
					END
				ELSE
					BEGIN

						begin
							INSERT tbu_trans_detail_original (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
							SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1, 2),fk_key_id,updated,updated_by,allocation_pct,
							ROUND(total_amount, 2),tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code ,fk_investment_id,fk_prog_code  from #tbu_trans_detail_original
	
							UPDATE tco_job_status Set  steps_completed= 70  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1
						end
						
					END 
					
					select @timestamp = sysdatetime();
					PRINT 'insert into tbu_tables part done '  + convert(nvarchar(19),@timestamp)


				IF(@isOriginal_1 = 0)
					BEGIN
						TRUNCATE TABLE #tbu_trans_detail
					END
				ELSE
					BEGIN
						TRUNCATE TABLE #tbu_trans_detail_original
					END 


				DROP TABLE IF EXISTS #tempPeriodicKeys
				
			END
		ELSE
			BEGIN
				
				select @timestamp = sysdatetime();
				PRINT 'Start finding accounting combinations '  + convert(nvarchar(19),@timestamp)


				CREATE  TABLE #combinations(
				row_id_num int identity(1,1) not null,bu_trans_id uniqueidentifier NULL,account_code nvarchar(25) NULL,department_code nvarchar(25) NULL,function_code nvarchar(25) NULL,
				project_code nvarchar(25) NULL,free_dim_1 nvarchar(25) NULL,free_dim_2 nvarchar(25) NULL,free_dim_3 nvarchar(25) NULL,free_dim_4 nvarchar(25) NULL,
				comments nvarchar(255) NULL,totalbudgetamount decimal(18, 2) NULL , adjustment_code nvarchar(25) NULL,alter_code nvarchar(25) NULL) 


				INSERT #combinations(bu_trans_id ,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,totalbudgetamount,adjustment_code,alter_code)
				SELECT newID() as bu_trans_id ,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,totalbudgetamount,
				 adjustment_code,alter_code from
				(select  account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,SUM(totalbudgetamount) as totalbudgetamount,
				adjustment_code,alter_code
				from #tbu_stage_budget_import where tenant_id = @TenantID_1 and   budget_year=@budgetYear_1
				group by account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,adjustment_code,alter_code)a 

				UPDATE tco_job_status Set  steps_completed= 7  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1

				IF(@isOriginal_1 = 0)
						BEGIN
							INSERT #tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,
							  free_dim_4, description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,
							  updated, updated_by,allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
							SELECT NEWID(), c.bu_trans_id ,t.tenant_id,5,0,t.account_code,t.department_code,t.function_code,t.project_code,t.free_dim_1,t.free_dim_2,t.free_dim_3,t.free_dim_4,
							t.comments,'',0,@budgetYear_1,t.period,1,SUM(t.totalbudgetamount),0,0,0,'',GETUTCDATE(),@UserID_1,
							0,							
							c.totalbudgetamount,0,0,'',t.adjustment_code,t.alter_code,t.fk_investment_id,t.fk_prog_code  
							from #tbu_stage_budget_import t
							join #combinations c
							ON 
							t.account_code= c.account_code AND 
							t.department_code= c.department_code AND
							t.function_code= c.function_code AND 
							t.project_code= c.project_code AND
							t.free_dim_1= c.free_dim_1 AND 
							t.free_dim_2= c.free_dim_2 AND
							t.free_dim_3= c.free_dim_3 AND 
							t.free_dim_4= c.free_dim_4 AND  
							t.comments = c.comments
							AND t.adjustment_code = c.adjustment_code
							AND t.alter_code = c.alter_code
							group by c.bu_trans_id ,t.tenant_id,t.account_code,t.department_code,t.function_code,t.project_code,t.free_dim_1,t.free_dim_2,t.free_dim_3,t.free_dim_4,
							t.comments,c.totalbudgetamount, t.period,t.adjustment_code,t.alter_code,t.fk_investment_id,t.fk_prog_code  

							INSERT #tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,
							  free_dim_4, description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,
							  updated, updated_by,allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
							SELECT NEWID(), c.bu_trans_id ,@TenantID_1,5,0,c.account_code,c.department_code,c.function_code,c.project_code, c.free_dim_1,c.free_dim_2,c.free_dim_3,c.free_dim_4,
							c.comments,'',0,@budgetYear_1,p.period,1,0,0,0,0,'',GETUTCDATE(),@UserID_1,
							0,							
							c.totalbudgetamount,0,0,'',c.adjustment_code,c.alter_code,0,''
							from @periods p
							join #combinations c ON 1=1
							WHERE NOT EXISTS ( select * from #tbu_stage_budget_import t WHERE
							t.account_code= c.account_code AND 
							t.department_code= c.department_code AND
							t.function_code= c.function_code AND 
							t.project_code= c.project_code AND
							t.free_dim_1= c.free_dim_1 AND 
							t.free_dim_2= c.free_dim_2 AND
							t.free_dim_3= c.free_dim_3 AND 
							t.free_dim_4= c.free_dim_4 AND  
							t.comments = c.comments
							AND t.adjustment_code = c.adjustment_code
							AND t.alter_code = c.alter_code
							AND p.period = t.period)
							group by c.bu_trans_id,c.account_code,c.department_code,c.function_code,c.project_code,c.free_dim_1,c.free_dim_2,c.free_dim_3,c.free_dim_4,
							c.comments,c.totalbudgetamount, p.period,c.adjustment_code,c.alter_code 

							
							UPDATE tco_job_status Set  steps_completed= 10  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1

						END
					ELSE
						BEGIN
							INSERT #tbu_trans_detail_original(pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,
							free_dim_4,description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,
							allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id ,fk_prog_code)
							
							SELECT NEWID(), c.bu_trans_id ,t.tenant_id,5,0,t.account_code,t.department_code,t.function_code,t.project_code,t.free_dim_1,t.free_dim_2,t.free_dim_3,t.free_dim_4,
							t.comments,'',0,@budgetYear_1,t.period,1,SUM(t.totalbudgetamount),'',GETUTCDATE(),@UserID_1,
							0,							
							c.totalbudgetamount,0,0,'',t.adjustment_code,t.alter_code,t.fk_investment_id,t.fk_prog_code  
							from #tbu_stage_budget_import t
							join #combinations c
							ON 
							t.account_code= c.account_code AND 
							t.department_code= c.department_code AND
							t.function_code= c.function_code AND 
							t.project_code= c.project_code AND
							t.free_dim_1= c.free_dim_1 AND 
							t.free_dim_2= c.free_dim_2 AND
							t.free_dim_3= c.free_dim_3 AND 
							t.free_dim_4= c.free_dim_4 AND  
							t.comments = c.comments
							AND t.adjustment_code = c.adjustment_code
							AND t.alter_code = c.alter_code
							group by c.bu_trans_id ,t.tenant_id,t.account_code,t.department_code,t.function_code,t.project_code,t.free_dim_1,t.free_dim_2,t.free_dim_3,t.free_dim_4,
							t.comments,c.totalbudgetamount, t.period,t.adjustment_code,t.alter_code,t.fk_investment_id,t.fk_prog_code  

							INSERT #tbu_trans_detail_original(pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,
							free_dim_4,description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,
							allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id ,fk_prog_code)
							SELECT NEWID(), c.bu_trans_id ,@TenantID_1,5,0,c.account_code,c.department_code,c.function_code,c.project_code, c.free_dim_1,c.free_dim_2,c.free_dim_3,c.free_dim_4,
							c.comments,'',0,@budgetYear_1,p.period,1,0,'',GETUTCDATE(),@UserID_1,
							0,							
							c.totalbudgetamount,0,0,'',c.adjustment_code,c.alter_code,0,''
							from @periods p
							join #combinations c ON 1=1
							WHERE NOT EXISTS ( select * from #tbu_stage_budget_import t WHERE
							t.account_code= c.account_code AND 
							t.department_code= c.department_code AND
							t.function_code= c.function_code AND 
							t.project_code= c.project_code AND
							t.free_dim_1= c.free_dim_1 AND 
							t.free_dim_2= c.free_dim_2 AND
							t.free_dim_3= c.free_dim_3 AND 
							t.free_dim_4= c.free_dim_4 AND  
							t.comments = c.comments
							AND t.adjustment_code = c.adjustment_code
							AND t.alter_code = c.alter_code
							AND p.period = t.period)
							group by c.bu_trans_id,c.account_code,c.department_code,c.function_code,c.project_code,c.free_dim_1,c.free_dim_2,c.free_dim_3,c.free_dim_4,
							c.comments,c.totalbudgetamount, p.period,c.adjustment_code,c.alter_code 

							
							UPDATE #tbu_trans_detail_original SET allocation_pct = amount_year_1 / total_amount * 100 WHERE total_amount NOT BETWEEN -0.********* AND  0.*********
						
							UPDATE tco_job_status Set  steps_completed= 10  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1


					
				END

				PRINT 'START INSERTING INTO tbu_Trans'

				IF(@isOriginal_1 = 0)
					BEGIN

						begin
							INSERT tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
							SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,
							amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
							ROUND(total_amount,0),tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code  from #tbu_trans_detail 
				
						UPDATE tco_job_status Set  steps_completed= 80  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1
						end						
					END
				ELSE
					BEGIN
						
						begin
							INSERT tbu_trans_detail_original (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
							total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
							SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
							description,resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1, 0),fk_key_id,updated,updated_by,allocation_pct,
							ROUND(total_amount, 0),tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code   from #tbu_trans_detail_original 
						
							
							UPDATE tco_job_status Set  steps_completed= 80  where fk_tenant_id = @TenantID_1 and pk_id= @jobID_1
						end
					END

					DROP TABLE IF EXISTS #combinations

					
			END	

			--DELETING PROCESSED RECORDS FROM STAGING TABLE
			--DELETE w FROM tbu_stage_budget_import w INNER JOIN #tbu_stage_budget_import e ON w.pk_id=e.pk_id


			DROP TABLE IF EXISTS #tbu_stage_budget_import
			DROP TABLE IF EXISTS #tbu_trans_detail
			DROP TABLE IF EXISTS #tbu_trans_detail_original
			DROP TABLE IF EXISTS #validAccountCodes
			DROP TABLE IF EXISTS #investmentsID

		update tco_user_adjustment_codes set status = 1 where fk_tenant_id = @TenantID and budget_year = @budgetYear and fk_user_id = @UserID and pk_adj_code = @userAdjCode_1 and org_level = 1
		
		select @timestamp = sysdatetime();
		PRINT 'ALL PROCESSESING DONE '  + convert(nvarchar(19),@timestamp)

END
