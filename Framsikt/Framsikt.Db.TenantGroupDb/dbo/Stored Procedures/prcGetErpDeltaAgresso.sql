CREATE OR ALTER PROCEDURE [dbo].[prcGetErpDeltaAgresso] 
( 
	@v1_export_id	BIGINT, 
	@v2_export_id	BIGINT 
) 
AS 
BEGIN 
 
--Get defaults and set the value  instead of empty 
DECLARE @defProject VARCHAR(50) 
DECLARE @defFreeDim1 VARCHAR(50) 
DECLARE @defFreeDim2 VARCHAR(50) 
DECLARE @defFreeDim3 VARCHAR(50) 
DECLARE @defFreeDim4 VARCHAR(50) 
DECLARE @defAlterCode VARCHAR(50) 
DECLARE @tenantId INT 
DECLARE @orgVersion VARCHAR(50) 
DECLARE @budgetYear VARCHAR(10)   
 
select top 1 @tenantId=tenant_id,@budgetYear=budget_year from tfp_erp_exports WHERE id = @v1_export_id 
 
select @orgVersion=pk_org_version from tco_org_version where fk_tenant_id=@tenantId and @budgetYear+'01' between period_from and period_to 
 
select @defProject= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='PROJECT' and fk_org_version= @orgVersion 
 
select @defFreeDim1= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_1' and fk_org_version= @orgVersion 
 
select @defFreeDim2= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_2' and fk_org_version= @orgVersion 
 
select @defFreeDim3= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_3' and fk_org_version= @orgVersion 
 
select @defFreeDim4= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='FREE_DIM_4' and fk_org_version= @orgVersion 
 
select @defAlterCode= acc_value from tmd_acc_defaults 
where fk_tenant_id=@tenantId and module='BU' and link_type='DEFAULT_POPUP' and link_value='YB' and active=1 and acc_type='ALTER_CODE' and fk_org_version= @orgVersion 

 
DECLARE @Param_Value NVARCHAR(10) 
SET @Param_Value='false' 
select @Param_Value=lower(param_value) from tco_parameters where param_name='ERP_SHOW_ADJ_ALTER_CODES' and fk_tenant_id = @tenantId and active = 1 
 
DECLARE @Param_Value_altercode NVARCHAR(10) 
SET @Param_Value_altercode='false' 
select @Param_Value_altercode=lower(param_value) from tco_parameters where param_name='ERP_FORCE_ALTER_CODES' and fk_tenant_id = @tenantId and active = 1 
 
DECLARE @Param_return_all NVARCHAR(10) 
SET @Param_return_all='false' 
select @Param_return_all=lower(param_value) from tco_parameters where param_name='ERP_RETURN_FULL_BUDGET' and fk_tenant_id = @tenantId and active = 1 

 
 
--Base data fetch for v1 and v2 data 
DROP TABLE IF EXISTS #temp_trans 
CREATE TABLE #temp_trans ( 
	[description] [nvarchar](700) NOT NULL, 
	[FkAccountCode] [nvarchar](25) NOT NULL, 
	[DepartmentCode] [nvarchar](25) NOT NULL, 
	[FkFunctionCode] [nvarchar](25) NOT NULL, 
	[FkProjectCode] [nvarchar](25) NOT NULL, 
	[FreeDim1] [nvarchar](25) NOT NULL, 
	[FreeDim2] [nvarchar](25) NOT NULL, 
	[FreeDim3] [nvarchar](25) NOT NULL, 
	[FreeDim4] [nvarchar](25) NOT NULL, 
	[FkAdjustmentCode] [nvarchar](25) NOT NULL, 
	[FkAlterCode] [nvarchar](25) NOT NULL, 
	[v1_Jan] [decimal](18, 2) NOT NULL, 
	[v1_Feb] [decimal](18, 2) NOT NULL, 
	[v1_Mar] [decimal](18, 2) NOT NULL, 
	[v1_Apr] [decimal](18, 2) NOT NULL, 
	[v1_May] [decimal](18, 2) NOT NULL, 
	[v1_Jun] [decimal](18, 2) NOT NULL, 
	[v1_Jul] [decimal](18, 2) NOT NULL, 
	[v1_Aug] [decimal](18, 2) NOT NULL, 
	[v1_Sep] [decimal](18, 2) NOT NULL, 
	[v1_Oct] [decimal](18, 2) NOT NULL, 
	[v1_Nov] [decimal](18, 2) NOT NULL, 
	[v1_Dec] [decimal](18, 2) NOT NULL, 
	[v2_Jan] [decimal](18, 2) NOT NULL, 
	[v2_Feb] [decimal](18, 2) NOT NULL, 
	[v2_Mar] [decimal](18, 2) NOT NULL, 
	[v2_Apr] [decimal](18, 2) NOT NULL, 
	[v2_May] [decimal](18, 2) NOT NULL, 
	[v2_Jun] [decimal](18, 2) NOT NULL, 
	[v2_Jul] [decimal](18, 2) NOT NULL, 
	[v2_Aug] [decimal](18, 2) NOT NULL, 
	[v2_Sep] [decimal](18, 2) NOT NULL, 
	[v2_Oct] [decimal](18, 2) NOT NULL, 
	[v2_Nov] [decimal](18, 2) NOT NULL, 
	[v2_Dec] [decimal](18, 2) NOT NULL, 
	[fk_attribute_id] [nvarchar](25) NOT NULL 
	) 
 
INSERT INTO #temp_trans 
SELECT	description = f.description 
		,FkAccountCode = fk_account_code 
		,DepartmentCode = department_code 
		,FkFunctionCode = fk_function_code 
		,FkProjectCode =	CASE WHEN fk_project_code ='' or fk_project_code IS NULL THEN isnull(@defProject,'') ELSE isnull(fk_project_code,'') END 
		,FreeDim1 =			CASE WHEN ISNULL(free_dim_1,'')='' or free_dim_1 is null  THEN isnull( @defFreeDim1,'') ELSE isnull( free_dim_1,'') END  
		,FreeDim2 =			CASE WHEN ISNULL(free_dim_2,'')='' or free_dim_2 is null  THEN isnull( @defFreeDim2,'') ELSE isnull( free_dim_2,'') END  
		,FreeDim3 =			CASE WHEN ISNULL(free_dim_3,'')='' or free_dim_3 is null  THEN isnull( @defFreeDim3,'') ELSE isnull( free_dim_3,'') END  
		,FreeDim4 =			CASE WHEN ISNULL(free_dim_4,'')='' or free_dim_4 is null  THEN isnull( @defFreeDim4,'') ELSE isnull( free_dim_4,'') END  
		,FkAdjustmentCode = CASE WHEN adj.prefix_adjCode = '' or adj.prefix_adjCode IS NULL THEN ISNULL(f.fk_adjustment_code,'') ELSE adj.prefix_adjCode END 
		,[FkAlterCode]    = CASE WHEN fk_alter_code='' or fk_alter_code is null THEN isnull(@defAlterCode,'') ELSE isnull(fk_alter_code,'')  END  
		,v1_Jan = jan 
		,v1_Feb = feb 
		,v1_Mar = mar 
		,v1_Apr = apr 
		,v1_May = may 
		,v1_Jun = jun 
		,v1_Jul = jul 
		,v1_Aug = aug 
		,v1_Sep = sep 
		,v1_Oct = oct 
		,v1_Nov = nov 
		,v1_Dec = dec 
		,v2_Jan = 0 
		,v2_Feb	= 0 
		,v2_Mar	= 0 
		,v2_Apr	= 0 
		,v2_May	= 0 
		,v2_Jun	= 0 
		,v2_Jul	= 0 
		,v2_Aug	= 0 
		,v2_Sep	= 0 
		,v2_Oct	= 0 
		,v2_Nov	= 0 
		,v2_Dec	= 0 
		,[fk_attribute_id] = ISNULL(adj.fk_attribute_id,'') 
FROM tfp_erp_export_log_agresso f  
LEFT JOIN tco_user_adjustment_codes adj ON f.fk_adjustment_code = adj.pk_adj_code AND adj.fk_tenant_id = @tenantId --AND adj.budget_year = @budgetYear 
WHERE export_id = @v1_export_id 
UNION ALL 
SELECT	description = f.description 
		,FkAccountCode = fk_account_code 
		,DepartmentCode = department_code 
		,FkFunctionCode = fk_function_code 
		,FkProjectCode =	CASE WHEN fk_project_code ='' or fk_project_code IS NULL THEN isnull(@defProject,'') ELSE isnull(fk_project_code,'') END 
		,FreeDim1 =			CASE WHEN ISNULL(free_dim_1,'')='' or free_dim_1 is null  THEN isnull( @defFreeDim1,'') ELSE isnull( free_dim_1,'') END  
		,FreeDim2 =			CASE WHEN ISNULL(free_dim_2,'')='' or free_dim_2 is null  THEN isnull( @defFreeDim2,'') ELSE isnull( free_dim_2,'') END  
		,FreeDim3 =			CASE WHEN ISNULL(free_dim_3,'')='' or free_dim_3 is null  THEN isnull( @defFreeDim3,'') ELSE isnull( free_dim_3,'') END  
		,FreeDim4 =			CASE WHEN ISNULL(free_dim_4,'')='' or free_dim_4 is null  THEN isnull( @defFreeDim4,'') ELSE isnull( free_dim_4,'') END  
		,FkAdjustmentCode = CASE WHEN adj.prefix_adjCode = '' or adj.prefix_adjCode IS NULL THEN ISNULL(f.fk_adjustment_code,'') ELSE adj.prefix_adjCode END 
		,[FkAlterCode]    = CASE WHEN fk_alter_code='' or fk_alter_code is null THEN isnull(@defAlterCode,'') ELSE isnull(fk_alter_code,'')  END  
		,v1_Jan = 0 
		,v1_Feb	= 0 
		,v1_Mar	= 0 
		,v1_Apr	= 0 
		,v1_May	= 0 
		,v1_Jun	= 0 
		,v1_Jul	= 0 
		,v1_Aug	= 0 
		,v1_Sep	= 0 
		,v1_Oct	= 0 
		,v1_Nov	= 0 
		,v1_Dec	= 0 
		,v2_Jan = jan 
		,v2_Feb = feb 
		,v2_Mar = mar 
		,v2_Apr = apr 
		,v2_May = may 
		,v2_Jun = jun 
		,v2_Jul = jul 
		,v2_Aug = aug 
		,v2_Sep = sep 
		,v2_Oct = oct 
		,v2_Nov = nov 
		,v2_Dec = dec 
		,[fk_attribute_id] = ISNULL(adj.fk_attribute_id,'') 
FROM tfp_erp_export_log_agresso f 
LEFT JOIN tco_user_adjustment_codes adj ON f.fk_adjustment_code = adj.pk_adj_code AND adj.fk_tenant_id = @tenantId --AND adj.budget_year = @budgetYear 
WHERE export_id = @v2_export_id 
 
--calculate delta changes 
 
DROP TABLE IF EXISTS #delta 
CREATE TABLE #delta ( 
	[description] [nvarchar](700) NOT NULL, 
	[FkAccountCode] [nvarchar](25) NOT NULL, 
	[DepartmentCode] [nvarchar](25) NOT NULL, 
	[FkFunctionCode] [nvarchar](25) NOT NULL, 
	[FkProjectCode] [nvarchar](25) NOT NULL, 
	[FreeDim1] [nvarchar](25) NOT NULL, 
	[FreeDim2] [nvarchar](25) NOT NULL, 
	[FreeDim3] [nvarchar](25) NOT NULL, 
	[FreeDim4] [nvarchar](25) NOT NULL, 
	[FkAdjustmentCode] [nvarchar](25) NOT NULL, 
	[FkAlterCode] [nvarchar](25) NOT NULL, 
	[jan] [decimal](18, 2) NOT NULL, 
	[feb] [decimal](18, 2) NOT NULL, 
	[mar] [decimal](18, 2) NOT NULL, 
	[apr] [decimal](18, 2) NOT NULL, 
	[may] [decimal](18, 2) NOT NULL, 
	[jun] [decimal](18, 2) NOT NULL, 
	[jul] [decimal](18, 2) NOT NULL, 
	[aug] [decimal](18, 2) NOT NULL, 
	[sep] [decimal](18, 2) NOT NULL, 
	[oct] [decimal](18, 2) NOT NULL, 
	[nov] [decimal](18, 2) NOT NULL, 
	[dec] [decimal](18, 2) NOT NULL, 
	[fk_attribute_id] [nvarchar](25) NOT NULL) 
 
	IF(@Param_Value ='true' AND @Param_Value_altercode='true' ) 
	 BEGIN 
        INSERT INTO #delta 
		SELECT *  
		FROM 
        (SELECT f.description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4 
        ,FkAdjustmentCode  
		,[FkAlterCode]     
		,SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec 
		,fk_attribute_id 
		FROM ( 
               SELECT	description 
						,FkAccountCode 
						,DepartmentCode 
						,FkFunctionCode 
						,FkProjectCode 
						,FreeDim1 
						,FreeDim2 
						,FreeDim3 
						,FreeDim4 
						,FkAdjustmentCode 
						,FkAlterCode 
						,Jan = SUM(v2_Jan-v1_Jan) 
						,Feb = SUM(v2_Feb-v1_Feb) 
						,Mar = SUM(v2_Mar-v1_Mar) 
						,Apr = SUM(v2_Apr-v1_Apr) 
						,May = SUM(v2_May-v1_May) 
						,Jun = SUM(v2_Jun-v1_Jun) 
						,Jul = SUM(v2_Jul-v1_Jul) 
						,Aug = SUM(v2_Aug-v1_Aug) 
						,Sep = SUM(v2_Sep-v1_Sep) 
						,Oct = SUM(v2_Oct-v1_Oct) 
						,Nov = SUM(v2_Nov-v1_Nov) 
						,Dec = SUM(v2_Dec-v1_Dec) 
						,fk_attribute_id 
				FROM #temp_trans TRANS 
				GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode,fk_attribute_id 
				HAVING	SUM(v2_Jan-v1_Jan) <> 0 OR 
						SUM(v2_Feb-v1_Feb) <> 0 OR 
						SUM(v2_Mar-v1_Mar) <> 0 OR 
						SUM(v2_Apr-v1_Apr) <> 0 OR 
						SUM(v2_May-v1_May) <> 0 OR 
						SUM(v2_Jun-v1_Jun) <> 0 OR 
						SUM(v2_Jul-v1_Jul) <> 0 OR 
						SUM(v2_Aug-v1_Aug) <> 0 OR 
						SUM(v2_Sep-v1_Sep) <> 0 OR 
						SUM(v2_Oct-v1_Oct) <> 0 OR 
						SUM(v2_Nov-v1_Nov) <> 0 OR 
						SUM(v2_Dec-v1_Dec) <> 0 
        ) f 
				 
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
        GROUP BY f.description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode,fk_attribute_id)  a 
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
    END 
 
	IF(@Param_Value ='true' AND @Param_Value_altercode='false') 
	 BEGIN 
        INSERT INTO #delta 
		SELECT *  
		FROM 
        (SELECT f.description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4 
        ,FkAdjustmentCode  
		,[FkAlterCode] = ''     
		,SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec 
		,fk_attribute_id 
		FROM ( 
               SELECT	description 
						,FkAccountCode 
						,DepartmentCode 
						,FkFunctionCode 
						,FkProjectCode 
						,FreeDim1 
						,FreeDim2 
						,FreeDim3 
						,FreeDim4 
						,FkAdjustmentCode 
						,FkAlterCode = '' 
						,Jan = SUM(v2_Jan-v1_Jan) 
						,Feb = SUM(v2_Feb-v1_Feb) 
						,Mar = SUM(v2_Mar-v1_Mar) 
						,Apr = SUM(v2_Apr-v1_Apr) 
						,May = SUM(v2_May-v1_May) 
						,Jun = SUM(v2_Jun-v1_Jun) 
						,Jul = SUM(v2_Jul-v1_Jul) 
						,Aug = SUM(v2_Aug-v1_Aug) 
						,Sep = SUM(v2_Sep-v1_Sep) 
						,Oct = SUM(v2_Oct-v1_Oct) 
						,Nov = SUM(v2_Nov-v1_Nov) 
						,Dec = SUM(v2_Dec-v1_Dec) 
						,fk_attribute_id 
				FROM #temp_trans TRANS 
				GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,fk_attribute_id 
				HAVING	SUM(v2_Jan-v1_Jan) <> 0 OR 
						SUM(v2_Feb-v1_Feb) <> 0 OR 
						SUM(v2_Mar-v1_Mar) <> 0 OR 
						SUM(v2_Apr-v1_Apr) <> 0 OR 
						SUM(v2_May-v1_May) <> 0 OR 
						SUM(v2_Jun-v1_Jun) <> 0 OR 
						SUM(v2_Jul-v1_Jul) <> 0 OR 
						SUM(v2_Aug-v1_Aug) <> 0 OR 
						SUM(v2_Sep-v1_Sep) <> 0 OR 
						SUM(v2_Oct-v1_Oct) <> 0 OR 
						SUM(v2_Nov-v1_Nov) <> 0 OR 
						SUM(v2_Dec-v1_Dec) <> 0 
        ) f 
				 
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
        GROUP BY f.description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,fk_attribute_id)  a 
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
    END 
 
    IF(@Param_Value ='false') 
     BEGIN 
	  
	 INSERT INTO #delta 
     SELECT *  
	 FROM 
        (SELECT f.description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4 
        ,FkAdjustmentCode = '' 
        ,FkAlterCode = '' 
        ,SUM(Jan)Jan,SUM(Feb)Feb,SUM(Mar)Mar,SUM(Apr)Apr,SUM(May)May,SUM(Jun)Jun,SUM(Jul)Jul,SUM(Aug)Aug,SUM(Sep)Sep,SUM(Oct)Oct,SUM(Nov)Nov,SUM(Dec)Dec 
		,fk_attribute_id 
		FROM ( 
               SELECT	description 
						,FkAccountCode 
						,DepartmentCode 
						,FkFunctionCode 
						,FkProjectCode 
						,FreeDim1 
						,FreeDim2 
						,FreeDim3 
						,FreeDim4 
						,FkAdjustmentCode 
						,FkAlterCode 
						,Jan = SUM(v2_Jan-v1_Jan) 
						,Feb = SUM(v2_Feb-v1_Feb) 
						,Mar = SUM(v2_Mar-v1_Mar) 
						,Apr = SUM(v2_Apr-v1_Apr) 
						,May = SUM(v2_May-v1_May) 
						,Jun = SUM(v2_Jun-v1_Jun) 
						,Jul = SUM(v2_Jul-v1_Jul) 
						,Aug = SUM(v2_Aug-v1_Aug) 
						,Sep = SUM(v2_Sep-v1_Sep) 
						,Oct = SUM(v2_Oct-v1_Oct) 
						,Nov = SUM(v2_Nov-v1_Nov) 
						,Dec = SUM(v2_Dec-v1_Dec) 
						,fk_attribute_id 
				FROM #temp_trans TRANS 
				GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,FkAdjustmentCode,FkAlterCode,fk_attribute_id 
				HAVING	SUM(v2_Jan-v1_Jan) <> 0 OR 
						SUM(v2_Feb-v1_Feb) <> 0 OR 
						SUM(v2_Mar-v1_Mar) <> 0 OR 
						SUM(v2_Apr-v1_Apr) <> 0 OR 
						SUM(v2_May-v1_May) <> 0 OR 
						SUM(v2_Jun-v1_Jun) <> 0 OR 
						SUM(v2_Jul-v1_Jul) <> 0 OR 
						SUM(v2_Aug-v1_Aug) <> 0 OR 
						SUM(v2_Sep-v1_Sep) <> 0 OR 
						SUM(v2_Oct-v1_Oct) <> 0 OR 
						SUM(v2_Nov-v1_Nov) <> 0 OR 
						SUM(v2_Dec-v1_Dec) <> 0 
        ) f      
 
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
        GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,fk_attribute_id)  a 
        WHERE jan != 0 OR feb != 0 OR mar != 0 OR apr != 0 OR may != 0 OR jun != 0  
        OR jul != 0 OR aug != 0 OR sep != 0 OR oct != 0 OR nov != 0 OR dec != 0 
 
    END 
END 
 
 
IF @Param_return_all = 'false' 
BEGIN 
	SELECT * FROM #delta 
END 
 
IF @Param_return_all = 'true' AND (SELECT COUNT(*) FROM #delta)>0 AND @Param_Value ='true' 
BEGIN 
	SELECT description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4 
        ,FkAdjustmentCode 
        ,FkAlterCode 
		,Jan = SUM(v2_Jan) 
		,Feb = SUM(v2_Feb) 
		,Mar = SUM(v2_Mar) 
		,Apr = SUM(v2_Apr) 
		,May = SUM(v2_May) 
		,Jun = SUM(v2_Jun) 
		,Jul = SUM(v2_Jul) 
		,Aug = SUM(v2_Aug) 
		,Sep = SUM(v2_Sep) 
		,Oct = SUM(v2_Oct) 
		,Nov = SUM(v2_Nov) 
		,Dec = SUM(v2_Dec) 
		,fk_attribute_id 
	FROM #temp_trans 
	GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4 
        ,FkAdjustmentCode 
        ,FkAlterCode 
		,fk_attribute_id 
END 
 
IF @Param_return_all = 'true' AND (SELECT COUNT(*) FROM #delta)>0 AND @Param_Value ='false' 
BEGIN 
	SELECT description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4 
        ,FkAdjustmentCode = '' 
        ,FkAlterCode = '' 
		,Jan = SUM(v2_Jan) 
		,Feb = SUM(v2_Feb) 
		,Mar = SUM(v2_Mar) 
		,Apr = SUM(v2_Apr) 
		,May = SUM(v2_May) 
		,Jun = SUM(v2_Jun) 
		,Jul = SUM(v2_Jul) 
		,Aug = SUM(v2_Aug) 
		,Sep = SUM(v2_Sep) 
		,Oct = SUM(v2_Oct) 
		,Nov = SUM(v2_Nov) 
		,Dec = SUM(v2_Dec) 
		,fk_attribute_id 
	FROM #temp_trans 
	GROUP BY description,FkAccountCode,DepartmentCode,FkFunctionCode,FkProjectCode,FreeDim1,FreeDim2,FreeDim3,FreeDim4,fk_attribute_id 
END 
GO