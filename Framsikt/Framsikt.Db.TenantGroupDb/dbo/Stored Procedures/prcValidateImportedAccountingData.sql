CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedAccountingData]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@jobID bigint
AS 

	--Clear all the error information and set default action type --
	UPDATE tbu_stage_accounting_import
	SET account_code_error = 0, 
		department_code_error = 0,
		function_code_error = 0,
		project_code_error = 0,
		free_dim_1_error = 0,
		free_dim_2_error = 0,
		free_dim_3_error = 0,
		free_dim_4_error = 0, 
		description_error = 0,
		period_error = 0,
		amount_error = 0,
		transaction_id_error = 0,
		transaction_date_error = 0,
		last_update_error = 0,
		invoice_date_error = 0,
		resource_error = 0,
		error_count = 0 
	WHERE tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year AND 
		tbu_stage_accounting_import.user_id = @user_id AND
		tbu_stage_accounting_import.job_Id = @jobID; 

	
	
	--Validate account codes--
	UPDATE tbu_stage_accounting_import
	SET account_code_error = 1, error_count = error_count + 1 
	FROM tbu_stage_accounting_import a
	LEFT JOIN tco_accounts acc on acc.pk_account_code = a.account_code AND 	a.fk_tenant_id = acc.pk_tenant_id 		
	WHERE a.fk_tenant_id = @tenant_id
	AND a.gl_year = @budget_year
	AND a.user_id = @user_id  
	AND a.job_Id = @jobID
	AND acc.pk_account_code IS NULL;

	--Validate department codes --
	UPDATE tbu_stage_accounting_import
	SET department_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import a
	LEFT JOIN tco_departments DEPTS ON DEPTS.pk_department_code = a.department_code AND a.fk_tenant_id = DEPTS.fk_tenant_id  AND @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
	WHERE a.fk_tenant_id = @tenant_id 
	AND	a.gl_year = @budget_year 
	AND	a.user_id = @user_id  
	AND a.job_Id = @jobID
	AND	DEPTS.pk_department_code IS NULL;

 
	--Validate Functions--
	UPDATE tbu_stage_accounting_import
	SET function_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import a
	LEFT JOIN tco_functions fn on fn.pk_Function_code = a.function_code AND a.fk_tenant_id = fn.pk_tenant_id  
	WHERE a.fk_tenant_id = @tenant_id 
	AND	a.gl_year = @budget_year 
	AND	a.user_id = @user_id  
	AND a.job_Id = @jobID
	AND fn.pk_Function_code IS NULL;

	--Validate Projects--

	UPDATE tbu_stage_accounting_import
	SET project_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import 
	LEFT JOIN tco_projects prj on prj.pk_project_code = tbu_stage_accounting_import.project_code AND 
		tbu_stage_accounting_import.fk_tenant_id = prj.fk_tenant_id  
	WHERE prj.pk_project_code IS NULL
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year  AND
		tbu_stage_accounting_import.user_id = @user_id  AND 
		tbu_stage_accounting_import.job_Id = @jobID AND
		tbu_stage_accounting_import.project_code != '' AND
		tbu_stage_accounting_import.project_code IS NOT NULL  

	--Validate Projects codes against Investment Accounts--
	UPDATE tbu_stage_accounting_import
	SET project_code_error = 1, error_count = error_count + 1 
	FROM tbu_stage_accounting_import 
	LEFT JOIN tco_accounts acc on tbu_stage_accounting_import.fk_tenant_id = acc.pk_tenant_id AND 
								  tbu_stage_accounting_import.account_code = acc.pk_account_code
	left outer join gco_kostra_accounts ka on acc.fk_kostra_account_code = ka.pk_kostra_account_code											
	WHERE tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year AND 
		tbu_stage_accounting_import.user_id = @user_id AND
		tbu_stage_accounting_import.job_Id = @jobID AND
		ka.type in ('investment') AND
		(tbu_stage_accounting_import.project_code is null or tbu_stage_accounting_import.project_code = '')


-- Validate free dim 1 --

	UPDATE tbu_stage_accounting_import
	SET free_dim_1_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_accounting_import.free_dim_1 AND 
		tbu_stage_accounting_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year  AND
		tbu_stage_accounting_import.user_id = @user_id  AND
		tbu_stage_accounting_import.job_Id = @jobID AND
		tbu_stage_accounting_import.free_dim_1 != '' AND
		tbu_stage_accounting_import.free_dim_1 IS NOT NULL;  

-- Validate free dim 2 --

	UPDATE tbu_stage_accounting_import
	SET free_dim_2_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_accounting_import.free_dim_2 AND 
		tbu_stage_accounting_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year  AND
		tbu_stage_accounting_import.user_id = @user_id  AND
		tbu_stage_accounting_import.job_Id = @jobID AND
		tbu_stage_accounting_import.free_dim_2 != '' AND
		tbu_stage_accounting_import.free_dim_2 IS NOT NULL;  

-- Validate free dim 3 --

	UPDATE tbu_stage_accounting_import
	SET free_dim_3_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_accounting_import.free_dim_3 AND 
		tbu_stage_accounting_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year  AND
		tbu_stage_accounting_import.user_id = @user_id  AND
		tbu_stage_accounting_import.job_Id = @jobID AND
		tbu_stage_accounting_import.free_dim_3 != '' AND
		tbu_stage_accounting_import.free_dim_3 IS NOT NULL;  

-- Validate free dim 4 --

	UPDATE tbu_stage_accounting_import
	SET free_dim_4_error = 1, error_count = error_count + 1
	FROM tbu_stage_accounting_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_accounting_import.free_dim_4 AND 
		tbu_stage_accounting_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
		tbu_stage_accounting_import.gl_year = @budget_year  AND
		tbu_stage_accounting_import.user_id = @user_id AND
		tbu_stage_accounting_import.job_Id = @jobID AND
		tbu_stage_accounting_import.free_dim_4 != '' AND
		tbu_stage_accounting_import.free_dim_4 IS NOT NULL; 
 

	-- Validate Periods
			
	SELECT a.years  INTO #tempYears FROM (
	SELECT  CAST(@budget_year as nvarchar(10))+'01' as years  UNION  SELECT CAST(@budget_year as nvarchar(10))+'02' 
	UNION  SELECT CAST(@budget_year as nvarchar(10))+'03' UNION  SELECT CAST(@budget_year as nvarchar(10))+'04'
	UNION  SELECT CAST(@budget_year as nvarchar(10))+'05' UNION  SELECT CAST(@budget_year as nvarchar(10))+'06'
	UNION  SELECT CAST(@budget_year as nvarchar(10))+'07' UNION  SELECT CAST(@budget_year as nvarchar(10))+'08'
	UNION  SELECT CAST(@budget_year as nvarchar(10))+'09' UNION  SELECT CAST(@budget_year as nvarchar(10))+'10'
	UNION  SELECT CAST(@budget_year as nvarchar(10))+'11' UNION  SELECT CAST(@budget_year as nvarchar(10))+'12' 
	)a

	UPDATE tbu_stage_accounting_import set period_error = 1, error_count = error_count + 1
	WHERE tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
			tbu_stage_accounting_import.gl_year = @budget_year AND 
			tbu_stage_accounting_import.user_id = @user_id AND
			tbu_stage_accounting_import.job_Id = @jobID AND
			(tbu_stage_accounting_import.period is NULL OR tbu_stage_accounting_import.period ='' OR tbu_stage_accounting_import.period  NOT IN (SELECT years from #tempYears)) ; 

	DROP TABLE #tempYears

	-- Check that total amounts is not 0 --

	UPDATE tbu_stage_accounting_import SET amount = 0
	WHERE amount is NULL
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
			tbu_stage_accounting_import.gl_year = @budget_year AND 
			tbu_stage_accounting_import.user_id = @user_id AND
			tbu_stage_accounting_import.job_Id = @jobID;


	-- Check if description is too long --

	UPDATE tbu_stage_accounting_import SET description_error = 1, error_count = error_count +1
	WHERE  (description IS NULL or len(description) >= 255)
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
			tbu_stage_accounting_import.gl_year = @budget_year AND 
			tbu_stage_accounting_import.user_id = @user_id AND
			tbu_stage_accounting_import.job_Id = @jobID;

	-- Check if resource is too long --
		UPDATE tbu_stage_accounting_import SET resource_error = 1, error_count = error_count +1
	WHERE  (resource IS NULL or len(resource) >= 50)
	AND tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
			tbu_stage_accounting_import.gl_year = @budget_year AND 
			tbu_stage_accounting_import.user_id = @user_id AND
			tbu_stage_accounting_import.job_Id = @jobID;


	--UPDATE PROGRAM CODE FROM TCO_PROJECTS
	--66300
	UPDATE ttd SET ttd.prog_code = tp.fk_prog_code from tbu_stage_accounting_import ttd
	join tco_projects tp on ttd.fk_tenant_id = tp.fk_tenant_id
	and ttd.project_code = tp.pk_project_code
	where ttd.fk_tenant_id=@tenant_id
	and  ttd.gl_year= @budget_year  and ttd.[user_id]=@user_id
	AND ttd.job_Id = @jobID
	and YEAR(tp.date_from) <= @budget_year and YEAR(tp.date_to) >= @budget_year

	-- UPDATE DEFAULT INV PROGRAM CODE FOR NULL OR EMPTY RECORDS
	DECLARE @defaultinvProgram INT = (select pk_prog_code from [dbo].[tco_inv_program] where fk_tenant_id =@tenant_id and default_flag = 1)
	UPDATE tbu_stage_accounting_import SET prog_code =  @defaultinvProgram
	WHERE  tbu_stage_accounting_import.fk_tenant_id = @tenant_id AND 
	tbu_stage_accounting_import.gl_year = @budget_year AND 
	tbu_stage_accounting_import.user_id = @user_id  AND
	tbu_stage_accounting_import.job_Id = @jobID AND
	(tbu_stage_accounting_import.prog_code is NULL OR tbu_stage_accounting_import.prog_code ='');


RETURN 0
