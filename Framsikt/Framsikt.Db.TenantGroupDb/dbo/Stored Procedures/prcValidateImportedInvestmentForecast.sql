
CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedInvestmentForecast]        
 @tenant_id int,        
 @user_id int,        
 @budget_year int,        
 @forecast_period int,
 @job_id int
AS        
             
 --Clear all the error information--  
 IF(@job_id = -1)
    BEGIN
         UPDATE tmr_stage_ISY_import        
         SET fk_project_code_error = 0,        
             fk_account_code_error = 0,        
          fk_department_code_error = 0,         
          fk_function_code_error = 0,        
          free_dim_1_error = 0,        
          free_dim_2_error = 0,        
          free_dim_3_error = 0,        
          free_dim_4_error = 0,        
          forecast_year_1_error = 0,        
          forecast_year_2_error = 0,         
          forecast_year_3_error = 0,        
          forecast_year_4_error = 0,       
          forecast_year_5_error = 0,    
          forecast_year_6_error = 0,    
          forecast_year_7_error = 0,    
          forecast_year_8_error = 0,    
          forecast_year_9_error = 0,    
          forecast_year_10_error = 0,    
          total_forecast_error = 0,   
          isy_update_date_error = 0,
          error_count = 0,
          approval_cost_error = 0
         WHERE tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
          tmr_stage_ISY_import.forecast_period = @forecast_period AND      
          tmr_stage_ISY_import.updated_by = @user_id;
    END
ELSE
    BEGIN
         UPDATE tmr_stage_ISY_import        
         SET fk_project_code_error = 0,        
             fk_account_code_error = 0,        
          fk_department_code_error = 0,         
          fk_function_code_error = 0,        
          free_dim_1_error = 0,        
          free_dim_2_error = 0,        
          free_dim_3_error = 0,        
          free_dim_4_error = 0,        
          forecast_year_1_error = 0,        
          forecast_year_2_error = 0,         
          forecast_year_3_error = 0,        
          forecast_year_4_error = 0,       
          forecast_year_5_error = 0,    
          forecast_year_6_error = 0,    
          forecast_year_7_error = 0,    
          forecast_year_8_error = 0,    
          forecast_year_9_error = 0,    
          forecast_year_10_error = 0,    
          total_forecast_error = 0,   
          isy_update_date_error = 0,
          error_count = 0,
          approval_cost_error = 0
         WHERE tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
          tmr_stage_ISY_import.forecast_period = @forecast_period AND      
          tmr_stage_ISY_import.fk_job_id = @job_id;
     END
    --Trim accounting data--  
    IF(@job_id = -1)
        BEGIN
             UPDATE tmr_stage_ISY_import        
             SET fk_account_code = RTRIM(LTRIM(fk_account_code)),        
             fk_department_code = RTRIM(LTRIM(fk_department_code)),        
             fk_function_code = RTRIM(LTRIM(fk_function_code)),        
             fk_project_code = RTRIM(LTRIM(fk_project_code)),        
             free_dim_1 = RTRIM(LTRIM(free_dim_1)),        
             free_dim_2 = RTRIM(LTRIM(free_dim_2)),        
             free_dim_3 = RTRIM(LTRIM(free_dim_3)),        
             free_dim_4 = RTRIM(LTRIM(free_dim_4))        
             WHERE tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
              tmr_stage_ISY_import.forecast_period = @forecast_period AND        
              tmr_stage_ISY_import.updated_by  = @user_id;
        END
    ELSE
        BEGIN
            UPDATE tmr_stage_ISY_import        
             SET fk_account_code = RTRIM(LTRIM(fk_account_code)),        
             fk_department_code = RTRIM(LTRIM(fk_department_code)),        
             fk_function_code = RTRIM(LTRIM(fk_function_code)),        
             fk_project_code = RTRIM(LTRIM(fk_project_code)),        
             free_dim_1 = RTRIM(LTRIM(free_dim_1)),        
             free_dim_2 = RTRIM(LTRIM(free_dim_2)),        
             free_dim_3 = RTRIM(LTRIM(free_dim_3)),        
             free_dim_4 = RTRIM(LTRIM(free_dim_4))        
             WHERE tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
              tmr_stage_ISY_import.forecast_period = @forecast_period AND        
              tmr_stage_ISY_import.fk_job_id  = @job_id;
        END
 --Validate account codes--    
     IF(@job_id = -1)
        BEGIN
             UPDATE tmr_stage_ISY_import        
             SET fk_account_code_error = 1, error_count = error_count + 1         
             FROM tmr_stage_ISY_import         
             LEFT JOIN tco_accounts acc on         
              tmr_stage_ISY_import.fk_tenant_id = acc.pk_tenant_id AND        
              tmr_stage_ISY_import.fk_account_code = acc.pk_account_code AND           
              @budget_year BETWEEN year(acc.dateFrom) AND year(acc.dateTo) AND        
              acc.isActive = 1 and acc.fk_kostra_account_code not in    
              (select pk_kostra_account_code from gco_kostra_accounts where type='operations')        
             WHERE acc.pk_account_code IS NULL AND         
              tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
              tmr_stage_ISY_import.forecast_period = @forecast_period AND        
              tmr_stage_ISY_import.updated_by = @user_id;
        END
     ELSE
        BEGIN
            UPDATE tmr_stage_ISY_import        
             SET fk_account_code_error = 1, error_count = error_count + 1         
             FROM tmr_stage_ISY_import         
             LEFT JOIN tco_accounts acc on         
              tmr_stage_ISY_import.fk_tenant_id = acc.pk_tenant_id AND        
              tmr_stage_ISY_import.fk_account_code = acc.pk_account_code AND           
              @budget_year BETWEEN year(acc.dateFrom) AND year(acc.dateTo) AND        
              acc.isActive = 1 and acc.fk_kostra_account_code not in    
              (select pk_kostra_account_code from gco_kostra_accounts where type='operations')        
             WHERE acc.pk_account_code IS NULL AND         
              tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
              tmr_stage_ISY_import.forecast_period = @forecast_period AND        
              tmr_stage_ISY_import.fk_job_id = @job_id;
        END
   --Validate department codes --  
       IF(@job_id = -1)
            BEGIN
                 UPDATE tmr_stage_ISY_import        
                 SET fk_department_code_error = 1, error_count = error_count + 1        
                 FROM tmr_stage_ISY_import         
                 LEFT JOIN tco_departments DEPTS ON         
                  tmr_stage_ISY_import.fk_tenant_id = DEPTS.fk_tenant_id AND        
                  tmr_stage_ISY_import.fk_department_code = DEPTS.pk_department_code AND         
                  @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND        
                  DEPTS.[status] = 1 AND        
                  @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to        
                 WHERE DEPTS.pk_department_code IS NULL AND         
                  tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND         
                  tmr_stage_ISY_import.updated_by = @user_id;
            END
        ELSE
            BEGIN
                 UPDATE tmr_stage_ISY_import        
                 SET fk_department_code_error = 1, error_count = error_count + 1        
                 FROM tmr_stage_ISY_import         
                 LEFT JOIN tco_departments DEPTS ON         
                  tmr_stage_ISY_import.fk_tenant_id = DEPTS.fk_tenant_id AND        
                  tmr_stage_ISY_import.fk_department_code = DEPTS.pk_department_code AND         
                  @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND        
                  DEPTS.[status] = 1 AND        
                  @budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to        
                 WHERE DEPTS.pk_department_code IS NULL AND         
                  tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND         
                  tmr_stage_ISY_import.fk_job_id = @job_id;
           END    
   --Validate Functions--   
       IF(@job_id = -1)
            BEGIN
                 UPDATE tmr_stage_ISY_import        
                 SET fk_function_code_error = 1 , error_count = error_count + 1        
                 FROM tmr_stage_ISY_import         
                 LEFT JOIN tco_functions fn on         
                  tmr_stage_ISY_import.fk_tenant_id = fn.pk_tenant_id AND        
                  tmr_stage_ISY_import.fk_function_code = fn.pk_Function_code  AND           
                  @budget_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND        
                  fn.isActive = 1        
                 WHERE fn.pk_Function_code IS NULL AND         
                  tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND         
                  tmr_stage_ISY_import.updated_by = @user_id
            END
        ELSE
            BEGIN
                 UPDATE tmr_stage_ISY_import        
                 SET fk_function_code_error = 1 , error_count = error_count + 1        
                 FROM tmr_stage_ISY_import         
                 LEFT JOIN tco_functions fn on         
                  tmr_stage_ISY_import.fk_tenant_id = fn.pk_tenant_id AND        
                  tmr_stage_ISY_import.fk_function_code = fn.pk_Function_code  AND           
                  @budget_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND        
                  fn.isActive = 1        
                 WHERE fn.pk_Function_code IS NULL AND         
                  tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND         
                  tmr_stage_ISY_import.fk_job_id = @job_id
            END
--Validate Projects--  
    IF(@job_id = -1)
        BEGIN
            UPDATE tmr_stage_ISY_import        
              SET fk_project_code_error = 1, error_count = error_count + 1        
              FROM tmr_stage_ISY_import         
              LEFT JOIN tco_projects prj on         
               tmr_stage_ISY_import.fk_tenant_id = prj.fk_tenant_id AND        
               tmr_stage_ISY_import.fk_project_code = prj.pk_project_code AND            
               @budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND        
               prj.active = 1        
              WHERE prj.pk_project_code IS NULL        
              AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
               tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
               tmr_stage_ISY_import.updated_by = @user_id        
               AND tmr_stage_ISY_import.fk_project_code != ''         
               AND tmr_stage_ISY_import.fk_project_code IS NOT NULL;
         END
    ELSE
        BEGIN
             UPDATE tmr_stage_ISY_import        
              SET fk_project_code_error = 1, error_count = error_count + 1        
              FROM tmr_stage_ISY_import         
              LEFT JOIN tco_projects prj on         
               tmr_stage_ISY_import.fk_tenant_id = prj.fk_tenant_id AND        
               tmr_stage_ISY_import.fk_project_code = prj.pk_project_code AND            
               @budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND        
               prj.active = 1        
              WHERE prj.pk_project_code IS NULL        
              AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
               tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
               tmr_stage_ISY_import.fk_job_id = @job_id        
               AND tmr_stage_ISY_import.fk_project_code != ''         
               AND tmr_stage_ISY_import.fk_project_code IS NOT NULL;
        END
  -- Validate free dim 1 --  
      IF(@job_id = -1)
            BEGIN
                 UPDATE tmr_stage_ISY_import      
                 SET free_dim_1_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_1 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.updated_by = @user_id        
                  AND tmr_stage_ISY_import.free_dim_1 != ''       
                  AND tmr_stage_ISY_import.free_dim_1 IS NOT NULL; 
           END
      ELSE
            BEGIN
                UPDATE tmr_stage_ISY_import      
                 SET free_dim_1_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_1 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.fk_job_id = @job_id        
                  AND tmr_stage_ISY_import.free_dim_1 != ''       
                  AND tmr_stage_ISY_import.free_dim_1 IS NOT NULL; 
            END
   -- Validate free dim 2 --  
       IF(@job_id = -1)
            BEGIN
                 UPDATE tmr_stage_ISY_import      
                 SET free_dim_2_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_2 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.updated_by = @user_id        
                  AND tmr_stage_ISY_import.free_dim_2 != ''       
                  AND tmr_stage_ISY_import.free_dim_2 IS NOT NULL;
            END
        ELSE
            BEGIN
                 UPDATE tmr_stage_ISY_import      
                 SET free_dim_2_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_2 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.fk_job_id = @job_id        
                  AND tmr_stage_ISY_import.free_dim_2 != ''       
                  AND tmr_stage_ISY_import.free_dim_2 IS NOT NULL;
            END
    -- Validate free dim 3 --  
        IF(@job_id = -1)
            BEGIN
                 UPDATE tmr_stage_ISY_import      
                 SET free_dim_2_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_3 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.updated_by = @user_id        
                  AND tmr_stage_ISY_import.free_dim_3 != ''       
                  AND tmr_stage_ISY_import.free_dim_3 IS NOT NULL;
           END
        ELSE
            BEGIN
                UPDATE tmr_stage_ISY_import      
                 SET free_dim_2_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_3 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.fk_job_id = @job_id        
                  AND tmr_stage_ISY_import.free_dim_3 != ''       
                  AND tmr_stage_ISY_import.free_dim_3 IS NOT NULL;
            END
    -- Validate free dim 4 -- 
        IF(@job_id = -1)
            BEGIN
                 UPDATE tmr_stage_ISY_import      
                 SET free_dim_4_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_4 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.updated_by = @user_id        
                  AND tmr_stage_ISY_import.free_dim_4 != ''       
                  AND tmr_stage_ISY_import.free_dim_4 IS NOT NULL;    
            END
        ELSE
            BEGIN
                 UPDATE tmr_stage_ISY_import      
                 SET free_dim_4_error = 1, error_count = error_count + 1      
                 FROM tmr_stage_ISY_import       
                 LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tmr_stage_ISY_import.free_dim_4 AND       
                  tmr_stage_ISY_import.fk_tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'      
                 WHERE frdm.free_dim_column IS NULL      
                 AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND       
                  tmr_stage_ISY_import.forecast_period = @forecast_period AND       
                  tmr_stage_ISY_import.fk_job_id = @job_id        
                  AND tmr_stage_ISY_import.free_dim_4 != ''       
                  AND tmr_stage_ISY_import.free_dim_4 IS NOT NULL;
            END
 -- Validate forecast year 1 --  
     IF(@job_id = -1)
        BEGIN
          UPDATE tmr_stage_ISY_import        
         SET forecast_year_1_error = 1  , error_count = error_count + 1     
         FROM tmr_stage_ISY_import           
         WHERE (ISNUMERIC(forecast_year_1) = 0)    
         AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
         tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
         tmr_stage_ISY_import.updated_by = @user_id
        END
    ELSE
        BEGIN
             UPDATE tmr_stage_ISY_import        
         SET forecast_year_1_error = 1  , error_count = error_count + 1     
         FROM tmr_stage_ISY_import           
         WHERE (ISNUMERIC(forecast_year_1) = 0)    
         AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
         tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
         tmr_stage_ISY_import.fk_job_id = @job_id
        END
 -- Validate forecast year 2 --        
 -- UPDATE tmr_stage_ISY_import        
 --SET forecast_year_2_error = 1      
 --FROM tmr_stage_ISY_import           
 --WHERE (forecast_year_1 = '' OR ISNUMERIC(forecast_year_2) = 0 OR try_parse(forecast_year_2 as decimal) is null)    
 --AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
 --tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
 --tmr_stage_ISY_import.updated_by = @user_id       
    
    
  -- Validate total forecast --  
      IF(@job_id = -1)
          BEGIN
              UPDATE tmr_stage_ISY_import        
         SET forecast_year_1_error = 1  , error_count = error_count + 1     
         FROM tmr_stage_ISY_import           
         WHERE (ISNUMERIC(forecast_year_1) = 0)    
         AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
         tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
         tmr_stage_ISY_import.updated_by = @user_id
        END
      ELSE
        BEGIN
             UPDATE tmr_stage_ISY_import        
         SET forecast_year_1_error = 1  , error_count = error_count + 1     
         FROM tmr_stage_ISY_import           
         WHERE (ISNUMERIC(forecast_year_1) = 0)    
         AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
         tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
         tmr_stage_ISY_import.fk_job_id = @job_id
        END
 
  -- Validate ISY update date
      IF(@job_id = -1)
          BEGIN
             UPDATE tmr_stage_ISY_import        
             SET isy_update_date_error = 1  , error_count = error_count + 1     
             FROM tmr_stage_ISY_import           
             WHERE tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
             tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
             tmr_stage_ISY_import.updated_by = @user_id AND
             TRY_CONVERT(DATETIME, ISY_update_date) IS NULL
          END
      ELSE
        BEGIN
            UPDATE tmr_stage_ISY_import        
            SET isy_update_date_error = 1  , error_count = error_count + 1     
            FROM tmr_stage_ISY_import           
            WHERE tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
            tmr_stage_ISY_import.forecast_period = @forecast_period  AND      
            tmr_stage_ISY_import.fk_job_id = @job_id AND
            TRY_CONVERT(DATETIME, ISY_update_date) IS NULL
        END

 -- -- Validate total forecast --        
 -- UPDATE tmr_stage_ISY_import        
 --SET approval_cost_error = 1  , error_count = error_count + 1     
 --FROM tmr_stage_ISY_import           
 --WHERE (ISNUMERIC(approval_cost) = 0)    
 --AND tmr_stage_ISY_import.fk_tenant_id = @tenant_id AND         
 --tmr_stage_ISY_import.forecast_period = @forecast_period  AND        
 --tmr_stage_ISY_import.updated_by = @user_id       
    
        
    
 RETURN 0 