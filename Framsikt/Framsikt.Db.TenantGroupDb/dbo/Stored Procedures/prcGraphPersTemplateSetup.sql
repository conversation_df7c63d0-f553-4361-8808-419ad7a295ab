-- =============================================
-- Description:	<procedure is being used by application when creating users. Must be maintaned in vso>
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[prcGraphPersTemplateSetup] 
	-- Add the parameters for the stored procedure here
	@tenant_id INT, 
	@user_id INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    INSERT INTO tco_section_config (fk_tenant_id, fk_template_id,fk_indicator_code,section_id,config,include_in_document,updated,updated_by)
	SELECT a.fk_tenant_id, a.pk_template_id,b.fk_indicator_code,b.section_id,b.config,b.include_in_document,getdate(),1000 
	FROM tko_user_templates a, gco_section_config b 
	WHERE a.fk_tenant_id = @tenant_id and a.fk_user_id = @user_id
	AND NOT EXISTS (SELECT * FROM tco_section_config c WHERE c.fk_tenant_id = a.fk_tenant_id AND c.fk_template_id = a.pk_template_id 
	AND c.fk_indicator_code = b.fk_indicator_code AND c.section_id = b.section_id)
	
END
GO
