CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTbuTransDetail]
     (
		@udtTransDetail udtTbuTransDetailType readonly
     )
AS
BEGIN
	insert into tbu_trans_detail 
	(
			[pk_id] , [bu_trans_id] , [fk_tenant_id] , [action_type] , [line_order] , [fk_account_code] , [department_code] , [fk_function_code] , [fk_project_code] , [free_dim_1] , 
			[free_dim_2] , [free_dim_3] , [free_dim_4] , [resource_id] , [fk_employment_id] , [description] , [budget_year] , [period] , [budget_type] , [amount_year_1] , [amount_year_2] , 
			[amount_year_3] , [amount_year_4] , [fk_key_id] , [updated] , [updated_by] , [allocation_pct] , [total_amount] , [tax_flag] , [holiday_flag] , [fk_pension_type] , [fk_action_id], [fk_investment_id],
			[fk_portfolio_code],[fk_alter_code],[fk_adjustment_code],[fk_prog_code]  
	)
	select [pk_id] , [bu_trans_id] , [fk_tenant_id] , [action_type] , [line_order] , fk_account_code ,department_code , fk_function_code ,fk_project_code ,free_dim_1 ,
			free_dim_2 ,free_dim_3 ,free_dim_4 ,resource_id ,fk_employment_id ,description ,[budget_year] , [period] , [budget_type] , [amount_year_1] , [amount_year_2] , 
			[amount_year_3] , [amount_year_4] , fk_key_id , [updated] , [updated_by] ,[allocation_pct] , [total_amount] , [tax_flag] , [holiday_flag] , [fk_pension_type], [fk_action_id], [fk_investment_id],
			[fk_portfolio_code],[fk_alter_code],[fk_adjustment_code],[fk_prog_code]  
	from @udtTransDetail
End