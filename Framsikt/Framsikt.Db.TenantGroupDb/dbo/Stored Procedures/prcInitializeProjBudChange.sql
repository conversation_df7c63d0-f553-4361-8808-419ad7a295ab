CREATE OR ALTER PROC [dbo].[prcInitializeProjBudChange] @forecast_period INT,  @fk_tenant_id INT, @user_id INT

AS


DECLARE @inv_forecast_type INT = (select inv_forecast_type from tmr_period_setup where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)

DECLARE @period INT = convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))

IF (@period < 13 AND @inv_forecast_type != 2)
BEGIN

delete from [tmr_proj_bud_changes]
where fk_tenant_id = @fk_tenant_id
and forecast_period = @forecast_period

INSERT INTO [dbo].[tmr_proj_bud_changes]
           ([pk_id]
           ,[trans_id]
           ,[fk_tenant_id]
           ,[forecast_period]
           ,[fk_account_code]
           ,[fk_function_code]
           ,[fk_department_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[vat_rate]
           ,[vat_refund]
           ,[year]
           ,[amount]
           ,[updated]
           ,[updated_by]
           ,[fk_alter_code]
           ,[is_vat_row]
           ,[fk_proj_trans_id]
           ,[description])
SELECT		[pk_id]
           ,[trans_id]
           ,[fk_tenant_id]
           ,[forecast_period]
           ,[fk_account_code]
           ,[fk_function_code]
           ,[fk_department_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[vat_rate]
           ,[vat_refund]
           ,[year]
           ,[amount]
           ,[updated] = getdate()
           ,[updated_by] = @user_id
           ,[fk_alter_code]
           ,[is_vat_row]
           ,[fk_proj_trans_id]
           ,[description]
from [dbo].[tmr_proj_transactions]
where fk_tenant_id = @fk_tenant_id
and forecast_period = @forecast_period
and is_change = 1
--and is_vat_row = 0

END

IF (@period = 13 OR @inv_forecast_type = 2)
BEGIN
	
	DECLARE @budget_year INT = @forecast_period / 100
	 	
	select 
	pk_main_project_code = ISNULL(MP.pk_main_project_code,'')
	,rl.line_item_id
	,PT.fk_account_code
	,PT.fk_department_code
	,PT.fk_function_code
	,PT.fk_project_code
	,PT.free_dim_1
	,PT.free_dim_2
	,PT.free_dim_3
	,PT.free_dim_4
	,SUM(pt.amount) as amount
	,owning_dep = MP.fk_department_code
	,owning_func = MP.fk_Function_code
	INTO #budget_data
	from tfp_proj_transactions PT
	LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
				select fk_tenant_id, pk_change_id from tfp_budget_changes
				where fk_tenant_id = @fk_tenant_id
				and budget_year <= @budget_year
		 )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	where PT.fk_tenant_id = @fk_tenant_id
	and (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
	and year = @budget_year
	GROUP BY MP.pk_main_project_code
	,rl.line_item_id
	,PT.fk_account_code,PT.fk_department_code,PT.fk_function_code,PT.fk_project_code,PT.free_dim_1,PT.free_dim_2,PT.free_dim_3,PT.free_dim_4
	,MP.fk_department_code,MP.fk_Function_code


	select 
	pk_main_project_code = ISNULL(MP.pk_main_project_code,'')
	,rl.line_item_id
	,PT.fk_account_code
	,fk_department_code = PT.department_code
	,PT.fk_function_code
	,PT.fk_project_code
	,PT.free_dim_1
	,PT.free_dim_2
	,PT.free_dim_3
	,PT.free_dim_4
	,SUM(pt.amount) as amount
	,owning_dep = MP.fk_department_code
	,owning_func = MP.fk_Function_code
	INTO #acc_data
	from tfp_accounting_data PT
	JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
	LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	WHERE PT.fk_tenant_id = @fk_tenant_id
	AND pt.gl_year = @budget_year
	GROUP BY MP.pk_main_project_code
	,rl.line_item_id
	,PT.fk_account_code,PT.department_code,PT.fk_function_code,PT.fk_project_code,PT.free_dim_1,PT.free_dim_2,PT.free_dim_3,PT.free_dim_4
	,MP.fk_department_code,MP.fk_Function_code


	/*********  BEGIN PROJECT MANIPULATION OF ACC DATA *********/

	DECLARE @project_rule INT = (select ISNULL(project,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on project the acc project is kept. If no match then the project with highest budget is used.
							
	*/

	IF @project_rule = 2
	BEGIN

	--Calculate max project within a given main project
	select * 
	INTO #max_project
	from 
	(
		select pk_main_project_code,fk_project_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by pk_main_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY pk_main_project_code,fk_project_code
	) CALC 
	WHERE rank = 1
	and pk_main_project_code != ''

	--Update with max project where there's no match between acc and budget
	update a set fk_project_code = c.fk_project_code 
	from #acc_data a
	LEFT JOIN #budget_data b ON a.pk_main_project_code = b.pk_main_project_code and a.fk_project_code = b.fk_project_code
	JOIN #max_project c ON a.pk_main_project_code = c.pk_main_project_code
	where b.fk_project_code IS NULL
	and a.pk_main_project_code != ''

	END



	/*********  BEGIN DEPARTMENT MANIPULATION OF ACC DATA *********/

	DECLARE @department_rule INT = (select ISNULL(department,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on department the acc dep is kept. If no match then the department with highest budget is used.
								- Rule 3 is using owning department. Fetched department from tco_main_project. If no dep is defined then the department i acc is kept.
	*/

	IF @department_rule = 2
	BEGIN

	--Calculate max department within a given project
	select * 
	INTO #max_department
	from 
	(
		select fk_project_code,fk_department_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,fk_department_code
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''

	--Update with max department where there's no match between acc and budget within a given project
	update a set fk_department_code = c.fk_department_code 
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code and a.fk_department_code = b.fk_department_code
	JOIN #max_department c ON a.fk_project_code = c.fk_project_code
	where b.fk_department_code IS NULL
	and a.fk_project_code != ''

	END

	IF @department_rule = 3
	BEGIN

	update #acc_data set fk_department_code = owning_dep
	where owning_dep IS NOT NULL
	and owning_dep != ''

	END

	/*********  BEGIN ACCOUNT MANIPULATION OF ACC DATA *********/

	DECLARE @account_rule INT = (select ISNULL(account,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on accounting the acc account is kept. 
									If no match then the account with highest budget is used.
									Match is on line item id and project
	*/

	IF @account_rule = 2
	BEGIN

	--Calculate max account within a given project and line item id
	select * 
	INTO #max_account
	from 
	(
		select fk_project_code,line_item_id,fk_account_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code,line_item_id ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,line_item_id,fk_account_code
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''

	--Update with max account where there's no match between acc and budget within a given project and line item id
	update a set fk_account_code = c.fk_account_code
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code and a.line_item_id = b.line_item_id AND a.fk_account_code = b.fk_account_code
	JOIN #max_account c ON a.fk_project_code = c.fk_project_code and a.line_item_id = c.line_item_id
	where b.fk_account_code IS NULL
	and a.fk_project_code != ''

	END

	/*********  BEGIN FUNCTION MANIPULATION OF ACC DATA *********/

	DECLARE @function_rule INT = (select ISNULL("function",0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on accounting the acc account is kept. 
									If no match then the account with highest budget is used.
									Match is on line item id and project
								- Rule 3 is owning function. Function from tco_main_projects is used where that is defined
	*/


	IF @function_rule = 2
	BEGIN

	--Calculate max function within a given project
	select * 
	INTO #max_function
	from 
	(
		select fk_project_code,fk_function_code,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,fk_function_code
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''


	--Update with max function where there's no match between acc and budget within a given project
	update a set fk_function_code = c.fk_function_code
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code AND a.fk_function_code = b.fk_function_code
	JOIN #max_function c ON a.fk_project_code = c.fk_project_code
	where b.fk_function_code IS NULL
	and a.fk_project_code != ''

	END

	IF @function_rule = 3
	BEGIN

	update #acc_data set fk_function_code = owning_func
	where owning_func IS NOT NULL
	and owning_func != ''

	END

	/*********  BEGIN free_dim_1 MANIPULATION OF ACC DATA *********/

	DECLARE @free_dim_1_rule INT = (select ISNULL(free_dim_1,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on free dim the acc free dim is kept. 
									If no match then the free dim with highest budget is used.
	*/


	IF @free_dim_1_rule = 2
	BEGIN

	--Calculate max function within a given project
	select * 
	INTO #max_free_dim_1
	from 
	(
		select fk_project_code,free_dim_1,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,free_dim_1
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''

	--Update with max free_dim where there's no match between acc and budget within a given project
	update a set free_dim_1 = c.free_dim_1
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code AND a.free_dim_1 = b.free_dim_1
	JOIN #max_free_dim_1 c ON a.fk_project_code = c.fk_project_code
	where b.free_dim_1 IS NULL
	and a.fk_project_code != ''

	END


	/*********  BEGIN free_dim_2 MANIPULATION OF ACC DATA *********/

	DECLARE @free_dim_2_rule INT = (select ISNULL(free_dim_2,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on free dim the acc free dim is kept. 
									If no match then the free dim with highest budget is used.
	*/


	IF @free_dim_2_rule = 2
	BEGIN

	--Calculate max function within a given project
	select * 
	INTO #max_free_dim_2
	from 
	(
		select fk_project_code,free_dim_2,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,free_dim_2
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''

	--Update with max free_dim where there's no match between acc and budget within a given project
	update a set free_dim_2 = c.free_dim_2
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code AND a.free_dim_2 = b.free_dim_2
	JOIN #max_free_dim_2 c ON a.fk_project_code = c.fk_project_code
	where b.free_dim_2 IS NULL
	and a.fk_project_code != ''

	END


	/*********  BEGIN free_dim_3 MANIPULATION OF ACC DATA *********/

	DECLARE @free_dim_3_rule INT = (select ISNULL(free_dim_3,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on free dim the acc free dim is kept. 
									If no match then the free dim with highest budget is used.
	*/


	IF @free_dim_3_rule = 2
	BEGIN

	--Calculate max function within a given project
	select * 
	INTO #max_free_dim_3
	from 
	(
		select fk_project_code,free_dim_3,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,free_dim_3
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''

	--Update with max free_dim where there's no match between acc and budget within a given project
	update a set free_dim_3 = c.free_dim_3
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code AND a.free_dim_3 = b.free_dim_3
	JOIN #max_free_dim_3 c ON a.fk_project_code = c.fk_project_code
	where b.free_dim_3 IS NULL
	and a.fk_project_code != ''

	END


	/*********  BEGIN free_dim_4 MANIPULATION OF ACC DATA *********/

	DECLARE @free_dim_4_rule INT = (select ISNULL(free_dim_4,0) from [tmr_rebud_aggr_rules] where fk_tenant_id = @fk_tenant_id and forecast_period = @forecast_period)
	/*	Rule classifications:	- Rule 0 is no rule, no changes done
								- Rule 1 is as in accounting (no changes done)
								- Rule 2 is as in budget. Where there's a match between accounting and budget on free dim the acc free dim is kept. 
									If no match then the free dim with highest budget is used.
	*/


	IF @free_dim_4_rule = 2
	BEGIN

	--Calculate max function within a given project
	select * 
	INTO #max_free_dim_4
	from 
	(
		select fk_project_code,free_dim_4,SUM(amount)amount, rank = ROW_NUMBER () OVER(partition by fk_project_code ORDER BY SUM(amount) desc)
		from #budget_data
		GROUP BY fk_project_code,free_dim_4
	) CALC 
	WHERE rank = 1
	and fk_project_code != ''

	--Update with max free_dim where there's no match between acc and budget within a given project
	update a set free_dim_4 = c.free_dim_4
	from #acc_data a
	LEFT JOIN #budget_data b ON a.fk_project_code = b.fk_project_code AND a.free_dim_4 = b.free_dim_4
	JOIN #max_free_dim_4 c ON a.fk_project_code = c.fk_project_code
	where b.free_dim_4 IS NULL
	and a.fk_project_code != ''

	END

	
	/*********  BEGIN FINAL CALCULATION FOR INSERT *********/
	

	CREATE TABLE #change_amount (
		[pk_main_project_code] NVARCHAR(25) NULL,
		[line_item_id] NVARCHAR(25) NULL,
		[fk_account_code] [nvarchar](25) NOT NULL,
		[fk_department_code] [nvarchar](25) NOT NULL,
		[fk_function_code] [nvarchar](25) NOT NULL,
		[fk_project_code] [nvarchar](25) NOT NULL,
		[free_dim_1] [nvarchar](25) NOT NULL,
		[free_dim_2] [nvarchar](25) NOT NULL,
		[free_dim_3] [nvarchar](25) NOT NULL,
		[free_dim_4] [nvarchar](25) NOT NULL,
		[trans_id] [uniqueidentifier] NOT NULL,
		[change_amount] [decimal](18, 2) NOT NULL,
		[vat_rate] [decimal](18, 2) NOT NULL,
		[vat_refund] [decimal](18, 2) NOT NULL,
		[is_vat_row] [bit] NOT NULL,
		[fk_proj_trans_id] [uniqueidentifier] NULL)
		
INSERT #change_amount
	select pk_main_project_code,line_item_id,
			fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id = newid()
			, change_amount = ROUND(SUM(acc_amount-budget_amount),0)
			,vat_rate = 0
			,vat_refund = 0
			,is_vat_row = 0
			,[fk_proj_trans_id] = newid()
	FROM (
		select pk_main_project_code,line_item_id,
		fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		,budget_amount = SUM(amount)
		,acc_amount = 0
		from #budget_data
		GROUP BY pk_main_project_code,line_item_id,
		fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		UNION ALL
		select pk_main_project_code,line_item_id,
		fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		,budget_amount = 0
		,acc_amount = SUM(amount)
		from #acc_data
		GROUP BY pk_main_project_code,line_item_id,
		fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		) TRANS
	GROUP BY pk_main_project_code,line_item_id,fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
	HAVING SUM(acc_amount-budget_amount) <> 0

	UPDATE #change_amount set [fk_proj_trans_id] = NULL

	--Vat row handling begins

	DECLARE @org_version VARCHAR(25) = (select pk_org_version from tco_org_version where fk_tenant_id = @fk_tenant_id and @budget_year*100+12 BETWEEN period_from and period_to)
	DECLARE @vat_account NVARCHAR(100) = (select acc_value from tmd_acc_defaults where fk_tenant_id = @fk_tenant_id and link_type like 'VAT_COST' and fk_org_version = @org_version)

--	--Try to update vat row with account from "parent row"
--	update a set fk_account_code = b.fk_account_code
--	from #change_amount a
--	JOIN (
--		--Try to find match on accounting combination and user account from this to update the vat row
--		select PT.* from #change_amount PT
--		JOIN tco_accounts ac ON @fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
--		JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
--		WHERE line_item_id = '1010'
--		and fk_account_code != @vat_account
--		) b ON a.fk_department_code = b.fk_department_code 
--			and a.fk_function_code = b.fk_function_code
--			and a.fk_project_code = b.fk_project_code 
--			and a.free_dim_1 = b.free_dim_1 
--			and a.free_dim_2 = b.free_dim_2
--			and a.free_dim_3 = b.free_dim_3
--			and a.free_dim_4 = b.free_dim_4
--	JOIN tco_projects P on @fk_tenant_id = P.fk_tenant_id and a.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
--	WHERE a.fk_account_code = @vat_account
--	and P.vat_rate != 0
--
--	--Update vat_rate for "parent" rows
--	select * from #change_amount PT
--	JOIN tco_accounts ac ON @fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
--	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
--	JOIN tco_projects P on @fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
--	WHERE rl.line_item_id = '1010'
--	and P.vat_rate != 0


	--Project list with vat (both setup and actual transactions)
	select p.pk_project_code,p.vat_rate,p.vat_refund, SUM(change_amount)total_vat
	INTO #vat_projects
	from #change_amount PT
	JOIN tco_accounts ac ON @fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
	JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
	JOIN tco_projects P on @fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	WHERE rl.line_item_id = '1010'
	and pt.fk_account_code = @vat_account
	and P.vat_rate != 0
	GROUP BY p.pk_project_code,p.vat_rate,p.vat_refund

	--Grossing the "parent row" with a calculated share of the total VAT on the project
	UPDATE PT set change_amount = ROUND(change_amount + (tv.total_vat * (PT.change_amount/pvl.total_ch_proj)),0)
				,vat_rate = tv.vat_rate
				,vat_refund = tv.vat_refund
	from #change_amount PT
	--JOIN tco_accounts ac ON @fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
	--JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
	JOIN #vat_projects tv ON PT.fk_project_code = tv.pk_project_code
	JOIN (--Fetch totals pr projects
		select pt.fk_project_code, SUM(change_amount)total_ch_proj from #change_amount PT
		--JOIN tco_accounts ac ON @fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
		--JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
		WHERE line_item_id = '1010'
		and fk_account_code != @vat_account
		GROUP BY pt.fk_project_code
		HAVING ABS(SUM(change_amount))>1
		) pvl ON pt.fk_project_code = pvl.fk_project_code
	WHERE line_item_id = '1010'
	and fk_account_code != @vat_account

	--delete vat rows that are now part of the parent row
	DELETE PT
	from #change_amount PT
	JOIN #vat_projects P on PT.fk_project_code = P.pk_project_code
	WHERE pt.fk_account_code = @vat_account

	ALTER TABLE #change_amount ADD [pk_id] [uniqueidentifier] NULL

	UPDATE #change_amount SET [pk_id] = NEWID()

	--Insert calculated VAT rows based on project setup
	INSERT INTO #change_amount
	(pk_main_project_code,line_item_id,
			fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id
			,change_amount
			,vat_rate
			,vat_refund
			,is_vat_row
			,[fk_proj_trans_id]
			, [pk_id]
	)
	select pk_main_project_code,line_item_id,
			fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id
			,change_amount = ROUND((change_amount / (1+vat_rate/100))*(vat_rate/100)*(vat_refund/100) * (-1),0)
			,vat_rate = 0
			,vat_refund = 0
			,is_vat_row = 1
			,[fk_proj_trans_id] = [pk_id]
			, [pk_id] = NEWID()
	from #change_amount
	where line_item_id = '1010'
	and vat_rate * vat_refund != 0
	and is_vat_row = 0

	INSERT INTO #change_amount
	(pk_main_project_code,line_item_id,
			fk_account_code
			,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id
			,change_amount
			,vat_rate
			,vat_refund
			,is_vat_row
			,[fk_proj_trans_id]
			, [pk_id]
	)
	select pk_main_project_code,line_item_id,
			fk_account_code = @vat_account
			,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id
			,change_amount = ROUND((change_amount / (1+vat_rate/100))*(vat_rate/100)*(vat_refund/100),0)
			,vat_rate = 0
			,vat_refund = 0
			,is_vat_row = 1
			,[fk_proj_trans_id] = [pk_id]
			, [pk_id] = NEWID()
	from #change_amount
	where line_item_id = '1010'
	and vat_rate * vat_refund != 0
	and is_vat_row = 0


	--Delete financing rows that should not be included
	DELETE From #change_amount
	where pk_main_project_code NOT IN ( --Main projects with transactions on these line item ids should retain all their transactions
		select pk_main_project_code From #change_amount
		where line_item_id IN ('1010','1020','1030','1040')
		and pk_main_project_code != ''
		GROUP BY pk_main_project_code
		)
	AND line_item_id NOT IN ('1010','1020','1030','1040')

	--Generate Y+1 transactions
	ALTER TABLE #change_amount ADD [year] INT NULL

	UPDATE #change_amount set [year] = @budget_year

	select pk_id, pk_id_new = NEWID() 
	INTO #new_pk_id
	FROM #change_amount GROUP BY pk_id

	INSERT INTO #change_amount
	(pk_main_project_code,line_item_id,
			fk_account_code
			,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id
			,change_amount
			,vat_rate
			,vat_refund
			,is_vat_row
			,[fk_proj_trans_id]
			,[pk_id]
			,[year])
	SELECT pk_main_project_code,line_item_id,
			fk_account_code
			,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
			,trans_id
			,change_amount =  change_amount * (-1)
			,vat_rate
			,vat_refund
			,is_vat_row
			,[fk_proj_trans_id] = c.pk_id_new
			,[pk_id] = b.pk_id_new
			,[year] = [YEAR] +1
	FROM #change_amount a
	LEFT JOIN #new_pk_id b on a.pk_id = b.pk_id
	LEFT JOIN #new_pk_id C on a.fk_proj_trans_id = c.pk_id

	--Round the data to closest 1000 if parameter is set
	IF (select param_value from vw_tco_parameters
		where fk_tenant_id = @fk_tenant_id
		and param_name = 'MR_PROJ_REBUDGET_ROUNDING'
		and param_value = 'TRUE'
		and active = 1
		)
	= 'TRUE'
	BEGIN
	UPDATE #change_amount set change_amount = ROUND(change_amount,-3)
	END


/*********  INSERT BEGINS *********/
	delete from [tmr_proj_bud_changes]
	where fk_tenant_id = @fk_tenant_id
	and forecast_period = @forecast_period

	--Insert current year amount
	INSERT INTO [dbo].[tmr_proj_bud_changes]
			   ([pk_id]
			   ,[trans_id]
			   ,[fk_tenant_id]
			   ,[forecast_period]
			   ,[fk_account_code]
			   ,[fk_function_code]
			   ,[fk_department_code]
			   ,[fk_project_code]
			   ,[free_dim_1]
			   ,[free_dim_2]
			   ,[free_dim_3]
			   ,[free_dim_4]
			   ,[vat_rate]
			   ,[vat_refund]
			   ,[year]
			   ,[amount]
			   ,[updated]
			   ,[updated_by]
			   ,[fk_alter_code]
			   ,[is_vat_row]
			   ,[fk_proj_trans_id]
			   ,[description])
	SELECT		[pk_id]
			   ,[trans_id]
			   ,[fk_tenant_id] = @fk_tenant_id
			   ,[forecast_period] = @forecast_period
			   ,[fk_account_code]
			   ,[fk_function_code]
			   ,[fk_department_code]
			   ,[fk_project_code]
			   ,[free_dim_1]
			   ,[free_dim_2]
			   ,[free_dim_3]
			   ,[free_dim_4]
			   ,[vat_rate]
			   ,[vat_refund]
			   ,[year]
			   ,[amount] = change_amount
			   ,[updated] = getdate()
			   ,[updated_by] = @user_id
			   ,[fk_alter_code] = ''
			   ,[is_vat_row]
			   ,[fk_proj_trans_id]
			   ,[description] = ''
	from #change_amount


END

