
CREATE OR ALTER PROCEDURE [dbo].[prcInitializeBudgetReport] @change_check  INT= 0, @tenant_id INT = 0
as 

DECLAR<PERSON> @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @timestamp datetime2
DECLARE @data_updated INT
DECLARE @current_year INT
DECLARE @next_year INT
DECLARE @continue INT
DECLARE @rowcount INT

DECLARE @changed_table TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);


select @current_year = (SELECT datepart(year, getdate()));
select @next_year = @current_year +1;

CREATE TABLE #TEMP_BUDGET (
	[fk_tenant_id] [int] NOT NULL DEFAULT 0,
	[budget_year] [int] NOT NULL DEFAULT 0,
	[fk_account_code] [nvarchar](25) NOT NULL DEFAULT '',
	[department_code] [nvarchar](25) NOT NULL DEFAULT '',
	[fk_function_code] [nvarchar](25) NOT NULL DEFAULT '',
	[fk_project_code] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_1] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_2] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_3] [nvarchar](25) NOT NULL DEFAULT '',
	[free_dim_4] [nvarchar](25) NOT NULL DEFAULT '',
	[resource_id] [nvarchar](25) NOT NULL DEFAULT '',
	[description] [nvarchar](255) NOT NULL DEFAULT '',
	[period] [int] NOT NULL DEFAULT 0,
	[allocation_pct] [decimal](18, 10) NOT NULL DEFAULT 0,
	[fk_key_id] [int] NOT NULL DEFAULT 0,
	[fk_investment_id] [int] NOT NULL DEFAULT 0,
	[fk_portfolio_code] [nvarchar](50) NOT NULL DEFAULT '',
	[fk_employment_id] [bigint] NOT NULL DEFAULT 0,
	[fk_adjustment_code] [nvarchar](50) NOT NULL DEFAULT '',
	[fk_alter_code] [nvarchar](25) NOT NULL DEFAULT '',
	[original_budget] [decimal](38, 2) NOT NULL DEFAULT 0,
	[revised_budget] [decimal](18, 2) NOT NULL DEFAULT 0,
	[cost_calc_budget] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_1_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_2_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_3_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[fp_year_4_amount] [decimal](18, 2) NOT NULL DEFAULT 0,
	[revised_budget_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
	[accounting_prev_year] [decimal](18, 2) NOT NULL DEFAULT 0,
	[finplan_changes_year_1] [decimal](18, 2) NOT NULL DEFAULT 0,
	[type_internal] [int] NOT NULL  DEFAULT 0,
	[fk_action_id] [int] NOT NULL DEFAULT 0,
	[fk_main_project_code] [nvarchar](25) NULL DEFAULT '',
	[ud_status] INT NULL DEFAULT 1
) ;

BEGIN
	UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'BUDGETFULL';
	select @timestamp = sysdatetime();
	select @StartDate = sysdatetime();

	PRINT 'Job starting at ' + convert(nvarchar(19),@timestamp)
END 


CREATE TABLE #operations_accounts (
fk_account_code varchar(24) NOT NULL,
fk_tenant_id INT NOT NULL)

INSERT INTO #operations_accounts (fk_account_code, fk_tenant_id)
SELECT DISTINCT ac.pk_account_code, ac.pk_tenant_id
FROM tco_accounts ac 
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'




IF @change_check = 0 AND @tenant_id = 0
BEGIN 

RAISERROR ('SELECT tenants and budget year and delete pending changes', 0, 1) WITH NOWAIT
	
	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@current_year-1 FROM tco_module_mapping WHERE fk_module_id = 4;

	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@current_year FROM tco_module_mapping WHERE fk_module_id = 4;

	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@next_year FROM tco_module_mapping WHERE fk_module_id = 4;

	DELETE FROM [dbo].[twh_temp_triggered_actions];
	DELETE FROM [dbo].[twh_temp_triggered_budget];
	DELETE FROM [dbo].[twh_temp_changed_actions];
	DELETE FROM [dbo].[twh_temp_changed_budget];
END

IF @change_check = 0 AND @tenant_id != 0
BEGIN
RAISERROR ('SELECT tenants and budget year and delete pending changes', 0, 1) WITH NOWAIT

	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	VALUES	
			(@tenant_id, @current_year-1),
			(@tenant_id, @current_year),
			(@tenant_id, @next_year)

DELETE T FROM [twh_temp_triggered_budget] T JOIN @changed_table C on T.fk_tenant_id = c.fk_tenant_id AND T.budget_year = C.budget_year;
DELETE T FROM [twh_temp_changed_budget] T JOIN @changed_table C on T.fk_tenant_id = c.fk_tenant_id AND T.budget_year = C.budget_year;

END






RAISERROR ('START : Fetch data for original budget into temp table', 0, 1) WITH NOWAIT

BEGIN

INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
fk_employment_id,fk_adjustment_code, original_budget,revised_budget, cost_calc_budget,
type_internal)
SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,'',period,0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id, '' as fk_portfolio_code,a.fk_employment_id,
'' as fk_adjustment_code,
SUM(amount_year_1) as original_budget, 
0 as revised_budget, 0 AS cost_calc_budget,
2 AS type_internal
FROM tbu_trans_detail_original a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year 
JOIN #operations_accounts ac ON a.fk_account_code = ac.fk_account_code AND a.fk_tenant_id = ac.fk_tenant_id
GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,period,a.fk_employment_id


END

select @timestamp = sysdatetime();
PRINT 'FINISH: Fetch data for original budget into temp table at ' + convert(nvarchar(19),@timestamp)

RAISERROR ('START : Fetch data for revised budget into temp table', 0, 1) WITH NOWAIT

BEGIN

INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
fk_employment_id,fk_adjustment_code, fk_alter_code,original_budget,revised_budget, cost_calc_budget,
type_internal)
SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
a.resource_id,SUBSTRING(a.description,1,255),a.period,allocation_pct,fk_key_id, a.fk_investment_id, isnull(a.fk_portfolio_code,''),
a.fk_employment_id,a.fk_adjustment_code,a.fk_alter_code,
0, SUM(amount_year_1), 0 as cost_calc_budget,
1 AS type_internal
FROM tbu_trans_detail a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year 
JOIN #operations_accounts ac ON a.fk_account_code = ac.fk_account_code AND a.fk_tenant_id = ac.fk_tenant_id
GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
a.resource_id,a.description,a.period,a.allocation_pct,a.fk_key_id, a.fk_investment_id, 
a.fk_portfolio_code,a.fk_employment_id, a.fk_adjustment_code, a.fk_alter_code



END

select @timestamp = sysdatetime();
PRINT 'FINISH: Fetch data for revised budget into temp table at ' + convert(nvarchar(19),@timestamp)


RAISERROR ('START : Fetch data for revised budget prev year into temp table', 0, 1) WITH NOWAIT

BEGIN

INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,fk_adjustment_code,
fk_employment_id,revised_budget_prev_year,
type_internal, ud_status)
SELECT a.fk_tenant_id,a.budget_year+1,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
a.resource_id,a.period+100,0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id,  '' as fk_portfolio_code,
'' as fk_adjustment_code,
a.fk_employment_id,SUM(amount_year_1), 
2 AS type_internal, ud.status
FROM tbu_trans_detail a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year -1
JOIN #operations_accounts ac ON a.fk_account_code = ac.fk_account_code AND a.fk_tenant_id = ac.fk_tenant_id
LEFT JOIN dbo.tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 0
GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,period,a.fk_employment_id, ud.status


DELETE FROM #TEMP_BUDGET WHERE ud_status = 0 

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Fetch data for revised budget prev year into temp table at ' + convert(nvarchar(19),@timestamp)



RAISERROR ('START : Fetch data for cost calc budget into temp table', 0, 1) WITH NOWAIT

BEGIN

INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
resource_id,description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
fk_employment_id,fk_adjustment_code, original_budget,revised_budget, cost_calc_budget,
type_internal)
SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
0 AS resource_id, '' AS description,a.budget_year*100+1 as period, 0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id, '' as fk_portfolio_code,
0 as fk_employment_id,'' as fk_adjustment_code,
0, 0, SUM(amount_year_1) as cost_calc_budget,
2 AS type_internal
FROM tbu_trans_detail_cost a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year 
JOIN #operations_accounts ac ON a.fk_account_code = ac.fk_account_code AND a.fk_tenant_id = ac.fk_tenant_id
GROUP BY   a.fk_tenant_id,a.budget_year,a.fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4



END

select @timestamp = sysdatetime();
PRINT 'FINISH: Fetch data for cost calc budget into temp table at ' + convert(nvarchar(19),@timestamp)


RAISERROR ('START : Fetch data for accounting into temp table', 0, 1) WITH NOWAIT

BEGIN

INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
period,accounting_prev_year,
type_internal)
SELECT a.fk_tenant_id, a.gl_year+1,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
a.period+100,
SUM(amount) as accounting_prev_year,
2 AS type_internal
FROM tfp_accounting_data a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.gl_year = ch.budget_year - 1
GROUP BY   a.fk_tenant_id, a.gl_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
a.period

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Fetch data for accounting into temp table at ' + convert(nvarchar(19),@timestamp)


RAISERROR ('START : Fetch data from finplan', 0, 1) WITH NOWAIT

BEGIN

INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
free_dim_1,free_dim_2,free_dim_3,free_dim_4,
description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
fk_adjustment_code, original_budget,revised_budget, cost_calc_budget,
type_internal,fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,
revised_budget_prev_year,accounting_prev_year,fk_action_id,fk_main_project_code,finplan_changes_year_1)

SELECT 
a.fk_tenant_id,b.budget_year,b.fk_account_code, b.department_code, b.function_code, b.project_code,
b.free_dim_1,b.free_dim_2,b.free_dim_3,b.free_dim_4,
'' as description,(b.budget_year*100) + 1 as period,0 as allocation_pct,0 as fk_key_id, 0 as fk_investment_id, 
'' as fk_portfolio_code,
b.fk_adjustment_code, 0 as original_budget,0 as revised_budget, 0 as cost_calc_budget,
3 as type_internal,SUM(b.year_1_amount),SUM(b.year_2_amount),
SUM(b.year_3_amount),SUM(b.year_4_amount),
0 as revised_budget_prev_year,0 as accounting_prev_year,a.pk_action_id,'' as fk_main_project_code,
finplan_changes_year_1 = CASE WHEN bc.org_budget_flag = 1 THEN SUM(b.year_1_amount) ELSE 0 END
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.pk_action_id = b.fk_action_id AND a.fk_tenant_id = b.fk_tenant_id
JOIN @changed_table ch ON b.fk_tenant_id = ch.fk_tenant_id AND b.budget_year = ch.budget_year 
LEFT JOIN tfp_budget_changes bc ON b.fk_tenant_id = bc.fk_tenant_id AND b.fk_change_id = bc.pk_change_id AND b.budget_year = bc.budget_year
GROUP BY 
a.fk_tenant_id,b.budget_year,b.fk_account_code, b.department_code, b.function_code, b.project_code,
b.free_dim_1,b.free_dim_2,b.free_dim_3,b.free_dim_4,b.budget_year,
b.fk_adjustment_code,a.pk_action_id,bc.org_budget_flag

END

select @timestamp = sysdatetime();
PRINT 'FINISH: fetching data from finplan at ' + convert(nvarchar(19),@timestamp)
RAISERROR ('START : Fetch data from investments', 0, 1) WITH NOWAIT

--BEGIN

----Removed as part of bug 70717

--CREATE TABLE #inv_bud_hlptab (
--	[fk_tenant_id] [int] NOT NULL DEFAULT 0,
--	[year] [int] NOT NULL DEFAULT 0,
--	[budget_year] [int] NOT NULL DEFAULT 0,
--	[fk_account_code] [nvarchar](25) NOT NULL DEFAULT '',
--	[department_code] [nvarchar](25) NOT NULL DEFAULT '',
--	[fk_function_code] [nvarchar](25) NOT NULL DEFAULT '',
--	[fk_project_code] [nvarchar](25) NOT NULL DEFAULT '',
--	[free_dim_1] [nvarchar](25) NOT NULL DEFAULT '',
--	[free_dim_2] [nvarchar](25) NOT NULL DEFAULT '',
--	[free_dim_3] [nvarchar](25) NOT NULL DEFAULT '',
--	[free_dim_4] [nvarchar](25) NOT NULL DEFAULT '',
--	[fk_adjustment_code] [nvarchar](50) NOT NULL DEFAULT '',
--	[fk_alter_code] [nvarchar](25) NOT NULL DEFAULT '',
--	[fk_main_project_code] [nvarchar](25) NOT NULL DEFAULT '',
--	[org_budget_flag] [int] NOT NULL DEFAULT 0,
--	[inv_status] [int] NOT NULL DEFAULT 0,
--	[fk_change_id] [int] NOT NULL DEFAULT 0, 
--	[amount] dec(18,2) NOT NULL DEFAULT 0
--) ;

--INSERT INTO #inv_bud_hlptab (fk_tenant_id, year,budget_year, fk_account_code, 
--department_code, fk_function_code, fk_project_code,free_dim_1, free_dim_2,free_dim_3, free_dim_4,
--fk_adjustment_code,fk_alter_code, fk_main_project_code, org_budget_flag, inv_status,fk_change_id, amount)
--SELECT PT.fk_tenant_id, PT.year, @current_year, pt.fk_account_code, 
--pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,pt.free_dim_1, pt.free_dim_2, pt.free_dim_3, pt.free_dim_4,
--pt.fk_adjustment_code, pt.fk_alter_code, ISNULL(mp.pk_main_project_code,''), bc.org_budget_flag, ISNULL(mp.inv_status,0), pt.fk_change_id, 
--SUM(pt.amount)
--FROM tfp_proj_transactions PT
--LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @current_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
--LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @current_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
--LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
--JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
--JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
--        select fk_tenant_id, pk_change_id, 1 as org_budget_flag from tfp_budget_changes
--        where fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM @changed_table)
--        and budget_year < @current_year
--        UNION
--        select fk_tenant_id, pk_change_id, org_budget_flag from tfp_budget_changes
--        where fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM @changed_table)
--        and budget_year = @current_year
--    )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
--where PT.fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM @changed_table)
--GROUP BY PT.fk_tenant_id, PT.year, pt.fk_account_code, 
--pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,pt.free_dim_1, pt.free_dim_2, pt.free_dim_3, pt.free_dim_4,
--pt.fk_adjustment_code, pt.fk_alter_code, mp.pk_main_project_code, bc.org_budget_flag, mp.inv_status, pt.fk_change_id




--SELECT fk_tenant_id, year,budget_year, fk_account_code, 
--department_code, fk_function_code, fk_project_code,free_dim_1, free_dim_2,free_dim_3, free_dim_4,
--fk_adjustment_code,fk_alter_code, fk_main_project_code, org_budget_flag, inv_status,fk_change_id,
--org_bud_amt_year = CASE WHEN year = budget_year AND org_budget_flag = 1 THEN amount else 0 end,
--rev_bud_amt_year = CASE WHEN year = budget_year THEN amount else 0 end,
--org_bud_last_year = CASE WHEN year = budget_year-1 THEN amount else 0 end,
--rev_bud_last_year = CASE WHEN year = budget_year-1 THEN amount else 0 end,
--year_1_amount = CASE WHEN year = budget_year THEN amount else 0 end,
--year_2_amount = CASE WHEN year = budget_year+1 THEN amount else 0 end,
--year_3_amount = CASE WHEN year = budget_year+2 THEN amount else 0 end,
--year_4_amount = CASE WHEN year = budget_year+3 THEN amount else 0 end,
--finplan_changes_year_1 = CASE WHEN year = budget_year AND org_budget_flag = 0 THEN amount else 0 end
--INTO #inv_bud_hlptab2
-- FROM #inv_bud_hlptab


---- Set original budget last year

--UPDATE a SET a.org_bud_last_year = 0
--FROM #inv_bud_hlptab2 a
--JOIN tfp_budget_changes ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.fk_change_id = ch.pk_change_id 
--AND ch.budget_year = a.budget_year-1 AND ch.org_budget_flag = 0


--UPDATE a SET a.org_bud_last_year = 0, rev_bud_last_year = 0
--FROM #inv_bud_hlptab2 a
--JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id AND a.fk_main_project_code = mp.pk_main_project_code
--AND a.budget_year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
--AND mp.inv_status IN (3,4,5)

---- Remove b-list actions 

--UPDATE #inv_bud_hlptab2 SET org_bud_amt_year = 0, rev_bud_amt_year = 0, year_1_amount = 0, year_2_amount = 0, year_3_amount = 0, year_4_amount = 0,
--finplan_changes_year_1 = 0
--WHERE inv_status IN (3,4,5)


--INSERT INTO  #TEMP_BUDGET (fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
--free_dim_1,free_dim_2,free_dim_3,free_dim_4,
--description,period,allocation_pct,fk_key_id, fk_investment_id, fk_portfolio_code,
--fk_adjustment_code, original_budget,revised_budget, cost_calc_budget,
--type_internal,fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,
--revised_budget_prev_year,accounting_prev_year,fk_action_id,fk_main_project_code,finplan_changes_year_1)
--SELECT 
--fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
--free_dim_1,free_dim_2,free_dim_3,free_dim_4,
--'' as description,(budget_year *100) +1 as period, 0 as allocation_pct,'' as fk_key_id, 0 as fk_investment_id, '' as fk_portfolio_code,
--fk_adjustment_code, 
--original_budget = SUM(org_bud_amt_year),
--revised_budget = SUM(rev_bud_amt_year), 
--0 AS cost_calc_budget,
--4 as type_internal,
--SUM(year_1_amount) as fp_year_1_amount,SUM(year_2_amount) as fp_year_2_amount,SUM(year_3_amount) as fp_year_3_amount,SUM(year_4_amount) as fp_year_4_amount,
--revised_budget_prev_year = SUM(rev_bud_last_year),
--0 as accounting_prev_year,
--0 as fk_action_id,fk_main_project_code,SUM(finplan_changes_year_1)
--FROM #inv_bud_hlptab2
--GROUP BY fk_tenant_id,budget_year,fk_account_code,department_code,fk_function_code,fk_project_code,
--free_dim_1,free_dim_2,free_dim_3,free_dim_4,fk_adjustment_code,fk_main_project_code

--END

--select @timestamp = sysdatetime();
--PRINT 'FINISH: Fetch data from investments at ' + convert(nvarchar(19),@timestamp)



IF @change_check = 0 AND @tenant_id = 0
begin
	TRUNCATE TABLE twh_budget_report_v2

END




IF @change_check = 0 AND @tenant_id != 0
BEGIN

RAISERROR ('START: Truncating table', 0, 1) WITH NOWAIT

BEGIN
SET  @continue = 1

WHILE @continue = 1
BEGIN
    PRINT GETDATE()
    SET ROWCOUNT 50000
    BEGIN TRANSACTION
    DELETE T FROM twh_budget_report_v2 T INNER JOIN @changed_table c ON T.fk_tenant_id = c.fk_tenant_id AND T.budget_year = c.budget_year 
    SET @rowcount = @@rowcount 
    COMMIT
    PRINT GETDATE()
    IF @rowcount = 0
    BEGIN
        SET @continue = 0
    END
END
END
END



SET ROWCOUNT 0;


IF @change_check = 0 AND @tenant_id = 0
BEGIN 
RAISERROR ('START : Dropping indexes', 0, 1) WITH NOWAIT

DROP INDEX ix_twh_budget_report_v2_1 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_2 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_3 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_4 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_5 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_6 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_7 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_8 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_9 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_10 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_11 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_12 ON [dbo].twh_budget_report_v2; 
DROP INDEX ix_twh_budget_report_v2_13 ON [dbo].twh_budget_report_v2; 

select @timestamp = sysdatetime();
PRINT 'FINISH: dropping indexes at ' + convert(nvarchar(19),@timestamp)
END


RAISERROR ('START : Fetch budget into twh_budget_report_v2', 0, 1) WITH NOWAIT

BEGIN

print 'her kommer inserten'

INSERT INTO twh_budget_report_v2 (fk_tenant_id,budget_year,fk_account_code,department_code,
fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,
description,period,allocation_pct,fk_key_id,fk_investment_id,fk_portfolio_code,
fk_employment_id,fk_adjustment_code,fk_alter_code,original_budget,revised_budget,cost_calc_budget,
fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,revised_budget_prev_year,
accounting_prev_year, finplan_changes_year_1,type_internal,fk_action_id,fk_main_project_code)
SELECT fk_tenant_id,budget_year,fk_account_code,department_code,
fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,
description,period,allocation_pct,fk_key_id,fk_investment_id,fk_portfolio_code,
fk_employment_id,fk_adjustment_code,fk_alter_code,SUM(original_budget),SUM(revised_budget),SUM(cost_calc_budget),
SUM(fp_year_1_amount),SUM(fp_year_2_amount),SUM(fp_year_3_amount),SUM(fp_year_4_amount),SUM(revised_budget_prev_year),
SUM(accounting_prev_year), SUM(finplan_changes_year_1),type_internal,fk_action_id,fk_main_project_code
FROM #TEMP_BUDGET
GROUP BY fk_tenant_id,budget_year,fk_account_code,department_code,
fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,
description,period,allocation_pct,fk_key_id,fk_investment_id,fk_portfolio_code,
fk_employment_id,fk_adjustment_code,type_internal,fk_action_id,fk_main_project_code,fk_alter_code
;


END

select @timestamp = sysdatetime();
PRINT 'FINISH: Fetch budget into twh_budget_report at ' + convert(nvarchar(19),@timestamp)


IF @change_check = 0 AND @tenant_id = 0
BEGIN
RAISERROR ('START : Rebuilding indexes', 0, 1) WITH NOWAIT

create index ix_twh_budget_report_v2_1 on twh_budget_report_v2 (budget_year, fk_tenant_id);
create index ix_twh_budget_report_v2_2 on twh_budget_report_v2 (department_code, fk_tenant_id, budget_year);
create index ix_twh_budget_report_v2_3 on twh_budget_report_v2 (fk_account_code, fk_tenant_id, budget_year);
create index ix_twh_budget_report_v2_4 on twh_budget_report_v2 (fk_employment_id,budget_year, fk_tenant_id);
create index ix_twh_budget_report_v2_5 on twh_budget_report_v2 (fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,resource_id,type_internal,fk_tenant_id,budget_year);
create index ix_twh_budget_report_v2_6 on twh_budget_report_v2 (fk_project_code, fk_tenant_id);
create index ix_twh_budget_report_v2_7 on twh_budget_report_v2 (resource_id, fk_tenant_id);
create index ix_twh_budget_report_v2_8 on twh_budget_report_v2 (fk_function_code, fk_tenant_id, budget_year);
create index ix_twh_budget_report_v2_9 on twh_budget_report_v2 (free_dim_1, fk_tenant_id);;
create index ix_twh_budget_report_v2_10 on twh_budget_report_v2 (free_dim_2, fk_tenant_id);;
create index ix_twh_budget_report_v2_11 on twh_budget_report_v2 (free_dim_3, fk_tenant_id);;
create index ix_twh_budget_report_v2_12 on twh_budget_report_v2 (free_dim_4, fk_tenant_id);
create index ix_twh_budget_report_v2_13 on twh_budget_report_v2 (fk_action_id, type_internal);


select @timestamp = sysdatetime();
PRINT 'FINISH: Rebuilding indexes at ' + convert(nvarchar(19),@timestamp)

END



BEGIN
UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name IN ('BUDGETFULL','TWHBUDGETUPDATE');
END 

PRINT 'Job ended ok'
GO


