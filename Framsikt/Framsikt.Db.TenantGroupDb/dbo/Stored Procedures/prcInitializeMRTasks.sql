CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRTasks]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS

	
	DECLARE @budget_year INT
	DECLARE @prev_forecast_period INT
	DECLARE @org_version VARCHAR(25) 
	DECLARE @language VARCHAR(10)
	DECLARE @assignment_flag INT
	DECLARE @prev_task_period INT


	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
	SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
	SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
	SET @budget_year = @forecast_period/100


	SET @prev_task_period = (
	SELECT MAX(forecast_period) FROM tbi_task_monthly_status
	WHERE fk_Tenant_id = @fk_tenant_id
	AND forecast_period < @forecast_period
	AND forecast_period > @budget_year * 100
	)


	SET @assignment_flag = (SELECT min(flag_status) FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id AND flag_name = 'MR_COPYREPORT_ORGLEVEL' AND fk_org_version = @org_version)

	IF @assignment_flag IS NULL
	BEGIN
	SET @assignment_flag = 0
	END



	IF @assignment_flag = 0
	begin

	INSERT INTO tbi_task_monthly_status (fk_task_id, status, risk, description, forecast_period, fk_tenant_id, updated, updated_by)
	SELECT fk_task_id, status, risk, '' as description, @forecast_period, fk_tenant_id, GETDATE() AS updated, updated_by 
	FROM tbi_task_monthly_status a
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.forecast_period = @prev_task_period
	AND NOT EXISTS (SELECT * FROM tbi_task_monthly_status b
	WHERE b.fk_tenant_id = a.fk_tenant_id
	AND b.forecast_period = @forecast_period
	AND a.fk_task_id = b.fk_task_id)

	end

	IF @assignment_flag > 0
	begin

	
	INSERT INTO tbi_task_monthly_status (fk_task_id, status, risk, description, forecast_period, fk_tenant_id, updated, updated_by)
	SELECT a.fk_task_id, a.status, a.risk, a.description, @forecast_period, a.fk_tenant_id, GETDATE() AS updated, a.updated_by 
	FROM tbi_task_monthly_status a
	JOIN tbi_tasks t ON a.fk_task_id = t.pk_task_id AND a.fk_tenant_id = t.fk_tenant_id AND t.org_level >= @assignment_flag
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.forecast_period = @prev_task_period
	AND NOT EXISTS (SELECT * FROM tbi_task_monthly_status b
	WHERE b.fk_tenant_id = a.fk_tenant_id
	AND b.forecast_period = @forecast_period
	AND a.fk_task_id = b.fk_task_id)

	end

	IF (SELECT COUNT(*) FROM tbi_task_monthly_status a WHERE a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period ) = 0 

	BEGIN 

	PRINT 'Insert task from virkplan'

	INSERT INTO tbi_task_monthly_status (fk_task_id, status, risk, description, forecast_period, fk_tenant_id, updated, updated_by)
	select a.pk_task_id, a.taskstatus, 0 as risk, '' as description, @forecast_period, a.fk_tenant_id, getdate() as updated, @user_id 
	FROM tbi_tasks a
	JOIN tco_progress_status s ON a.taskstatus = s.status_id AND a.fk_tenant_id = s.fk_tenant_id AND s.type = 'sunit_bplan'
	JOIN tbi_assignments ba ON a.fk_tenant_id = ba.fk_tenant_id AND a.fk_assignment_id = ba.pk_assignment_id AND ba.included_in_monthly_report = 1
	WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year

	END


	PRINT 'Finish: Getting task status from prev reporting period'
RETURN 0
