CREATE OR ALTER PROCEDURE [dbo].[prcimportActionBudgetsintoTbuTransDetail]   
 @UserID int,  
 @budgetYear int,  
 @changeID int,  
 @TenantID int,  
 @multiplyBy int,  
 @adjustmentCode varchar(500),  
 @UpdateYBNextYear bit -- SP NEEDS TO BE RUN TWICE IF YOU WANT TO UPDATE BOTH CURRENT YEAR AND NEXT YEAR. ONE RUN WITH @UpdateYBNextYear = 0 and one with = 1.  
  
AS  
  
declare @timestamp datetime2  
  
select @timestamp = sysdatetime();  
PRINT 'START PROCESSING at ' + convert(nvarchar(19),@timestamp)  
  
declare @last_period int = (@budgetYear + @UpdateYBNextYear)*100  +12  
  
DECLARE @defaultperiodicKey INT  
  
SET @defaultperiodicKey = (SELECT param_value FROM vw_tco_parameters where param_name='DEFAULT_BUDGET_PER_KEY' and fk_tenant_id=@TenantID);  
  
  
DROP TABLE IF EXISTS #tfp_stage_action_import  
  
CREATE TABLE  #tfp_stage_action_import (  
[user_id] [int] NOT NULL  
,[tenant_id] [int] NOT NULL  
,[budget_year] [int] NOT NULL  
,[account_code] [nvarchar](25) NULL DEFAULT ('')  
,[department_code] [nvarchar](25) NULL DEFAULT ('')  
,[function_code] [nvarchar](25) NULL DEFAULT ('')  
,[project_code] [nvarchar](25) NULL DEFAULT ('')  
,[free_dim_1] [nvarchar](25) NULL DEFAULT ('')  
,[free_dim_2] [nvarchar](25) NULL DEFAULT ('')  
,[free_dim_3] [nvarchar](25) NULL DEFAULT ('')  
,[free_dim_4] [nvarchar](25) NULL DEFAULT ('')  
,[year_1_amount] [decimal](18, 2) NULL DEFAULT ((0))  
,[description] [nvarchar](max) NULL DEFAULT ('')  
,[periodicKey] [nvarchar](25) NULL DEFAULT ('')  
,[fk_action_id] INT NOT NULL  
,[fk_adjustment_code] NVARCHAR(25) NULL  
,[alter_code] NVARCHAR (25) NULL  
, [bu_trans_id] [uniqueidentifier] NOT NULL    
);   
  
  
  
--Fetch data from stage to temp table  
INSERT #tfp_stage_action_import (  
user_id  
,tenant_id  
,budget_year  
,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4  
,year_1_amount  
,description,periodicKey  
,fk_action_id  
,fk_adjustment_code,alter_code, bu_trans_id)     
SELECT     
user_id,tenant_id  
,budget_year = @budgetYear + @UpdateYBNextYear  
,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4  
,year_1_amount = CASE WHEN @UpdateYBNextYear = 0 THEN year_1_amount* @multiplyBy  
      WHEN @UpdateYBNextYear = 1 THEN year_2_amount* @multiplyBy  
      ELSE 0  
      END  
,description  
,periodicKey = CASE WHEN periodicKey is null OR periodicKey = '' THEN @defaultperiodicKey ELSE periodicKey END  
,fk_action_id = action_id  
,adjustment_code,alter_code  
,bu_trans_id = NEWID()
FROM  tfp_stage_action_import   
WHERE  tenant_id= @TenantID  AND [user_id]=@UserID AND budget_year= @budgetYear AND change_id= @changeID  
  
  
print 'FIND ALLOCATION PCT FOR KEY 0 ROWS'  
  
drop table if exists #acc_details  
  
 SELECT   
fk_tenant_id = tenant_id   
,budget_year  
,[fk_account_code] = account_code   
,[department_code]  
,[fk_function_code] = function_code   
,[fk_project_code] = project_code  
,[free_dim_1]  
,[free_dim_2]  
,[free_dim_3]  
,[free_dim_4]  
INTO #acc_details  
from #tfp_stage_action_import  
WHERE periodicKey = 0  
GROUP BY  tenant_id   
,budget_year  
,account_code   
,[department_code]  
,function_code   
,project_code  
,[free_dim_1]  
,[free_dim_2]  
,[free_dim_3]  
,[free_dim_4]  
  
  
DROP TABLE IF EXISTS #tbu_trans_detailForZero  
  
 CREATE TABLE  #tbu_trans_detailForZero ([fk_account_code] [nvarchar](25) NULL,[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) 
NULL,  
  [free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[period] [int] NULL,[amount_year_1] [decimal](18, 2) NULL,  
  [total_amount] [decimal](18, 2) NULL,[allocation_pct] [decimal](18, 3) NULL)  
  
--Fetch period data for calculating key 0 alloc pct  
INSERT INTO #tbu_trans_detailForZero (fk_account_code, department_code, fk_function_code, fk_project_code,  
free_dim_1, free_dim_2, free_dim_3, free_dim_4, period, amount_year_1, total_amount, allocation_pct)  
SELECT a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code,  
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4  
, a.period  
,amount_year_1 = SUM(a.amount_year_1)  
,total_amount = 0  
,allocation_pct = 0  
FROM tbu_trans_detail a  
JOIN #acc_details b ON   
a.fk_tenant_id = b.fk_tenant_id  
AND a.budget_year = b.budget_year  
AND a.fk_account_code = b.fk_account_code  
AND a.department_code = b.department_code  
AND a.fk_function_code = b.fk_function_code  
AND a.fk_project_code = b.fk_project_code  
AND a.free_dim_1 = b.free_dim_1  
AND a.free_dim_2 = b.free_dim_2  
AND a.free_dim_3 = b.free_dim_3   
AND a.free_dim_4 = b.free_dim_4  
GROUP BY a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code,  
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, a.period  
  
--Set total amount for key 0 calc  
UPDATE a SET a.total_amount = b.total_amount  
FROM #tbu_trans_detailForZero a  
JOIN (SELECT fk_account_code, department_code, fk_function_code, fk_project_code,  
free_dim_1, free_dim_2, free_dim_3, free_dim_4, SUM (amount_year_1) as total_amount  
FROM #tbu_trans_detailForZero  
GROUP BY fk_account_code, department_code, fk_function_code, fk_project_code,  
free_dim_1, free_dim_2, free_dim_3, free_dim_4) b  
ON a.fk_account_code = b.fk_account_code  
AND a.department_code = b.department_code  
AND a.fk_function_code = b.fk_function_code  
AND a.fk_project_code = b.fk_project_code  
AND a.free_dim_1 = b.free_dim_1  
AND a.free_dim_2 = b.free_dim_2  
AND a.free_dim_3 = b.free_dim_3   
AND a.free_dim_4 = b.free_dim_4  
  
DELETE FROM #tbu_trans_detailForZero WHERE total_amount = 0   
  
--Set allocation pct for key 0  
UPDATE #tbu_trans_detailForZero SET allocation_pct = amount_year_1 / total_amount * 100  
  
--Set default periodic key if key = 0 and no current data exists  
UPDATE a SET periodicKey = @defaultperiodicKey  
FROM #tfp_stage_action_import a  
WHERE NOT EXISTS   
(  
 SELECT * FROM  #tbu_trans_detailForZero b  
 where a.account_code = b.fk_account_code  
 AND a.department_code = b.department_code  
 AND a.function_code = b.fk_function_code  
 AND a.project_code = b.fk_project_code  
 AND a.free_dim_1 = b.free_dim_1  
 AND a.free_dim_2 = b.free_dim_2  
 AND a.free_dim_3 = b.free_dim_3   
 AND a.free_dim_4 = b.free_dim_4  
)  
AND a.periodicKey = 0  
  
  
DROP TABLE if exists #hlp_tbu_trans_detail  
  
CREATE TABLE  #hlp_tbu_trans_detail  (  
[pk_id] [uniqueidentifier] NULL, [bu_trans_id] [uniqueidentifier] NOT NULL, [fk_tenant_id] [int] NULL,[action_type] [int] NULL,[line_order] [int] NULL,[fk_account_code] [nvarchar](25) NULL,  
[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,  
[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[resource_id] [nvarchar](25) NULL,[fk_employment_id] [bigint] NULL,[description] [nvarchar](max) NULL,  
[budget_year] [int] NULL,[period] [int] NULL,[budget_type] [int] NULL,[amount_year_1] [decimal](18, 2) NULL,[amount_year_2] [decimal](18, 2) NULL,[amount_year_3] [decimal](18, 2) NULL,  
[amount_year_4] [decimal](18, 2) NULL,[fk_key_id] [int] NULL,[updated] [datetime] NULL,[updated_by] [int] NULL,[allocation_pct] [decimal](18, 3) NULL,[total_amount] [decimal](18, 2) NULL,  
[tax_flag] [int] NULL,[holiday_flag] [int] NULL,[fk_pension_type] [nvarchar](12) NULL  
,[fk_action_id] INT NOT NULL  
,[fk_adjustment_code] NVARCHAR(25) NULL,[alter_code] NVARCHAR (25) NULL, prefix_adjCode NVARCHAR (25) NULL,[fk_adjustment_code_finplan] NVARCHAR(25) NULL)    
  
  
DROP table if exists #per_key  
  
CREATE TABLE #per_key (key_id INT NOT  NULL,  
allocation_pct dec(18,3) NOT NULL,   
period INT NOT NULL  
)  
  
CREATE unique index #ind_per_key_1 ON #per_key ( key_id, period)  
  
INSERT INTO #per_key (key_id, allocation_pct, period)  
select key_id, allocation_pct,   
period = (@budgetYear + @UpdateYBNextYear)*100 + period  
from tco_periodic_key  
WHERE fk_tenant_id IN (0,@TenantID)  
AND key_id IN (SELECT DISTINCT periodicKey FROM #tfp_stage_action_import)  
  
--Calculate rows that has periodic key  
INSERT #hlp_tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,  
    description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,  
    total_amount,tax_flag,holiday_flag,fk_pension_type,fk_action_id,fk_adjustment_code ,alter_code, fk_adjustment_code_finplan)  
SELECT NEWID() AS pk_id  
,i.bu_trans_id  
,i.tenant_id  
,action_type =  5  
,line_order = 0   
,i.account_code  
,i.department_code  
,i.function_code  
,i.project_code  
,i.free_dim_1  
,i.free_dim_2  
,i.free_dim_3  
,i.free_dim_4  
,i.description  
,resource_id = ''  
,fk_employment_id = 0   
,budget_year = i.budget_year  
,period = k.period  
,budget_type = 1   
,amount_year_1 = ROUND(i.year_1_amount * k.allocation_pct/100,0)  
,amount_year_2 = 0  
,amount_year_3 = 0  
,amount_year_4 = 0  
,fk_key_id = periodicKey  
,updated = GETUTCDATE()  
,updated_by = @UserID  
,allocation_pct = k.allocation_pct  
,total_amount = i.year_1_amount  
,tax_flag = 0  
,holiday_flag = 0  
,fk_pension_type = ''  
,fk_action_id  
,fk_adjustment_code = @adjustmentCode  
,alter_code  
,fk_adjustment_code_finplan = i.fk_adjustment_code  
FROM #tfp_stage_action_import i  
JOIN #per_key k ON i.periodicKey = k.key_id  
where i.periodicKey != 0  
  
--Calculate for rows that has key id 0  
INSERT #hlp_tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,  
    description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,  
    total_amount,tax_flag,holiday_flag,fk_pension_type,fk_action_id,fk_adjustment_code,alter_code ,fk_adjustment_code_finplan)  
SELECT NEWID() AS pk_id  
,i.bu_trans_id  
,i.tenant_id  
,action_type =  5  
,line_order = 0   
,i.account_code  
,i.department_code  
,i.function_code  
,i.project_code  
,i.free_dim_1  
,i.free_dim_2  
,i.free_dim_3  
,i.free_dim_4  
,i.description  
,resource_id = ''  
,fk_employment_id = 0   
,budget_year = i.budget_year  
,period = b.period  
,budget_type = 1   
,amount_year_1 = ROUND(i.year_1_amount * b.allocation_pct/100,0)  
,amount_year_2 = 0  
,amount_year_3 = 0  
,amount_year_4 = 0  
,fk_key_id = periodicKey  
,updated = GETUTCDATE()  
,updated_by = @UserID  
,allocation_pct = b.allocation_pct  
,total_amount = i.year_1_amount  
,tax_flag = 0  
,holiday_flag = 0  
,fk_pension_type = ''  
,fk_action_id  
,fk_adjustment_code = @adjustmentCode  
,alter_code   
,fk_adjustment_code_finplan = i.fk_adjustment_code 
FROM #tfp_stage_action_import i  
JOIN #tbu_trans_detailForZero b  
ON i.account_code = b.fk_account_code  
AND i.department_code = b.department_code  
AND i.function_code = b.fk_function_code  
AND i.project_code = b.fk_project_code  
AND i.free_dim_1 = b.free_dim_1  
AND i.free_dim_2 = b.free_dim_2  
AND i.free_dim_3 = b.free_dim_3   
AND i.free_dim_4 = b.free_dim_4  
where i.periodicKey = 0  
  
  
--Fix deviations due to rounding  
DROP TABLE IF EXISTS #temp_diff  
  
select A.bu_trans_id,A.amount_year_1-B.year_1_amount as diff  
INTO #temp_diff  
FROM  
(select bu_trans_id,SUM(amount_year_1)amount_year_1 from #hlp_tbu_trans_detail  
group by bu_trans_id) A  
JOIN  
(select bu_trans_id, year_1_amount from #tfp_stage_action_import) B ON A.bu_trans_id = B.bu_trans_id  
  
update #hlp_tbu_trans_detail set amount_year_1 = A.amount_year_1 - B.diff  
from #hlp_tbu_trans_detail A  
JOIN #temp_diff B on a.bu_Trans_id = b.bu_Trans_id  
where a.period = @last_period  
  
  
select @timestamp = sysdatetime();  
PRINT 'Periodic allocation part done '  + convert(nvarchar(19),@timestamp)  
   
  
     
select @timestamp = sysdatetime();  
PRINT 'Insert into tbu '  + convert(nvarchar(19),@timestamp)  
  
INSERT tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,  
description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,  
total_amount,tax_flag,holiday_flag,fk_pension_type,fk_action_id,fk_adjustment_code,fk_alter_code,fk_adjustment_code_finplan)  
SELECT pk_id,bu_trans_id,fk_tenant_id, action_type,  
line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4  
,descriptiopn = CASE WHEN LEN(description) > 700 THEN SUBSTRING(description, 1, 700)  ELSE  description END  
,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,  
updated,updated_by,allocation_pct,  
total_amount,tax_flag,holiday_flag,fk_pension_type  
,fk_action_id  
,fk_adjustment_code = @adjustmentCode  
,alter_code = alter_code  
,fk_adjustment_code_finplan -- = fk_adjustment_code
from #hlp_tbu_trans_detail  
  
     
select @timestamp = sysdatetime();  
PRINT 'finish - Insert into tbu '  + convert(nvarchar(19),@timestamp)  
  