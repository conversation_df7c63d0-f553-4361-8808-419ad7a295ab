CREATE OR ALTER PROCEDURE [dbo].[prcInitializeFinplanReport]  @truncate_all INT = 0 

AS
BEGIN
       -- SET NOCOUNT ON added to prevent extra result sets from
       -- interfering with SELECT statements.
       SET NOCOUNT ON;
       declare
             @inserted int,
             @merged int,
             @start datetime,
             @end datetime,
             @stage varchar(25),
			 @budget_year INT,
			 @last_budget_year INT

	SET @last_budget_year = (SELECT DATEPART(YEAR,GETDATE()))
	SET @budget_year = @last_budget_year+1

	BEGIN
	UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'FINPLANFULL';
	TRUNCATE TABLE [twh_temp_triggered_actions];
	TRUNCATE TABLE [dbo].[twh_temp_changed_actions];
    END  

	Print 'Run script for auto access to users'
	
	exec [prcAutoAccessOrgRole]


	--IF @truncate_all = 1 
	--BEGIN
	print 'Truncating twh_finplan_report...'
	TRUNCATE TABLE [twh_finplan_report]
	--<PERSON><PERSON>

    begin 
             select @start = sysdatetime();
             set @stage = 'TRUNCATE';
             print 'Truncating twh_temp_finplan_report...'
             TRUNCATE TABLE twh_temp_finplan_report;
             SET @stage = 'INSERT';
             print 'Inserting from finplan...'
             INSERT INTO [twh_finplan_report] (pk_id,fk_tenant_id,budget_year,budget_phase,budget_change,approval_reference,approval_date,
                    finplan_section,finplan_line,kostra_account,account,department,fk_account_code, fk_department_code, fk_function_code,
                    fk_project_code, service_level_1, service_level_2, service_level_3, service_level_4, service_level_5, [function],
                    project,free_dim_1,free_dim_2,free_dim_3,free_dim_4, free_dim_1_description, free_dim_2_description, free_dim_3_description, free_dim_4_description,
                    action,alter_code_id,alter_code,adjustment_code,adjustment_code_id,action_type,sum_description,category,tags,description,
                    fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,priority,display_description_apendix_flag,updated,updated_by, pk_action_id,
					service_level_1_code,service_level_2_code,service_level_3_code,service_level_4_code,service_level_5_code,
					acc_type_code,acc_type_description, acc_group_code, acc_group_description, 
					org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5, data_type, fk_change_id, external_description, internal_description)
			SELECT newid(),fk_tenant_id,budget_year,budget_phase,budget_change,approval_reference,approval_date,
                    finplan_section,finplan_line,kostra_account,account,SUBSTRING(department,1,100),fk_account_code, fk_department_code, fk_function_code,
                    fk_project_code, service_level_1, service_level_2, service_level_3, service_level_4, service_level_5, [function],
                    project,free_dim_1,free_dim_2,free_dim_3,free_dim_4, SUBSTRING(free_dim_1_description,1,100), SUBSTRING(free_dim_2_description,1,100), SUBSTRING(free_dim_3_description,1,100), SUBSTRING(free_dim_4_description,1,100),
                    action,fk_alter_code,SUBSTRING(alter_code,1,128),SUBSTRING(adjustment_code,1,128),fk_adjustment_code,action_type,SUBSTRING(sum_description,1,128),category,tags,description,
                    fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,priority,display_description_apendix_flag,GETDATE(),1002, pk_action_id,
					service_level_1_code,service_level_2_code,service_level_3_code,service_level_4_code,service_level_5_code,
					acc_type_code,acc_type_description, acc_group_code, acc_group_description,
					org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5, data_type, fk_change_id, external_description, internal_description
             FROM vw_twh_finplan_report WHERE budget_year IN (@last_budget_year, @budget_year,@last_budget_year-1)
             select @inserted = @@ROWCOUNT;


			              SET @stage = 'INSERT';
             print 'Inserting from deleted...'
             INSERT INTO [twh_finplan_report] (pk_id,fk_tenant_id,budget_year,budget_phase,budget_change,approval_reference,approval_date,
                    finplan_section,finplan_line,kostra_account,account,department,fk_account_code, fk_department_code, fk_function_code,
                    fk_project_code, service_level_1, service_level_2, service_level_3, service_level_4, service_level_5, [function],
                    project,free_dim_1,free_dim_2,free_dim_3,free_dim_4, free_dim_1_description, free_dim_2_description, free_dim_3_description, free_dim_4_description,
                    action,alter_code_id,alter_code,adjustment_code,adjustment_code_id,action_type,sum_description,category,tags,description,
                    fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,priority,display_description_apendix_flag,updated,updated_by, pk_action_id,
					service_level_1_code,service_level_2_code,service_level_3_code,service_level_4_code,service_level_5_code,
					acc_type_code,acc_type_description, acc_group_code, acc_group_description, 
					org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5, data_type, fk_change_id, external_description, internal_description)
             SELECT newid(),fk_tenant_id,budget_year,budget_phase,budget_change,approval_reference,approval_date,
                    finplan_section,finplan_line,kostra_account,account,SUBSTRING(department,1,100),fk_account_code, fk_department_code, fk_function_code,
                    fk_project_code, service_level_1, service_level_2, service_level_3, service_level_4, service_level_5, [function],
                    project,free_dim_1,free_dim_2,free_dim_3,free_dim_4, SUBSTRING(free_dim_1_description,1,100), SUBSTRING(free_dim_2_description,1,100), 
					SUBSTRING (free_dim_3_description,1,100), SUBSTRING(free_dim_4_description,1,100),
                    action,fk_alter_code,SUBSTRING(alter_code,1,128),SUBSTRING(adjustment_code,1,128),fk_adjustment_code,action_type,SUBSTRING(sum_description,1,128),category,tags,description,
                    fp_year_1_amount,fp_year_2_amount,fp_year_3_amount,fp_year_4_amount,priority,display_description_apendix_flag,GETDATE(),1002, pk_action_id,
					service_level_1_code,service_level_2_code,service_level_3_code,service_level_4_code,service_level_5_code,
					acc_type_code,acc_type_description, acc_group_code, acc_group_description,
					org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5, data_type, fk_change_id, external_description, internal_description
             FROM vw_twh_delete_finplan_report
			 WHERE budget_year IN (@last_budget_year, @budget_year,@last_budget_year-1)
             select @inserted = @@ROWCOUNT;
		 
			 
       END

	
       select @end = sysdatetime();
END
BEGIN
			UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name = 'FINPLANFULL';
END
GO

