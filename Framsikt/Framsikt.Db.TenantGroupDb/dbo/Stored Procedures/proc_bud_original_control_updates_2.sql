CREATE OR ALTER PROC [dbo].[proc_bud_original_control_updates]
@tenant_id INT,
@budget_year INT,
@batch_id NVARCHAR(14)

AS

--DECLARE
--@tenant_id INT = 1641,
--@budget_year INT = 2023,
--@batch_id NVARCHAR(14) = '21428736888961'

DECLARE @tenant_type NVARCHAR(30) = (select erp_type from gco_tenants where pk_id = @tenant_id)

--Find log table name based on ERP type
DECLARE @log_table_name NVARCHAR(50)
DECLARE @postback_table_name NVARCHAR(50)

IF @tenant_type = 'Agresso'
BEGIN
	SET @log_table_name = 'tfp_erp_export_log_agresso'
	SET @postback_table_name = 'integ_agrpostback_origbud'
END

IF @tenant_type = 'Visma'
BEGIN
	SET @log_table_name = 'tfp_erp_export_log_visma'
	SET @postback_table_name = 'integnew_export_visma_posted_origbud'
END

IF @log_table_name = ''
BEGIN
	PRINT 'Problem with finding tenant ERP type - Contact support'
	RETURN;
END


DROP TABLE IF EXISTS #error_types
CREATE TABLE #error_types (
fk_error_type NVARCHAR(50),
case_id INT)

--Define the error types supported in the SP.
--1		just 1 dimension
--2		Relation error
--3		Required default value - replace blank field with some value
--4		Field required to be blank - not implemented support for as of yet
--5		Other with no handling - support needs to handle

INSERT INTO #error_types VALUES
('dim_not_valid'				, 1	),
('dim_locked'					, 1	),
('invalid_account'				, 1	),
('DIM_INVALID_CONTROL_SP'		, 1	),
('DIM_TEMPORARY_CONTROL_SP'		, 1	),
('DIM_NOT_ACTIVE_CONTROL_SP'	, 1	),
('dim_does_not_exist'			, 1	),
('default_value'				, 1	),
('dim_combination_not_valid'	, 2	),
('RELATION_INVALID_CONTROL_SP'	, 2	),
('dim_required'					, 3	),
('dim_not_allowed'				, 4	),
('dim_automatic'				, 4	),
('dim_prohibited'				, 4	),
('budget_unbalanced'			, 5	),
('dim_unknown_error'			, 5	),
('costcentre_control_violation'	, 5	)


--Error list for relation errors
DROP TABLE IF EXISTS #relation_error_list
select row_id = row_number()  OVER(order by A.pk_id),pk_id,fk_tenant_id,export_id,batch_id,a.fk_error_type
,dim_1_type = CASE	WHEN dim_1_type = 'fk_freedim1_code' THEN 'free_dim_1'
					WHEN dim_1_type = 'fk_freedim2_code' THEN 'free_dim_2'
					WHEN dim_1_type = 'fk_freedim3_code' THEN 'free_dim_3'
					WHEN dim_1_type = 'fk_freedim4_code' THEN 'free_dim_4'
					ELSE dim_1_type
					END
,dim_1_id
,dim_2_type  = CASE	WHEN dim_2_type = 'fk_freedim1_code' THEN 'free_dim_1'
					WHEN dim_2_type = 'fk_freedim2_code' THEN 'free_dim_2'
					WHEN dim_2_type = 'fk_freedim3_code' THEN 'free_dim_3'
					WHEN dim_2_type = 'fk_freedim4_code' THEN 'free_dim_4'
					ELSE dim_2_type
					END
,dim_2_id = CASE WHEN dim_2_id = '*' THEN '' ELSE dim_2_id END
,dim_to_change = CASE	WHEN dim_to_change = 'fk_freedim1_code' THEN 'free_dim_1'
						WHEN dim_to_change = 'fk_freedim2_code' THEN 'free_dim_2'
						WHEN dim_to_change = 'fk_freedim3_code' THEN 'free_dim_3'
						WHEN dim_to_change = 'fk_freedim4_code' THEN 'free_dim_4'
						ELSE dim_to_change
						END
,id_new
INTO #relation_error_list
from tbu_integ_error_handling a
JOIN #error_types b on a.fk_error_type = b.fk_error_type AND b.case_id IN (2,3)
where a.fk_tenant_id = @tenant_id
and a.batch_id = @batch_id
and a.[keep] = 0
and a.fixed = 0
and a.id_new != '' 
AND a.id_new IS NOT NULL
AND a.dim_2_type IS NOT NULL
AND a.dim_2_type != ''


--Error list for dimension invalid errors
DROP TABLE IF EXISTS #dim_invalid_error_list
select row_id = row_number()  OVER(order by A.pk_id),pk_id,fk_tenant_id,export_id,batch_id,a.fk_error_type
,dim_1_type = CASE	WHEN dim_1_type = 'fk_freedim1_code' THEN 'free_dim_1'
					WHEN dim_1_type = 'fk_freedim2_code' THEN 'free_dim_2'
					WHEN dim_1_type = 'fk_freedim3_code' THEN 'free_dim_3'
					WHEN dim_1_type = 'fk_freedim4_code' THEN 'free_dim_4'
					ELSE dim_1_type
					END
,dim_1_id
,dim_2_type  = CASE	WHEN dim_2_type = 'fk_freedim1_code' THEN 'free_dim_1'
					WHEN dim_2_type = 'fk_freedim2_code' THEN 'free_dim_2'
					WHEN dim_2_type = 'fk_freedim3_code' THEN 'free_dim_3'
					WHEN dim_2_type = 'fk_freedim4_code' THEN 'free_dim_4'
					ELSE dim_2_type
					END
,dim_2_id = CASE WHEN dim_2_id = '*' THEN '' ELSE dim_2_id END
,dim_to_change = CASE	WHEN dim_to_change = 'fk_freedim1_code' THEN 'free_dim_1'
						WHEN dim_to_change = 'fk_freedim2_code' THEN 'free_dim_2'
						WHEN dim_to_change = 'fk_freedim3_code' THEN 'free_dim_3'
						WHEN dim_to_change = 'fk_freedim4_code' THEN 'free_dim_4'
						ELSE dim_to_change
						END
,id_new
INTO #dim_invalid_error_list
from tbu_integ_error_handling a
JOIN #error_types b on a.fk_error_type = b.fk_error_type AND b.case_id = 1
where a.fk_tenant_id = @tenant_id
and a.batch_id = @batch_id
and a.[keep] = 0
and a.fixed = 0
and a.id_new != '' 
AND a.id_new IS NOT NULL


DECLARE @CursorID INT = 1;
DECLARE @RowCnt BIGINT = 0;
DECLARE @dim_1_type NVARCHAR(50);
DECLARE @dim_1_id NVARCHAR(50);
DECLARE @dim_2_type NVARCHAR(50);
DECLARE @dim_2_id NVARCHAR(50);
DECLARE @dim_to_change NVARCHAR(50);
DECLARE @id_new NVARCHAR(50);
DECLARE @export_id NVARCHAR(50);
DECLARE @fk_error_type NVARCHAR(100);
DECLARE @pk_id NVARCHAR(100);

DECLARE @update_string_1 NVARCHAR(MAX)
DECLARE @update_string_2 NVARCHAR(MAX)
DECLARE @update_string_3 NVARCHAR(MAX)

--Updates for relation errors
SET @CursorID = 1
SELECT @RowCnt = COUNT(0) FROM #relation_error_list

WHILE @CursorID <= @RowCnt
BEGIN
	SET @dim_1_type =		(select dim_1_type		from #relation_error_list where row_id = @CursorID)
	SET @dim_1_id =			(select dim_1_id		from #relation_error_list where row_id = @CursorID)
	SET @dim_2_type =		(select dim_2_type		from #relation_error_list where row_id = @CursorID)
	SET @dim_2_id =			(select dim_2_id		from #relation_error_list where row_id = @CursorID)
	SET @dim_to_change =	(select dim_to_change	from #relation_error_list where row_id = @CursorID)
	SET @id_new =			(select id_new			from #relation_error_list where row_id = @CursorID)
	SET @fk_error_type =	(select fk_error_type	from #relation_error_list where row_id = @CursorID)
	SET @export_id =		(select export_id		from #relation_error_list where row_id = @CursorID)
	SET @pk_id =			(select pk_id			from #relation_error_list where row_id = @CursorID)

	PRINT 'Fixing relation error number'+CONVERT(NVARCHAR(20),@CursorID)

	SET @update_string_1 =	'INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = ''tbu_trans_detail_original''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM tbu_trans_detail_original
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';

							UPDATE tbu_trans_detail_original set '+ CASE WHEN @dim_to_change = 'fk_department_code' THEN 'department_code' ELSE @dim_to_change END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';
							
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = ''tbu_trans_detail''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM tbu_trans_detail
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';

							UPDATE tbu_trans_detail set '+ CASE WHEN @dim_to_change = 'fk_department_code' THEN 'department_code' ELSE @dim_to_change END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';'
	SET @update_string_2 = '
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = ''tfp_proj_transactions''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							from tfp_proj_transactions PT
							JOIN	( --Fetch budget rounds to be included in finplan (no budget changes for current year)
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year < '+CONVERT(VARCHAR(20),@budget_year)+'
									UNION
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year = '+CONVERT(VARCHAR(20),@budget_year)+'
									and org_budget_flag = 1
									)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
							where year = '+CONVERT(VARCHAR(20),@budget_year)+'
							AND pt.fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
							AND '+ @dim_1_type + '= ''' + @dim_1_id + '''
							AND '+ @dim_2_type + '= ''' + @dim_2_id + ''';
							
							UPDATE PT SET '+ @dim_to_change + '= '''+ @id_new + '''
							from tfp_proj_transactions PT
							JOIN	( --Fetch budget rounds to be included in finplan (no budget changes for current year)
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year < '+CONVERT(VARCHAR(20),@budget_year)+'
									UNION
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year = '+CONVERT(VARCHAR(20),@budget_year)+'
									and org_budget_flag = 1
									)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
							where year = '+CONVERT(VARCHAR(20),@budget_year)+'
							AND pt.fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
							AND '+ @dim_1_type + '= ''' + @dim_1_id + '''
							AND '+ @dim_2_type + '= ''' + @dim_2_id + ''';'
								
	SET @update_string_3 =	'INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = '''+@log_table_name+'''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM '+@log_table_name+' 
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';

							UPDATE '+@log_table_name+' set '+ CASE WHEN @dim_to_change = 'fk_department_code' THEN 'department_code' ELSE @dim_to_change END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';
							
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = '''+@postback_table_name+'''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM '+@postback_table_name+' 
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud' THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';

							UPDATE '+@postback_table_name+' set '+ CASE WHEN @dim_to_change = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud' THEN 'department_code' ELSE @dim_to_change END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud'  THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + '''
							AND '+ CASE WHEN @dim_2_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud'  THEN 'department_code' ELSE @dim_2_type END + '= ''' + @dim_2_id + ''';
							
							UPDATE tbu_integ_error_handling SET fixed = 1 WHERE fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+' AND pk_id = '+@pk_id

	--PRINT @update_string_1 
	--PRINT @update_string_2
	--PRINT @update_string_3
	EXEC sp_executesql @update_string_1
	EXEC sp_executesql @update_string_2
	EXEC sp_executesql @update_string_3
   	
	SET @CursorID = @CursorID + 1 
 
END


--Updates for invalid dimensions
SET @CursorID = 1
SELECT @RowCnt = COUNT(0) FROM #dim_invalid_error_list

WHILE @CursorID <= @RowCnt
BEGIN
	SET @dim_1_type =		(select ISNULL(dim_1_type	,'')	from #dim_invalid_error_list where row_id = @CursorID)
	SET @dim_1_id =			(select ISNULL(dim_1_id		,'')	from #dim_invalid_error_list where row_id = @CursorID)
	SET @dim_2_type =		(select ISNULL(dim_2_type	,'')	from #dim_invalid_error_list where row_id = @CursorID)
	SET @dim_2_id =			(select ISNULL(dim_2_id		,'')	from #dim_invalid_error_list where row_id = @CursorID)
	SET @dim_to_change =	(select ISNULL(dim_to_change,'')	from #dim_invalid_error_list where row_id = @CursorID)
	SET @id_new =			(select ISNULL(id_new		,'')	from #dim_invalid_error_list where row_id = @CursorID)
	SET @fk_error_type =	(select fk_error_type	from #dim_invalid_error_list where row_id = @CursorID)
	SET @export_id =		(select export_id		from #dim_invalid_error_list where row_id = @CursorID)
	SET @pk_id =			(select pk_id			from #dim_invalid_error_list where row_id = @CursorID)

	SET @update_string_1 =	'INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = ''tbu_trans_detail_original''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM tbu_trans_detail_original
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';

							UPDATE tbu_trans_detail_original set '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';
							
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = ''tbu_trans_detail''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM tbu_trans_detail
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';

							UPDATE tbu_trans_detail set '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							and budget_year = '+CONVERT(VARCHAR(20),@budget_year) +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';'
	SET @update_string_2 = '
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = ''tfp_proj_transactions''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							from tfp_proj_transactions PT
							JOIN	( --Fetch budget rounds to be included in finplan (no budget changes for current year)
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year < '+CONVERT(VARCHAR(20),@budget_year)+'
									UNION
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year = '+CONVERT(VARCHAR(20),@budget_year)+'
									and org_budget_flag = 1
									)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
							where year = '+CONVERT(VARCHAR(20),@budget_year)+'
							AND pt.fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
							AND '+ @dim_1_type + '= ''' + @dim_1_id + ''';
							
							UPDATE PT SET '+ @dim_1_type + '= '''+ @id_new + '''
							from tfp_proj_transactions PT
							JOIN	( --Fetch budget rounds to be included in finplan (no budget changes for current year)
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year < '+CONVERT(VARCHAR(20),@budget_year)+'
									UNION
									select fk_tenant_id, pk_change_id from tfp_budget_changes
									where fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
									and budget_year = '+CONVERT(VARCHAR(20),@budget_year)+'
									and org_budget_flag = 1
									)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
							where year = '+CONVERT(VARCHAR(20),@budget_year)+'
							AND pt.fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+'
							AND '+ @dim_1_type + '= ''' + @dim_1_id + ''';'
							
								
	SET @update_string_3 =	'INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = '''+@log_table_name+'''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM '+@log_table_name+' 
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';

							UPDATE '+@log_table_name+' set '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';
							
							INSERT INTO [dbo].[tbu_integ_error_handling_updates_log]
							([fk_tenant_id],[export_id],[batch_id],[table_name],[table_pk_id],[fk_error_type],[dim_1_type],[dim_2_type],[dim_1_id],[dim_2_id],[dim_to_change],[id_new],[update_time])
							SELECT 
							fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id) +'
							,export_id = '+ @export_id +'
							,batch_id = '''+@batch_id+'''
							,table_name = '''+@postback_table_name+'''
							,table_pk_id = pk_id
							,fk_error_type = '''+ @fk_error_type + '''
							,dim_1_type = '''+ @dim_1_type + '''
							,dim_2_type = '''+ @dim_2_type + '''
							,dim_1_id = '''+ @dim_1_id + '''
							,dim_2_id = '''+ @dim_2_id + '''
							,dim_to_change = '''+ @dim_to_change + '''
							,id_new = '''+ @id_new + '''
							,update_time = getdate()
							FROM '+@postback_table_name+' 
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud' THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';

							UPDATE '+@postback_table_name+' set '+ CASE WHEN @dim_1_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud' THEN 'department_code' ELSE @dim_1_type END + ' = '''+ @id_new + '''
							where fk_tenant_id = '+ CONVERT(VARCHAR(20),@tenant_id) +'
							AND export_id = '+ @export_id +'
							AND '+ CASE WHEN @dim_1_type = 'fk_department_code' AND @postback_table_name = 'integ_agrpostback_origbud'  THEN 'department_code' ELSE @dim_1_type END + '= ''' + @dim_1_id + ''';
							
							UPDATE tbu_integ_error_handling SET fixed = 1 WHERE fk_tenant_id = '+CONVERT(VARCHAR(20),@tenant_id)+' AND pk_id = '+@pk_id
	
	--PRINT @update_string_1
	--PRINT @update_string_2
	--PRINT @update_string_3
	EXEC sp_executesql @update_string_1
	EXEC sp_executesql @update_string_2
	EXEC sp_executesql @update_string_3
   
	SET @CursorID = @CursorID + 1 
 
END

--Bug 170041 - Removing this since we're always generating a full new export. If we set this to fixed, then the batch from the error log will be sent AND the new full report. This results in the budget being sent twice.
/*

/******* SET ERROR FIXED FOR BATCH IF ALL ERRORS FIXED *******/ 

DECLARE @total_rows INT
DECLARE @fixed_rows INT

SET @total_rows =	(
					select COUNT(*) from tbu_integ_error_handling
					where batch_id = @batch_id
					)

SET @fixed_rows =	(
					select COUNT(*) from tbu_integ_error_handling
					where batch_id = @batch_id
					AND ([keep] = 1 OR [fixed] = 1)
					)

IF @total_rows = @fixed_rows
BEGIN
	IF @tenant_type = 'Visma'
	BEGIN
		UPDATE integnew_export_visma_postbatches SET error_fixed = 1
		where batch_id = @batch_id
		AND fk_tenant_id = @tenant_id
	END

	IF @tenant_type = 'Agresso'
	BEGIN
		UPDATE integ_agrpostback_batches SET error_fixed = 1
		where Batchid = @batch_id
		AND fk_tenant_id = @tenant_id
	END
END

*/