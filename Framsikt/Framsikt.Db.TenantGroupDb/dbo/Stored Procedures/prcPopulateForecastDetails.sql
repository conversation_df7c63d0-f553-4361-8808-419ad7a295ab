CREATE OR ALTER PROCEDURE [dbo].[prcPopulateForecastDetails]

		@tenant_id int,
		@forecast_period varchar(20),
		@ID UNIQUEIDENTIFIER,
		@org_version NVARCHAR(50)
AS
BEGIN	

	 --DECLARE @tenant_id int= 7892,	@forecast_period varchar(20) = '202406',	@ID UNIQUEIDENTIFIER = newID(),@org_version NVARCHAR(50) ='2023v1'
	 
	 DECLARE @budget_year INT = CAST(@forecast_period/100 AS INT)
     DECLARE @org_level NVARCHAR(25) = (SELECT param_value FROM tco_parameters WHERE fk_tenant_id = @tenant_id AND param_name = '1B_FORECAST_ORG_SA' AND active = 1)  
   
     DECLARE @param_value_service nvarchar(500)  
     SET @param_value_service = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id AND active = 1 AND param_name = 'MRDOC_FUNCTION_TABLE_AGG_LEVEL')  
  
     IF @param_value_service IS NULL  
     BEGIN  
      SET @param_value_service = ''  
     END  
  
     SELECT pk_account_code into #tempAccountCodes FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1   
     WHERE f.pk_tenant_id = @tenant_id AND g.fk_kostra_account_code = f.fk_kostra_account_code   
     AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT'   
     AND p1.active = 1 AND g.line_item_id NOT IN (140,150,320,330,350,360,210,230, 410,250,160,170)   
  
     SELECT pk_account_code into #tempAccountCodeswithallLineitems FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1 WHERE f.pk_tenant_id = @tenant_id  
     AND g.fk_kostra_account_code = f.fk_kostra_account_code AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id  
     AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT' AND p1.active = 1  
  

     CREATE TABLE #FinancialForecast    
     (fk_tenant_id INT,forecast_period INT,mr_level_1_value varchar(10),mr_level_2_value varchar(10),aggregate_id varchar(25),aggregate_name varchar(100),aggregate_short_name varchar(100),  
     forecast_amount decimal(18,2),original_budget decimal(18,2),revised_budget decimal(18,2),bud_amt_ytd decimal(18,2),bud_amt_period decimal(18,2),actual_amt_year decimal(18,2),  
     actual_amt_ytd decimal(18,2),actual_amt_period decimal(18,2),org_bud_amt_last_year decimal(18,2),actual_amt_last_ytd decimal(18,2),budget_changes decimal(18,2),revised_budget_income decimal(18,2),  
     revised_budget_expence decimal(18,2),actual_amt_ytd_income decimal(18,2),  
     actual_amt_ytd_expence decimal(18,2),previous_forecast decimal(18,2),remaining_amount decimal(18,2),income_flag int,last_entered_forecast decimal(18,2),  
     function_code varchar(100),function_name varchar(500), attribute_id nvarchar(25), unaprv_bud_change decimal(18,2))  
  
     CREATE  CLUSTERED INDEX [IX_FinancialForecast]  
     ON #FinancialForecast  
     (fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
     aggregate_short_name,income_flag,function_code,function_name)   
   
  
     -- LOGIC FOR FETCHING PREVIOUS FORECAST STARTS  
     declare @previousForeCast varchar(20)   
     select top 1 @previousForeCast =forecast_period   from tmr_period_setup where fk_tenant_id = @forecast_period and forecast_period < @forecast_period  
     order by forecast_period desc  
  
     IF @previousForeCast IS NOT NULL  
      BEGIN   
       PRINT 'Previous Forecast Available'  
       SET @previousForeCast  = CAST ((CAST ( @forecast_period AS INT) - CAST (@previousForeCast AS INT) ) AS varchar(20))  
      END  
     ELSE  
      BEGIN  
       SET @previousForeCast  = '1'  
      END   
  
     SET @previousForeCast = CAST ((CAST ( @forecast_period AS INT) - CAST (@previousForeCast AS INT) ) AS varchar(20))  
     SELECT @previousForeCast  
     -- LOGIC FOR FETCHING PREVIOUS FORECAST ENDS  
  
  
     Select * into #tco_org_hierarchy from tco_org_hierarchy where fk_tenant_id = @tenant_id and fk_org_version = @org_version  
     SELECT * into #temp  from dbo.getDatafromDW (@tenant_id, @forecast_period)  
  
     Select department_code,fk_tenant_id,fk_account_code,forecast_period,fk_kostra_account_code,sum(forecast_amount) as  forecast_amount,  
     fk_function_code,[1A_line]  into #tempForecast from dbo.getDataforForecast(@tenant_id, @forecast_period)  
     group by department_code,fk_tenant_id,fk_account_code,forecast_period,fk_kostra_account_code,  
     fk_function_code,[1A_line]  
  
     Select * into #tempChangedForecast from dbo.getDataforChangedForecast(@tenant_id, @forecast_period)  
     Select * into #tempBudgetChanges from dbo.getDataforProposedBudgetChanges(@tenant_id, @forecast_period)  
     Select * into #tempDeviationData from dbo.getDeviationData(@tenant_id, @forecast_period)   
     select * into #tempFunctionServices from dbo.getFunctionServices(@tenant_id,@budget_year)
     select * into #tempAttributesForForecast from dbo.getAttributesForForecast(@tenant_id,@budget_year)
   
     Select department_code,fk_tenant_id,fk_account_code,forecast_period,fk_kostra_account_code,sum(forecast_amount) as  forecast_amount,  
     fk_function_code,[1A_line] into #tempForecastPrevious from dbo.getDataforForecast(@tenant_id, @previousForeCast)  
     group by department_code,fk_tenant_id,fk_account_code,forecast_period,fk_kostra_account_code,  
     fk_function_code,[1A_line]  
  
   
     BEGIN  
      -- 1 DATA FROM DATAWAREHOUSE  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name, attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value = CASE   
        WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
        WHEN p3.param_value = 'service_id_2' THEN b.service_id_2  
      ELSE org_id_2  
      END,  
      aggregate_id = CASE   
		WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE 
		WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE 
		WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE org_name_3  
      END,  
      0 as forecast_amount,  original_budget, revised_budget,   
      bud_amt_ytd,bud_amt_period,actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,  
      0 as budget_changes,  
      revised_budget_income = CASE WHEN k.income_flag = 1 then revised_budget else 0 end,  
      revised_budget_expence = CASE WHEN k.income_flag = 0 then revised_budget else 0 end,   
      actual_amt_ytd_income = CASE WHEN k.income_flag = 1 then actual_amt_ytd else 0 end,  
      actual_amt_ytd_expence = CASE WHEN k.income_flag = 0 then actual_amt_ytd else 0 end,   
      0 as previous_forecast,0 as remaining_amount,isnull(k.income_flag, 0) ,0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = ISNULL(trv.pk_attribute_id, ''),
      a.unaprv_bud_change
      FROM #temp a  
      LEFT JOIN #tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year 
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN #tempAttributesForForecast trv ON a.fk_tenant_id = trv.fk_tenant_id  and a.fk_department_code = trv.pk_department_code and a. forecast_period/100 = trv.budget_year
      WHERE --ov.active = 1 AND  
      p2.param_value like 'org_id%'  
      AND a.[1A_line] = 0 
  
      -- 2 DATA FOR FORECAST TRANSACTIONS   
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value = CASE   
        WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p3.param_value = 'service_id_2' THEN b.service_id_2   
      ELSE org_id_2  
      END,  
      aggregate_id = CASE   
		WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE org_name_3  
      END,  
      forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes,revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      forecast_expence = 0,0 as remaining_amount,isnull(k.income_flag, 0) ,0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = ISNULL(trv.pk_attribute_id, ''),
      0 AS unaprv_bud_change
      FROM #tempForecast a      
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year  
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN #tempAttributesForForecast trv ON a.fk_tenant_id = trv.fk_tenant_id  and a.department_code = trv.pk_department_code and a. forecast_period/100 = trv.budget_year
      WHERE --ov.active = 1 AND   
      p2.param_value like 'org_id%'   
      AND a.[1A_line] = 0   
  
     
     -- 2 DATA FOR PREVIOUS FORECAST TRANSACTIONS   
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name, attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value = CASE   
        WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p3.param_value = 'service_id_2' THEN b.service_id_2   
      ELSE org_id_2  
      END,  
      aggregate_id = CASE   
		WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE org_name_3  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes,revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      forecast_expence = a.forecast_amount,0 as remaining_amount,isnull(k.income_flag, 0) ,0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = ISNULL(trv.pk_attribute_id, ''),
      0 AS unaprv_bud_change
      FROM #tempForecastPrevious a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN #tempAttributesForForecast trv ON a.fk_tenant_id = trv.fk_tenant_id  and a.department_code = trv.pk_department_code and a. forecast_period/100 = trv.budget_year
      WHERE --ov.active = 1 AND   
      p2.param_value like 'org_id%'   
      AND a.[1A_line] = 0   

      -- CHANGED FORECAST DATA  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name, attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value = CASE   
        WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p3.param_value = 'service_id_2' THEN b.service_id_2   
      ELSE org_id_2  
      END,  
      aggregate_id = CASE   
		WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE org_name_3  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes,revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      forecast_expence = 0,0 as remaining_amount,isnull(k.income_flag, 0) ,isnull(change_forecast_amount, 0),  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = ISNULL(trv.pk_attribute_id, ''),
      0 AS unaprv_bud_change
      FROM #tempChangedForecast a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN #tempAttributesForForecast trv ON a.fk_tenant_id = trv.fk_tenant_id  and a.department_code = trv.pk_department_code and a. forecast_period/100 = trv.budget_year
      WHERE --ov.active = 1 AND   
      p2.param_value like 'org_id%' AND a.[1A_line]=0  

  
      -- 3 DATA FOR BUDGET CHANGES  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
      SELECT a.fk_tenant_id,  a.forecast_period,mr_level_1_value = CASE   
        WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
        WHEN p3.param_value = 'service_id_2' THEN b.service_id_2  
      ELSE org_id_2  
      END,  
      aggregate_id = CASE 
		WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1 
		WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE org_name_3  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      budget_changes, revised_budget_income = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      revised_budget_expence = 0, 0 as previous_forecast,0 as remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = ISNULL(trv.pk_attribute_id, ''),
      0 AS unaprv_bud_change
      FROM  #tempBudgetChanges a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN #tempAttributesForForecast trv ON a.fk_tenant_id = trv.fk_tenant_id  and a.department_code = trv.pk_department_code and a. forecast_period/100 = trv.budget_year
      WHERE --ov.active = 1 AND   
      k.type = 'operations'  
      AND a.action_type IN (1,2,3,4,60,100,101)  
      AND p2.param_value like 'org_id%'   
      AND a.is_MROverview_flag = 0  
  
  
      -- 4 DEVIATION DATA  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name, attribute_id, unaprv_bud_change)  
      SELECT a.fk_tenant_id,  a.forecast_period,mr_level_1_value = CASE   
        WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
        WHEN p3.param_value = 'service_id_2' THEN b.service_id_2  
      ELSE org_id_2  
      END,  
      aggregate_id = CASE   
		WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	
		WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE org_name_3  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes, revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,  0 as previous_forecast,remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = ISNULL(trv.pk_attribute_id, ''),
      0 AS unaprv_bud_change
      FROM  #tempDeviationData a  
      LEFT JOIN #tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN #tempAttributesForForecast trv ON a.fk_tenant_id = trv.fk_tenant_id  and a.fk_department_code = trv.pk_department_code and a. forecast_period/100 = trv.budget_year
      WHERE p2.param_value like 'org_id%'  
  
      -- 5 DATA FROM DATAWAREHOUSE  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value =   
      CASE	WHEN p2.param_value = 'service_id_1' THEN b.service_id_1  
        WHEN p2.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p2.param_value = 'service_id_3' THEN b.service_id_3  
        WHEN p2.param_value = 'service_id_4' THEN b.service_id_4  
        WHEN p2.param_value = 'service_id_5' THEN b.service_id_5   
      ELSE service_id_2  
      END,  
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,  
      aggregate_id = CASE   
        WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_id_3  
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_name_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_name_3  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE ''  
      END,  
      0 as forecast_amount,  original_budget, revised_budget,   
      bud_amt_ytd,bud_amt_period,actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,  
      0 as budget_changes,  
      revised_budget_income = CASE WHEN k.income_flag = 1 then revised_budget else 0 end,  
      revised_budget_expence = CASE WHEN k.income_flag = 0 then revised_budget else 0 end,   
      actual_amt_ytd_income = CASE WHEN k.income_flag = 1 then actual_amt_ytd else 0 end,  
      actual_amt_ytd_expence = CASE WHEN k.income_flag = 0 then actual_amt_ytd else 0 end,   
      0 as previous_forecast,0 as remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = '',
      a.unaprv_bud_change
      FROM #temp a  
      LEFT JOIN #tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      --WHERE ov.active = 1  
      WHERE a.[1A_line] = 0 AND p2.param_value like 'service_id%'  
    
      -- 6 DATA FOR FORECAST TRANSACTIONS   
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value =   
      CASE WHEN p2.param_value = 'service_id_1' THEN b.service_id_1  
        WHEN p2.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p2.param_value = 'service_id_3' THEN b.service_id_3  
        WHEN p2.param_value = 'service_id_4' THEN b.service_id_4  
        WHEN p2.param_value = 'service_id_5' THEN b.service_id_5   
      ELSE service_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,  
        aggregate_id = CASE   
        WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_id_3  
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_name_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_name_3  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE ''  
      END,  
      forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes,revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      forecast_expence = 0,0 as remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = '',
      0 AS unaprv_bud_change
      FROM #tempForecast a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version 
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      WHERE p2.param_value like 'service_id%' AND a.[1A_line] = 0   
  
      -- 6 DATA FOR PREVIOUS FORECAST TRANSACTIONS   
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value =   
      CASE WHEN p2.param_value = 'service_id_1' THEN b.service_id_1  
        WHEN p2.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p2.param_value = 'service_id_3' THEN b.service_id_3  
        WHEN p2.param_value = 'service_id_4' THEN b.service_id_4  
        WHEN p2.param_value = 'service_id_5' THEN b.service_id_5   
      ELSE service_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,  
        aggregate_id = CASE   
        WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_id_3  
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_name_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_name_3  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE ''  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes,revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      forecast_expence = a.forecast_amount ,0 as remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = '',
      0 AS unaprv_bud_change
      FROM #tempForecastPrevious a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      WHERE p2.param_value like 'service_id%' AND a.[1A_line] = 0 
  
      -- 6 DATA FOR CHANGE FORECAST TRANSACTIONS   
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
  
      SELECT a.fk_tenant_id, a.forecast_period,  
      mr_level_1_value =   
      CASE WHEN p2.param_value = 'service_id_1' THEN b.service_id_1  
        WHEN p2.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p2.param_value = 'service_id_3' THEN b.service_id_3  
        WHEN p2.param_value = 'service_id_4' THEN b.service_id_4  
        WHEN p2.param_value = 'service_id_5' THEN b.service_id_5   
      ELSE service_id_2  
      END,   
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,  
        aggregate_id = CASE   
        WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_id_3  
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_name_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_name_3  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE ''  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes,revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
      forecast_expence = 0,0 as remaining_amount,isnull(k.income_flag, 0) ,isnull(change_forecast_amount, 0),  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = '',
      0 AS unaprv_bud_change
      FROM #tempChangedForecast a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      WHERE p2.param_value like 'service_id%' AND a.[1A_line]=0  
  
      -- 7 DATA FOR BUDGET CHANGES  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
      SELECT a.fk_tenant_id,  a.forecast_period,  
        mr_level_1_value =   
      CASE	WHEN p2.param_value = 'service_id_1' THEN b.service_id_1  
        WHEN p2.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p2.param_value = 'service_id_3' THEN b.service_id_3  
        WHEN p2.param_value = 'service_id_4' THEN b.service_id_4  
        WHEN p2.param_value = 'service_id_5' THEN b.service_id_5   
      ELSE service_id_2  
      END,  
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,  
        aggregate_id = CASE   
        WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_id_3  
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_name_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_name_3  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE ''  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      budget_changes, revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
       0 as previous_forecast,0 as remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = '',
      0 AS unaprv_bud_change
      FROM  #tempBudgetChanges a  
      LEFT JOIN #tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      WHERE k.type = 'operations'  
      AND a.action_type IN (1,2,3,4,60,100,101)  
      AND p2.param_value like 'service_id%'   
      AND a.is_MROverview_flag = 0  
  
  
      -- 8 DEVIATION DATA  
      INSERT #FinancialForecast(fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,  
      aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,  
      actual_amt_year,actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,  
      revised_budget_income,revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,income_flag,last_entered_forecast,  
      function_code,function_name,attribute_id, unaprv_bud_change)  
      SELECT a.fk_tenant_id,  a.forecast_period,      
      mr_level_1_value =   
      CASE	WHEN p2.param_value = 'service_id_1' THEN b.service_id_1  
        WHEN p2.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p2.param_value = 'service_id_3' THEN b.service_id_3  
        WHEN p2.param_value = 'service_id_4' THEN b.service_id_4  
        WHEN p2.param_value = 'service_id_5' THEN b.service_id_5   
      ELSE service_id_2  
      END,  
      mr_level_2_value = CASE   
        WHEN p3.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p3.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p3.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p3.param_value = 'org_id_5' THEN oh.org_id_5   
      ELSE org_id_2  
      END,  
        aggregate_id = CASE   
        WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_id_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_id_3  
      ELSE org_id_3  
      END,  
      aggregate_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5  
        WHEN p1.param_value = 'service_id_2' THEN b.service_name_2  
        WHEN p1.param_value = 'service_id_3' THEN b.service_name_3  
        ELSE org_name_3  
      END,  
      aggregate_short_name =   
      CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_shortname_1  
        WHEN p1.param_value = 'org_id_2' THEN oh.org_shortname_2  
        WHEN p1.param_value = 'org_id_3' THEN oh.org_shortname_3  
        WHEN p1.param_value = 'org_id_4' THEN oh.org_shortname_4  
        WHEN p1.param_value = 'org_id_5' THEN oh.org_shortname_5  
        ELSE ''  
      END,  
      0 as forecast_amount, 0 as original_budget, 0 AS revised_budget,   
      0 AS bud_amt_ytd,0 AS bud_amt_period,0 AS actual_amt_year,0 AS actual_amt_ytd,  
      0 AS actual_amt_period,0 AS org_bud_amt_last_year, 0 AS actual_amt_last_ytd,  
      0 as budget_changes, revised_budget_income = 0,  
      revised_budget_expence = 0,  
      actual_amt_ytd_income = 0,  
      actual_amt_ytd_expence = 0,   
       0 as previous_forecast,remaining_amount,isnull(k.income_flag, 0) , 0 as last_entered_forecast,  
      function_code = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_id_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_id_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_id_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_id_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_id_5,'')  
      ELSE ISNULL(b.pk_function_code,'') END,  
      function_name = CASE   
        WHEN @param_value_service = 'service_id_1' THEN ISNULL(b.service_name_1,'')  
        WHEN @param_value_service = 'service_id_2' THEN ISNULL(b.service_name_2,'')  
        WHEN @param_value_service = 'service_id_3' THEN ISNULL(b.service_name_3,'')  
        WHEN @param_value_service = 'service_id_4' THEN ISNULL(b.service_name_4,'')  
        WHEN @param_value_service = 'service_id_5' THEN ISNULL(b.service_name_5,'')  
      ELSE ISNULL(b.function_name,'') END,  
      attribute_id = '',
      0 AS unaprv_bud_change
      FROM  #tempDeviationData a  
      LEFT JOIN #tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  
      LEFT JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version and ov.pk_org_version=@org_version  
      LEFT JOIN #tempFunctionServices b ON a.fk_function_code = b.pk_function_code AND a.fk_tenant_id = b.fk_tenant_id AND @forecast_period/100 = b.budget_year
      JOIN tmd_reporting_line rl ON a.fk_account_code = rl.fk_account_code AND a.fk_tenant_id = rl.fk_tenant_id AND rl.report = 'MNDRAPP'  
      LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code  
      LEFT JOIN tco_parameters p1 ON p1.param_name = '1B_FORECAST_ORG_SA' AND p1.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p2 ON p2.param_name = 'MONTHREP_LEVEL_1' AND p2.fk_tenant_id = a.fk_tenant_id  
      LEFT JOIN tco_parameters p3 ON p3.param_name = 'MONTHREP_LEVEL_2' AND p3.fk_tenant_id = a.fk_tenant_id  
      WHERE p2.param_value like 'service_id%'   
  
   
  
     END  
  
     IF (SELECT COUNT(*) FROM tco_parameters p WHERE p.fk_tenant_id = @tenant_id AND p.param_name = 'MR_FINSTATUS_FUNCTION' AND p.active = 1) > 0  
     BEGIN   
  
     -- STORY 49166 --- added parameter MR_FINSTATUS_FUNCTION  
  
     declare @mr_function_value VARCHAR(100)   
  
     SET @mr_function_value = (SELECT param_value FROM tco_parameters p WHERE p.fk_tenant_id = @tenant_id AND p.param_name = 'MR_FINSTATUS_FUNCTION' AND p.active = 1)  
  
     UPDATE  a SET a.function_code = KF.pk_kostra_function_code, a.function_name = kf.kostra_function_name  
     FROM #FinancialForecast a  
     join tco_functions f ON a.fk_tenant_id = f.pk_tenant_id AND a.function_code = f.pk_Function_code  
     join gmd_kostra_function kf ON f.fk_kostra_function_code = kf.pk_kostra_function_code  
     WHERE @mr_function_value = 'kostra_function'  
  
  
     UPDATE  a SET a.function_code = sv.service_id_2, a.function_name = sv.service_name_2  
     FROM #FinancialForecast a  
     join tco_service_values sv ON a.function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id  
     WHERE @mr_function_value = 'service_id_2'  
  
     UPDATE  a SET a.function_code = sv.service_id_3, a.function_name = sv.service_name_3  
     FROM #FinancialForecast a  
     join tco_service_values sv ON a.function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id  
     WHERE @mr_function_value = 'service_id_3'  
  
     UPDATE  a SET a.function_code = sv.service_id_4, a.function_name = sv.service_name_4  
     FROM #FinancialForecast a  
     join tco_service_values sv ON a.function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id  
     WHERE @mr_function_value = 'service_id_4'  
  
     UPDATE  a SET a.function_code = sv.service_id_5, a.function_name = sv.service_name_5  
     FROM #FinancialForecast a  
     join tco_service_values sv ON a.function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id  
     WHERE @mr_function_value = 'service_id_5'  
  
  
  
  
  
     END  
  
  
    INSERT tmr_doc_ForecastDetails(RowID,fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,
    aggregate_short_name,forecast_amount,original_budget,revised_budget,bud_amt_ytd,bud_amt_period,actual_amt_year,
    actual_amt_ytd,actual_amt_period,org_bud_amt_last_year,actual_amt_last_ytd,budget_changes,revised_budget_income,
    revised_budget_expence,actual_amt_ytd_income,actual_amt_ytd_expence,previous_forecast,remaining_amount,last_entered_forecast,income_flag,
    function_code,function_name,forecast_incl_deviation,attribute_id, unaprv_bud_change)
    SELECT  @ID, fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,aggregate_short_name,
    sum(forecast_amount) ,sum(original_budget) ,sum(revised_budget) ,sum(bud_amt_ytd) ,sum(bud_amt_period) ,sum(actual_amt_year) ,sum(actual_amt_ytd) ,
    sum(actual_amt_period) ,sum(org_bud_amt_last_year) ,sum(actual_amt_last_ytd) ,sum(budget_changes) ,sum(revised_budget_income) ,sum(revised_budget_expence) ,sum(actual_amt_ytd_income) ,sum(actual_amt_ytd_expence),
    sum(previous_forecast),sum(remaining_amount), sum(last_entered_forecast),income_flag,function_code,function_name, (sum(forecast_amount) + sum(remaining_amount)),attribute_id, sum(unaprv_bud_change)
    from #FinancialForecast   
    group by fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,aggregate_short_name,income_flag,
    function_code,function_name,attribute_id;

    SELECT  @ID, fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,aggregate_short_name,
    sum(forecast_amount) ,sum(original_budget) ,sum(revised_budget) ,sum(bud_amt_ytd) ,sum(bud_amt_period) ,sum(actual_amt_year) ,sum(actual_amt_ytd) ,
    sum(actual_amt_period) ,sum(org_bud_amt_last_year) ,sum(actual_amt_last_ytd) ,sum(budget_changes) ,sum(revised_budget_income) ,sum(revised_budget_expence) ,
    sum(previous_forecast),sum(remaining_amount), sum(last_entered_forecast),income_flag,function_code,function_name, (sum(forecast_amount) + sum(remaining_amount)),attribute_id, sum(unaprv_bud_change)
    from #FinancialForecast   
    group by fk_tenant_id,forecast_period,mr_level_1_value,mr_level_2_value,aggregate_id,aggregate_name,aggregate_short_name,income_flag,
    function_code,function_name,attribute_id


	DROP TABLE #temp;
	DROP TABLE #FinancialForecast;
	DROP TABLE  #tco_org_hierarchy;
	DROP TABLE #tempForecast;
	DROP TABLE #tempChangedForecast;
	DROP TABLE #tempForecastPrevious
	DROP TABLE  #tempBudgetChanges;
	DROP TABLE #tempDeviationData; 
	DROP TABLE #tempAccountCodes;
	DROP TABLE #tempAccountCodeswithallLineitems
	DROP TABLE #tempFunctionServices
	DROP TABLE #tempAttributesForForecast
END
GO