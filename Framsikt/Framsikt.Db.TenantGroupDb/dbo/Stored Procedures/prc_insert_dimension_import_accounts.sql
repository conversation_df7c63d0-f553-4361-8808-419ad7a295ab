--CREATE PROCEDURES--   
  
CREATE OR ALTER procedure [dbo].[prc_insert_dimension_import_accounts]   
@tenant_id int ,   
@user_id int ,   
@dimension_id int ,   
@dimension_type varchar (20) ,   
@job_id bigint  
  
As  
    DECLARE  
        @fk_tenant_id INT = @tenant_id;  
  
    DROP TABLE IF EXISTS #budget_year       
 CREATE TABLE #budget_year (budget_year INT)  
    Declare @CurrentYear int = 2000  
 Declare @EndYear int = 2099  
  
 WHILE @CurrentYear <= @EndYear  
 BEGIN    
  Insert Into #budget_year Values(@CurrentYear)  
  Set @CurrentYear = @CurrentYear + 1  
 END    
  
        /******   GENERATE TABLES WITH BUDGET YEAR  ********/  
---Generate current details pr budget year  
DROP TABLE IF EXISTS #current_data  
select  
    [pk_tenant_id] ,
	[pk_account_code] ,
	[display_name] ,
	[description],
	[fk_kostra_account_code] ,
	[isActive]  ,
	[dateFrom]  ,
	[dateTo]   ,
	[fk_key_id],
	budget_year 
        INTO #current_data  
        from tco_accounts a  
         JOIN #budget_year b on b.budget_year BETWEEN DATEPART(YEAR,a.[dateFrom]) AND DATEPART(YEAR,a.[dateTo])  
        where pk_tenant_id = @fk_tenant_id  
        
---Generate details from stage table pr budget year  
DROP TABLE IF EXISTS #stage_import_years_account
CREATE TABLE #stage_import_years_account  
(  
    [fk_tenant_id] [int] NOT NULL,  
  [budget_year] INT NOT NULL,  
  [pk_account_code] [nvarchar](25) NOT NULL,  
  [account_name] [nvarchar](255) NOT NULL,  
  [kostra_account_code] [varchar](25) NULL, 
	[dateFrom]  [DATETIME] NULL,
	[dateTo] [DATETIME] NULL ,  
  [isActive] [bit] NULL,  
  [fk_key_id] [int] NULL,  
  [seq] INT
)
  
---- import data from actual staging table   
  
     IF( @job_id = -1 )--check if import is from excel or api (api import will have job id)  
                     BEGIN  
        INSERT #stage_import_years_account  
        select  
                    fk_tenant_id  
                   , budget_year  
                   , account_code  
                   , account_name  				   
					,kostra_account_code= CASE WHEN c.type IS NULL THEN '' ELSE a.kostra_account_code END
				   , CONVERT(DATETIME,DATEFROMPARTS(a.[account_year_from],1,1))  as [dateFrom]
                   ,CONVERT(DATETIME,DATEFROMPARTS(a.[account_year_To],12,31)) as [dateTo]
				   ,account_isActive as [isActive]
					,Null  as fk_key_id                                 
                   , seq = ROW_NUMBER() OVER (PARTITION BY a.main_project_code, b.budget_year ORDER BY a.year_to)  
                                              from tbu_stage_dimensions_import a  
                                                   JOIN #budget_year b  on b.budget_year BETWEEN (a.account_year_from) AND (a.account_year_To)  
											 LEFT JOIN gco_kostra_accounts c on a.kostra_account_code = c.pk_kostra_account_code  
                                            WHERE (c.type IN ('operations', 'investment') OR c.type IS NULL)
                                             and a.fk_tenant_id = @fk_tenant_id  
                                              AND a.dimension_type=@dimension_id  
                                              AND [user_id] = @user_id  
                    END   
                    ELSE   
                    BEGIN  
        INSERT #stage_import_years_account  
        select  fk_tenant_id  
                  fk_tenant_id  
                   , budget_year  
                   , account_code  
                   , account_name
					,kostra_account_code= CASE WHEN c.type IS NULL THEN '' ELSE a.kostra_account_code END				
                   , CONVERT(DATETIME,DATEFROMPARTS(a.[account_year_from],1,1))  as [dateFrom]
                   ,CONVERT(DATETIME,DATEFROMPARTS(a.[account_year_To],12,31)) as [dateTo]
				   ,account_isActive as [isActive]
					,Null  as  fk_key_id                               
                   , seq =  ROW_NUMBER() OVER (PARTITION BY a.main_project_code, b.budget_year ORDER BY a.year_to)  
                                              from tbu_stage_dimensions_import a  
                                                  JOIN #budget_year b  on b.budget_year BETWEEN (a.account_year_from) AND (a.account_year_To)  
 LEFT JOIN gco_kostra_accounts c on a.kostra_account_code = c.pk_kostra_account_code  
                                            WHERE (c.type IN ('operations', 'investment') OR c.type IS NULL) and a.fk_tenant_id = @fk_tenant_id AND a.dimension_type=@dimension_id AND [job_id]= @job_id  
  
                    END  


 DELETE a from   
 #stage_import_years_account a  
 LEFT JOIN  
 (  
  select fk_tenant_id,budget_year,pk_account_code,seq_max = MAX(seq)  
  from #stage_import_years_account a  
  GROUP BY fk_tenant_id,budget_year,pk_account_code  
 ) b  
 ON a.fk_tenant_id = b.fk_tenant_id and a.budget_year = b.budget_year and a.pk_account_code = b.pk_account_code and a.seq = b.seq_max  
 WHERE b.pk_account_code IS NULL  



 DROP TABLE IF EXISTS #duplicate_list  
 CREATE TABLE  #duplicate_list(  
  [fk_tenant_id] [int] NOT NULL,  
  [pk_account_code] [nvarchar](25) NOT NULL,  
 )

--Check for duplicates in stage table, if any found should log the IDs that is duplicate  
 INSERT #duplicate_list  
  select  fk_tenant_id,pk_account_code 
  FROM (  
  select a.fk_tenant_id, a.pk_account_code,budget_year, COUNT(*)cnt  
  from #stage_import_years_account a  
  GROUP BY a.fk_tenant_id, a.pk_account_code,budget_year  
  HAVING COUNT(*) >1  
  ) a  
  GROUP BY fk_tenant_id,pk_account_code  
   
                      
  
 --Update kostra account if missing in import  
 update a set kostra_account_code = b.fk_kostra_account_code  
 from #stage_import_years_account a  
 LEFT JOIN #current_data b ON a.fk_tenant_id = b.pk_tenant_id and a.pk_account_code = b.pk_account_code and a.budget_year = b.budget_year  
 WHERE (a.kostra_account_code IS NULL OR a.kostra_account_code = '')  
 and b.pk_account_code IS NOT NULL  
  
 --Set values based on current data for those fields that Framsikt is master for  
 update a set fk_key_id = b.fk_key_id  
 from #stage_import_years_account a  
 LEFT JOIN #current_data b ON a.fk_tenant_id = b.pk_tenant_id and a.pk_account_code = b.pk_account_code and a.budget_year = b.budget_year  
 WHERE b.pk_account_code IS NOT NULL  

  
--Create set with grouped values  
DROP TABLE IF EXISTS #import_pre_grouped_accounts
 select fk_tenant_id,pk_account_code,account_name,kostra_account_code, isActive,fk_key_id  
 , start_year = MIN (a.budget_year)  
 , end_year = MAX(a.budget_year)  
 INTO #import_pre_grouped_accounts  
 from #stage_import_years_account a  
 GROUP BY fk_tenant_id,pk_account_code,account_name,kostra_account_code, isActive,fk_key_id  
  

 --Sequence the grouped values  
 DROP TABLE IF EXISTS #import_sequenced  
 select *  
 ,seq = ROW_NUMBER() OVER (PARTITION BY a.pk_account_code ORDER BY a.start_year)  
 INTO #import_sequenced  
 from #import_pre_grouped_accounts a  
  
 --Fetch status for those records that come BEFORE we have data  
 update a set kostra_account_code = b.kostra_account_code  
 from #import_sequenced a  
 JOIN #import_sequenced b on a.pk_account_code = b.pk_account_code and a.seq = b.seq-1  
 where (a.kostra_account_code IS NULL OR a.kostra_account_code = '')  
  
 update a set fk_key_id = b.fk_key_id  
 from #import_sequenced a  
 JOIN #import_sequenced b on a.pk_account_code = b.pk_account_code and a.seq = b.seq-1  
 where a.fk_key_id IS NULL  
  
  
 --Fetch status for those records that come AFTER we have data  
 update a set kostra_account_code = b.kostra_account_code  
 from #import_sequenced a  
 JOIN #import_sequenced b on a.pk_account_code = b.pk_account_code and a.seq = b.seq+1  
 where (a.kostra_account_code IS NULL OR a.kostra_account_code = '')  
  
 update a set fk_key_id = b.fk_key_id  
 from #import_sequenced a  
 JOIN #import_sequenced b on a.pk_account_code = b.pk_account_code and a.seq = b.seq+1  
 where a.fk_key_id IS NULL  
  
 --Set defaults where we don't have values  
 update a set kostra_account_code = '1'  
 from #import_sequenced a  
 where (a.kostra_account_code IS NULL OR a.kostra_account_code = '')  
  
  
 /******   CREATE FINAL TABLE BEFORE INSERTS  ********/  
DROP TABLE IF EXISTS #final_before_import  
 select fk_tenant_id,pk_account_code,account_name,kostra_account_code, isActive,fk_key_id  
 ,start_year = CONVERT(DATETIME,DATEFROMPARTS(MIN(start_year),1,1))  
 ,end_year = CONVERT(DATETIME,DATEFROMPARTS(MAX(end_year),12,31))  
 ,need_to_update = 1  
 INTO #final_before_import  
 from #import_sequenced 
 GROUP BY fk_tenant_id,pk_account_code,account_name,kostra_account_code, isActive,fk_key_id   
  
--Identical rows are set to not be updated  
 update a set need_to_update = 0  
 from #final_before_import a  
 LEFT JOIN tco_accounts b   
 ON a.fk_tenant_id = b.pk_tenant_id   
 AND a.pk_account_code = b.pk_account_code  
 AND a.account_name = b.display_name  
 AND ISNULL(a.kostra_account_code,'') = b.fk_kostra_account_code  
 AND a.isActive = b.isActive  
 AND ISNULL(a.fk_key_id,0) = ISNULL(b.fk_key_id,0)  
 AND a.start_year = b.dateFrom  
 AND a.end_year = b.dateTo  
 where b.pk_account_code IS NOT NULL  
  
 --Create LIST of projects to delete and insert - duplicate projects are excluded  
 DROP TABLE IF EXISTS #list_to_import  
 CREATE TABLE #list_to_import (pk_tenant_id INT NOT NULL, pk_account_code NVARCHAR(25) NOT NULL)  
 INSERT #list_to_import  
 select a.fk_tenant_id,a.pk_account_code  
 from #final_before_import a  
 LEFT JOIN #duplicate_list b on a.fk_tenant_id = b.fk_tenant_id and a.pk_account_code = b.pk_account_code  
 where need_to_update = 1 AND b.pk_account_code IS NULL  
 GROUP BY a.fk_tenant_id,a.pk_account_code  
   
   

                    
/******   EXECUTE DELETE AND INSERT  ********/  

BEGIN TRY  
BEGIN TRANSACTION  
         
  INSERT INTO [dbo].[tco_import_account_log]
  ( [job_id]
,[timestamp]
,[legend]
,[user_id]
,[fk_tenant_id]
,[pk_account_code]
,[display_name]
,[description]
,[fk_kostra_account_code]
,[isActive]
,[dateFrom]
,[dateTo]
,[fk_key_id])
select * FROM
(
select [job_id] = @job_id
,[timestamp] = getdate()
  ,[legend] = 'NEW'
  ,[user_id]= @user_id
  ,a.[fk_tenant_id]
  ,a.pk_account_code    
  ,display_name = account_name
  ,description = account_name
  ,kostra_account_code
  ,[isActive]
  ,[dateFrom] = start_year
  ,[dateTo] = end_year
  ,fk_key_id
from #final_before_import a
JOIN #list_to_import b ON a.fk_tenant_id = b.pk_tenant_id and a.pk_account_code = b.pk_account_code

UNION ALL

select [job_id] = @job_id
,[timestamp] = getdate()
  ,[legend] = 'OLD'
  ,a.[updated_by] as [user_id]
  ,a.[pk_tenant_id]  
  ,a.pk_account_code  
  ,display_name
  ,description
  ,fk_kostra_account_code
  ,[isActive]
  ,[dateFrom]
  ,[dateTo]
  ,fk_key_id
from tco_accounts a
JOIN #list_to_import b ON a.pk_tenant_id = b.pk_tenant_id and a.pk_account_code = b.pk_account_code
) A
       --Execute delete  
   DELETE a  
   from tco_accounts a  
   JOIN #list_to_import b ON a.pk_tenant_id = b.pk_tenant_id and a.pk_account_code = b.pk_account_code  
     
   --Insert changed and new IDs  
   INSERT INTO [dbo].[tco_accounts]  
        ([pk_tenant_id]  
        ,[pk_account_code]  
        ,[display_name]  
        ,[description]  
        ,[fk_kostra_account_code]  
        ,[isActive]  
        ,[dateFrom]  
        ,[dateTo]  
        ,[updated]  
        ,[updated_by]  
        ,[fk_key_id])  
   select   
      a.[fk_tenant_id]  
        ,a.[pk_account_code]  
        ,[display_name] = account_name  
        ,[description] = account_name  
        ,[fk_kostra_account_code] = kostra_account_code  
        ,[isActive]  
        ,[dateFrom] = start_year  
        ,[dateTo] = end_year  
        ,[updated] = getdate()  
        ,[updated_by] = @user_id  
        ,[fk_key_id]  
   from #final_before_import a  
   JOIN #list_to_import b ON a.fk_tenant_id = b.pk_tenant_id and a.pk_account_code = b.pk_account_code  
    
	 Declare @transffered int = 0;
	 SELECT @transffered=count(*) from #final_before_import;  
   insert into tco_import_log(status_msg,import_type,updated_by,updated,fk_job_id,fk_tenant_id,transferred,error_count,budget_year)  
  SELECT 'Account Imported Sucessfully',@dimension_type,@user_id, GETDATE(), @job_id, @fk_tenant_id,@transffered,0 ,0;  
  
COMMIT TRANSACTION; 
 END TRY  
 BEGIN CATCH  
 RAISERROR('error deleting-inserting accounts', 0, 1) with nowait;  
 IF (@@TRANCOUNT > 0)  
  BEGIN  
   ROLLBACK TRANSACTION;       
  END   
  
    
   insert into tco_import_log(status_msg,import_type,updated_by,updated,fk_job_id,fk_tenant_id,transferred,error_count,budget_year)  
  SELECT CONCAT('Account Imported Failled', '-', ERROR_MESSAGE()),@dimension_type,@user_id, GETDATE(), @job_id, @fk_tenant_id,0,0 ,0; 
  
   RETURN;--Stops the SP  
 END CATCH  