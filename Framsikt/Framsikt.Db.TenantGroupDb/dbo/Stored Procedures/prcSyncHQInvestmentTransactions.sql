CREATE OR ALTER PROCEDURE [dbo].[prcSyncHQInvestmentTransactions]
	@tenant_id INT = 0,
	@budget_year INT = 0

AS
	
DECLARE @job_running INT
DECLARE @timestamp datetime2
DECLARE @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @RowsUpdated INT
DECLARE @last_investment_id AS INT
DECLARE @last_inv_details_id INT
DECLARE @last_pk_id INT
DECLARE @last_header_actionid INT
DECLARE @current_year INT
DECLARE @next_year INT

SET @current_year = (SELECT DATEPART(YEAR,GETDATE()))
SET @next_year = @current_year + 1

SET NOCOUNT ON

DECLARE @tenant_table TABLE (tenant_id_mother INT NOT NULL, 
	tenant_id_child INT NOT NULL,
	[budget_year] INT NOT NULL,
	[fk_change_id] INT NOT NULL,
	[fk_program_code] NVARCHAR(25) NOT NULL)

DECLARE @investment_removed  TABLE (
	fk_tenant_id INT NOT NULL,
	pk_investment_id INT NOT NULL)

DECLARE @inv_detail_removed  TABLE (
	fk_tenant_id INT NOT NULL,
	pk_investment_id INT NOT NULL,
	fk_inv_action_id INT NOT NULL,
	budget_year INT NOT NULL)

DECLARE @inv_fp_removed  TABLE (
	fk_tenant_id INT NOT NULL,
	pk_investment_id INT NOT NULL,
	fk_inv_action_id INT NOT NULL,
	budget_year INT NOT NULL)


DECLARE @investments TABLE (
	[pk_id] INT NOT NULL IDENTITY,
	[pk_investment_id] INT NOT NULL, 
	[pk_investment_id_new] INT NOT NULL, 
	[fk_tenant_id] INT NOT NULL,
    [fk_tenant_id_new] NVARCHAR(25) NOT NULL, 
    [status] INT NULL, 
    [self_cost_flag] INT NULL, 
    [budget_year_established] INT NOT NULL, 
    [start_year] INT NOT NULL, 
	[completion_pct] DECIMAL(18, 2) NOT NULL, 
    [approval] NVARCHAR(50) NULL, 
    [approval_date] DATE NULL, 
    [responsible] INT NULL, 
    [fk_org_id] NVARCHAR(25) NOT NULL, 
	[org_name] NVARCHAR(100) NOT NULL, 
    [fk_project_id] NVARCHAR(25) NULL, 
	project_phase NVARCHAR(25) NULL,
	fk_fdv_codes nvarchar(25) NULL,
	unit_value DECIMAL(18, 2) NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL,
	[previously_budgeted]  DECIMAL (18, 2) NOT NULL,
    [previously_financing]  DECIMAL (18, 2) NOT NULL,
	[monthrep_flag] BIT  DEFAULT ((1)) NOT NULL,
	[fk_main_project_code] NVARCHAR(25)  NULL,
	[fk_portfolio_code] NVARCHAR(25) NOT NULL,
	[tags] NVARCHAR(4000)  NOT NULL,
	[goalId] uniqueidentifier NOT NULL,
    [prev_actual] DECIMAL(18, 2) NULL, 
	[update_flag] VARCHAR(1) NOT NULL)


DECLARE @budgetyear_config TABLE
(
	[Id] INT NOT NULL IDENTITY, 
	[fk_tenant_id] INT NOT NULL,
	[fk_tenant_id_new] INT NOT NULL,
    [fk_investment_id] INT NOT NULL,
	[fk_investment_id_new] INT NOT NULL,  
    [budget_year] INT NOT NULL, 
    [inv_status] INT NOT NULL, 
    [priority] INT NOT NULL,
	[investment_name] NVARCHAR(100) NOT NULL,
	[completion_date] DATE NOT NULL,
	[updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL
)

DECLARE @investment_detail TABLE (	[pk_id] INT NOT NULL IDENTITY, 
	[fk_inv_detail_id] INT NOT NULL,
	[fk_inv_detail_id_new] INT NOT NULL,
    [fk_investment_id] INT NOT NULL, 
	[fk_investment_id_new] INT NOT NULL, 
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [fk_tenant_id_new] NVARCHAR(25) NOT NULL, 
	[budget_year] INT NOT NULL, 
	[start_year] INT NOT NULL,
    [description] NVARCHAR(MAX) NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	[vat_refund] DECIMAL(18, 2) NOT NULL,     
	[year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
    [year_5_amount] DECIMAL(18, 2) NOT NULL, 
    [year_6_amount] DECIMAL(18, 2) NOT NULL, 
    [year_7_amount] DECIMAL(18, 2) NOT NULL, 
    [year_8_amount] DECIMAL(18, 2) NOT NULL,     
	[year_9_amount] DECIMAL(18, 2) NOT NULL, 
    [year_10_amount] DECIMAL(18, 2) NOT NULL, 
	[type] NVARCHAR(2) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [existing_flag] INT NULL, 
	[fk_change_id] int not null,
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
	[fk_program_code] nvarchar(50) NOT NULL, 
	[update_flag] VARCHAR(1) NOT NULL,
	[fk_inv_action_id] int NULL DEFAULT (0),
	[status] INT NOT NULL)

DECLARE @investment_finplan_trans TABLE (
	[pk_id] INT NOT NULL IDENTITY, 
	[fk_inv_action_id] int NOT NULL DEFAULT (0),
	[fk_inv_details_id] int NOT NULL,
    [fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [department_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	vat_refund DECIMAL(18, 2) NOT NULL, 
	[year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
	[updated] DATETIME NOT NULL, 
	[type] NVARCHAR(1) NOT NULL,
    [updated_by] INT NOT NULL, 
    [existing_flag] INT NULL, 
	[fk_change_id] int not null,
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
    [fk_prog_code] NVARCHAR(25) DEFAULT 1 NULL,	 
	[update_flag] VARCHAR(1) NOT NULL)

DECLARE @inv_header TABLE  (
	pk_id INT NOT NULL IDENTITY, 
	fk_tenant_id INT NOT NULL,
	action_name nvarchar(150) NOT NULL,
	budget_year INT NOT NULL,
    [description] UNIQUEIDENTIFIER NOT NULL, 
	[action_type] [int] NOT NULL,
	[line_order] [int] NOT NULL,
	[isManuallyAdded] [int] NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] INT NOT NULL)

DECLARE @header_level_table TABLE (
	pk_id INT NOT NULL IDENTITY,
	fk_tenant_id INT NOT NULL,
	fk_investment_id INT NOT NULL,
	org_id NVARCHAR(25) NOT NULL,
	org_name NVARCHAR(100) NOT NULL)



SET @job_running = (SELECT MAX(run_flag) FROM [twh_report_job_queue] WHERE job_name IN ('HQINVUPDATE'))

SET @RowsUpdated = @@rowcount;


IF @job_running = 1
	BEGIN
	PRINT 'Job already running. No update is done.'
	RETURN
	END
ELSE 
	BEGIN
	SELECT @StartDate = sysdatetime();
	select @timestamp = sysdatetime();
	PRINT 'Job starting at ' + convert(nvarchar(19),@timestamp)
	RAISERROR ('Job starting', 0, 1) WITH NOWAIT
	
	UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'HQINVUPDATE';
	END

IF @tenant_id != 0 AND @budget_year != 0

BEGIN

PRINT 'Insert into tenant table'

INSERT INTO @tenant_table (tenant_id_mother, tenant_id_child, budget_year, fk_change_id, fk_program_code)
SELECT a.fk_tenant_id, a.tenant_id_child, @budget_year, default_change_id,CONVERT(INT,b.municipality_id)
FROM tmd_hq_tenant_definition a, gco_tenants b
WHERE tenant_id_child = @tenant_id
AND a.tenant_id_child = b.pk_id
AND a.budget_year = @budget_year

END

IF @tenant_id = 0 AND @budget_year = 0

BEGIN

INSERT INTO @tenant_table (tenant_id_mother, tenant_id_child, budget_year, fk_change_id, fk_program_code)
SELECT a.fk_tenant_id, a.tenant_id_child, @current_year, default_change_id,CONVERT(INT,b.municipality_id)
FROM tmd_hq_tenant_definition a, gco_tenants b
WHERE a.tenant_id_child = b.pk_id
AND a.budget_year = @current_year


INSERT INTO @tenant_table (tenant_id_mother, tenant_id_child, budget_year, fk_change_id, fk_program_code)
SELECT a.fk_tenant_id, a.tenant_id_child, @next_year, default_change_id, CONVERT(INT,b.municipality_id)
FROM tmd_hq_tenant_definition a, gco_tenants b
WHERE a.tenant_id_child = b.pk_id
AND a.budget_year = @next_year


END

--BEGIN

--PRINT 'Find change id'

--UPDATE @tenant_table SET fk_change_id = b.pk_change_id
--FROM @tenant_table a, (SELECT fk_tenant_id,budget_year, MAX(pk_change_id) AS pk_change_id FROM tfp_budget_changes WHERE org_budget_flag = 1 GROUP BY fk_tenant_id, budget_year) b
--WHERE a.tenant_id_mother = b.fk_tenant_id
--AND a.budget_year = b.budget_year

--UPDATE @tenant_table SET fk_change_id = b.pk_change_id
--FROM @tenant_table a, (SELECT fk_tenant_id,budget_year, MAX(pk_change_id) AS pk_change_id FROM tfp_budget_changes WHERE org_budget_flag = 1 AND status = 1 GROUP BY fk_tenant_id, budget_year) b
--WHERE a.tenant_id_mother = b.fk_tenant_id
--AND a.budget_year = b.budget_year

--END

BEGIN PRINT 'Update tfp_inv_header if action_id dont exist'

INSERT INTO @inv_header (fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by)
SELECT DISTINCT tenant_id_mother, 'Investeringer', budget_year, NEWID(), 1, 1, 0, GETDATE(), 1002
FROM @tenant_table a
WHERE NOT EXISTS 
(SELECT * FROM tfp_inv_header b WHERE b.fk_tenant_id = a.tenant_id_mother
AND b.budget_year = a.budget_year
AND isManuallyAdded = 0 
AND line_order = 1
AND action_type = 1)


INSERT INTO @inv_header (fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by)
SELECT DISTINCT tenant_id_mother, rl.line_group, budget_year, NEWID(), 2, rl.line_group_id, 0, GETDATE(), 1002
FROM @tenant_table a, (SELECT DISTINCT line_group_id, line_group FROM gmd_reporting_line WHERE report = 'financing') rl
WHERE NOT EXISTS 
(SELECT * FROM tfp_inv_header b WHERE b.fk_tenant_id = a.tenant_id_mother
AND b.budget_year = a.budget_year
AND isManuallyAdded = 0 
AND line_order = rl.line_group_id
AND action_type = 2)


SET @last_header_actionid = (SELECT max(pk_inv_action_id) FROM tfp_inv_header)

INSERT INTO tfp_inv_header (fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by,pk_inv_action_id)
SELECT fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by, pk_id + @last_header_actionid
FROM @inv_header

END

BEGIN 

PRINT 'Insert into investment table 1'

INSERT INTO @investments (pk_investment_id,pk_investment_id_new,fk_tenant_id,fk_tenant_id_new,status,self_cost_flag,budget_year_established,start_year,completion_pct ,approval,approval_date,responsible,fk_org_id,org_name,fk_project_id ,project_phase,updated,updated_by,fk_fdv_codes,unit_value,previously_budgeted,previously_financing,monthrep_flag,fk_main_project_code,fk_portfolio_code,tags,goalId,prev_actual,update_flag)
SELECT DISTINCT a.pk_investment_id,0,a.fk_tenant_id,c.tenant_id_mother,a.status,a.self_cost_flag,a.budget_year_established,a.start_year,a.completion_pct ,a.approval,a.approval_date,a.responsible,oh.org_id_2,oh.org_name_2,'' as fk_project_id ,a.project_phase,a.updated,a.updated_by,a.fk_fdv_codes,a.unit_value,a.previously_budgeted,a.previously_financing,a.monthrep_flag,a.fk_main_project_code,a.fk_portfolio_code,'' as tags,'00000000-0000-0000-0000-000000000000' as goalId,a.prev_actual, 'I'
FROM tco_investments a 
JOIN tco_investment_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_investment_id = b.fk_investment_id
JOIN @tenant_table c ON b.budget_year = c.budget_year AND b.fk_tenant_id = c.tenant_id_child
LEFT JOIN tmd_hq_departments_mapping dm ON b.fk_tenant_id = dm.tenant_id_source AND b.fk_department_code = dm.department_code_source
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and dm.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
WHERE NOT EXISTS (SELECT * FROM tfp_hq_investment_mapping d WHERE a.pk_investment_id = d.investment_id_source AND a.fk_tenant_id = d.tenant_id_source)

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

PRINT 'Insert into investment detail table 1'

INSERT INTO @investment_detail  (fk_inv_detail_id,fk_inv_detail_id_new,fk_investment_id,fk_investment_id_new,fk_tenant_id,fk_tenant_id_new,description,fk_account_code,fk_department_code,fk_function_code,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,type,updated,updated_by,existing_flag,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_year,start_year,fk_alter_code,fk_adjustment_code,fk_change_id,fk_program_code, update_flag, fk_inv_action_id, status)
SELECT b.pk_id, 0,b.fk_investment_id,0, b.fk_tenant_id, c.tenant_id_mother,b.description, ac.fk_kostra_account_code, dm.fk_department_code, fu.fk_kostra_function_code, b.vat_rate, b.vat_refund, b.year_1_amount, b.year_2_amount,b.year_3_amount,b.year_4_amount,b.year_5_amount,b.year_6_amount,b.year_7_amount,b.year_8_amount,b.year_9_amount,b.year_10_amount,b.type,b.updated,b.updated_by,b.existing_flag,'' as fk_project_code,'' as free_dim_1,'' as free_dim_2,'' as free_dim_3,'' as free_dim_4,b.budget_year,a.start_year,b.fk_alter_code,b.fk_adjustment_code,c.fk_change_id,c.fk_program_code,'I',ih.pk_inv_action_id, a.status
FROM tco_investments a 
JOIN tco_investment_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_investment_id = b.fk_investment_id
JOIN @tenant_table c ON b.budget_year = c.budget_year AND b.fk_tenant_id = c.tenant_id_child
LEFT JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
LEFT JOIN tco_functions fu ON fu.pk_tenant_id = b.fk_tenant_id AND fu.pk_Function_code = b.fk_function_code
LEFT JOIN tmd_hq_departments_mapping dm ON b.fk_tenant_id = dm.tenant_id_source AND b.fk_department_code = dm.department_code_source
LEFT JOIN tfp_inv_transactions it ON b.pk_id = it.fk_inv_details_id AND b.fk_investment_id = it.fk_investment_id AND b.fk_tenant_id = it.fk_tenant_id
LEFT JOIN tfp_inv_header ihd ON ihd.fk_tenant_id = b.fk_tenant_id AND ihd.pk_inv_action_id = it.fk_inv_action_id AND ihd.budget_year = it.budget_year
LEFT JOIN tfp_inv_header ih ON ih.fk_tenant_id = c.tenant_id_mother AND ih.budget_year = b.budget_year AND ih.isManuallyAdded = 0  AND ih.line_order = ihd.line_order AND ih.action_type = ihd.action_type AND ih.isManuallyAdded = ihd.isManuallyAdded
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and dm.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
WHERE NOT EXISTS (SELECT * FROM tfp_hq_investment_mapping d WHERE b.fk_investment_id = d.investment_id_source AND b.fk_tenant_id = d.tenant_id_source AND b.pk_id = d.detail_id_source)
AND ov.active = 1

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

PRINT 'Insert into investment table 2'

INSERT INTO @investments (pk_investment_id,pk_investment_id_new,fk_tenant_id,fk_tenant_id_new,status,self_cost_flag,budget_year_established,start_year,completion_pct ,approval,approval_date,responsible,fk_org_id,org_name,fk_project_id ,project_phase,updated,updated_by,fk_fdv_codes,unit_value,previously_budgeted,previously_financing,monthrep_flag,fk_main_project_code,fk_portfolio_code,tags,goalId,prev_actual,update_flag)
SELECT DISTINCT a.pk_investment_id,d.fk_investment_id,a.fk_tenant_id,c.tenant_id_mother,a.status,a.self_cost_flag,a.budget_year_established,a.start_year,a.completion_pct ,a.approval,a.approval_date,a.responsible,oh.org_id_2,oh.org_name_2,a.fk_project_id ,a.project_phase,a.updated,a.updated_by,a.fk_fdv_codes,a.unit_value,a.previously_budgeted,a.previously_financing,a.monthrep_flag,a.fk_main_project_code,a.fk_portfolio_code,'' as tags,'00000000-0000-0000-0000-000000000000' as goalId,a.prev_actual, 'U'
FROM tco_investments a
JOIN tco_investment_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_investment_id = b.fk_investment_id
JOIN @tenant_table c ON b.budget_year = c.budget_year AND b.fk_tenant_id = c.tenant_id_child
LEFT JOIN tmd_hq_departments_mapping dm ON b.fk_tenant_id = dm.tenant_id_source AND b.fk_department_code = dm.department_code_source
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and dm.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tfp_hq_investment_mapping d ON a.pk_investment_id = d.investment_id_source AND a.fk_tenant_id = d.tenant_id_source
WHERE oh.org_id_2 IS NOT NULL

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END


PRINT 'Insert into investment detail table 2'

INSERT INTO @investment_detail  (fk_inv_detail_id,fk_inv_detail_id_new,fk_investment_id,fk_investment_id_new,fk_tenant_id,fk_tenant_id_new,description,fk_account_code,fk_department_code,fk_function_code,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,type,updated,updated_by,existing_flag,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_year,start_year,fk_alter_code,fk_adjustment_code,fk_change_id,fk_program_code, update_flag,fk_inv_action_id, status)
SELECT b.pk_id, d.fk_inv_detail_id,b.fk_investment_id, d.fk_investment_id,b.fk_tenant_id, c.tenant_id_mother,b.description, ac.fk_kostra_account_code, dm.fk_department_code, fu.fk_kostra_function_code, b.vat_rate, b.vat_refund, b.year_1_amount, b.year_2_amount,b.year_3_amount,b.year_4_amount,b.year_5_amount,b.year_6_amount,b.year_7_amount,b.year_8_amount,b.year_9_amount,b.year_10_amount,b.type,b.updated,b.updated_by,b.existing_flag,b.fk_project_code,b.free_dim_1,b.free_dim_2,b.free_dim_3,b.free_dim_4,b.budget_year,a.start_year,b.fk_alter_code,b.fk_adjustment_code,c.fk_change_id,c.fk_program_code,'U', ih.pk_inv_action_id, a.status
FROM tco_investments a
JOIN tco_investment_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_investment_id = b.fk_investment_id
JOIN @tenant_table c ON b.budget_year = c.budget_year AND b.fk_tenant_id = c.tenant_id_child
LEFT JOIN tco_accounts ac ON ac.pk_tenant_id = b.fk_tenant_id AND ac.pk_account_code = b.fk_account_code
LEFT JOIN tco_functions fu ON fu.pk_tenant_id = b.fk_tenant_id AND fu.pk_Function_code = b.fk_function_code
LEFT JOIN tmd_hq_departments_mapping dm ON b.fk_tenant_id = dm.tenant_id_source AND b.fk_department_code = dm.department_code_source
LEFT JOIN tfp_inv_header ih ON ih.fk_tenant_id = c.tenant_id_mother AND ih.budget_year = b.budget_year AND isManuallyAdded = 0  AND line_order = 1 AND action_type = 1
JOIN tfp_hq_investment_mapping d ON a.pk_investment_id = d.investment_id_source AND a.fk_tenant_id = d.tenant_id_source AND b.pk_id = d.detail_id_source


IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END


BEGIN PRINT 'Fetch org id for header information'

INSERT INTO @header_level_table (fk_tenant_id, fk_investment_id,org_id, org_name)
SELECT a.fk_tenant_id_new, a.fk_investment_id,
org_id = CASE 
	WHEN p1.param_value = 'org_id_1' THEN ISNULL(oh.org_id_1,'')
	WHEN p1.param_value = 'org_id_2' THEN ISNULL(oh.org_id_2,'')
	WHEN p1.param_value = 'org_id_3' THEN ISNULL(oh.org_id_3,'')
	WHEN p1.param_value = 'org_id_4' THEN ISNULL(oh.org_id_4,'')
	WHEN p1.param_value = 'org_id_5' THEN ISNULL(oh.org_id_5,'')
	WHEN p1.param_value = 'service_id_1' THEN ISNULL(sv.service_id_1,'')
	WHEN p1.param_value = 'service_id_2' THEN ISNULL(sv.service_id_2,'')
	WHEN p1.param_value = 'service_id_3' THEN ISNULL(sv.service_id_3,'')
	WHEN p1.param_value = 'service_id_4' THEN ISNULL(sv.service_id_4,'')
	WHEN p1.param_value = 'service_id_5' THEN ISNULL(sv.service_id_5,'')
	ELSE '' END, 
	org_name = CASE 
	WHEN p1.param_value = 'org_id_1' THEN ISNULL(oh.org_name_1,'')
	WHEN p1.param_value = 'org_id_2' THEN ISNULL(oh.org_name_2,'')
	WHEN p1.param_value = 'org_id_3' THEN ISNULL(oh.org_name_3,'')
	WHEN p1.param_value = 'org_id_4' THEN ISNULL(oh.org_name_4,'')
	WHEN p1.param_value = 'org_id_5' THEN ISNULL(oh.org_name_5,'')
	WHEN p1.param_value = 'service_id_1' THEN ISNULL(sv.service_name_1,'')
	WHEN p1.param_value = 'service_id_2' THEN ISNULL(sv.service_name_2,'')
	WHEN p1.param_value = 'service_id_3' THEN ISNULL(sv.service_name_3,'')
	WHEN p1.param_value = 'service_id_4' THEN ISNULL(sv.service_name_4,'')
	WHEN p1.param_value = 'service_id_5' THEN ISNULL(sv.service_name_5,'')
	ELSE '' END
FROM @investment_detail a
LEFT JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.fk_department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT JOIN tco_service_values sv ON sv.fk_function_code = a.fk_function_code AND sv.fk_tenant_id = a.fk_tenant_id_new
LEFT JOIN tco_parameters p1 ON p1.fk_tenant_id = a.fk_tenant_id_new AND p1.active = 1 AND p1.param_name = 'FINPLAN_LEVEL_1'


SELECT fk_tenant_id, fk_investment_id INTO #TEMP_UNIQUE_INV
FROM @header_level_table
GROUP BY fk_tenant_id, fk_investment_id
HAVING COUNT(*) > 1

DELETE FROM @header_level_table WHERE pk_id IN (
SELECT MAX(a.pk_id)
FROM 
#TEMP_UNIQUE_INV S, @header_level_table a
WHERE a.fk_tenant_id = S.fk_tenant_id
AND a.fk_investment_id = S.fk_investment_id
GROUP BY a.fk_tenant_id, a.fk_investment_id)

UPDATE @investments SET fk_org_id = b.org_id, org_name = b.org_name 
FROM @investments a, @header_level_table b
WHERE a.fk_tenant_id_new = b.fk_tenant_id
AND a.pk_investment_id = b.fk_investment_id

END


BEGIN TRANSACTION

SET NOCOUNT OFF

PRINT 'START inserting new transactions'

SET @last_investment_id = (SELECT max(pk_investment_id) FROM tco_investments)

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

SET IDENTITY_INSERT tco_investments ON 

INSERT INTO tco_investments (pk_investment_id,fk_tenant_id,status,self_cost_flag,budget_year_established,start_year,completion_pct ,approval,approval_date,responsible,fk_project_id ,project_phase,updated,updated_by,fk_fdv_codes,unit_value,previously_budgeted,previously_financing,monthrep_flag,fk_main_project_code,fk_portfolio_code,tags,goalId,prev_actual)
SELECT pk_id + @last_investment_id,fk_tenant_id_new,status,self_cost_flag,budget_year_established,start_year,completion_pct ,approval,approval_date,responsible,fk_project_id ,project_phase,updated,updated_by,fk_fdv_codes,unit_value,previously_budgeted,previously_financing,monthrep_flag,fk_main_project_code,fk_portfolio_code,tags,goalId,prev_actual
FROM @investments
WHERE update_flag = 'I'

SET IDENTITY_INSERT tco_investments OFF 

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END
   

UPDATE @investments SET pk_investment_id_new = pk_id +  @last_investment_id WHERE update_flag = 'I'

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END


PRINT 'insert   tco_inv_budgetyear_config'

INSERT INTO tco_inv_budgetyear_config (fk_tenant_id, fk_investment_id, budget_year, inv_status, priority, updated, updated_by, investment_name, completion_date, fk_org_id, org_name)
SELECT DISTINCT a.fk_tenant_id_new, b.pk_investment_id_new, a.budget_year,c.inv_status, c.priority, c.updated, c.updated_by, investment_name, completion_date, b.fk_org_id, b.org_name
FROM @investment_detail a, @investments b, tco_inv_budgetyear_config c
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.fk_investment_id = b.pk_investment_id
AND a.fk_tenant_id = c.fk_tenant_id
AND a.fk_investment_id = c.fk_investment_id
AND a.budget_year = c.budget_year
AND a.update_flag = 'I'
AND NOT EXISTS (SELECT * FROM tco_inv_budgetyear_config cf WHERE cf.fk_tenant_id = a.fk_tenant_id_new 
AND cf.fk_investment_id = b.pk_investment_id_new
AND a.budget_year = cf.budget_year)


IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

PRINT 'update  @investment_detail'

UPDATE @investment_detail SET fk_investment_id_new = b.pk_investment_id_new
FROM @investment_detail a, @investments b
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.fk_investment_id = b.pk_investment_id
AND a.update_flag = 'I'

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

SET @last_inv_details_id = (SELECT MAX(pk_id) FROM tco_investment_detail)


PRINT 'insert   tco_investment_detail'

SET IDENTITY_INSERT tco_investment_detail ON 

INSERT INTO tco_investment_detail (pk_id,fk_investment_id,fk_tenant_id,description,fk_account_code,fk_department_code,fk_function_code,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,type,updated,updated_by,existing_flag,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_year,fk_alter_code,fk_adjustment_code,fk_change_id,fk_program_code)
SELECT pk_id + @last_inv_details_id,fk_investment_id_new,fk_tenant_id_new,description,fk_account_code,fk_department_code,fk_function_code,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,type,updated,updated_by,existing_flag,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_year,fk_alter_code,fk_adjustment_code,fk_change_id,fk_program_code
FROM @investment_detail WHERE update_flag = 'I'

SET IDENTITY_INSERT tco_investment_detail OFF 

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

UPDATE @investment_detail SET fk_inv_detail_id_new = pk_id + @last_inv_details_id WHERE update_flag = 'I'

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

INSERT INTO tfp_hq_investment_mapping (fk_tenant_id, fk_investment_id, fk_inv_detail_id, tenant_id_source, investment_id_source, detail_id_source, updated, updated_by)
SELECT DISTINCT fk_tenant_id_new, fk_investment_id_new, fk_inv_detail_id_new, fk_tenant_id, fk_investment_id, fk_inv_detail_id, GETDATE(), updated_by
FROM @investment_detail  WHERE update_flag = 'I'

COMMIT

PRINT 'Start updating exsisting investments'

BEGIN TRANSACTION

UPDATE tco_investments SET status = b.status, responsible = b.responsible, updated = b.updated, updated_by = b.updated_by
FROM tco_investments a, @investments b
WHERE a.fk_tenant_id = b.fk_tenant_id_new
AND a.pk_investment_id = b.pk_investment_id_new
AND b.update_flag = 'U'
AND DATEDIFF(DAY, b.updated, GETDATE()) <= 1

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

UPDATE tco_investment_detail SET year_1_amount = b.year_1_amount, year_2_amount = b.year_2_amount, year_3_amount = b.year_3_amount, year_4_amount = b.year_4_amount, year_5_amount = b.year_5_amount, year_6_amount = b.year_6_amount, year_7_amount = b.year_7_amount, year_8_amount = b.year_8_amount, year_9_amount = b.year_9_amount, year_10_amount = b.year_10_amount,
fk_account_code = b.fk_account_code, fk_department_code = b.fk_department_code, fk_function_code = b.fk_function_code,updated = b.updated, updated_by = b.updated_by
FROM tco_investment_detail a, @investment_detail b
WHERE a.fk_tenant_id = b.fk_tenant_id_new
AND a.fk_investment_id = b.fk_investment_id_new
AND a.pk_id = b.fk_inv_detail_id_new
AND b.update_flag = 'U'
AND DATEDIFF(DAY, b.updated, GETDATE()) <= 1

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

SET NOCOUNT ON


INSERT INTO @investment_finplan_trans (fk_inv_action_id, fk_investment_id, fk_tenant_id, budget_year, fk_account_code, fk_function_code , department_code , vat_rate, vat_refund, year_1_amount, year_2_amount, year_3_amount, year_4_amount, updated, type, updated_by , fk_project_code, free_dim_1, fk_change_id, free_dim_2, free_dim_3, free_dim_4, fk_inv_details_id, fk_alter_code, fk_adjustment_code, fk_prog_code, update_flag)
SELECT fk_inv_action_id, fk_investment_id_new, fk_tenant_id_new, budget_year, fk_account_code, fk_function_code , fk_department_code , vat_rate, vat_refund, 
year_1_amount = CASE
		WHEN budget_year-start_year = 0 THEN year_1_amount
		WHEN budget_year-start_year = 1 THEN year_2_amount
		WHEN budget_year-start_year = 2 THEN year_3_amount
		WHEN budget_year-start_year = 3 THEN year_4_amount
		WHEN budget_year-start_year = 4 THEN year_5_amount
		WHEN budget_year-start_year = 5 THEN year_6_amount
		WHEN budget_year-start_year = 6 THEN year_7_amount
		WHEN budget_year-start_year = 7 THEN year_8_amount
		WHEN budget_year-start_year = 8 THEN year_9_amount
		WHEN budget_year-start_year = 9 THEN year_10_amount
		ELSE 0 END, 
year_2_amount = CASE
		WHEN budget_year-start_year = -1 THEN year_1_amount
		WHEN budget_year-start_year = 0 THEN year_2_amount
		WHEN budget_year-start_year = 1 THEN year_3_amount
		WHEN budget_year-start_year = 2 THEN year_4_amount
		WHEN budget_year-start_year = 3 THEN year_5_amount
		WHEN budget_year-start_year = 4 THEN year_6_amount
		WHEN budget_year-start_year = 5 THEN year_7_amount
		WHEN budget_year-start_year = 6 THEN year_8_amount
		WHEN budget_year-start_year = 7 THEN year_9_amount
		WHEN budget_year-start_year = 8 THEN year_10_amount
		ELSE 0 END, 
year_3_amount = CASE
		WHEN budget_year-start_year = -2 THEN year_1_amount
		WHEN budget_year-start_year = -1 THEN year_2_amount
		WHEN budget_year-start_year = 0 THEN year_3_amount
		WHEN budget_year-start_year = 1 THEN year_4_amount
		WHEN budget_year-start_year = 2 THEN year_5_amount
		WHEN budget_year-start_year = 3 THEN year_6_amount
		WHEN budget_year-start_year = 4 THEN year_7_amount
		WHEN budget_year-start_year = 5 THEN year_8_amount
		WHEN budget_year-start_year = 6 THEN year_9_amount
		WHEN budget_year-start_year = 7 THEN year_10_amount
		ELSE 0 END, 
year_4_amount = CASE
		WHEN budget_year-start_year = -3 THEN year_1_amount
		WHEN budget_year-start_year = -2 THEN year_2_amount
		WHEN budget_year-start_year = -1 THEN year_3_amount
		WHEN budget_year-start_year = 0 THEN year_4_amount
		WHEN budget_year-start_year = 1 THEN year_5_amount
		WHEN budget_year-start_year = 2 THEN year_6_amount
		WHEN budget_year-start_year = 3 THEN year_7_amount
		WHEN budget_year-start_year = 4 THEN year_8_amount
		WHEN budget_year-start_year = 5 THEN year_9_amount
		WHEN budget_year-start_year = 6 THEN year_10_amount
		ELSE 0 END,
updated, type, updated_by , fk_project_code, free_dim_1, fk_change_id, free_dim_2, free_dim_3, free_dim_4, fk_inv_detail_id_new, fk_alter_code, fk_adjustment_code, fk_program_code, update_flag
FROM @investment_detail WHERE type IN ('i','f') AND fk_inv_action_id is not null
ORDER BY update_flag, fk_investment_id

IF @@ERROR <> 0
   BEGIN
			ROLLBACK
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

SET @last_pk_id = (SELECT MAX(pk_id) FROM tfp_inv_transactions)

SET NOCOUNT OFF

PRINT 'START updating inv finplan transactions'

INSERT INTO tfp_inv_transactions (pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type, updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code)
SELECT pk_id+@last_pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code
FROM @investment_finplan_trans
WHERE update_flag = 'I'

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

UPDATE tfp_inv_transactions SET fk_account_code = b.fk_account_code, fk_adjustment_code = b.fk_adjustment_code, fk_alter_code = b.fk_alter_code, fk_function_code = b.fk_function_code, fk_prog_code = b.fk_prog_code, fk_project_code = b.fk_project_code,
year_1_amount = b.year_1_amount, year_2_amount = b.year_2_amount, year_3_amount = b.year_3_amount, year_4_amount = b.year_4_amount
FROM tfp_inv_transactions a, @investment_finplan_trans b
WHERE b.update_flag = 'U'
AND a.fk_inv_details_id = b.fk_inv_details_id
AND a.fk_investment_id = b.fk_investment_id
AND a.fk_tenant_id = b.fk_tenant_id
AND DATEDIFF(DAY, b.updated, GETDATE()) <= 1

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

COMMIT


BEGIN TRANSACTION

IF @tenant_id = 0 
BEGIN
PRINT 'Find transactions to be deleted'

INSERT INTO @investment_removed (fk_tenant_id, pk_investment_id) 
SELECT DISTINCT fk_tenant_id, pk_investment_id
FROM tco_investments a, @tenant_table b
WHERE a.fk_tenant_id = b.tenant_id_mother
AND NOT EXISTS (SELECT * FROM @investments d WHERE d.fk_tenant_id_new = a.fk_tenant_id AND a.pk_investment_id = d.pk_investment_id_new)
END


IF @tenant_id != 0 
BEGIN
PRINT 'Find transactions to be deleted tenantid !=0'

INSERT INTO @investment_removed (fk_tenant_id, pk_investment_id) 
SELECT DISTINCT a.fk_tenant_id, pk_investment_id
FROM tco_investments a, @tenant_table b, tfp_hq_investment_mapping m
WHERE a.fk_tenant_id = b.tenant_id_mother
AND a.fk_tenant_id = m.fk_tenant_id
AND a.pk_investment_id = m.fk_investment_id
AND m.tenant_id_source =  @tenant_id
AND NOT EXISTS (SELECT * FROM @investments d WHERE d.fk_tenant_id_new = a.fk_tenant_id AND a.pk_investment_id = d.pk_investment_id_new)


END

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

IF @tenant_id = 0 
BEGIN

INSERT INTO @inv_detail_removed (fk_tenant_id, pk_investment_id, fk_inv_action_id,budget_year)
SELECT DISTINCT a.fk_tenant_id, a.fk_investment_id, a.pk_id,a.budget_year
FROM tco_investment_detail a, @tenant_table b
WHERE a.budget_year = b.budget_year
AND a.fk_tenant_id = b.tenant_id_mother
AND NOT EXISTS (SELECT * FROM @investment_detail d WHERE d.fk_tenant_id_new = a.fk_tenant_id AND a.fk_investment_id = d.fk_investment_id_new AND a.pk_id = d.fk_inv_detail_id_new)

END


IF @tenant_id != 0 
BEGIN

INSERT INTO @inv_detail_removed (fk_tenant_id, pk_investment_id, fk_inv_action_id,budget_year)
SELECT DISTINCT a.fk_tenant_id, a.fk_investment_id, a.pk_id,a.budget_year
FROM tco_investment_detail a, @tenant_table b, tfp_hq_investment_mapping m
WHERE a.budget_year = b.budget_year
AND a.fk_tenant_id = b.tenant_id_mother
AND a.fk_tenant_id = m.fk_tenant_id
AND a.fk_investment_id = m.fk_investment_id
AND a.pk_id = m.fk_inv_detail_id
AND m.tenant_id_source =  @tenant_id
AND NOT EXISTS (SELECT * FROM @investment_detail d WHERE d.fk_tenant_id_new = a.fk_tenant_id AND a.fk_investment_id = d.fk_investment_id_new AND a.pk_id = d.fk_inv_detail_id_new)

END


IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

DELETE T FROM tco_investment_detail T INNER JOIN @investment_removed A ON T.fk_investment_id = A.pk_investment_id AND T.fk_tenant_id = A.fk_tenant_id;

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END


DELETE T FROM tfp_inv_transactions T INNER JOIN @investment_removed A ON T.fk_investment_id = A.pk_investment_id AND T.fk_tenant_id = A.fk_tenant_id;

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

DELETE T FROM tco_investment_detail T INNER JOIN @inv_detail_removed A ON T.fk_investment_id = A.pk_investment_id AND T.fk_tenant_id = A.fk_tenant_id AND A.fk_inv_action_id = T.pk_id AND A.budget_year = T.budget_year

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

DELETE T FROM tfp_inv_transactions T INNER JOIN @inv_detail_removed A ON T.fk_investment_id = A.pk_investment_id AND T.fk_tenant_id = A.fk_tenant_id AND A.fk_inv_action_id = T.fk_inv_details_id AND A.budget_year = T.budget_year

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

DELETE T FROM tfp_inv_transactions T INNER JOIN @investment_detail A ON T.fk_investment_id = A.fk_investment_id_new AND T.fk_tenant_id = A.fk_tenant_id_new AND A.fk_inv_detail_id_new = T.fk_inv_details_id AND A.budget_year = T.budget_year AND A.status = 0

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

DELETE T FROM tco_investments T INNER JOIN @investment_removed A ON T.pk_investment_id = A.pk_investment_id AND T.fk_tenant_id = A.fk_tenant_id

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

DELETE T FROM tfp_hq_investment_mapping T INNER JOIN @inv_detail_removed A ON T.fk_tenant_id = A.fk_tenant_id AND T.fk_investment_id = A.pk_investment_id AND T.fk_inv_detail_id = A.fk_inv_action_id

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

COMMIT

exec [prcSyncHQFinancing]

BEGIN

			UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name = 'HQINVUPDATE';
			select @timestamp = sysdatetime();
			SELECT @EndDate = sysdatetime();
			SELECT @TimeUsed = (select convert(varchar(5),DateDiff(s, @startDate, @EndDate)/3600)+' hrs '+convert(varchar(5),DateDiff(s, @startDate, @EndDate)%3600/60)+' mins '+convert(varchar(5),(DateDiff(s, @startDate, @EndDate)%60))+' seconds')
			PRINT 'Job finished ok at ' + convert(nvarchar(19),@EndDate) + ' using ' + @TimeUsed
END

RETURN 0
GO


