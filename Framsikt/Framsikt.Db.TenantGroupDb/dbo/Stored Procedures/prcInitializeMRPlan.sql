CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRPlan]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS
	
	DECLARE @budget_year INT
	DECLARE @prev_forecast_period INT
	DECLARE @org_version VARCHAR(25) 
	DECLARE @language VARCHAR(10)
	DECLARE @prev_plan_period INT


	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
	SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
	SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
	SET @budget_year = @forecast_period/100


	SET @prev_plan_period = (
	SELECT MAX(forecast_period) FROM tmr_planreport_setup
	WHERE fk_Tenant_id = @fk_tenant_id
	AND forecast_period < @forecast_period
	AND forecast_period > @budget_year * 100
	)

	IF @prev_plan_period IS NULL
	begin
	Return
	end


PRINT 'START: Fetch Plan data'



IF (select count(*) from tmr_planreport_setup WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period) = 0
BEGIN


INSERT INTO tmr_planreport_setup (fk_plan_id, fk_tenant_id, forecast_period, is_reported, updated_by, updated)
select fk_plan_id, fk_tenant_id, @forecast_period AS forecast_period, is_reported, updated_by, updated from tmr_planreport_setup
WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @prev_plan_period

END



PRINT 'END: Fetch Plan data'

RETURN 0
