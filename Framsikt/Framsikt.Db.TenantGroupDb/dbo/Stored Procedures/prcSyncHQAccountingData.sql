CREATE OR ALTER PROCEDURE [dbo].[prcSyncHQAccountingData]
	@tenant_id int,
	@gl_year int,
	@user_id INT

AS

DECLARE @hq_tenant_definition AS TABLE (
    [fk_tenant_id] INT NOT NULL, 
    [tenant_id_child] INT NOT NULL, 
	[short_name_child] NVARCHAR(4) NOT NULL DEFAULT '')

INSERT INTO @hq_tenant_definition (fk_tenant_id,tenant_id_child, short_name_child)
SELECT DISTINCT fk_tenant_id, tenant_id_child, short_name_child
FROM tmd_hq_tenant_definition

DECLARE @period INT = CONVERT(char(4),@gl_year)+'01'

DELETE FROM tfp_accounting_data WHERE fk_tenant_id = @tenant_id AND gl_year = @gl_year
AND fk_tenant_id IN (SELECT fk_tenant_id FROM tmd_hq_tenant_definition);


INSERT INTO tfp_accounting_data (fk_tenant_id,fk_account_code,department_code,fk_function_code,fk_project_code,asset_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,description,gl_year,period,amount,updated,updated_by,fk_prog_code)
SELECT hq.fk_tenant_id,ISNULL(ac.fk_kostra_account_code,''),ISNULL(dm.fk_department_code,''),ISNULL(f.fk_kostra_function_code,''),fk_project_code,asset_code,'' free_dim_1,'' free_dim_2,'' free_dim_3,'' free_dim_4,'' description,
gl_year,@period,sum(amount),getdate(),@user_id,convert(int,t.municipality_id) as fk_prog_code
FROM tfp_accounting_data a
JOIN @hq_tenant_definition hq ON a.fk_tenant_id = hq.tenant_id_child 
JOIN gco_tenants t ON a.fk_tenant_id = t.pk_id
LEFT JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code
LEFT JOIN tmd_hq_departments_mapping dm ON a.fk_tenant_id = dm.tenant_id_source AND a.department_code = dm.department_code_source
LEFT JOIN tco_functions f ON a.fk_tenant_id = f.pk_tenant_id AND a.fk_function_code = f.pk_Function_code
WHERE hq.fk_tenant_id = @tenant_id
AND a.gl_year = @gl_year
GROUP BY  hq.fk_tenant_id,ac.fk_kostra_account_code,dm.fk_department_code,f.fk_kostra_function_code,fk_project_code,asset_code,
gl_year,t.municipality_id
;
RETURN 0
