--EXEC  [procWhatIsWrongWithMySetup] @tenant_id = 3084
CREATE OR ALTER PROCEDURE [dbo].[procWhatIsWrongWithMySetup] @tenant_id INT = 0

       
AS

DECLARE @current_year INT = DATEPART(YEAR, GETDATE())
DECLARE @prev_year INT = @current_year - 1


DECLARE @changed_table TABLE(
    fk_tenant_id INT  NOT NULL
);


DECLARE @error_table TABLE(
    fk_tenant_id INT  NOT NULL,
	error_type NVARCHAR(50),
	error_value NVARCHAR(50),
	error_description NVARCHAR (500)
);



IF  @tenant_id = 0
BEGIN 
	INSERT INTO @changed_table (fk_tenant_id)
	SELECT distinct fk_tenant_id FROM [dbo].[tco_module_mapping] WHERE fk_module_id in (3,4,5);
END


IF  @tenant_id != 0
BEGIN 
	INSERT INTO @changed_table (fk_tenant_id)
	VALUES (@tenant_id)
END


Print 'START Validate department setup';

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)

SELECT a.fk_tenant_id, 'DEPARTMENT_MISSING', department_code, 'Dept used in tfp_trans_detail but not in tco_departments' 
FROM tfp_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DEPARTMENT_MISSING', department_code, 'Dept used in tbu_trans_detail but not in tco_departments' 
FROM tbu_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DEPARTMENT_MISSING', department_code, 'Dept used in tfp_accounting_data but not in tco_departments' 
FROM tfp_accounting_data a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DEPARTMENT_MISSING', fk_department_code, 'Dept used in tco_investment_detail but not in tco_departments' 
FROM tco_investment_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DEPARTMENT_MISSING', fk_department_code, 'Dept used in tco_org_hierarchy but not in tco_departments' 
FROM tco_org_hierarchy a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DEPARTMENT_MISSING', pk_department_code, 'Dept used in tco_departments but not in tco_org_hierarchy' 
FROM tco_departments a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.pk_department_code NOT IN 
(SELECT fk_department_code FROM  tco_org_hierarchy  m where a.fk_tenant_id = m.fk_tenant_id
);

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DUPLICATE DEPT', a.pk_department_code, 'Dept might be duplicate in tco_departments'
FROM tco_departments a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
GROUP BY a.fk_tenant_id, a.pk_department_code
HAVING COUNT(*) > 1;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DUPLICATE DEPT', a.fk_department_code, 'Dept is duplicate in tco_org_hierarchy'
FROM tco_org_hierarchy a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
GROUP BY a.fk_tenant_id, a.fk_department_code, a.fk_org_version
HAVING COUNT(*) > 1


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT distinct dp.fk_tenant_id, 'MISSING DEPT IN HQ SETUP', dp.pk_department_code,'Dept missing in tmd_hq_departments_mapping'
FROM tco_departments dp
JOIN tmd_hq_tenant_definition td ON dp.fk_tenant_id = td.tenant_id_child
JOIN @changed_table b ON dp.fk_tenant_id = b.fk_tenant_id
WHERE NOT EXISTS (
SELECT * from tmd_hq_departments_mapping hq
WHERE dp.fk_tenant_id = hq.tenant_id_source
AND dp.pk_department_code = hq.department_code_source)


Print 'END Validate department setup';

PRINT 'START Validate project setup'

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DUPLICATE PROJECTS', a.pk_project_code,'Project is overlapping for budget year ' + convert(char(4),  DATEPART(YEAR,getdate()))
FROM tco_projects a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND DATEPART(YEAR,getdate()) BETWEEN DATEPART(YEAR, date_from) AND DATEPART(YEAR, date_to)
GROUP BY a.fk_tenant_id, a.pk_project_code
HAVING COUNT(*) > 1

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DUPLICATE PROJECTS', a.pk_project_code,'Project is overlapping for budget year ' + convert(char(4),  DATEPART(YEAR,getdate())+1)
FROM tco_projects a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND DATEPART(YEAR,getdate())+1 BETWEEN DATEPART(YEAR, date_from) AND DATEPART(YEAR, date_to)
GROUP BY a.fk_tenant_id, a.pk_project_code
HAVING COUNT(*) > 1


PRINT 'END Validate project setup'



Print 'START Validate function setup';

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'FUNCTION_MISSING', function_code, 'Function used in tfp_trans_detail but not in tco_functions' 
FROM tfp_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.function_code NOT IN 
(SELECT pk_function_code FROM  tco_functions m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'FUNCTION_MISSING', fk_function_code, 'Function used in tbu_trans_detail but not in tco_functions' 
FROM tbu_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_function_code NOT IN 
(SELECT pk_function_code FROM  tco_functions m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'FUNCTION_MISSING', fk_function_code, 'Function used in tfp_accounting_data but not in tco_functions' 
FROM tfp_accounting_data a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_function_code NOT IN 
(SELECT pk_function_code FROM  tco_functions m where a.fk_tenant_id = m.pk_tenant_id
) ;


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'FUNCTION_MISSING', fk_function_code, 'Function used in tco_investment_detail but not in tco_functions' 
FROM tco_investment_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_function_code NOT IN 
(SELECT pk_function_code FROM  tco_functions m where a.fk_tenant_id = m.pk_tenant_id
) ;


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'FUNCTION_MISSING', fk_function_code, 'Function used in tco_service_values but not in tco_functions' 
FROM tco_service_values a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_function_code NOT IN 
(SELECT pk_function_code FROM  tco_functions m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.pk_tenant_id, 'FUNCTION_MISSING', pk_function_code, 'function used in tco_functions but not in tco_service_values' 
FROM tco_functions a, @changed_table b WHERE 1=1
AND a.pk_tenant_id = b.fk_tenant_id
AND a.pk_function_code NOT IN 
(SELECT fk_function_code FROM  tco_service_values  m where a.pk_tenant_id = m.fk_tenant_id
) AND b.fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM tco_service_values)
;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.pk_tenant_id, 'DUPLICATE_FUNCTION', a.pk_function_code, 'Function might be duplicate in tco_functions'
FROM tco_functions a, @changed_table b WHERE 1=1
AND a.pk_tenant_id = b.fk_tenant_id
GROUP BY a.pk_tenant_id, a.pk_function_code
HAVING COUNT(*) > 1;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'DUPLICATE_FUNCTION', a.fk_function_code, 'function is duplicate in tco_service_values'
FROM tco_service_values a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
GROUP BY a.fk_tenant_id, a.fk_function_code
HAVING COUNT(*) > 1;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.pk_tenant_id, 'KOSTRA_FUNCTION_MISSING', fk_kostra_function_code, 'Kostra function does not exist in gmd_kostra_function' 
FROM tco_functions a, @changed_table b WHERE 1=1
AND a.pk_tenant_id = b.fk_tenant_id
AND a.fk_kostra_function_code NOT IN 
(SELECT pk_kostra_function_code FROM  gmd_kostra_function);

Print 'END Validate function setup';

Print 'START Validate account setup';

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'ACCOUNT_MISSING', a.fk_account_code, 'Account used in tfp_trans_detail but not in tco_accounts' 
FROM tfp_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_account_code NOT IN 
(SELECT pk_account_code FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'ACCOUNT_MISSING', a.fk_account_code, 'Account used in tbu_trans_detail but not in tco_accounts' 
FROM tbu_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_account_code NOT IN 
(SELECT pk_account_code FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'ACCOUNT_MISSING', a.fk_account_code, 'Account used in tfp_accounting_data but not in tco_accounts' 
FROM tfp_accounting_data a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_account_code NOT IN 
(SELECT pk_account_code FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'ACCOUNT_MISSING', a.fk_account_code, 'Account used in tco_investment_detail but not in tco_accounts' 
FROM tco_investment_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_account_code NOT IN 
(SELECT pk_account_code FROM  tco_accounts m where a.fk_tenant_id = m.pk_tenant_id
) ;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.pk_tenant_id, 'DUPLICATE_ACCOUNT', a.pk_account_code, 'Account might be duplicate in tco_accounts'
FROM tco_accounts a, @changed_table b WHERE 1=1
AND a.pk_tenant_id = b.fk_tenant_id
GROUP BY a.pk_tenant_id, a.pk_account_code
HAVING COUNT(*) > 1;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.pk_tenant_id, 'KOSTRA_ACCOUNT_MISSING', a.fk_kostra_account_code, 'Kostra account code in tco_accounts does not exist in gco_kostra_accounts'
FROM tco_accounts a, @changed_table b WHERE 1=1
AND a.pk_tenant_id = b.fk_tenant_id
AND a.fk_kostra_account_code NOT IN (SELECT pk_kostra_account_code FROM gco_kostra_accounts);

Print 'END Validate account setup';

Print 'START Find missing relations in finplan'

--INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
--SELECT a.fk_tenant_id, 'FINPLAN_TRANS_INCONSISTENCY', a.pk_action_id, 'Action in tfp_trans_header but missing in detail'
--FROM tfp_trans_header a, @changed_table b WHERE 1=1
--AND a.fk_tenant_id = b.fk_tenant_id
--AND NOT EXISTS (SELECT * FROM tfp_trans_detail d
--WHERE a.pk_action_id = d.fk_action_id AND a.fk_tenant_id = d.fk_tenant_id
--)


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'FINPLAN_TRANS_INCONSISTENCY', a.fk_action_id, 'Action in tfp_trans_detail but missing in header'
FROM tfp_trans_detail a, @changed_table b WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS (SELECT * FROM tfp_trans_header d
WHERE a.fk_action_id = d.pk_action_id AND a.fk_tenant_id = d.fk_tenant_id
)


Print 'END Find missing relations in finplan'

Print 'START Check alter code setup' 

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT a.fk_tenant_id, 'ALTER_CODE_SETUP_ERROR', a.fk_alter_code, 'alter code in tfp_trans_detail but missing in tco_fp_alter_codes'
FROM tfp_trans_detail a, @changed_table b, tfp_trans_header h
WHERE 1=1
AND a.fk_tenant_id = b.fk_tenant_id
AND a.fk_tenant_id = h.fk_tenant_id
AND a.fk_action_id = h.pk_action_id
AND h.action_type != 40
AND NOT EXISTS (SELECT * FROM tco_fp_alter_codes d
WHERE a.fk_alter_code = d.pk_alter_code AND a.fk_tenant_id = d.fk_tenant_id
)

Print 'END Check alter code setup' 

PRINT 'START Check on default accounting setup'

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)

SELECT  S.fk_tenant_id, 'MISSING_DEFAULT_SETUP', 
'YEAR ' +CONVERT(CHAR(4), S.budget_year) + ',level1value ' +  S.fp_level_1_value + ',level2value ' + S.fp_level_2_value,
'Data missing in tmd_fp_level_defaults'
FROM
(SELECT a.fk_tenant_id, b.budget_year, 
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount,
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value =
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND S.fp_level_2_value  IS NOT NULL
AND S.fp_level_1_value  IS NOT NULL
AND NOT EXISTS
(SELECT * FROM [dbo].[tmd_fp_level_defaults] T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND S.fp_level_1_value = T.fp_level_1_value
AND S.fp_level_2_value = T.fp_level_2_value)
AND S.fk_tenant_id = b.fk_tenant_id
GROUP BY
S.fk_tenant_id, S.budget_year, S.fp_level_1_value,S.fp_level_2_value;

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT  S.fk_tenant_id, 'MISSING_DEFAULT_SETUP_LEVEL_1', 
'YEAR ' +CONVERT(CHAR(4), S.budget_year) + ',level1value ' +  S.fp_level_1_value,
'Data missing in tmd_fp_level_defaults'
FROM
(SELECT a.fk_tenant_id, b.budget_year, 
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount,
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = ''
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS
(SELECT * FROM [dbo].[tmd_fp_level_defaults] T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND S.fp_level_1_value = T.fp_level_1_value
AND S.fp_level_2_value = T.fp_level_2_value)
AND S.fk_tenant_id = b.fk_tenant_id
GROUP BY
S.fk_tenant_id, S.budget_year, S.fp_level_1_value,S.fp_level_2_value;



INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)

SELECT df.fk_tenant_id, 'INVALID_DEFAULT_SETUP_DEPARTMENTS', df.fk_department_code,
'Invalid department defined in tmd_fp_level_defaults' 
FROM tmd_fp_level_defaults df, @changed_table b 
WHERE df.fk_tenant_id = b.fk_tenant_id
AND not exists
(SELECT * FROM 
(
SELECT oh.fk_tenant_id, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fk_department_code FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version 
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id) A
WHERE df.fp_level_1_value = a.fp_level_1_value AND df.fk_tenant_id = A.fk_tenant_id
AND df.fk_department_code = A.fk_department_code
)
AND df.fk_tenant_id IN (
SELECT fk_tenant_id FROM tco_parameters p1 WHERE p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1 AND p1.param_value like 'org_id%'
AND NOT EXISTS (SELECT * FROM tco_parameters p2 WHERE p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1 AND p2.param_value like 'org_id%'
AND p1.fk_tenant_id = p2.fk_tenant_id)
)
union 
SELECT df.fk_tenant_id, 'INVALID_DEFAULT_SETUP_DEPARTMENTS', df.fk_department_code,
'Invalid department defined in tmd_fp_level_defaults' 
FROM tmd_fp_level_defaults df, @changed_table b 
WHERE df.fk_tenant_id = b.fk_tenant_id
and fp_level_2_value != ''
AND not exists
(SELECT * FROM 
(
SELECT oh.fk_tenant_id, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fk_department_code FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id) A
WHERE df.fp_level_1_value = a.fp_level_1_value AND df.fp_level_2_value = A.fp_level_2_value AND df.fk_tenant_id = A.fk_tenant_id
AND df.fk_department_code = A.fk_department_code
)
AND df.fk_tenant_id IN (SELECT fk_tenant_id FROM tco_parameters p2 WHERE p2.fk_tenant_id = @tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1 AND param_value like 'org_id%')



INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT  S.fk_tenant_id, 'MISSING_DEFAULT_SETUP_ACCOUNTS', 
'YEAR ' +CONVERT(CHAR(4), S.budget_year) + ',level1value ' +  S.fp_level_1_value,
'Account missing in tmd_acc_defaults'
FROM
(SELECT a.fk_tenant_id, b.budget_year, action_type =
    CASE WHEN action_type = 9 THEN 90
        WHEN action_type = 21 THEN 20
        WHEN action_type = 31 THEN 30
        WHEN action_type = 41 THEN 40
        end,
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount,
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = ''
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
WHERE a.action_type IN (9, 21, 31, 41)) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS
(SELECT * FROM tmd_acc_defaults T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND T.link_type = 'SERVICEAREA'
AND T.module = 'FP'
AND T.link_value = S.fp_level_1_value
AND acc_type = 'ACCOUNT'
);


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT  S.fk_tenant_id, 'MISSING_DEFAULT_SETUP_DEPARTMENT', 
'YEAR ' +CONVERT(CHAR(4), S.budget_year) + ',level1value ' +  S.fp_level_1_value,
'Deoartment missing in tmd_acc_defaults'
FROM
(SELECT a.fk_tenant_id, b.budget_year, action_type =
    CASE WHEN action_type = 9 THEN 90
        WHEN action_type = 21 THEN 20
        WHEN action_type = 31 THEN 30
        WHEN action_type = 41 THEN 40
        end,
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount,
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = ''
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
WHERE a.action_type IN (9, 21, 31, 41)) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS
(SELECT * FROM tmd_acc_defaults T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND T.link_type = 'SERVICEAREA'
AND T.module = 'FP'
AND T.link_value = S.fp_level_1_value
AND acc_type = 'DEPARTMENT'
);

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT DISTINCT S.fk_tenant_id, 'MISSING_DEFAULT_SETUP_FUNCTIONS_MONTHREP', 
'YEAR ' +CONVERT(CHAR(4), @current_year) + ',level1value ' +  S.fp_level_1_value,
'Function missing in tmd_acc_defaults'
FROM
(SELECT oh.fk_tenant_id, 
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = ''
FROM tco_org_hierarchy oh 
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND @current_year*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = oh.fk_tenant_id ) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS
(SELECT * FROM tmd_acc_defaults T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND T.link_type = 'SERVICEAREA'
AND T.module = 'FP'
AND T.link_value = S.fp_level_1_value
AND acc_type = 'FUNCTION'
);
print 'END Check on default accounting setup MR function'


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT DISTINCT S.fk_tenant_id, 'MISSING_DEFAULT_SETUP_ACCOUNTS_MONTHREP', 
'YEAR ' +CONVERT(CHAR(4), @current_year) + ',level1value ' +  S.fp_level_1_value,
'Account missing in tmd_acc_defaults'
FROM
(SELECT oh.fk_tenant_id, 
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = ''
FROM tco_org_hierarchy oh 
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND @current_year*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = oh.fk_tenant_id ) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS
(SELECT * FROM tmd_acc_defaults T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND T.link_type = 'SERVICEAREA'
AND T.module = 'FP'
AND T.link_value = S.fp_level_1_value
AND acc_type = 'ACCOUNT'
);
print 'END Check on default accounting setup MR account'

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT  S.fk_tenant_id, 'MISSING_DEFAULT_SETUP_FUNCTIONS', 
'YEAR ' +CONVERT(CHAR(4), S.budget_year) + ',level1value ' +  S.fp_level_1_value,
'Function missing in tmd_acc_defaults'
FROM
(SELECT a.fk_tenant_id, b.budget_year, action_type =
    CASE WHEN action_type = 9 THEN 90
        WHEN action_type = 21 THEN 20
        WHEN action_type = 31 THEN 30
        WHEN action_type = 41 THEN 40
        end,
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount,
fp_level_1_value =
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = ''
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND (b.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
WHERE a.action_type IN (9, 21, 31, 41)) S,  @changed_table b
WHERE 1=1
AND S.fk_tenant_id = b.fk_tenant_id
AND NOT EXISTS
(SELECT * FROM tmd_acc_defaults T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND T.link_type = 'SERVICEAREA'
AND T.module = 'FP'
AND T.link_value = S.fp_level_1_value
AND acc_type = 'FUNCTION'
);
print 'END Check on default accounting setup'


--select @timestamp = sysdatetime();
--PRINT 'Job ended ok at ' + convert(nvarchar(19),@timestamp)


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)

SELECT df.fk_tenant_id, 'INVALID VALUES IN SETUP', df.fk_account_code, 'Account used in tmd_fp_level_defaults is not valid. Budprop will throw an error' 
FROM tmd_fp_level_defaults df
WHERE fk_tenant_id = @tenant_id
AND NOT EXISTS (
SELECT * FROM tco_accounts ac WHERE df.fk_account_code = ac.pk_account_code AND df.fk_tenant_id = ac.pk_tenant_id AND AC.isActive = 1)

UNION ALL

SELECT df.fk_tenant_id, 'INVALID VALUES IN SETUP', df.fk_department_code, 'Department used in tmd_fp_level_defaults is not valid. Budprop will throw an error' 
FROM tmd_fp_level_defaults df
WHERE fk_tenant_id = @tenant_id
AND NOT EXISTS (
SELECT * FROM tco_departments d WHERE df.fk_department_code = d.pk_department_code AND df.fk_tenant_id = d.fk_tenant_id AND d.status = 1)

UNION ALL

SELECT df.fk_tenant_id, 'INVALID VALUES IN SETUP', df.fk_function_code, 'Function used in tmd_fp_level_defaults is not valid. Budprop will throw an error' 
FROM tmd_fp_level_defaults df
WHERE fk_tenant_id = @tenant_id
AND NOT EXISTS (
SELECT * FROM tco_functions fc WHERE df.fk_function_code = fc.pk_Function_code AND df.fk_tenant_id = fc.pk_tenant_id AND fc.isActive = 1)

--- check tmd_acc_defaults


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select d.fk_tenant_id, 'INVALID_ACCOUNT_SETUP', d.acc_value, 'Account in tmd_acc_defaults does not exist in tco_accounts' 
from tmd_acc_defaults d
join @changed_table c on d.fk_tenant_id = c.fk_tenant_id
where acc_type='ACCOUNT'
and acc_value not in (select pk_account_code from tco_accounts where pk_tenant_id=c.fk_tenant_id)

--- check tmd_acc_defaults for invalid departments


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select d.fk_tenant_id, 'INVALID_DEPT_SETUP', d.acc_value, 'Department in tmd_acc_defaults does not exist in tco_departments' 
from tmd_acc_defaults d
join @changed_table c on d.fk_tenant_id = c.fk_tenant_id
where acc_type='DEPARTMENT'
and acc_value not in (select pk_department_code from tco_departments where fk_tenant_id=c.fk_tenant_id)

--- Check tmd_acc_defaults for invalid departments and link to org

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select d.fk_tenant_id, 'INVALID_DEPT_SETUP_2', d.acc_value, 'Department/org setup invalid in tmd_acc_defaults for ' + d.link_type + ' link_value = ' + link_value
FROM
(select ad.* from tmd_acc_defaults ad 
join @changed_table c on ad.fk_tenant_id = c.fk_tenant_id
where acc_type='DEPARTMENT' and module='FP'  and link_type='BUDALLOC_ORG_L1'
AND NOT EXISTS (SELECT * FROM tco_org_hierarchy oh WHERE oh.fk_tenant_id = ad.fk_tenant_id 
AND oh.fk_org_version = ad.fk_org_version AND oh.fk_department_code = ad.acc_value
AND ad.link_value = oh.org_id_1)
UNION ALL
select ad.* from tmd_acc_defaults ad
join @changed_table c on ad.fk_tenant_id = c.fk_tenant_id
where acc_type='DEPARTMENT' and module='FP'  and link_type='BUDALLOC_ORG_L2'
AND NOT EXISTS (SELECT * FROM tco_org_hierarchy oh WHERE oh.fk_tenant_id = ad.fk_tenant_id 
AND oh.fk_org_version = ad.fk_org_version AND oh.fk_department_code = ad.acc_value
AND ad.link_value = oh.org_id_2)
UNION ALL
select ad.* from tmd_acc_defaults ad 
join @changed_table c on ad.fk_tenant_id = c.fk_tenant_id
where acc_type='DEPARTMENT' and module='FP'  and link_type='BUDALLOC_ORG_L3'
AND NOT EXISTS (SELECT * FROM tco_org_hierarchy oh WHERE oh.fk_tenant_id = ad.fk_tenant_id 
AND oh.fk_org_version = ad.fk_org_version AND oh.fk_department_code = ad.acc_value
AND ad.link_value = oh.org_id_3)
UNION ALL
select ad.* from tmd_acc_defaults ad
join @changed_table c on ad.fk_tenant_id = c.fk_tenant_id
where acc_type='DEPARTMENT' and module='FP'  and link_type='BUDALLOC_ORG_L4'
AND NOT EXISTS (SELECT * FROM tco_org_hierarchy oh WHERE oh.fk_tenant_id = ad.fk_tenant_id 
AND oh.fk_org_version = ad.fk_org_version AND oh.fk_department_code = ad.acc_value
AND ad.link_value = oh.org_id_4)
union all
select ad.* from tmd_acc_defaults ad 
join @changed_table c on ad.fk_tenant_id = c.fk_tenant_id
where acc_type='DEPARTMENT' and module='FP'  and link_type='BUDALLOC_ORG_L5'
AND NOT EXISTS (SELECT * FROM tco_org_hierarchy oh WHERE oh.fk_tenant_id = ad.fk_tenant_id 
AND oh.fk_org_version = ad.fk_org_version AND oh.fk_department_code = ad.acc_value
AND ad.link_value = oh.org_id_5)) d


--- Data check on investments

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)

SELECT DISTINCT tr.fk_tenant_id, 'INVESTMENT TABLES DISCREPENCY', convert(varchar(10),tr.fk_investment_id) + '-' + CONVERT(char(4),tr.budget_year), 'Transactions exist in tfp_inv_transactions but not in tco_investment_detail' 
FROM tfp_inv_transactions tr 
JOIN @changed_table c ON tr.fk_tenant_id = c.fk_tenant_id
WHERE NOT EXISTS (SELECT * FROM tco_investment_detail d WHERE 
tr.fk_tenant_id = d.fk_tenant_id
AND tr.fk_inv_details_id = d.pk_id
AND tr.budget_year = d.budget_year
AND tr.fk_investment_id = d.fk_investment_id)
AND tr.fk_investment_id != 0
AND tr.budget_year IN (@current_year, @current_year+1, @prev_year)

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT DISTINCT a.fk_tenant_id, 'INVESTMENT TABLES DISCREPENCY_2',convert(varchar(10),a.fk_investment_id) + '-' + CONVERT(char(4),a.budget_year), 'Transactions exist in tco_investment_detail but not in tfp_inv_transactions' 
FROM tco_investment_detail a 
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id
JOIN tco_inv_budgetyear_config c ON a.fk_tenant_id = c.fk_tenant_id AND a.budget_year = c.budget_year AND a.fk_investment_id = c.fk_investment_id
JOIN tco_investments i ON a.fk_tenant_id = i.fk_tenant_id AND a.fk_investment_id = i.pk_investment_id
WHERE c.inv_status IN (0,1,2,7,8) AND type != 'oe'
AND NOT EXISTS (SELECT * FROM tfp_inv_transactions b WHERE a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year
AND b.fk_inv_details_id = a.pk_id AND a.fk_investment_id = b.fk_investment_id)
 
--- Setup of payroll accounts 

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT ac.pk_tenant_id, 'SALARY_ACCOUNT_SETUP', ac.pk_account_code, 'Account is not defined in tmd_salary_acc_def' 
FROM tco_accounts ac
JOIN gmd_reporting_line rl ON rl.report = 'YBUD1' AND rl.fk_kostra_account_code = ac.fk_kostra_account_code AND line_group_id = 3000 AND line_item_id between 3000 and 3005
JOIN tco_module_mapping mp ON ac.pk_tenant_id = mp.fk_tenant_id AND mp.fk_module_id= 4
JOIN @changed_table c ON ac.pk_tenant_id = c.fk_tenant_id
AND NOT EXISTS (SELECT * FROM tmd_salary_acc_def df WHERE ac.pk_account_code = df.fk_account_code AND ac.pk_tenant_id = df.fk_tenant_id)
ORDER BY ac.fk_kostra_account_code

--- Account setup in monthly report

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT ac.pk_tenant_id, 'MONTHREP_ACCOUNT_SETUP_1', ac.pk_account_code, 'Account is not defined in tmd_reporting_line type MNDRAPP'  
FROM tco_accounts ac
JOIN gmd_reporting_line rl ON rl.report = 'YBUD1' AND rl.fk_kostra_account_code = ac.fk_kostra_account_code 
JOIN tco_module_mapping mp ON ac.pk_tenant_id = mp.fk_tenant_id AND mp.fk_module_id= 5
JOIN @changed_table c ON ac.pk_tenant_id = c.fk_tenant_id
AND NOT EXISTS (SELECT * FROM tmd_reporting_line df WHERE ac.pk_account_code = df.fk_account_code AND ac.pk_tenant_id = df.fk_tenant_id AND report = 'MNDRAPP')
AND ac.fk_kostra_account_code not in ('1580','1980')
order BY ac.fk_kostra_account_code

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT ac.pk_tenant_id, 'MONTHREP_ACCOUNT_SETUP_2', ac.pk_account_code, 'Account is not defined in tmd_reporting_line type INVREPORT'  
FROM tco_accounts ac
JOIN gmd_reporting_line rl ON rl.report = 'B2A' AND rl.fk_kostra_account_code = ac.fk_kostra_account_code 
JOIN tco_module_mapping mp ON ac.pk_tenant_id = mp.fk_tenant_id AND mp.fk_module_id= 5
JOIN @changed_table c ON ac.pk_tenant_id = c.fk_tenant_id
AND NOT EXISTS (SELECT * FROM tmd_reporting_line df WHERE ac.pk_account_code = df.fk_account_code AND ac.pk_tenant_id = df.fk_tenant_id AND report = 'INVREPORT')
AND ac.fk_kostra_account_code not in ('1580','1980')
order BY ac.fk_kostra_account_code

--- Check for missing reference for budget phase (bug 52818)

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select ch.fk_tenant_id, 'BUDGET_PHASE_MISSING',ch.pk_change_id, 'Invalid budget phase defined for this budget change' from tfp_budget_changes ch
JOIN @changed_table c ON ch.fk_tenant_id = c.fk_tenant_id 
where not exists 
(select *
from tco_budget_phase ph
WHERE  ch.fk_tenant_id = ph.fk_tenant_id
AND ch.fk_budget_phase_id = PH.pk_budget_phase_id)

--- Check for missing project setup connection to investments


SELECT DISTINCT td.fk_tenant_id,td.gl_year, td.fk_project_code,p.pk_project_code, p.project_name, mp.pk_main_project_code, mp.main_project_name
INTO #missing_projects
FROM gmd_reporting_line rl  
 JOIN tco_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code  
 JOIN tfp_accounting_data td ON ac.pk_account_code = td.fk_account_code AND ac.pk_tenant_id= td.fk_tenant_id  
 JOIN @changed_table c ON td.fk_tenant_id = c.fk_tenant_id 
 LEFT JOIN tco_projects p ON td.fk_tenant_id = p.fk_tenant_id AND td.fk_project_code = p.pk_project_code AND td.gl_year BETWEEN DATEPART (year, p.date_from) AND DATEPART (year, p.date_to)  
 LEFT JOIN (SELECT DISTINCT a.fk_tenant_id, a.pk_investment_id, a.fk_main_project_code,b.budget_year, yc.fk_org_id, yc.org_name FROM tco_investments a, tco_investment_detail b, tco_inv_budgetyear_config yc WHERE a.fk_tenant_id = b.fk_tenant_id AND a.pk_investment_id = b.fk_investment_id AND b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year ) i   
 ON i.fk_tenant_id = p.fk_tenant_id AND i.budget_year = td.gl_year AND i.fk_main_project_code = p.fk_main_project_code  AND i.fk_main_project_code != ''
 LEFT JOIN tco_main_projects mp ON mp.fk_tenant_id = p.fk_tenant_id AND mp.pk_main_project_code = p.fk_main_project_code  
WHERE rl.report = 'B2A' AND rl.line_item_id = 500 AND td.gl_year IN (@current_year, @prev_year)
AND i.pk_investment_id is null

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT p.fk_tenant_id, 'INVEST_PROJECT_SETUP_1', p.fk_project_code, 'Blank project on investment account in tfp_accounting_data. Year=' + convert(char(4),p.gl_year)
FROM #missing_projects p
WHERE p.fk_project_code = ''

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT p.fk_tenant_id, 'INVEST_PROJECT_SETUP_2', p.fk_project_code, 'Project used in tfp_accounting_data invalid in tco_projects. Year=' + convert(char(4),p.gl_year)
FROM #missing_projects p
WHERE p.pk_project_code is null
AND p.fk_project_code != ''

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT p.fk_tenant_id, 'INVEST_PROJECT_SETUP_3', p.fk_project_code, 'Invalid main project in tco_projects. Year=' + convert(char(4),p.gl_year)
FROM #missing_projects p
WHERE p.pk_project_code is NOT null
AND p.fk_project_code != ''
AND p.pk_main_project_code is null
order by p.fk_project_code


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT p.fk_tenant_id, 'INVEST_PROJECT_SETUP_4', pk_main_project_code, 'Main project missing tco_investments. Year=' + convert(char(4),p.gl_year)
FROM #missing_projects p
WHERE p.pk_project_code is NOT null
AND p.fk_project_code != ''
AND p.pk_main_project_code is not null
order by p.fk_project_code

--- Check that accounts for staff planning is defined ---
INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select fk_tenant_id, 'MISSING_DEFAULT_STAFF PLANNING', 'AGA_HOL', 'Not defined in tmd_acc_defaults for org version: ' + pk_org_version
FROM
(
select t.pk_id as fk_tenant_id, pk_org_version
FROM gco_tenants t
JOIN tco_module_mapping mp ON t.pk_id = mp.fk_tenant_id AND mp.fk_module_id = 4
JOIN tco_org_version ov ON t.pk_id = ov.fk_tenant_id
join @changed_table c ON t.pk_id = c.fk_tenant_id
WHERE NOT exists (SELECT *
from tmd_acc_defaults ad
where ad.module = 'BU'
and ad.link_value = 'AGA_HOL'
and ad.fk_tenant_id = t.pk_id
and ad.fk_org_version = ov.pk_org_version)) s

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select fk_tenant_id, 'MISSING_DEFAULT_STAFF PLANNING', 'AGA_SAL', 'Not defined in tmd_acc_defaults for org version: ' + pk_org_version
FROM
(
select t.pk_id as fk_tenant_id, ov.pk_org_version
FROM gco_tenants t
JOIN tco_module_mapping mp ON t.pk_id = mp.fk_tenant_id AND mp.fk_module_id = 4
JOIN tco_org_version ov ON t.pk_id = ov.fk_tenant_id
join @changed_table c ON t.pk_id = c.fk_tenant_id
WHERE NOT exists (SELECT *
from tmd_acc_defaults ad
where ad.module = 'BU'
and ad.link_value = 'AGA_SAL'
and ad.fk_tenant_id = t.pk_id
and ad.fk_org_version = ov.pk_org_version)) s

--- Check that setup for employmnents is in place

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
select t.pk_id as fk_tenant_id,'MISSING_DEFAULT_STAFF PLANNING_2', 'TAX_RATE', 'Not defined default tax_rate in tbu_employments_tax_rate'
FROM gco_tenants t
JOIN tco_module_mapping mp ON t.pk_id = mp.fk_tenant_id AND mp.fk_module_id = 4
join @changed_table c ON t.pk_id = c.fk_tenant_id
WHERE NOT exists (select count(*) from [dbo].[tbu_employments_tax_rate] tr
where tr.fk_tenant_id = c.fk_tenant_id AND is_default = 1  HAVING COUNT(*) = 1
)

---

--- Check that org version is defined in different tables

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT S.fk_tenant_id, 'TABLES_MISSING_ORG_VERSION', table_name, error FROM (
SELECT 'tmd_acc_defaults' as table_name, fk_tenant_id, convert(varchar(25),count(*)) + ' rader som ligger uten fk_org_version' as error FROM tmd_acc_defaults			WHERE fk_org_version is null or fk_org_version = '' GROUP BY fk_tenant_id
union
SELECT 'tmd_fp_level_defaults' as table_name, fk_tenant_id, convert(varchar(25),count(*)) + ' rader som ligger uten fk_org_version' as error FROM tmd_fp_level_defaults		WHERE fk_org_version is null or fk_org_version = ''	GROUP BY fk_tenant_id
union
SELECT 'tco_user_orgrole' as table_name, fk_tenant_id, convert(varchar(25),count(*)) + ' rader som ligger uten fk_org_version' as error FROM tco_user_orgrole			WHERE fk_org_version is null or fk_org_version = ''	GROUP BY fk_tenant_id
) S 
join @changed_table c ON S.fk_tenant_id = c.fk_tenant_id

---- Check that aga rate is set up


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT t.pk_id, 'AGA_RATE_MISSING', t.municipality_id, 'Mangler for i gmd_emp_tax_rates for dette kommunenr'
FROM gco_tenants t
JOIN tco_module_mapping m ON m.fk_tenant_id = t.pk_id AND m.fk_module_id = 4
WHERE not exists (SELECT * FROM gmd_emp_tax_rates e WHERE t.municipality_id = e.fk_municipality_id) 
AND @tenant_id = 0 


-- Check that absence data has valid function code

INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT DISTINCT a.fk_tenant_id, 'ABSENCE_FUNCTION',a.fk_function_code, 'Ugyldig funksjonskode i tmr_absence_aggregated'
FROM tmr_absence_aggregated a
join @changed_table c ON a.fk_tenant_id = c.fk_tenant_id
WHERE 1=1
AND NOT EXISTS (SELECT * FROM tco_functions f WHERE a.fk_tenant_id = f.pk_tenant_id AND a.fk_function_code = f.pk_Function_code)
AND a.fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM tco_service_level)


INSERT INTO @error_table (fk_tenant_id, error_type, error_value, error_description)
SELECT DISTINCT a.fk_tenant_id, 'ABSENCE_FUNCTION2',a.fk_function_code, 'Ugyldig funksjonskode i tmr_absence_details'
FROM tmr_absence_details a
join @changed_table c ON a.fk_tenant_id = c.fk_tenant_id
WHERE 1=1
AND NOT EXISTS (SELECT * FROM tco_functions f WHERE a.fk_tenant_id = f.pk_tenant_id AND a.fk_function_code = f.pk_Function_code)
AND a.fk_tenant_id IN (SELECT DISTINCT fk_tenant_id FROM tco_service_level)



IF @tenant_id = 0 

BEGIN 

PRINT 'Check table tco_error_in_setup for findings'

TRUNCATE TABLE tco_error_in_setup;

INSERT INTO tco_error_in_setup (fk_tenant_id, error_type, error_value, error_description, updated)
SELECT DISTINCT fk_tenant_id, error_type, error_value, error_description, GETDATE() 
FROM @error_table 
ORDER BY fk_tenant_id, error_type, error_description, error_value

END

ELSE

BEGIN
SELECT DISTINCT * FROM @error_table ORDER BY fk_tenant_id, error_type, error_description, error_value
END

GO


