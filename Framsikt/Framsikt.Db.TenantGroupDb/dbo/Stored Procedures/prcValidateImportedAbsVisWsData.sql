CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedAbsVisWsData]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@org_version NVARCHAR(50),
	@jobID bigint
AS
		--Clear all the error information--
	IF( @jobID = -1 )
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET 
			budget_year_error = 0,
			employee_id_error = 0,
			id_error = 0,
			unit_name_error = 0,
			unit_code_error = 0,
			absence_code_error = 0,
			date_from_error = 0,
			date_to_error = 0,
			absence_percentage_error = 0,
			position_id_error = 0,
			continued_external_id_error = 0,
			work_related_error = 0,
			pregnancy_related_error = 0,
			sick_leave_id_error = 0,
			error_count = 0
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id ;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET 
			budget_year_error = 0,
			employee_id_error = 0,
			id_error = 0,
			unit_name_error = 0,
			unit_code_error = 0,
			absence_code_error = 0,
			date_from_error = 0,
			date_to_error = 0,
			absence_percentage_error = 0,
			position_id_error = 0,
			continued_external_id_error = 0,
			work_related_error = 0,
			pregnancy_related_error = 0,
			sick_leave_id_error = 0,
			error_count = 0
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID;
		END

	--Validate id --
	IF(@jobID = -1)
	  BEGIN
		UPDATE tco_stage_absence_employees_import
		SET id_error = 1, error_count = error_count + 1
		FROM tco_stage_absence_employees_import 
		WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
			tco_stage_absence_employees_import.budget_year = @budget_year AND 
			tco_stage_absence_employees_import.user_id = @user_id AND 
			tco_stage_absence_employees_import.ida IS NULL;
	  END
	ELSE
	   BEGIN
		UPDATE tco_stage_absence_employees_import
		SET id_error = 1, error_count = error_count + 1
		FROM tco_stage_absence_employees_import 
		WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
			tco_stage_absence_employees_import.budget_year = @budget_year AND 
			tco_stage_absence_employees_import.job_id = @jobID AND 
			tco_stage_absence_employees_import.ida IS NULL;
	   END
	

	--Validate unit_name --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET unit_name_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.unit_name IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET unit_name_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.unit_name IS NULL;
		END

	--Validate unit_code --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET unit_code_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.unit_code IS NULL;
		END
	ELSE
	    BEGIN
			UPDATE tco_stage_absence_employees_import
			SET unit_code_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.unit_code IS NULL;
		END

	--Validate absence_code --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET absence_code_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.absence_code IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET absence_code_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.absence_code IS NULL;
		END

	--Validate date_from --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET date_from_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.date_from IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET date_from_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.date_from IS NULL;
		END

	--Validate date_to --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET date_to_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.date_to IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET date_to_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.date_to IS NULL;
		END

	--Validate absence_percentage --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET absence_percentage_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.absence_percentage IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET absence_percentage_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.absence_percentage IS NULL;
		END

	--Validate work_related --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET work_related_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.work_related IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET work_related_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.work_related IS NULL;
		END
	--Validate pregnancy_related --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET pregnancy_related_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.pregnancy_related IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET pregnancy_related_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.pregnancy_related IS NULL;
		END

	--Validate sick_leave_id --
	IF(@jobID = -1)
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET sick_leave_id_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.user_id = @user_id AND 
				tco_stage_absence_employees_import.sick_leave_id IS NULL;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_absence_employees_import
			SET sick_leave_id_error = 1, error_count = error_count + 1
			FROM tco_stage_absence_employees_import 
			WHERE tco_stage_absence_employees_import.fk_tenant_id = @tenant_id AND 
				tco_stage_absence_employees_import.budget_year = @budget_year AND 
				tco_stage_absence_employees_import.job_id = @jobID AND 
				tco_stage_absence_employees_import.sick_leave_id IS NULL;
		END

RETURN 0

