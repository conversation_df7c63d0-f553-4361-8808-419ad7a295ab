CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRAssignments]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS
	
DECLARE @budget_year INT
DECLARE @org_version VARCHAR(25) 
DECLARE @language VARCHAR(10)
DECLARE @assignment_flag VARCHAR(25)
DECLARE @assignment_org_level INT
DECLARE @prev_assignment_period INT


SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
SET @budget_year = @forecast_period/100


SET @prev_assignment_period = (
SELECT MAX(forecast_period) FROM tbi_assignment_monthly_status
WHERE fk_Tenant_id = @fk_tenant_id
AND forecast_period < @forecast_period
AND forecast_period > @budget_year * 100
)

SET @assignment_org_level = (SELECT min(flag_status) FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id AND flag_name = 'MR_COPYREPORT_ASSIGNMENTS_ORGLEVEL' AND fk_org_version = @org_version)
SET @assignment_flag = (SELECT min(a.flag_key_id) FROM tco_application_flag a WHERE fk_tenant_id = @fk_tenant_id AND flag_name = 'MR_COPYREPORT_ASSIGNMENTS_ORGLEVEL' AND fk_org_version = @org_version)


IF @assignment_flag IS NULL
BEGIN
SET @assignment_flag = '0'
END


IF @assignment_flag = '0'
BEGIN

	PRINT 'Insert assignments from virkplan with blank status, risk and description'

	INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
	SELECT a.pk_assignment_id,0 as status,0 as risk,'' as description,
	@forecast_period,a.fk_tenant_id,getdate() as updated,@user_id,a.included_in_monthly_report as is_included_in_mr_doc 
	FROM tbi_assignments a
	WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
	AND a.included_in_monthly_report = 1
	AND NOT EXISTS (SELECT 1 FROM tbi_assignment_monthly_status b
	WHERE b.fk_tenant_id = a.fk_tenant_id
	AND b.forecast_period = @forecast_period
	AND a.pk_assignment_id = b.fk_assignment_id)
	
END

	INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
	SELECT a.fk_assignment_id,a.status,a.risk,a.description,@forecast_period,a.fk_tenant_id,GETDATE() AS updated,a.updated_by,a.is_included_in_mr_doc
	FROM tbi_assignment_monthly_status a
	JOIN tco_progress_status s ON a.fk_tenant_id = s.fk_tenant_id AND a.status = s.status_id AND s.finished_flag = 1 AND s.type = 'sunit_bplan' 
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.forecast_period = @prev_assignment_period
	AND NOT EXISTS (SELECT * FROM tbi_assignment_monthly_status b
	WHERE b.fk_tenant_id = a.fk_tenant_id
	AND b.forecast_period = @forecast_period
	AND a.fk_assignment_id = b.fk_assignment_id)

PRINT 'Start: Getting assignment and task status from prev reporting period'

	IF @assignment_flag = '1' AND @assignment_org_level = 0
	begin

	
	INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
	SELECT fk_assignment_id,status,risk,'' AS description,@forecast_period,fk_tenant_id,GETDATE() AS updated,updated_by,is_included_in_mr_doc
	FROM tbi_assignment_monthly_status a
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.forecast_period = @prev_assignment_period
	AND NOT EXISTS (SELECT * FROM tbi_assignment_monthly_status b
	WHERE b.fk_tenant_id = a.fk_tenant_id
	AND b.forecast_period = @forecast_period
	AND a.fk_assignment_id = b.fk_assignment_id)
	
	end

	IF @assignment_flag = '1' AND @assignment_org_level > 0
	begin

	-- Temporary removed copy of description because of ck5 issue.

	INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
	SELECT a.fk_assignment_id,a.status,a.risk,a.description,@forecast_period,a.fk_tenant_id,GETDATE() AS updated,a.updated_by,
	a.is_included_in_mr_doc
	FROM tbi_assignment_monthly_status a
	JOIN tbi_assignments bi ON A.fk_tenant_id = BI.fk_tenant_id AND a.fk_assignment_id = bi.pk_assignment_id AND bi.org_level >=  @assignment_org_level
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.forecast_period = @prev_assignment_period
	AND NOT EXISTS (SELECT * FROM tbi_assignment_monthly_status b
	WHERE b.fk_tenant_id = a.fk_tenant_id
	AND b.forecast_period = @forecast_period
	AND a.fk_assignment_id = b.fk_assignment_id)
	
	end

	if  @assignment_flag = '2' AND @assignment_org_level = 0
		begin

		INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
		SELECT a.fk_assignment_id,a.status,ISNULL(m.risk, 0) as risk,'' AS description,@forecast_period,
		a.fk_tenant_id,GETDATE() AS updated,a.updated_by,ISNULL(m.is_included_in_mr_doc,0) as is_included_in_mr_doc
		FROM tbi_assignment_live_status a
		LEFT JOIN tbi_assignment_monthly_status m ON m.forecast_period = @prev_assignment_period AND m.fk_tenant_id = a.fk_tenant_id AND m.fk_assignment_id = a.fk_assignment_id
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.year = @budget_year
		AND NOT EXISTS (SELECT * FROM tbi_assignment_monthly_status b
		WHERE b.fk_tenant_id = a.fk_tenant_id
		AND b.forecast_period = @forecast_period
		AND a.fk_assignment_id = b.fk_assignment_id)

		end

	if  @assignment_flag = '2' AND @assignment_org_level > 0
		begin

		INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
		SELECT DISTINCT a.fk_assignment_id,a.status,ISNULL(m.risk, 0) as risk,a.description AS description,@forecast_period,
		a.fk_tenant_id,GETDATE() AS updated,a.updated_by,ISNULL(m.is_included_in_mr_doc,0) as is_included_in_mr_doc
		FROM tbi_assignment_live_status a
		JOIN tbi_assignments bi ON a.fk_tenant_id = BI.fk_tenant_id AND a.fk_assignment_id = bi.pk_assignment_id AND bi.org_level >=  @assignment_org_level
		LEFT JOIN tbi_assignment_monthly_status m ON m.forecast_period = @prev_assignment_period AND m.fk_tenant_id = a.fk_tenant_id AND m.fk_assignment_id = a.fk_assignment_id
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.year = @budget_year
		AND NOT EXISTS (SELECT * FROM tbi_assignment_monthly_status b
		WHERE b.fk_tenant_id = a.fk_tenant_id
		AND b.forecast_period = @forecast_period
		AND a.fk_assignment_id = b.fk_assignment_id)

		end


	IF (SELECT COUNT(*) FROM tbi_assignment_monthly_status a WHERE a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period) = 0

	BEGIN

	PRINT 'Insert assignments from virkplan'

	INSERT INTO tbi_assignment_monthly_status (fk_assignment_id,status,risk,description,forecast_period,fk_tenant_id,updated,updated_by,is_included_in_mr_doc)
	SELECT a.pk_assignment_id,a.status as status,0 as risk,'' as description,
	@forecast_period,a.fk_tenant_id,getdate() as updated,@user_id,a.included_in_monthly_report as is_included_in_mr_doc 
	FROM tbi_assignments a
	JOIN tco_progress_status s ON a.status = s.status_id AND a.fk_tenant_id = s.fk_tenant_id AND s.type = 'sunit_bplan'
	WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
	AND a.included_in_monthly_report = 1
	
	END


RETURN 0
