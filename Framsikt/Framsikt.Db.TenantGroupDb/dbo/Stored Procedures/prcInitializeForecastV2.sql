CREATE OR ALTER PROCEDURE [dbo].[prcInitializeForecastV2] @forecast_period INT,  @fk_tenant_id INT, 
@init_type NVARCHAR(25), @user_id INT, @update_only INT = 0

AS

SET NOCOUNT ON;

DECLARE @budget_year INT
DECLARE @last_period INT
DECLARE @ub_period INT
DECLARE @month_remaining dec(18,6) 
DECLARE @month_ytd dec(18,6)
DECLARE @not_use_fr_budget INT
DECLARE @not_use_salary_forecast INT
DECLARE @prev_forecast_period INT
DECLARE @prev_assignment_period INT
DECLARE @service_level INT
DECLARE @continue INT
DECLARE @rowcount INT
DECLARE @periods_ytd INT
DECLARE @periods_remaining INT
DECLARE @org_version VARCHAR(25) 
DECLARE @language VARCHAR(10)
DECLARE @assignment_flag INT

DECLARE @report1A varchar(25)
DECLARE @report1B varchar (25)

SET @assignment_flag = (SELECT min(flag_status) FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id AND flag_name = 'MR_COPYREPORT_ORGLEVEL')

IF @assignment_flag IS NULL
BEGIN
SET @assignment_flag = 0
END


SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)


SET @periods_ytd= convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))
SET @periods_remaining = 12 - @periods_ytd;


DECLARE @acc_table TABLE (fk_account_code NVARCHAR(25))


SET @report1A = '54_DRIFTA'
SET @report1B = '54_DRIFTB'

IF @forecast_period <= 202113
BEGIN

SET @report1A = '54_DRIFTA_2021'
SET @report1B = '54_DRIFTB_2021'

END


DECLARE @tbl_reporting_line as table (report varchar(50)
, fk_kostra_account_code VARCHAR(25)
, line_group_id INT
, line_group varchar(200)
, line_item_id INT
, line_item varchar(150))


INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)
SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id, 
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),
line_item_id,
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))
FROM gmd_reporting_line rl
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @fk_tenant_id AND tg.Language = @language
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @fk_tenant_id AND tI.Language = @language
WHERE rl.report = @report1A

PRINT 'Start procedure '  + convert(varchar(400),GETDATE());

/*

Regnskap så langt + periodisert budsjett											= PerBud
Regnskap så langt + periodisert budsjett korrigert for budsjettavvik så langt i år	= PerBudCorr
Regnskap så langt + forrige prognose (periodisert) resten av året					= LastForecastPer
Regnskap så langt + (forrige prognose – regnskap så langt)							= LastForecastAcc
Regnskap så langt + årsbudsjett*andel måneder gjenstående							= BudRemaining
Årsbudsjett																			= YearlyBudget
Årsbudsjett drift/Prognose lønn														= AccSal
Actual																				= Actual
Actual/Salary forecast																= AccSalFor
Forecast from previous period														= LastForecast
Salary forecast + Last forecast														= SalLastforecast
Salary forecast + Accounting ytd/accrued budget rest of the year					= SalPerBud
Yearly budget included none approved budget changes									= YearBudUnappr
Forecast = 0																		= ForecastZero

*/

DECLARE @trans_id_table  TABLE (
	[forecast_period] [int] NOT NULL,
	[bu_trans_id] [uniqueidentifier] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[budget_type]  [int] NOT NULL,
    [tax_flag] INT NOT NULL, 
    [holiday_flag] INT NOT NULL, 
	[fk_pension_type] [nvarchar](12) NOT NULL,
	[total_amount] [decimal](18, 2) NOT NULL,
UNIQUE CLUSTERED(fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, forecast_period, budget_type, tax_flag, holiday_flag, fk_pension_type)
)

CREATE TABLE #forecast_table  ([pk_id] [uniqueidentifier] not null, 
	[forecast_period] INT NOT NULL,
    [bu_trans_id] UNIQUEIDENTIFIER NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [action_type] INT NOT NULL, 
    [line_order] INT NOT NULL, 
	fk_account_code NVARCHAR (25) NOT NULL,
	department_code NVARCHAR (25) NOT NULL, 
	fk_function_code NVARCHAR (25) NOT NULL,
	fk_project_code NVARCHAR (25) NOT NULL,
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
	resource_id NVARCHAR (25) NOT NULL,
	fk_employment_id BIGINT NOT NULL,
	description NVARCHAR (255) NOT NULL,
    [budget_year] INT NOT NULL, 
    [period] INT NOT NULL, 
    [budget_type] INT NOT NULL, 
    [amount_year_1] DECIMAL(18, 2) NOT NULL, 
    [amount_year_2] DECIMAL(18, 2) NOT NULL, 
    [amount_year_3] DECIMAL(18, 2) NOT NULL, 
    [amount_year_4] DECIMAL(18, 2) NOT NULL, 
	fk_key_id INT NOT NULL,
    [allocation_pct] DECIMAL(10, 7) NOT NULL, 
    [total_amount] DECIMAL(18, 2) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [tax_flag] INT NOT NULL, 
    [holiday_flag] INT NOT NULL, 
	[fk_pension_type] [nvarchar](12) NOT NULL,
    [fk_prog_code] NVARCHAR(25) NULL,
	[1A_line] bit DEFAULT ((0)) NOT NULL,
	[line_item_id] INT DEFAULT (0) NOT NULL,
	[line_item] NVARCHAR(200) DEFAULT(''),
	[line_group_id] INT DEFAULT (0) NOT NULL,
	[line_group] NVARCHAR(200) DEFAULT('')
UNIQUE CLUSTERED(pk_id, fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, forecast_period)	
	)

CREATE INDEX #ind_1_forecast_table ON #forecast_table (fk_account_code, department_code, fk_function_code ,fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_tenant_id);

CREATE INDEX #ind_2_forecast_table ON #forecast_table ([1A_line], line_group_id);


DECLARE @temp_table_1 TABLE 
	(fk_tenant_id INT NOT NULL, 
    forecast_period INT NOT NULL, 
	period INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
    free_dim_1 NVARCHAR(25) NOT NULL, 
    free_dim_2 NVARCHAR(25) NOT NULL, 
    free_dim_3 NVARCHAR(25) NOT NULL, 
    free_dim_4 NVARCHAR(25) NOT NULL, 
    bud_amt_year DECIMAL(18, 2) NOT NULL, 
    bud_amt_ytd DECIMAL(18, 2) NOT NULL, 
    bud_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_ytd DECIMAL(18, 2) NOT NULL, 
    actual_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_last_ytd DECIMAL(18, 2) NOT NULL,
    tech_amt_budget DECIMAL(18, 2) NOT NULL, 
    tech_amt_running DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	actual_amt_running DECIMAL(18, 2) NOT NULL, 	
	[fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',
	UNIQUE CLUSTERED (fk_tenant_id, period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, actual_amt_year,bud_amt_year,actual_amt_last_year,org_bud_amt_year,org_bud_amt_last_year, fk_prog_code))
	 ;


CREATE TABLE #temp_table_2
	(fk_tenant_id INT NOT NULL, 
    forecast_period INT NOT NULL, 
	period INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
    free_dim_1 NVARCHAR(25) NOT NULL, 
    free_dim_2 NVARCHAR(25) NOT NULL, 
    free_dim_3 NVARCHAR(25) NOT NULL, 
    free_dim_4 NVARCHAR(25) NOT NULL, 
    bud_amt_year DECIMAL(18, 2) NOT NULL, 
    bud_amt_ytd DECIMAL(18, 2) NOT NULL, 
    bud_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_ytd DECIMAL(18, 2) NOT NULL, 
    actual_amt_period DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_last_ytd DECIMAL(18, 2) NOT NULL,
    tech_amt_budget DECIMAL(18, 2) NOT NULL, 
    tech_amt_running DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	actual_amt_running DECIMAL(18, 2) NOT NULL, 
	[1A_line] bit DEFAULT ((0)) NOT NULL,
	[line_item_id] INT DEFAULT (0) NOT NULL,
	[line_item] NVARCHAR(200) DEFAULT(''),
	[line_group_id] INT DEFAULT (0) NOT NULL,
	[line_group] NVARCHAR(200) DEFAULT(''),
	[fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',
	UNIQUE CLUSTERED (fk_tenant_id, period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, actual_amt_year,bud_amt_year,actual_amt_last_year,org_bud_amt_year,org_bud_amt_last_year, fk_prog_code))
	 ;

CREATE INDEX #ind_1_temp_table_2 ON #temp_table_2 (fk_account_code, fk_department_code, fk_function_code ,fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_tenant_id);

CREATE INDEX #ind_2_temp_table_2 ON #temp_table_2 ([1A_line], line_group_id);

	
DECLARE @calc_table TABLE (pk_id INT IDENTITY NOT NULL,
	[forecast_period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL, 
	fk_account_code NVARCHAR (25) NOT NULL,
	department_code NVARCHAR (25) NOT NULL, 
	fk_function_code NVARCHAR (25) NOT NULL,
    [factor] float NOT NULL, 
    [gl_amount_ytd] DECIMAL(18, 2) NOT NULL, 
    [budget_amount_ytd] DECIMAL(18, 2) NOT NULL,
	[budget_amount_period] DECIMAL(18, 2)  NULL,
	[bud_amt_year] DECIMAL(18, 2)  NULL,
	UNIQUE CLUSTERED(forecast_period, fk_tenant_id, fk_account_code,department_code,fk_function_code)
)

DECLARE @period_table TABLE (pk_id INT IDENTITY NOT NULL,
	[period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL) 


SET @budget_year = @forecast_period/100
SET @last_period = (@budget_year*100)+12
SET @ub_period = (@budget_year*100)+13
SET @month_remaining = (12 - CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))/12
SET @month_ytd = (CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))
SET @not_use_fr_budget = (SELECT COUNT(*) FROM tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'DONT_USE_FRAMSIKT_SALARY' AND param_value = 'TRUE')
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_report_setup_status WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period AND tab_type = 1 AND status IN (2,4))
SET @not_use_salary_forecast =  (SELECT COUNT(*) FROM tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND active = 1 AND param_name = 'DONT_USE_SALARY_FORECAST' AND param_value = 'TRUE')
SET @prev_assignment_period = (
SELECT MAX(forecast_period) FROM tbi_assignment_monthly_status
WHERE fk_Tenant_id = @fk_tenant_id
AND forecast_period < @forecast_period
AND forecast_period > @budget_year * 100
)


BEGIN 

INSERT INTO @period_table (period, fk_tenant_id)
VALUES 
((@budget_year*100)+1, @fk_tenant_id),
((@budget_year*100)+2, @fk_tenant_id),
((@budget_year*100)+3, @fk_tenant_id),
((@budget_year*100)+4, @fk_tenant_id),
((@budget_year*100)+5, @fk_tenant_id),
((@budget_year*100)+6, @fk_tenant_id),
((@budget_year*100)+7, @fk_tenant_id),
((@budget_year*100)+8, @fk_tenant_id),
((@budget_year*100)+9, @fk_tenant_id),
((@budget_year*100)+10, @fk_tenant_id),
((@budget_year*100)+11, @fk_tenant_id),
((@budget_year*100)+12, @fk_tenant_id);

DELETE FROM @period_table WHERE period <= @forecast_period

END

INSERT INTO @acc_table(fk_account_code)
SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU' AND fk_org_version = @org_version AND link_value != 'SALARY'
union 
SELECT fk_account_code FROM tmd_salary_positions_accounts WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @budget_year
union
SELECT fk_account_code FROM tmd_pension_type WHERE fk_tenant_id = @fk_tenant_id 
union 
SELECT fk_account_code_aga FROM tmd_pension_type WHERE fk_tenant_id = @fk_tenant_id;

SELECT * 
INTO #rep_line_1B
FROM gmd_reporting_line
WHERE report = @report1B

--IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = '1B_EXLC_FINANCE_ROWS' AND active = 1 AND param_value = 'TRUE'
--AND fk_tenant_id = @fk_tenant_id) >=1
--BEGIN

--DELETE FROM #rep_line_1B WHERE line_group_id = 12
--END


BEGIN



	PRINT 'Start prosessing at '  + convert(varchar(400),GETDATE());


	IF @init_type NOT IN  ('YearBudUnappr','YearlyBudget','AccSal','PerBudCorr','LastForecast','SalLastforecast','SalPerBud')
	BEGIN

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID(), @forecast_period, '********-0000-0000-0000-********0000' as bu_trans_id,fk_tenant_id,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 as fk_employment_id,'' AS description,gl_year as budget_year,period,59 as budget_type,sum(amount) as amount_year_1,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount) as total_amount,GETDATE(),@user_id as updated_by,0 as tax_flag,0 as holiday_flag,0 as fk_pension_type, 0 as fk_prog_code
	FROM tfp_accounting_data
	WHERE fk_tenant_id = @fk_tenant_id
	AND period <= @forecast_period
	AND gl_year = @budget_year
	GROUP BY fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, period
	ORDER BY fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, period;

	PRINT 'End fetching GL- transactions...'  + convert(varchar(400),GETDATE());

	END



	SELECT fk_tenant_id,period, fk_account_code, department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year,fk_prog_code,
	SUM(amount) AS amount
	INTO #ACC_DATA
	FROM tfp_accounting_data 
	WHERE fk_tenant_id = @fk_tenant_id AND gl_year =  @budget_year
	GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
	HAVING SUM(amount) != 0;

	CREATE INDEX #ind_1_ACC_DATA ON #ACC_DATA (period, fk_tenant_id);
	CREATE INDEX #ind_2_ACC_DATA ON #ACC_DATA (fk_account_code, fk_tenant_id);
	CREATE INDEX #ind_3_ACC_DATA ON #ACC_DATA (department_code, fk_tenant_id);
	CREATE INDEX #ind_4_ACC_DATA ON #ACC_DATA (fk_function_code, fk_tenant_id);
	CREATE INDEX #ind_5_ACC_DATA ON #ACC_DATA (fk_project_code, fk_tenant_id);

	IF @init_type = 'PerBudCorr'

	BEGIN

	IF @not_use_fr_budget = 0 AND @not_use_salary_forecast = 0 

	BEGIN



	PRINT 'Initialize operations warehouse using salary forecast'  + convert(varchar(400),GETDATE());

	INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
	actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code)
	SELECT a.fk_tenant_id, @forecast_period,a.period, a.fk_account_code, a.department_code, a.fk_function_code,a.fk_project_code,
	a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, SUM(a.amount_year_1) as  bud_amt_year, bud_amt_ytd = 
	CASE WHEN period <= @forecast_period THEN SUM(amount_year_1) ELSE 0 END,
	bud_amt_period = CASE WHEN period = @forecast_period THEN SUM(amount_year_1) ELSE 0 END, 
	0 AS actual_amt_year, 0 AS actual_amt_ytd, 0 AS actual_amt_period,0,0, 
	tech_amt_budget = 
	CASE WHEN period > @forecast_period THEN SUM(amount_year_1) ELSE 0 END,
	0 AS tech_amt_running, 0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, '' as fk_prog_code
	FROM tbu_trans_detail a
	 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
	WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
	AND fk_employment_id = 0
	GROUP BY a.fk_tenant_id, a.period, a.fk_account_code, a.department_code, a.fk_function_code , a.fk_project_code,
	a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4
	HAVING SUM(amount_year_1)!= 0 ;


	PRINT 'Insert from accounting data 1'  + convert(varchar(400),GETDATE());

	INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
	actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year,actual_amt_running,fk_prog_code)
	SELECT fk_tenant_id, @forecast_period,period, fk_account_code, department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 as  bud_amt_year, 0 as bud_amt_ytd,
	0 as bud_amt_period, 
	SUM(amount) AS actual_amt_year, actual_amt_ytd = 
	CASE WHEN period <= @forecast_period THEN SUM(amount) ELSE 0 END,
	actual_amt_period= 
	CASE WHEN period = @forecast_period THEN SUM(amount) ELSE 0 END, 0,0,
	tech_amt_budget = 
	CASE WHEN period <= @forecast_period THEN SUM(amount) ELSE 0 END,
	tech_amt_running = 
	CASE WHEN period <= @forecast_period THEN SUM(amount)+(SUM(amount)/@periods_ytd*@periods_remaining) ELSE 0 END, 
	0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, fk_prog_code
	FROM #ACC_DATA 
	WHERE fk_tenant_id = @fk_tenant_id AND gl_year = @budget_year
	GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_prog_code
	HAVING SUM(amount) != 0;


	END

	ELSE
	BEGIN


	PRINT 'Initilize forcast PerBudCorr  not using salary forecast'  + convert(varchar(400),GETDATE());

	INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
	actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code)
	SELECT a.fk_tenant_id, @forecast_period,a.period, a.fk_account_code, a.department_code, a.fk_function_code,a.fk_project_code,
	a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4, SUM(a.amount_year_1) as  bud_amt_year, 
	bud_amt_ytd = 
	CASE WHEN period <= @forecast_period THEN SUM(amount_year_1) ELSE 0 END,
	bud_amt_period = CASE WHEN period = @forecast_period THEN SUM(amount_year_1) ELSE 0 END, 
	0 AS actual_amt_year, 0 AS actual_amt_ytd, 0 AS actual_amt_period,0,0, 
	tech_amt_budget = 
	CASE WHEN period > @forecast_period THEN SUM(amount_year_1) ELSE 0 END,
	0 AS tech_amt_running, 0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, '' as fk_prog_code
	FROM tbu_trans_detail a
	JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
	WHERE a.fk_tenant_id = @fk_tenant_id AND a.budget_year = @budget_year
	GROUP BY a.fk_tenant_id, a.period, a.fk_account_code, a.department_code, a.fk_function_code ,a.fk_project_code,
	a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4
	HAVING SUM(amount_year_1)!= 0 ;


	PRINT 'Insert from accounting data 1'  + convert(varchar(400),GETDATE());

	INSERT INTO @temp_table_1 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
	actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year,actual_amt_running,fk_prog_code)
	SELECT fk_tenant_id, @forecast_period,period, fk_account_code, department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 as  bud_amt_year, 0 as bud_amt_ytd,
	0 as bud_amt_period, 
	SUM(amount) AS actual_amt_year, actual_amt_ytd = 
	CASE WHEN period <= @forecast_period THEN SUM(amount) ELSE 0 END,
	actual_amt_period= 
	CASE WHEN period = @forecast_period THEN SUM(amount) ELSE 0 END, 0,0,
	tech_amt_budget = 
	CASE WHEN period <= @forecast_period THEN SUM(amount) ELSE 0 END,
	tech_amt_running = 
	CASE WHEN period <= @forecast_period THEN SUM(amount)+(SUM(amount)/@periods_ytd*@periods_remaining) ELSE 0 END, 
	0 as org_bud_amt_year, 0 AS org_bud_amt_last_year, 0 as actual_amt_running, fk_prog_code
	FROM #ACC_DATA 
	WHERE fk_tenant_id = @fk_tenant_id AND gl_year = @budget_year
	GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_prog_code
	HAVING SUM(amount) != 0;


	END



	INSERT INTO #temp_table_2 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
	actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running, org_bud_amt_year, org_bud_amt_last_year, actual_amt_running, fk_prog_code)

	SELECT fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code ,fk_project_code,
	free_dim_1, free_dim_2, free_dim_3, free_dim_4,  SUM(bud_amt_year), SUM(bud_amt_ytd), SUM(bud_amt_period), SUM(actual_amt_year), SUM(actual_amt_ytd),
	SUM(actual_amt_period), SUM(actual_amt_last_year), SUM(actual_amt_last_ytd), SUM(tech_amt_budget), 0 as tech_amt_running, SUM(org_bud_amt_year), SUM(org_bud_amt_last_year), SUM(actual_amt_running),fk_prog_code
	FROM @temp_table_1
	GROUP BY fk_tenant_id, forecast_period,period,fk_account_code, fk_department_code, fk_function_code,fk_project_code,
	 free_dim_1,  free_dim_2, free_dim_3, free_dim_4, fk_prog_code;


	INSERT INTO @calc_table (forecast_period,fk_tenant_id,fk_account_code,department_code,fk_function_code,gl_amount_ytd, budget_amount_ytd,factor, bud_amt_year)
	SELECT forecast_period,fk_tenant_id,fk_account_code,fk_department_code,fk_function_code,SUM(actual_amt_ytd), SUM(bud_amt_ytd),0 as factor, SUM(bud_amt_year)	
	FROM @temp_table_1
	GROUP BY forecast_period,fk_tenant_id,fk_account_code,fk_department_code,fk_function_code;


		UPDATE @calc_table SET factor = 
			CASE WHEN ROUND(budget_amount_ytd,0) = 0 THEN 0
			ELSE gl_amount_ytd/budget_amount_ytd
			END;

			UPDATE @calc_table SET budget_amount_period = gl_amount_ytd  / @month_ytd
											

	UPDATE #temp_table_2 SET tech_amt_running = CASE
		--WHEN ABS(b.bud_amt_year) >= 100 AND b.gl_amount_ytd = 0 THEN a.bud_amt_year
		WHEN a.period <= @forecast_period THEN a.actual_amt_ytd
		WHEN ABS(b.bud_amt_year) >= 100 THEN ROUND(a.bud_amt_year*b.factor,0)
		ELSE 0 END
	FROM #temp_table_2 a, @calc_table b
	WHERE a.forecast_period = b.forecast_period
	AND a.fk_tenant_id = b.fk_tenant_id
	AND a.fk_account_code = b.fk_account_code
	AND a.fk_department_code = b.department_code
	AND a.fk_function_code = b.fk_function_code


INSERT INTO #temp_table_2 (fk_tenant_id, forecast_period,period, fk_account_code, fk_department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,  bud_amt_year, bud_amt_ytd, bud_amt_period, actual_amt_year, actual_amt_ytd,
actual_amt_period, actual_amt_last_year, actual_amt_last_ytd, tech_amt_budget, tech_amt_running,org_bud_amt_year, org_bud_amt_last_year, actual_amt_running)
SELECT b.fk_tenant_id, b.forecast_period,c.period, b.fk_account_code, b.department_code, b.fk_function_code,'' as fk_project_code,
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4, 0 as bud_amt_year, 0 as bud_amt_ytd, 0 as bud_amt_period, 0 as actual_amt_year, 0 as actual_amt_ytd,
0 as actual_amt_period, 0 as actual_amt_last_year, 0 as actual_amt_last_ytd, 0 as tech_amt_budget, 
tech_amt_running= b.budget_amount_period, 0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS actual_amt_running
FROM @calc_table b, @period_table c
WHERE b.fk_tenant_id = c.fk_tenant_id
AND ABS (b.bud_amt_year) < 100
AND b.budget_amount_period is not null



	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description, @budget_year,period,1 as budget_type,tech_amt_running,0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,tech_amt_running AS total_amount,GETDATE() AS updated,@user_id AS updated_by,0 as tax_flag,0 as holiday_flag,0 as fk_pension_type,0 AS fk_prog_code
	FROM #temp_table_2

	END



	IF @init_type = 'AccSal'
	BEGIN

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID(), @forecast_period, '********-0000-0000-0000-********0000' as bu_trans_id,fk_tenant_id,5,0,a.fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 as fk_employment_id,'' AS description,gl_year as budget_year,period
	,1 as budget_type
	,sum(amount) as amount_year_1,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount) as total_amount,GETDATE(),@user_id as updated_by,0 as tax_flag,0 as holiday_flag,0 as fk_pension_type, 0 as fk_prog_code
	FROM tfp_accounting_data a, @acc_table b
	WHERE fk_tenant_id = @fk_tenant_id
	AND period <= @forecast_period
	AND gl_year = @budget_year
	AND a.fk_account_code = b.fk_account_code
	GROUP BY fk_tenant_id, a.fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, period
	ORDER BY fk_tenant_id, a.fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, period;


	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
	FROM tbu_trans_detail a
	 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.budget_year = @budget_year
	AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
	GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;

	

	IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SALFORCAST_EMP_ZERO' AND param_value = 'TRUE') > 0
	BEGIN

	Print 'Insert variable salary data'

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,a.fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,0 AS fk_prog_code
	FROM tbu_trans_detail a
	JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
	JOIN tmd_salary_positions_accounts tsp ON a.fk_tenant_id = tsp.fk_tenant_id AND a.fk_account_code = tsp.fk_account_code AND a.budget_year = tsp.budget_year
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.budget_year = @budget_year
	AND a.period > @forecast_period
	GROUP BY a.fk_tenant_id, a.fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;
	

	END



	END

	IF @init_type = 'SalLastforecast'
	BEGIN

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID(), @forecast_period, '********-0000-0000-0000-********0000' as bu_trans_id,fk_tenant_id,5,0,a.fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 as fk_employment_id,'' AS description,gl_year as budget_year,period
	,1 as budget_type
	,sum(amount) as amount_year_1,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount) as total_amount,GETDATE(),@user_id as updated_by,0 as tax_flag,0 as holiday_flag,0 as fk_pension_type, 0 as fk_prog_code
	FROM tfp_accounting_data a
	JOIN @acc_table ac ON a.fk_account_code = ac.fk_account_code
	WHERE fk_tenant_id = @fk_tenant_id
	AND period <= @forecast_period
	AND gl_year = @budget_year
	GROUP BY fk_tenant_id, a.fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, period
	ORDER BY fk_tenant_id, a.fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, period;

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,0 AS fk_prog_code
	FROM tbu_forecast_transactions
	WHERE fk_tenant_id = @fk_tenant_id
	AND budget_year = @budget_year
	AND forecast_period = @prev_forecast_period
	AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
	--AND fk_employment_id = 0
	--AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
	GROUP BY fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, budget_type, period, tax_flag,holiday_flag,fk_pension_type

	END
	

	IF @init_type = 'SalPerBud'
	BEGIN

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID(), @forecast_period, '********-0000-0000-0000-********0000' as bu_trans_id,fk_tenant_id,5,0,a.fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 as fk_employment_id,'' AS description,gl_year as budget_year,period
	,1 as budget_type
	,sum(amount) as amount_year_1,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount) as total_amount,GETDATE(),@user_id as updated_by,0 as tax_flag,0 as holiday_flag,0 as fk_pension_type, 0 as fk_prog_code
	FROM tfp_accounting_data a
	WHERE fk_tenant_id = @fk_tenant_id
	AND period <= @forecast_period
	AND gl_year = @budget_year
	GROUP BY fk_tenant_id, a.fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, period
	ORDER BY fk_tenant_id, a.fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, period;

	INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
	SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
	FROM tbu_trans_detail a
	 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.budget_year = @budget_year
	AND a.period > @forecast_period
	AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
	GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;

	END

	IF @init_type = 'LastForecast'

			BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,0 AS fk_prog_code
			FROM tbu_forecast_transactions
			WHERE fk_tenant_id = @fk_tenant_id
			AND budget_year = @budget_year
			AND forecast_period = @prev_forecast_period
			--AND fk_employment_id = 0
			--AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
			GROUP BY fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, budget_type, period, tax_flag,holiday_flag,fk_pension_type


			END 


			IF @init_type = 'LastForecastPer'

			BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,0 AS fk_prog_code
			FROM tbu_forecast_transactions
			WHERE fk_tenant_id = @fk_tenant_id
			AND period > @forecast_period
			AND budget_year = @budget_year
			AND forecast_period = @prev_forecast_period
			AND fk_employment_id = 0
			--AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
			GROUP BY fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, budget_type, period, tax_flag,holiday_flag,fk_pension_type

			PRINT 'End fetching last forecast - transactions with per allocation - init type LastForecastPer'  + convert(varchar(400),GETDATE())

			END

			IF @init_type = 'LastForecastAcc'

			BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,fk_tenant_id,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,''  AS description,YEAR AS budget_year,@last_period as period,budget_type,SUM(budget_amount) - SUM(gl_amount),0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(budget_amount) - SUM(gl_amount) as total_amount,GETDATE(),@user_id as updated_by,tax_flag,holiday_flag,fk_pension_type,0 as fk_prog_code
			FROM
			(SELECT ac.fk_tenant_id, ac.fk_account_code, department_code, fk_function_code, '' as fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year as year, 1 as budget_type, sum(amount) as gl_amount, 0 as budget_amount,ISNULL(tax_flag, 0) as tax_flag,ISNULL(sa.holiday_flag, 0) as holiday_flag,ISNULL(sa.fk_pension_type,'') as fk_pension_type
			FROM tfp_accounting_data ac
			LEFT JOIN tmd_salary_acc_def sa ON ac.fk_tenant_id = sa.fk_tenant_id AND ac.fk_account_code = sa.fk_account_code
			WHERE ac.fk_tenant_id = @fk_tenant_id
			AND period <= @forecast_period
			AND gl_year = @budget_year
			GROUP BY ac.fk_tenant_id, ac.fk_account_code, department_code, fk_function_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, sa.tax_flag, sa.holiday_flag, sa.fk_pension_type
			UNION ALL
			SELECT fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, budget_year as year, budget_type, 0 as gl_amount, SUM(amount_year_1) as budget_amount, tax_flag,holiday_flag,fk_pension_type
			FROM tbu_forecast_transactions
			WHERE fk_tenant_id = @fk_tenant_id
			AND budget_year = @budget_year
			AND forecast_period = @prev_forecast_period
			AND fk_employment_id = 0
			--AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
			GROUP BY fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, budget_year, budget_type,tax_flag,holiday_flag,fk_pension_type) A
			GROUP BY fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, year, budget_type, tax_flag,holiday_flag,fk_pension_type
			HAVING SUM(budget_amount) - SUM(gl_amount) != 0

			PRINT 'End fetching last forecast - transactions without per allocation - init_type LastForecastAcc'  + convert(varchar(400),GETDATE());

			END


	/*
	BEGIN

			INSERT INTO @forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,fk_tenant_id,5,0,fk_account_code,department_code,@dummy_function AS  fk_function_code,'' AS fk_project_code,'' AS free_dim_1,'' AS free_dim_2,'' AS free_dim_3,'' AS free_dim_4,'' AS resource_id,0 AS fk_employment_id,''  AS description,YEAR AS budget_year,@last_period as period,1 AS budget_type,SUM(budget_amount) - SUM(gl_amount),0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(budget_amount) - SUM(gl_amount) as total_amount,GETDATE(),@user_id as updated_by,0 as tax_flag,0 as holiday_flag,0 as fk_pension_type,0 as fk_prog_code
			FROM
			(SELECT fk_tenant_id, fk_account_code, department_code, gl_year as year, sum(amount) as gl_amount, 0 as budget_amount
			FROM tfp_accounting_data
			WHERE fk_tenant_id = @fk_tenant_id
			AND period <= @forecast_period
			AND gl_year = @budget_year
			GROUP BY fk_tenant_id, fk_account_code, department_code, gl_year
			UNION ALL
			SELECT fk_tenant_id, fk_account_code, department_code, budget_year as year, 0 as gl_amount, SUM(amount_year_1) as budget_amount
			FROM tbu_forecast_transactions
			WHERE fk_tenant_id = @fk_tenant_id
			AND budget_year = @budget_year
			AND forecast_period = @forecast_period - 1
			AND fk_employment_id = 0
			AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
			GROUP BY fk_tenant_id, fk_account_code, department_code, budget_year) A
			GROUP BY fk_tenant_id, fk_account_code, department_code, year
			HAVING SUM(budget_amount) != 0

			PRINT 'End fetching last forecast - transactions without per allocation and with dummy function'

	END
	*/

		IF @init_type = 'PerBud' AND @not_use_fr_budget = 0 AND @not_use_salary_forecast = 0 

		-- man bruker både lønnsbudsjettering i framsikt og lønnsprognose ?
			BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
			FROM tbu_trans_detail a
			JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
			WHERE a.fk_tenant_id = @fk_tenant_id
			AND period > @forecast_period
			AND a.budget_year = @budget_year
			AND fk_employment_id = 0
			--AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
			GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;

			PRINT 'End fetching Budget - transactions with per allocation' +  + convert(varchar(400),GETDATE());

		END

		IF @init_type = 'PerBud' AND @not_use_salary_forecast != 0
		
		-- man bruker ikke lønnsprognose

			BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
			FROM tbu_trans_detail a
			JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
			WHERE a.fk_tenant_id = @fk_tenant_id
			AND period > @forecast_period
			AND a.budget_year = @budget_year
			--AND fk_employment_id = 0
			--AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
			GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;

			PRINT 'End fetching Budget - transactions with per allocation'  + convert(varchar(400),GETDATE());

		END

		IF @init_type = 'PerBud' AND @not_use_fr_budget != 0  AND @not_use_salary_forecast = 0
			
			-- Man bruker ikke lønnsbudsjett, men bruker lønnsprognose.

			BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
			FROM tbu_trans_detail a
			 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
			WHERE a.fk_tenant_id = @fk_tenant_id
			AND period > @forecast_period
			AND a.budget_year = @budget_year
			--AND fk_employment_id = 0
			AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
			GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;

			PRINT 'End fetching Budget - transactions with per allocation ' + convert(varchar(400),GETDATE());

			END


		IF @init_type = 'YearlyBudget'
		BEGIN

		INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
		SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
		FROM tbu_trans_detail a
		 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.budget_year = @budget_year
		GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, a.budget_year, budget_type, period, tax_flag,holiday_flag,fk_pension_type;

		PRINT 'End fetching Budget - transactions with init type Yearly budget 1 ' + convert(varchar(400),GETDATE());

		END

		-- New logic for YearBudUnappr

		IF @init_type = 'YearBudUnappr'
		BEGIN

		INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
		SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
		FROM tbu_trans_detail a
		JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.budget_year = @budget_year
		GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, a.budget_year, budget_type, period, tax_flag,holiday_flag,fk_pension_type;

		PRINT 'End fetching Budget - transactions with init type Yearly budget 1 ' + convert(varchar(400),GETDATE());

		PRINT 'Insert unapproved budget_changes from operations'

		INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
		SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,
		d.fk_tenant_id,5 AS action_type,0 AS line_order,d.fk_account_code,d.department_code,
		d.function_code,d.project_code,d.free_dim_1,d.free_dim_2,d.free_dim_3,d.free_dim_4,
		'' AS resource_id,0 AS fk_employment_id,'' AS description,d.budget_year,@forecast_period AS period,1 as budget_type,
		SUM(d.year_1_amount),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,
		0 AS allocation_pct,SUM(d.year_1_amount) AS total_amount,
		GETDATE() AS updated,@user_id AS updated_by,0 as tax_flag,0 as holiday_flag,
		'' as fk_pension_type,'' AS fk_prog_code
		FROM tfp_trans_header h
		JOIN tfp_trans_detail d ON h.fk_tenant_id = d.fk_tenant_id AND h.pk_action_id = d.fk_action_id
		JOIN tco_user_adjustment_codes uac ON d.fk_tenant_id = uac.fk_tenant_id AND d.fk_adj_code = uac.pk_adj_code AND uac.status = 0 AND uac.include_in_calculation = 1
		WHERE d.fk_tenant_id = @fk_tenant_id AND d.budget_year = @budget_year
		GROUP BY d.fk_tenant_id,d.fk_account_code,d.department_code,
		d.function_code,d.project_code,d.free_dim_1,d.free_dim_2,d.free_dim_3,d.free_dim_4
		,d.budget_year


		END

		IF @init_type = 'BudRemaining' AND @not_use_fr_budget = 0 AND @not_use_salary_forecast = 0 
		BEGIN

		INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
		SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,''  AS description, a.budget_year,@last_period as period,budget_type, SUM(amount_year_1)*@month_remaining,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount_year_1)*@month_remaining as total_amount,GETDATE(),@user_id as updated_by,tax_flag,holiday_flag,fk_pension_type,'' as fk_prog_code
		FROM tbu_trans_detail a
		 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.budget_year = @budget_year
		AND fk_employment_id = 0
		--AND fk_account_code NOT IN (SELECT acc_value FROM tmd_acc_defaults WHERE fk_tenant_id = @fk_tenant_id AND link_type = 'STAFF_PLANNING' AND acc_type = 'ACCOUNT' AND module = 'BU')
		GROUP BY a.fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.budget_year, budget_type,tax_flag,holiday_flag,fk_pension_type
		HAVING SUM(amount_year_1) != 0;

		PRINT 'End fetching Budget - transactions without per allocation multiplied by ' + CONVERT(varchar(25), @month_remaining)

		END

		IF @init_type = 'BudRemaining' AND @not_use_fr_budget != 0 AND @not_use_salary_forecast != 0
		BEGIN

		INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
		SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,''  AS description, a.budget_year,@last_period as period,budget_type, SUM(amount_year_1)*@month_remaining,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount_year_1)*@month_remaining as total_amount,GETDATE(),@user_id as updated_by,tax_flag,holiday_flag,fk_pension_type,'' as fk_prog_code
		FROM tbu_trans_detail a
		 JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.budget_year = @budget_year
		--AND fk_employment_id = 0
		--AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
		GROUP BY a.fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.budget_year, budget_type,tax_flag,holiday_flag,fk_pension_type
		HAVING SUM(amount_year_1) != 0;

		PRINT 'End fetching Budget - transactions without per allocation multiplied by ' + CONVERT(varchar(25), @month_remaining)  + convert(varchar(400),GETDATE());

		END

	
		IF @init_type = 'BudRemaining' AND @not_use_fr_budget != 0 AND @not_use_salary_forecast = 0
		BEGIN

		INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
		SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,''  AS description, a.budget_year,@last_period as period,budget_type, SUM(amount_year_1)*@month_remaining,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,0 as fk_key_id,0 as allocation_pct,SUM(amount_year_1)*@month_remaining as total_amount,GETDATE(),@user_id as updated_by,tax_flag,holiday_flag,fk_pension_type,'' as fk_prog_code
		FROM tbu_trans_detail a
		JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id AND a.budget_year = ud.budget_year AND ud.status = 1 
		WHERE a.fk_tenant_id = @fk_tenant_id
		AND a.budget_year = @budget_year
		--AND fk_employment_id = 0
		AND fk_account_code NOT IN (SELECT fk_account_code FROM @acc_table)
		GROUP BY a.fk_tenant_id, fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.budget_year, budget_type,tax_flag,holiday_flag,fk_pension_type
		HAVING SUM(amount_year_1) != 0;

		PRINT 'End fetching Budget - transactions without per allocation multiplied by ' + CONVERT(varchar(25), @month_remaining) + convert(varchar(400),GETDATE());

		END

	BEGIN


	IF @init_type = 'ForecastZero'
	BEGIN

			INSERT INTO #forecast_table (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code)
			SELECT NEWID() AS pk_id,@forecast_period,'********-0000-0000-0000-********0000' AS bu_trans_id,a.fk_tenant_id,5 AS action_type,0 AS line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,'' AS resource_id,0 AS fk_employment_id,'' AS description,a.budget_year,period,budget_type,SUM(amount_year_1),0 AS amount_year_2,0 AS amount_year_3,0 AS amount_year_4,0 AS fk_key_id,0 AS allocation_pct,SUM(amount_year_1) AS total_amount,GETDATE() AS updated,@user_id AS updated_by,tax_flag,holiday_flag,fk_pension_type,'' AS fk_prog_code
			FROM tbu_trans_detail a
			WHERE a.fk_tenant_id = @fk_tenant_id
			AND period > @forecast_period
			AND a.budget_year = @budget_year
			GROUP BY a.fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,a.budget_year, budget_type, period,tax_flag,holiday_flag,fk_pension_type;

			UPDATE #forecast_table SET amount_year_1 = 0, amount_year_2 = 0, amount_year_3 = 0, amount_year_4 = 0, total_amount = 0

	END

	IF @init_type NOT IN  ('AccSal','SalLastforecast','SalPerBud')
	BEGIN

	UPDATE #forecast_table SET  tax_flag = b.tax_flag, holiday_flag = b.holiday_flag, fk_pension_type = b.fk_pension_type
	FROM #forecast_table A, [tmd_salary_acc_def] B
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.budget_year = @budget_year
	AND a.forecast_period = @forecast_period
	--AND a.bu_trans_id = '********-0000-0000-0000-********0000'
	AND a.fk_tenant_id = B.fk_tenant_id
	AND a.fk_account_code = B.fk_account_code;

	END

	IF @init_type IN  ('AccSal','SalLastforecast','SalPerBud')
	BEGIN

	UPDATE #forecast_table SET  tax_flag = b.tax_flag, holiday_flag = b.holiday_flag, fk_pension_type = b.fk_pension_type
	FROM #forecast_table A, [tmd_salary_acc_def] B
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.budget_year = @budget_year
	AND a.forecast_period = @forecast_period
	--AND a.bu_trans_id = '********-0000-0000-0000-********0000'
	AND a.fk_tenant_id = B.fk_tenant_id
	AND a.fk_account_code = B.fk_account_code
	AND a.period <= @forecast_period
	AND a.fk_account_code IN (SELECT fk_account_code FROM @acc_table);

	END

	UPDATE #forecast_table SET budget_type = 1 WHERE budget_type BETWEEN 10 AND 20 AND forecast_period = @forecast_period AND fk_tenant_id = @fk_tenant_id;


	PRINT 'End setting tax_flags, budget type etc ' + convert(varchar(400),GETDATE());


	INSERT INTO @trans_id_table (bu_trans_id,fk_tenant_id,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,budget_type,tax_flag,holiday_flag,fk_pension_type, forecast_period, total_amount)
	SELECT NEWID() AS bu_trans_id, fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_type, tax_flag,holiday_flag,fk_pension_type,forecast_period, SUM(total_amount) as total_amount
	FROM #forecast_table
	WHERE fk_tenant_id = @fk_tenant_id
	AND budget_year = @budget_year
	AND forecast_period = @forecast_period
	AND bu_trans_id = '********-0000-0000-0000-********0000'
	GROUP BY fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_type, tax_flag,holiday_flag,fk_pension_type,forecast_period
	ORDER BY fk_tenant_id, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4;



	UPDATE #forecast_table SET bu_trans_id = b.bu_trans_id, total_amount = b.total_amount
	FROM #forecast_table A, @trans_id_table B
	WHERE a.fk_tenant_id = @fk_tenant_id
	AND a.budget_year = @budget_year
	AND a.forecast_period = @forecast_period
	AND a.bu_trans_id = '********-0000-0000-0000-********0000'
	AND a.forecast_period = B.forecast_period
	AND a.fk_tenant_id = B.fk_tenant_id
	AND a.fk_account_code = B.fk_account_code
	AND a.department_code = B.department_code
	AND a.fk_function_code = B.fk_function_code
	AND a.fk_project_code = B.fk_project_code 
	AND a.free_dim_1 = B.free_dim_1
	AND a.free_dim_2 = B.free_dim_2
	AND a.free_dim_3 = B.free_dim_3
	AND a.free_dim_4 = B.free_dim_4
	AND a.budget_type = B.budget_type
	AND a.tax_flag = B.tax_flag
	AND a.holiday_flag = B.holiday_flag
	AND a.fk_pension_type = B.fk_pension_type



	PRINT 'Updated bu_trans_id ' + convert(varchar(400),GETDATE());
	END

	PRINT 'Remove financing rows ' + convert(varchar(400),GETDATE());

	UPDATE #forecast_table SET budget_type = 1000
	FROM #forecast_table a, tco_accounts ac, gco_kostra_accounts k
	WHERE a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code 
	AND ac.fk_kostra_account_code = k.pk_kostra_account_code
	AND k.type = 'investment' 

	DELETE FROM #forecast_table WHERE budget_type = 1000;

	PRINT 'Remove financing rows ' + convert(varchar(400),GETDATE());

	Print 'Logic for fetching 1A status start ' + convert(varchar(400),GETDATE());

	IF @forecast_period >= 202000 AND (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') > 0

	BEGIN
	print 'New logic for 1a 2020'

	UPDATE #forecast_table SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
	FROM  @tbl_reporting_line c 
	LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
	LEFT OUTER JOIN  #forecast_table a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
	AND a.fk_tenant_id = b.pk_tenant_id
	AND b.fk_kostra_account_code = c.fk_kostra_account_code
	AND a.department_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_DEPARTMENTS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
	AND c.line_group_id = 10
	WHERE  c.report = @report1A 

	UPDATE #forecast_table SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
	FROM  @tbl_reporting_line c 
	LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
	LEFT OUTER JOIN  #forecast_table a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
	AND a.fk_tenant_id = b.pk_tenant_id
	AND b.fk_kostra_account_code = c.fk_kostra_account_code
	AND a.fk_function_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
	AND c.line_group_id = 10
	WHERE  c.report = @report1A 


	UPDATE #forecast_table SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
	FROM  @tbl_reporting_line c 
	LEFT OUTER JOIN tco_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
	LEFT OUTER JOIN  #forecast_table a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id AND a.forecast_period/100 BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
	AND a.fk_tenant_id = b.pk_tenant_id
	AND b.fk_kostra_account_code = c.fk_kostra_account_code
	AND c.line_group_id != 10
	WHERE  c.report = @report1A 

	
	UPDATE a SET a.[1A_line] = 0, a.line_group_id = 0, a.line_group = '', a.line_item_id=0, a.line_item = ''
	FROM #forecast_table a 
	JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
	JOIN #rep_line_1B rl ON rl.report = @report1B AND rl.fk_kostra_account_code = ac.fk_kostra_account_code
	WHERE a.[1A_line] = 1 
	AND a.department_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
	AND a.fk_function_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_FUNCTIONS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
	AND a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period



	END

	IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE fk_tenant_id = @fk_tenant_id AND param_name = 'MR_SCREEN_USE_1A' AND param_value = 'TRUE') = 0 

	BEGIN 

		UPDATE #forecast_table
		SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
		FROM #forecast_table imp, tmd_finplan_line_setup ls
		WHERE imp.fk_account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 0;

		UPDATE #forecast_table
		SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
		FROM #forecast_table imp, tmd_finplan_line_setup ls
		WHERE imp.fk_account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 5;	
	
		UPDATE #forecast_table
		SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
		FROM #forecast_table imp, tmd_finplan_line_setup ls
		WHERE imp.fk_account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 4;	
	
		UPDATE #forecast_table
		SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
		FROM #forecast_table imp, tmd_finplan_line_setup ls
		WHERE imp.fk_account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 3;	
	
		UPDATE #forecast_table
		SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
		FROM #forecast_table imp, tmd_finplan_line_setup ls
		WHERE imp.fk_account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 2;	
	
		UPDATE #forecast_table
		SET [1A_line] = 1, line_group_id = ls.action_type, line_item_id = ls.line_order, line_item = ls.action_name
		FROM #forecast_table imp, tmd_finplan_line_setup ls
		WHERE imp.fk_account_code = ls.fk_account_code
		AND imp.department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
		AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
		AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
		AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
		AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
		AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
		AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
		AND imp.fk_tenant_id = ls.fk_tenant_id
		AND imp.budget_year = ls.budget_year
		AND ls.priority = 1;


	UPDATE #forecast_table set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
	JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
	LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
	WHERE ls.context = 'Common'
	AND ls.language = @language
	AND ls.Id = 'revenues_text'
	AND t.pk_id = @fk_tenant_id)
	WHERE [1A_line] = 1 AND line_group_id = 1

	UPDATE #forecast_table set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
	JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
	LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
	WHERE ls.context = 'Common'
	AND ls.language = @language
	AND ls.Id = 'centralexpenses_text'
	AND t.pk_id = @fk_tenant_id)
	WHERE [1A_line] = 1 AND line_group_id = 100

	UPDATE #forecast_table set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
	JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
	LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
	WHERE ls.context = 'Common'
	AND ls.language = @language
	AND ls.Id = 'financial_income_expenses_text'
	AND t.pk_id = @fk_tenant_id)
	WHERE [1A_line] = 1 AND line_group_id = 2

	UPDATE #forecast_table set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
	JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
	LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
	WHERE ls.context = 'Common'
	AND ls.language = @language
	AND ls.Id = 'provisions_others_text'
	AND t.pk_id = @fk_tenant_id)
	WHERE [1A_line] = 1 AND line_group_id = 3

	UPDATE #forecast_table set line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
	JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
	LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
	WHERE 
	ls.context  = 'BudgetManagement'
	AND ls.language = @language
	AND ls.Id = 'bm_transfered_to_investments_text'
	AND t.pk_id = @fk_tenant_id)
	WHERE [1A_line] = 1 AND line_group_id = 4

	UPDATE #forecast_table SET line_group_id = line_group_id * 1000
	WHERE [1A_line] = 1 AND line_group_id IN (2,3,4);


	END

	Print 'Logic for fetching 1A status finish ' + convert(varchar(400),GETDATE());


	IF @update_only = 0 
	BEGIN

	BEGIN
	SET  @continue = 1

	WHILE @continue = 1
	BEGIN
		PRINT GETDATE()
		SET ROWCOUNT 50000
		BEGIN TRANSACTION
		
		DELETE a FROM tbu_forecast_transactions a
		JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
		JOIN gco_kostra_accounts ka on ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'
		WHERE a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period;
		
		SET @rowcount = @@rowcount 
		COMMIT
		PRINT GETDATE()
		IF @rowcount = 0
		BEGIN
			SET @continue = 0
		END
	END

	END

	END

	IF @update_only = 1
	BEGIN

	BEGIN
	SET  @continue = 1

	WHILE @continue = 1
	BEGIN
		PRINT GETDATE()
		SET ROWCOUNT 50000
		BEGIN TRANSACTION
		
		DELETE a FROM tbu_forecast_transactions a
		JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.forecast_period/100 BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) 
		JOIN gco_kostra_accounts ka on ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'
		WHERE a.fk_tenant_id = @fk_tenant_id AND a.forecast_period = @forecast_period AND budget_type NOT IN (10,11,12,13,14,15,16,17,18,19);
		
		SET @rowcount = @@rowcount 
		COMMIT
		PRINT GETDATE()
		IF @rowcount = 0
		BEGIN
			SET @continue = 0
		END
	END

	END

	END

	SET ROWCOUNT 0;

	DELETE FROM tco_application_flag
	WHERE flag_name LIKE 'MONREP_FS_STAT%'
	AND fk_tenant_id = @fk_tenant_id
	AND period = @forecast_period;


	PRINT 'Deleting ' + convert(varchar(400),GETDATE());


	INSERT INTO [tbu_forecast_transactions] (pk_id,forecast_period,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,allocation_pct,total_amount,updated,updated_by,tax_flag,holiday_flag,fk_pension_type,fk_prog_code,
	[1A_line],line_group_id, line_group, line_item_id, line_item, fk_alter_code)
	SELECT newid(),f.forecast_period,f.bu_trans_id,f.fk_tenant_id,f.action_type,f.line_order,trim(f.fk_account_code),
	trim(f.department_code),trim(f.fk_function_code),trim(f.fk_project_code),
	trim(f.free_dim_1),trim(f.free_dim_2),trim(f.free_dim_3),trim(f.free_dim_4),
	f.resource_id,f.fk_employment_id,f.description,f.budget_year,f.period,f.budget_type,SUM(f.amount_year_1),SUM(f.amount_year_2),SUM(f.amount_year_3),SUM(f.amount_year_4),f.fk_key_id,f.allocation_pct,ROUND(f.total_amount,0),getdate(),f.updated_by,f.tax_flag,f.holiday_flag,f.fk_pension_type,f.fk_prog_code,
	[1A_line],line_group_id, line_group, line_item_id, line_item, '' as fk_alter_code
	FROM #forecast_table f
	GROUP BY f.forecast_period,f.bu_trans_id,f.fk_tenant_id,f.action_type,f.line_order,f.fk_account_code,f.department_code,f.fk_function_code,f.fk_project_code,f.free_dim_1,f.free_dim_2,f.free_dim_3,f.free_dim_4,f.resource_id,f.fk_employment_id,f.description,f.budget_year,f.period,f.budget_type,f.fk_key_id,f.allocation_pct,f.total_amount,f.updated_by,f.tax_flag,f.holiday_flag,f.fk_pension_type,f.fk_prog_code,
	[1A_line],line_group_id, line_group, line_item_id, line_item
	;


	PRINT 'Updating tbu_forecast_transactions ' + convert(varchar(400),GETDATE());

	UPDATE tbu_employments SET forecast_change_flag = 1 WHERE budget_year = @budget_year AND fk_tenant_id = @fk_tenant_id;

	PRINT 'Updating tbu_employments ' + convert(varchar(400),GETDATE());

	

	IF (SELECT count(*) FROM #forecast_table a, tco_accounts ac, gco_kostra_accounts k
	WHERE a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code 
	AND ac.fk_kostra_account_code = k.pk_kostra_account_code
	AND k.type = 'investment') = 0 AND @update_only != 1

	BEGIN 




	PRINT 'START: Copy deviation actions ' + convert(varchar(400),GETDATE());

	BEGIN

	DELETE FROM tmr_deviation_report 
	WHERE fk_tenant_id = @fk_tenant_id
	AND forecast_period = @forecast_period;

	INSERT INTO tmr_deviation_report (pk_deviation_action_id,fk_tenant_id,deviation_action_name,forecast_period,fk_account_code,fk_department_code,fk_function_code,start_date,risk,fk_deviation_type,yearly_effect_amount,remaining_amount,description,updated,updated_by, period_created)
	SELECT NEWID(),fk_tenant_id,deviation_action_name,@forecast_period,fk_account_code,fk_department_code,fk_function_code,start_date,risk,fk_deviation_type,yearly_effect_amount,remaining_amount,description,updated,updated_by, period_created
	FROM tmr_deviation_report
	WHERE fk_tenant_id = @fk_tenant_id
	AND forecast_period = @prev_forecast_period
	AND forecast_period/100 = @budget_year

	END

END

	PRINT 'FINISH: Copy deviation actions ' + convert(varchar(400),GETDATE());

		IF @assignment_flag > 0
	begin

	PRINT 'Start Copy 1A descriptions from prev period'

	
		INSERT INTO tco_monthrep_descriptions(fk_tenant_id, budget_year, forecast_Period, org_id, org_level, description_type,
		service_id, service_id_new, acc_group_value, description_text, description_history, fk_attribute_id, fk_attribute_id_new,
		updated, updated_by)
			SELECT fk_tenant_id, budget_year,
			@forecast_period AS forecast_period, org_id, org_level, description_type, 
			service_id, service_id_new, acc_group_value, description_text, 
			NEWID() AS description_history, fk_attribute_id, fk_attribute_id_new,
			GETDATE() AS updated, updated_by
			FROM tco_monthrep_descriptions monthRepDesc
			WHERE monthRepDesc.fk_tenant_id = @fk_tenant_id
			AND   monthRepDesc.forecast_period = @prev_forecast_period
			AND   monthRepDesc.description_type = 'MROverviewLineGroupText'
			AND   monthRepDesc.acc_group_value LIKE '1A%'
			AND   monthRepDesc.description_text IS NOT NULL
			AND   NOT EXISTS (
				SELECT * FROM tco_monthrep_descriptions monthRepDescDup
				WHERE monthRepDesc.fk_tenant_id = monthRepDescDup.fk_tenant_id
				AND monthRepDesc.forecast_period = @forecast_period
				AND monthRepDesc.acc_group_value = monthRepDescDup.acc_group_value
				AND   monthRepDesc.description_type = 'MROverviewLineGroupText'
			)

	PRINT 'Finished Copy 1A descriptions from prev period'

	end

END




DROP TABLE IF EXISTS #checklist_items
DROP TABLE IF EXISTS #forecast_table
DROP TABLE IF EXISTS #temp_blank_financing_lines
DROP TABLE if exists #TEMP_ACC_DATA
DROP TABLE if exists #ACC_DATA

RETURN 0




GO