CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedDimensionAccountData]
	@tenant_id int,
	@user_id int,
	@dimension_type_id int,
	@job_id bigint
AS 
	IF( @job_id = -1 )
		BEGIN
			UPDATE tbu_stage_dimensions_import
			SET project_code_error = 0,
				project_name_error = 0,
				year_from_error = 0,
				year_to_error = 0,
				free_dim_1_error = 0,
				free_dim_2_error = 0,
				free_dim_3_error = 0,
				free_dim_4_error = 0,
				main_project_code_error = 0,
				vat_rate_error = 0,
				vat_refund_error = 0,
				inv_status_error=0,
				start_year_error=0,
				completion_date_error=0,
				original_finish_year_error=0,
				fk_department_code_error=0,
				fk_Function_code_error=0,
				status_error=0,
				error_count = 0,
				account_name_error=0,
				account_code_error=0,
				--account_desc_error=0,
				kostra_account_error=0,
				account_year_from_error=0,
				account_year_To_error=0,
				account_isActive_error=0		
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id;  
		END
	ELSE
		BEGIN
			UPDATE tbu_stage_dimensions_import
			SET project_code_error = 0,
				project_name_error = 0,
				year_from_error = 0,
				year_to_error = 0,
				free_dim_1_error = 0,
				free_dim_2_error = 0,
				free_dim_3_error = 0,
				free_dim_4_error = 0,
				main_project_code_error = 0,
				vat_rate_error = 0,
				vat_refund_error = 0,
				inv_status_error=0,
				start_year_error=0,
				completion_date_error=0,
				original_finish_year_error=0,
				fk_department_code_error=0,
				fk_Function_code_error=0,
				status_error=0,
				error_count = 0,
				account_name_error=0,
				account_code_error=0,
				--account_desc_error=0,
				kostra_account_error=0,
				account_year_from_error=0,
				account_year_To_error=0,
				account_isActive_error=0		
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id;
	END

		DECLARE @pkIdTable TABLE(pkIds int)

		-- Validate account code
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_code_error = account_code_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_code is NULL OR account_code = '');
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_code_error = account_code_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (account_code is NULL OR account_code = ''); 
		  END

		-- Validate account name
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_name_error = account_name_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_name is NULL OR account_name = '');
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_name_error = account_name_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (account_name is NULL OR account_name = ''); 
		  END 

		-- Validate account desc
			--update tbu_stage_dimensions_import SET account_desc_error = account_desc_error + 1, error_count = error_count + 1 
			--WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_desc is NULL OR account_desc = '');

		-- Validate kostra code
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET kostra_account_error = kostra_account_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (kostra_account_code is NULL OR kostra_account_code = '');
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET kostra_account_error = kostra_account_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (kostra_account_code is NULL OR kostra_account_code = '');
		  END   

		-- Validate isActive
--	update tbu_stage_dimensions_import SET account_isActive_error = account_isActive_error + 1, error_count = error_count + 1 
	--	WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_isActive is NULL OR account_isActive = ''); 
		
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_isActive_error = account_isActive_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_isActive not in (0,1));
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_isActive_error = account_isActive_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (account_isActive not in (0,1));
		  END 
	
		-- Validate Year from NULL CHECK
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_from_error = account_year_from_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_year_from is NULL OR account_year_from = '' OR  (account_year_from)>(account_year_To));
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_from_error = account_year_from_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (account_year_from is NULL OR account_year_from = '' OR  (account_year_from)>(account_year_To));
		  END 

		-- Validate Year to NULL CHECK 
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_To_error = account_year_To_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_year_To is NULL OR account_year_To = '' OR account_year_To =0 OR (account_year_from)>(account_year_To));
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_To_error = account_year_To_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (account_year_To is NULL OR account_year_To = '' OR account_year_To =0 OR (account_year_from)>(account_year_To));
		  END

		
		-- Validate length of the year
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_To_error = account_year_To_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND (account_year_To is NULL OR account_year_To = '' OR account_year_To =0 OR (account_year_from)>(account_year_To));
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_To_error = account_year_To_error + 1, error_count = error_count + 1 
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND (account_year_To is NULL OR account_year_To = '' OR account_year_To =0 OR (account_year_from)>(account_year_To));
		  END

		-- Validate length of the year
		IF (@job_id = -1)
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_To_error = account_year_To_error + 1, error_count = error_count + 1
			WHERE  fk_tenant_id = @tenant_id AND [user_id] = @user_id AND dimension_type = @dimension_type_id AND  LEN(account_year_To) < 4;
		  END
	    ELSE
		  BEGIN
			update tbu_stage_dimensions_import SET account_year_To_error = account_year_To_error + 1, error_count = error_count + 1
			WHERE  fk_tenant_id = @tenant_id AND [job_id] = @job_id AND dimension_type = @dimension_type_id AND  LEN(account_year_To) < 4;
		  END

	
			--#87111
			--UPDATE tbu_stage_dimensions_import
			--SET account_code_error =account_code_error + 1,account_year_from_error =account_year_from_error + 1,account_year_To_error =account_year_To_error + 1, error_count = error_count + 1 
			--FROM tbu_stage_dimensions_import 
			--JOIN tco_accounts acc on tbu_stage_dimensions_import.account_code = acc.pk_account_code AND 
			--tbu_stage_dimensions_import.account_name= acc.display_name AND
			--CAST(CASt(tbu_stage_dimensions_import.account_year_from as varchar(8)) + '-01-01'  AS DATE)  = acc.dateFrom AND 
			--CAST(CASt(tbu_stage_dimensions_import.account_year_To as varchar(8)) + '-12-31'  AS DATE)  = acc.dateTo AND tbu_stage_dimensions_import.fk_tenant_id = acc.pk_tenant_id AND acc.isActive=1
			--WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_type_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND 
			--LEN(tbu_stage_dimensions_import.account_year_from) = 4
			--AND  LEN(tbu_stage_dimensions_import.account_year_To) = 4
			--OR ((tbu_stage_dimensions_import.account_year_from)< (tbu_stage_dimensions_import.account_year_To))


		IF (@job_id = -1)
		  BEGIN
			UPDATE tbu_stage_dimensions_import
			SET kostra_account_error =kostra_account_error + 1, error_count = error_count + 1		
			WHERE tbu_stage_dimensions_import.user_id = @user_id AND dimension_type = @dimension_type_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND 
			LEN(tbu_stage_dimensions_import.account_year_from) = 4
			AND  LEN(tbu_stage_dimensions_import.account_year_To) = 4
			AND tbu_stage_dimensions_import.kostra_account_code not in (select pk_kostra_account_code from gco_kostra_accounts)
		  END
	    ELSE
		  BEGIN
			UPDATE tbu_stage_dimensions_import
			SET kostra_account_error =kostra_account_error + 1, error_count = error_count + 1		
			WHERE tbu_stage_dimensions_import.job_Id = @job_id AND dimension_type = @dimension_type_id AND tbu_stage_dimensions_import.fk_tenant_id = @tenant_id AND 
			LEN(tbu_stage_dimensions_import.account_year_from) = 4
			AND  LEN(tbu_stage_dimensions_import.account_year_To) = 4
			AND tbu_stage_dimensions_import.kostra_account_code not in (select pk_kostra_account_code from gco_kostra_accounts)
		  END
			
RETURN 0