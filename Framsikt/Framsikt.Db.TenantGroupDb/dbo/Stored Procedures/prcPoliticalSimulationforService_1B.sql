 CREATE OR ALTER PROCEDURE [dbo].[prcPoliticalSimulationforService_1B]
	@tenant_id int,
	@budget_year int,
	@user_id int,
	@proposal_id UNIQUEIDENTIFIER 

AS

DECLARE @incluce_blist BIT = (SELECT include_blist from  [tps_admin_config] WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year)


BEGIN		
		--DECLARE @tenant_id int =31, @budget_year int =2018, @user_id int =31999, @proposal_id UNIQUEIDENTIFIER=newid()  SELECT @proposal_id	

		select ch.pk_change_id as changeids into #tempChangeIds from [dbo].[tco_budget_phase] ph
		join tfp_budget_changes ch  
		on ph.pk_budget_phase_id = ch.fk_budget_phase_id  AND ph.fk_tenant_id = ch.fk_tenant_id 
		where ph.fk_tenant_id=@tenant_id  and ph.council_sugg_flag=1 and ch.budget_year = @budget_year 

		Select oh.fk_department_code,oh.fk_tenant_id,oh.fk_org_version
		,oh.org_id_1,oh.org_id_2,oh.org_id_3,oh.org_id_4,oh.org_id_5
		,oh.org_name_1,oh.org_name_2,oh.org_name_3,oh.org_name_4,oh.org_name_5   into #temphierarchyService
		from tco_org_hierarchy oh with(nolock)  
		LEFT JOIN tco_org_version ov with(nolock) ON oh.fk_tenant_id = ov.fk_Tenant_id and (@budget_year)*100+1 between ov.period_from and ov.period_to
		WHERE oh.fk_tenant_id =@tenant_id 
		 

		DECLARE @temp_table_tth_service TABLE 
		([fk_tenant_id] INT NOT NULL, [account_code] NVARCHAR(25) NOT NULL, [department_code] NVARCHAR(25) NOT NULL, [function_code] NVARCHAR(25) NOT NULL,
		[fk_kostra_account_code] NVARCHAR(25) NOT NULL,[gl_amount] DECIMAL(18, 2) NOT NULL, [budget_amount] DECIMAL(18, 2) NOT NULL, [year_1_amount] DECIMAL(18, 2) NOT NULL, 
		[year_2_amount] DECIMAL(18, 2) NOT NULL, [year_3_amount] DECIMAL(18, 2) NOT NULL, [year_4_amount] DECIMAL(18, 2) NOT NULL,[budget_year] int NOT NULL,
		[actionid] int NOT NULL,[actiondesc] varchar(1000) NOT NULL,[altercode] varchar(20) NOT NULL,[show_flag] bit NOT NULL,
		[org_id_1] NVARCHAR(25) NULL,[org_id_2] NVARCHAR(25) NULL,[org_id_3] NVARCHAR(25) NULL,[org_id_4] NVARCHAR(25) NULL,[org_id_5] NVARCHAR(25) NULL,
		[org_name_1] NVARCHAR(200) NULL,[org_name_2] NVARCHAR(200) NULL,[org_name_3] NVARCHAR(200) NULL,[org_name_4] NVARCHAR(200) NULL,[org_name_5] NVARCHAR(200) NULL
		,longdesc NVARCHAR(MAX) NOT NULL ,isblistaction bit NOT NULL DEFAULT(0));

		DECLARE @temp_table_ttemph_service TABLE 
		([fk_tenant_id] INT NOT NULL, [account_code] NVARCHAR(25) NOT NULL, [department_code] NVARCHAR(25) NOT NULL, [function_code] NVARCHAR(25) NOT NULL,
		[fk_kostra_account_code] NVARCHAR(25) NOT NULL,[gl_amount] DECIMAL(18, 2) NOT NULL, [budget_amount] DECIMAL(18, 2) NOT NULL, [year_1_amount] DECIMAL(18, 2) NOT NULL, 
		[year_2_amount] DECIMAL(18, 2) NOT NULL, [year_3_amount] DECIMAL(18, 2) NOT NULL, [year_4_amount] DECIMAL(18, 2) NOT NULL,[budget_year] int NOT NULL,
		[actionid] int NOT NULL,[actiondesc] varchar(1000) NOT NULL,[altercode] varchar(20) NOT NULL,[show_flag] bit NOT NULL,
		[org_id_1] NVARCHAR(25) NULL,[org_id_2] NVARCHAR(25) NULL,[org_id_3] NVARCHAR(25) NULL,[org_id_4] NVARCHAR(25) NULL,[org_id_5] NVARCHAR(25) NULL,
		[org_name_1] NVARCHAR(200) NULL,[org_name_2] NVARCHAR(200) NULL,[org_name_3] NVARCHAR(200) NULL,[org_name_4] NVARCHAR(200) NULL,[org_name_5] NVARCHAR(200) NULL
		,longdesc NVARCHAR(MAX) NOT NULL,isblistaction bit NOT NULL DEFAULT(0));

		DECLARE @temp_table_tad_service TABLE 
		([fk_tenant_id] INT NOT NULL, [account_code] NVARCHAR(25) NOT NULL, [department_code] NVARCHAR(25) NOT NULL, [function_code] NVARCHAR(25) NOT NULL,
		[fk_kostra_account_code] NVARCHAR(25) NOT NULL,[gl_amount] DECIMAL(18, 2) NOT NULL, [budget_amount] DECIMAL(18, 2) NOT NULL, [year_1_amount] DECIMAL(18, 2) NOT NULL, 
		[year_2_amount] DECIMAL(18, 2) NOT NULL, [year_3_amount] DECIMAL(18, 2) NOT NULL, [year_4_amount] DECIMAL(18, 2) NOT NULL,[budget_year] int NOT NULL,
		[actionid] int NOT NULL,[actiondesc] varchar(1000) NOT NULL,[altercode] varchar(20) NOT NULL,[show_flag] bit NOT NULL,
		[org_id_1] NVARCHAR(25) NULL,[org_id_2] NVARCHAR(25) NULL,[org_id_3] NVARCHAR(25) NULL,[org_id_4] NVARCHAR(25) NULL,[org_id_5] NVARCHAR(25) NULL,
		[org_name_1] NVARCHAR(200) NULL,[org_name_2] NVARCHAR(200) NULL,[org_name_3] NVARCHAR(200) NULL,[org_name_4] NVARCHAR(200) NULL,[org_name_5] NVARCHAR(200) NULL
		,longdesc NVARCHAR(MAX) NOT NULL ,isblistaction bit NOT NULL DEFAULT(0));

		DECLARE @temp_table_ttfd_service TABLE 
		([fk_tenant_id] INT NOT NULL, [account_code] NVARCHAR(25) NOT NULL, [department_code] NVARCHAR(25) NOT NULL, [function_code] NVARCHAR(25) NOT NULL,
		[fk_kostra_account_code] NVARCHAR(25) NOT NULL,[gl_amount] DECIMAL(18, 2) NOT NULL, [budget_amount] DECIMAL(18, 2) NOT NULL, [year_1_amount] DECIMAL(18, 2) NOT NULL, 
		[year_2_amount] DECIMAL(18, 2) NOT NULL, [year_3_amount] DECIMAL(18, 2) NOT NULL, [year_4_amount] DECIMAL(18, 2) NOT NULL,[budget_year] int NOT NULL,
		[actionid] int NOT NULL,[actiondesc] varchar(1000) NOT NULL,[altercode] varchar(20) NOT NULL,[show_flag] bit NOT NULL,
		[org_id_1] NVARCHAR(25) NULL,[org_id_2] NVARCHAR(25) NULL,[org_id_3] NVARCHAR(25) NULL,[org_id_4] NVARCHAR(25) NULL,[org_id_5] NVARCHAR(25) NULL,
		[org_name_1] NVARCHAR(200) NULL,[org_name_2] NVARCHAR(200) NULL,[org_name_3] NVARCHAR(200) NULL,[org_name_4] NVARCHAR(200) NULL,[org_name_5] NVARCHAR(200) NULL
		,longdesc NVARCHAR(MAX) NOT NULL,isblistaction bit NOT NULL DEFAULT(0));
  


		INSERT @temp_table_tth_service
		([fk_tenant_id],[actionid],[altercode],[actiondesc],[budget_year],[account_code],[department_code],[function_code],org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5
		,[gl_amount],[budget_amount],[year_1_amount],[year_2_amount],[year_3_amount],[year_4_amount],[show_flag],[fk_kostra_account_code],[longdesc],isblistaction)

		Select tth.fk_tenant_id,tth.pk_action_id as actionid,ttd.fk_alter_code as alterCode, tth.description as actiondesc,ttd.budget_year, ttd.fk_account_code,ttd.department_code,ttd.function_code
		,tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,
		0 as gl_amount,0 as budget_amount ,sum(ttd.year_1_amount)year_1_amount,
		sum(ttd.year_2_amount)year_2_amount,
		sum(ttd.year_3_amount)year_3_amount,
		sum(ttd.year_4_amount) year_4_amount,
		show_flag = CASE WHEN fa.show_flag = 0 THEN 0 WHEN tth.display_cab_flag is NULL THEN 0 ELSE tth.display_cab_flag END,
		ac.fk_kostra_account_code,COALESCE(tth.long_description,'') ,0
		from tfp_trans_header tth with(nolock)
		inner join tfp_trans_detail ttd with(nolock) on tth.pk_action_id  = ttd.fk_action_id
		LEFT JOIN #temphierarchyService oh ON ttd.department_code = oh.fk_department_code AND ttd.fk_tenant_id = oh.fk_tenant_id
		LEFT join tco_service_values tsv  with(nolock) on ttd.fk_tenant_id=tsv.fk_tenant_id AND ttd.function_code=tsv.fk_function_code
		LEFT JOIN tco_accounts ac with(nolock) ON ttd.fk_account_code = ac.pk_account_code AND tth.fk_tenant_id = ac.pk_tenant_id	
		LEFT JOIN tco_fp_alter_codes fa ON ttd.fk_alter_code = fa.pk_alter_code AND ttd.fk_tenant_id = fa.fk_tenant_id		
		where tth.fk_tenant_id=@tenant_id AND  ttd.fk_change_id in (select changeids from #tempChangeIds)  
		group by tth.fk_tenant_id,tth.pk_action_id  ,ttd.fk_alter_code, tth.description,ttd.budget_year, ttd.fk_account_code,ttd.department_code,ttd.function_code,
		tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,tth.display_cab_flag,ac.fk_kostra_account_code,tth.long_description, fa.show_flag 

IF @incluce_blist = 1 
BEGIN

		INSERT @temp_table_ttemph_service
		([fk_tenant_id],[actionid],[altercode],[actiondesc],[budget_year],[account_code],[department_code],[function_code],org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5
		,[gl_amount],[budget_amount],[year_1_amount],[year_2_amount],[year_3_amount],[year_4_amount],[show_flag],[fk_kostra_account_code],[longdesc],isblistaction)
		Select tth.fk_tenant_id,tth.pk_temp_id as actionid,ttd.fk_alter_code as alterCode, tth.description as actiondesc,ttd.budget_year, ttd.fk_account_code,ttd.department_code,ttd.function_code
		,tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,
		0 as gl_amount,0 as budget_amount ,sum(ttd.year_1_amount)year_1_amount,
		sum(ttd.year_2_amount)year_2_amount,
		sum(ttd.year_3_amount)year_3_amount,
		sum(ttd.year_4_amount) year_4_amount,
		show_flag = CASE WHEN fa.show_flag = 0 THEN 0 WHEN tth.display_cab_flag is NULL THEN 0 ELSE tth.display_cab_flag END,
		ac.fk_kostra_account_code,COALESCE(tth.long_description,'') ,1
		from tfp_temp_header tth with(nolock)
		inner join tfp_temp_detail ttd with(nolock) on tth.pk_temp_id  = ttd.fk_temp_id AND tth.fk_tenant_id = ttd.fk_tenant_id
		LEFT JOIN #temphierarchyService oh ON ttd.department_code = oh.fk_department_code AND ttd.fk_tenant_id = oh.fk_tenant_id
		LEFT join tco_service_values tsv  with(nolock) on ttd.fk_tenant_id=tsv.fk_tenant_id AND ttd.function_code=tsv.fk_function_code
		LEFT JOIN tco_accounts ac with(nolock) ON ttd.fk_account_code = ac.pk_account_code AND tth.fk_tenant_id = ac.pk_tenant_id	
		LEFT JOIN tco_fp_alter_codes fa ON ttd.fk_alter_code = fa.pk_alter_code AND ttd.fk_tenant_id = fa.fk_tenant_id		
		where tth.fk_tenant_id=@tenant_id AND  ttd.fk_change_id in (select changeids from #tempChangeIds)   AND ttd.budget_year=@budget_year AND tth.action_type not in(1,2,3,4,60) AND tth.is_parked_action = 0
		group by tth.fk_tenant_id,tth.pk_temp_id  ,ttd.fk_alter_code, tth.description,ttd.budget_year, ttd.fk_account_code,ttd.department_code,ttd.function_code,
		tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,tth.display_cab_flag,ac.fk_kostra_account_code,tth.long_description, fa.show_flag 

END	 

		INSERT @temp_table_tad_service
		([fk_tenant_id],[actionid],[altercode],[actiondesc],[budget_year],[account_code],[department_code],[function_code],org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5
		,[gl_amount],[budget_amount],[year_1_amount],[year_2_amount],[year_3_amount],[year_4_amount],[show_flag],[fk_kostra_account_code],[longdesc],isblistaction)
		Select tth.fk_tenant_id,-2 ,'-1' , 'Other Actions' ,tth.gl_year + 2, tth.fk_account_code,tth.department_code,tth.fk_function_code
		,tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,
		sum(tth.amount) as gl_amount,0 as budget_amount , 0 as year_1_amount,
		0 as year_2_amount,
		0 as year_3_amount,
		0 as year_4_amount,0 ,ac.fk_kostra_account_code,'',0 from tfp_accounting_data tth with(nolock)  
		LEFT JOIN #temphierarchyService oh ON tth.department_code = oh.fk_department_code AND tth.fk_tenant_id = oh.fk_tenant_id
		LEFT join tco_service_values tsv  with(nolock) on tth.fk_tenant_id=tsv.fk_tenant_id AND tth.fk_function_code=tsv.fk_function_code
		LEFT JOIN tco_accounts ac with(nolock) ON tth.fk_account_code = ac.pk_account_code AND tth.fk_tenant_id = ac.pk_tenant_id 
		where tth.fk_tenant_id=@tenant_id and gl_year = @budget_year-2  -- REPLACE THIS CHECK WITH TENANT VARIABLE
		group by tth.fk_tenant_id,  tth.description,tth.gl_year, tth.fk_account_code,tth.department_code,tth.fk_function_code
		,tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5, ac.fk_kostra_account_code 
		having (sum(tth.amount) != 0)

		 

		INSERT @temp_table_ttfd_service
		([fk_tenant_id],[actionid],[altercode],[actiondesc],[budget_year],[account_code],[department_code],[function_code],org_id_1,org_id_2,org_id_3,org_id_4,org_id_5,org_name_1,org_name_2,org_name_3,org_name_4,org_name_5
		,[gl_amount],[budget_amount],[year_1_amount],[year_2_amount],[year_3_amount],[year_4_amount],[show_flag],[fk_kostra_account_code],[longdesc],isblistaction)
		Select tth.fk_tenant_id,-2 ,'-1' , 'Other Actions' ,tth.budget_year + 1, tth.fk_account_code,tth.department_code,tth.fk_function_code
		,tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,
		0 as gl_amount,sum(tth.amount_year_1) as budget_amount , 0 as year_1_amount,
		0 as year_2_amount,
		0 as year_3_amount,
		0 as year_4_amount,0 ,ac.fk_kostra_account_code,'',0 from tbu_trans_detail_original tth with(nolock) 
		LEFT JOIN #temphierarchyService oh ON tth.department_code = oh.fk_department_code AND tth.fk_tenant_id = oh.fk_tenant_id
		LEFT join tco_service_values tsv  with(nolock) on tth.fk_tenant_id=tsv.fk_tenant_id AND tth.fk_function_code=tsv.fk_function_code
		LEFT JOIN tco_accounts ac with(nolock) ON tth.fk_account_code = ac.pk_account_code AND tth.fk_tenant_id = ac.pk_tenant_id 
		where tth.fk_tenant_id=@tenant_id and budget_year = @budget_year-1  -- REPLACE THIS CHECK WITH TENANT VARIABLE
		group by tth.fk_tenant_id,  tth.description,tth.budget_year, tth.fk_account_code,tth.department_code,tth.fk_function_code
		,tsv.service_id_1,tsv.service_id_2,tsv.service_id_3,tsv.service_id_4,tsv.service_id_5
		,tsv.service_name_1,tsv.service_name_2,tsv.service_name_3,tsv.service_name_4,tsv.service_name_5,ac.fk_kostra_account_code
		having (sum(tth.amount_year_1)  != 0)

		 
		SELECT pk_account_code into #accountcodes FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1
		WHERE f.pk_tenant_id = @tenant_id
		AND g.fk_kostra_account_code = f.fk_kostra_account_code
		AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT' AND p1.active = 1
		AND g.line_item_id NOT IN (115,140,150,320,330,350,360,210,230, 410,250)

		--[twh_buddoc_reports]
		SELECT a.fk_tenant_id, a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		aggregate_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		aggregate_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END,
		0 as gl_amount, a.budget_amount, a.year_1_amount, a.year_2_amount,  a.year_3_amount, a.year_4_amount,a.show_flag ,a.longdesc,a.isblistaction into #temp 
		FROM @temp_table_tth_service a 
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE a.function_code NOT IN (SELECT p.param_value FROM tco_parameters p  WHERE param_name = 'FP_CENTRAL_FUNCTIONS'
		AND p.fk_tenant_id = a.fk_tenant_id)
		AND  a.account_code NOT  IN 
		(SELECT pk_account_code from #accountcodes)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%'  AND a.fk_tenant_id =@tenant_id

		UNION ALL


		--[twh_buddoc_reports]
		SELECT a.fk_tenant_id,  a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		service_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		service_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END, 0 as gl_amount, a.budget_amount, a.year_1_amount, a.year_2_amount,  a.year_3_amount, a.year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM   @temp_table_tth_service a
		LEFT JOIN tco_parameters e ON  a.function_code = e.param_value AND a.fk_tenant_id = e.fk_tenant_id 
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS'
		AND a.account_code NOT  IN 
		(SELECT pk_account_code FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1
		WHERE f.pk_tenant_id = a.fk_tenant_id
		AND g.fk_kostra_account_code = f.fk_kostra_account_code
		AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT' AND p1.active = 1)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%' 
		AND NOT EXISTS  (SELECT param_value FROM tco_parameters p WHERE p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'EXCLUDE_FUNCTIONS_1B' AND p.active = 1) AND a.fk_tenant_id =@tenant_id

		UNION ALL

		SELECT a.fk_tenant_id, a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		aggregate_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		aggregate_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END,
		0 as gl_amount, a.budget_amount, a.year_1_amount, a.year_2_amount,  a.year_3_amount, a.year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM @temp_table_ttemph_service a 
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE a.function_code NOT IN (SELECT p.param_value FROM tco_parameters p  WHERE param_name = 'FP_CENTRAL_FUNCTIONS'
		AND p.fk_tenant_id = a.fk_tenant_id)
		AND  a.account_code NOT  IN 
		(SELECT pk_account_code from #accountcodes)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%'  AND a.fk_tenant_id =@tenant_id

		UNION ALL


		--[twh_buddoc_reports]
		SELECT a.fk_tenant_id,  a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		service_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		service_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END, 0 as gl_amount, a.budget_amount, a.year_1_amount, a.year_2_amount,  a.year_3_amount, a.year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM   @temp_table_ttemph_service a
		LEFT JOIN tco_parameters e ON  a.function_code = e.param_value AND a.fk_tenant_id = e.fk_tenant_id 
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS'
		AND a.account_code NOT  IN 
		(SELECT pk_account_code FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1
		WHERE f.pk_tenant_id = a.fk_tenant_id
		AND g.fk_kostra_account_code = f.fk_kostra_account_code
		AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT' AND p1.active = 1)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%' 
		AND NOT EXISTS  (SELECT param_value FROM tco_parameters p WHERE p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'EXCLUDE_FUNCTIONS_1B' AND p.active = 1) AND a.fk_tenant_id =@tenant_id

		UNION ALL
		
		--tfp_accounting_data
		SELECT a.fk_tenant_id, a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		aggregate_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		aggregate_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END,
		gl_amount, 0  as budget_amount, 0 as year_1_amount, 0 as year_2_amount,  0 as year_3_amount, 0 as year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM @temp_table_tad_service a
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE a.function_code NOT IN (SELECT p.param_value FROM tco_parameters p  WHERE param_name = 'FP_CENTRAL_FUNCTIONS'
		AND p.fk_tenant_id = a.fk_tenant_id)
		AND  a.account_code NOT  IN 
		(SELECT pk_account_code from #accountcodes)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%'  AND a.fk_tenant_id =@tenant_id

		UNION ALL


		-----tfp_accounting_data
		SELECT a.fk_tenant_id, a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		aggregate_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		aggregate_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END,  gl_amount, 0 as budget_amount,  0 as year_1_amount, 0 as year_2_amount,  0 as year_3_amount, 0 as year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM   @temp_table_tad_service a
		JOIN tco_parameters e ON a.function_code = e.param_value AND a.fk_tenant_id = e.fk_tenant_id
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' 
		AND a.account_code NOT  IN 
		(SELECT pk_account_code FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1
		WHERE f.pk_tenant_id = a.fk_tenant_id
		AND g.fk_kostra_account_code = f.fk_kostra_account_code
		AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT' AND p1.active = 1)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%' 
		AND NOT EXISTS  (SELECT param_value FROM tco_parameters p WHERE p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'EXCLUDE_FUNCTIONS_1B' AND p.active = 1) AND a.fk_tenant_id =@tenant_id


		UNION ALL

		 

		--tbu_trans_detail_original
		SELECT a.fk_tenant_id, a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		aggregate_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		aggregate_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END,
		0 as gl_amount,  budget_amount, 0 as year_1_amount, 0 as year_2_amount,  0 as year_3_amount, 0 as year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM @temp_table_ttfd_service a
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE a.function_code NOT IN (SELECT p.param_value FROM tco_parameters p  WHERE param_name = 'FP_CENTRAL_FUNCTIONS'
		AND p.fk_tenant_id = a.fk_tenant_id)
		AND  a.account_code NOT  IN 
		(SELECT pk_account_code from #accountcodes)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%'  AND a.fk_tenant_id =@tenant_id

		UNION ALL


		-----tbu_trans_detail_original
		SELECT a.fk_tenant_id, a.budget_year,  a.actionid,a.actiondesc,a.altercode,
		aggregate_id = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_id_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_id_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_id_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_id_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_id_5	
		ELSE org_id_2
		END,
		aggregate_name = 
		CASE	WHEN p1.param_value = 'service_id_1' THEN a.org_name_1
				WHEN p1.param_value = 'service_id_2' THEN a.org_name_2
				WHEN p1.param_value = 'service_id_3' THEN a.org_name_3
				WHEN p1.param_value = 'service_id_4' THEN a.org_name_4
				WHEN p1.param_value = 'service_id_5' THEN a.org_name_5
				ELSE org_name_2
		END,0 as  gl_amount, budget_amount ,  0 as year_1_amount, 0 as year_2_amount,  0 as year_3_amount, 0 as year_4_amount,a.show_flag ,a.longdesc,a.isblistaction
		FROM   @temp_table_ttfd_service a
		JOIN tco_parameters e ON a.function_code = e.param_value AND a.fk_tenant_id = e.fk_tenant_id
		LEFT JOIN gco_kostra_accounts k ON a.fk_kostra_account_code = k.pk_kostra_account_code
		LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
		WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' 
		AND a.account_code NOT  IN 
		(SELECT pk_account_code FROM tco_accounts f, gmd_reporting_line g, tco_parameters p1
		WHERE f.pk_tenant_id = a.fk_tenant_id
		AND g.fk_kostra_account_code = f.fk_kostra_account_code
		AND g.report = p1.param_value AND f.pk_tenant_id = p1.fk_tenant_id AND p1.param_name = 'BMDOC_1A_REPLINE_REPORT' AND p1.active = 1)
		AND k.type = 'operations'
		AND p1.param_value like 'service_id%' 
		AND NOT EXISTS  (SELECT param_value FROM tco_parameters p WHERE p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'EXCLUDE_FUNCTIONS_1B' AND p.active = 1) AND a.fk_tenant_id =@tenant_id


		SELECT fk_tenant_id,budget_year,actionid,actiondesc,alterCode,aggregate_id,aggregate_name,show_flag,longdesc,isblistaction,sum(gl_amount)gl_amount,
		sum(budget_amount)budget_amount,
		sum(year_1_amount)year_1_amount,
		sum(year_2_amount)year_2_amount,
		sum(year_3_amount)year_3_amount,
		sum(year_4_amount)year_4_amount into #temp2 from #temp
		group by fk_tenant_id,budget_year,actionid,actiondesc,alterCode,aggregate_id,aggregate_name ,show_flag, longdesc,isblistaction

		DELETE from #temp2 where ( gl_amount = 0 and budget_amount= 0 and year_1_amount= 0 and year_2_amount= 0 and year_3_amount= 0 and year_4_amount=0 )   


		INSERT tps_operations_propopsal
		(fk_tenant_id,fk_proposal_id,budget_year,line_group_id,line_group,line_item_id,line_item,fk_action_id,action_name,alterCode,org_id,org_name,gl_amount,
		org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
		change_4_amount,comment,description,status,updated,updated_by,show_flag,isblistaction )
		SELECT fk_tenant_id,@proposal_id,budget_year,0,0,0,0,
		actionid,actiondesc,alterCode,aggregate_id,aggregate_name,gl_amount,budget_amount,0,year_1_amount,year_2_amount,year_3_amount,year_4_amount,0,0,0,0,
		'',longdesc,case when isblistaction = 1 then 0 else 1 end,getutcdate(),@user_id,show_flag,isblistaction from #temp2   


		DROP TABLE #temp
		DROP TABLE #temp2   
		DROP TABLE #tempChangeIds   
		DROP TABLE #temphierarchyService 
		DROP TABLE #accountcodes

END