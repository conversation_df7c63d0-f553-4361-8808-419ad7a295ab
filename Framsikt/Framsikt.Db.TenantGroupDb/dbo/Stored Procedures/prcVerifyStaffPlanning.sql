
CREATE OR ALTER PROCEDURE [dbo].[prcVerifyStaffPlanning] ( @tenant_id INT,@budget_year INT,@6g_amount dec(18,2) = 561804)

as

DECLARE @holiday_rate_low dec(18,4) = 0.12
DECLARE @holiday_rate_high dec(18,4) = 0.143

DECLARE @table_name AS varchar(100)
DECLARE @table_name_2 AS varchar(100)
DECLARE @org_version AS varchar(25)

SET @org_version = (SELECT pk_org_version FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND @budget_year*100+1 BETWEEN period_from AND period_to)

IF @tenant_id = 0 
BEGIN
PRINT 'This does not work anymore. Have to run it per tenant'
RETURN
END

SET NOCOUNT ON

SET @table_name = 'TempVerifyStaffplanning' -- + convert(varchar(11), @tenant_id)
SET @table_name_2 = 'TempVerifyStaffplanningsummary' -- + convert(varchar(11), @tenant_id)

DECLARE @key_id INT = (select CONVERT(INT,param_value) from tco_parameters where fk_tenant_id = @tenant_id AND param_name = 'BU_ALLOCATION_KEY_HOLIDAY')
DECLARE @key_id_salary INT = (select CONVERT(INT,param_value) from tco_parameters where fk_tenant_id = @tenant_id AND param_name = 'BU_ALLOCATION_KEY_SALARY')

DECLARE @key_id_6w INT = (select CONVERT(INT,param_value) from tco_parameters where fk_tenant_id = @tenant_id AND param_name = 'BU_ALLOCATION_KEY_HOLIDAY_6W')
DECLARE @key_id_salary_6w INT = (select CONVERT(INT,param_value) from tco_parameters where fk_tenant_id = @tenant_id AND param_name = 'BU_ALLOCATION_KEY_SALARY_6W')

DECLARE @param_264 NVARCHAR(100) = (SELECT param_value FROM vw_tco_parameters p WHERE  p.fk_tenant_id =  @tenant_id AND p.param_name = 'Staffplan_264_work_days' AND p.active = 1)


DECLARE @all_pct_1 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 1 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_2 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 2 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_3 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 3 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_4 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 4 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_5 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 5 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_6 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 6 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_7 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 7 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_8 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 8 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_9 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 9 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_10 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 10 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_11 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 11 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_12 DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id AND period = 12 AND fk_tenant_id IN (0,@tenant_id))

DECLARE @all_pct_1_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 1 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_2_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 2 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_3_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 3 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_4_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 4 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_5_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 5 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_6_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 6 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_7_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 7 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_8_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 8 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_9_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 9 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_10_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 10 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_11_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 11 AND fk_tenant_id IN (0,@tenant_id))
DECLARE @all_pct_12_6w DEC(18,5) = (SELECT allocation_pct/100 FROM tco_periodic_key WHERE key_id = @key_id_6w AND period = 12 AND fk_tenant_id IN (0,@tenant_id))

IF OBJECT_ID('tempdb..#TEMP_EMPLOYMENTS') IS NOT NULL
BEGIN
DROP TABLE #TEMP_EMPLOYMENTS
END


CREATE TABLE #TEMP_EMPLOYMENTS
(
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
pk_employment_id INT NOT NULL,
addon_id INT NOT NULL,
fk_account_code NVARCHAR(25) NOT NULL,
fk_department_code NVARCHAR(25) NOT NULL,
org_id_2 NVARCHAR(25)  NULL,
org_id_3 NVARCHAR(25)  NULL,
pension_type NVARCHAR(25) NOT NULL,
amount_salary_year DECIMAL(18,2) NOT NULL,
start_period INT NOT NULL,
end_period INT NOT NULL,
position_pct DECIMAL(18,2) NOT NULL,
fk_holiday_type_id INT NOT NULL,
yearly_budget DECIMAL(18,2) NOT NULL,
amount_pension DECIMAL(18,2) NOT NULL,
amount_holiday DECIMAL(18,2) NOT NULL,
amount_aga_salary DECIMAL(18,2) NOT NULL,
amount_aga_pension DECIMAL(18,2) NOT NULL,
amount_aga_holiday DECIMAL(18,2) NOT NULL,
new_yearly_budget_salary DECIMAL(18,2) NOT NULL,
new_yearly_budget_addon DECIMAL(18,2) NOT NULL,
new_amount_pension DECIMAL(18,2) NOT NULL,
new_amount_holiday DECIMAL(18,2) NOT NULL,
new_amount_aga_salary DECIMAL(18,2) NOT NULL,
new_amount_aga_pension DECIMAL(18,2) NOT NULL,
new_amount_aga_holiday DECIMAL(18,2) NOT NULL,
firm_salary DECIMAL(18,2) NOT NULL,
salary_allocation_pct DECIMAL(18,10) NOT NULL,
new_estimated_employment DECIMAL(18,2) DEFAULT 0 NOT NULL,
existing_employments_total DECIMAL(18,2) DEFAULT 0 NOT NULL,
new_estimated_tbu DECIMAL(18,2) DEFAULT 0 NOT NULL,
existing_budget_tbu DECIMAL(18,2) DEFAULT 0 NOT NULL)


DECLARE @budget_table TABLE (
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
fk_employment_id INT NOT NULL,
department_code NVARCHAR(25) NOT NULL,
org_id_2 NVARCHAR(25)  NULL,
org_id_3 NVARCHAR(25)  NULL,
amount_year_1 DEC(18,2) NOT NULL
)

DECLARE @employments TABLE (
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
pk_employment_id INT NOT NULL,
fk_account_code NVARCHAR(25) NOT NULL,
fk_department_code NVARCHAR(25) NOT NULL,
pension_type NVARCHAR(25) NOT NULL,
amount_salary_year DECIMAL(18,2) NOT NULL,
start_period INT NOT NULL,
end_period INT NOT NULL,
position_pct DECIMAL(18,2) NOT NULL,
fk_holiday_type_id INT NOT NULL,
yearly_budget DECIMAL(18,2) NOT NULL,
amount_pension DECIMAL(18,2) NOT NULL,
amount_holiday DECIMAL(18,2) NOT NULL,
amount_aga_salary DECIMAL(18,2) NOT NULL,
amount_aga_pension DECIMAL(18,2) NOT NULL,
amount_aga_holiday DECIMAL(18,2) NOT NULL,
new_yearly_budget DECIMAL(18,2) NOT NULL,
new_amount_pension DECIMAL(18,2) NOT NULL,
new_amount_holiday DECIMAL(18,2) NOT NULL,
new_amount_aga_salary DECIMAL(18,2) NOT NULL,
new_amount_aga_pension DECIMAL(18,2) NOT NULL,
new_amount_aga_holiday DECIMAL(18,2) NOT NULL,
factor_hol_deduction DECIMAL(18,10) NOT NULL,
firm_salary DECIMAL(18,2) NOT NULL,
salary_allocation_pct DECIMAL(18,10) NOT NULL,
amount_above_6g DECIMAL(18,2) NOT NULL,
amount_below_6g DECIMAL(18,2) NOT NULL,
holiday_base DECIMAL(18,2) NOT NULL,
jan DECIMAL(18,2) NOT NULL,
feb DECIMAL(18,2) NOT NULL,
mar DECIMAL(18,2) NOT NULL,
apr DECIMAL(18,2) NOT NULL,
may DECIMAL(18,2) NOT NULL,
jun DECIMAL(18,2) NOT NULL,
jul DECIMAL(18,2) NOT NULL,
aug DECIMAL(18,2) NOT NULL,
sep DECIMAL(18,2) NOT NULL,
oct DECIMAL(18,2) NOT NULL,
nov DECIMAL(18,2) NOT NULL,
[dec] DECIMAL(18,2) NOT NULL,
[fk_tax_rate] [decimal](18, 3) NOT NULL
)


DECLARE @addon TABLE (
pk_id INT NOT NULL,
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
pk_employment_id INT NOT NULL,
fk_account_code NVARCHAR(25) NOT NULL,
fk_department_code NVARCHAR(25) NOT NULL,
pension_type NVARCHAR(25) NOT NULL,
amount_salary_year DECIMAL(18,2) NOT NULL,
start_period INT NOT NULL,
end_period INT NOT NULL,
position_pct DECIMAL(18,2) NOT NULL,
fk_holiday_type_id INT NOT NULL,
yearly_budget DECIMAL(18,2) NOT NULL,
amount_pension DECIMAL(18,2) NOT NULL,
amount_holiday DECIMAL(18,2) NOT NULL,
amount_aga_salary DECIMAL(18,2) NOT NULL,
amount_aga_pension DECIMAL(18,2) NOT NULL,
amount_aga_holiday DECIMAL(18,2) NOT NULL,
new_yearly_budget DECIMAL(18,2) NOT NULL,
new_amount_pension DECIMAL(18,2) NOT NULL,
new_amount_holiday DECIMAL(18,2) NOT NULL,
new_amount_aga_salary DECIMAL(18,2) NOT NULL,
new_amount_aga_pension DECIMAL(18,2) NOT NULL,
new_amount_aga_holiday DECIMAL(18,2) NOT NULL,
factor_hol_deduction DECIMAL(18,10) NOT NULL,
firm_salary DECIMAL(18,2) NOT NULL,
salary_allocation_pct DECIMAL(18,10) NOT NULL,
amount_above_6g DECIMAL(18,2) NOT NULL,
amount_below_6g DECIMAL(18,2) NOT NULL,
holiday_base DECIMAL(18,2) NOT NULL,
jan DECIMAL(18,2) NOT NULL,
feb DECIMAL(18,2) NOT NULL,
mar DECIMAL(18,2) NOT NULL,
apr DECIMAL(18,2) NOT NULL,
may DECIMAL(18,2) NOT NULL,
jun DECIMAL(18,2) NOT NULL,
jul DECIMAL(18,2) NOT NULL,
aug DECIMAL(18,2) NOT NULL,
sep DECIMAL(18,2) NOT NULL,
oct DECIMAL(18,2) NOT NULL,
nov DECIMAL(18,2) NOT NULL,
[dec] DECIMAL(18,2) NOT NULL, 
tax_flag INT NOT NULL,
holiday_flag INT NOT NULL,
pension_flag INT NOT NULL,
[fk_tax_rate] [decimal](18, 3) NOT NULL
)


IF @tenant_id != 0
BEGIN
--Fetch all positions
INSERT INTO @employments (fk_tenant_id, budget_year, pk_employment_id,fk_account_code,fk_department_code, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
new_yearly_budget,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,
factor_hol_deduction,firm_salary,salary_allocation_pct,amount_above_6g,amount_below_6g,holiday_base, jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec, fk_tax_rate)
SELECT 
fk_tenant_id, budget_year, pk_employment_id,fk_account_code,fk_department_code, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
ROUND(((convert(numeric(18,2),end_period)-start_period+1)/12)*amount_salary_year*position_pct/100,2),0,0,0,0,0,
0 as factor_hol_deduction,0 as firm_salary,0 as salary_allocation_pct,0 as amount_above_6g,0 as amount_below_6g, 0 AS holiday_base
, 0 as jan, 0 as feb, 0 as mar, 0 as apr, 0 as may, 0 as jun, 0 as jul, 0 as aug, 0 as sep, 0 as oct, 0 as nov, 0 as dec 
, fk_tax_rate
FROM tbu_employments
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year AND delete_flag = 0

END


IF @tenant_id = 0
BEGIN
--Fetch all positions
INSERT INTO @employments (fk_tenant_id, budget_year, pk_employment_id,fk_account_code,fk_department_code, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
new_yearly_budget,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,
factor_hol_deduction,firm_salary,salary_allocation_pct,amount_above_6g,amount_below_6g,holiday_base, jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec, fk_tax_rate)
SELECT 
fk_tenant_id, budget_year, pk_employment_id,fk_account_code,fk_department_code, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
ROUND(((convert(numeric(18,2),end_period)-start_period+1)/12)*amount_salary_year*position_pct/100,2),0,0,0,0,0,
0 as factor_hol_deduction,0 as firm_salary,0 as salary_allocation_pct,0 as amount_above_6g,0 as amount_below_6g, 0 AS holiday_base
, 0 as jan, 0 as feb, 0 as mar, 0 as apr, 0 as may, 0 as jun, 0 as jul, 0 as aug, 0 as sep, 0 as oct, 0 as nov, 0 as dec 
,fk_tax_rate
FROM tbu_employments
WHERE budget_year = @budget_year AND delete_flag = 0 

END

update a set fk_tax_rate = c.tax_rate 
from @employments a
LEFT JOIN [tbu_employments_tax_rate] b ON a.fk_tenant_id = b.fk_tenant_id and a.fk_tax_rate = b.tax_rate
LEFT JOIN [tbu_employments_tax_rate] c on a.fk_tenant_id = c.fk_tenant_id and c.is_default = 1
where b.tax_rate is null


--Calculate total salary allocation pct pr position
UPDATE @employments SET salary_allocation_pct = c.allocation_pct
FROM @employments d, 
(SELECT a.pk_employment_id, SUM(b.allocation_pct) as allocation_pct
FROM @employments a, tco_periodic_key b
WHERE b.period BETWEEN a.start_period AND a.end_period AND b.key_id = @key_id_salary and a.fk_holiday_type_id != 6
GROUP BY a.pk_employment_id) c
WHERE d.pk_employment_id = c.pk_employment_id

UPDATE @employments SET salary_allocation_pct = c.allocation_pct
FROM @employments d, 
(SELECT a.pk_employment_id, SUM(b.allocation_pct) as allocation_pct
FROM @employments a, tco_periodic_key b
WHERE b.period BETWEEN a.start_period AND a.end_period AND b.key_id = @key_id_salary_6w and a.fk_holiday_type_id = 6
GROUP BY a.pk_employment_id) c
WHERE d.pk_employment_id = c.pk_employment_id

--Calculate for 0 vacation
UPDATE @employments SET salary_allocation_pct = c.allocation_pct
FROM @employments d, 
(SELECT a.pk_employment_id, SUM(b.allocation_pct) as allocation_pct
FROM @employments a, tco_periodic_key b
WHERE b.period BETWEEN a.start_period AND a.end_period AND b.key_id = 13 and a.fk_holiday_type_id = 0 AND (a.start_period != 1 OR a.end_period != 12)
GROUP BY a.pk_employment_id) c
WHERE d.pk_employment_id = c.pk_employment_id

--Calculate correct pension amount
UPDATE @employments SET new_amount_pension = new_yearly_budget * b.rate/100, 
factor_hol_deduction =CASE
        WHEN @param_264= 'TRUE' THEN 1-convert(numeric(18,4),fk_holiday_type_id)*5/264
        ELSE 1-convert(numeric(18,4),fk_holiday_type_id)*5/260
        END

FROM @employments a, tmd_pension_type b 
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.budget_year between b.year_from and b.year_to
AND a.pension_type = b.pension_type

--Calculate salary amount for full year
UPDATE @employments SET firm_salary = amount_salary_year*position_pct/100*factor_hol_deduction

UPDATE @employments SET amount_above_6g = CASE WHEN (amount_salary_year*position_pct/100) > @6g_amount THEN (amount_salary_year*position_pct/100)-@6g_amount else 0 end
WHERE fk_holiday_type_id = 6

UPDATE @employments SET amount_below_6g =(amount_salary_year*position_pct/100)-amount_above_6g
WHERE fk_holiday_type_id = 6

-- Below query is changed so that @holiday_rate_high is used instead of low. this is a bug but makes it consistent with the application The correct logic is: 

--UPDATE @employments SET holiday_base = (amount_above_6g*@holiday_rate_low*factor_hol_deduction)+(amount_below_6g*@holiday_rate_high*factor_hol_deduction)
--WHERE fk_holiday_type_id = 6


--Calculate holiday base amount for full year
UPDATE @employments SET holiday_base = (amount_above_6g*@holiday_rate_high*factor_hol_deduction)+(amount_below_6g*@holiday_rate_high*factor_hol_deduction)
WHERE fk_holiday_type_id = 6

UPDATE @employments SET holiday_base = firm_salary * @holiday_rate_low
WHERE fk_holiday_type_id = 5


--Calculate holiday amount corrected for periods
UPDATE @employments SET 
jan = holiday_base * @all_pct_1,
feb = holiday_base * @all_pct_2,
mar = holiday_base * @all_pct_3,
apr = holiday_base * @all_pct_4,
may = holiday_base * @all_pct_5,
jun = holiday_base * @all_pct_6,
jul = holiday_base * @all_pct_7,
aug = holiday_base * @all_pct_8,
sep = holiday_base * @all_pct_9,
oct = holiday_base * @all_pct_10,
nov = holiday_base * @all_pct_11,
dec = holiday_base * @all_pct_12
WHERE fk_holiday_type_id !=6

UPDATE @employments SET 
jan = holiday_base * @all_pct_1_6w,
feb = holiday_base * @all_pct_2_6w,
mar = holiday_base * @all_pct_3_6w,
apr = holiday_base * @all_pct_4_6w,
may = holiday_base * @all_pct_5_6w,
jun = holiday_base * @all_pct_6_6w,
jul = holiday_base * @all_pct_7_6w,
aug = holiday_base * @all_pct_8_6w,
sep = holiday_base * @all_pct_9_6w,
oct = holiday_base * @all_pct_10_6w,
nov = holiday_base * @all_pct_11_6w,
dec = holiday_base * @all_pct_12_6w
WHERE fk_holiday_type_id =6

UPDATE @employments SET jan = 0  
WHERE start_period > 1

UPDATE @employments SET jan = 0, feb = 0   
WHERE start_period > 2

UPDATE @employments SET jan = 0, feb = 0, mar = 0   
WHERE start_period > 3

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0    
WHERE start_period > 4

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0     
WHERE start_period > 5

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0      
WHERE start_period > 6

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0       
WHERE start_period > 7

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0        
WHERE start_period > 8

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0        
WHERE start_period > 9

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0         
WHERE start_period > 10

UPDATE @employments SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0         
WHERE start_period > 11

UPDATE @employments SET feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 2

UPDATE @employments SET mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 3

UPDATE @employments SET apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 4

UPDATE @employments SET may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 5

UPDATE @employments SET jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 6

UPDATE @employments SET jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 7

UPDATE @employments SET aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 8

UPDATE @employments SET sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 9

UPDATE @employments SET oct = 0, nov = 0, [dec]=0
WHERE end_period < 10

UPDATE @employments SET nov = 0, [dec]=0
WHERE end_period < 11

UPDATE @employments SET [dec]=0
WHERE end_period < 12

UPDATE @employments SET new_amount_holiday = jan+feb+mar+apr+may+jun+jul+aug+sep+oct+nov+dec


UPDATE @employments SET new_amount_aga_salary = firm_salary * salary_allocation_pct /100 * fk_tax_rate, new_amount_aga_pension = new_amount_pension*fk_tax_rate, new_amount_aga_holiday = new_amount_holiday*fk_tax_rate
--FROM @employments a, gco_tenants t, gmd_emp_tax_rates tr 
--WHERE a.fk_tenant_id = t.pk_id
--AND t.municipality_id = tr.fk_municipality_id 


INSERT INTO @addon (pk_id, fk_tenant_id, budget_year, pk_employment_id,fk_account_code,fk_department_code, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
new_yearly_budget,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,
factor_hol_deduction,firm_salary,salary_allocation_pct,amount_above_6g,amount_below_6g,holiday_base, jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec,
tax_flag, holiday_flag, pension_flag,fk_tax_rate)

SELECT a.pk_id, a.fk_tenant_id, b.budget_year, b.pk_employment_id,a.fk_account_code,a.fk_department_code, b.pension_type,
a.amount_month * 12,a.start_period,a.end_period,b.position_pct,b.fk_holiday_type_id, a.amount_year as yearly_budget,a.amount_pension,a.amount_holiday,
a.amount_aga_salary,a.amount_aga_pension,a.amount_aga_holiday,
ROUND(((convert(numeric(18,2),a.end_period)-a.start_period+1)/12)*a.amount_month*12,2) as new_yearly_budget,
0 as new_amount_pension,0 as new_amount_holiday,
0 as new_amount_aga_salary,0 as new_amount_aga_pension,0 as new_amount_aga_holiday,
0 as factor_hol_deduction,0 as firm_salary,0 as salary_allocation_pct, 0 as amount_above_6g,0 as amount_below_6g,0 as holiday_base, 
0 as jan,0 as feb, 0 as mar, 0 as apr, 0 as may, 0 as jun, 0 as jul, 0 as aug, 0 as sep, 0 as oct, 0 as nov, 0 as dec,
a.tax_flag, a.holiday_flag, a.pension_flag, b.fk_tax_rate
FROM tbu_employments_add_on a, @employments b
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.fk_employment_id = b.pk_employment_id






UPDATE @addon SET salary_allocation_pct = c.allocation_pct
FROM @addon d, 
(SELECT a.pk_id, SUM(b.allocation_pct) as allocation_pct
FROM @addon a, tco_periodic_key b
WHERE b.period BETWEEN a.start_period AND a.end_period AND key_id = @key_id_salary and a.fk_holiday_type_id !=6
GROUP BY a.pk_id) c
WHERE d.pk_id = c.pk_id

UPDATE @addon SET salary_allocation_pct = c.allocation_pct
FROM @addon d, 
(SELECT a.pk_id, SUM(b.allocation_pct) as allocation_pct
FROM @addon a, tco_periodic_key b
WHERE b.period BETWEEN a.start_period AND a.end_period AND key_id = @key_id_salary_6w and a.fk_holiday_type_id =6
GROUP BY a.pk_id) c
WHERE d.pk_id = c.pk_id

--Calculation for vacation = 0 addon
UPDATE @addon SET salary_allocation_pct = c.allocation_pct
FROM @addon d, 
(SELECT a.pk_id, SUM(b.allocation_pct) as allocation_pct
FROM @addon a, tco_periodic_key b
WHERE b.period BETWEEN a.start_period AND a.end_period AND key_id = 13 and a.fk_holiday_type_id = 0 AND (a.start_period != 1 OR a.end_period != 12)
GROUP BY a.pk_id) c
WHERE d.pk_id = c.pk_id


UPDATE @addon SET new_amount_pension = new_yearly_budget * b.rate/100, 
factor_hol_deduction = CASE
        WHEN @param_264= 'TRUE' THEN 1-convert(numeric(18,4),fk_holiday_type_id)*5/264
        ELSE 1-convert(numeric(18,4),fk_holiday_type_id)*5/260
        END
FROM @addon a, tmd_pension_type b 
WHERE a.fk_tenant_id = b.fk_tenant_id
AND a.budget_year between b.year_from and b.year_to
AND a.pension_type = b.pension_type

UPDATE @addon SET firm_salary = amount_salary_year*factor_hol_deduction WHERE holiday_flag = 1

UPDATE @addon SET firm_salary = amount_salary_year WHERE holiday_flag = 0

UPDATE @addon SET amount_above_6g = CASE WHEN amount_salary_year > @6g_amount THEN amount_salary_year-@6g_amount else 0 end
WHERE fk_holiday_type_id = 6

UPDATE @addon SET amount_below_6g =amount_salary_year-amount_above_6g
WHERE fk_holiday_type_id = 6

-- Below query is changed so that @holiday_rate_high is used instead of low. this is a bug but makes it consistent with the application The correct logic is: 

--UPDATE @addon SET holiday_base = (amount_above_6g*@holiday_rate_low*factor_hol_deduction)+(amount_below_6g*@holiday_rate_high*factor_hol_deduction)
--WHERE fk_holiday_type_id = 6

UPDATE @addon SET holiday_base = (amount_above_6g*@holiday_rate_high*factor_hol_deduction)+(amount_below_6g*@holiday_rate_high*factor_hol_deduction)
WHERE fk_holiday_type_id = 6

UPDATE @addon SET holiday_base = firm_salary * @holiday_rate_low
WHERE fk_holiday_type_id = 5

UPDATE @addon SET 
jan = holiday_base * @all_pct_1,
feb = holiday_base * @all_pct_2,
mar = holiday_base * @all_pct_3,
apr = holiday_base * @all_pct_4,
may = holiday_base * @all_pct_5,
jun = holiday_base * @all_pct_6,
jul = holiday_base * @all_pct_7,
aug = holiday_base * @all_pct_8,
sep = holiday_base * @all_pct_9,
oct = holiday_base * @all_pct_10,
nov = holiday_base * @all_pct_11,
dec = holiday_base * @all_pct_12
WHERE fk_holiday_type_id != 6

UPDATE @addon SET 
jan = holiday_base * @all_pct_1_6w,
feb = holiday_base * @all_pct_2_6w,
mar = holiday_base * @all_pct_3_6w,
apr = holiday_base * @all_pct_4_6w,
may = holiday_base * @all_pct_5_6w,
jun = holiday_base * @all_pct_6_6w,
jul = holiday_base * @all_pct_7_6w,
aug = holiday_base * @all_pct_8_6w,
sep = holiday_base * @all_pct_9_6w,
oct = holiday_base * @all_pct_10_6w,
nov = holiday_base * @all_pct_11_6w,
dec = holiday_base * @all_pct_12_6w
WHERE fk_holiday_type_id = 6


UPDATE @addon SET jan = 0  
WHERE start_period > 1

UPDATE @addon SET jan = 0, feb = 0   
WHERE start_period > 2

UPDATE @addon SET jan = 0, feb = 0, mar = 0   
WHERE start_period > 3

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0    
WHERE start_period > 4

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0     
WHERE start_period > 5

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0      
WHERE start_period > 6

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0       
WHERE start_period > 7

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0        
WHERE start_period > 8

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0        
WHERE start_period > 9

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0         
WHERE start_period > 10

UPDATE @addon SET jan = 0, feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0         
WHERE start_period > 11

UPDATE @addon SET feb = 0, mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 2

UPDATE @addon SET mar = 0, apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 3

UPDATE @addon SET apr = 0, may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 4

UPDATE @addon SET may = 0, jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 5

UPDATE @addon SET jun = 0, jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 6

UPDATE @addon SET jul = 0, aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 7

UPDATE @addon SET aug = 0, sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 8

UPDATE @addon SET sep = 0, oct = 0, nov = 0, [dec]=0
WHERE end_period < 9

UPDATE @addon SET oct = 0, nov = 0, [dec]=0
WHERE end_period < 10

UPDATE @addon SET nov = 0, [dec]=0
WHERE end_period < 11

UPDATE @addon SET [dec]=0
WHERE end_period < 12

UPDATE @addon SET new_amount_holiday = jan+feb+mar+apr+may+jun+jul+aug+sep+oct+nov+dec


UPDATE @addon SET new_amount_aga_salary = firm_salary * fk_tax_rate * salary_allocation_pct / 100, new_amount_aga_pension = new_amount_pension*fk_tax_rate, new_amount_aga_holiday = new_amount_holiday*fk_tax_rate
--FROM @addon a, gco_tenants t, gmd_emp_tax_rates tr 
--WHERE a.fk_tenant_id = t.pk_id
--AND t.municipality_id = tr.fk_municipality_id 

UPDATE @addon SET new_amount_aga_salary = 0 WHERE tax_flag = 0

UPDATE @addon SET new_amount_pension = 0, new_amount_aga_pension = 0
WHERE pension_flag = 0 

UPDATE @addon SET new_amount_aga_holiday = 0, new_amount_holiday = 0 
WHERE holiday_flag = 0


INSERT INTO #TEMP_EMPLOYMENTS (fk_tenant_id, budget_year, pk_employment_id,addon_id, fk_account_code,fk_department_code,
org_id_2, org_id_3, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
new_yearly_budget_salary, new_yearly_budget_addon,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,firm_salary, salary_allocation_pct)
SELECT a.fk_tenant_id, budget_year, pk_employment_id,0 as addon_id, fk_account_code,a.fk_department_code,
oh.org_id_2, oh.org_id_3, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
new_yearly_budget as  new_yearly_budget_salary, 0 as new_yearly_budget_addon,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,firm_salary, salary_allocation_pct 
FROM @employments a
LEFT JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version


INSERT INTO #TEMP_EMPLOYMENTS (fk_tenant_id, budget_year, pk_employment_id,addon_id, fk_account_code,fk_department_code, 
org_id_2, org_id_3, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,amount_pension,amount_holiday,
amount_aga_salary,amount_aga_pension,amount_aga_holiday,
new_yearly_budget_salary, new_yearly_budget_addon,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,firm_salary, salary_allocation_pct)

SELECT a.fk_tenant_id, budget_year, pk_employment_id,a.pk_id as addon_id, fk_account_code,a.fk_department_code, 
oh.org_id_2, oh.org_id_3, pension_type,
amount_salary_year,start_period,end_period,position_pct,fk_holiday_type_id,yearly_budget,0 as amount_pension,0 as amount_holiday,
0 as amount_aga_salary,0 as amount_aga_pension,0 as amount_aga_holiday,
0 as new_yearly_budget_salary, new_yearly_budget as new_yearly_budget_addon,new_amount_pension,new_amount_holiday,
new_amount_aga_salary,new_amount_aga_pension,new_amount_aga_holiday,firm_salary, salary_allocation_pct 
FROM @addon a
LEFT JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version





IF OBJECT_ID('tempdb..#temp_staff_check') IS NOT NULL
BEGIN
DROP TABLE #temp_staff_check
END


UPDATE #TEMP_EMPLOYMENTS SET new_estimated_employment = new_yearly_budget_salary+new_yearly_budget_addon+new_amount_pension+new_amount_aga_pension+new_amount_holiday+new_amount_aga_holiday+new_amount_aga_salary,
existing_employments_total = yearly_budget+amount_pension+amount_aga_pension+amount_holiday+amount_aga_holiday+amount_aga_salary,
new_estimated_tbu = convert (dec(18,2), (firm_salary * salary_allocation_pct / 100+new_amount_pension+new_amount_aga_pension+new_amount_holiday+new_amount_aga_holiday+new_amount_aga_salary)) 

--SELECT * FROM @employments

SELECT fk_tenant_id, budget_year, pk_employment_id, fk_department_code,org_id_2, org_id_3,
SUM(new_yearly_budget_salary+new_yearly_budget_addon+new_amount_pension+new_amount_aga_pension+new_amount_holiday+new_amount_aga_holiday+new_amount_aga_salary) as new_estimated_employment,
SUM(yearly_budget+amount_pension+amount_aga_pension+amount_holiday+amount_aga_holiday+amount_aga_salary) as existing_employments_total,
convert (dec(18,2), SUM(firm_salary * salary_allocation_pct / 100+new_amount_pension+new_amount_aga_pension+new_amount_holiday+new_amount_aga_holiday+new_amount_aga_salary)) as new_estimated_tbu,
convert(dec(18,2), 0) as existing_budget_tbu, convert(dec(18,2), 0) as discr_employments, convert(dec(18,2), 0) as discr_budget
INTO #temp_staff_check
FROM #TEMP_EMPLOYMENTS
GROUP BY fk_tenant_id, budget_year, pk_employment_id, fk_department_code,org_id_2, org_id_3


IF @tenant_id != 0 

BEGIN

INSERT INTO @budget_table (fk_tenant_id, budget_year, fk_employment_id, department_code, org_id_2, org_id_3, amount_year_1)
SELECT a.fk_tenant_id,a.budget_year,a.fk_employment_id,a.department_code,oh.org_id_2,oh.org_id_3,sum(a.amount_year_1)
FROM tbu_trans_detail a
LEFT JOIN tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version
WHERE budget_year = @budget_year AND fk_employment_id != 0 AND a.fk_tenant_id = @tenant_id
GROUP BY a.fk_tenant_id,a.budget_year,a.fk_employment_id,a.department_code,oh.org_id_2,oh.org_id_3

END


IF @tenant_id = 0 

BEGIN



INSERT INTO @budget_table (fk_tenant_id, budget_year, fk_employment_id, department_code, org_id_2, org_id_3, amount_year_1)
SELECT a.fk_tenant_id,a.budget_year,a.fk_employment_id,a.department_code,oh.org_id_2,oh.org_id_3,sum(a.amount_year_1) as amount_year_1
FROM tbu_trans_detail a
LEFT JOIN tco_org_hierarchy oh ON a.department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version
WHERE budget_year = @budget_year AND fk_employment_id != 0
GROUP BY a.fk_tenant_id,a.budget_year,a.fk_employment_id,a.department_code,oh.org_id_2,oh.org_id_3

END


UPDATE #temp_staff_check SET existing_budget_tbu = b.amount_year_1
FROM #temp_staff_check a, @budget_table b
WHERE a.pk_employment_id = b.fk_employment_id
AND a.fk_department_code = b.department_code
AND a.fk_tenant_id = b.fk_tenant_id
AND a.budget_year= b.budget_year


INSERT INTO #temp_staff_check (fk_tenant_id,budget_year,pk_employment_id,fk_department_code,org_id_2,org_id_3,new_estimated_employment,existing_employments_total,new_estimated_tbu,existing_budget_tbu,discr_employments,discr_budget)
SELECT a.fk_tenant_id,a.budget_year,a.fk_employment_id,a.department_code,a.org_id_2,a.org_id_3,0 as new_estimated_employment,0 as existing_employments_total,0 as new_estimated_tbu,sum(a.amount_year_1) as existing_budget_tbu,0 as discr_employments,0 as discr_budget
FROM @budget_table a
WHERE NOT exists (SELECT * FROM  #temp_staff_check  b WHERE a.fk_employment_id = b.pk_employment_id
AND a.department_code = b.fk_department_code
AND a.fk_tenant_id = b.fk_tenant_id
AND a.budget_year = b.budget_year)
GROUP BY a.fk_tenant_id,a.budget_year,a.fk_employment_id,a.department_code,a.org_id_2,a.org_id_3



UPDATE #temp_staff_check SET discr_employments = existing_employments_total-new_estimated_employment, discr_budget = existing_budget_tbu-new_estimated_tbu 

--SELECT pk_employment_id, SUM(new_estimated_employment) AS new_estimated_employment, SUM(existing_employments_total) AS existing_employments_total, SUM(new_estimated_tbu) AS new_estimated_tbu, SUM(existing_budget_tbu) AS existing_budget_tbu, SUM(discr_employments) AS discr_employments,SUM(discr_budget) AS discr_budget
--FROM #temp_staff_check
--WHERE abs(discr_employments) > 10 or abs(discr_budget) > 20
--GROUP BY pk_employment_id


IF NOT EXISTS(SELECT name 
          FROM sysobjects 
          WHERE name = @table_name AND xtype = 'U')

BEGIN
    EXEC (
'SELECT *
INTO '+ @table_name + 
' FROM  #TEMP_EMPLOYMENTS WHERE 1=2'
)

END


IF NOT EXISTS(SELECT name 
          FROM sysobjects 
          WHERE name = @table_name_2 AND xtype = 'U')

BEGIN

EXEC (
'SELECT *
INTO '+ @table_name_2 + 
' FROM  #temp_staff_check WHERE 1=2'
)

END


DELETE FROM TempVerifyStaffplanning WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year
DELETE FROM TempVerifyStaffplanningsummary WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year

INSERT INTO TempVerifyStaffplanning SELECT * FROM #TEMP_EMPLOYMENTS
INSERT INTO TempVerifyStaffplanningsummary SELECT * FROM #temp_staff_check



print 'To get details query from table updated: SELECT * FROM ' + @table_name + ' ORDER BY pk_employment_id, addon_id'
print ' '
print 'To get overview query from table:'
print ' '
print 'SELECT fk_tenant_id, budget_year,pk_employment_id,SUM(new_estimated_employment)new_estimated_employment, SUM(existing_employments_total)existing_employments_total, SUM(new_estimated_tbu)new_estimated_tbu, SUM(existing_budget_tbu)existing_budget_tbu, SUM(discr_employments)discr_employments,SUM(discr_budget)discr_budget '
print 'FROM TempVerifyStaffplanningsummary WHERE abs(discr_employments) > 10 or abs(discr_budget) > 20'
print 'group by fk_tenant_id, budget_year,pk_employment_id'


--SELECT * FROM @addon where pk_employment_id = 290112
GO


