CREATE OR ALTER PROCEDURE [dbo].[prcPoliticalSimulation2B_2021]      
 @tenant_id int,       
 @budget_year int,      
 @user_id int,      
 @proposal_id UNIQUEIDENTIFIER       
      
      
AS      
      
--DECLARE @tenant_id int = 4097, @budget_year int = 2022, @user_id int = 2097999, @proposal_id UNIQUEIDENTIFIER = newid()      
--SELECT @proposal_id      
DECLARE @include_Inv_blist BIT = (SELECT include_Inv_blist from  [tps_admin_config] WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year)      
      
DECLARE @org_version VARCHAR(24)= (select pk_org_version FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND @budget_year * 100 +1 BETWEEN period_from AND period_to)      
DECLARE @langstring_project NVARCHAR(255)       
DECLARE @param_inv_grouping VARCHAR(255)      
DECLARE @default_change_id INT      
DECLARE @param_value_18 nvarchar(500)      
DECLARE @year INT = @budget_year      
DECLARE @language VARCHAR(10)      
DECLARE @sub_header_id VARCHAR(25)       
DECLARE @aggregate_id VARCHAR(25)       
DECLARE @langstring_1020 NVARCHAR(500)       
DECLARE @langstring_1030 NVARCHAR(500)       
DECLARE @langstring_1040 NVARCHAR(500)       
      
SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @tenant_id)      
      
      
SET @langstring_1020 =       
(      
SELECT isnull(s3.description, ISNULL(s2.description, s.description)) FROM gco_language_strings s      
LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language      
LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @tenant_id      
WHERE s.id = 'PS_2b_change_1020' AND s.language = @language)      
      
SET @langstring_1030 =       
(      
SELECT isnull(s3.description, ISNULL(s2.description, s.description)) FROM gco_language_strings s      
LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language      
LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @tenant_id      
WHERE s.id = 'PS_2b_change_1030' AND s.language = @language)      
      
SET @langstring_1040 =       
(      
SELECT isnull(s3.description, ISNULL(s2.description, s.description)) FROM gco_language_strings s      
LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language      
LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @tenant_id      
WHERE s.id = 'PS_2b_change_1040' AND s.language = @language)      
      
      
      
DECLARE @tbl_reporting_line as table (report varchar(50)      
, fk_kostra_account_code VARCHAR(25)      
, line_group_id INT      
, line_group varchar(200)      
, line_item_id INT      
, line_item varchar(150))      
      
      
INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)      
SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id,       
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),      
line_item_id,      
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))      
FROM gmd_reporting_line rl      
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language      
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language      
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @tenant_id AND tg.Language = @language      
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @tenant_id AND tI.Language = @language      
      
      
PRINT 'Get the 2B setup'      
      
SET @sub_header_id = (SELECT min(grouping_id) from tco_table_grouping WHERE fk_tenant_id = @tenant_id AND table_id = 'BudgetForms2B2020' AND level_id = 1)      
SET @aggregate_id =  (SELECT min(grouping_id) from tco_table_grouping WHERE fk_tenant_id = @tenant_id AND table_id = 'BudgetForms2B2020' AND level_id = 2)      
      
IF @sub_header_id IS NULL AND @aggregate_id IS NULL      
BEGIN      
      
SET @sub_header_id = 'org_id_2'      
SET @aggregate_id = 'fk_main_project_code'      
      
END      
      
      
IF @sub_header_id IS NOT NULL AND @aggregate_id IS NULL      
BEGIN      
      
SET @aggregate_id = @sub_header_id      
SET @sub_header_id = 'ZZ'      
      
END      
      
IF @sub_header_id IS NULL AND @aggregate_id IS NOT NULL      
BEGIN      
      
SET @sub_header_id = 'ZZ'      
      
END      
      
      
SET @param_value_18 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id AND active = 1 AND param_name = 'FINPLAN_INVESTMENT_LEVEL')      
      
DECLARE @defAccountCode NVARCHAR(25)         
SELECT @defAccountCode =  MIN(ac.pk_account_code) FROM tco_accounts ac      
JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id = 1010      
WHERE ac.pk_tenant_id = @tenant_id AND @year BETWEEN DATEPART(YEAR, ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) AND ac.isActive = 1      
      
DECLARE @defAccountCode_1020 NVARCHAR(25)         
SELECT @defAccountCode_1020 =  MIN(ac.pk_account_code) FROM tco_accounts ac      
JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id = 1020      
WHERE ac.pk_tenant_id = @tenant_id  AND @year BETWEEN DATEPART(YEAR, ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) AND ac.isActive = 1      
      
DECLARE @defAccountCode_1030 NVARCHAR(25)         
SELECT @defAccountCode_1030 =  MIN(ac.pk_account_code) FROM tco_accounts ac      
JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id = 1030      
WHERE ac.pk_tenant_id = @tenant_id  AND @year BETWEEN DATEPART(YEAR, ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) AND ac.isActive = 1      
      
DECLARE @defAccountCode_1040 NVARCHAR(25)         
SELECT @defAccountCode_1040 =  MIN(ac.pk_account_code) FROM tco_accounts ac      
JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND report = '55_OVINV' AND line_item_id = 1040      
WHERE ac.pk_tenant_id = @tenant_id  AND @year BETWEEN DATEPART(YEAR, ac.dateFrom) AND DATEPART(YEAR, ac.dateTo) AND ac.isActive = 1      
      
      
SET @langstring_project =       
(      
SELECT isnull(s3.description, ISNULL(s2.description, s.description)) FROM gco_language_strings s      
LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language      
LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @tenant_id      
WHERE s.id = 'BudForm_2B_projectmissing' AND s.language = @language)      
      
SET @param_inv_grouping = (SELECT param_value FROM vw_tco_parameters WHERE param_name = '2B_TABS_GROUPING' AND fk_tenant_id = @tenant_id)      
      
IF @param_inv_grouping IS NULL       
BEGIN      
SET @param_inv_grouping = 'project_code'      
END      
      
      
CREATE TABLE #temp_table_1      
 (fk_tenant_id INT NOT NULL,       
    fk_account_code NVARCHAR(25) NOT NULL,       
 forecast_period INT NULL DEFAULT 0,      
    fk_department_code NVARCHAR(25) NOT NULL,       
    fk_function_code NVARCHAR(25) NOT NULL,       
    fk_project_code NVARCHAR(25) NOT NULL,       
 free_dim_1 NVARCHAR(25) NOT NULL,       
 free_dim_2 NVARCHAR(25) NOT NULL,       
 free_dim_3 NVARCHAR(25) NOT NULL,       
 free_dim_4 NVARCHAR(25) NOT NULL,       
    actual_amt_year DECIMAL(18, 2) NOT NULL,       
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,      
    actual_amt_year_minus2 DECIMAL(18, 2) NOT NULL,      
 org_bud_amt_year DECIMAL (18, 2) NOT NULL,      
 org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,      
 revised_bud_amt_year DECIMAL (18, 2) NOT NULL,      
 revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,      
 finplan_year_1_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_2_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_3_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_4_amount DECIMAL (18, 2) NOT NULL,      
    finplan_year_5_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_6_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_7_amount DECIMAL (18, 2) NOT NULL,      
    finplan_year_8_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_9_amount DECIMAL (18, 2) NOT NULL,      
 finplan_year_10_amount DECIMAL (18, 2) NOT NULL,      
 forecast_amount DECIMAL (18, 2) NOT NULL,      
 budget_period DECIMAL (18, 2) NULL DEFAULT 0,      
 accounting_period DECIMAL (18, 2) NULL DEFAULT 0,      
 accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,      
 budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,      
 accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,      
 deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,      
 deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,      
 budget_change DECIMAL (18, 2) NULL DEFAULT 0,      
 deviation_action DECIMAL (18, 2) NULL DEFAULT 0,      
 forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,      
 deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,      
 [fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',      
 action_name NVARCHAR(150) NULL,      
 action_type INT NULL,      
 line_order INT NULL,      
 org_budget_flag INT NOT NULL DEFAULT 1,      
 inv_fk_org_id NVARCHAR(25)  NULL,       
 inv_org_name NVARCHAR(255)  NULL,      
 fk_investment_id INT NULL,       
 fk_portfolio_code NVARCHAR(25) NULL,      
 inv_line_group INT NULL,      
 [fk_adjustment_code] NVARCHAR(25) DEFAULT '' NOT NULL,      
 [fk_change_id] INT NOT NULL DEFAULT 0,      
 [year_1_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_2_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_3_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_4_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
    [year_5_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_6_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_7_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
    [year_8_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_9_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_10_vat_base] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_1_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_2_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_3_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_4_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
    [year_5_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_6_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_7_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
    [year_8_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_9_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
 [year_10_vat_cost] [decimal](18, 2) NOT NULL DEFAULT 0,      
    [vat_refund_1] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_2] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_3] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_4] decimal(8,2) NOT NULL DEFAULT -1,      
    [vat_refund_5] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_6] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_7] decimal(8,2) NOT NULL DEFAULT -1,      
    [vat_refund_8] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_9] decimal(8,2) NOT NULL DEFAULT -1,      
 [vat_refund_10] decimal(8,2) NOT NULL DEFAULT -1,      
 [inv_status] INT NOT NULL DEFAULT 100,      
 [fk_fdv_codes] [nvarchar](25) NULL,      
    [approval_cost] decimal(18,2) NOT NULL DEFAULT 0      
 )      
      
      
CREATE TABLE #valid_accounts(       
 [pk_id] INT NOT NULL IDENTITY,       
 pk_tenant_id INT NOT NULL,      
    pk_account_code NVARCHAR(25) NOT NULL,      
    fk_kostra_account_code NVARCHAR(25) NOT NULL)      
      
INSERT INTO #valid_accounts (pk_tenant_id, pk_account_code, fk_kostra_account_code)      
      
      
SELECT DISTINCT pk_tenant_id, pk_account_code, fk_kostra_account_code FROM       
(SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code      
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @year BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)      
UNION      
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code      
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @year-1 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)      
UNION       
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code      
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @year-2 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)      
) s      
      
      
CREATE TABLE #valid_projects(       
 [pk_id] INT NOT NULL IDENTITY,       
 fk_tenant_id INT NOT NULL,      
    pk_project_code NVARCHAR(25) NOT NULL,      
    fk_main_project_code NVARCHAR(25) NOT NULL,      
    project_name NVARCHAR(250) NOT NULL)      
      
CREATE UNIQUE INDEX #_IND_valid_projects ON #valid_projects (pk_project_code, fk_tenant_id)      
      
INSERT INTO #valid_projects ( fk_tenant_id, pk_project_code, fk_main_project_code, project_name)      
SELECT DISTINCT fk_tenant_id, pk_project_code, '' as fk_main_project_code, '' AS project_name  FROM       
(SELECT fk_tenant_id,pk_project_code      
FROM tco_projects WHERE fk_tenant_id = @tenant_id AND @year BETWEEN DATEPART(YEAR,date_From) AND DATEPART(YEAR,date_to)      
UNION      
SELECT fk_tenant_id,pk_project_code      
FROM tco_projects WHERE fk_tenant_id = @tenant_id AND @year-1 BETWEEN DATEPART(YEAR,date_From) AND DATEPART(YEAR,date_to)      
UNION       
SELECT fk_tenant_id,pk_project_code      
FROM tco_projects WHERE fk_tenant_id = @tenant_id AND @year-2 BETWEEN DATEPART(YEAR,date_From) AND DATEPART(YEAR,date_to)      
) s      
      
UPDATE a SET a.fk_main_project_code = b.fk_main_project_code, a.project_name = b.project_name      
from #valid_projects a      
JOIN tco_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_project_code = b.pk_project_code AND @year BETWEEN DATEPART(YEAR,b.date_From) AND DATEPART(YEAR,b.date_to)      
WHERE a.fk_main_project_code = ''      
      
UPDATE a SET a.fk_main_project_code = b.fk_main_project_code, a.project_name = b.project_name      
from #valid_projects a      
JOIN tco_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_project_code = b.pk_project_code AND @year-1 BETWEEN DATEPART(YEAR,b.date_From) AND DATEPART(YEAR,b.date_to)      
WHERE a.fk_main_project_code = ''      
      
UPDATE a SET a.fk_main_project_code = b.fk_main_project_code, a.project_name = b.project_name      
from #valid_projects a      
JOIN tco_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_project_code = b.pk_project_code  AND @year-2 BETWEEN DATEPART(YEAR,b.date_From) AND DATEPART(YEAR,b.date_to)      
WHERE a.fk_main_project_code = ''      
      
      
      
CREATE TABLE #valid_mainprojects(       
 [pk_id] INT NOT NULL IDENTITY,       
 fk_tenant_id INT NOT NULL,      
    pk_main_project_code NVARCHAR(25) NOT NULL,      
    main_project_name NVARCHAR(100) NOT NULL,      
 fk_department_code NVARCHAR(25) NOT NULL,      
 fk_function_code NVARCHAR(25) NOT NULL)      
      
CREATE UNIQUE INDEX #_IND_valid_mainprojects ON #valid_mainprojects (pk_main_project_code, fk_tenant_id)      
      
INSERT INTO #valid_mainprojects ( fk_tenant_id, pk_main_project_code, main_project_name,fk_department_code, fk_function_code)      
SELECT DISTINCT fk_tenant_id, pk_main_project_code, '' as main_project_name,'' as fk_department_code,'' as fk_function_code FROM       
(SELECT fk_tenant_id,pk_main_project_code      
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @year BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)      
UNION      
SELECT fk_tenant_id,pk_main_project_code      
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @year-1 BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)      
UNION       
SELECT fk_tenant_id,pk_main_project_code      
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @year-2 BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)      
) s      
      
UPDATE a SET a.main_project_name = b.main_project_name, a.fk_department_code = ISNULL(b.fk_department_code,''), a.fk_function_code = ISNULL(b.fk_function_code, '')      
from #valid_mainprojects a      
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @year BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)      
WHERE a.main_project_name = ''      
      
UPDATE a SET a.main_project_name = b.main_project_name, a.fk_department_code = ISNULL(b.fk_department_code,''), a.fk_function_code = ISNULL(b.fk_function_code, '')      
from #valid_mainprojects a      
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @year-1 BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)      
WHERE a.main_project_name = ''      
      
UPDATE a SET a.main_project_name = b.main_project_name, a.fk_department_code = ISNULL(b.fk_department_code,''), a.fk_function_code = ISNULL(b.fk_function_code, '')      
from #valid_mainprojects a      
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @year-2 BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)      
WHERE a.main_project_name = ''      
      
SELECT oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,      
oh.org_name_1, oh.org_name_2, oh.org_name_3, oh.org_name_4, oh.org_name_5,      
oh.fk_department_code, p.pk_project_code      
INTO #org_data      
FROM tco_org_hierarchy oh       
LEFT JOIN #valid_mainprojects mp ON oh.fk_tenant_id = mp.fk_tenant_id AND oh.fk_department_code = mp.fk_department_code --AND @year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)       
LEFT JOIN #valid_projects p ON mp.fk_tenant_id = p.fk_tenant_id  AND p.fk_main_project_code = mp.pk_main_project_code --and @year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)      
WHERE oh.fk_tenant_id = @tenant_id AND oh.fk_org_version = @org_version      
      
SELECT sv.service_id_1, sv.service_id_2,sv.service_id_3, sv.service_id_4, sv.service_id_5,      
sv.service_name_1, sv.service_name_2, sv.service_name_3, sv.service_name_4, sv.service_name_5,      
sv.fk_function_code, p.pk_project_code      
INTO #service_data      
FROM tco_service_values sv       
LEFT JOIN #valid_mainprojects mp ON sv.fk_tenant_id = mp.fk_tenant_id AND sv.fk_function_code = mp.fk_function_code       
LEFT JOIN #valid_projects p ON mp.fk_tenant_id = p.fk_tenant_id  AND p.fk_main_project_code = mp.pk_main_project_code       
WHERE sv.fk_tenant_id = @tenant_id      
      
      
RAISERROR ('START : Fetch data', 0, 1) WITH NOWAIT      
PRINT convert(nvarchar(19),SYSDATETIME())        
      
INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code,       
free_dim_1, free_dim_2, free_dim_3, free_dim_4,      
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,      
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,      
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount, finplan_year_5_amount, finplan_year_6_amount,      
finplan_year_7_amount, finplan_year_8_amount, finplan_year_9_amount, finplan_year_10_amount, forecast_amount, fk_prog_code)      
SELECT fk_tenant_id, fk_account_code, department_code, fk_function_code,      
free_dim_1, free_dim_2, free_dim_3, free_dim_4,      
fk_project_code, 0 as actual_amt_year,0 as actual_amt_last_year,sum(amount) AS actual_amt_year_minus2,      
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS  revised_bud_amt_year, 0 AS revised_bud_amt_last_year,      
0 AS finplan_year_1_amount, 0 AS finplan_year_2_amount, 0 AS  finplan_year_3_amount, 0 AS finplan_year_4_amount, 0 AS  finplan_year_5_amount,       
0 AS  finplan_year_6_amount, 0 AS  finplan_year_7_amount, 0 AS  finplan_year_8_amount, 0 AS  finplan_year_9_amount, 0 AS  finplan_year_10_amount, 0 AS forecast_amount,      
'' as fk_prog_code      
FROM tfp_accounting_data a      
 JOIN #valid_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code       
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'      
WHERE fk_tenant_id = @tenant_id      
AND gl_year = @year-2      
GROUP BY  fk_tenant_id, fk_account_code, department_code, fk_function_code,fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.fk_prog_code      
      
UPDATE h SET h.fk_prog_code = p.fk_prog_code      
FROM #temp_table_1 h      
JOIN  tco_projects p on  @year-2 between datepart(YEAR, p.date_from) AND datepart(year, p.date_to)      
AND p.fk_tenant_id = h.fk_tenant_id AND h.fk_project_code = p.pk_project_code AND p.fk_prog_code IS NOT NULL      
      
RAISERROR ('FINISH: Fetch data tfp_accounting_data 3', 0, 1) WITH NOWAIT      
PRINT convert(nvarchar(19),SYSDATETIME())      
      
      
      
      
UPDATE #temp_table_1 SET  org_bud_amt_year = 0, org_bud_amt_last_year = 0, revised_bud_amt_year = 0,revised_bud_amt_last_year=0      
FROM #temp_table_1 a, #valid_accounts b, gco_kostra_accounts c      
WHERE  a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --AND a.year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)      
AND b.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'investment'      
      
      
select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,       
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,ISNULL(pt.fk_change_id,0) AS fk_change_id,       
pt.fk_alter_code, pt.fk_adjustment_code,isnull(p.fk_prog_code,'1') AS fk_prog_code,      
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function,       
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,      
adjustment_code_status = CASE          
WHEN UAD.status = 1 THEN 1      
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @year THEN 1      
ELSE 0      
END, ps.fk_fdv_codes,      
vat_base = CASE WHEN ac.fk_kostra_account_code != '0429' THEN SUM(pt.amount*pt.vat_refund/100) ELSE SUM(pt.amount)*-1 END,      
vat_cost = CASE WHEN ac.fk_kostra_account_code = '0429' THEN SUM(pt.amount) ELSE 0 END,      
vat_refund = pt.vat_refund      
INTO #inv_hlptab      
FROM tfp_proj_transactions PT      
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)      
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)      
JOIN #valid_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code      
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code      
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1      
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)      
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year from tfp_budget_changes      
        where fk_tenant_id = @tenant_id      
        and budget_year < @year      
        UNION      
        ((SELECT tf.fk_tenant_id, tf.pk_change_id, tf.org_budget_flag, tf.budget_year        
          FROM tfp_budget_changes tf        
          JOIN tco_budget_phase tc         
            ON   tf.fk_tenant_id = tc.fk_tenant_id      
            AND  tf.fk_budget_phase_id = tc.pk_budget_phase_id     
          WHERE  tf.fk_tenant_id = @tenant_id        
            AND  tf.budget_year = @year and         
          tc.sort_order <= (        
           SELECT sort_order        
           FROM tco_budget_phase tp    
          JOIN  tps_admin_config ta         
           ON   ta.fk_tenant_id = tp.fk_tenant_id     
           AND  ta.budgetphase_id = tp.pk_budget_phase_id    
           WHERE ta.fk_tenant_id = @tenant_id and ta.budget_year = @year) )        
      ))    BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id      
where PT.fk_tenant_id = @tenant_id      
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,       
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,      
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,      
UAD.status, UAD.include_in_calculation,BC.budget_year, ps.fk_fdv_codes, ac.fk_kostra_account_code, pt.vat_refund      
      
DELETE FROM #inv_hlptab WHERE adjustment_code_status = 0      
      
 SET @default_change_id = (      
 SELECT top 1 pk_change_id FROM tfp_budget_changes ch      
 JOIN tco_budget_phase ph ON ch.fk_tenant_id = ph.fk_tenant_id AND ch.fk_budget_phase_id = ph.pk_budget_phase_id      
 WHERE ch.fk_tenant_id = @tenant_id AND ch.budget_year = @year AND ch.org_budget_flag = 1      
 ORDER BY ph.sort_order, ch.pk_change_id)      
      
    if @default_change_id is null      
    begin      
    set @default_change_id = 0      
    end      
      
      
 UPDATE a SET a.fk_change_id = @default_change_id      
 FROM #inv_hlptab a      
 JOIN tfp_budget_changes ch ON ch.fk_tenant_id = a.fk_tenant_id AND a.fk_change_id = ch.pk_change_id and ch.budget_year < @year      
 ;       
      
    -- START New logic to generate vat refund ---      
      
          
 SELECT a.fk_tenant_id, a.year, a.pk_main_project_code,a.vat_refund, sum(a.amount) as finplan_amount,       
 convert(dec(18,2),0) as total_amount, weighted_vat = convert(dec(18,2),0)      
 INTO #vat_calc      
 FROM #inv_hlptab a      
 JOIN #valid_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code       
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV' AND rl.line_item_id = 1010      
 WHERE year != -1 AND a.pk_main_project_code is not null AND a.org_budget_flag = 1      
 GROUP BY a.fk_tenant_id, a.year, a.pk_main_project_code,a.vat_refund      
      
      
update a set a.total_amount = b.total_amount      
FROM #vat_calc a      
 JOIN (      
 SELECT fk_tenant_id, year, pk_main_project_code, sum(finplan_amount) as total_amount      
 FROM #vat_calc      
 GROUP BY fk_tenant_id, year, pk_main_project_code) b      
 ON a.fk_tenant_id = b.fk_tenant_id AND a.year = b.year AND a.pk_main_project_code = b.pk_main_project_code      
      
UPDATE a SET  a.weighted_vat = CASE WHEN total_amount = 0 THEN 0 ELSE  vat_refund*(finplan_amount/total_amount) END      
 FROM #vat_calc a      
      
 SELECT fk_tenant_id, pk_main_project_code,       
 vat_refund_1 = SUM(vat_refund_1),      
 vat_refund_2 = SUM(vat_refund_2),      
 vat_refund_3 = SUM(vat_refund_3),      
 vat_refund_4 = SUM(vat_refund_4),      
 vat_refund_5 = SUM(vat_refund_5),      
 vat_refund_6 = SUM(vat_refund_6),      
 vat_refund_7 = SUM(vat_refund_7),      
 vat_refund_8 = SUM(vat_refund_8),      
 vat_refund_9 = SUM(vat_refund_9),      
 vat_refund_10 = SUM(vat_refund_10)      
 INTO #vatref_table      
 FROM (      
 SELECT fk_tenant_id, pk_main_project_code,       
 vat_refund_1 =  CASE WHEN a.year = @budget_year THEN weighted_vat ELSE 0 END,      
 vat_refund_2 =  CASE WHEN a.year = @budget_year+1 THEN weighted_vat ELSE 0 END,      
 vat_refund_3 =  CASE WHEN a.year = @budget_year+2 THEN weighted_vat ELSE 0 END,      
 vat_refund_4 =  CASE WHEN a.year = @budget_year+3 THEN weighted_vat ELSE 0 END,      
 vat_refund_5 =  CASE WHEN a.year = @budget_year+4 THEN weighted_vat ELSE 0 END,      
 vat_refund_6 =  CASE WHEN a.year = @budget_year+5 THEN weighted_vat ELSE 0 END,      
 vat_refund_7 =  CASE WHEN a.year = @budget_year+6 THEN weighted_vat ELSE 0 END,      
 vat_refund_8 =  CASE WHEN a.year = @budget_year+7 THEN weighted_vat ELSE 0 END,      
 vat_refund_9 =  CASE WHEN a.year = @budget_year+8 THEN weighted_vat ELSE 0 END,      
 vat_refund_10 =  CASE WHEN a.year = @budget_year+9 THEN weighted_vat ELSE 0 END      
 FROM #vat_calc a) S      
 GROUP BY fk_tenant_id, pk_main_project_code      
       
 UPDATE #vatref_table SET vat_refund_1 = 100 WHERE vat_refund_1 > 100      
 UPDATE #vatref_table SET vat_refund_2 = 100 WHERE vat_refund_2 > 100      
 UPDATE #vatref_table SET vat_refund_3 = 100 WHERE vat_refund_3 > 100      
 UPDATE #vatref_table SET vat_refund_4 = 100 WHERE vat_refund_4 > 100      
 UPDATE #vatref_table SET vat_refund_5 = 100 WHERE vat_refund_5 > 100      
 UPDATE #vatref_table SET vat_refund_6 = 100 WHERE vat_refund_6 > 100      
 UPDATE #vatref_table SET vat_refund_7 = 100 WHERE vat_refund_7 > 100      
 UPDATE #vatref_table SET vat_refund_8 = 100 WHERE vat_refund_8 > 100      
 UPDATE #vatref_table SET vat_refund_9 = 100 WHERE vat_refund_9 > 100      
 UPDATE #vatref_table SET vat_refund_10 = 100 WHERE vat_refund_10 > 100      
      
 UPDATE #vatref_table SET vat_refund_1 = 0 WHERE vat_refund_1 < 0      
 UPDATE #vatref_table SET vat_refund_2 = 0 WHERE vat_refund_2 < 0      
 UPDATE #vatref_table SET vat_refund_3 = 0 WHERE vat_refund_3 < 0      
 UPDATE #vatref_table SET vat_refund_4 = 0 WHERE vat_refund_4 < 0      
 UPDATE #vatref_table SET vat_refund_5 = 0 WHERE vat_refund_5 < 0      
 UPDATE #vatref_table SET vat_refund_6 = 0 WHERE vat_refund_6 < 0      
 UPDATE #vatref_table SET vat_refund_7 = 0 WHERE vat_refund_7 < 0      
 UPDATE #vatref_table SET vat_refund_8 = 0 WHERE vat_refund_8 < 0      
 UPDATE #vatref_table SET vat_refund_9 = 0 WHERE vat_refund_9 < 0      
 UPDATE #vatref_table SET vat_refund_10 = 0 WHERE vat_refund_10 < 0      
      
      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_2 WHERE vat_refund_1 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_3 WHERE vat_refund_1 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_4 WHERE vat_refund_1 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_5 WHERE vat_refund_1 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_6 WHERE vat_refund_1 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_7 WHERE vat_refund_1 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_8 WHERE vat_refund_1 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_9 WHERE vat_refund_1 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_1 = vat_refund_10 WHERE vat_refund_1 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_1 WHERE vat_refund_2 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_3 WHERE vat_refund_2 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_4 WHERE vat_refund_2 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_5 WHERE vat_refund_2 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_6 WHERE vat_refund_2 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_7 WHERE vat_refund_2 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_8 WHERE vat_refund_2 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_9 WHERE vat_refund_2 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_2 = vat_refund_10 WHERE vat_refund_2 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_1 WHERE vat_refund_3 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_2 WHERE vat_refund_3 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_4 WHERE vat_refund_3 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_5 WHERE vat_refund_3 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_6 WHERE vat_refund_3 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_7 WHERE vat_refund_3 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_8 WHERE vat_refund_3 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_9 WHERE vat_refund_3 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_3 = vat_refund_10 WHERE vat_refund_3 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_1 WHERE vat_refund_4 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_2 WHERE vat_refund_4 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_3 WHERE vat_refund_4 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_5 WHERE vat_refund_4 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_6 WHERE vat_refund_4 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_7 WHERE vat_refund_4 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_8 WHERE vat_refund_4 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_9 WHERE vat_refund_4 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_4 = vat_refund_10 WHERE vat_refund_4 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_1 WHERE vat_refund_5 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_2 WHERE vat_refund_5 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_3 WHERE vat_refund_5 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_4 WHERE vat_refund_5 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_6 WHERE vat_refund_5 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_7 WHERE vat_refund_5 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_8 WHERE vat_refund_5 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_9 WHERE vat_refund_5 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_5 = vat_refund_10 WHERE vat_refund_5 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_1 WHERE vat_refund_6 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_2 WHERE vat_refund_6 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_3 WHERE vat_refund_6 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_4 WHERE vat_refund_6 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_5 WHERE vat_refund_6 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_7 WHERE vat_refund_6 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_8 WHERE vat_refund_6 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_9 WHERE vat_refund_6 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_6 = vat_refund_10 WHERE vat_refund_6 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_1 WHERE vat_refund_7 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_2 WHERE vat_refund_7 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_3 WHERE vat_refund_7 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_4 WHERE vat_refund_7 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_5 WHERE vat_refund_7 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_6 WHERE vat_refund_7 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_8 WHERE vat_refund_7 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_9 WHERE vat_refund_7 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_7 = vat_refund_10 WHERE vat_refund_7 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_1 WHERE vat_refund_8 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_2 WHERE vat_refund_8 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_3 WHERE vat_refund_8 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_4 WHERE vat_refund_8 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_5 WHERE vat_refund_8 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_6 WHERE vat_refund_8 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_7 WHERE vat_refund_8 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_9 WHERE vat_refund_8 = 0 AND vat_refund_9 != 0      
 UPDATE #vatref_table SET vat_refund_8 = vat_refund_10 WHERE vat_refund_8 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_1 WHERE vat_refund_9 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_2 WHERE vat_refund_9 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_3 WHERE vat_refund_9 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_4 WHERE vat_refund_9 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_5 WHERE vat_refund_9 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_6 WHERE vat_refund_9 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_7 WHERE vat_refund_9 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_8 WHERE vat_refund_9 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_9 = vat_refund_10 WHERE vat_refund_9 = 0 AND vat_refund_10 != 0      
      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_1 WHERE vat_refund_10 = 0 AND vat_refund_1 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_2 WHERE vat_refund_10 = 0 AND vat_refund_2 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_3 WHERE vat_refund_10 = 0 AND vat_refund_3 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_4 WHERE vat_refund_10 = 0 AND vat_refund_4 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_5 WHERE vat_refund_10 = 0 AND vat_refund_5 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_6 WHERE vat_refund_10 = 0 AND vat_refund_6 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_7 WHERE vat_refund_10 = 0 AND vat_refund_7 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_8 WHERE vat_refund_10 = 0 AND vat_refund_8 != 0      
 UPDATE #vatref_table SET vat_refund_10 = vat_refund_9 WHERE vat_refund_10 = 0 AND vat_refund_9 != 0      
      
      
    -- STOP New logic to generate vat refund ---      
      
select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,       
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,@default_change_id as fk_change_id,       
pt.fk_alter_code, pt.fk_adjustment_code,isnull(p.fk_prog_code,'1') AS fk_prog_code,      
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function,       
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,      
adjustment_code_status = CASE          
WHEN UAD.status = 1 THEN 1      
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @year THEN 1      
ELSE 0      
END      
INTO #inv_hlptab_prev_year      
FROM tfp_proj_transactions PT      
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @year-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)      
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)      
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code      
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1      
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)      
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year from tfp_budget_changes      
        where fk_tenant_id = @tenant_id      
        and budget_year < @year-1      
        UNION      
        select fk_tenant_id, pk_change_id, org_budget_flag, budget_year from tfp_budget_changes      
        where fk_tenant_id = @tenant_id      
        and budget_year = @year-1      
    )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id      
where PT.fk_tenant_id = @tenant_id      
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date,       
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,      
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,      
UAD.status, UAD.include_in_calculation, BC.budget_year      
      
DELETE FROM #inv_hlptab_prev_year WHERE adjustment_code_status = 0;      
      
      
SELECT fk_tenant_id,@year AS budget_year,pk_main_project_code,main_project_name,inv_status,      
is_temp,completion_date,fk_account_code,fk_department_code,      
fk_function_code,fk_project_code,project_name,fk_change_id,      
fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept, header_function,approval_reference,       
approval_ref_url,original_finish_year,fk_fdv_codes,      
org_bud_last_year = convert(dec(18,2),0),      
rev_bud_last_year = convert(dec(18,2),0),      
year_1_amount = CASE WHEN year = @year AND org_budget_flag = 1 THEN amount else 0 end,      
year_2_amount = CASE WHEN year = @year+1 AND org_budget_flag = 1 THEN amount else 0 end,      
year_3_amount = CASE WHEN year = @year+2 AND org_budget_flag = 1 THEN amount else 0 end,      
year_4_amount = CASE WHEN year = @year+3 AND org_budget_flag = 1 THEN amount else 0 end,      
year_5_amount = CASE WHEN year = @year+4 AND org_budget_flag = 1 THEN amount else 0 end,      
year_6_amount = CASE WHEN year = @year+5 AND org_budget_flag = 1 THEN amount else 0 end,      
year_7_amount = CASE WHEN year = @year+6 AND org_budget_flag = 1 THEN amount else 0 end,      
year_8_amount = CASE WHEN year = @year+7 AND org_budget_flag = 1 THEN amount else 0 end,      
year_9_amount = CASE WHEN year = @year+8 AND org_budget_flag = 1 THEN amount else 0 end,      
year_10_amount = CASE WHEN year = @year+9 AND org_budget_flag = 1 THEN amount else 0 end,      
net_cost = CASE WHEN inv_status = 2 OR year = -1 THEN 0 else amount END,      
previously_budgeted = CASE WHEN year < @year AND year != -1 THEN amount ELSE 0 END,      
approved_cost = CASE WHEN year = -1 THEN amount else 0 end,      
      
year_1_vat_base = CASE WHEN year = @year AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_2_vat_base = CASE WHEN year = @year+1 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_3_vat_base = CASE WHEN year = @year+2 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_4_vat_base = CASE WHEN year = @year+3 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_5_vat_base = CASE WHEN year = @year+4 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_6_vat_base = CASE WHEN year = @year+5 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_7_vat_base = CASE WHEN year = @year+6 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_8_vat_base = CASE WHEN year = @year+7 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_9_vat_base = CASE WHEN year = @year+8 AND org_budget_flag = 1 THEN vat_base else 0 end,      
year_10_vat_base = CASE WHEN year = @year+9 AND org_budget_flag = 1 THEN vat_base else 0 end,      
      
year_1_vat_cost= CASE WHEN year = @year AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_2_vat_cost= CASE WHEN year = @year+1 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_3_vat_cost = CASE WHEN year = @year+2 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_4_vat_cost= CASE WHEN year = @year+3 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_5_vat_cost= CASE WHEN year = @year+4 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_6_vat_cost = CASE WHEN year = @year+5 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_7_vat_cost= CASE WHEN year = @year+6 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_8_vat_cost= CASE WHEN year = @year+7 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_9_vat_cost = CASE WHEN year = @year+8 AND org_budget_flag = 1 THEN vat_cost else 0 end,      
year_10_vat_cost= CASE WHEN year = @year+9 AND org_budget_flag = 1 THEN vat_cost else 0 end      
      
INTO #inv_hlptab2 FROM #inv_hlptab      
       
      
 INSERT INTO #inv_hlptab2 (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,inv_status,is_temp,      
 completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,      
 fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function,      
 approval_reference,approval_ref_url,original_finish_year,org_bud_last_year,rev_bud_last_year,      
 year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,      
 year_7_amount,year_8_amount,year_9_amount,year_10_amount,net_cost,previously_budgeted,approved_cost,      
 year_1_vat_base,year_2_vat_base,year_3_vat_base,year_4_vat_base,year_5_vat_base,year_6_vat_base,year_7_vat_base,year_8_vat_base,year_9_vat_base,year_10_vat_base,      
 year_1_vat_cost,year_2_vat_cost,year_3_vat_cost,year_4_vat_cost,year_5_vat_cost,year_6_vat_cost,year_7_vat_cost,year_8_vat_cost,year_9_vat_cost,year_10_vat_cost)      
SELECT fk_tenant_id,@year AS budget_year,pk_main_project_code,main_project_name,inv_status,      
is_temp,completion_date,fk_account_code,fk_department_code,      
fk_function_code,fk_project_code,project_name,fk_change_id,      
fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept, header_function,approval_reference,       
approval_ref_url,original_finish_year,      
org_bud_last_year = CASE WHEN year = @year-1 AND org_budget_flag = 1 THEN amount else 0 end,      
rev_bud_last_year = CASE WHEN year = @year-1 THEN amount else 0 end,      
year_1_amount = 0,      
year_2_amount = 0,      
year_3_amount = 0,      
year_4_amount = 0,      
year_5_amount = 0,      
year_6_amount = 0,      
year_7_amount = 0,      
year_8_amount = 0,      
year_9_amount = 0,      
year_10_amount = 0,      
net_cost = 0,      
previously_budgeted = 0,      
approved_cost = 0,      
year_1_vat_base = 0,      
year_2_vat_base = 0,      
year_3_vat_base = 0,      
year_4_vat_base = 0,      
year_5_vat_base = 0,      
year_6_vat_base = 0,      
year_7_vat_base = 0,      
year_8_vat_base = 0,      
year_9_vat_base = 0,      
year_10_vat_base = 0,      
year_1_vat_cost = 0,      
year_2_vat_cost = 0,      
year_3_vat_cost = 0,      
year_4_vat_cost = 0,      
year_5_vat_cost = 0,      
year_6_vat_cost = 0,      
year_7_vat_cost = 0,      
year_8_vat_cost = 0,      
year_9_vat_cost = 0,      
year_10_vat_cost = 0      
FROM #inv_hlptab_prev_year      
      
-- Set original budget last year      
      
--UPDATE a SET a.org_bud_last_year = 0      
--FROM #inv_hlptab2 a      
--JOIN tfp_budget_changes ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.fk_change_id = ch.pk_change_id       
--AND ch.budget_year = @year-1 AND ch.org_budget_flag = 0      
      
      
--UPDATE a SET a.org_bud_last_year = 0, rev_bud_last_year = 0      
--FROM #inv_hlptab2 a      
--JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id AND a.pk_main_project_code = mp.pk_main_project_code      
--AND @year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)      
--AND mp.inv_status IN (3,4,5)      
      
      
      
-- Setting the finplan values to 0 for status 4 & 5      
      
UPDATE #inv_hlptab2 SET org_bud_last_year = 0, rev_bud_last_year = 0,year_1_amount = 0, year_2_amount = 0, year_3_amount = 0, year_4_amount = 0, year_5_amount = 0,      
year_6_amount = 0, year_7_amount = 0, year_8_amount = 0, year_9_amount = 0, year_10_amount = 0      
WHERE inv_status IN (4,5)      
      
      
UPDATE #inv_hlptab2 SET org_bud_last_year = 0, rev_bud_last_year = 0      
WHERE inv_status = 3      
      
 SELECT a.fk_tenant_id,a.budget_year,fp_level_1_value = CONVERT(NVARCHAR(25),''),fp_level_1_name= CONVERT(NVARCHAR(100),''),      
 a.year_1_vat_base, a.year_2_vat_base, a.year_3_vat_base, a.year_4_vat_base, a.year_5_vat_base, a.year_6_vat_base, a.year_7_vat_base, a.year_8_vat_base, a.year_9_vat_base, a.year_10_vat_base,      
 a.year_1_vat_cost, a.year_2_vat_cost, a.year_3_vat_cost, a.year_4_vat_cost, a.year_5_vat_cost, a.year_6_vat_cost, a.year_7_vat_cost, a.year_8_vat_cost, a.year_9_vat_cost, a.year_10_vat_cost,      
 COALESCE(fk_fdv_codes,'') as fk_fdv_codes,      
 fp_level_2_value = CONVERT(NVARCHAR(25),''), fp_level_2_name = CONVERT(NVARCHAR(100),''),      
 ISNULL(a.pk_main_project_code,'') AS pk_main_project_code,      
 ISNULL(a.main_project_name,'') AS main_project_name,      
 ISNULL(a.inv_status,100) AS inv_status,      
 ISNULL(a.is_temp,0) AS is_temp,      
 ISNULL(a.completion_date,'1900-01-01') AS completion_date,      
 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,      
 a.fk_prog_code,a.header_dept, a.header_function,      
 oh.org_id_1 as header_org_id_1, oh.org_name_1 as header_org_name_1, oh.org_id_2  as header_org_id_2, oh.org_name_2 as header_org_name_2,      
 oh.org_id_3  as header_org_id_3, oh.org_name_3 as header_org_name_3,oh.org_id_4  as header_org_id_4, oh.org_name_4 as header_org_name_4,      
 oh.org_id_5  as header_org_id_5, oh.org_name_5 as header_org_name_5,      
 od.org_id_1 as detail_org_id_1, od.org_name_1 as detail_org_name_1, od.org_id_2  as detail_org_id_2, od.org_name_2 as detail_org_name_2,      
 od.org_id_3  as detail_org_id_3, od.org_name_3 as detail_org_name_3,od.org_id_4  as detail_org_id_4, od.org_name_4 as detail_org_name_4,      
 od.org_id_5  as detail_org_id_5, od.org_name_5 as detail_org_name_5,      
 ISNULL(sh.service_id_1,'')  as header_service_id_1, ISNULL(sh.service_name_1,'') as header_service_name_1, ISNULL(sh.service_id_2,'')  as header_service_id_2, ISNULL(sh.service_name_2,'') as header_service_name_2,      
 ISNULL(sh.service_id_3,'')  as header_service_id_3, ISNULL(sh.service_name_3,'') as header_service_name_3, ISNULL(sh.service_id_4,'')  as header_service_id_4, ISNULL(sh.service_name_4,'') as header_service_name_4,      
 ISNULL(sh.service_id_5,'')  as header_service_id_5, ISNULL(sh.service_name_5,'') as header_service_name_5,                    
 ISNULL(sd.service_id_1,'')  as detail_service_id_1, ISNULL(sd.service_name_1,'') as detail_service_name_1, ISNULL(sd.service_id_2,'')  as detail_service_id_2, ISNULL(sd.service_name_2,'') as detail_service_name_2,      
 ISNULL(sd.service_id_3,'')  as detail_service_id_3, ISNULL(sd.service_name_3,'') as detail_service_name_3, ISNULL(sd.service_id_4,'')  as detail_service_id_4, ISNULL(sd.service_name_4,'') as detail_service_name_4,      
 ISNULL(sd.service_id_5,'')  as detail_service_id_5, ISNULL(sd.service_name_5,'') as detail_service_name_5,      
 ISNULL(ip.description,'') as program_code_description,rl.line_group_id, rl.line_item_id,      
 sum(org_bud_last_year) as org_bud_last_year, sum(rev_bud_last_year) as rev_bud_last_year,      
 sum(year_1_amount) as year_1_amount,sum(year_2_amount) as year_2_amount,      
 sum(year_3_amount) as year_3_amount,      
 sum(year_4_amount) as year_4_amount,sum(year_5_amount) as year_5_amount,      
 sum(year_6_amount) as year_6_amount,sum(year_7_amount) as year_7_amount,      
 sum(year_8_amount) as year_8_amount,sum(year_9_amount) as year_9_amount,      
 sum(year_10_amount) as year_10_amount,        
 sum_finplan = convert(dec(18,2),0),      
finished_year = isnull(DATEPART(YEAR, completion_date),@year),       
SUM(approved_cost) as approved_cost,       
ISNULL(a.approval_reference,'') AS approval_reference,      
ISNULL(a.approval_ref_url,'') AS approval_ref_url,      
ISNULL(a.original_finish_year,0) AS original_finish_year,      
inv_year_1_amount = convert(dec(18,2),0),       
inv_year_2_amount = convert(dec(18,2),0),       
inv_year_3_amount = convert(dec(18,2),0),       
inv_year_4_amount = convert(dec(18,2),0),       
inv_year_5_amount = convert(dec(18,2),0),       
inv_year_6_amount = convert(dec(18,2),0),       
inv_year_7_amount = convert(dec(18,2),0),       
inv_year_8_amount = convert(dec(18,2),0),      
inv_year_9_amount = convert(dec(18,2),0),       
inv_year_10_amount = convert(dec(18,2),0),      
fin_year_1_amount = convert(dec(18,2),0),       
fin_year_2_amount = convert(dec(18,2),0),       
fin_year_3_amount = convert(dec(18,2),0),       
fin_year_4_amount = convert(dec(18,2),0),       
fin_total_amount = convert(dec(18,2),0),      
SUM(previously_budgeted) as previously_budgeted,      
gross_cost = convert(dec(18,2),0),      
financed_amount = convert(dec(18,2),0),      
SUM(net_cost) AS net_cost,      
type = CASE WHEN rl.line_item_id = 1010 THEN 'i' ELSE 'f' END      
 INTO #inv_hlptab3      
 FROM #inv_hlptab2 a      
 LEFT JOIN tco_org_hierarchy oh ON a.header_dept = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version      
 JOIN tco_org_hierarchy od ON a.fk_department_code = od.fk_department_code AND a.fk_tenant_id = od.fk_tenant_id AND od.fk_org_version = @org_version      
 LEFT JOIN tco_service_values sh ON a.header_function = sh.fk_function_code AND a.fk_tenant_id = sh.fk_tenant_id      
 LEFT JOIN tco_service_values sd ON a.fk_function_code = sd.fk_function_code AND a.fk_tenant_id = sd.fk_tenant_id      
 LEFT JOIN tco_inv_program ip ON a.fk_prog_code = ip.pk_prog_code AND a.fk_tenant_id = ip.fk_tenant_id      
 LEFT JOIN #valid_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code --AND a.budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)      
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'      
 group by a.fk_tenant_id,a.budget_year,a.pk_main_project_code,a.main_project_name,a.inv_status,a.is_temp,completion_date,      
 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,      
 a.fk_prog_code,a.header_dept, a.header_function,      
 oh.org_id_1, oh.org_name_1, oh.org_id_2, oh.org_name_2,      
 oh.org_id_3, oh.org_name_3,oh.org_id_4, oh.org_name_4,      
 oh.org_id_5, oh.org_name_5,      
 od.org_id_1, od.org_name_1, od.org_id_2, od.org_name_2,      
 od.org_id_3, od.org_name_3, od.org_id_4, od.org_name_4,      
 od.org_id_5, od.org_name_5,  sh.service_id_1, sh.service_name_1, sh.service_id_2, sh.service_name_2,      
 sh.service_id_3, sh.service_name_3,sh.service_id_4, sh.service_name_4,      
 sh.service_id_5, sh.service_name_5,      
 sd.service_id_1, sd.service_name_1, sd.service_id_2, sd.service_name_2,      
 sd.service_id_3, sd.service_name_3,sd.service_id_4, sd.service_name_4,      
 sd.service_id_5, sd.service_name_5,      
ip.description,rl.line_group_id, rl.line_item_id,a.approval_reference,a.approval_ref_url, a.original_finish_year,      
a.year_1_vat_base, a.year_2_vat_base, a.year_3_vat_base, a.year_4_vat_base, a.year_5_vat_base, a.year_6_vat_base, a.year_7_vat_base, a.year_8_vat_base, a.year_9_vat_base, a.year_10_vat_base,      
a.year_1_vat_cost, a.year_2_vat_cost, a.year_3_vat_cost, a.year_4_vat_cost, a.year_5_vat_cost, a.year_6_vat_cost, a.year_7_vat_cost, a.year_8_vat_cost, a.year_9_vat_cost, a.year_10_vat_cost, a.fk_fdv_codes;       
      
      
UPDATE #inv_hlptab3 SET       
header_org_id_1 = detail_org_id_1,      
header_org_id_2 = detail_org_id_2,      
header_org_id_3 = detail_org_id_3,      
header_org_id_4 = detail_org_id_4,      
header_org_id_5 = detail_org_id_5,      
header_org_name_1 = detail_org_name_1,      
header_org_name_2 = detail_org_name_2,      
header_org_name_3 = detail_org_name_3,      
header_org_name_4 = detail_org_name_4,      
header_org_name_5 = detail_org_name_5,      
header_dept = fk_department_code      
WHERE inv_status = 100 or header_org_id_1 is null;      
      
      
PRINT 'Get investment finplan data'      
      
INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code,       
free_dim_1, free_dim_2, free_dim_3, free_dim_4,      
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,      
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,      
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount, finplan_year_5_amount,       
finplan_year_6_amount, finplan_year_7_amount, finplan_year_8_amount, finplan_year_9_amount, finplan_year_10_amount, forecast_amount,      
fk_investment_id, fk_portfolio_code, fk_prog_code, fk_change_id,      
year_1_vat_base, year_2_vat_base, year_3_vat_base, year_4_vat_base, year_5_vat_base, year_6_vat_base, year_7_vat_base, year_8_vat_base, year_9_vat_base, year_10_vat_base,       
year_1_vat_cost, year_2_vat_cost, year_3_vat_cost, year_4_vat_cost , year_5_vat_cost, year_6_vat_cost, year_7_vat_cost, year_8_vat_cost, year_9_vat_cost, year_10_vat_cost,       
fk_fdv_codes, inv_status, approval_cost)      
SELECT a.fk_tenant_id, a.fk_account_code, a.fk_department_code, a.fk_function_code,       
'' AS free_dim_1, '' AS free_dim_2, '' AS free_dim_3, '' AS free_dim_4,      
a.fk_project_code,0 as actual_amt_year,0 as actual_amt_last_year,0 as actual_amt_year_minus2,      
SUM(a.year_1_amount) as org_bud_amt_year,SUM(a.org_bud_last_year) as  org_bud_amt_last_year,      
SUM(a.year_1_amount) as  revised_bud_amt_year,SUM(a.rev_bud_last_year)as revised_bud_amt_last_year,      
SUM(a.year_1_amount) as finplan_year_1_amount, SUM(a.year_2_amount)  as finplan_year_2_amount,       
SUM(a.year_3_amount) as finplan_year_3_amount, SUM(a.year_4_amount) as finplan_year_4_amount,      
SUM(a.year_5_amount) as finplan_year_5_amount, SUM(a.year_6_amount) as finplan_year_6_amount,      
SUM(a.year_7_amount) as finplan_year_7_amount, SUM(a.year_8_amount) as finplan_year_8_amount,      
SUM(a.year_9_amount) as finplan_year_9_amount, SUM(a.year_10_amount) as finplan_year_10_amount,      
0 as forecast_amount,      
0 as fk_investment_id, '' as fk_portfolio_code, a.fk_prog_code, fk_change_id,       
a.year_1_vat_base, a.year_2_vat_base, a.year_3_vat_base, a.year_4_vat_base, a.year_5_vat_base, a.year_6_vat_base, a.year_7_vat_base, a.year_8_vat_base, a.year_9_vat_base, a.year_10_vat_base,       
a.year_1_vat_cost, a.year_2_vat_cost, a.year_3_vat_cost, a.year_4_vat_cost, a.year_5_vat_cost, a.year_6_vat_cost, a.year_7_vat_cost, a.year_8_vat_cost, a.year_9_vat_cost, a.year_10_vat_cost,      
a.fk_fdv_codes,a.inv_status, SUM(a.approved_cost)      
FROM #inv_hlptab3 a      
GROUP BY a.fk_tenant_id, a.fk_account_code, a.fk_department_code, a.fk_function_code,       
a.fk_project_code,a.fk_prog_code, fk_change_id,      
a.year_1_vat_base, a.year_2_vat_base, a.year_3_vat_base, a.year_4_vat_base, a.year_5_vat_base, a.year_6_vat_base, a.year_7_vat_base, a.year_8_vat_base, a.year_9_vat_base, a.year_10_vat_base,      
a.year_1_vat_cost, a.year_2_vat_cost, a.year_3_vat_cost, a.year_4_vat_cost, a.year_5_vat_cost, a.year_6_vat_cost, a.year_7_vat_cost, a.year_8_vat_cost, a.year_9_vat_cost, a.year_10_vat_cost, a.fk_fdv_codes,a.inv_status      
      
PRINT 'Fecth investment header org/service'      
      
UPDATE a      
SET a.inv_fk_org_id = CASE      
WHEN @param_value_18 = 'org_id_1' THEN b.org_id_1      
WHEN @param_value_18 = 'org_id_2' THEN b.org_id_2      
WHEN @param_value_18 = 'org_id_3' THEN b.org_id_3      
WHEN @param_value_18 = 'org_id_4' THEN b.org_id_4      
WHEN @param_value_18 = 'org_id_5' THEN b.org_id_5      
ELSE b.org_id_2      
END,      
inv_org_name = CASE       
WHEN @param_value_18 = 'org_id_1' THEN b.org_name_1      
WHEN @param_value_18 = 'org_id_2' THEN b.org_name_2      
WHEN @param_value_18 = 'org_id_3' THEN b.org_name_3      
WHEN @param_value_18 = 'org_id_4' THEN b.org_name_4      
WHEN @param_value_18 = 'org_id_5' THEN b.org_name_5      
ELSE b.org_name_2      
END      
FROM #temp_table_1 a      
JOIN #org_data b ON a.fk_project_code = b.pk_project_code       
WHERE @param_value_18 like 'org%'      
      
UPDATE a      
SET a.inv_fk_org_id = CASE      
WHEN @param_value_18 = 'org_id_1' THEN b.org_id_1      
WHEN @param_value_18 = 'org_id_2' THEN b.org_id_2      
WHEN @param_value_18 = 'org_id_3' THEN b.org_id_3      
WHEN @param_value_18 = 'org_id_4' THEN b.org_id_4      
WHEN @param_value_18 = 'org_id_5' THEN b.org_id_5      
ELSE b.org_id_2      
END,      
inv_org_name = CASE       
WHEN @param_value_18 = 'org_id_1' THEN b.org_name_1      
WHEN @param_value_18 = 'org_id_2' THEN b.org_name_2      
WHEN @param_value_18 = 'org_id_3' THEN b.org_name_3      
WHEN @param_value_18 = 'org_id_4' THEN b.org_name_4      
WHEN @param_value_18 = 'org_id_5' THEN b.org_name_5      
ELSE b.org_name_2      
END      
FROM #temp_table_1 a      
JOIN #org_data b ON a.fk_department_code = b.fk_department_code      
WHERE @param_value_18 like 'org%' AND a.inv_fk_org_id is null      
      
      
      
UPDATE a      
SET a.inv_fk_org_id = CASE      
WHEN @param_value_18 = 'service_id_1' THEN b.service_id_1      
WHEN @param_value_18 = 'service_id_2' THEN b.service_id_2      
WHEN @param_value_18 = 'service_id_3' THEN b.service_id_3      
WHEN @param_value_18 = 'service_id_4' THEN b.service_id_4      
WHEN @param_value_18 = 'service_id_5' THEN b.service_id_5      
ELSE b.service_id_2      
END,      
inv_org_name = CASE       
WHEN @param_value_18 = 'service_id_1' THEN b.service_name_1      
WHEN @param_value_18 = 'service_id_2' THEN b.service_name_2      
WHEN @param_value_18 = 'service_id_3' THEN b.service_name_3      
WHEN @param_value_18 = 'service_id_4' THEN b.service_name_4      
WHEN @param_value_18 = 'service_id_5' THEN b.service_name_5      
ELSE b.service_name_2      
END      
FROM #temp_table_1 a      
JOIN #service_data b ON a.fk_project_code = b.pk_project_code       
WHERE @param_value_18 like 'service%'      
      
      
UPDATE a      
SET a.inv_fk_org_id = CASE      
WHEN @param_value_18 = 'service_id_1' THEN b.service_id_1      
WHEN @param_value_18 = 'service_id_2' THEN b.service_id_2      
WHEN @param_value_18 = 'service_id_3' THEN b.service_id_3      
WHEN @param_value_18 = 'service_id_4' THEN b.service_id_4      
WHEN @param_value_18 = 'service_id_5' THEN b.service_id_5      
ELSE b.service_id_2      
END,      
inv_org_name = CASE       
WHEN @param_value_18 = 'service_id_1' THEN b.service_name_1      
WHEN @param_value_18 = 'service_id_2' THEN b.service_name_2      
WHEN @param_value_18 = 'service_id_3' THEN b.service_name_3      
WHEN @param_value_18 = 'service_id_4' THEN b.service_name_4      
WHEN @param_value_18 = 'service_id_5' THEN b.service_name_5      
ELSE b.service_name_2      
END      
FROM #temp_table_1 a      
JOIN #service_data b ON a.fk_function_code = b.fk_function_code      
WHERE @param_value_18 like 'service%' AND a.inv_fk_org_id is null      
      
      
UPDATE a      
SET a.inv_fk_org_id = 'ZZ',      
inv_org_name = 'Oppsett mangler'      
FROM #temp_table_1 a WHERE a.inv_fk_org_id is null      
      
      
RAISERROR ('FINISH: Fetch data from investments', 0, 1) WITH NOWAIT      
PRINT convert(nvarchar(19),SYSDATETIME())      
      
SELECT S.fk_tenant_id,      
COALESCE(fk_fdv_codes,'')as fk_fdv_codes,       
@year as [budgetYear],      
invID =  COALESCE(S.invID,  '-2'),      
InvName =  COALESCE(S.InvName,  'Andre Investeringer'),      
account_code = account_code,      
sum(actual_amt_year_minus2) AS gl_amount,      
sum(org_bud_amt_last_year) AS budget_amount,       
sum(finplan_year_1_amount) AS year_1_amount,       
sum(finplan_year_2_amount) AS year_2_amount,      
sum(finplan_year_3_amount) AS year_3_amount,       
sum(finplan_year_4_amount) AS year_4_amount,      
sum(finplan_year_5_amount) AS year_5_amount,      
sum(finplan_year_6_amount) AS year_6_amount,       
sum(finplan_year_7_amount) AS year_7_amount,      
sum(finplan_year_8_amount) AS year_8_amount,      
sum(finplan_year_9_amount) AS year_9_amount,       
sum(finplan_year_10_amount) AS year_10_amount,      
approval_cost = SUM(approval_cost),      
sub_header_id =  CASE   WHEN @aggregate_id = 'pk_main_project_code' AND @sub_header_id = 'ZZ' THEN 'YY' ELSE sub_header_id END,      
sub_header_name,      
aggregate_id,      
aggregate_name,      
org_id = CASE   WHEN @aggregate_id = 'pk_main_project_code' AND @sub_header_id = 'ZZ' THEN 'YY'      
                WHEN line_item_id IN (1020,1030,1040) THEN '0'      
                WHEN @sub_header_id = 'ZZ' THEN aggregate_id ELSE sub_header_id END,      
org_name = CASE WHEN @aggregate_id = 'pk_main_project_code' AND @sub_header_id = 'ZZ' THEN sub_header_name      
                WHEN line_item_id IN (1020,1030,1040) THEN '0'      
                WHEN @sub_header_id = 'ZZ' THEN aggregate_name ELSE sub_header_name END,      
line_group_id,      
line_item_id,      
line_group ,      
line_item,      
CASE WHEN sum(S.year_1_vat_base) = 0 then 0 else (sum(S.year_1_vat_cost)/ sum(S.year_1_vat_base)) * 100  END as year_1_pct,      
CASE WHEN sum(S.year_2_vat_base) = 0 then 0 else (sum(S.year_2_vat_cost)/ sum(S.year_2_vat_base)) * 100  END as year_2_pct,      
CASE WHEN sum(S.year_3_vat_base) = 0 then 0 else (sum(S.year_3_vat_cost)/ sum(S.year_3_vat_base)) * 100  END as year_3_pct ,      
CASE WHEN sum(S.year_4_vat_base) = 0 then 0 else (sum(S.year_4_vat_cost)/ sum(S.year_4_vat_base)) * 100  END as year_4_pct,      
CASE WHEN sum(S.year_5_vat_base) = 0 then 0 else (sum(S.year_5_vat_cost)/ sum(S.year_5_vat_base)) * 100  END as year_5_pct,      
CASE WHEN sum(S.year_6_vat_base) = 0 then 0 else (sum(S.year_6_vat_cost)/ sum(S.year_6_vat_base)) * 100  END as year_6_pct,      
CASE WHEN sum(S.year_7_vat_base) = 0 then 0 else (sum(S.year_7_vat_cost)/ sum(S.year_7_vat_base)) * 100  END as year_7_pct,      
CASE WHEN sum(S.year_8_vat_base) = 0 then 0 else (sum(S.year_8_vat_cost)/ sum(S.year_8_vat_base)) * 100  END as year_8_pct ,      
CASE WHEN sum(S.year_9_vat_base) = 0 then 0 else (sum(S.year_9_vat_cost)/ sum(S.year_9_vat_base)) * 100  END as year_9_pct,      
CASE WHEN sum(S.year_10_vat_base) = 0 then 0 else (sum(S.year_10_vat_cost)/ sum(S.year_10_vat_base)) * 100  END as year_10_pct,      
isBlistInvestment, vat_refund_1, vat_refund_2,vat_refund_3, vat_refund_4, vat_refund_5, vat_refund_6,vat_refund_7, vat_refund_8, vat_refund_9,vat_refund_10       
into #temp       
FROM (      
SELECT      
a.fk_tenant_id,       
account_code = CASE WHEN c.line_item_id IN (1020,1030,1040) THEN a.fk_account_code ELSE @defAccountCode END,      
actual_amt_year_minus2 ,org_bud_amt_last_year,  approval_cost,      
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount, finplan_year_5_amount,      
finplan_year_6_amount, finplan_year_7_amount, finplan_year_8_amount, finplan_year_9_amount, finplan_year_10_amount,      
invID = CASE WHEN c.line_item_id IN (1020,1030,1040) THEN '0' ELSE mp.pk_main_project_code END,       
InvName= CASE WHEN c.line_item_id IN (1020,1030,1040) THEN '0' ELSE mp.main_project_name END,      
c.line_group_id,c.line_item_id,c.line_group,c.line_item,      
a.year_1_vat_base,a.year_2_vat_base,a.year_3_vat_base,a.year_4_vat_base,a.year_5_vat_base,a.year_6_vat_base,a.year_7_vat_base,a.year_8_vat_base,a.year_9_vat_base,a.year_10_vat_base,      
a.year_1_vat_cost,a.year_2_vat_cost,a.year_3_vat_cost,a.year_4_vat_cost,a.year_5_vat_cost,a.year_6_vat_cost,a.year_7_vat_cost,a.year_8_vat_cost,a.year_9_vat_cost,a.year_10_vat_cost,      
CASE WHEN a.inv_status = 3 THEN 1 ELSE 0 end as isBlistInvestment,ps.fk_fdv_codes,      
sub_header_id =      
CASE  WHEN c.line_item_id IN (1020,1030,1040) THEN 'ZZ'      
        WHEN @sub_header_id = 'ZZ' THEN @sub_header_id      
  WHEN @sub_header_id = 'pk_main_project_code' THEN ISNULL (mp.pk_main_project_code,'ZZZZ')      
  WHEN @sub_header_id = 'pk_prog_code' THEN ISNULL (p.pk_prog_code, 'ZZ')                                 
        WHEN @sub_header_id = 'proj_gr_1' AND ph.proj_gr_1 = '' THEN 'ZZ'      
        WHEN @sub_header_id = 'proj_gr_2' AND ph.proj_gr_2 = '' THEN 'ZZ'      
        WHEN @sub_header_id = 'proj_gr_3' AND ph.proj_gr_3 = '' THEN 'ZZ'      
        WHEN @sub_header_id = 'proj_gr_4' AND ph.proj_gr_4 = '' THEN 'ZZ'      
        WHEN @sub_header_id = 'proj_gr_5' AND ph.proj_gr_5 = '' THEN 'ZZ'      
  WHEN @sub_header_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_1,'ZZ')      
        WHEN @sub_header_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_2,'ZZ')      
        WHEN @sub_header_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_3,'ZZ')      
        WHEN @sub_header_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_4,'ZZ')      
        WHEN @sub_header_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_5,'ZZ')      
        WHEN @sub_header_id = 'org_id_header_1' THEN ISNULL(oh2.org_id_1,'ZZ')      
        WHEN @sub_header_id = 'org_id_header_2' THEN ISNULL(oh2.org_id_2,'ZZ')      
        WHEN @sub_header_id = 'org_id_header_3' THEN ISNULL(oh2.org_id_3,'ZZ')      
        WHEN @sub_header_id = 'org_id_header_4' THEN ISNULL(oh2.org_id_4,'ZZ')      
        WHEN @sub_header_id = 'org_id_header_5' THEN ISNULL(oh2.org_id_5,'ZZ')      
        WHEN @sub_header_id = 'service_id_header_1' THEN ISNULL(sv2.service_id_1,'ZZ')                       
        WHEN @sub_header_id = 'service_id_header_2' THEN ISNULL(sv2.service_id_2,'ZZ')      
        WHEN @sub_header_id = 'service_id_header_3' THEN ISNULL(sv2.service_id_3,'ZZ')      
        WHEN @sub_header_id = 'service_id_header_4' THEN ISNULL(sv2.service_id_4,'ZZ')      
        WHEN @sub_header_id = 'service_id_header_5' THEN ISNULL(sv2.service_id_5,'ZZ')        
  WHEN @sub_header_id = 'org_id_1' THEN ISNULL(oh.org_id_1,'ZZ')      
        WHEN @sub_header_id = 'org_id_2' THEN ISNULL(oh.org_id_2,'ZZ')      
        WHEN @sub_header_id = 'org_id_3' THEN ISNULL(oh.org_id_3,'ZZ')      
        WHEN @sub_header_id = 'org_id_4' THEN ISNULL(oh.org_id_4,'ZZ')      
        WHEN @sub_header_id = 'org_id_5' THEN ISNULL(oh.org_id_5,'ZZ')      
        WHEN @sub_header_id = 'service_id_1' THEN ISNULL(sv.service_id_1,'ZZ')                       
        WHEN @sub_header_id = 'service_id_2' THEN ISNULL(sv.service_id_2,'ZZ')      
        WHEN @sub_header_id = 'service_id_3' THEN ISNULL(sv.service_id_3,'ZZ')      
        WHEN @sub_header_id = 'service_id_4' THEN ISNULL(sv.service_id_4,'ZZ')      
        WHEN @sub_header_id = 'service_id_5' THEN ISNULL(sv.service_id_5,'ZZ')        
ELSE oh.org_id_2      
END,      
sub_header_name =      
CASE   WHEN c.line_item_id IN (1020,1030,1040) THEN 'Investeringer'      
        WHEN @sub_header_id = 'ZZ' THEN 'Investeringer'      
  WHEN @sub_header_id = 'pk_main_project_code' THEN ISNULL(mp.main_project_name, @langstring_project)      
  WHEN @sub_header_id = 'pk_main_project_code' AND p5.param_value = 'TRUE' THEN ISNULL(mp.pk_main_project_code + ' - ' + mp.main_project_name, @langstring_project)      
  WHEN @sub_header_id = 'pk_prog_code' THEN ISNULL (p.description, 'Programkode mangler')      
        WHEN @sub_header_id = 'proj_gr_1' AND ph.proj_gr_name_1 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @sub_header_id = 'proj_gr_2' AND ph.proj_gr_name_2 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @sub_header_id = 'proj_gr_3' AND ph.proj_gr_name_3 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @sub_header_id = 'proj_gr_4' AND ph.proj_gr_name_4 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @sub_header_id = 'proj_gr_5' AND ph.proj_gr_name_5 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @sub_header_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_name_1,'Prosjektstruktur mangler')      
        WHEN @sub_header_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_name_2,'Prosjektstruktur mangler')      
        WHEN @sub_header_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_name_3,'Prosjektstruktur mangler')      
        WHEN @sub_header_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_name_4,'Prosjektstruktur mangler')      
        WHEN @sub_header_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_name_5,'Prosjektstruktur mangler')      
        WHEN @sub_header_id = 'org_id_header_1' THEN ISNULL(oh2.org_name_1,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_header_2' THEN ISNULL(oh2.org_name_2,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_header_3' THEN ISNULL(oh2.org_name_3,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_header_4' THEN ISNULL(oh2.org_name_4,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_header_5' THEN ISNULL(oh2.org_name_5,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'service_id_header_1' THEN ISNULL(sv2.service_name_1,'Tjenesteoppsett mangler')                       
        WHEN @sub_header_id = 'service_id_header_2' THEN ISNULL(sv2.service_name_2,'Tjenesteoppsett mangler')      
        WHEN @sub_header_id = 'service_id_header_3' THEN ISNULL(sv2.service_name_3,'Tjenesteoppsett mangler')      
        WHEN @sub_header_id = 'service_id_header_4' THEN ISNULL(sv2.service_name_4,'Tjenesteoppsett mangler')      
        WHEN @sub_header_id = 'service_id_header_5' THEN ISNULL(sv2.service_name_5,'Tjenesteoppsett mangler')        
  WHEN @sub_header_id = 'org_id_1' THEN ISNULL(oh.org_name_1,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_2' THEN ISNULL(oh.org_name_2,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_3' THEN ISNULL(oh.org_name_3,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_4' THEN ISNULL(oh.org_name_4,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'org_id_5' THEN ISNULL(oh.org_name_5,'Org. oppsett mangler')      
        WHEN @sub_header_id = 'service_id_1' THEN ISNULL(sv.service_name_1,'Tjenesteoppsett mangler')                       
        WHEN @sub_header_id = 'service_id_2' THEN ISNULL(sv.service_name_2,'Tjenesteoppsett mangler')      
        WHEN @sub_header_id = 'service_id_3' THEN ISNULL(sv.service_name_3,'Tjenesteoppsett mangler')      
        WHEN @sub_header_id = 'service_id_4' THEN ISNULL(sv.service_name_4,'Tjenesteoppsett mangler')      
        WHEN @sub_header_id = 'service_id_5' THEN ISNULL(sv.service_name_5,'Tjenesteoppsett mangler')         
ELSE oh.org_name_2      
END,       
aggregate_id =      
CASE   WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_1' THEN ISNULL(sv.service_id_1,'')           WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_2' THEN ISNULL(sv.service_id_2,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_3' THEN ISNULL(sv.service_id_3,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_4' THEN ISNULL(sv.service_id_4,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_5' THEN ISNULL(sv.service_id_5,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_1' THEN ISNULL(oh.org_id_1,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_2' THEN ISNULL(oh.org_id_2,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_3' THEN ISNULL(oh.org_id_3,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_4' THEN ISNULL(oh.org_id_4,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_5' THEN ISNULL(oh.org_id_5,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'project_code' AND a.fk_project_code != '' AND a.fk_project_code IS NOT NULL THEN a.fk_project_code       
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'account_code' THEN a.fk_account_code      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'program_code' THEN isnull(p.pk_prog_code, 'ZZ')       
        WHEN c.line_item_id IN (1020,1030,1040) THEN a.fk_account_code      
        WHEN @aggregate_id = 'pk_main_project_code' THEN ISNULL (mp.pk_main_project_code,'ZZZZ')      
  WHEN @aggregate_id = 'pk_prog_code' THEN ISNULL (p.pk_prog_code, 'zz')                                 
        WHEN @aggregate_id = 'proj_gr_1' AND ph.proj_gr_1 = '' THEN 'ZZ'      
        WHEN @aggregate_id = 'proj_gr_2' AND ph.proj_gr_2 = '' THEN 'ZZ'      
        WHEN @aggregate_id = 'proj_gr_3' AND ph.proj_gr_3 = '' THEN 'ZZ'      
        WHEN @aggregate_id = 'proj_gr_4' AND ph.proj_gr_4 = '' THEN 'ZZ'      
        WHEN @aggregate_id = 'proj_gr_5' AND ph.proj_gr_5 = '' THEN 'ZZ'      
  WHEN @aggregate_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_1,'ZZ')      
        WHEN @aggregate_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_2,'ZZ')      
        WHEN @aggregate_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_3,'ZZ')      
        WHEN @aggregate_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_4,'ZZ')      
        WHEN @aggregate_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_5,'ZZ')      
        WHEN @aggregate_id = 'org_id_header_1' THEN ISNULL(oh2.org_id_1,'ZZ')      
        WHEN @aggregate_id = 'org_id_header_2' THEN ISNULL(oh2.org_id_2,'ZZ')      
        WHEN @aggregate_id = 'org_id_header_3' THEN ISNULL(oh2.org_id_3,'ZZ')      
        WHEN @aggregate_id = 'org_id_header_4' THEN ISNULL(oh2.org_id_4,'ZZ')      
        WHEN @aggregate_id = 'org_id_header_5' THEN ISNULL(oh2.org_id_5,'ZZ')      
        WHEN @aggregate_id = 'service_id_header_1' THEN ISNULL(sv2.service_id_1,'ZZ')                       
        WHEN @aggregate_id = 'service_id_header_2' THEN ISNULL(sv2.service_id_2,'ZZ')      
        WHEN @aggregate_id = 'service_id_header_3' THEN ISNULL(sv2.service_id_3,'ZZ')      
        WHEN @aggregate_id = 'service_id_header_4' THEN ISNULL(sv2.service_id_4,'ZZ')      
        WHEN @aggregate_id = 'service_id_header_5' THEN ISNULL(sv2.service_id_5,'ZZ')        
  WHEN @aggregate_id = 'org_id_1' THEN ISNULL(oh.org_id_1,'ZZ')      
        WHEN @aggregate_id = 'org_id_2' THEN ISNULL(oh.org_id_2,'ZZ')      
        WHEN @aggregate_id = 'org_id_3' THEN ISNULL(oh.org_id_3,'ZZ')      
        WHEN @aggregate_id = 'org_id_4' THEN ISNULL(oh.org_id_4,'ZZ')      
        WHEN @aggregate_id = 'org_id_5' THEN ISNULL(oh.org_id_5,'ZZ')      
        WHEN @aggregate_id = 'service_id_1' THEN ISNULL(sv.service_id_1,'ZZ')                       
        WHEN @aggregate_id = 'service_id_2' THEN ISNULL(sv.service_id_2,'ZZ')      
        WHEN @aggregate_id = 'service_id_3' THEN ISNULL(sv.service_id_3,'ZZ')      
        WHEN @aggregate_id = 'service_id_4' THEN ISNULL(sv.service_id_4,'ZZ')      
        WHEN @aggregate_id = 'service_id_5' THEN ISNULL(sv.service_id_5,'ZZ')         
ELSE oh.org_id_2      
END,      
aggregate_name =      
CASE   WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_1' THEN ISNULL(sv.service_name_1,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_2' THEN ISNULL(sv.service_name_2,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_3' THEN ISNULL(sv.service_name_3,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_4' THEN ISNULL(sv.service_name_4,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'service_id_5' THEN ISNULL(sv.service_name_5,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_1' THEN ISNULL(oh.org_name_1,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_2' THEN ISNULL(oh.org_name_2,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_3' THEN ISNULL(oh.org_name_3,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_4' THEN ISNULL(oh.org_name_4,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'org_id_5' THEN ISNULL(oh.org_name_5,'')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'project_code' AND a.fk_project_code != '' AND a.fk_project_code IS NOT NULL THEN ISNULL(f.project_name ,'Ugyldig prosjekt')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'account_code' THEN ISNULL(ac.display_name,'Ugyldig konto')      
     WHEN c.line_item_id IN (1020,1030,1040) AND @param_inv_grouping = 'program_code' THEN isnull(p.description, '')       
        WHEN c.line_item_id IN (1020,1030,1040) THEN ISNULL(ac.display_name,'Ugyldig konto')      
        WHEN @aggregate_id = 'pk_main_project_code' THEN ISNULL(mp.main_project_name, @langstring_project)      
  WHEN @aggregate_id = 'pk_main_project_code' AND p5.param_value = 'TRUE' THEN ISNULL(mp.pk_main_project_code + ' - ' + mp.main_project_name, @langstring_project)      
  WHEN @aggregate_id = 'pk_prog_code' THEN ISNULL (p.description, 'Programkode mangler')      
        WHEN @aggregate_id = 'proj_gr_1' AND ph.proj_gr_name_1 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @aggregate_id = 'proj_gr_2' AND ph.proj_gr_name_2 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @aggregate_id = 'proj_gr_3' AND ph.proj_gr_name_3 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @aggregate_id = 'proj_gr_4' AND ph.proj_gr_name_4 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @aggregate_id = 'proj_gr_5' AND ph.proj_gr_name_5 = '' THEN 'Prosjektstruktur mangler'      
        WHEN @aggregate_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_name_1,'Prosjektstruktur mangler')      
        WHEN @aggregate_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_name_2,'Prosjektstruktur mangler')      
        WHEN @aggregate_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_name_3,'Prosjektstruktur mangler')      
        WHEN @aggregate_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_name_4,'Prosjektstruktur mangler')      
        WHEN @aggregate_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_name_5,'Prosjektstruktur mangler')      
        WHEN @aggregate_id = 'org_id_header_1' THEN ISNULL(oh2.org_name_1,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_header_2' THEN ISNULL(oh2.org_name_2,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_header_3' THEN ISNULL(oh2.org_name_3,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_header_4' THEN ISNULL(oh2.org_name_4,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_header_5' THEN ISNULL(oh2.org_name_5,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'service_id_header_1' THEN ISNULL(sv2.service_name_1,'Tjenesteoppsett mangler')                       
        WHEN @aggregate_id = 'service_id_header_2' THEN ISNULL(sv2.service_name_2,'Tjenesteoppsett mangler')      
        WHEN @aggregate_id = 'service_id_header_3' THEN ISNULL(sv2.service_name_3,'Tjenesteoppsett mangler')      
        WHEN @aggregate_id = 'service_id_header_4' THEN ISNULL(sv2.service_name_4,'Tjenesteoppsett mangler')      
        WHEN @aggregate_id = 'service_id_header_5' THEN ISNULL(sv2.service_name_5,'Tjenesteoppsett mangler')        
  WHEN @aggregate_id = 'org_id_1' THEN ISNULL(oh.org_name_1,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_2' THEN ISNULL(oh.org_name_2,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_3' THEN ISNULL(oh.org_name_3,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_4' THEN ISNULL(oh.org_name_4,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'org_id_5' THEN ISNULL(oh.org_name_5,'Org. oppsett mangler')      
        WHEN @aggregate_id = 'service_id_1' THEN ISNULL(sv.service_name_1,'Tjenesteoppsett mangler')                       
        WHEN @aggregate_id = 'service_id_2' THEN ISNULL(sv.service_name_2,'Tjenesteoppsett mangler')      
        WHEN @aggregate_id = 'service_id_3' THEN ISNULL(sv.service_name_3,'Tjenesteoppsett mangler')      
        WHEN @aggregate_id = 'service_id_4' THEN ISNULL(sv.service_name_4,'Tjenesteoppsett mangler')      
        WHEN @aggregate_id = 'service_id_5' THEN ISNULL(sv.service_name_5,'Tjenesteoppsett mangler')      
        WHEN @param_inv_grouping = 'service_id_1' THEN ISNULL(sv.service_id_1,'')      
ELSE oh.org_name_2      
END, a.fk_change_id,       
vat_refund_1 = ISNULL(vf.vat_refund_1,0), vat_refund_2 = ISNULL(vf.vat_refund_2,0),       
vat_refund_3 = ISNULL(vf.vat_refund_3,0), vat_refund_4 = ISNULL(vf.vat_refund_4,0),      
vat_refund_5 = ISNULL(vf.vat_refund_5,0), vat_refund_6 = ISNULL(vf.vat_refund_6,0),      
vat_refund_7 = ISNULL(vf.vat_refund_7,0), vat_refund_8 = ISNULL(vf.vat_refund_8,0),      
vat_refund_9 = ISNULL(vf.vat_refund_9,0), vat_refund_10 = ISNULL(vf.vat_refund_10,0)      
FROM #temp_table_1 a        
             JOIN #valid_accounts b ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --AND @year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)       
             LEFT JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND @year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)      
             JOIN @tbl_reporting_line c ON b.fk_kostra_account_code = c.fk_kostra_account_code AND c.report = '55_OVINV' AND c.line_item_id in (1010,1020,1030,1040)      
             LEFT JOIN tco_inv_program p ON a.fk_prog_code = p.pk_prog_code AND a.fk_tenant_id = p.fk_tenant_id      
             LEFT JOIN #valid_projects f ON f.pk_project_code = a.fk_project_code AND f.fk_tenant_id = a.fk_tenant_id AND f.fk_main_project_code != ''      
    LEFT JOIN #valid_mainprojects mp ON mp.fk_tenant_id = f.fk_tenant_id AND mp.pk_main_project_code = f.fk_main_project_code --AND @year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to)       
             LEFT JOIN #vatref_table vf ON vf.fk_tenant_id = mp.fk_tenant_id AND vf.pk_main_project_code = mp.pk_main_project_code      
             LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code      
             LEFT JOIN tco_service_values sv on a.fk_tenant_id = sv.fk_tenant_id AND a.fk_function_code = sv.fk_function_code      
             JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_tenant_id AND a.fk_department_code = oh.fk_department_code   and oh.fk_org_version = @org_version      
             LEFT JOIN tco_parameters p5 ON a.fk_tenant_id = p5.fk_tenant_id AND p5.param_name = '2B_DISPLAY_PROJECT_CODE' AND p5.param_value = 'TRUE' AND p5.active = 1      
             LEFT JOIN tco_proj_version pv ON a.fk_tenant_id = pv.fk_tenant_id and pv.active = 1 and @year BETWEEN pv.period_from and pv.period_to      
       LEFT JOIN tco_proj_hierarchy ph ON a.fk_tenant_id = ph.fk_tenant_id AND pv.pk_proj_version = ph.fk_proj_version AND a.fk_project_code = ph.fk_project_code      
             LEFT JOIN #org_data oh2 ON a.fk_project_code = oh2.pk_project_code      
          LEFT JOIN #service_data sv2 ON  a.fk_project_code = sv2.pk_project_code      
 ) S      
 GROUP BY S.fk_tenant_id,  sub_header_id, sub_header_name , aggregate_id, aggregate_name, s.invID,s.InvName,      
 line_group_id,line_item_id,line_group ,line_item,isBlistInvestment,fk_fdv_codes, account_code,       
 vat_refund_1, vat_refund_2,vat_refund_3, vat_refund_4, vat_refund_5,vat_refund_6, vat_refund_7,      
 vat_refund_8,vat_refund_9, vat_refund_10       
      
      
Select fk_tenant_id,sub_header_id,sub_header_name,aggregate_id, aggregate_name, budgetYear,org_id,org_name,line_group_id,line_item_id,line_group,line_item, invID,invName,account_code,sum(gl_amount)gl_amount,sum(budget_amount)budget_amount,      
sum(year_1_amount)year_1_amount,      
sum(year_2_amount)year_2_amount,      
sum(year_3_amount)year_3_amount,      
sum(year_4_amount)year_4_amount, sum(year_5_amount)year_5_amount, sum(year_6_amount)year_6_amount, sum(year_7_amount)year_7_amount, sum(year_8_amount)year_8_amount, sum(year_9_amount)year_9_amount, sum(year_10_amount)year_10_amount,      
sum(approval_cost) as approval_cost,fk_fdv_codes, year_1_pct , year_2_pct , year_3_pct , year_4_pct, year_5_pct , year_6_pct , year_7_pct, year_8_pct , year_9_pct , year_10_pct,      
isBlistInvestment, vat_refund_1, vat_refund_2,vat_refund_3, vat_refund_4,      
vat_refund_5, vat_refund_6,vat_refund_7, vat_refund_8, vat_refund_9,vat_refund_10      
into #temp2 from #temp   with(nolock)      
group by fk_tenant_id,budgetYear,org_id,org_name,line_group_id,line_item_id,line_group,line_item, invID,invName,account_code, fk_fdv_codes, year_1_pct , year_2_pct , year_3_pct , year_4_pct , year_5_pct , year_6_pct , year_7_pct ,      
year_8_pct , year_9_pct , year_10_pct , isBlistInvestment ,sub_header_id,sub_header_name,aggregate_id, aggregate_name,      
vat_refund_1, vat_refund_2,vat_refund_3, vat_refund_4, vat_refund_5, vat_refund_6,vat_refund_7, vat_refund_8, vat_refund_9, vat_refund_10      
      
DELETE from #temp2 where (year_1_amount= 0 and year_2_amount= 0 and year_3_amount= 0 and year_4_amount=0 and year_5_amount= 0 and year_6_amount= 0 and year_7_amount=0 and year_8_amount= 0       
and year_9_amount= 0 and year_10_amount=0 and gl_amount = 0 and budget_amount = 0)        
      
IF (@include_Inv_blist=0)      
 BEGIN      
 delete from #temp2 where isBlistInvestment=1      
 END      
      
      
UPDATE #temp2 set year_1_pct = 25 where year_1_pct > 100      
UPDATE #temp2 set year_2_pct = 25 where year_2_pct > 100      
UPDATE #temp2 set year_3_pct = 25 where year_3_pct > 100      
UPDATE #temp2 set year_4_pct = 25 where year_4_pct > 100      
UPDATE #temp2 set year_5_pct = 25 where year_5_pct > 100      
UPDATE #temp2 set year_6_pct = 25 where year_6_pct > 100      
UPDATE #temp2 set year_7_pct = 25 where year_7_pct > 100      
UPDATE #temp2 set year_8_pct = 25 where year_8_pct > 100      
UPDATE #temp2 set year_9_pct = 25 where year_9_pct > 100      
UPDATE #temp2 set year_10_pct = 25 where year_10_pct > 100      
      
UPDATE #temp2 set year_1_pct = 0 where year_1_pct < 0      
UPDATE #temp2 set year_2_pct = 0 where year_2_pct < 0      
UPDATE #temp2 set year_3_pct = 0 where year_3_pct < 0      
UPDATE #temp2 set year_4_pct = 0 where year_4_pct < 0      
UPDATE #temp2 set year_5_pct = 0 where year_5_pct < 0      
UPDATE #temp2 set year_6_pct = 0 where year_6_pct < 0      
UPDATE #temp2 set year_7_pct = 0 where year_7_pct < 0      
UPDATE #temp2 set year_8_pct = 0 where year_8_pct < 0      
UPDATE #temp2 set year_9_pct = 0 where year_9_pct < 0      
UPDATE #temp2 set year_10_pct = 0 where year_10_pct < 0      
      
UPDATE #temp2 set year_2_pct = year_1_pct WHERE year_1_pct <> 0 AND year_2_pct = 0;      
UPDATE #temp2 set year_3_pct = year_2_pct WHERE year_2_pct <> 0 AND year_3_pct = 0;      
UPDATE #temp2 set year_4_pct = year_3_pct WHERE year_3_pct <> 0 AND year_4_pct = 0;      
UPDATE #temp2 set year_5_pct = year_4_pct WHERE year_4_pct <> 0 AND year_5_pct = 0;      
UPDATE #temp2 set year_6_pct = year_5_pct WHERE year_5_pct <> 0 AND year_6_pct = 0;      
UPDATE #temp2 set year_7_pct = year_6_pct WHERE year_6_pct <> 0 AND year_7_pct = 0;      
UPDATE #temp2 set year_8_pct = year_7_pct WHERE year_7_pct <> 0 AND year_8_pct = 0;      
UPDATE #temp2 set year_9_pct = year_8_pct WHERE year_8_pct <> 0 AND year_9_pct = 0;      
UPDATE #temp2 set year_10_pct = year_9_pct WHERE year_9_pct <> 0 AND year_10_pct = 0;      
      
UPDATE #temp2 set year_9_pct = year_10_pct WHERE year_10_pct <> 0 AND year_9_pct = 0;      
UPDATE #temp2 set year_8_pct = year_9_pct WHERE year_9_pct <> 0 AND year_8_pct = 0;      
UPDATE #temp2 set year_7_pct = year_8_pct WHERE year_8_pct <> 0 AND year_7_pct = 0;       
UPDATE #temp2 set year_6_pct = year_7_pct WHERE year_7_pct <> 0 AND year_6_pct = 0;      
UPDATE #temp2 set year_5_pct = year_6_pct WHERE year_6_pct <> 0 AND year_5_pct = 0;      
UPDATE #temp2 set year_4_pct = year_5_pct WHERE year_5_pct <> 0 AND year_4_pct = 0;       
UPDATE #temp2 set year_3_pct = year_4_pct WHERE year_4_pct <> 0 AND year_3_pct = 0;      
UPDATE #temp2 set year_2_pct = year_3_pct WHERE year_3_pct <> 0 AND year_2_pct = 0;      
UPDATE #temp2 set year_1_pct = year_2_pct WHERE year_2_pct <> 0 AND year_1_pct = 0;       
      
-- Create empty change rows for 1020,1030,1040      
      
insert into tps_fin_inv_propopsal (fk_tenant_id,fk_proposal_id,budget_year,org_id,org_name,line_group_id,line_group,line_item_id,line_item,fk_investment_id,investment_name,      
year_1_amount,year_2_amount,year_3_amount,year_4_amount, year_5_amount,year_6_amount,year_7_amount, year_8_amount,year_9_amount,year_10_amount,       
change_1_amount,change_2_amount,change_3_amount,change_4_amount,change_5_amount,change_6_amount,change_7_amount,change_8_amount,change_9_amount,change_10_amount,       
gl_amount,budget_amount,updated,updated_by,status,comments,new_investment_description,isEditable,fk_fdv_codes,year_1_pct,year_2_pct,year_3_pct,year_4_pct,year_5_pct,      
year_6_pct,year_7_pct,year_8_pct,year_9_pct,year_10_pct, account_code,isBlistInvestment,sub_header_id,sub_header_name,aggregate_id,aggregate_name,previous_change_1,      
previous_change_2,previous_change_3,previous_change_4,previous_change_5,previous_change_6,previous_change_7,previous_change_8,previous_change_9,previous_change_10, approval_cost)      
SELECT ac.pk_tenant_id,@proposal_id,@budget_year,'0','0',      
rl.line_group_id,rl.line_group,rl.line_item_id,rl.line_item,      
0 as fk_investment_id,'0' as investment_name,      
0 as year_1_amount, 0 as year_2_amount,0 as year_3_amount,0 as year_4_amount, 0 as year_5_amount,0 as year_6_amount,0 as year_7_amount, 0 as year_8_amount,0 as year_9_amount,0 as year_10_amount,      
0 as change_1_amount,0 as change_2_amount,0 as change_3_amount,0 as change_4_amount,0 as change_5_amount,0 as change_6_amount,0 as change_7_amount, 0 as change_8_amount,0 as change_9_amount,0 as change_10_amount, 0 as gl_amount,0 as budget_amount,      
getdate() as updated,      
@user_id as updated_by,1 as status,'' as comments,'' as new_investment_description,0 as isEditable,'' as fk_fdv_codes,      
0 as year_1_pct,0 as year_2_pct,0 as year_3_pct,0 as year_4_pct,0 as year_5_pct,0 as year_6_pct,0 as year_7_pct,0 as year_8_pct,0 as year_9_pct,0 as year_10_pct,      
min(ac.pk_account_code),0 as isBlistInvestment,'ZZ' as sub_header_id,'' as sub_header_name,      
'ZZ' as aggregate_id,      
aggregate_name = @langstring_1020,      
0 as previous_change_1,0 as previous_change_2,0 as previous_change_3,0 as previous_change_4,0 as previous_change_5,0 as previous_change_6,0 as previous_change_7,0 as previous_change_8,0 as previous_change_9,0 as previous_change_10,      
0 as approval_cost      
FROM @tbl_reporting_line rl       
JOIN tco_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code      
WHERE rl.report = '55_OVINV' AND rl.line_item_id = 1020 AND ac.pk_tenant_id = @tenant_id      
GROUP BY ac.pk_tenant_id, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item      
      
      
insert into tps_fin_inv_propopsal (fk_tenant_id,fk_proposal_id,budget_year,org_id,org_name,line_group_id,line_group,line_item_id,line_item,fk_investment_id,investment_name,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount    
,year_7_amount,year_8_amount,year_9_amount,year_10_amount,change_1_amount,change_2_amount,change_3_amount,change_4_amount,change_5_amount,change_6_amount,change_7_amount,change_8_amount,change_9_amount,change_10_amount,gl_amount,budget_amount,updated,    
updated_by,status,comments,new_investment_description,isEditable,fk_fdv_codes,year_1_pct,year_2_pct,year_3_pct,year_4_pct,year_5_pct,year_6_pct,year_7_pct,year_8_pct,year_9_pct,year_10_pct,account_code,isBlistInvestment,sub_header_id,sub_header_name,aggregate_id,    
aggregate_name,previous_change_1,      
previous_change_2,previous_change_3,previous_change_4,previous_change_5,previous_change_6,previous_change_7,previous_change_8,previous_change_9,previous_change_10, approval_cost)      
SELECT ac.pk_tenant_id,@proposal_id,@budget_year,'0','0',      
rl.line_group_id,rl.line_group,rl.line_item_id,rl.line_item,      
0 as fk_investment_id,'0' as investment_name,      
0 as year_1_amount, 0 as year_2_amount,0 as year_3_amount,0 as year_4_amount, 0 as year_5_amount,0 as year_6_amount,0 as year_7_amount,0 as year_8_amount,0 as year_9_amount,0 as year_10_amount,      
0 as change_1_amount,0 as change_2_amount,0 as change_3_amount,0 as change_4_amount, 0 as change_5_amount,0 as change_6_amount,0 as change_7_amount,0 as change_8_amount,0 as change_9_amount,0 as change_10_amount, 0 as gl_amount,0 as budget_amount,      
getdate() as updated,      
@user_id as updated_by,1 as status,'' as comments,'' as new_investment_description,0 as isEditable,'' as fk_fdv_codes,      
0 as year_1_pct,0 as year_2_pct,0 as year_3_pct,0 as year_4_pct,0 as year_5_pct,0 as year_6_pct,0 as year_7_pct,0 as year_8_pct,0 as year_9_pct,0 as year_10_pct,      
min(ac.pk_account_code),0 as isBlistInvestment,'ZZ' as sub_header_id,'' as sub_header_name,      
'ZZ' as aggregate_id,      
aggregate_name = @langstring_1030,      
0 as previous_change_1,0 as previous_change_2,0 as previous_change_3,0 as previous_change_4,0 as previous_change_5,0 as previous_change_6,0 as previous_change_7,      
0 as previous_change_8,0 as previous_change_9,0 as previous_change_10, 0 as approval_cost      
FROM @tbl_reporting_line rl       
JOIN tco_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code      
WHERE rl.report = '55_OVINV' AND rl.line_item_id = 1030 AND ac.pk_tenant_id = @tenant_id      
GROUP BY ac.pk_tenant_id, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item      
      
insert into tps_fin_inv_propopsal (fk_tenant_id,fk_proposal_id,budget_year,org_id,org_name,line_group_id,line_group,line_item_id,line_item,fk_investment_id,investment_name,year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount
  
    
,year_7_amount,year_8_amount,year_9_amount,year_10_amount, change_1_amount,change_2_amount,change_3_amount,change_4_amount,change_5_amount,change_6_amount,change_7_amount,change_8_amount,change_9_amount,change_10_amount,gl_amount,budget_amount,updated,   
 
updated_by,status,comments,new_investment_description,isEditable,fk_fdv_codes,year_1_pct,year_2_pct,year_3_pct,year_4_pct,year_5_pct,year_6_pct,year_7_pct,year_8_pct,year_9_pct,year_10_pct,account_code,isBlistInvestment,sub_header_id,sub_header_name,aggregate_id,    
aggregate_name,      
previous_change_1,previous_change_2,previous_change_3,previous_change_4,previous_change_5,previous_change_6,previous_change_7,previous_change_8,previous_change_9,previous_change_10, approval_cost)      
SELECT ac.pk_tenant_id,@proposal_id,@budget_year,'0','0',      
rl.line_group_id,rl.line_group,rl.line_item_id,rl.line_item,      
0 as fk_investment_id,'0' as investment_name,      
0 as year_1_amount, 0 as year_2_amount,0 as year_3_amount,0 as year_4_amount,0 as year_5_amount,0 as year_6_amount,0 as year_7_amount,0 as year_8_amount,0 as year_9_amount,0 as year_10_amount,      
0 as change_1_amount,0 as change_2_amount,0 as change_3_amount,0 as change_4_amount,0 as change_5_amount,0 as change_6_amount,0 as change_7_amount,0 as change_8_amount,0 as change_9_amount,0 as change_10_amount,0 as gl_amount,0 as budget_amount,      
getdate() as updated,      
@user_id as updated_by,1 as status,'' as comments,'' as new_investment_description,0 as isEditable,'' as fk_fdv_codes,      
0 as year_1_pct,0 as year_2_pct,0 as year_3_pct,0 as year_4_pct,0 as year_5_pct,0 as year_6_pct,0 as year_7_pct,0 as year_8_pct,0 as year_9_pct,0 as year_10_pct,      
min(ac.pk_account_code),0 as isBlistInvestment,'ZZ' as sub_header_id,'' as sub_header_name,      
'ZZ' as aggregate_id,      
aggregate_name = @langstring_1040,      
0 as previous_change_1,0 as previous_change_2,0 as previous_change_3,0 as previous_change_4,0 as previous_change_5,0 as previous_change_6,0 as previous_change_7,0 as previous_change_8,0 as previous_change_9,0 as previous_change_10,      
0 as approval_cost      
FROM @tbl_reporting_line rl       
JOIN tco_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code      
WHERE rl.report = '55_OVINV' AND rl.line_item_id = 1040 AND ac.pk_tenant_id = @tenant_id      
GROUP BY ac.pk_tenant_id, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item      
      
      
      
INSERT tps_fin_inv_propopsal      
(fk_tenant_id,fk_proposal_id,budget_year,org_id,org_name,line_group_id,line_group,line_item_id,line_item,fk_investment_id,investment_name,account_code,      
gl_amount,budget_amount,year_1_amount,year_2_amount,      
year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,change_1_amount,change_2_amount,change_3_amount,change_4_amount,      
change_5_amount,change_6_amount,change_7_amount,change_8_amount,change_9_amount,change_10_amount,updated,updated_by,status,comments,new_investment_description,isEditable,fk_fdv_codes,      
year_1_pct,year_2_pct,year_3_pct,year_4_pct,year_5_pct,year_6_pct,year_7_pct,year_8_pct,year_9_pct,year_10_pct, isBlistInvestment,sub_header_id, sub_header_name, aggregate_id, aggregate_name,       
vat_refund_1, vat_refund_2,vat_refund_3, vat_refund_4,vat_refund_5,vat_refund_6, vat_refund_7,vat_refund_8,vat_refund_9, vat_refund_10, approval_cost)      
Select fk_tenant_id,@proposal_id,budgetYear,COALESCE(org_id,''),COALESCE(org_name,''),line_group_id,line_group,line_item_id,line_item ,invID,invName,account_code, gl_amount, budget_amount,      
year_1_amount,year_2_amount,      
year_3_amount,year_4_amount,year_5_amount,year_6_amount,year_7_amount,year_8_amount,year_9_amount,year_10_amount,      
0,0,0,0,0,0,0,0,0,0,getutcdate(),@user_id,Case when isBlistInvestment = 1 then 0 else 1 end as [status],'' as comments,'',0,fk_fdv_codes,year_1_pct,year_2_pct,year_3_pct,year_4_pct ,year_5_pct,year_6_pct,year_7_pct,      
year_8_pct,year_9_pct,year_10_pct,isBlistInvestment,      
sub_header_id, sub_header_name, aggregate_id, aggregate_name, vat_refund_1, vat_refund_2,vat_refund_3, vat_refund_4,vat_refund_5,vat_refund_6, vat_refund_7,vat_refund_8,vat_refund_9, vat_refund_10,      
approval_cost      
from #temp2 with(nolock)        
      
DROP TABLE #service_data      
DROP TABLE #org_data      
DROP TABLE #temp_table_1      
DROP TABLE #valid_accounts      
DROP TABLE #valid_mainprojects      
DROP TABLE #inv_hlptab      
DROP TABLE #inv_hlptab2      
DROP TABLE #inv_hlptab3        
DROP TABLE #temp      
DROP TABLE #temp2       
DROP TABLE #valid_projects      
DROP TABLE #inv_hlptab_prev_year      
      
RETURN 0 