CREATE OR ALTER PROCEDURE [dbo].[prcInsertIntoTfpTransHeader]
	 (
		@udtTfpTransHeader udtTfpTransHeader readonly
     )
AS
BEGIN
	insert into tfp_trans_header 
	(	[pk_action_id] ,[fk_tenant_id] ,[description] ,[consequence] ,[start_date] ,[action_type] ,[action_source] ,[line_order] ,[isManuallyAdded] ,[title] ,[updated] ,[updated_by] ,
	    [fk_area_id] ,[tag] ,[long_description] ,[priority] ,[display_financial_plan_flag] ,[display_description_apendix_flag] ,[fk_cat_id] ,[financial_plan_description] ,[consequence_flag] ,
	    [different_external_description_flag] ,[change_text_flag] ,[tags] ,[monthly_report_flag] ,[previous_pk_action_id] ,[finished_date] )
	select
		[pk_action_id] ,[fk_tenant_id] ,[description] ,[consequence] ,[start_date] ,[action_type] ,[action_source] ,[line_order] ,[isManuallyAdded] ,[title] ,[updated] ,[updated_by] ,
	    [fk_area_id] ,[tag] ,[long_description] ,[priority] ,[display_financial_plan_flag] ,[display_description_apendix_flag] ,[fk_cat_id] ,[financial_plan_description] ,[consequence_flag] ,
	    [different_external_description_flag] ,[change_text_flag] ,[tags] ,[monthly_report_flag] ,[previous_pk_action_id] ,[finished_date] 
	from @udtTfpTransHeader
End
