-- This procedure is used for the sync functionality developed for Oslo phase 2 --

CREATE OR ALTER PROCEDURE [dbo].[prcSyncFinplanWarehouse]
	@tenant_id int,
	@budget_year int,
    @user_id int
AS
	
DECLARE @tenantid INT = @tenant_id
DECLARE @budgetyear INT = @budget_year
DECLARE @userId INT = @user_id

DECLARE @orgversion VARCHAR(25) 

SET @orgversion = (SELECT pk_org_version FROM tco_org_version ov WHERE ov.fk_tenant_id = @tenantid AND @budgetyear * 100 + 01 BETWEEN ov.period_from AND ov.period_to)

CREATE TABLE #budgetdata
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [sub_tenant_id] INT NOT NULL, 
    [org_bud_main] DECIMAL(18, 2) NOT NULL, 
    [org_bud_sub] DECIMAL(18, 2) NOT NULL, 
    [finplan_amt_sub] DECIMAL(18, 2) NOT NULL,  
    [finplan_amt_sub_final] DECIMAL(18, 2) NOT NULL
)

CREATE TABLE #finplanmain
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [sub_tenant_id] INT NOT NULL, 
	[fk_phase_id] UNIQUEIDENTIFIER NOT NULL, 
    [finplan_amt_main] DECIMAL(18, 2) NOT NULL
)

DECLARE @subtenants TABLE 
(sub_tenant_id INT)

DECLARE @changeid TABLE 
(fk_tenant_id INT,
fk_budget_phase_id	UNIQUEIDENTIFIER,
fk_change_id INT
)


DECLARE @phase_id UNIQUEIDENTIFIER
DECLARE @sortOrder INT

DECLARE db_cursor CURSOR FOR 

SELECT pk_budget_phase_id FROM tco_budget_phase ph
WHERE ph.fk_tenant_id = @tenantid AND org_budget_flag = 1 ORDER BY sort_order

OPEN db_cursor  
FETCH NEXT FROM db_cursor INTO @phase_id

WHILE @@FETCH_STATUS = 0  
BEGIN  
     SET @sortOrder = 
	 (SELECT sort_order FROM tco_budget_phase ph
	 WHERE ph.fk_tenant_id = @tenantid AND org_budget_flag = 1 AND pk_budget_phase_id = @phase_id)

INSERT INTO @changeid (fk_tenant_id, fk_budget_phase_id, fk_change_id)	 
SELECT ph.fk_tenant_id, @phase_id, ch.pk_change_id FROM tco_budget_phase ph
JOIN tfp_budget_changes ch ON ph.fk_tenant_id = ch.fk_tenant_id AND ch.budget_year = @budgetyear AND ch.fk_budget_phase_id = ph.pk_budget_phase_id
AND ph.fk_tenant_id = @tenantid
AND ch.org_budget_flag = 1
AND ph.sort_order <= @sortOrder
GROUP BY ph.fk_tenant_id,ph.pk_budget_phase_id, pk_change_id


      FETCH NEXT FROM db_cursor INTO @phase_id 
END 

CLOSE db_cursor  
DEALLOCATE db_cursor 


INSERT INTO @subtenants (sub_tenant_id)
SELECT sub_tenant_id FROM tco_sync_company_setup WHERE fk_tenant_id = @tenantid AND sync_flag != 0 GROUP BY sub_tenant_id

-- start fetching data --

INSERT INTO #budgetdata (fk_tenant_id, budget_year, sub_tenant_id, org_bud_main, org_bud_sub, finplan_amt_sub, finplan_amt_sub_final)
SELECT fk_tenant_id, budget_year, sub_tenant_id, SUM(org_bud_main), SUM(org_bud_sub), SUM(finplan_amt_sub), SUM(finplan_amt_sub_final)
FROM
( SELECT @tenantid as fk_tenant_id, @budgetyear as budget_year, s.sub_tenant_id, sum(amount_year_1) as org_bud_main, 0 as org_bud_sub, 0 as finplan_amt_sub, 0 as finplan_amt_sub_final 
FROM tbu_trans_detail_original t
JOIN tco_org_hierarchy oh ON t.fk_tenant_id = oh.fk_tenant_id AND t.department_code = oh.fk_department_code AND oh.fk_org_version = @orgversion
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
WHERE t.fk_tenant_id = @tenantid AND t.budget_year = @budgetyear - 1
GROUP BY s.sub_tenant_id

UNION ALL

SELECT @tenantid, @budgetyear, t.fk_tenant_id, 0 as org_bud_main, sum(amount_year_1) as org_bud_sub, 0 as finplan_amt_sub, 0 as finplan_amt_sub_final  
FROM tbu_trans_detail_original t
JOIN @subtenants s ON t.fk_tenant_id = s.sub_tenant_id
WHERE t.budget_year = @budgetyear - 1
GROUP BY t.fk_tenant_id

UNION ALL

SELECT @tenantid, @budgetyear, d.fk_tenant_id, 0 as org_bud_main, 0 as org_bud_sub, sum(d.year_1_amount)  as finplan_amt_sub, 0 as finplan_amt_sub_final 
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN @subtenants s ON d.fk_tenant_id = s.sub_tenant_id
WHERE d.budget_year = @budgetyear
GROUP BY d.fk_tenant_id ) S
GROUP BY fk_tenant_id, budget_year, sub_tenant_id


INSERT INTO #finplanmain (fk_tenant_id, budget_year, sub_tenant_id, fk_phase_id, finplan_amt_main)
SELECT s.fk_tenant_id,@budgetyear,s.sub_tenant_id, ch.fk_budget_phase_id,sum(d.year_1_amount) FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN tco_org_hierarchy oh ON d.fk_tenant_id = oh.fk_tenant_id AND d.department_code = oh.fk_department_code AND oh.fk_org_version = @orgversion
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
JOIN @changeid ch ON d.fk_tenant_id = ch.fk_tenant_id AND d.fk_change_id = ch.fk_change_id
WHERE d.budget_year = @budgetyear AND d.fk_tenant_id = @tenantid
GROUP BY s.fk_tenant_id,s.sub_tenant_id, ch.fk_budget_phase_id

DELETE FROM [tfp_sync_datawarehouse] WHERE fk_tenant_id = @tenantid AND budget_year = @budgetyear

INSERT INTO [tfp_sync_datawarehouse] (fk_tenant_id, budget_year, sub_tenant_id, fk_phase_id, org_bud_main, org_bud_sub, finplan_amt_main,
finplan_amt_sub, finplan_amt_sub_final, updated_by, updated)
SELECT f.fk_tenant_id, f.budget_year, f.sub_tenant_id, f.fk_phase_id, ISNULL(SUM(b.org_bud_main),0), ISNULL(SUM(b.org_bud_sub),0), ISNULL(SUM(f.finplan_amt_main),0), 
ISNULL(SUM(b.finplan_amt_sub),0), ISNULL(SUM(b.finplan_amt_sub_final),0),@userid, getdate()
FROM #finplanmain f 
LEFT JOIN #budgetdata b ON f.fk_tenant_id = b.fk_tenant_id AND f.budget_year = b.budget_year AND f.sub_tenant_id = b.sub_tenant_id
GROUP BY f.fk_tenant_id, f.budget_year, f.sub_tenant_id, f.fk_phase_id

RETURN 0
GO