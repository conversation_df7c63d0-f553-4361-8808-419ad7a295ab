CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRStrategy]

	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS
	
	DECLARE @budget_year INT
	DECLARE @prev_forecast_period INT
	DECLARE @org_version VARCHAR(25) 
	DECLARE @language VARCHAR(10)
	DECLARE @last_period INT
	DECLARE @ub_period INT
    DECLARE @flag_name varchar(30)
    DECLARE @flag_status INT
    DECLARE @flag INT
    DECLARE @last_reported_period INT
    DECLARE @last_reported_budget_year INT
	DECLARE @copylevel_flag INT
	DECLARE @prev_strategy_period INT


	SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
	SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
	SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
	SET @budget_year = @forecast_period/100
	SET @last_period = (@budget_year*100)+12
	SET @ub_period = (@budget_year*100)+13

	SET @copylevel_flag = (SELECT min(flag_status) FROM tco_application_flag WHERE fk_tenant_id = @fk_tenant_id AND flag_name = 'MR_COPYREPORT_ORGLEVEL' AND fk_org_version = @org_version)

	IF @copylevel_flag IS NULL
	BEGIN
	SET @copylevel_flag = 0
	END

	SET @prev_strategy_period = 
	(SELECT max(forecast_period) FROM tmr_strategy_monthly_status
	WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)

	IF @copylevel_flag > 0
	begin

	PRINT 'Copy from previous forecast period'

	INSERT INTO tmr_strategy_monthly_status (fk_tenant_id, fk_strategy_id, forecast_period, status, description, is_reported, updated, updated_by)
	SELECT ms.fk_tenant_id, ms.fk_strategy_id, @forecast_period AS forecast_period, ms.status, ms.description, ms.is_reported, GETDATE() AS updated, @user_id as  updated_by
	FROM tmr_strategy_monthly_status ms
	JOIN  tfp_strategy_text f ON f.pk_strategy_id = ms.fk_strategy_id AND f.fk_tenant_id = ms.fk_tenant_id AND f.budget_year = @budget_year
	WHERE ms.fk_tenant_id = @fk_tenant_id AND ms.forecast_period = @prev_strategy_period AND f.org_level >= @copylevel_flag

	end


RETURN 0
