
CREATE OR ALTER PROCEDURE [dbo].[prcSyncHQBudget]
	@tenant_id int,
	@budget_year int,
	@user_id INT
AS
	
DECLARE @period INT = CONVERT(char(4),@budget_year)+'01'

DECLARE @hq_tenant_definition AS TABLE (
    [fk_tenant_id] INT NOT NULL, 
    [tenant_id_child] INT NOT NULL, 
	[short_name_child] NVARCHAR(4) NOT NULL DEFAULT '')

INSERT INTO @hq_tenant_definition (fk_tenant_id,tenant_id_child, short_name_child)
SELECT DISTINCT fk_tenant_id, tenant_id_child, short_name_child
FROM tmd_hq_tenant_definition 


DELETE FROM tbu_trans_detail_original WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year
AND fk_tenant_id IN (SELECT fk_tenant_id FROM tmd_hq_tenant_definition);

INSERT INTO tbu_trans_detail_original (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,total_amount,tax_flag,holiday_flag,fk_pension_type,fk_investment_id,fk_portfolio_code,fk_prog_code)
SELECT NEWID(),NEWID(),hq.fk_tenant_id,action_type,line_order,ISNULL(ac.fk_kostra_account_code,''),ISNULL(dm.fk_department_code,''),ISNULL(f.fk_kostra_function_code,''),fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,'' AS description,budget_year,@period,budget_type,SUM(amount_year_1),
0 AS fk_key_id,GETDATE() AS updated,@user_id,0 AS allocation_pct,SUM(amount_year_1),tax_flag,holiday_flag,fk_pension_type,0 as fk_investment_id,'' fk_portfolio_code,convert(INT, t.municipality_id) as fk_prog_code
FROM tbu_trans_detail_original a
JOIN gco_tenants t ON a.fk_tenant_id = t.pk_id
JOIN @hq_tenant_definition hq ON a.fk_tenant_id = hq.tenant_id_child
LEFT JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code
LEFT JOIN tmd_hq_departments_mapping dm ON a.fk_tenant_id = dm.tenant_id_source AND a.department_code = dm.department_code_source
LEFT JOIN tco_functions f ON a.fk_tenant_id = f.pk_tenant_id AND a.fk_function_code = f.pk_Function_code
WHERE hq.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
GROUP BY hq.fk_tenant_id,action_type,line_order,ac.fk_kostra_account_code,dm.fk_department_code,f.fk_kostra_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,budget_year,budget_type,
tax_flag,holiday_flag,fk_pension_type,t.municipality_id;

DELETE FROM tbu_trans_detail WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year
AND fk_tenant_id IN (SELECT fk_tenant_id FROM tmd_hq_tenant_definition);

INSERT INTO tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,description,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,total_amount,tax_flag,holiday_flag,fk_pension_type,fk_action_id,fk_investment_id,fk_portfolio_code,fk_prog_code)
SELECT NEWID(),NEWID(),hq.fk_tenant_id,action_type,line_order,ISNULL(ac.fk_kostra_account_code,''),ISNULL(dm.fk_department_code,''),ISNULL(f.fk_kostra_function_code,''),fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,'' AS description,budget_year,@period,budget_type,SUM(amount_year_1),SUM(amount_year_2),SUM(amount_year_3),SUM(amount_year_4),
0 AS fk_key_id,GETDATE() AS updated,@user_id,0 AS allocation_pct,SUM(amount_year_1),tax_flag,holiday_flag,fk_pension_type,0 as fk_action_id,0 as fk_investment_id,'' fk_portfolio_code,convert(INT, t.municipality_id) as fk_prog_code
FROM tbu_trans_detail a
JOIN gco_tenants t ON a.fk_tenant_id = t.pk_id
JOIN @hq_tenant_definition hq ON a.fk_tenant_id = hq.tenant_id_child 
LEFT JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code
LEFT JOIN tmd_hq_departments_mapping dm ON a.fk_tenant_id = dm.tenant_id_source AND a.department_code = dm.department_code_source
LEFT JOIN tco_functions f ON a.fk_tenant_id = f.pk_tenant_id AND a.fk_function_code = f.pk_Function_code
WHERE hq.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
GROUP BY hq.fk_tenant_id,action_type,line_order,ac.fk_kostra_account_code,dm.fk_department_code,f.fk_kostra_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,resource_id,fk_employment_id,budget_year,budget_type,
tax_flag,holiday_flag,fk_pension_type,t.municipality_id

--122115


RETURN 0
GO


