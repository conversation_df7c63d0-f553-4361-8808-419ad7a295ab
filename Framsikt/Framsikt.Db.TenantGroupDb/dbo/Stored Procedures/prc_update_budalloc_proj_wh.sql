CREATE OR ALTER PROCEDURE [dbo].[prc_update_budalloc_proj_wh]
	@tenant_id int,
	@budget_year int,
	@user_id int 

AS

DECLARE	@org_version NVARCHAR(50) = (SELECT min(pk_org_version) FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND @budget_year*100+1 BETWEEN period_from and period_to)

DECLARE @langstring_project NVARCHAR(255) 
DECLARE @param_inv_grouping VARCHAR(255)
DECLARE @default_change_id INT
DECLARE @param_value_18 nvarchar(500)
DECLARE @sub_header_id VARCHAR(25) 
DECLARE @aggregate_id VARCHAR(25) 
DECLARE @sub_header_id_ldip VARCHAR(25) 
DECLARE @aggregate_id_ldip VARCHAR(25) 
DECLARE @timestamp datetime2
DECLARE @last_period INT
DECLARE @language VARCHAR(10)


DECLARE @report1A varchar(25)
DECLARE @report1B varchar (25)
DECLARE @report3 varchar (25)
DECLARE @report2A varchar (25)


SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @tenant_id)


DECLARE @tbl_reporting_line as table (report varchar(50)
, fk_kostra_account_code VARCHAR(25)
, line_group_id INT
, line_group varchar(200)
, line_item_id INT
, line_item varchar(150))


INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)
SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id, 
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),
line_item_id,
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))
FROM gmd_reporting_line rl
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @tenant_id AND tg.Language = @language
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @tenant_id AND tI.Language = @language


SET @report2A = '55_OVINV'


select @timestamp = sysdatetime();
PRINT 'START PROCESSING at ' + convert(nvarchar(19),@timestamp)


SET @param_value_18 = (SELECT param_value FROM vw_tco_parameters WHERE fk_tenant_id = @tenant_id AND active = 1 AND param_name = 'FINPLAN_INVESTMENT_LEVEL')

IF @param_value_18 IS NULL 
BEGIN 
SET @param_value_18 = 'org_id_2'
END





PRINT 'Get the 2B setup'

SET @sub_header_id = (SELECT min(grouping_id) from tco_table_grouping WHERE fk_tenant_id = @tenant_id AND table_id = 'BudgetForms2B2020' AND level_id = 1)
SET @aggregate_id =  (SELECT min(grouping_id) from tco_table_grouping WHERE fk_tenant_id = @tenant_id AND table_id = 'BudgetForms2B2020' AND level_id = 2)
SET @sub_header_id_ldip = (SELECT min(grouping_id) from tco_table_grouping WHERE fk_tenant_id = @tenant_id AND table_id = 'BudgetForms2Bldip' AND level_id = 1)
SET @aggregate_id_ldip =  (SELECT min(grouping_id) from tco_table_grouping WHERE fk_tenant_id = @tenant_id AND table_id = 'BudgetForms2Bldip' AND level_id = 2)




IF @sub_header_id IS NULL AND @aggregate_id IS NULL
BEGIN

SET @sub_header_id = 'org_id_2'
SET @aggregate_id = 'pk_main_project_code'

END


--IF @sub_header_id IS NOT NULL AND @aggregate_id IS NULL
--BEGIN

--SET @aggregate_id = @sub_header_id
--SET @sub_header_id = 'ZZ'

--END

IF @sub_header_id IS NULL AND @aggregate_id IS NOT NULL
BEGIN

SET @sub_header_id = 'ZZ'

END


IF @sub_header_id_ldip IS NULL AND @aggregate_id_ldip IS NULL
BEGIN

SET @sub_header_id_ldip = 'org_id_2'
SET @aggregate_id_ldip = 'pk_main_project_code'

END


IF @sub_header_id_ldip IS NOT NULL AND @aggregate_id_ldip IS NULL
BEGIN

SET @aggregate_id = @sub_header_id_ldip
SET @sub_header_id_ldip = 'ZZ'

END

IF @sub_header_id_ldip IS NULL AND @aggregate_id_ldip IS NOT NULL
BEGIN

SET @sub_header_id_ldip = 'ZZ'

END


SET @langstring_project = 
(
SELECT isnull(s3.description, ISNULL(s2.description, s.description)) FROM gco_language_strings s
LEFT JOIN gco_language_string_overrides s2 ON s.id = s2.id AND s.language=s2.language
LEFT JOIN gco_language_string_overrides_tenant S3 ON s.id = s3.id AND s.language=s3.language AND s3.fk_tenant_id = @tenant_id
WHERE s.id = 'BudForm_2B_projectmissing' AND s.language = @language)

SET @param_inv_grouping = (SELECT param_value FROM vw_tco_parameters WHERE param_name = '2B_TABS_GROUPING' AND fk_tenant_id = @tenant_id)

IF @param_inv_grouping IS NULL 
BEGIN
SET @param_inv_grouping = 'project_code'
END




CREATE TABLE #temp_table_2
	(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY,
	fk_tenant_id INT NOT NULL, 
	budget_year INT NOT NULL,
	org_budget_flag INT NOT NULL,
	[aggregate_id_1] NVARCHAR(25) NOT NULL, 
    [aggregate_name_1] NVARCHAR(300) NOT NULL,     
	[aggregate_id_2] NVARCHAR(25) NOT NULL, 
    [aggregate_name_2] NVARCHAR(300) NOT NULL,
	[aggregate_id_3] NVARCHAR(25) NOT NULL, 
    [aggregate_name_3] NVARCHAR(300) NOT NULL,     
	[aggregate_id_4] NVARCHAR(25) NOT NULL, 
    [aggregate_name_4] NVARCHAR(300) NOT NULL,
	fk_adj_code NVARCHAR(25) NOT NULL,
	fk_change_id NVARCHAR(50) NOT NULL,
	fk_bud_auth_code NVARCHAR(8) NOT NULL,
	[fk_attribute_id] [nvarchar](25) NOT NULL,
	finplan_year_1_amount DECIMAL (18, 2) NOT NULL, 
	rebud_approved DECIMAL (18, 2) NOT NULL,
	rebudget_approved_flag INT NOT NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL
	)



	
CREATE TABLE #valid_accounts( 
	[pk_id] INT NOT NULL IDENTITY, 
	pk_tenant_id INT NOT NULL,
    pk_account_code NVARCHAR(25) NOT NULL,
    fk_kostra_account_code NVARCHAR(25) NOT NULL)

--CREATE INDEX #valid_accounts_ind_1 on #valid_accounts (pk_account_code)
--CREATE INDEX #valid_accounts_ind_2 on #valid_accounts (fk_kostra_account_code)


INSERT INTO #valid_accounts (pk_tenant_id, pk_account_code, fk_kostra_account_code)

SELECT DISTINCT pk_tenant_id, pk_account_code, fk_kostra_account_code FROM 
(SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @budget_year BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
UNION
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @budget_year-1 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
UNION 
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @budget_year-2 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
) s


CREATE TABLE #valid_projects( 
	[pk_id] INT NOT NULL IDENTITY, 
	fk_tenant_id INT NOT NULL,
    pk_project_code NVARCHAR(25) NOT NULL,
    fk_main_project_code NVARCHAR(25) NOT NULL, 
    fk_prog_code NVARCHAR(25) NOT NULL)

CREATE UNIQUE INDEX #_IND_valid_projects ON #valid_projects (pk_project_code, fk_tenant_id)


INSERT INTO #valid_projects ( fk_tenant_id, pk_project_code, fk_main_project_code, fk_prog_code)
SELECT DISTINCT fk_tenant_id, pk_project_code, '' as fk_main_project_code,''  FROM 
(SELECT fk_tenant_id,pk_project_code
FROM tco_projects WHERE fk_tenant_id = @tenant_id AND @budget_year BETWEEN DATEPART(YEAR,date_From) AND DATEPART(YEAR,date_to)
UNION
SELECT fk_tenant_id,pk_project_code
FROM tco_projects WHERE fk_tenant_id = @tenant_id AND @budget_year-1 BETWEEN DATEPART(YEAR,date_From) AND DATEPART(YEAR,date_to)
UNION 
SELECT fk_tenant_id,pk_project_code
FROM tco_projects WHERE fk_tenant_id = @tenant_id AND @budget_year-2 BETWEEN DATEPART(YEAR,date_From) AND DATEPART(YEAR,date_to)
) s

UPDATE a SET a.fk_main_project_code = b.fk_main_project_code, a.fk_prog_code = ISNULL(b.fk_prog_code,'')
from #valid_projects a
JOIN tco_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_project_code = b.pk_project_code AND @budget_year BETWEEN DATEPART(YEAR,b.date_From) AND DATEPART(YEAR,b.date_to)
WHERE a.fk_main_project_code = ''

UPDATE a SET a.fk_main_project_code = b.fk_main_project_code, a.fk_prog_code = ISNULL(b.fk_prog_code,'')
from #valid_projects a
JOIN tco_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_project_code = b.pk_project_code AND @budget_year-1 BETWEEN DATEPART(YEAR,b.date_From) AND DATEPART(YEAR,b.date_to)
WHERE a.fk_main_project_code = ''

UPDATE a SET a.fk_main_project_code = b.fk_main_project_code, a.fk_prog_code = ISNULL(b.fk_prog_code,'')
from #valid_projects a
JOIN tco_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_project_code = b.pk_project_code  AND @budget_year-2 BETWEEN DATEPART(YEAR,b.date_From) AND DATEPART(YEAR,b.date_to)
WHERE a.fk_main_project_code = ''



CREATE TABLE #valid_mainprojects( 
	[pk_id] INT NOT NULL IDENTITY, 
	fk_tenant_id INT NOT NULL,
    pk_main_project_code NVARCHAR(25) NOT NULL,
    main_project_name NVARCHAR(100) NOT NULL,
	fk_department_code NVARCHAR(25) NOT NULL,
	fk_function_code NVARCHAR(25) NOT NULL)

CREATE UNIQUE INDEX #_IND_valid_mainprojects ON #valid_mainprojects (pk_main_project_code, fk_tenant_id)


INSERT INTO #valid_mainprojects ( fk_tenant_id, pk_main_project_code, main_project_name,fk_department_code, fk_function_code)
SELECT DISTINCT fk_tenant_id, pk_main_project_code, '' as main_project_name,'' as fk_department_code,'' as fk_function_code FROM 
(SELECT fk_tenant_id,pk_main_project_code
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @budget_year BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)
UNION
SELECT fk_tenant_id,pk_main_project_code
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @budget_year-1 BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)
UNION 
SELECT fk_tenant_id,pk_main_project_code
FROM tco_main_projects WHERE fk_tenant_id = @tenant_id AND @budget_year-2 BETWEEN DATEPART(year, budget_year_from) AND DATEPART(year, budget_year_to)
) s

UPDATE a SET a.main_project_name = b.main_project_name, a.fk_department_code = ISNULL(b.fk_department_code,''), a.fk_function_code = ISNULL(b.fk_function_code, '')
from #valid_mainprojects a
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @budget_year BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)
WHERE a.main_project_name = ''

UPDATE a SET a.main_project_name = b.main_project_name, a.fk_department_code = ISNULL(b.fk_department_code,''), a.fk_function_code = ISNULL(b.fk_function_code, '')
from #valid_mainprojects a
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @budget_year-1 BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)
WHERE a.main_project_name = ''

UPDATE a SET a.main_project_name = b.main_project_name, a.fk_department_code = ISNULL(b.fk_department_code,''), a.fk_function_code = ISNULL(b.fk_function_code, '')
from #valid_mainprojects a
JOIN tco_main_projects b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_main_project_code = b.pk_main_project_code AND @budget_year-2 BETWEEN DATEPART(year, b.budget_year_from) AND DATEPART(year, b.budget_year_to)
WHERE a.main_project_name = ''

SELECT oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,
oh.org_name_1, oh.org_name_2, oh.org_name_3, oh.org_name_4, oh.org_name_5,
oh.fk_department_code, p.pk_project_code
INTO #org_data
FROM tco_org_hierarchy oh 
LEFT JOIN #valid_mainprojects mp ON oh.fk_tenant_id = mp.fk_tenant_id AND oh.fk_department_code = mp.fk_department_code --AND @budget_year BETWEEN DATEPART(YEAR,mp.budget_year_from) AND DATEPART(YEAR,mp.budget_year_to) 
LEFT JOIN #valid_projects p ON mp.fk_tenant_id = p.fk_tenant_id  AND p.fk_main_project_code = mp.pk_main_project_code --and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
WHERE oh.fk_tenant_id = @tenant_id AND oh.fk_org_version = @org_version

SELECT sv.service_id_1, sv.service_id_2,sv.service_id_3, sv.service_id_4, sv.service_id_5,
sv.service_name_1, sv.service_name_2, sv.service_name_3, sv.service_name_4, sv.service_name_5,
sv.fk_function_code, p.pk_project_code
INTO #service_data
FROM tco_service_values sv 
LEFT JOIN #valid_mainprojects mp ON sv.fk_tenant_id = mp.fk_tenant_id AND sv.fk_function_code = mp.fk_function_code 
LEFT JOIN #valid_projects p ON mp.fk_tenant_id = p.fk_tenant_id  AND p.fk_main_project_code = mp.pk_main_project_code 
WHERE sv.fk_tenant_id = @tenant_id



RAISERROR ('START : Fetch data from base tables', 0, 1) WITH NOWAIT



select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,
pt.free_dim_1, pt.free_dim_2, pt.free_dim_3,pt.free_dim_4,
ISNULL(pt.fk_change_id,0) AS fk_change_id, 
pt.fk_alter_code, fk_adjustment_code = pt.fk_user_adjustment_code,
fk_bud_auth_code=ISNULL(UAD.fk_bud_auth_code,''), case_ref=ISNULL(UAD.case_ref,''), fk_attribute_id=ISNULL(UAD.fk_attribute_id,''),
isnull(p.fk_prog_code,'1') AS fk_prog_code,
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function, 
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,
adjustment_code_status = CASE    
WHEN UAD.status = 1 THEN 1
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 1
ELSE 0
END,
rebudget_approved_flag = BC.rebudget_approved
INTO #inv_hlptab
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year,0 as rebudget_approved from tfp_budget_changes
        where fk_tenant_id = @tenant_id
        and budget_year < @budget_year
        UNION
        select fk_tenant_id, pk_change_id, org_budget_flag, budget_year,rebudget_approved from tfp_budget_changes
        where fk_tenant_id = @tenant_id
        and budget_year = @budget_year
    )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
where PT.fk_tenant_id = @tenant_id
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_user_adjustment_code,
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,
UAD.status, UAD.include_in_calculation,BC.budget_year,pt.free_dim_1, pt.free_dim_2, pt.free_dim_3,pt.free_dim_4,
UAD.fk_bud_auth_code, UAD.case_ref, UAD.fk_attribute_id, BC.rebudget_approved

DELETE FROM #inv_hlptab WHERE adjustment_code_status = 0

	SET @default_change_id = (
	SELECT top 1 pk_change_id FROM tfp_budget_changes ch
	JOIN tco_budget_phase ph ON ch.fk_tenant_id = ph.fk_tenant_id AND ch.fk_budget_phase_id = ph.pk_budget_phase_id
	WHERE ch.fk_tenant_id = @tenant_id AND ch.budget_year = @budget_year AND ch.org_budget_flag = 1
	ORDER BY ph.sort_order, ch.pk_change_id)

    if @default_change_id is null
    begin
    set @default_change_id = 0
    end


	UPDATE a SET a.fk_change_id = @default_change_id
	FROM #inv_hlptab a
	JOIN tfp_budget_changes ch ON ch.fk_tenant_id = a.fk_tenant_id AND a.fk_change_id = ch.pk_change_id and ch.budget_year < @budget_year
	; 



SELECT h.fk_tenant_id,@budget_year AS budget_year,h.pk_main_project_code,h.main_project_name,h.inv_status,
h.is_temp,completion_date,h.fk_account_code,h.fk_department_code,
h.fk_function_code,h.fk_project_code,h.project_name,
h.free_dim_1, h.free_dim_2, h.free_dim_3,h.free_dim_4,
fk_change_id = ISNULL(bc.approval_reference,'INGEN'),
h.fk_alter_code,h.fk_adjustment_code,
h.fk_bud_auth_code, h.case_ref, h.fk_attribute_id,
h.org_budget_flag,
h.fk_prog_code,header_dept, h.header_function,h.approval_reference, 
h.approval_ref_url,h.original_finish_year,
org_year_1_amount = CASE WHEN year = @budget_year AND h.org_budget_flag = 1 THEN h.amount else 0 end,
rev_year_1_amount = CASE WHEN year = @budget_year THEN h.amount else 0 end,
previously_budgeted = CASE WHEN year < @budget_year AND year != -1 THEN h.amount ELSE 0 END,
approved_cost = CASE WHEN year = -1 THEN h.amount else 0 end,
h.rebudget_approved_flag
INTO #inv_hlptab2
 FROM #inv_hlptab h
LEFT JOIN tfp_budget_changes bc ON h.fk_change_id = bc.pk_change_id AND h.fk_tenant_id = bc.fk_tenant_id

-- Remove b-list actions 

UPDATE #inv_hlptab2 SET  org_year_1_amount = 0, rev_year_1_amount = 0
WHERE inv_status IN (3,4,5)

 SELECT a.fk_tenant_id,a.budget_year,fp_level_1_value = CONVERT(NVARCHAR(25),''),fp_level_1_name= CONVERT(NVARCHAR(100),''),
 fp_level_2_value = CONVERT(NVARCHAR(25),''), fp_level_2_name = CONVERT(NVARCHAR(100),''),
 ISNULL(a.pk_main_project_code,'') AS pk_main_project_code,
 ISNULL(a.main_project_name,'') AS main_project_name,
 ISNULL(a.inv_status,100) AS inv_status,
 ISNULL(a.is_temp,0) AS is_temp,
 ISNULL(a.completion_date,'1900-01-01') AS completion_date,
 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,
 a.free_dim_1, a.free_dim_2, a.free_dim_3,a.free_dim_4,
 a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,
 a.fk_bud_auth_code, a.case_ref, a.fk_attribute_id,a.org_budget_flag,
 a.fk_prog_code,a.header_dept, a.header_function,
 oh.org_id_1 as header_org_id_1, oh.org_name_1 as header_org_name_1, oh.org_id_2  as header_org_id_2, oh.org_name_2 as header_org_name_2,
 oh.org_id_3  as header_org_id_3, oh.org_name_3 as header_org_name_3,oh.org_id_4  as header_org_id_4, oh.org_name_4 as header_org_name_4,
 oh.org_id_5  as header_org_id_5, oh.org_name_5 as header_org_name_5,
 ISNULL(od.org_id_1,'ZZ') as detail_org_id_1, ISNULL(od.org_name_1, 'Ugyldig kode') as detail_org_name_1, ISNULL(od.org_id_2,'ZZ')  as detail_org_id_2, ISNULL(od.org_name_2, 'Ugyldig kode') as detail_org_name_2,
 ISNULL(od.org_id_3,'ZZ') as detail_org_id_3, ISNULL(od.org_name_3, 'Ugyldig kode') as detail_org_name_3, ISNULL(od.org_id_4,'ZZ')  as detail_org_id_4, ISNULL(od.org_name_4, 'Ugyldig kode') as detail_org_name_4,
 ISNULL(od.org_id_5,'ZZ') as detail_org_id_5, ISNULL(od.org_name_5, 'Ugyldig kode') as detail_org_name_5,
 ISNULL(sh.service_id_1,'')  as header_service_id_1, ISNULL(sh.service_name_1,'') as header_service_name_1, ISNULL(sh.service_id_2,'')  as header_service_id_2, ISNULL(sh.service_name_2,'') as header_service_name_2,
 ISNULL(sh.service_id_3,'')  as header_service_id_3, ISNULL(sh.service_name_3,'') as header_service_name_3, ISNULL(sh.service_id_4,'')  as header_service_id_4, ISNULL(sh.service_name_4,'') as header_service_name_4,
 ISNULL(sh.service_id_5,'')  as header_service_id_5, ISNULL(sh.service_name_5,'') as header_service_name_5,														
 ISNULL(sd.service_id_1,'')  as detail_service_id_1, ISNULL(sd.service_name_1,'') as detail_service_name_1, ISNULL(sd.service_id_2,'')  as detail_service_id_2, ISNULL(sd.service_name_2,'') as detail_service_name_2,
 ISNULL(sd.service_id_3,'')  as detail_service_id_3, ISNULL(sd.service_name_3,'') as detail_service_name_3, ISNULL(sd.service_id_4,'')  as detail_service_id_4, ISNULL(sd.service_name_4,'') as detail_service_name_4,
 ISNULL(sd.service_id_5,'')  as detail_service_id_5, ISNULL(sd.service_name_5,'') as detail_service_name_5,
 ISNULL(ip.description,'') as program_code_description,rl.line_group_id, rl.line_item_id,
 sum(org_year_1_amount) as org_year_1_amount,sum(rev_year_1_amount) as rev_year_1_amount,
SUM(approved_cost) as approved_cost, 
ISNULL(a.approval_reference,'') AS approval_reference,
ISNULL(a.approval_ref_url,'') AS approval_ref_url,
ISNULL(a.original_finish_year,0) AS original_finish_year,
type = CASE WHEN rl.line_item_id = 1010 THEN 'i' ELSE 'f' END,
a.rebudget_approved_flag
 INTO #inv_hlptab3
 FROM #inv_hlptab2 a
 LEFT JOIN tco_org_hierarchy oh ON a.header_dept = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = @org_version
 LEFT JOIN tco_org_hierarchy od ON a.fk_department_code = od.fk_department_code AND a.fk_tenant_id = od.fk_tenant_id AND od.fk_org_version = @org_version
 LEFT JOIN tco_service_values sh ON a.header_function = sh.fk_function_code AND a.fk_tenant_id = sh.fk_tenant_id
 LEFT JOIN tco_service_values sd ON a.fk_function_code = sd.fk_function_code AND a.fk_tenant_id = sd.fk_tenant_id
 LEFT JOIN tco_inv_program ip ON a.fk_prog_code = ip.pk_prog_code AND a.fk_tenant_id = ip.fk_tenant_id
 LEFT JOIN #valid_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code --AND a.budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
 JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = @report2A
 group by a.fk_tenant_id,a.budget_year,a.pk_main_project_code,a.main_project_name,a.inv_status,a.is_temp,completion_date,
 a.fk_account_code,a.fk_department_code,a.fk_function_code,a.fk_project_code,a.project_name,a.fk_change_id,a.fk_alter_code,a.fk_adjustment_code,
 a.fk_prog_code,a.header_dept, a.header_function,
 oh.org_id_1, oh.org_name_1, oh.org_id_2, oh.org_name_2,
 oh.org_id_3, oh.org_name_3,oh.org_id_4, oh.org_name_4,
 oh.org_id_5, oh.org_name_5,
 od.org_id_1, od.org_name_1, od.org_id_2, od.org_name_2,
 od.org_id_3, od.org_name_3, od.org_id_4, od.org_name_4,
 od.org_id_5, od.org_name_5,  sh.service_id_1, sh.service_name_1, sh.service_id_2, sh.service_name_2,
 sh.service_id_3, sh.service_name_3,sh.service_id_4, sh.service_name_4,
 sh.service_id_5, sh.service_name_5,
 sd.service_id_1, sd.service_name_1, sd.service_id_2, sd.service_name_2,
 sd.service_id_3, sd.service_name_3,sd.service_id_4, sd.service_name_4,
 sd.service_id_5, sd.service_name_5,
ip.description,rl.line_group_id, rl.line_item_id,a.approval_reference,a.approval_ref_url, 
a.original_finish_year, a.free_dim_1, a.free_dim_2, a.free_dim_3,a.free_dim_4,
 a.fk_bud_auth_code, a.case_ref, a.fk_attribute_id,a.org_budget_flag, a.rebudget_approved_flag
;


UPDATE #inv_hlptab3 SET 
header_org_id_1 = detail_org_id_1,
header_org_id_2 = detail_org_id_2,
header_org_id_3 = detail_org_id_3,
header_org_id_4 = detail_org_id_4,
header_org_id_5 = detail_org_id_5,
header_org_name_1 = detail_org_name_1,
header_org_name_2 = detail_org_name_2,
header_org_name_3 = detail_org_name_3,
header_org_name_4 = detail_org_name_4,
header_org_name_5 = detail_org_name_5,
header_dept = fk_department_code
WHERE inv_status = 100 or header_org_id_1 is null;


PRINT 'Get investment finplan data'

CREATE TABLE #temp_table_1
	(fk_tenant_id INT NOT NULL, 
    pk_main_project_code NVARCHAR(25) NOT NULL, 
    main_project_name NVARCHAR(100) NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
    project_name NVARCHAR(250) NOT NULL,
	free_dim_1 NVARCHAR(25) NOT NULL, 
	free_dim_2 NVARCHAR(25) NOT NULL, 
	free_dim_3 NVARCHAR(25) NOT NULL, 
	free_dim_4 NVARCHAR(25) NOT NULL, 
    org_year_1_amount DECIMAL(18, 2) NOT NULL, 
    rev_year_1_amount DECIMAL(18, 2) NOT NULL,
	[fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',
	org_budget_flag INT NOT NULL DEFAULT 1,
	inv_fk_org_id NVARCHAR(25)  NULL, 
	inv_org_name NVARCHAR(255)  NULL,
	[fk_adjustment_code] NVARCHAR(25) DEFAULT '' NOT NULL,
	[fk_change_id] NVARCHAR(50) NOT NULL DEFAULT '',
	fk_bud_auth_code NVARCHAR(8) NOT NULL,
	[fk_attribute_id] [nvarchar](25) NOT NULL,
	rebudget_approved_flag INT NOT NULL
)

INSERT INTO #temp_table_1 (fk_tenant_id, pk_main_project_code, main_project_name, fk_account_code, fk_department_code, fk_function_code, 
 free_dim_1, free_dim_2, free_dim_3,free_dim_4, fk_project_code, project_name,fk_change_id, 
 	fk_adjustment_code,fk_bud_auth_code,[fk_attribute_id],org_budget_flag,org_year_1_amount, rev_year_1_amount, rebudget_approved_flag)

SELECT a.fk_tenant_id, a.pk_main_project_code, a.main_project_name, a.fk_account_code, a.fk_department_code, a.fk_function_code, 
 a.free_dim_1, a.free_dim_2, a.free_dim_3,a.free_dim_4, a.fk_project_code, a.project_name,fk_change_id, 
 	a.[fk_adjustment_code],
	a.fk_bud_auth_code,
	a.[fk_attribute_id],a.org_budget_flag,
	SUM(org_year_1_amount), SUM(rev_year_1_amount),a.rebudget_approved_flag
FROM #inv_hlptab3 a
GROUP BY a.fk_tenant_id, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.pk_main_project_code, a.main_project_name,
a.fk_project_code,a.project_name,a.fk_prog_code, fk_change_id, a.free_dim_1, a.free_dim_2, a.free_dim_3,a.free_dim_4,
 	a.[fk_adjustment_code],
	a.fk_bud_auth_code,
	a.[fk_attribute_id],a.org_budget_flag,a.rebudget_approved_flag


PRINT 'Fecth investment header org/service'

UPDATE a
SET a.inv_fk_org_id = CASE
WHEN @param_value_18 = 'org_id_1' THEN b.org_id_1
WHEN @param_value_18 = 'org_id_2' THEN b.org_id_2
WHEN @param_value_18 = 'org_id_3' THEN b.org_id_3
WHEN @param_value_18 = 'org_id_4' THEN b.org_id_4
WHEN @param_value_18 = 'org_id_5' THEN b.org_id_5
ELSE b.org_id_2
END,
inv_org_name = CASE 
WHEN @param_value_18 = 'org_id_1' THEN b.org_name_1
WHEN @param_value_18 = 'org_id_2' THEN b.org_name_2
WHEN @param_value_18 = 'org_id_3' THEN b.org_name_3
WHEN @param_value_18 = 'org_id_4' THEN b.org_name_4
WHEN @param_value_18 = 'org_id_5' THEN b.org_name_5
ELSE b.org_name_2
END
FROM #temp_table_1 a
JOIN #org_data b ON a.fk_project_code = b.pk_project_code 
WHERE @param_value_18 like 'org%'

UPDATE a
SET a.inv_fk_org_id = CASE
WHEN @param_value_18 = 'org_id_1' THEN b.org_id_1
WHEN @param_value_18 = 'org_id_2' THEN b.org_id_2
WHEN @param_value_18 = 'org_id_3' THEN b.org_id_3
WHEN @param_value_18 = 'org_id_4' THEN b.org_id_4
WHEN @param_value_18 = 'org_id_5' THEN b.org_id_5
ELSE b.org_id_2
END,
inv_org_name = CASE 
WHEN @param_value_18 = 'org_id_1' THEN b.org_name_1
WHEN @param_value_18 = 'org_id_2' THEN b.org_name_2
WHEN @param_value_18 = 'org_id_3' THEN b.org_name_3
WHEN @param_value_18 = 'org_id_4' THEN b.org_name_4
WHEN @param_value_18 = 'org_id_5' THEN b.org_name_5
ELSE b.org_name_2
END
FROM #temp_table_1 a
JOIN #org_data b ON a.fk_department_code = b.fk_department_code
WHERE @param_value_18 like 'org%' AND a.inv_fk_org_id is null



UPDATE a
SET a.inv_fk_org_id = CASE
WHEN @param_value_18 = 'service_id_1' THEN b.service_id_1
WHEN @param_value_18 = 'service_id_2' THEN b.service_id_2
WHEN @param_value_18 = 'service_id_3' THEN b.service_id_3
WHEN @param_value_18 = 'service_id_4' THEN b.service_id_4
WHEN @param_value_18 = 'service_id_5' THEN b.service_id_5
ELSE b.service_id_2
END,
inv_org_name = CASE 
WHEN @param_value_18 = 'service_id_1' THEN b.service_name_1
WHEN @param_value_18 = 'service_id_2' THEN b.service_name_2
WHEN @param_value_18 = 'service_id_3' THEN b.service_name_3
WHEN @param_value_18 = 'service_id_4' THEN b.service_name_4
WHEN @param_value_18 = 'service_id_5' THEN b.service_name_5
ELSE b.service_name_2
END
FROM #temp_table_1 a
JOIN #service_data b ON a.fk_project_code = b.pk_project_code 
WHERE @param_value_18 like 'service%'


UPDATE a
SET a.inv_fk_org_id = CASE
WHEN @param_value_18 = 'service_id_1' THEN b.service_id_1
WHEN @param_value_18 = 'service_id_2' THEN b.service_id_2
WHEN @param_value_18 = 'service_id_3' THEN b.service_id_3
WHEN @param_value_18 = 'service_id_4' THEN b.service_id_4
WHEN @param_value_18 = 'service_id_5' THEN b.service_id_5
ELSE b.service_id_2
END,
inv_org_name = CASE 
WHEN @param_value_18 = 'service_id_1' THEN b.service_name_1
WHEN @param_value_18 = 'service_id_2' THEN b.service_name_2
WHEN @param_value_18 = 'service_id_3' THEN b.service_name_3
WHEN @param_value_18 = 'service_id_4' THEN b.service_name_4
WHEN @param_value_18 = 'service_id_5' THEN b.service_name_5
ELSE b.service_name_2
END
FROM #temp_table_1 a
JOIN #service_data b ON a.fk_function_code = b.fk_function_code
WHERE @param_value_18 like 'service%' AND a.inv_fk_org_id is null


UPDATE a
SET a.inv_fk_org_id = 'ZZ',
inv_org_name = 'Oppsett mangler'
FROM #temp_table_1 a WHERE a.inv_fk_org_id is null

INSERT INTO  #temp_table_2 (fk_tenant_id, 
	budget_year,
	org_budget_flag,
	[aggregate_id_1], 
    [aggregate_name_1],     
	[aggregate_id_2], 
    [aggregate_name_2],
	[aggregate_id_3], 
    [aggregate_name_3],     
	[aggregate_id_4], 
    [aggregate_name_4],
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	[fk_attribute_id],
	finplan_year_1_amount, 
	rebud_approved,
    rebudget_approved_flag,
    [updated], 
    [updated_by]
    )
SELECT a.fk_tenant_id,
budget_year = @budget_year,
	a.org_budget_flag,
	aggregate_id_1 = c.line_group_id, 
    aggregate_name_1 = c.line_group,     
	aggregate_id_2 = c.line_item_id, 
    aggregate_name_2 = c.line_item,
	aggregate_id_3 =
        CASE 	WHEN @sub_header_id = 'ZZ' THEN @sub_header_id
		        WHEN @sub_header_id = 'pk_main_project_code' THEN ISNULL (mp.pk_main_project_code,'ZZZZ')
		        WHEN @sub_header_id = 'pk_prog_code' THEN ISNULL (p.pk_prog_code, 'ZZ')                           
                WHEN @sub_header_id = 'proj_gr_1' AND ph.proj_gr_1 = '' THEN 'ZZ'
                WHEN @sub_header_id = 'proj_gr_2' AND ph.proj_gr_2 = '' THEN 'ZZ'
                WHEN @sub_header_id = 'proj_gr_3' AND ph.proj_gr_3 = '' THEN 'ZZ'
                WHEN @sub_header_id = 'proj_gr_4' AND ph.proj_gr_4 = '' THEN 'ZZ'
                WHEN @sub_header_id = 'proj_gr_5' AND ph.proj_gr_5 = '' THEN 'ZZ'
		        WHEN @sub_header_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_1,'ZZ')
                WHEN @sub_header_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_2,'ZZ')
                WHEN @sub_header_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_3,'ZZ')
                WHEN @sub_header_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_4,'ZZ')
                WHEN @sub_header_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_5,'ZZ')
                WHEN @sub_header_id = 'org_id_header_1' THEN ISNULL(oh2.org_id_1,'ZZ')
                WHEN @sub_header_id = 'org_id_header_2' THEN ISNULL(oh2.org_id_2,'ZZ')
                WHEN @sub_header_id = 'org_id_header_3' THEN ISNULL(oh2.org_id_3,'ZZ')
                WHEN @sub_header_id = 'org_id_header_4' THEN ISNULL(oh2.org_id_4,'ZZ')
                WHEN @sub_header_id = 'org_id_header_5' THEN ISNULL(oh2.org_id_5,'ZZ')
                WHEN @sub_header_id = 'service_id_header_1' THEN ISNULL(sv2.service_id_1,'ZZ')                 
                WHEN @sub_header_id = 'service_id_header_2' THEN ISNULL(sv2.service_id_2,'ZZ')
                WHEN @sub_header_id = 'service_id_header_3' THEN ISNULL(sv2.service_id_3,'ZZ')
                WHEN @sub_header_id = 'service_id_header_4' THEN ISNULL(sv2.service_id_4,'ZZ')
                WHEN @sub_header_id = 'service_id_header_5' THEN ISNULL(sv2.service_id_5,'ZZ')  
		        WHEN @sub_header_id = 'org_id_1' THEN ISNULL(oh.org_id_1,'ZZ')
                WHEN @sub_header_id = 'org_id_2' THEN ISNULL(oh.org_id_2,'ZZ')
                WHEN @sub_header_id = 'org_id_3' THEN ISNULL(oh.org_id_3,'ZZ')
                WHEN @sub_header_id = 'org_id_4' THEN ISNULL(oh.org_id_4,'ZZ')
                WHEN @sub_header_id = 'org_id_5' THEN ISNULL(oh.org_id_5,'ZZ')
                WHEN @sub_header_id = 'service_id_1' THEN ISNULL(sv.service_id_1,'ZZ')                 
                WHEN @sub_header_id = 'service_id_2' THEN ISNULL(sv.service_id_2,'ZZ')
                WHEN @sub_header_id = 'service_id_3' THEN ISNULL(sv.service_id_3,'ZZ')
                WHEN @sub_header_id = 'service_id_4' THEN ISNULL(sv.service_id_4,'ZZ')
                WHEN @sub_header_id = 'service_id_5' THEN ISNULL(sv.service_id_5,'ZZ')  
        ELSE oh.org_id_2
        END, 
    aggregate_name_3 = 
        CASE  	WHEN @sub_header_id = 'ZZ' THEN 'Investeringer'
		        WHEN @sub_header_id = 'pk_main_project_code' THEN ISNULL(mp.main_project_name, @langstring_project)
		        WHEN @sub_header_id = 'pk_main_project_code' AND p5.param_value = 'TRUE' THEN ISNULL(mp.pk_main_project_code + ' - ' + mp.main_project_name, @langstring_project)
		        WHEN @sub_header_id = 'pk_prog_code' THEN ISNULL (p.description, 'Programkode mangler')
                WHEN @sub_header_id = 'proj_gr_1' AND ph.proj_gr_name_1 = '' THEN 'Prosjektstruktur mangler'
                WHEN @sub_header_id = 'proj_gr_2' AND ph.proj_gr_name_2 = '' THEN 'Prosjektstruktur mangler'
                WHEN @sub_header_id = 'proj_gr_3' AND ph.proj_gr_name_3 = '' THEN 'Prosjektstruktur mangler'
                WHEN @sub_header_id = 'proj_gr_4' AND ph.proj_gr_name_4 = '' THEN 'Prosjektstruktur mangler'
                WHEN @sub_header_id = 'proj_gr_5' AND ph.proj_gr_name_5 = '' THEN 'Prosjektstruktur mangler'
                WHEN @sub_header_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_name_1,'Prosjektstruktur mangler')
                WHEN @sub_header_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_name_2,'Prosjektstruktur mangler')
                WHEN @sub_header_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_name_3,'Prosjektstruktur mangler')
                WHEN @sub_header_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_name_4,'Prosjektstruktur mangler')
                WHEN @sub_header_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_name_5,'Prosjektstruktur mangler')
                WHEN @sub_header_id = 'org_id_header_1' THEN ISNULL(oh2.org_name_1,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_header_2' THEN ISNULL(oh2.org_name_2,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_header_3' THEN ISNULL(oh2.org_name_3,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_header_4' THEN ISNULL(oh2.org_name_4,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_header_5' THEN ISNULL(oh2.org_name_5,'Org. oppsett mangler')
                WHEN @sub_header_id = 'service_id_header_1' THEN ISNULL(sv2.service_name_1,'Tjenesteoppsett mangler')                 
                WHEN @sub_header_id = 'service_id_header_2' THEN ISNULL(sv2.service_name_2,'Tjenesteoppsett mangler')
                WHEN @sub_header_id = 'service_id_header_3' THEN ISNULL(sv2.service_name_3,'Tjenesteoppsett mangler')
                WHEN @sub_header_id = 'service_id_header_4' THEN ISNULL(sv2.service_name_4,'Tjenesteoppsett mangler')
                WHEN @sub_header_id = 'service_id_header_5' THEN ISNULL(sv2.service_name_5,'Tjenesteoppsett mangler')  
		        WHEN @sub_header_id = 'org_id_1' THEN ISNULL(oh.org_name_1,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_2' THEN ISNULL(oh.org_name_2,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_3' THEN ISNULL(oh.org_name_3,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_4' THEN ISNULL(oh.org_name_4,'Org. oppsett mangler')
                WHEN @sub_header_id = 'org_id_5' THEN ISNULL(oh.org_name_5,'Org. oppsett mangler')
                WHEN @sub_header_id = 'service_id_1' THEN ISNULL(sv.service_name_1,'Tjenesteoppsett mangler')                 
                WHEN @sub_header_id = 'service_id_2' THEN ISNULL(sv.service_name_2,'Tjenesteoppsett mangler')
                WHEN @sub_header_id = 'service_id_3' THEN ISNULL(sv.service_name_3,'Tjenesteoppsett mangler')
                WHEN @sub_header_id = 'service_id_4' THEN ISNULL(sv.service_name_4,'Tjenesteoppsett mangler')
                WHEN @sub_header_id = 'service_id_5' THEN ISNULL(sv.service_name_5,'Tjenesteoppsett mangler')   
        ELSE oh.org_name_2
        END,     
    aggregate_id_4 =
        CASE  	WHEN @aggregate_id = 'pk_main_project_code' THEN ISNULL (mp.pk_main_project_code,'ZZZZ')
		        WHEN @aggregate_id = 'pk_prog_code' THEN ISNULL (p.pk_prog_code, 'zz')                           
                WHEN @aggregate_id = 'proj_gr_1' AND ph.proj_gr_1 = '' THEN 'ZZ'
                WHEN @aggregate_id = 'proj_gr_2' AND ph.proj_gr_2 = '' THEN 'ZZ'
                WHEN @aggregate_id = 'proj_gr_3' AND ph.proj_gr_3 = '' THEN 'ZZ'
                WHEN @aggregate_id = 'proj_gr_4' AND ph.proj_gr_4 = '' THEN 'ZZ'
                WHEN @aggregate_id = 'proj_gr_5' AND ph.proj_gr_5 = '' THEN 'ZZ'
		        WHEN @aggregate_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_1,'ZZ')
                WHEN @aggregate_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_2,'ZZ')
                WHEN @aggregate_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_3,'ZZ')
                WHEN @aggregate_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_4,'ZZ')
                WHEN @aggregate_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_5,'ZZ')
                WHEN @aggregate_id = 'org_id_header_1' THEN ISNULL(oh2.org_id_1,'ZZ')
                WHEN @aggregate_id = 'org_id_header_2' THEN ISNULL(oh2.org_id_2,'ZZ')
                WHEN @aggregate_id = 'org_id_header_3' THEN ISNULL(oh2.org_id_3,'ZZ')
                WHEN @aggregate_id = 'org_id_header_4' THEN ISNULL(oh2.org_id_4,'ZZ')
                WHEN @aggregate_id = 'org_id_header_5' THEN ISNULL(oh2.org_id_5,'ZZ')
                WHEN @aggregate_id = 'service_id_header_1' THEN ISNULL(sv2.service_id_1,'ZZ')                 
                WHEN @aggregate_id = 'service_id_header_2' THEN ISNULL(sv2.service_id_2,'ZZ')
                WHEN @aggregate_id = 'service_id_header_3' THEN ISNULL(sv2.service_id_3,'ZZ')
                WHEN @aggregate_id = 'service_id_header_4' THEN ISNULL(sv2.service_id_4,'ZZ')
                WHEN @aggregate_id = 'service_id_header_5' THEN ISNULL(sv2.service_id_5,'ZZ')  
		        WHEN @aggregate_id = 'org_id_1' THEN ISNULL(oh.org_id_1,'ZZ')
                WHEN @aggregate_id = 'org_id_2' THEN ISNULL(oh.org_id_2,'ZZ')
                WHEN @aggregate_id = 'org_id_3' THEN ISNULL(oh.org_id_3,'ZZ')
                WHEN @aggregate_id = 'org_id_4' THEN ISNULL(oh.org_id_4,'ZZ')
                WHEN @aggregate_id = 'org_id_5' THEN ISNULL(oh.org_id_5,'ZZ')
                WHEN @aggregate_id = 'service_id_1' THEN ISNULL(sv.service_id_1,'ZZ')                 
                WHEN @aggregate_id = 'service_id_2' THEN ISNULL(sv.service_id_2,'ZZ')
                WHEN @aggregate_id = 'service_id_3' THEN ISNULL(sv.service_id_3,'ZZ')
                WHEN @aggregate_id = 'service_id_4' THEN ISNULL(sv.service_id_4,'ZZ')
                WHEN @aggregate_id = 'service_id_5' THEN ISNULL(sv.service_id_5,'ZZ')   
        ELSE ''
        END, 
    aggregate_name_4 =
        CASE  	WHEN @aggregate_id = 'pk_main_project_code' THEN ISNULL(mp.main_project_name, @langstring_project)
		        WHEN @aggregate_id = 'pk_main_project_code' AND p5.param_value = 'TRUE' THEN ISNULL(mp.pk_main_project_code + ' - ' + mp.main_project_name, @langstring_project)
		        WHEN @aggregate_id = 'pk_prog_code' THEN ISNULL (p.description, 'Programkode mangler')
                WHEN @aggregate_id = 'proj_gr_1' AND ph.proj_gr_name_1 = '' THEN 'Prosjektstruktur mangler'
                WHEN @aggregate_id = 'proj_gr_2' AND ph.proj_gr_name_2 = '' THEN 'Prosjektstruktur mangler'
                WHEN @aggregate_id = 'proj_gr_3' AND ph.proj_gr_name_3 = '' THEN 'Prosjektstruktur mangler'
                WHEN @aggregate_id = 'proj_gr_4' AND ph.proj_gr_name_4 = '' THEN 'Prosjektstruktur mangler'
                WHEN @aggregate_id = 'proj_gr_5' AND ph.proj_gr_name_5 = '' THEN 'Prosjektstruktur mangler'
                WHEN @aggregate_id = 'proj_gr_1' THEN ISNULL(ph.proj_gr_name_1,'Prosjektstruktur mangler')
                WHEN @aggregate_id = 'proj_gr_2' THEN ISNULL(ph.proj_gr_name_2,'Prosjektstruktur mangler')
                WHEN @aggregate_id = 'proj_gr_3' THEN ISNULL(ph.proj_gr_name_3,'Prosjektstruktur mangler')
                WHEN @aggregate_id = 'proj_gr_4' THEN ISNULL(ph.proj_gr_name_4,'Prosjektstruktur mangler')
                WHEN @aggregate_id = 'proj_gr_5' THEN ISNULL(ph.proj_gr_name_5,'Prosjektstruktur mangler')
                WHEN @aggregate_id = 'org_id_header_1' THEN ISNULL(oh2.org_name_1,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_header_2' THEN ISNULL(oh2.org_name_2,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_header_3' THEN ISNULL(oh2.org_name_3,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_header_4' THEN ISNULL(oh2.org_name_4,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_header_5' THEN ISNULL(oh2.org_name_5,'Org. oppsett mangler')
                WHEN @aggregate_id = 'service_id_header_1' THEN ISNULL(sv2.service_name_1,'Tjenesteoppsett mangler')                 
                WHEN @aggregate_id = 'service_id_header_2' THEN ISNULL(sv2.service_name_2,'Tjenesteoppsett mangler')
                WHEN @aggregate_id = 'service_id_header_3' THEN ISNULL(sv2.service_name_3,'Tjenesteoppsett mangler')
                WHEN @aggregate_id = 'service_id_header_4' THEN ISNULL(sv2.service_name_4,'Tjenesteoppsett mangler')
                WHEN @aggregate_id = 'service_id_header_5' THEN ISNULL(sv2.service_name_5,'Tjenesteoppsett mangler')  
		        WHEN @aggregate_id = 'org_id_1' THEN ISNULL(oh.org_name_1,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_2' THEN ISNULL(oh.org_name_2,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_3' THEN ISNULL(oh.org_name_3,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_4' THEN ISNULL(oh.org_name_4,'Org. oppsett mangler')
                WHEN @aggregate_id = 'org_id_5' THEN ISNULL(oh.org_name_5,'Org. oppsett mangler')
                WHEN @aggregate_id = 'service_id_1' THEN ISNULL(sv.service_name_1,'Tjenesteoppsett mangler')                 
                WHEN @aggregate_id = 'service_id_2' THEN ISNULL(sv.service_name_2,'Tjenesteoppsett mangler')
                WHEN @aggregate_id = 'service_id_3' THEN ISNULL(sv.service_name_3,'Tjenesteoppsett mangler')
                WHEN @aggregate_id = 'service_id_4' THEN ISNULL(sv.service_name_4,'Tjenesteoppsett mangler')
                WHEN @aggregate_id = 'service_id_5' THEN ISNULL(sv.service_name_5,'Tjenesteoppsett mangler')   
        ELSE ''
        END,
	fk_adj_code = a.fk_adjustment_code,
	a.fk_change_id,
	a.fk_bud_auth_code,
	a.fk_attribute_id,
	finplan_year_1_amount = a.org_year_1_amount, 
	rebud_approved = a.rev_year_1_amount,
    a.rebudget_approved_flag,
    updated = GETDATE(), 
    updated_by = @user_id
FROM #temp_table_1 a 
             JOIN #valid_accounts b ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id 
             JOIN @tbl_reporting_line c ON b.fk_kostra_account_code = c.fk_kostra_account_code AND c.report = @report2A 
             LEFT JOIN tco_inv_program p ON a.fk_prog_code = p.pk_prog_code AND a.fk_tenant_id = p.fk_tenant_id
             LEFT JOIN #valid_projects f ON f.pk_project_code = a.fk_project_code AND f.fk_tenant_id = a.fk_tenant_id AND f.fk_main_project_code != '' 
			 LEFT JOIN #valid_mainprojects mp ON mp.fk_tenant_id = f.fk_tenant_id AND mp.pk_main_project_code = f.fk_main_project_code 
             LEFT JOIN tco_service_values sv on a.fk_tenant_id = sv.fk_tenant_id AND a.fk_function_code = sv.fk_function_code
             JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_tenant_id AND a.fk_department_code = oh.fk_department_code   and oh.fk_org_version = @org_version
			 LEFT JOIN tco_proj_version pv ON a.fk_tenant_id = pv.fk_tenant_id and pv.active = 1 and @budget_year BETWEEN pv.period_from and pv.period_to
			 LEFT JOIN tco_proj_hierarchy ph ON a.fk_tenant_id = ph.fk_tenant_id AND pv.pk_proj_version = ph.fk_proj_version AND a.fk_project_code = ph.fk_project_code
             LEFT JOIN tco_parameters p5 ON a.fk_tenant_id = p5.fk_tenant_id AND p5.param_name = '2B_DISPLAY_PROJECT_CODE' AND p5.param_value = 'TRUE' AND p5.active = 1
			LEFT JOIN #org_data oh2 ON a.fk_project_code = oh2.pk_project_code
			LEFT JOIN #service_data sv2 ON  a.fk_project_code = sv2.pk_project_code



DELETE FROM twh_budalloc_project_data WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year


UPDATE #temp_table_2 SET fk_adj_code = 'INGEN' WHERE fk_adj_code = '' AND org_budget_flag = 0
UPDATE #temp_table_2 SET fk_change_id = 'INGEN' WHERE fk_change_id = '' AND org_budget_flag = 0
UPDATE #temp_table_2 SET fk_bud_auth_code = 'INGEN' WHERE fk_bud_auth_code = '' AND org_budget_flag = 0
UPDATE #temp_table_2 SET fk_attribute_id = 'INGEN' WHERE fk_attribute_id = '' AND org_budget_flag = 0


INSERT INTO twh_budalloc_project_data (
    fk_tenant_id, 
	budget_year,
	org_budget_flag,
	[aggregate_id_1], 
    [aggregate_name_1],     
	[aggregate_id_2], 
    [aggregate_name_2],
	[aggregate_id_3], 
    [aggregate_name_3],     
	[aggregate_id_4], 
    [aggregate_name_4],
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	[fk_attribute_id],
	finplan_year_1_amount, 
	rebud_approved,
    rebudget_approved_flag,
    [updated], 
    [updated_by]
    )
SELECT 
    fk_tenant_id,
    budget_year,
	org_budget_flag,
	[aggregate_id_1], 
    [aggregate_name_1],     
	[aggregate_id_2], 
    [aggregate_name_2],
	[aggregate_id_3], 
    [aggregate_name_3],     
	[aggregate_id_4], 
    [aggregate_name_4],
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	[fk_attribute_id],
	sum(finplan_year_1_amount), 
	sum(rebud_approved),
    rebudget_approved_flag,
    [updated] = GETDATE(), 
    [updated_by]
    FROM #temp_table_2
    GROUP BY 
    fk_tenant_id,
    budget_year,
	org_budget_flag,
	[aggregate_id_1], 
    [aggregate_name_1],     
	[aggregate_id_2], 
    [aggregate_name_2],
	[aggregate_id_3], 
    [aggregate_name_3],     
	[aggregate_id_4], 
    [aggregate_name_4],
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	[fk_attribute_id],
    rebudget_approved_flag,
    [updated_by]



RAISERROR ('FINISH: Fetch data from investments', 0, 1) WITH NOWAIT
PRINT convert(nvarchar(19),SYSDATETIME())



RETURN 0
GO
