
CREATE OR ALTER PROCEDURE prcMetadataSynk  (@datasource nvarchar(50))

AS
	
DECLARE @script nvarchar(4000) 

begin
print 'Creating global tables - START'


IF OBJECT_ID('gal_alert_definition_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gal_alert_definition_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gal_alert_definition_global]
(
	[pk_alert_id] UNIQUEIDENTIFIER,
	[alert_type] NVARCHAR(500),
	[name] NVARCHAR(500),
	[description] NVARCHAR(1000),
	[recurrence] BIT , -- ONE TIME DEFAULT TO 0 ELSE 1 FOR DAILY
	[subject] NVARCHAR(1000),
	[default_email_template] NVARCHAR(max),
	[default_sms_template] NVARCHAR(500),
	[type] NVARCHAR(250),
	[sort_order] INT 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gal_alert_definition''
)'

exec sp_executesql @script


IF OBJECT_ID('gal_macros_definition_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gal_macros_definition_global
end


set @script =
'CREATE EXTERNAL TABLE [dbo].[gal_macros_definition_global]
(
	[id] [int],
	[macros_name] [nvarchar](50),
	[macros_description] [nvarchar](500)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gal_macros_definition''
)'

exec sp_executesql @script

IF OBJECT_ID('gal_macros_mapping_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gal_macros_mapping_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gal_macros_mapping_global]
(
	[id] INT ,
	[fk_alert_id] UNIQUEIDENTIFIER
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gal_macros_mapping''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_color_defaults_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_color_defaults_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_color_defaults_global](
    [Id]			NVARCHAR (50),
    [Colors]			NVARCHAR (3000)	
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_color_defaults''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_column_defs_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_column_defs_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_column_defs_global]
(
	[table_id] NVARCHAR(50)  , 
    [column_id] NVARCHAR(50) , 
    [lang_string_id] NVARCHAR(50), 
	[internal_langstr_id] NVARCHAR(50), 
    [active] BIT, 
    [sort_order] INT, 
	[is_fixed] BIT,
	[is_left_justify] BIT ,
	[doc_width] INT,
	[web_width] INT,
	[highlight] BIT,
    [is_visible]          BIT,
	[is_finplanyear_col]  BIT  
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_column_defs''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_dashboard_grouping_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_dashboard_grouping_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_dashboard_grouping_global]
(
	[pk_dash_group_id] INT,
	[dash_group_name] NVARCHAR (500),
	[dash_group_langstring] NVARCHAR (500),
	[icon_path] NVARCHAR (500),
	[sort_order] INT,
	[updated] datetime,
	[updated_by] int
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_dashboard_grouping''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_doc_table_defs_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_doc_table_defs_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_doc_table_defs_global]
(
	[table_id] NVARCHAR(50) , 
    [lang_string_id] NVARCHAR(50) , 
    [doc_type] NVARCHAR(20) , 
    [amount_format] INT,
	[is_landscape] BIT, 
    [version] INT,
	[caption] NVARCHAR(max),
    [parameter_json] NVARCHAR(max)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_doc_table_defs''
)'

exec sp_executesql @script



IF OBJECT_ID('gco_integ_error_types_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_integ_error_types_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_integ_error_types_global]
(
	[pk_id] [bigint],
	[pk_error_type] [nvarchar](150),
	[error_name] [nvarchar](150),
	[error_description] [nvarchar](500),
	[flag] [bit] 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_integ_error_types''
)'

exec sp_executesql @script


IF OBJECT_ID('gco_language_string_overrides_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_language_string_overrides_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_language_string_overrides_global] (
    [ID]          NVARCHAR (50),
    [Description] NVARCHAR (1000),
    [Language]    NVARCHAR (10),
    [context] NVARCHAR(25), 
	[tenant_type] INT 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_language_string_overrides''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_language_strings_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_language_strings_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_language_strings_global] (
    [ID]          NVARCHAR (50)  ,
    [Description] NVARCHAR (2000) ,
    [Language]    NVARCHAR (10),
    [context] NVARCHAR(25)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_language_strings''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_modules_new_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_modules_new_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_modules_new_global]
 (
    [pk_module_id] INT ,
    [Name] NVARCHAR (100) 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_modules_new''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_org_validations_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_org_validations_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_org_validations_global]
(
	pk_id int,
	[validation_id] INT ,
	description varchar(500),
	lang_string_id varchar(500)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_org_validations''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_plantype_colors_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_plantype_colors_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_plantype_colors_global]
(
	pk_color_id INT,
	color_name nvarchar(100),
	color_code nvarchar(20),
	is_active_type bit 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_plantype_colors''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_reporting_columns_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_reporting_columns_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_reporting_columns_global]
(
	[pkid] INT , 
    [column_id] NVARCHAR(250), 
    [column_lang_id] NVARCHAR(500), 
    [column_data_type] NVARCHAR(150), 
    [is_searchable] BIT, 
    [page_id] NVARCHAR(50),
    [updated] DATETIME, 
    [updated_by] INT,
    [type] VARCHAR(25),
    [offset] int,
    [isCommaSeperatedData] bit, 
    [alias] NVARCHAR(100),
	[filter_main_data_query] NVARCHAR (500),
    [is_toggle_applicable]  BIT,
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_reporting_columns''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_table_grouping_def_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_table_grouping_def_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_table_grouping_def_global]
(
    [pk_id] INT,
	[table_id] NVARCHAR(50), 
	[table_name] NVARCHAR(255),
	[level_id] NVARCHAR(50),
	[level_name] NVARCHAR(255),
	[grouping_id] NVARCHAR(50),
	[grouping_name] NVARCHAR(255),
    [updated] DATETIME, 
    [updated_by] INT
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_table_grouping_def''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_widgets_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_widgets_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_widgets_global]
(
	[Id] INT, 
    [widget_title] NVARCHAR(100), 
    [widget_Description]  NVARCHAR(1000), 
    [widget_thumbnail_url]  NVARCHAR(1000), 
	[widget_url]  NVARCHAR(1000),
	[allow_multiple] bit ,
	[dashboard_view] NVARCHAR(1000),
	[maximized_view] NVARCHAR(1000),
	[config_view] NVARCHAR(1000),
	[default_width] INT,
	[default_height] INT,
	[updated_date] datetime ,
	[updated_by] int 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_widgets''
)'

exec sp_executesql @script


IF OBJECT_ID('gmd_action_types_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gmd_action_types_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gmd_action_types_global](
	[pk_action_type] [int],
	[pk_language] NVARCHAR(10),
	[action_type_descr] NVARCHAR(100),
	[updated_by] INT, 
    [updated] DATETIME
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gmd_action_types''
)'

exec sp_executesql @script

IF OBJECT_ID('gmd_publish_tree_node_definitions_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gmd_publish_tree_node_definitions_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gmd_publish_tree_node_definitions_global]
(
    [type] VARCHAR(50), 
    [tree_type] VARCHAR(50), 
    [content_type] VARCHAR(50), 
    [is_editable] BIT, 
    [title_key] VARCHAR(50), 
    [expanded] BIT, 
    [is_checked] BIT, 
    [drag] BIT 
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gmd_publish_tree_node_definitions''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_parameter_definitions_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_parameter_definitions_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_parameter_definitions_global]
(
    [param_name] NVARCHAR(50), 
    [param_description] NVARCHAR(500) , 
    [default_value] NVARCHAR(100) , 
	[sprint] VARCHAR(24),
	[data_type] VARCHAR (24),
    [editable] INT,
    [module] NVARCHAR (50),
    [lang_string_id] NVARCHAR (50),
    [multiple_flag] BINARY (1),
    [is_visible] BINARY (1),
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_parameter_definitions''
)'

exec sp_executesql @script

IF OBJECT_ID('tco_fonts_definition_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_fonts_definition_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[tco_fonts_definition_global]
(	
	   [pk_id] INT ,
       [font_id] INT,
	   [font_name] NVARCHAR(100) ,
	   [font_url] NVARCHAR(500)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''tco_fonts_definition''
)'

exec sp_executesql @script

IF OBJECT_ID('tco_insertable_elements_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_insertable_elements_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[tco_insertable_elements_global]
(
	[key] [int] ,
	[value] [varchar](50),
	[type] [nchar](10)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''tco_insertable_elements''
)'

exec sp_executesql @script

IF OBJECT_ID('tco_publish_chapter_info_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_publish_chapter_info_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[tco_publish_chapter_info_global]
(
	[pk_Id] INT,
	chapter_type nvarchar(1000)
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''tco_publish_chapter_info''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_period_list_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_period_list_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_period_list_global]
(
	[budget_year] [int] NULL,
	[period] [int] NULL
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_period_list''
)'

exec sp_executesql @script


IF OBJECT_ID('gmd_kpi_setup_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gmd_kpi_setup_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gmd_kpi_setup_global]
(
	[kpi_id] INT NOT NULL, 
    [description] VARCHAR(255) NOT NULL, 
    [lang_string_id_kpi_name] NVARCHAR(50) NOT NULL, 
    [lang_string_id_denominator] NVARCHAR(50) NOT NULL, 
    [lang_string_id_numerator] NVARCHAR(50) NOT NULL, 
    [lang_string_id_long_description] NVARCHAR(50) NOT NULL,
    [high_target_flag] INT NOT NULL,
	[indicator_kasse]                 NVARCHAR (25) NOT NULL,
    [indicator_konsern]               NVARCHAR (25) NOT NULL,
    [indicator_konsolidert]           NVARCHAR (25) NOT NULL,
    [indicator2_kasse]                NVARCHAR (25) NOT NULL,
    [indicator2_konsern]              NVARCHAR (25) NOT NULL,
    [indicator2_konsolidert]          NVARCHAR (25) NOT NULL,
    [indicator_kasse_fylke]           NVARCHAR (25) NOT NULL,
    [indicator_konsern_fylke]         NVARCHAR (25) NOT NULL,
    [indicator_konsolidert_fylke]     NVARCHAR (25) NOT NULL,
    [indicator2_kasse_fylke]          NVARCHAR (25) NOT NULL,
    [indicator2_konsern_fylke]        NVARCHAR (25) NOT NULL,
    [indicator2_konsolidert_fylke]    NVARCHAR (25) NOT NULL,
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gmd_kpi_setup''
)'

exec sp_executesql @script


IF OBJECT_ID('gco_pub_colors_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_pub_colors_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_pub_colors_global]
(
	[color_name] NVARCHAR(500) NOT NULL,
	[color_code] NVARCHAR(500) NOT NULL
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_pub_colors''
)'

exec sp_executesql @script


IF OBJECT_ID('gco_pub_colour_palette_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_pub_colour_palette_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_pub_colour_palette_global]
(
	[css_variable_name] NVARCHAR(50) NOT NULL,
	[color_name] NVARCHAR(500) NOT NULL,
	[publish_type] NVARCHAR(20) NOT NULL
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_pub_colour_palette''
)'

exec sp_executesql @script


IF OBJECT_ID('tco_api_auth_activities_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_api_auth_activities_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[tco_api_auth_activities_global]
(
	[pk_id] INT NOT NULL, 
    [method_name] NVARCHAR(100) NOT NULL,
	[controller_name] NVARCHAR(100),
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''tco_api_auth_activities''
)'

exec sp_executesql @script


IF OBJECT_ID('gco_user_widgets_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_user_widgets_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_user_widgets_global]
(
	[Id] INT NOT NULL, 
	[fk_group_id] INT NOT NULL,
    [widget_title] NVARCHAR(100) NOT NULL, 
    [widget_Description]  NVARCHAR(1000) NOT NULL, 
    [widget_thumbnail_url]  NVARCHAR(1000) NOT NULL, 
	[allow_multiple] bit NOT NULL,
	[default_width] INT NOT NULL,
	[default_height] INT NOT NULL,
	[updated_date] datetime NOT NULL,
	[updated_by] int NULL,
   	[min_width] INT NOT NULL,
	[min_height] INT NOT NULL,
	[sort_order] INT NOT NULL
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_user_widgets''
)'

exec sp_executesql @script

IF OBJECT_ID('gco_user_widget_group_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_user_widget_group_global
end

set @script =
'CREATE EXTERNAL TABLE [dbo].[gco_user_widget_group_global]
(
	[pk_group_id] INT NOT NULL,
	[group_name] NVARCHAR(500) NOT NULL,	
	[updated] datetime NOT NULL,
	[updated_by] int NOT NULL
)
WITH 
(
DATA_SOURCE = ' + @datasource + ',
SCHEMA_NAME = ''dbo'',
OBJECT_NAME = ''gco_user_widget_group''
)'

exec sp_executesql @script

print 'Creating global tables - END'

end

begin try
declare @error_check int =  (select count(*) from gco_language_string_overrides_global)
end try
begin catch
print 'ERROR !!! Global tables have not been created successfully. Proc stops..'
RETURN
end catch

if @error_check = 0 or @error_check is null
BEGIN
print 'ERROR !!! Global tables lang string does not contain data. Proc stops..'
RETURN
END

BEGIN

PRINT 'Updating tables - START'


DELETE FROM [dbo].[gal_alert_definition] 
/*Reporting Alerts start*/
INSERT [dbo].[gal_alert_definition] ([pk_alert_id],[name],[description],[recurrence],[subject],[default_email_template],[default_sms_template],[type],[alert_type],[sort_order]) 
SELECT [pk_alert_id],[name],[description],[recurrence],[subject],[default_email_template],[default_sms_template],[type],[alert_type],[sort_order]
FROM gal_alert_definition_global;

PRINT 'UPDATED TABLE [gal_alert_definition]'


DELETE FROM [dbo].[gal_macros_definition]

INSERT [dbo].[gal_macros_definition] ([id],[macros_name],[macros_description]) 
SELECT [id],[macros_name],[macros_description]
FROM gal_macros_definition_global;

PRINT 'UPDATED TABLE [gal_macros_definition]'



DELETE FROM [dbo].[gal_macros_mapping] 

INSERT [dbo].[gal_macros_mapping] ([id],[fk_alert_id]) 
SELECT [id],[fk_alert_id]
FROM gal_macros_mapping_global;

PRINT 'UPDATED TABLE [gal_macros_mapping]'


delete from [gco_color_defaults]

INSERT [dbo].[gco_color_defaults] ([Id], [Colors])
SELECT [Id], [Colors] FROM gco_color_defaults_global;

PRINT 'UPDATED TABLE [gco_color_defaults]'

DELETE FROM [dbo].[gco_column_defs]

INSERT INTO [dbo].[gco_column_defs]([table_id],[column_id],[lang_string_id],[is_fixed],[active], [sort_order], [internal_langstr_id], [is_left_justify], [doc_width], [web_width], [highlight], [is_visible], [is_finplanyear_col] )
SELECT [table_id],[column_id],[lang_string_id],[is_fixed],[active], [sort_order], [internal_langstr_id], [is_left_justify], [doc_width], [web_width], [highlight], [is_visible], [is_finplanyear_col] 
FROM gco_column_defs_global

PRINT 'UPDATED TABLE [gco_column_defs]'

DELETE FROM [dbo].[gco_dashboard_grouping]

INSERT INTO [dbo].[gco_dashboard_grouping](pk_dash_group_id, dash_group_name, dash_group_langstring,icon_path,sort_order, updated, updated_by)
SELECT pk_dash_group_id, dash_group_name, dash_group_langstring,icon_path,sort_order, updated, updated_by
FROM gco_dashboard_grouping_global

PRINT 'UPDATED TABLE [gco_dashboard_grouping]'

DELETE FROM [dbo].[gco_doc_table_defs]

INSERT INTO [dbo].[gco_doc_table_defs]([table_id],[lang_string_id],[doc_type],[amount_format], [is_landscape], [version], [caption],[parameter_json])
SELECT [table_id],[lang_string_id],[doc_type],[amount_format], [is_landscape], [version], [caption],[parameter_json]
FROM gco_doc_table_defs_global

PRINT 'UPDATED TABLE [gco_doc_table_defs]'

DELETE FROM [dbo].[gco_integ_error_types]

INSERT INTO [dbo].[gco_integ_error_types](pk_error_type, error_name, error_description, flag)
SELECT pk_error_type, error_name, error_description, flag
FROM gco_integ_error_types_global

PRINT 'UPDATED TABLE [gco_integ_error_types]'

DELETE FROM [gco_language_string_overrides]

INSERT [dbo].[gco_language_string_overrides] ([ID], [Description], [Language], [context], [tenant_type])
SELECT [ID], [Description], [Language], [context], [tenant_type]
FROM gco_language_string_overrides_global

PRINT 'UPDATED TABLE [gco_language_string_overrides]'

DELETE FROM gco_language_strings

INSERT [dbo].[gco_language_strings] ([ID], [Description], [Language], [context])
SELECT [ID], [Description], [Language], [context]
FROM gco_language_strings_global;

PRINT 'UPDATED TABLE gco_language_strings'

DELETE FROM gco_modules_new

INSERT INTO gco_modules_new (pk_module_id, name)
SELECT pk_module_id, name
FROM gco_modules_new_global;

PRINT 'UPDATED TABLE gco_modules_new'


DELETE FROM [dbo].[gco_org_validations] 

INSERT [dbo].[gco_org_validations] (validation_id, description, lang_string_id)
SELECT validation_id, description, lang_string_id FROM gco_org_validations_global;

PRINT 'UPDATED TABLE [gco_org_validations]'

DELETE FROM [gco_plantype_colors]

INSERT [gco_plantype_colors](pk_color_id,color_name,color_code,is_active_type)
SELECT pk_color_id,color_name,color_code,is_active_type
FROM gco_plantype_colors_global

PRINT 'UPDATED TABLE [gco_plantype_colors]'

DELETE FROM [dbo].[gco_reporting_columns]

INSERT INTO [dbo].[gco_reporting_columns]([column_id],[column_lang_id],[column_data_type],[is_searchable],[page_id],[updated],[updated_by],[type],[isCommaSeperatedData],[offset],alias,[filter_main_data_query],[is_toggle_applicable]) 
SELECT [column_id],[column_lang_id],[column_data_type],[is_searchable],[page_id],[updated],[updated_by],[type],[isCommaSeperatedData],[offset],alias,[filter_main_data_query],[is_toggle_applicable]
FROM gco_reporting_columns_global

PRINT 'UPDATED TABLE [gco_reporting_columns]'

DELETE FROM [gco_table_grouping_def]

INSERT [dbo].[gco_table_grouping_def] ([table_id], [table_name], [level_id], [level_name], [grouping_id], [grouping_name], [updated], [updated_by])
SELECT [table_id], [table_name], [level_id], [level_name], [grouping_id], [grouping_name], [updated], [updated_by]
FROM gco_table_grouping_def_global

PRINT 'UPDATED TABLE [gco_table_grouping_def]'

DELETE FROM [gco_widgets]

INSERT gco_widgets([Id],[widget_title],[widget_Description],[widget_thumbnail_url],[widget_url],[allow_multiple],
[dashboard_view], [default_width], [default_height],
[maximized_view],[config_view],[updated_date],[updated_by] )
SELECT [Id],[widget_title],[widget_Description],[widget_thumbnail_url],[widget_url],[allow_multiple],
[dashboard_view], [default_width], [default_height],
[maximized_view],[config_view],[updated_date],[updated_by]
FROM gco_widgets_global

PRINT 'UPDATED TABLE [gco_widgets]'

delete from [gmd_action_types]

INSERT [dbo].[gmd_action_types] ([pk_action_type], [pk_language], [action_type_descr], [updated_by], [updated])
SELECT [pk_action_type], [pk_language], [action_type_descr], [updated_by], [updated]
FROM gmd_action_types_global

PRINT 'UPDATED TABLE [gmd_action_types]'

DELETE FROM tco_publish_template where tree_type = 'MonthlyReport' and is_default = 1

DELETE FROM [gmd_publish_tree_node_definitions]

INSERT [dbo].[gmd_publish_tree_node_definitions] ([type], [tree_type], [content_type], [is_editable], [title_key], [expanded], [is_checked], [drag]) 
SELECT [type], [tree_type], [content_type], [is_editable], [title_key], [expanded], [is_checked], [drag]
FROM gmd_publish_tree_node_definitions_global

PRINT 'UPDATED TABLE [gmd_publish_tree_node_definitions]'


DELETE FROM [dbo].[gco_parameter_definitions] 

INSERT [dbo].[gco_parameter_definitions] ([param_name], [param_description], [default_value], [sprint],
[data_type],[editable],[module],[lang_string_id],[multiple_flag],[is_visible])
SELECT [param_name], [param_description], [default_value], [sprint],
[data_type],[editable],[module],[lang_string_id],[multiple_flag],[is_visible]
FROM gco_parameter_definitions_global

PRINT 'UPDATED TABLE [gco_parameter_definitions]'

delete from tco_fonts_definition

INSERT INTO tco_fonts_definition ([font_id], [font_name], [font_url]) 
select [font_id], [font_name], [font_url] from tco_fonts_definition_global

PRINT 'UPDATED TABLE tco_fonts_definition'


DELETE  FROM [dbo].[tco_insertable_elements]

INSERT [dbo].[tco_insertable_elements] ([key], [value], [type]) 
SELECT [key], [value], [type]
FROM tco_insertable_elements_global

PRINT 'UPDATED TABLE [tco_insertable_elements]'


DELETE FROM [tco_publish_chapter_info]

INSERT [dbo].[tco_publish_chapter_info] ([chapter_type])
SELECT chapter_type from tco_publish_chapter_info_global

PRINT 'UPDATED TABLE [tco_publish_chapter_info]'

DELETE FROM [gco_period_list]

INSERT [dbo].[gco_period_list] ([budget_year],[period])
SELECT budget_year,period from gco_period_list_global

PRINT 'UPDATED TABLE [gco_period_list]'


DELETE FROM [gmd_kpi_setup]

INSERT [dbo].[gmd_kpi_setup] (kpi_id, description,[lang_string_id_kpi_name],[lang_string_id_denominator],[lang_string_id_numerator],[lang_string_id_long_description],[high_target_flag],
indicator_kasse,indicator_konsern,indicator_konsolidert,indicator2_kasse,indicator2_konsern,indicator2_konsolidert,indicator_kasse_fylke,indicator_konsern_fylke,indicator_konsolidert_fylke,indicator2_kasse_fylke,indicator2_konsern_fylke,indicator2_konsolidert_fylke
)
SELECT kpi_id, description,[lang_string_id_kpi_name],[lang_string_id_denominator],[lang_string_id_numerator],[lang_string_id_long_description],[high_target_flag],
indicator_kasse,indicator_konsern,indicator_konsolidert,indicator2_kasse,indicator2_konsern,indicator2_konsolidert,indicator_kasse_fylke,indicator_konsern_fylke,indicator_konsolidert_fylke,indicator2_kasse_fylke,indicator2_konsern_fylke,indicator2_konsolidert_fylke
FROM gmd_kpi_setup_global

PRINT 'UPDATED TABLE [gmd_kpi_setup]'


DELETE FROM [gco_pub_colors]

INSERT [dbo].[gco_pub_colors] (color_name,color_code)
SELECT color_name,color_code
FROM gco_pub_colors_global

PRINT 'UPDATED TABLE [gco_pub_colors]'


DELETE FROM [gco_pub_colour_palette]

INSERT [dbo].[gco_pub_colour_palette] (css_variable_name, color_name, publish_type)
SELECT css_variable_name, color_name, publish_type
FROM gco_pub_colour_palette_global

PRINT 'UPDATED TABLE [gco_pub_colour_palette]'

DELETE FROM tco_api_auth_activities

INSERT [dbo].tco_api_auth_activities ([pk_id], method_name,controller_name)
SELECT [pk_id],[method_name], controller_name
FROM tco_api_auth_activities_global

PRINT 'UPDATED TABLE [gco_pub_colour_palette]'


DELETE FROM gco_user_widgets

INSERT INTO gco_user_widgets (
	[Id], [fk_group_id],[widget_title], [widget_Description],[widget_thumbnail_url], [allow_multiple],[default_width],
	[default_height],[updated_date],[updated_by],[min_width],[min_height], [sort_order])
SELECT [Id], [fk_group_id],[widget_title], [widget_Description],[widget_thumbnail_url], [allow_multiple],[default_width],
	[default_height],[updated_date],[updated_by],[min_width],[min_height], [sort_order]
FROM gco_user_widgets_global

PRINT 'UPDATED TABLE [gco_user_widgets]'

DELETE FROM gco_user_widget_group

INSERT INTO gco_user_widget_group ([pk_group_id],[group_name],[updated],[updated_by])
SELECT [pk_group_id],[group_name],[updated],[updated_by] 
FROM gco_user_widget_group_global

PRINT 'UPDATED TABLE [gco_user_widget_group]'

PRINT 'Updating tables - END'


END


begin
print 'Drop global tables - Start'

IF OBJECT_ID('gal_alert_definition_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gal_alert_definition_global
end


IF OBJECT_ID('gal_macros_definition_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gal_macros_definition_global
end


IF OBJECT_ID('gal_macros_mapping_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gal_macros_mapping_global
end

IF OBJECT_ID('gco_color_defaults_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_color_defaults_global
end

IF OBJECT_ID('gco_column_defs_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_column_defs_global
end

IF OBJECT_ID('gco_dashboard_grouping_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_dashboard_grouping_global
end

IF OBJECT_ID('gco_doc_table_defs_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_doc_table_defs_global
end

IF OBJECT_ID('gco_integ_error_types_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_integ_error_types_global
end

IF OBJECT_ID('gco_language_string_overrides_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_language_string_overrides_global
end

IF OBJECT_ID('gco_language_strings_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_language_strings_global
end

IF OBJECT_ID('gco_modules_new_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_modules_new_global
end

IF OBJECT_ID('gco_org_validations_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_org_validations_global
end

IF OBJECT_ID('gco_plantype_colors_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_plantype_colors_global
end

IF OBJECT_ID('gco_reporting_columns_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_reporting_columns_global
end

IF OBJECT_ID('gco_table_grouping_def_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_table_grouping_def_global
end

IF OBJECT_ID('gco_widgets_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_widgets_global
end

IF OBJECT_ID('gmd_action_types_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gmd_action_types_global
end

IF OBJECT_ID('gmd_publish_tree_node_definitions_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gmd_publish_tree_node_definitions_global
end

IF OBJECT_ID('gco_parameter_definitions_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_parameter_definitions_global
end

IF OBJECT_ID('tco_fonts_definition_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_fonts_definition_global
end

IF OBJECT_ID('tco_insertable_elements_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_insertable_elements_global
end

IF OBJECT_ID('tco_publish_chapter_info_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_publish_chapter_info_global
end

IF OBJECT_ID('gco_period_list_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_period_list_global
end


IF OBJECT_ID('gmd_kpi_setup_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gmd_kpi_setup_global
end


IF OBJECT_ID('gco_pub_colors_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_pub_colors_global
end


IF OBJECT_ID('gco_pub_colour_palette_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_pub_colour_palette_global
end


IF OBJECT_ID('tco_api_auth_activities_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE tco_api_auth_activities_global
end

IF OBJECT_ID('gco_user_widgets_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_user_widgets_global
end

IF OBJECT_ID('gco_user_widget_group_global', 'ET') IS NOT NULL 
begin
drop EXTERNAL TABLE gco_user_widget_group_global
end

print 'Drop global tables - END'
end

RETURN 0
