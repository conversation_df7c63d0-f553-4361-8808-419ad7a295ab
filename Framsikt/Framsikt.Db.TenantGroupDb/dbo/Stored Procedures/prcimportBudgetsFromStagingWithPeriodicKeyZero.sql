CREATE OR ALTER PROCEDURE [dbo].[prcimportBudgetsFromStagingWithPeriodicKeyZero]
	@TenantID int,
	@isOriginal bit,
	@UserID int ,
	@budgetYear int,
	@jobID int,
	@deleteExistingRecords bit,
	@isPeriodicKey bit,
	@startId bigint,
	@endId bigint
AS
BEGIN	

		DECLARE @TenantID_1 int,@isOriginal_1 bit,@UserID_1 int,@budgetYear_1 int,@jobID_1 int,@deleteExistingRecords_1 bit,@isPeriodicKey_1 bit,@startId_1 bigint, @endId_1 bigint
		SET @TenantID_1 = @TenantID
		SET @isOriginal_1 = @isOriginal
		SET @UserID_1 = @UserID
		SET @budgetYear_1 = @budgetYear
		SET @jobID_1 = @jobID
		SET @deleteExistingRecords_1 = @deleteExistingRecords
		SET @isPeriodicKey_1 = @isPeriodicKey
		SET @startId_1 = @startId
		SET @endId_1 = @endId

		Declare @adjustmentCode varchar(50) = (select top 1 adjustment_code from [dbo].[tbu_stage_budget_import] where tenant_id = @TenantID_1 and budget_year = @budgetYear and [user_id] = @UserID)

		DECLARE @defaultinvProgram INT = (select top 1 pk_prog_code from [dbo].[tco_inv_program] where fk_tenant_id =@TenantID_1 and default_flag = 1);
		DECLARE @defaultPeriodicKey INT = (SELECT param_value FROM vw_tco_parameters where param_name='DEFAULT_BUDGET_PER_KEY' and fk_tenant_id=@TenantID_1);

		CREATE TABLE  #tbu_stage_budget_import (
		[rowNo] [bigint] IDENTITY(1,1) NOT NULL, [pk_id] [bigint] ,[user_id] [int] NOT NULL,[tenant_id] [int] NOT NULL,[budget_year] [int] NOT NULL,
		[account_code] [nvarchar](25) NULL DEFAULT (''),[department_code] [nvarchar](25) NULL DEFAULT (''),[function_code] [nvarchar](25) NULL DEFAULT (''),[project_code] [nvarchar](25) NULL DEFAULT (''),	
		[free_dim_1] [nvarchar](25) NULL DEFAULT (''),[free_dim_2] [nvarchar](25) NULL DEFAULT (''),[free_dim_3] [nvarchar](25) NULL DEFAULT (''),[free_dim_4] [nvarchar](25) NULL DEFAULT (''),
		[totalbudgetamount] [decimal](18, 2) NULL DEFAULT ((0)),[comments] [nvarchar](max) NULL DEFAULT (''),[period] [nvarchar](10) NULL DEFAULT (''),[periodicKey] [nvarchar](25) NULL DEFAULT (''),[account_code_error] [bit] NULL DEFAULT ((0)),
		[department_code_error] [bit] NULL DEFAULT ((0)),[function_code_error] [bit] NULL DEFAULT ((0)),[project_code_error] [bit] NULL DEFAULT ((0)),[free_dim_1_error] [bit] NULL DEFAULT ((0)),
		[free_dim_2_error] [bit] NULL DEFAULT ((0)),[free_dim_3_error] [bit] NULL DEFAULT ((0)),[free_dim_4_error] [bit] NULL DEFAULT ((0)),[periodicKey_error] [bit] NULL DEFAULT ((0)),[period_error] [bit] NULL DEFAULT ((0)),		
		[totalbudgetamount_error] [bit] NULL DEFAULT ((0)),[adjustment_code] [nvarchar](25) NULL DEFAULT (''),[alter_code] [nvarchar](25) NULL DEFAULT (''),[adjustment_code_error] [bit] NULL DEFAULT ((0)),
		[alter_code_error] [bit] NULL DEFAULT ((0)),[error_count] [int] NULL DEFAULT ((0)), [fk_investment_id] [int] DEFAULT ((0)),
		[fk_prog_code] NVARCHAR(25) NULL,[isPeriodicKeyZero] [bit] Default(0) NOT NULL CONSTRAINT [PK_tbu_stage_budget_importtemp] PRIMARY KEY ([rowNo])); 
		
		INSERT #tbu_stage_budget_import (pk_id,user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,
		free_dim_4,totalbudgetamount,comments,periodicKey,account_code_error,department_code_error,function_code_error,project_code_error,free_dim_1_error,
		free_dim_2_error,free_dim_3_error,free_dim_4_error,periodicKey_error,totalbudgetamount_error,error_count,period,period_error,adjustment_code,alter_code,
		adjustment_code_error,alter_code_error,fk_investment_id,fk_prog_code,isPeriodicKeyZero)  
		SELECT   pk_id,user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,
		free_dim_4,totalbudgetamount,comments,periodicKey,account_code_error,department_code_error,function_code_error,project_code_error,free_dim_1_error,
		free_dim_2_error,free_dim_3_error,free_dim_4_error,periodicKey_error,totalbudgetamount_error,error_count,period,period_error,adjustment_code,alter_code,
		adjustment_code_error,alter_code_error,fk_investment_id ,NULL,isPeriodicKeyZero
		FROM  tbu_stage_budget_import 
		WHERE  tenant_id= @TenantID_1  AND [user_id]=@UserID_1 AND budget_year= @budgetYear_1 AND pk_id between @startId_1 and @endId_1 AND isPeriodicKeyZero = 1
		order by pk_id 
		

		CREATE  TABLE #tbu_trans_detail(
		[pk_id] [uniqueidentifier] , [bu_trans_id] [uniqueidentifier] NULL, [fk_tenant_id] [int] NULL,[action_type] [int] NULL,[line_order] [int] NULL,[fk_account_code] [nvarchar](25) NULL,
		[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,
		[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[resource_id] [nvarchar](25) NULL,[fk_employment_id] [bigint] NULL,[description] [nvarchar](255) NULL,
		[budget_year] [int] NULL,[period] [int] NULL,[budget_type] [int] NULL,[amount_year_1] [decimal](18, 2) NULL,[amount_year_2] [decimal](18, 2) NULL,[amount_year_3] [decimal](18, 2) NULL,
		[amount_year_4] [decimal](18, 2) NULL,[fk_key_id] [int] NULL,[updated] [datetime] NULL,[updated_by] [int] NULL,[allocation_pct] [decimal](13, 10) NULL,[total_amount] [decimal](18, 2) NULL,
		[tax_flag] [int] NULL,[holiday_flag] [int] NULL,[fk_pension_type] [nvarchar](12) NULL,[fk_adjustment_code] [nvarchar](25) NULL,[fk_alter_code] [nvarchar](25) NULL,[fk_investment_id]  [int] NOT NULL DEFAULT ((0)),
		[fk_prog_code] NVARCHAR(25) NULL CONSTRAINT [PK_tbu_trans_detail_temp] PRIMARY KEY ([pk_id])  ) 

		CREATE  TABLE #tbu_trans_detail_original (
		[pk_id] [uniqueidentifier] , [bu_trans_id] [uniqueidentifier] NULL, [fk_tenant_id] [int] NULL,[action_type] [int] NULL,[line_order] [int] NULL,[fk_account_code] [nvarchar](25) NULL,
		[department_code] [nvarchar](25) NULL,[fk_function_code] [nvarchar](25) NULL,[fk_project_code] [nvarchar](25) NULL,[free_dim_1] [nvarchar](25) NULL,[free_dim_2] [nvarchar](25) NULL,
		[free_dim_3] [nvarchar](25) NULL,[free_dim_4] [nvarchar](25) NULL,[resource_id] [nvarchar](25) NULL,[fk_employment_id] [bigint] NULL,[description] [nvarchar](255) NULL,
		[budget_year] [int] NULL,[period] [int] NULL,[budget_type] [int] NULL,[amount_year_1] [decimal](18, 2) NULL, [fk_key_id] [int] NULL,[updated] [datetime] NULL,[updated_by] [int] NULL,[allocation_pct] [decimal](13, 10) NULL
		,[total_amount] [decimal](18, 2) NULL,[tax_flag] [int] NULL,[holiday_flag] [int] NULL,[fk_pension_type] [nvarchar](12) NULL,[fk_adjustment_code] [nvarchar](25) NULL,[fk_alter_code] [nvarchar](25) NULL ,
		[fk_investment_id]  [int] NOT NULL DEFAULT ((0)), [fk_prog_code] NVARCHAR(25) NULL
		CONSTRAINT [PK_tbu_trans_detail_original_temp] PRIMARY KEY ([pk_id])) 
	 
		DECLARE @id INT =1 , @PK_ID UNIQUEIDENTIFIER, @periodicKey INT , @totalbudgetamount DECIMAL(18,2), @tbucount INT = (SELECT COUNT(1) FROM #tbu_stage_budget_import)

		-- COLLECT ALL INVESTMENTS ACCOUNT--
		select distinct tc.pk_account_code into #validAccountCodes from gmd_reporting_line as rl
		join tco_accounts as tc on rl.fk_kostra_account_code=tc.fk_kostra_account_code
		join #tbu_stage_budget_import as tu on tc.pk_account_code=tu.account_code and tc.pk_tenant_id=tu.tenant_id
		where report='B2A' and pk_tenant_id=@TenantID_1  and  tu.budget_Year=@budgetYear_1

		-- GET ALL PROJECT CODE AND INVESTMENT ID COMBINATIONS FOR THAT GIVEN YEAR--
		select  fk_main_project_code ,ISNULL(pk_investment_id, 0 ) as invID into #investmentsID from tco_investments as i
		join tco_inv_budgetYear_config as ty on i.pk_investment_id=ty.fk_investment_id
		where  i.fk_tenant_id=@TenantID_1 and ty.budget_year=@budgetYear_1 
		and fk_main_project_code in (select fk_main_project_code from tco_projects as p
		join #tbu_stage_budget_import tu on p.pk_project_code=tu.project_code and p.fk_tenant_id=tu.tenant_id
		where p.fk_tenant_id=@TenantID_1 and  tu.budget_Year=@budgetYear_1)

		-- UPDATE INVESTMENT IDS--
		UPDATE t SET t.fk_investment_id=invlist.invID 
		from #tbu_stage_budget_import t
		join #investmentsID  invlist on  t.project_code=invlist.fk_main_project_code
		where account_code in (SELECT pk_account_code from #validAccountCodes) and ( project_code <> '' or  project_code is not null) 

		-- UPDATE PROGRAM CODES FOR INVESTMENT ACCOUNTS
		UPDATE ttd SET ttd.fk_prog_code=tfls.fk_prog_code from #tbu_stage_budget_import ttd
		join tmd_program_code_setup tfls on ttd.tenant_id=tfls.fk_tenant_id
		and ttd.account_code between tfls.fk_account_code_from and tfls.fk_account_code_to
		and ttd.department_code between tfls.fk_department_code_from and tfls.fk_department_code_to
		and ttd.function_code between tfls.fk_function_code_from and tfls.fk_function_code_to
		and ttd.project_code between tfls.fk_project_code_from and tfls.fk_project_code_to
		and ttd.free_dim_1 between tfls.free_dim_1_from and tfls.free_dim_1_to
		and ttd.free_dim_2 between tfls.free_dim_2_from and tfls.free_dim_2_to
		and ttd.free_dim_3 between tfls.free_dim_3_from and tfls.free_dim_3_to
		and ttd.free_dim_4 between tfls.free_dim_4_from and tfls.free_dim_4_to
		where ttd.tenant_id=@TenantID_1 and ttd.account_code in (SELECT pk_account_code from #validAccountCodes) 
		

		update  #tbu_stage_budget_import set fk_investment_id = 0   where fk_investment_id is NULL;  
		update  #tbu_stage_budget_import set fk_prog_code = @defaultinvProgram   where fk_prog_code is NULL;   
		update  #tbu_stage_budget_import SET periodicKey = @defaultPeriodicKey WHERE periodicKey = 0;

		IF(@deleteExistingRecords = 0 AND @isOriginal = 0)
		BEGIN
			select a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4, SUM(a.amount_year_1) as ExistingAmount 
			into #combinationTransdetail from tbu_trans_detail a 
			join 
			(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id
			from #tbu_stage_budget_import where user_id = @UserID_1 and tenant_id = @TenantID_1 and budget_year = @budgetYear_1 and isPeriodicKeyZero = 1) b
			on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
			and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4
			and a.budget_year = b.budget_year and a.fk_tenant_id = b.tenant_id
			group by a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4 

			select a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
			SUM(a.amount_year_1) as ExistingAmount, a.period
			into #combinationTransdetailbyPeriod from tbu_trans_detail a 
			join 
			(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id
			from #tbu_stage_budget_import where user_id = @UserID_1 and tenant_id = @TenantID_1 and budget_year = @budgetYear_1 and isPeriodicKeyZero = 1) b
			on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
			and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4
			and a.budget_year = b.budget_year and a.fk_tenant_id = b.tenant_id
			group by a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,a.period

			UPDATE a SET a.periodicKey = 0  FROM #tbu_stage_budget_import a
			join #combinationTransdetail b ON a.account_code = b.fk_account_code and a.department_code= b.department_code and a.function_code = b.fk_function_code and a.project_code= b.fk_project_code
			and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4
			WHERE b.ExistingAmount != 0  AND  a.user_id = @UserID_1 AND a.tenant_id = @TenantID_1 AND a.budget_year = @budgetYear_1 AND isPeriodicKeyZero = 1;
		END

		IF(@deleteExistingRecords = 0 AND @isOriginal = 1)
		BEGIN
			select a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4, SUM(a.amount_year_1) as ExistingAmount 
			into #combinationTransdetailOriginal from tbu_trans_detail_original a 
			join 
			(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id
			from #tbu_stage_budget_import where user_id = @UserID_1 and tenant_id = @TenantID_1 and budget_year = @budgetYear_1 and isPeriodicKeyZero = 1) b
			on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
			and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4
			and a.budget_year = b.budget_year and a.fk_tenant_id = b.tenant_id
			group by a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4 

			select a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4, SUM(a.amount_year_1) as ExistingAmount 
			, a.period
			into #combinationTransdetailOriginalbyPeriod from tbu_trans_detail_original a 
			join 
			(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id
			from #tbu_stage_budget_import where user_id = @UserID_1 and tenant_id = @TenantID_1 and budget_year = @budgetYear_1 and isPeriodicKeyZero = 1) b
			on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
			and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4
			and a.budget_year = b.budget_year and a.fk_tenant_id = b.tenant_id
			group by a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4 , a.period

			UPDATE a SET a.periodicKey = 0  FROM #tbu_stage_budget_import a
			join #combinationTransdetailOriginal b ON a.account_code = b.fk_account_code and a.department_code= b.department_code and a.function_code = b.fk_function_code and a.project_code= b.fk_project_code
			and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4
			WHERE b.ExistingAmount != 0  AND  a.user_id = @UserID_1 AND a.tenant_id = @TenantID_1 AND a.budget_year = @budgetYear_1 AND isPeriodicKeyZero = 1;
		END

		IF (@isPeriodicKey_1 = 1)
			BEGIN
				CREATE TABLE #tempPeriodicKeys (ID int,Period varchar(10),allocation_pct decimal(9,3) )
				INSERT #tempPeriodicKeys(ID,Period,allocation_pct)
				Select DISTINCT  ID ,Period ,allocation_pct   from 
				(select distinct key_id as ID,CASE WHEN   (LEN(period) = 1) THEN cast (@budgetYear_1 as varchar(4))+'0'+cast (period as varchar(4)) ELSE cast (@budgetYear_1 as varchar(4))+cast (period as varchar(4))  END as Period,allocation_pct from tco_periodic_key   where  fk_tenant_id=0
				UNION ALL
				select distinct key_id, CASE WHEN   (LEN(period) = 1) THEN cast (@budgetYear_1 as varchar(4))+'0'+cast (period as varchar(4))  ELSE cast (@budgetYear_1 as varchar(4))+cast (period as varchar(4))  END as Period,allocation_pct from tco_periodic_key   where  fk_tenant_id=@TenantID_1 ) a

				WHILE (@id <=  @tbucount)
				BEGIN		

					SELECT  @periodicKey =  periodicKey,@totalbudgetamount = totalbudgetamount FROM #tbu_stage_budget_import where rowNo = @id; 
					SET @PK_ID=  NEWID();
					DECLARE @budgetamount DECIMAL(18,2) = 0,@allocationamount DECIMAL(18,2) = 0;
					IF(@periodicKey = @defaultPeriodicKey)
					BEGIN

						CREATE TABLE #PeriodicAllocation(rowNo INT,period INT,allocation_pct decimal(9,3) CONSTRAINT [PK_PeriodicAllocation_temp] PRIMARY KEY ([rowNo]))

						INSERT #PeriodicAllocation(rowNo,period,allocation_pct)
						SELECT  row_number() over  (order by  period) as rowNo,period,allocation_pct FROM #tempPeriodicKeys where ID  = @periodicKey  order by period 
	
						DECLARE @Periodicid int =1 , @PeriodicCount int = (SELECT count(rowNo) FROM #PeriodicAllocation),
						@allocation_pct DECIMAL(9,3)

						WHILE (@Periodicid <= @PeriodicCount)
						BEGIN 

							DECLARE @period varchar(10)
							SELECT @period =period,@allocationamount = CASE WHEN @Periodicid = @PeriodicCount THEN (@totalbudgetamount - @budgetamount)
												   ELSE (@totalbudgetamount * (allocation_pct /100)) END,
											
							@allocation_pct=allocation_pct 
							FROM #PeriodicAllocation where rowNo = @Periodicid ;

							IF(@isOriginal_1 = 0)
								BEGIN
									INSERT #tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
									description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
									total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
									SELECT NEWID(),@PK_ID,@TenantID_1,5,0,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,
									resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1,2),amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
									ROUND(total_amount, 2),tax_flag,holiday_flag,fk_pension_type,adjustment_code,alter_code,fk_investment_id,fk_prog_code from
									(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,
									'' as resource_id,0 as fk_employment_id,@budgetYear_1 as budget_year,@period as period,1 as budget_type,
									Round(@allocationamount,0) as amount_year_1,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,@periodicKey as fk_key_id,GETUTCDATE() as updated,@UserID_1 as updated_by,
									@allocation_pct as allocation_pct,@totalbudgetamount as total_amount,0 as tax_flag
									,0 as holiday_flag,'' as fk_pension_type,adjustment_code,alter_code,fk_investment_id,fk_prog_code FROM #tbu_stage_budget_import where rowNo = @id)a
				 
									SET @budgetamount = @budgetamount + round(@allocationamount,0); 
									SET @Periodicid = @Periodicid + 1 ;

								END
							ELSE
								BEGIN 

									INSERT #tbu_trans_detail_original (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
									description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
									total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code)
									SELECT NEWID(),@PK_ID,@TenantID_1,5,0,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,
									resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1, 2),fk_key_id,updated,updated_by,allocation_pct,
									ROUND(total_amount, 2),tax_flag,holiday_flag,fk_pension_type,adjustment_code,alter_code,fk_investment_id,fk_prog_code from
									(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,
									'' as resource_id,0 as fk_employment_id,@budgetYear_1 as budget_year,@period as period,1 as budget_type,
									@allocationamount as amount_year_1,@periodicKey as fk_key_id,GETUTCDATE() as updated,@UserID_1 as updated_by,
									@allocation_pct as allocation_pct,@totalbudgetamount as total_amount,0 as tax_flag
									,0 as holiday_flag,'' as fk_pension_type,adjustment_code,alter_code,fk_investment_id,fk_prog_code FROM #tbu_stage_budget_import where rowNo = @id)a

									SET @budgetamount = @budgetamount + @allocationamount; 
									SET @Periodicid = @Periodicid + 1 ;
								END	
						

						END 

						DROP TABLE #PeriodicAllocation	

						IF(@isOriginal_1 = 0)
							BEGIN			

								INSERT tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
								total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code, fk_investment_id,fk_prog_code )
								SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1, 2),amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
								ROUND(total_amount,2),tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code  from #tbu_trans_detail
							END
						ELSE
							BEGIN
								INSERT tbu_trans_detail_original (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
								total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )
								SELECT pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
								description,resource_id,fk_employment_id,budget_year,period,budget_type,ROUND(amount_year_1, 2),fk_key_id,updated,updated_by,allocation_pct,
								ROUND(total_amount, 2),tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code ,fk_investment_id,fk_prog_code  from #tbu_trans_detail_original
							END

					END

					ELSE
					BEGIN
						DECLARE @previousAmount DECIMAL(18,2) = 0,@alloAmountPeriodicKey DECIMAL(9,3) = 0, @index int =1 , @countPKZero int = 0;

						IF(@isOriginal_1 = 0)
								BEGIN	
									
									select  @previousAmount = a.ExistingAmount from #combinationTransdetail a 
									join 
									(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id
									from #tbu_stage_budget_import where rowNo = @id) b
									on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
									and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4 

									select  row_number() over  (order by  a.period) as rowNo,
									a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,a.ExistingAmount
									,a.period,b.comments,b.adjustment_code,b.alter_code,b.fk_investment_id,b.fk_prog_code
									into #tempDatabyPeriod  from #combinationTransdetailbyPeriod a  join  
									(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id,comments,
									adjustment_code,alter_code,fk_investment_id,fk_prog_code
									from #tbu_stage_budget_import where rowNo = @id) b
									on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
									and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4 

									SET @countPKZero = (SELECT count(rowNo) FROM #tempDatabyPeriod)
									WHILE (@index <= @countPKZero)
									BEGIN

										SELECT  @alloAmountPeriodicKey  = CASE WHEN @index = @countPKZero THEN (@totalbudgetamount - @budgetamount)
												ELSE ((ExistingAmount /@previousAmount) * @totalbudgetamount) END FROM #tempDatabyPeriod where rowNo = @index;

										INSERT tbu_trans_detail (pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
										description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,amount_year_2,amount_year_3,amount_year_4,fk_key_id,updated,updated_by,allocation_pct,
										total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code, fk_investment_id,fk_prog_code )
										SELECT NEWID(),@PK_ID,@TenantID_1,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,
										'' as resource_id,0 as fk_employment_id,@budgetYear_1 as budget_year,[period] as period,1 as budget_type,
										CASE WHEN @index = @countPKZero THEN (@totalbudgetamount - @budgetamount)
										ELSE round(@alloAmountPeriodicKey,0) END,0 as amount_year_2,0 as amount_year_3,0 as amount_year_4,@periodicKey as fk_key_id,GETUTCDATE() as updated,@UserID_1 as updated_by,
										0 as allocation_pct,@totalbudgetamount as total_amount,0 as tax_flag
										,0 as holiday_flag,'' as fk_pension_type,adjustment_code,alter_code,fk_investment_id,fk_prog_code FROM #tempDatabyPeriod where rowNo = @index

										SET @budgetamount = @budgetamount + round(@alloAmountPeriodicKey,0);
										SET @index = @index+1;
									END

									DROP TABLE IF EXISTS #tempDatabyPeriod
								END
							ELSE
								BEGIN

									select  @previousAmount = a.ExistingAmount from #combinationTransdetailOriginal a 
									join 
									(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id
									from #tbu_stage_budget_import where rowNo = @id) b
									on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
									and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4 

									select  row_number() over  (order by  a.period) as rowNo,
									a.fk_account_code,a.department_code,a.fk_function_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,a.ExistingAmount
									,a.period,b.comments,b.adjustment_code,b.alter_code,b.fk_investment_id,b.fk_prog_code
									into #tempDatabyPeriodOriginal  from #combinationTransdetailOriginalbyPeriod a  join  
									(SELECT account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4, budget_year, tenant_id,comments,
									adjustment_code,alter_code,fk_investment_id,fk_prog_code
									from #tbu_stage_budget_import where rowNo = @id) b
									on a.fk_account_code = b.account_code and a.department_code= b.department_code and a.fk_function_code = b.function_code and a.fk_project_code= b.project_code
									and a.free_dim_1 = b.free_dim_1 and a.free_dim_2 = b.free_dim_2 and a.free_dim_3 = b.free_dim_3 and a.free_dim_4 = b.free_dim_4 

									SET @countPKZero = (SELECT count(rowNo) FROM #tempDatabyPeriodOriginal)
									WHILE (@index <= @countPKZero)
									BEGIN

										SELECT  @alloAmountPeriodicKey  = CASE WHEN @index = @countPKZero THEN (@totalbudgetamount - @budgetamount)
												ELSE ((ExistingAmount /@previousAmount) * @totalbudgetamount) END FROM #tempDatabyPeriodOriginal where rowNo = @index;

										INSERT tbu_trans_detail_original 
										(pk_id,bu_trans_id,fk_tenant_id,action_type,line_order,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
										description,resource_id,fk_employment_id,budget_year,period,budget_type,amount_year_1,fk_key_id,updated,updated_by,allocation_pct,
										total_amount,tax_flag,holiday_flag,fk_pension_type,fk_adjustment_code,fk_alter_code,fk_investment_id,fk_prog_code )

										SELECT NEWID(),@PK_ID,@TenantID_1,5,0,fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,comments,
										'' as resource_id,0 as fk_employment_id,@budgetYear_1 as budget_year,[period] as period,1 as budget_type,
										CASE WHEN @index = @countPKZero THEN (@totalbudgetamount - @budgetamount)
										ELSE round(@alloAmountPeriodicKey,0) END, @periodicKey as fk_key_id,GETUTCDATE() as updated,@UserID_1 as updated_by,
										0 as allocation_pct,@totalbudgetamount as total_amount,0 as tax_flag
										,0 as holiday_flag,'' as fk_pension_type,adjustment_code,alter_code,fk_investment_id,fk_prog_code FROM #tempDatabyPeriodOriginal where rowNo = @index

										SET @budgetamount = @budgetamount + round(@alloAmountPeriodicKey,0);
										SET @index = @index+1;
									END

									DROP TABLE IF EXISTS #tempDatabyPeriodOriginal

								END

					END
					

					IF(@isOriginal_1 = 0)
						BEGIN
							DELETE from #tbu_trans_detail
						END
					ELSE
						BEGIN
							DELETE from #tbu_trans_detail_original
						END 

					-- UPDATING STEPS COMPLETED AFTER EVERY ITERATION
					UPDATE tco_job_status Set  steps_completed=steps_completed +1  where pk_id= @jobID_1 
	 
					SET  @id = @id+1;
					END; 

					DROP TABLE #tempPeriodicKeys
			END

			DROP TABLE IF EXISTS #tbu_stage_budget_import
			DROP TABLE IF EXISTS #tbu_trans_detail
			DROP TABLE IF EXISTS #tbu_trans_detail_original
			DROP TABLE IF EXISTS #validAccountCodes
			DROP TABLE IF EXISTS #investmentsID
			DROP TABLE IF EXISTS #combinationTransdetail
			DROP TABLE IF EXISTS #combinationTransdetailOriginal
			DROP TABLE IF EXISTS #combinationTransdetailbyPeriod
			DROP TABLE IF EXISTS #combinationTransdetailOriginalbyPeriod

			update tco_user_adjustment_codes set status = 1 where fk_tenant_id = @TenantID and budget_year = @budgetYear and fk_user_id = @UserID and pk_adj_code = @adjustmentCode and org_level = 1
		
END 