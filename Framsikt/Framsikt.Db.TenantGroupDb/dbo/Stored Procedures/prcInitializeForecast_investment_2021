CREATE PROCEDURE [dbo].[prcInitializeForecast_investment_2021]
@forecast_period INT,  @fk_tenant_id INT, @user_id INT, @investment_init_type NVARCHAR(25) = ''

AS

SET NOCOUNT ON;

DECLARE @budget_year INT
DECLARE @last_period INT
DECLARE @ub_period INT
DECLARE @month_remaining dec(18,6) 
DECLARE @month_ytd dec(18,6)
DECLARE @prev_forecast_period INT
DECLARE @service_level INT
DECLARE @periods_ytd INT
DECLARE @periods_remaining INT
DECLARE @org_version VARCHAR(25) 
DECLARE @inv_org_level INT
DECLARE @unapproved_check INT 
DECLARE @vat_account NVARCHAR(25)

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

DECLARE @change_id_table TABLE (fk_change_id INT)

SET @periods_ytd= convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))
SET @periods_remaining = 12 - @periods_ytd;

SET @inv_org_level = 2

IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1) = 1
begin
SET @inv_org_level = (
 SELECT org_level = 
CASE	WHEN param_value = 'org_id_2' THEN 2 
		WHEN param_value = 'org_id_3' THEN 3
		WHEN param_value = 'org_id_4' THEN 4
		WHEN param_value = 'org_id_5' THEN 5 
ELSE 2 END FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1)
end

PRINT 'Start procedure '  + convert(varchar(400),GETDATE());


DECLARE @period_table TABLE (pk_id INT IDENTITY NOT NULL,
	[period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL) 


CREATE TABLE #temp_proj_transactions
(
	   [pk_id] [uniqueidentifier] NOT NULL,
       [trans_id] [uniqueidentifier] NOT NULL,
       [fk_tenant_id] [int] NOT NULL,
       [forecast_period] [int] NOT NULL,
       [fk_account_code] [nvarchar](25) NOT NULL,
       [fk_function_code] [nvarchar](25) NOT NULL,
       [fk_department_code] [nvarchar](25) NOT NULL,
       [fk_main_project_code] [nvarchar](25) NULL,
       [fk_project_code] [nvarchar](25) NOT NULL,
       [free_dim_1] [nvarchar](25) NOT NULL,
       [free_dim_2] [nvarchar](25) NOT NULL,
       [free_dim_3] [nvarchar](25) NOT NULL,
       [free_dim_4] [nvarchar](25) NOT NULL,
       [vat_rate] [decimal](18, 2) NOT NULL,
       [vat_refund] [decimal](18, 2) NOT NULL,
       [year] [int] NOT NULL,
       [amount] [decimal](18, 2) NOT NULL,
       [updated] [datetime] NOT NULL,
       [updated_by] [int] NOT NULL,
       [fk_alter_code] [nvarchar](50) NOT NULL,
       [fk_adjustment_code] [nvarchar](50) NOT NULL,
       [is_vat_row] [bit] NOT NULL DEFAULT ((0)),
       [fk_proj_trans_id] [uniqueidentifier] NULL,
       [description] [nvarchar](max) NULL,
       [is_change] [int] NULL,
	   [inv_status] INT NOT NULL,
	   [fk_change_id] INT NOT NULL,
       [gl_amount] [decimal](18, 2) NOT NULL)




DECLARE @inv_gl_table TABLE
(
	[pk_id] BIGINT NOT NULL IDENTITY,
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [forecast_period] INT NOT NULL, 
	[fk_main_project_code] NVARCHAR(25) NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL,  
	[gl_amount] DECIMAL(18, 2) NOT NULL, 
    [fk_program_code] nvarchar(50) NOT NULL,
	[type] NVARCHAR (2) NOT NULL
)



SET @budget_year = @forecast_period/100
SET @last_period = (@budget_year*100)+12
SET @ub_period = (@budget_year*100)+13
SET @month_remaining = (12 - CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))/12
SET @month_ytd = (CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)


IF @forecast_period = @ub_period AND @org_version is null
BEGIN

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period -1 BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

END

SET @vat_account = (
select MIN(acc_value) from  tmd_acc_defaults b 
WHERE  module = 'INV' and link_type = 'VAT_COST'
AND fk_tenant_id = @fk_tenant_id
AND fk_org_version = @org_version)


SET @unapproved_check = (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = 'MRI_INCL_UNAPPR_BUDCH' AND param_value = 'TRUE' AND active = 1 AND fk_tenant_id = @fk_tenant_id)

IF @unapproved_check = 0 
BEGIN 

INSERT INTO @change_id_table (fk_change_id)
		select pk_change_id from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year < @budget_year
        UNION
        select pk_change_id from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year = @budget_year
        and org_budget_flag = 1
		AND rebudget_approved = 1

END

ELSE
BEGIN 

INSERT INTO @change_id_table (fk_change_id)
		select pk_change_id from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year < @budget_year
        UNION
        select pk_change_id from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year = @budget_year
        and org_budget_flag = 1

END

BEGIN 

INSERT INTO @period_table (period, fk_tenant_id)
VALUES 
((@budget_year*100)+1, @fk_tenant_id),
((@budget_year*100)+2, @fk_tenant_id),
((@budget_year*100)+3, @fk_tenant_id),
((@budget_year*100)+4, @fk_tenant_id),
((@budget_year*100)+5, @fk_tenant_id),
((@budget_year*100)+6, @fk_tenant_id),
((@budget_year*100)+7, @fk_tenant_id),
((@budget_year*100)+8, @fk_tenant_id),
((@budget_year*100)+9, @fk_tenant_id),
((@budget_year*100)+10, @fk_tenant_id),
((@budget_year*100)+11, @fk_tenant_id),
((@budget_year*100)+12, @fk_tenant_id);

DELETE FROM @period_table WHERE period <= @forecast_period

END


PRINT 'Fetch budget amount ' + convert(varchar(400),GETDATE());


INSERT INTO #temp_proj_transactions (pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,
fk_function_code,fk_department_code, fk_main_project_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
vat_rate,vat_refund,year,amount,updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,
fk_proj_trans_id,description,is_change, inv_status, fk_change_id, gl_amount)

SELECT newid() as pk_id, newid() as trans_id, pt.fk_tenant_id,@forecast_period as forecast_period,
pt.fk_account_code,
pt.fk_function_code,pt.fk_department_code, mp.pk_main_project_code as fk_main_project_code,
pt.fk_project_code,pt.free_dim_1,pt.free_dim_2,pt.free_dim_3,pt.free_dim_4,
pt.vat_rate,pt.vat_refund,
pt.year, sum(pt.amount) , getdate() as updated, @user_id as updated_by,'' as fk_alter_code,'' as fk_adjustment_code,0 as is_vat_row,
NULL AS fk_proj_trans_id,'' AS description,0 AS is_change, mp.inv_status, pt.fk_change_id, 0 as gl_amount
FROM tfp_proj_transactions PT
LEFT JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN @change_id_table ch ON pt.fk_change_id = ch.fk_change_id
JOIN tco_accounts ac ON ac.pk_tenant_id = pt.fk_tenant_id AND ac.pk_account_code = pt.fk_account_code AND @budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV'
WHERE PT.fk_tenant_id = @fk_tenant_id AND inv_status NOT IN (3,4,5)
GROUP BY  pt.fk_tenant_id, pt.fk_account_code,
pt.fk_function_code,pt.fk_department_code, mp.pk_main_project_code,
pt.fk_project_code,pt.free_dim_1,pt.free_dim_2,pt.free_dim_3,pt.free_dim_4,
pt.vat_rate,pt.vat_refund,
pt.year, mp.inv_status, pt.fk_change_id



IF @forecast_period  IN (@last_period, @ub_period) AND @investment_init_type in ('', 'TotalBudget')
BEGIN 

PRINT 'Kommer tilbake til denne logikken'

/*
PRINT 'Fetch gl amount ' + convert(varchar(400),GETDATE());

INSERT INTO @inv_gl_table (fk_tenant_id, forecast_period, fk_main_project_code,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,fk_program_code,
gl_amount, type)
SELECT a.fk_tenant_id, @forecast_period, p.fk_main_project_code,a.fk_account_code, a.department_code, a.fk_function_code, ISNULL(a.fk_project_code,''), a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,a.fk_prog_code,
SUM(a.amount), type = case when line_item_id in (1010,1020) then 'i' else 'f' end
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV'
LEFT JOIN tco_projects p ON p.fk_tenant_id = a.fk_tenant_id AND p.pk_project_code = a.fk_project_code AND a.gl_year BETWEEN datepart(year,p.date_from) AND  datepart(year,p.date_to)
WHERE a.fk_tenant_id = @fk_tenant_id AND a.gl_year = @budget_year
GROUP BY a.fk_tenant_id, p.fk_main_project_code,a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, 
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,a.fk_prog_code,line_item_id; 

PRINT 'UPDATE gl table with program code ' + convert(varchar(400),GETDATE());

UPDATE @inv_gl_table SET fk_program_code = b.fk_program_code
FROM @inv_gl_table a
JOIN (SELECT fk_investment_id, MIN(fk_program_code) AS fk_program_code FROM tco_investment_detail WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @forecast_period/100 GROUP BY fk_investment_id) b ON a.fk_investment_id = b.fk_investment_id
WHERE a.forecast_period = @forecast_period AND a.fk_tenant_id = @fk_tenant_id AND a.fk_program_code IS NULL;

UPDATE @inv_gl_table SET fk_program_code = b.fk_program_code
FROM @inv_gl_table a
JOIN (SELECT fk_investment_id, MIN(fk_program_code) AS fk_program_code FROM tco_investment_detail WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @forecast_period/100 GROUP BY fk_investment_id) b ON a.fk_investment_id = b.fk_investment_id
WHERE a.forecast_period = @forecast_period AND a.fk_tenant_id = @fk_tenant_id AND a.fk_program_code = '';


print 'Fetch gl amount into inv_status_detail '  + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET gl_amount = gl.gl_amount
FROM @inv_status_detail id, @inv_gl_table gl
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_investment_id = id.fk_investment_id
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.fk_program_code = id.fk_program_code;


PRINT 'Update vat rates '  + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET vat_rate = tid.vat_rate, vat_refund = tid.vat_refund
FROM @inv_status_detail id, tco_investment_detail tid
WHERE 
tid.fk_tenant_id = id.fk_tenant_id
AND tid.fk_investment_id = id.fk_investment_id
AND tid.fk_account_code   = id.fk_account_code
AND tid.fk_department_code = id.fk_department_code
AND tid.fk_function_code = id.fk_function_code
AND tid.fk_project_code  = id.fk_project_code
AND tid.free_dim_1 = id.free_dim_1
AND tid.free_dim_2 = id.free_dim_2
AND tid.free_dim_3 = id.free_dim_3
AND tid.free_dim_4 = id.free_dim_4
AND tid.fk_program_code = id.fk_program_code
AND id.forecast_period = @forecast_period
AND tid.budget_year = @budget_year;

IF @forecast_period IN (@last_period)
BEGIN

Print 'Insert blank gl-lines in detail table  '  + convert(varchar(400),GETDATE());

INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type)
SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
SUM(gl_amount),0 as vat_refund,0 as  vat_rate, gl.type
FROM @inv_gl_table gl
WHERE NOT EXISTS 
(SELECT * FROM @inv_status_detail id 
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_investment_id = id.fk_investment_id
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.fk_program_code = id.fk_program_code
) 
GROUP BY fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,  fk_program_code, type
;

Print 'Mark transactions that will use budget amount '  + convert(varchar(400),GETDATE());

--UPDATE @inv_status_detail SET use_budget = 1
--FROM @inv_status_detail id, (
--SELECT fk_investment_id, fk_program_code, SUM(year_1_amount) AS year_1_amount, SUM(gl_amount) as gl_amount
--FROM @inv_status_detail
--WHERE fk_program_code = 2
--GROUP BY fk_investment_id, fk_program_code
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE id.fk_investment_id = B.fk_investment_id
--AND id.fk_program_code = B.fk_program_code;


UPDATE @inv_status_detail SET use_budget = 1
FROM @inv_status_detail id, tco_inv_budgetyear_config i
WHERE id.fk_investment_id = i.fk_investment_id
AND id.fk_tenant_id = i.fk_tenant_id
AND i.budget_year = @budget_year
AND i.inv_status = 2;

Print 'Mark transactions that will update year 2 amount '  + convert(varchar(400),GETDATE());


--UPDATE @inv_status_detail SET update_year2 = 1
--FROM @inv_status_detail id, (
--SELECT fk_tenant_id, fk_investment_id, SUM(year_1_amount) year_1_amount, SUM(gl_amount) gl_amount
--FROM @inv_status_detail 
--GROUP BY fk_tenant_id, fk_investment_id
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE use_budget = 0 
--AND id.fk_tenant_id = B.fk_tenant_id
--AND id.fk_investment_id = B.fk_investment_id;

UPDATE @inv_status_detail SET update_year2 = 1
WHERE use_budget = 0 



PRINT 'UPDATE with gl_amount ' + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET year_2_amount = year_2_amount+year_1_amount-gl_amount
WHERE update_year2 = 1 AND use_budget = 0;

UPDATE @inv_status_detail SET year_1_amount = gl_amount WHERE use_budget = 0;

END


IF @forecast_period IN (@ub_period)
BEGIN

UPDATE @inv_status_detail SET gl_amount = 0


PRINT 'Calculate the value to spread '  + convert(varchar(400),GETDATE());

SELECT newid() as pk_id,fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
vat_rate, vat_refund,vat_rate/100*vat_refund/100 as vat_pct,'i' as type,SUM(year_1_amount) as year_1_amount, convert(dec(18,2),0) as total_amount, convert(dec(18,6),0) as pct_value,
convert(dec(18,2),0) as gl_amount, convert(dec(18,2),0) as vat_amount, line_item_id
INTO #distribution_table FROM @inv_status_detail
WHERE is_vat_row = 0 AND fk_account_code != @vat_account
GROUP BY fk_tenant_id, forecast_period,fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_program_code,fk_adjustment_code,fk_alter_code,
vat_rate, vat_refund, line_item_id

UPDATE #distribution_table SET type = 'f' WHERE line_item_id = 2020


UPDATE a SET a.total_amount = S.year_1_amount
FROM #distribution_table a
join 
(SELECT fk_tenant_id, fk_investment_id,line_item_id,SUM(year_1_amount) as year_1_amount
FROM @inv_status_detail
WHERE is_vat_row = 0 AND fk_account_code != @vat_account
GROUP BY fk_tenant_id, fk_investment_id, line_item_id
HAVING sum(year_1_amount) != 0) S
ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.line_item_id = S.line_item_id

UPDATE a SET a.gl_amount = S.gl_amount
FROM #distribution_table a
join 
(SELECT fk_tenant_id, fk_investment_id,type,SUM(gl_amount) as gl_amount
FROM @inv_gl_table 
GROUP BY fk_tenant_id, fk_investment_id, type
HAVING sum(gl_amount) != 0) S
ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.type = S.type


DELETE a
FROM @inv_gl_table a
JOIN 
(SELECT DISTINCT fk_tenant_id, fk_investment_id,type
FROM #distribution_table) S
ON a.fk_tenant_id  = s.fk_tenant_id AND a.fk_investment_id = S.fk_investment_id AND a.type = S.type


UPDATE a SET a.pct_value = a.year_1_amount/a.total_amount
FROM #distribution_table a
WHERE total_amount != 0

-- Set one of the lines to 100% when there is no budget.
UPDATE #distribution_table
SET pct_value = 100
WHERE pk_id IN (
SELECT min(pk_id) FROM #distribution_table 
WHERE total_amount = 0 
GROUP BY total_amount, fk_investment_id)

UPDATE a SET a.gl_amount = round(a.gl_amount * a.pct_value,0)
FROM #distribution_table a

UPDATE a SET a.vat_amount = round(a.gl_amount / (1+a.vat_pct) * vat_pct,0)
FROM #distribution_table a

UPDATE @inv_status_detail SET gl_amount = gl.gl_amount, use_budget = 0
FROM @inv_status_detail id, #distribution_table gl
WHERE 
gl.fk_tenant_id = id.fk_tenant_id
AND gl.forecast_period = id.forecast_period
AND gl.fk_investment_id = id.fk_investment_id
AND gl.fk_account_code   = id.fk_account_code
AND gl.fk_department_code = id.fk_department_code
AND gl.fk_function_code = id.fk_function_code
AND gl.fk_project_code  = id.fk_project_code
AND gl.free_dim_1 = id.free_dim_1
AND gl.free_dim_2 = id.free_dim_2
AND gl.free_dim_3 = id.free_dim_3
AND gl.free_dim_4 = id.free_dim_4
AND gl.fk_program_code = id.fk_program_code
AND gl.fk_adjustment_code = id.fk_adjustment_code
AND gl.fk_alter_code = id.fk_alter_code
AND gl.type = id.type



Print 'Insert vat rows'

--INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
--gl_amount,vat_refund, vat_rate,type, is_vat_row, use_budget)
--SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 
--0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
--vat_amount*-1 as  gl_amount, 0 as vat_refund, 0 as vat_rate,type, 1 as is_vat_row,0 as use_budget
--FROM #distribution_table


--INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
--gl_amount,vat_refund, vat_rate,type, is_vat_row,use_budget)
--SELECT fk_tenant_id, forecast_period, fk_investment_id,@vat_account as fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
--0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 
--0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
--vat_amount as  gl_amount, 0 as vat_refund, 0 as vat_rate,type, 1 as is_vat_row, 0 as use_budget
--FROM #distribution_table


Print 'Insert  gl-lines without budget match in detail table  '  + convert(varchar(400),GETDATE());

INSERT INTO @inv_status_detail (fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, fk_alter_code, fk_adjustment_code, fk_program_code,
year_0_amount,year_1_amount, year_2_amount, year_3_amount, year_4_amount, year_5_amount, year_6_amount, year_7_amount, year_8_amount, year_9_amount, year_10_amount,
gl_amount,vat_refund, vat_rate,type, use_budget)
SELECT fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4, '' as fk_alter_code, '' as fk_adjustment_code, fk_program_code,
0 as year_0_amount,0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount, 0 as year_5_amount, 0 as year_6_amount, 0 as year_7_amount, 0 as year_8_amount, 0 as year_9_amount, 0 as year_10_amount,
SUM(gl_amount),0 as vat_refund,0 as  vat_rate, gl.type,0 as use_budget
FROM @inv_gl_table gl
GROUP BY fk_tenant_id, forecast_period, fk_investment_id,fk_account_code, fk_department_code, fk_function_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,  fk_program_code, type
;


Print 'Mark transactions that will use budget amount '  + convert(varchar(400),GETDATE());

--UPDATE @inv_status_detail SET use_budget = 1
--FROM @inv_status_detail id, (
--SELECT fk_investment_id, fk_program_code, SUM(year_1_amount) AS year_1_amount, SUM(gl_amount) as gl_amount
--FROM @inv_status_detail
--WHERE fk_program_code = 2
--GROUP BY fk_investment_id, fk_program_code
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE id.fk_investment_id = B.fk_investment_id
--AND id.fk_program_code = B.fk_program_code;


UPDATE @inv_status_detail SET use_budget = 1
FROM @inv_status_detail id, tco_inv_budgetyear_config i
WHERE id.fk_investment_id = i.fk_investment_id
AND id.fk_tenant_id = i.fk_tenant_id
AND i.budget_year = @budget_year
AND i.inv_status = 2;

Print 'Mark transactions that will update year 2 amount '  + convert(varchar(400),GETDATE());


--UPDATE @inv_status_detail SET update_year2 = 1
--FROM @inv_status_detail id, (
--SELECT fk_tenant_id, fk_investment_id, SUM(year_1_amount) year_1_amount, SUM(gl_amount) gl_amount
--FROM @inv_status_detail 
--GROUP BY fk_tenant_id, fk_investment_id
--HAVING SUM(year_1_amount) > SUM(gl_amount)) B
--WHERE use_budget = 0 
--AND id.fk_tenant_id = B.fk_tenant_id
--AND id.fk_investment_id = B.fk_investment_id;

UPDATE @inv_status_detail SET update_year2 = 1
WHERE use_budget = 0 



PRINT 'UPDATE with gl_amount ' + convert(varchar(400),GETDATE());

UPDATE @inv_status_detail SET year_2_amount = year_2_amount+year_1_amount-gl_amount
WHERE update_year2 = 1 AND use_budget = 0;

UPDATE @inv_status_detail SET year_1_amount = gl_amount WHERE use_budget = 0;

DELETE FROM @inv_status_detail 
WHERE  year_0_amount = 0
AND year_1_amount = 0 
AND year_2_amount = 0
AND year_3_amount = 0
AND year_4_amount = 0
AND year_5_amount = 0
AND year_6_amount = 0 
AND year_7_amount = 0 
AND year_8_amount = 0
AND year_9_amount = 0
AND year_10_amount = 0

END

PRINT 'Data ready for insert for option TotalBudget'
*/
END

IF @investment_init_type = 'ActualYtd'
BEGIN
PRINT 'Running ActualYtd'

DELETE FROM #temp_proj_transactions

SELECT fk_tenant_id,period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year,fk_prog_code,
SUM(amount) AS amount
INTO #TEMP_ACC_DATA
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV'
WHERE fk_tenant_id = @fk_tenant_id 
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
HAVING SUM(amount) != 0;


INSERT INTO #temp_proj_transactions (pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,
fk_function_code,fk_department_code, fk_main_project_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,
vat_rate,vat_refund,year,amount,updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,
fk_proj_trans_id,description,is_change, inv_status, fk_change_id, gl_amount)
SELECT newid() as pk_id, newid() as trans_id, a.fk_tenant_id,@forecast_period as forecast_period,a.fk_account_code,
a.fk_function_code, a.department_code, '' as main_project_code,a.fk_project_code,a.free_dim_1,a.free_dim_2,a.free_dim_3,a.free_dim_4,
0 as vat_rate,0 as vat_refund,gl_year as year,a.amount,
getdate() as updated,@user_id as updated_by,'' as fk_alter_code,'' as fk_adjustment_code,0 as is_vat_row,
NULL AS fk_proj_trans_id,'' AS description,0 as is_change, 0 as inv_status, 0 as fk_change_id, amount as gl_amount
FROM #TEMP_ACC_DATA a

END



PRINT 'INSERT INTO tmr_proj_transactions '  + convert(varchar(400),GETDATE());

INSERT INTO tmr_proj_transactions (pk_id, trans_id, fk_tenant_id, forecast_period, fk_account_code, fk_function_code,
fk_department_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3,free_dim_4,vat_rate,vat_refund,year,amount,
updated,updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,fk_proj_trans_id,description,is_change)
SELECT pk_id,trans_id,fk_tenant_id,forecast_period,fk_account_code,fk_function_code,fk_department_code,
fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,year,amount,updated,
updated_by,fk_alter_code,fk_adjustment_code,is_vat_row,fk_proj_trans_id,description,is_change
FROM #temp_proj_transactions;






PRINT 'FINISH: Copy new investments from last budget ' + convert(varchar(400),GETDATE());




go