


	CREATE OR ALTER PROCEDURE [dbo].[prcGenerateInvestmentReportData]
	@fk_tenant_id INT,
	@budget_year INT,
	@user_id INT

	AS


	SELECT *
	INTO #hlptab1
	FROM
	(

		select 
	PT.fk_tenant_id
	,PT.year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,budget_amount = SUM(pt.amount)
	,actual_amount = 0
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year-2 THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,bud_process = CASE WHEN bc.budget_year = @budget_year-2 THEN CONVERT(VARCHAR(4),@budget_year-2)+'ØP' ELSE CONVERT(VARCHAR(4),@budget_year-2)+'IB' END
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year-2 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year-2 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, 1 as org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year < @budget_year-2
			UNION
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year-2
			and org_budget_flag = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	LEFT JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code and PT.year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where PT.fk_tenant_id = @fk_tenant_id
	and PT.year = @budget_year-2
	GROUP BY 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,mp.fk_department_code
	,mp.fk_function_code
	,UAD.status
	,UAD.include_in_calculation
	,BC.budget_year

	UNION ALL


	select 
	PT.fk_tenant_id
	,PT.year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,budget_amount = SUM(pt.amount)
	,actual_amount = 0
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year-2 THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,bud_process = CONVERT(VARCHAR(4),@budget_year-2)+'RB'
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year-2 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year-2 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year-2
			and org_budget_flag = 0
			UNION ALL
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year-1
			and org_budget_flag = 0
			and rebudget_approved = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	LEFT JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code and PT.year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where PT.fk_tenant_id = @fk_tenant_id
	and PT.year = @budget_year-2
	GROUP BY 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,mp.fk_department_code
	,mp.fk_function_code
	,UAD.status
	,UAD.include_in_calculation
	,BC.budget_year

	UNION ALL


		select 
	PT.fk_tenant_id
	,PT.year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,budget_amount = SUM(pt.amount)
	,actual_amount = 0
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year-1 THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,bud_process = CASE WHEN bc.budget_year = @budget_year-1 THEN CONVERT(VARCHAR(4),@budget_year-1)+'ØP' ELSE CONVERT(VARCHAR(4),@budget_year-1)+'IB' END
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, 1 as org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year < @budget_year-1
			UNION
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year-1
			and org_budget_flag = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	LEFT JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code and PT.year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where PT.fk_tenant_id = @fk_tenant_id
	and PT.year = @budget_year-1
	GROUP BY 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,mp.fk_department_code
	,mp.fk_function_code
	,UAD.status
	,UAD.include_in_calculation
	,BC.budget_year


	UNION ALL


	select 
	PT.fk_tenant_id
	,PT.year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,budget_amount = SUM(pt.amount)
	,actual_amount = 0
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year-1 THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,bud_process = CONVERT(VARCHAR(4),@budget_year-1)+'RB'
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year-1
			and org_budget_flag = 0
			UNION ALL
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 0
			and rebudget_approved = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	LEFT JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code and PT.year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where PT.fk_tenant_id = @fk_tenant_id
	and PT.year = @budget_year-1
	GROUP BY 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,mp.fk_department_code
	,mp.fk_function_code
	,UAD.status
	,UAD.include_in_calculation
	,BC.budget_year

	UNION ALL



	select 
	PT.fk_tenant_id
	,PT.year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,budget_amount = SUM(pt.amount)
	,actual_amount = 0
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,bud_process = CASE WHEN bc.budget_year = @budget_year THEN CONVERT(VARCHAR(4),@budget_year)+'ØP' ELSE CONVERT(VARCHAR(4),@budget_year)+'IB' END
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, 1 as org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year < @budget_year
			UNION
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 1
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	LEFT JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code and PT.year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where PT.fk_tenant_id = @fk_tenant_id
	and PT.year >= @budget_year
	GROUP BY 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,mp.fk_department_code
	,mp.fk_function_code
	,UAD.status
	,UAD.include_in_calculation
	,BC.budget_year

	UNION ALL


	select 
	PT.fk_tenant_id
	,PT.year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,budget_amount = SUM(pt.amount)
	,actual_amount = 0
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = CASE	WHEN UAD.status = 1 THEN 'Godkjent'
									WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 'Godkjent'
									ELSE 'Åpen'
									END
	,bud_process = CONVERT(VARCHAR(4),@budget_year)+'RB'
	from tfp_proj_transactions PT
	JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
	JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
			select fk_tenant_id, pk_change_id, org_budget_flag,budget_year from tfp_budget_changes
			where fk_tenant_id = @fk_tenant_id
			and budget_year = @budget_year
			and org_budget_flag = 0
		)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
	LEFT JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code and PT.year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where PT.fk_tenant_id = @fk_tenant_id
	and PT.year >= @budget_year
	GROUP BY 
	PT.fk_tenant_id
	,PT.year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,pt.fk_account_code
	,pt.fk_department_code
	,pt.fk_function_code
	,pt.fk_project_code
	,p.project_name
	,pt.free_dim_1
	,pt.free_dim_2
	,pt.free_dim_3
	,pt.free_dim_4
	,pt.fk_change_id
	,mp.fk_department_code
	,mp.fk_function_code
	,UAD.status
	,UAD.include_in_calculation
	,BC.budget_year

		UNION ALL


	select 
	AD.fk_tenant_id
	,AD.gl_year
	,pk_main_project_code = ISNULL(mp.pk_main_project_code,'')
	,main_project_name = ISNULL(mp.main_project_name,'')
	,inv_status = ISNULL(mp.inv_status,'-1')
	,AD.fk_account_code
	,AD.department_code
	,AD.fk_function_code
	,AD.fk_project_code
	,p.project_name
	,AD.free_dim_1
	,AD.free_dim_2
	,AD.free_dim_3
	,AD.free_dim_4
	,fk_change_id = -1
	,budget_amount = 0
	,actual_amount = SUM(AD.amount)
	,owning_dept = ISNULL(mp.fk_department_code,'')
	,owning_function = ISNULL(mp.fk_function_code,'')
	,adjustment_code_status = 'Godkjent'
	,bud_process = ''
	from tfp_accounting_data AD
	JOIN tco_projects P on AD.fk_tenant_id = P.fk_tenant_id and AD.fk_project_code = P.pk_project_code and AD.gl_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
	LEFT JOIN tco_main_projects MP ON AD.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and AD.gl_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
	LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
	LEFT JOIN tco_accounts ac ON AD.fk_tenant_id = ac.pk_tenant_id AND AD.fk_account_code = ac.pk_account_code and AD.gl_year BETWEEN DATEPART(YEAR, ac.datefrom) AND DATEPART (YEAR, ac.dateto)
	JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code and ka.type = 'investment'
	where AD.fk_tenant_id = @fk_tenant_id
	and AD.gl_year IN (@budget_year-4, @budget_year-3, @budget_year-2, @budget_year-1, @budget_year)
	GROUP BY 
	AD.fk_tenant_id
	,AD.gl_year
	,mp.pk_main_project_code
	,mp.main_project_name
	,mp.inv_status
	,AD.fk_account_code
	,AD.department_code
	,AD.fk_function_code
	,AD.fk_project_code
	,p.project_name
	,AD.free_dim_1
	,AD.free_dim_2
	,AD.free_dim_3
	,AD.free_dim_4
	,mp.fk_department_code
	,mp.fk_function_code
	) TRANS


	SELECT 
	fk_tenant_id
	,@budget_year AS budget_year
	,pk_main_project_code AS fk_main_project_code
	,main_project_name
	,inv_status
	,fk_account_code
	,fk_department_code
	,fk_function_code
	,fk_project_code
	,project_name
	,free_dim_1
	,free_dim_2
	,free_dim_3
	,free_dim_4
	,fk_change_id
	,year_minus_2_amount = CASE WHEN year = @budget_year - 2 THEN budget_amount ELSE 0 END
	,year_minus_1_amount = CASE WHEN year = @budget_year - 1 THEN budget_amount ELSE 0 END
	,year_0_amount = CASE WHEN year = @budget_year THEN budget_amount ELSE 0 END
	,year_1_amount = CASE WHEN year = @budget_year + 1 THEN budget_amount ELSE 0 END
	,year_2_amount = CASE WHEN year = @budget_year + 2 THEN budget_amount ELSE 0 END
	,year_3_amount = CASE WHEN year = @budget_year + 3 THEN budget_amount ELSE 0 END
	,year_4_amount = CASE WHEN year = @budget_year + 4 THEN budget_amount ELSE 0 END
	,year_5_amount = CASE WHEN year = @budget_year + 5 THEN budget_amount ELSE 0 END
	,year_6_amount = CASE WHEN year = @budget_year + 6 THEN budget_amount ELSE 0 END
	,year_7_amount = CASE WHEN year = @budget_year + 7 THEN budget_amount ELSE 0 END
	,year_8_amount = CASE WHEN year = @budget_year + 8 THEN budget_amount ELSE 0 END
	,year_9_amount = CASE WHEN year = @budget_year + 9 THEN budget_amount ELSE 0 END
	,year_10_amount = CASE WHEN year = @budget_year + 10 THEN budget_amount ELSE 0 END
	,year_11_amount = CASE WHEN year = @budget_year + 11 THEN budget_amount ELSE 0 END
	,year_12_amount = CASE WHEN year = @budget_year + 12 THEN budget_amount ELSE 0 END
	,year_13_amount = CASE WHEN year = @budget_year + 13 THEN budget_amount ELSE 0 END
	,year_14_amount = CASE WHEN year = @budget_year + 14 THEN budget_amount ELSE 0 END
	,year_15_amount = CASE WHEN year = @budget_year + 15 THEN budget_amount ELSE 0 END
	,year_16_amount = CASE WHEN year = @budget_year + 16 THEN budget_amount ELSE 0 END
	,year_17_amount = CASE WHEN year = @budget_year + 17 THEN budget_amount ELSE 0 END
	,year_18_amount = CASE WHEN year = @budget_year + 18 THEN budget_amount ELSE 0 END
	,year_19_amount = CASE WHEN year = @budget_year + 19 THEN budget_amount ELSE 0 END
	,actual_amt_year_minus_4 = CASE WHEN year = @budget_year - 4 THEN actual_amount ELSE 0 END
	,actual_amt_year_minus_3 = CASE WHEN year = @budget_year - 3 THEN actual_amount ELSE 0 END
	,actual_amt_year_minus_2 = CASE WHEN year = @budget_year - 2 THEN actual_amount ELSE 0 END
	,actual_amt_year_minus_1 = CASE WHEN year = @budget_year - 1 THEN actual_amount ELSE 0 END
	,actual_amt_year_0 = CASE WHEN year = @budget_year THEN actual_amount ELSE 0 END
	,owning_dept
	,owning_function
	,adjustment_code_status
	,bud_process
	INTO #hlptab2
	FROM #hlptab1
	where adjustment_code_status = 'Godkjent'

	DELETE FROM twh_investment_report_data WHERE budget_year = @budget_year AND fk_tenant_id = @fk_tenant_id

	INSERT INTO twh_investment_report_data (
    [fk_tenant_id], 
    [budget_year], 
    [fk_main_project_code], 
    [main_project_name], 
    [inv_status], 
    [fk_account_code], 
    [fk_department_code], 
    [fk_function_code], 
    [fk_project_code], 
    [project_name], 
    [free_dim_1], 
    [free_dim_2], 
    [free_dim_3], 
    [free_dim_4], 
    [fk_change_id], 
    [year_minus_2_amount], 
    [year_minus_1_amount], 
    [year_0_amount], 
    [year_1_amount], 
    [year_2_amount],
    [year_3_amount], 
    [year_4_amount], 
    [year_5_amount],
    [year_6_amount], 
    [year_7_amount], 
    [year_8_amount],
    [year_9_amount], 
    [year_10_amount], 
    [year_11_amount],
    [year_12_amount], 
    [year_13_amount], 
    [year_14_amount],
    [year_15_amount], 
    [year_16_amount], 
    [year_17_amount],
    [year_18_amount], 
    [year_19_amount], 
    [actual_amt_year_minus_4], 
    [actual_amt_year_minus_3], 
    [actual_amt_year_minus_2], 
    [actual_amt_year_minus_1], 
    [actual_amt_year_0], 
    [owning_dept], 
    [owning_function], 
    [adjustment_code_status], 
    [bud_process],
	[updated],
	[updated_by])
	SELECT 
	   [fk_tenant_id], 
    [budget_year], 
    [fk_main_project_code], 
    [main_project_name], 
    [inv_status], 
    [fk_account_code], 
    [fk_department_code], 
    [fk_function_code], 
    [fk_project_code], 
    [project_name], 
    [free_dim_1], 
    [free_dim_2], 
    [free_dim_3], 
    [free_dim_4], 
    [fk_change_id], 
    [year_minus_2_amount], 
    [year_minus_1_amount], 
    [year_0_amount], 
    [year_1_amount], 
    [year_2_amount],
    [year_3_amount], 
    [year_4_amount], 
    [year_5_amount],
    [year_6_amount], 
    [year_7_amount], 
    [year_8_amount],
    [year_9_amount], 
    [year_10_amount], 
    [year_11_amount],
    [year_12_amount], 
    [year_13_amount], 
    [year_14_amount],
    [year_15_amount], 
    [year_16_amount], 
    [year_17_amount],
    [year_18_amount], 
    [year_19_amount], 
    [actual_amt_year_minus_4], 
    [actual_amt_year_minus_3], 
    [actual_amt_year_minus_2], 
    [actual_amt_year_minus_1], 
    [actual_amt_year_0], 
    [owning_dept], 
    [owning_function], 
    [adjustment_code_status], 
    [bud_process],
	GETDATE() AS [updated],
	@user_id AS [updated_by]
	FROM #hlptab2


drop table if exists #hlptab1
drop table if exists #hlptab2
