CREATE OR ALTER PROCEDURE [dbo].[prcGenereateSalaryTableBudchange]

 @tenant_id INT,
 @budget_year INT,
 @fk_salary_table_id UNIQUEIDENTIFIER,
 @fk_user_id INT

AS


--DECLARE @tenant_id INT = 4
--DECLARE @budget_year INT = 2023
--DECLARE @fk_salary_table_id uniqueidentifier = 'F5BB5306-2A62-40F6-9467-122FF0896828'
--DECLARE @fk_user_id INT  = 1132


DECLARE @account_aga_sal [nvarchar](25)
DECLARE @account_aga_hol [nvarchar](25)
DECLARE @param_264 NVARCHAR(10) = (SELECT param_value FROM vw_tco_parameters p WHERE  p.fk_tenant_id =  @tenant_id AND p.param_name = 'Staffplan_264_work_days' AND p.active = 1)

SET @account_aga_sal = (
						select MAX(acc_value) from tmd_acc_defaults
						where fk_tenant_id = @tenant_id
						and module = 'BU'
						and link_value = 'AGA_SAL'
						)
SET @account_aga_hol = (
						select MAX(acc_value) from tmd_acc_defaults
						where fk_tenant_id = @tenant_id
						and module = 'BU'
						and link_value = 'AGA_HOL'
						)


--Define holiday types and pct to be used for holiday calculation
DROP TABLE IF EXISTS #holiday_def
CREATE TABLE #holiday_def (holiday_type_id INT, [pct] decimal(18,9))

INSERT INTO #holiday_def (holiday_type_id, [pct])
select holiday_type_id = 0, [pct] = 0
UNION
select holiday_type_id = 5, [pct] = 0.12
UNION
select holiday_type_id = 6, [pct] = 0.143


DECLARE @holiday_account NVARCHAR(50)
SET @holiday_account = (
						select acc_value from tmd_acc_defaults
						where fk_tenant_id = @tenant_id
						and module = 'BU'
						and link_value = 'HOL'
						)



--Define periodic keys to be used based on parameters
DECLARE @default_key INT
SET @default_key =	(	select MAX(param_value) from tco_parameters
						where fk_tenant_id = @tenant_id
						and param_name like 'DEFAULT_STAFF_BUDGET_PER_KEY'
					)

IF  (	select COUNT(*) from tco_periodic_key
		WHERE fk_tenant_id IN (0,@tenant_id)
		and [key_id] = @default_key
	) = 0
BEGIN
SET @default_key = 72
END


DROP TABLE IF EXISTS #periodic_key_def
CREATE TABLE #periodic_key_def ([type] NVARCHAR(25),holiday_type_id INT, [key_id] INT)

INSERT INTO #periodic_key_def ([type], holiday_type_id, [key_id])
SELECT [type] = 'AGA', holiday_type_id = 5, [key_id] = ISNULL(MAX(param_value), @default_key) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_AGA'
UNION
SELECT [type] = 'AGA', holiday_type_id = 6, [key_id] = ISNULL(MAX(param_value), @default_key) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_AGA_6W'
UNION
SELECT [type] = 'HOLIDAY', holiday_type_id = 5, [key_id] = ISNULL(MAX(param_value), @default_key) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_HOLIDAY'
UNION
SELECT [type] = 'HOLIDAY', holiday_type_id = 6, [key_id] = ISNULL(MAX(param_value), @default_key) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_HOLIDAY_6W'
UNION
SELECT [type] = 'PENSION', holiday_type_id = 5, [key_id] = ISNULL(MAX(param_value), 13) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_PENSION'
UNION
SELECT [type] = 'PENSION', holiday_type_id = 6, [key_id] = ISNULL(MAX(param_value), 13) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_PENSION'
UNION
SELECT [type] = 'SALARY', holiday_type_id = 5, [key_id] = ISNULL(MAX(param_value), @default_key) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_SALARY'
UNION
SELECT [type] = 'SALARY', holiday_type_id = 6, [key_id] = ISNULL(MAX(param_value), @default_key) FROM tco_parameters where fk_tenant_id = @tenant_id and param_name = 'BU_ALLOCATION_KEY_SALARY_6W'
UNION
SELECT [type] = 'SALARY', holiday_type_id = 0, [key_id] = 13
UNION
SELECT [type] = 'HOLIDAY', holiday_type_id = 0, [key_id] = 13
UNION
SELECT [type] = 'AGA', holiday_type_id = 0, [key_id] = 13
UNION
SELECT [type] = 'PENSION', holiday_type_id = 0, [key_id] = 13




--Fetch the base amounts to be used in the further calculations
DROP TABLE IF EXISTS #base_amounts
CREATE TABLE #base_amounts(
	[pk_employment_id] [bigint] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	--[bu_trans_id] uniqueidentifier NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[start_period] [int] NOT NULL,
	[end_period] [int] NOT NULL,
	[position_pct] [decimal](18, 3) NOT NULL,
	[pension_type] [nvarchar](25) NOT NULL,
	[pension_flag] BIT NOT NULL,
	[fk_holiday_type_id] [int] NOT NULL,
	[holiday_flag] BIT NOT NULL,
	[fk_tax_rate] [decimal](18, 3) NOT NULL,
	[tax_flag] BIT NOT NULL,
	[salary_new] [decimal](18, 2) NOT NULL,
	)

INSERT INTO #base_amounts
select 
a.pk_employment_id 
,a.fk_tenant_id
,a.budget_year
--,[bu_trans_id] = NULL
,a.fk_account_code	
,a.fk_department_code	
,a.fk_function_code	
,a.fk_project_code	
,a.free_dim_1	
,a.free_dim_2	
,a.free_dim_3	
,a.free_dim_4
,a.start_period
,a.end_period
,a.position_pct
,a.pension_type
,pension_flag = 1
,a.fk_holiday_type_id
,holiday_flag = CASE WHEN a.fk_holiday_type_id = 0 THEN 0 ELSE 1 END
,a.fk_tax_rate
,tax_flag = 1
,salary_new = b.yearly_salary
from tbu_employments a
JOIN gco_salary_table b ON a.salary_step = b.salary_step
where a.fk_tenant_id = @tenant_id
and a.budget_year = @budget_year
and pk_salary_table_id = @fk_salary_table_id
and delete_flag = 0

--Need to calculate for add ons as well since we will compare against full budget in tbu which includes both positions and add on
INSERT INTO #base_amounts
select 
b.pk_employment_id 
,a.fk_tenant_id
,b.budget_year
--,[bu_trans_id] = NULL
,a.fk_account_code	
,a.fk_department_code	
,b.fk_function_code	
,b.fk_project_code	
,b.free_dim_1	
,b.free_dim_2	
,b.free_dim_3	
,b.free_dim_4
,a.start_period
,a.end_period
,position_pct = 100
,b.pension_type
,a.pension_flag
,b.fk_holiday_type_id
,a.holiday_flag
,b.fk_tax_rate
,a.tax_flag
,salary_new = a.amount_year
from tbu_employments_add_on a
JOIN tbu_employments b ON a.fk_tenant_id = b.fk_tenant_id and a.fk_employment_id = b.pk_employment_id and b.delete_flag = 0
where a.fk_tenant_id = @tenant_id
and b.budget_year = @budget_year

--DROP TABLE IF EXISTS #base_amounts_trans_ids
--select fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,pension_type,pension_flag,holiday_flag, bu_trans_id = NEWID()
--INTO #base_amounts_trans_ids
--from #base_amounts
--GROUP BY fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,pension_type,pension_flag,holiday_flag 
--
--
--UPDATE a set bu_trans_id = b.bu_trans_id
--from #base_amounts a
--JOIN #base_amounts_trans_ids b 
--ON	a.fk_account_code		=  b.fk_account_code	
--AND a.fk_department_code	=  b.fk_department_code
--AND a.fk_function_code		=  b.fk_function_code	
--AND a.fk_project_code		=  b.fk_project_code	
--AND a.free_dim_1			=  b.free_dim_1		
--AND a.free_dim_2			=  b.free_dim_2		
--AND a.free_dim_3			=  b.free_dim_3		
--AND a.free_dim_4			=  b.free_dim_4		
--AND a.pension_type			=  b.pension_type		
--AND a.pension_flag			=  b.pension_flag		
--AND a.holiday_flag			=  b.holiday_flag		
--


DROP TABLE IF EXISTS #calc_table

CREATE TABLE #calc_table(
	[pk_employment_id] [bigint] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	--[bu_trans_id] uniqueidentifier NULL,
	--[parent_bu_trans_id] uniqueidentifier NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[fk_tax_rate] [decimal](18, 3) NOT NULL,
	[tax_flag] BIT NOT NULL,
	[fk_account_code_aga] [nvarchar](25) NOT NULL,
	--[holiday_flag] BIT NOT NULL,
	--[pension_flag] BIT NOT NULL,
	--[pension_type] [nvarchar](25) NOT NULL,
	[period] INT NOT NULL,
	[period_amt] [decimal](18, 2) NOT NULL,
	[type] NVARCHAR(20) NOT NULL)

INSERT INTO #calc_table
select 
a.pk_employment_id 
,a.fk_tenant_id
,a.budget_year
--,[bu_trans_id] = a.bu_trans_id
--,[parent_bu_trans_id] = NULL
,a.fk_account_code	
,a.fk_department_code	
,a.fk_function_code	
,a.fk_project_code	
,a.free_dim_1	
,a.free_dim_2	
,a.free_dim_3	
,a.free_dim_4
,a.fk_tax_rate
,tax_flag
,fk_account_code_aga = @account_aga_sal
--,holiday_flag
--,pension_flag
--,pension_type
,[period] = @budget_year*100 + d.[period]
,period_amt = CASE
        WHEN @param_264= 'TRUE' THEN a.salary_new * (1-(a.fk_holiday_type_id*5/264.0)) * (a.position_pct /100) * (d.allocation_pct/100)
        ELSE a.salary_new * (1-(a.fk_holiday_type_id*5/260.0)) * (a.position_pct /100) * (d.allocation_pct/100)
        END


,[type] = 'SALARY'
from #base_amounts a
JOIN #periodic_key_def c ON a.fk_holiday_type_id = c.holiday_type_id and c.type = 'SALARY'
JOIN tco_periodic_key d ON c.[key_id] = d.[key_id] AND d.period BETWEEN a.start_period and a.end_period
WHERE d.fk_tenant_id IN (0,@tenant_id)

INSERT INTO #calc_table
select 
a.pk_employment_id 
,a.fk_tenant_id
,a.budget_year
--,[bu_trans_id] = NULL
--,[parent_bu_trans_id] = a.bu_trans_id
,fk_account_code = CASE WHEN ths.holiday_account IS NOT NULL THEN ths.holiday_account 
						WHEN @holiday_account IS NOT NULL THEN @holiday_account
						ELSE a.fk_account_code END
,a.fk_department_code	
,a.fk_function_code	
,a.fk_project_code	
,a.free_dim_1	
,a.free_dim_2	
,a.free_dim_3	
,a.free_dim_4
,a.fk_tax_rate
,tax_flag = 1
,fk_account_code_aga = @account_aga_hol
--,holiday_flag = 0
--,pension_flag = 0
--,pension_type
,[period] = @budget_year*100 + d.[period]
,period_amt = CASE
        WHEN @param_264= 'TRUE' THEN a.salary_new * (1-(a.fk_holiday_type_id*5/264.0)) * (a.position_pct /100) * e.pct * (d.allocation_pct/100)
        ELSE a.salary_new * (1-(a.fk_holiday_type_id*5/260.0)) * (a.position_pct /100) * e.pct * (d.allocation_pct/100)
        END
		
,[type] = 'HOLIDAY'
from #base_amounts a
JOIN #periodic_key_def c ON a.fk_holiday_type_id = c.holiday_type_id and c.type = 'HOLIDAY'
JOIN tco_periodic_key d ON c.[key_id] = d.[key_id] AND d.period BETWEEN a.start_period and a.end_period
JOIN #holiday_def e ON a.fk_holiday_type_id = e.holiday_type_id
JOIN tco_accounts ta ON a.fk_tenant_id = ta. pk_tenant_id and a.fk_account_code = ta.pk_account_code and a.budget_year BETWEEN datepart(year, dateFrom) AND datepart(year, dateto)
LEFT JOIN tco_holiday_setup ths ON a.fk_tenant_id = ths.fk_tenant_id and ta.fk_kostra_account_code = ths.fk_kostra_account_code
WHERE d.fk_tenant_id IN (0,@tenant_id)
and a.holiday_flag = 1


INSERT INTO #calc_table
select 
a.pk_employment_id 
,a.fk_tenant_id
,a.budget_year
--,[bu_trans_id] = NULL
--,[parent_bu_trans_id] = a.bu_trans_id
,fk_account_code = e.fk_account_code
,a.fk_department_code	
,a.fk_function_code	
,a.fk_project_code	
,a.free_dim_1	
,a.free_dim_2	
,a.free_dim_3	
,a.free_dim_4
,a.fk_tax_rate
,tax_flag = 1
,e.fk_account_code_aga
--,holiday_flag = 0
--,pension_flag = 0
--,pension_type = ''
,[period] = @budget_year*100 + d.[period]
,period_amt = a.salary_new * (a.position_pct /100) * (e.rate/100) * (d.allocation_pct/100)
,[type] = 'PENSION'
from #base_amounts a
JOIN #periodic_key_def c ON a.fk_holiday_type_id = c.holiday_type_id and c.type = 'PENSION'
JOIN tco_periodic_key d ON c.[key_id] = d.[key_id] AND d.period BETWEEN a.start_period and a.end_period
JOIN tmd_pension_type e ON a.fk_tenant_id = e.fk_tenant_id AND a.pension_type = e.pension_type and @budget_year BETWEEN e.year_from AND e.year_to
WHERE d.fk_tenant_id IN (0,@tenant_id)
and a.pension_flag = 1

INSERT INTO #calc_table
select 
a.pk_employment_id 
,a.fk_tenant_id
,a.budget_year
,fk_account_code = fk_account_code_aga
,a.fk_department_code	
,a.fk_function_code	
,a.fk_project_code	
,a.free_dim_1	
,a.free_dim_2	
,a.free_dim_3	
,a.free_dim_4
,a.fk_tax_rate
,tax_flag = 0
,fk_account_code_aga = ''
--,holiday_flag = 0
--,pension_flag = 0
--,pension_type
,[period]
,period_amt = period_amt * fk_tax_rate
,[type] = 'AGA'
from #calc_table a
WHERE a.tax_flag = 1


--Compare against current budget from positions
DROP TABLE IF EXISTS #change_amts

select fk_tenant_id, budget_year, fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,	free_dim_4
,[period]
,change_amt = SUM(calc_amt-budget_amt)
INTO #change_amts
FROM (
		select fk_tenant_id, budget_year, fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,	free_dim_4
		,[period]
		, calc_amt = SUM(period_amt)
		,budget_amt = 0
		from #calc_table
		GROUP BY fk_tenant_id, budget_year, fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		,[period]

		UNION ALL

		select fk_tenant_id, budget_year, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		,[period]
		, calc_amt = 0
		,budget_amt = SUM(amount_year_1)
		from tbu_trans_detail
		where fk_tenant_id = @tenant_id
		and budget_year = @budget_year
		and fk_employment_id != 0
		GROUP BY fk_tenant_id, budget_year, fk_account_code,department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
		,[period]
	) A
GROUP BY fk_tenant_id, budget_year, fk_account_code,fk_department_code,fk_function_code,fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,[period]
HAVING SUM(calc_amt-budget_amt) <> 0


DROP TABLE IF EXISTS #chamge_amts_bu_trans
select fk_account_code,	fk_department_code,	fk_function_code, fk_project_code,	free_dim_1,	free_dim_2,	free_dim_3,	free_dim_4, bu_trans_id = NEWID(), total_amt = SUM(change_amt)
INTO #chamge_amts_bu_trans
from #change_amts
GROUP BY fk_account_code,	fk_department_code,	fk_function_code, fk_project_code,	free_dim_1,	free_dim_2,	free_dim_3,	free_dim_4

--Delete and insert changes
delete from [dbo].[tbu_stage_salary_table_bud_changes]
where fk_tenant_id = @tenant_id
and budget_year = @budget_year

INSERT INTO [dbo].[tbu_stage_salary_table_bud_changes]
           ([pk_id]
           ,[fk_salary_table_id]
           ,[bu_trans_id]
           ,[fk_tenant_id]
           ,[budget_year]
           ,[period]
           ,[budget_type]
           ,[fk_key_id]
           ,[allocation_pct]
           ,[fk_account_code]
           ,[fk_department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[change_amount]
		   ,[annual_effect]
           ,[total_amount]
           ,[tax_flag]
           ,[holiday_flag]
           ,[fk_pension_type]
           ,[description]
           ,[parent_bu_trans_id]
           ,[is_manually_added]
           ,[updated]
           ,[updated_by])
SELECT		[pk_id] = newid()
           ,[fk_salary_table_id] = @fk_salary_table_id
           ,[bu_trans_id] = b.bu_trans_id
           ,a.[fk_tenant_id]
           ,a.[budget_year]
           ,a.[period]
           ,[budget_type] = 50
           ,[fk_key_id] = 0
           ,[allocation_pct] = 0
           ,a.[fk_account_code]
           ,a.[fk_department_code]
           ,a.[fk_function_code]
           ,a.[fk_project_code]
           ,a.[free_dim_1]
           ,a.[free_dim_2]
           ,a.[free_dim_3]
           ,a.[free_dim_4]
           ,[change_amount] = a.change_amt
		   ,[annual_effect] = a.change_amt*8/12
           ,[total_amount] = b.total_amt
           ,[tax_flag] = 0
           ,[holiday_flag] = 0
           ,[fk_pension_type] = ''
           ,[description] = ''
           ,[parent_bu_trans_id] = NULL
           ,[is_manually_added] = 0
           ,[updated] = GETDATE()
           ,[updated_by] =  @fk_user_id
FROM #change_amts a
JOIN #chamge_amts_bu_trans b 
ON	a.fk_account_code		= b.fk_account_code	
AND	a.fk_department_code	= b.fk_department_code
AND	a.fk_function_code		= b.fk_function_code	
AND	a.fk_project_code		= b.fk_project_code	
AND	a.free_dim_1			= b.free_dim_1		
AND	a.free_dim_2			= b.free_dim_2		
AND	a.free_dim_3			= b.free_dim_3		
AND	a.free_dim_4			= b.free_dim_4		

GO
