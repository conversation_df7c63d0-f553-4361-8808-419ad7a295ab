CREATE OR ALTER PROCEDURE procYBInvestmentLockOrginalBudget
@tenant_id INT,
@budget_year INT,
@adjusmntCode VARCHAR(250),
@updatedBy int,
@updated datetime,
@udtDepartmentCode udtDepartmentCodes readonly
AS
BEGIN

--Get distinct account code for batch processing
DECLARE @account_codeTable TABLE(
	account_Code varchar(50) NOT NULL,
	department_code varchar(50) NOT NULL,
	function_code varchar(50) NOT NULL,
	project_code varchar(50) NOT NULL
);

INSERT INTO @account_codeTable
SELECT distinct fk_account_code ,department_code, fk_function_code, fk_project_code 
FROM tbu_trans_detail tbu
join tco_accounts ac on tbu.fk_account_code=ac.pk_account_code and tbu.fk_tenant_id=ac.pk_tenant_id
join gco_kostra_accounts gk on gk.pk_kostra_account_code=ac.fk_kostra_account_code
where gk.type='investment' and budget_year = @budget_year and fk_tenant_id=@tenant_id and department_code in (select department_code from @udtDepartmentCode);



 --<PERSON>reate cursor and delete the records
SET NOCOUNT ON
DECLARE @departmentCodeDel varchar(50)
DECLARE cur_delete CURSOR
STATIC FOR 
select distinct(department_code) from @udtDepartmentCode
OPEN cur_delete
IF @@CURSOR_ROWS > 0
 BEGIN 
 FETCH NEXT FROM cur_delete INTO @departmentCodeDel
 WHILE @@Fetch_status = 0
 BEGIN

	 --Delete existing rows
	 DELETE tbu FROM tbu_trans_detail_original tbu
		join tco_accounts ac on tbu.fk_account_code=ac.pk_account_code and tbu.fk_tenant_id=ac.pk_tenant_id
		join gco_kostra_accounts gk on gk.pk_kostra_account_code=ac.fk_kostra_account_code and gk.type='investment'
	 WHERE fk_tenant_id=@tenant_id and budget_year = @budget_year 
	 and department_code = @departmentCodeDel ;

 FETCH NEXT FROM cur_delete INTO @departmentCodeDel
 END
END
CLOSE cur_delete
DEALLOCATE cur_delete
SET NOCOUNT OFF 


--Create cursor and insert the records
SET NOCOUNT ON
DECLARE @accountCode varchar(50)
DECLARE @departmentCode varchar(50)
DECLARE @functionCode varchar(50)
DECLARE @projectCode varchar(50)
DECLARE cur_insert CURSOR
STATIC FOR 
select account_Code,department_code,function_code,project_code from @account_codeTable
OPEN cur_insert
IF @@CURSOR_ROWS > 0
 BEGIN 
 FETCH NEXT FROM cur_insert INTO @accountCode,@departmentCode,@functionCode,@projectCode
 WHILE @@Fetch_status = 0
 BEGIN
 --Insert new rows
INSERT INTO tbu_trans_detail_original(
								 pk_id,
								 action_type, 
								 allocation_pct,
								 amount_year_1,
								 budget_type,
								 budget_year,
								 bu_trans_id,
								 department_code,
								 [description],
								 fk_account_code,
								 fk_employment_id,
								 fk_function_code,
								 fk_key_id,
								 fk_pension_type,
								 fk_project_code,
								 fk_tenant_id,
								 free_dim_1,
								 free_dim_2,
								 free_dim_3,
								 free_dim_4,
								 holiday_flag,
								 line_order,
								 period,
								 resource_id,
								 tax_flag,
								 total_amount,
								 updated,
								 updated_by,
								 fk_adjustment_code,
								 fk_investment_id,
								 fk_portfolio_code)

						(SELECT	 pk_id,
								 action_type, 
								 allocation_pct,
								 amount_year_1,
								 budget_type,
								 budget_year,
								 bu_trans_id,
								 department_code,
								 [description],
								 fk_account_code,
								 fk_employment_id,
								 fk_function_code,
								 fk_key_id,
								 fk_pension_type,
								 fk_project_code,
								 fk_tenant_id,
								 free_dim_1,
								 free_dim_2,
								 free_dim_3,
								 free_dim_4,
								 holiday_flag,
								 line_order,
								 period,
								 resource_id,
								 tax_flag,
								 total_amount,
								 @updated,
								 @updatedBy,
								 @adjusmntCode ,
								 fk_investment_id,
								 fk_portfolio_code
						 FROM tbu_trans_detail WHERE fk_tenant_id=@tenant_id and budget_year = @budget_year and department_code=@departmentCode and fk_account_code= @accountCode and fk_function_code= @functionCode and fk_project_code= @projectCode)
update tbu_trans_detail set fk_adjustment_code=@adjusmntCode, updated = @updated, updated_by = @updatedBy WHERE fk_tenant_id=@tenant_id and budget_year = @budget_year and department_code=@departmentCode and fk_account_code= @accountCode and fk_function_code= @functionCode and fk_project_code= @projectCode
 FETCH NEXT FROM cur_insert INTO @accountCode,@departmentCode,@functionCode,@projectCode
 END
END
CLOSE cur_insert
DEALLOCATE cur_insert
SET NOCOUNT OFF 

END

