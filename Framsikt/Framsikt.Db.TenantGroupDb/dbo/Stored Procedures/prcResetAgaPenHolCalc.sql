CREATE OR ALTER PROCEDURE [dbo].[prcResetAgaPenHolCalc]
	@fk_tenant_id int,
	@fk_user_id int,
	@budget_year int,
	@updbudgetorforecast int

AS

IF @updbudgetorforecast NOT IN (1,2) 
	BEGIN
	PRINT 'Wrong value for parameter @updbudgetorforecast. Use 1 for budget or 2 for forecast'
	RETURN
	END

IF @updbudgetorforecast = 1

BEGIN
	UPDATE [tco_application_flag] SET flag_status = 1 
	WHERE flag_name = 'SP_SU_HOL_PEN_AGA' AND budget_year = @budget_year AND fk_tenant_id = @fk_tenant_id AND flag_status = 0;

	INSERT INTO [tco_application_flag] (flag_name, flag_key_id, flag_status, fk_tenant_id, budget_year, updated, updated_by)
	SELECT DISTINCT 'SP_SU_HOL_PEN_AGA', S.org_id, 1, S.fk_tenant_id, @budget_year, GETDATE() , @fk_user_id
	FROM
	(SELECT a.fk_tenant_id, org_id = CASE 
		WHEN c.org_level = 1 THEN org_id_1
		WHEN c.org_level = 2 THEN org_id_2	
		WHEN c.org_level = 3 THEN org_id_3	
		WHEN c.org_level = 4 THEN org_id_4
		WHEN c.org_level = 5 THEN org_id_5
		else '' 
		END
	FROM tco_org_hierarchy a
	LEFT JOIN tco_org_version b ON a.fk_tenant_id = b.fk_Tenant_id and (@budget_year)*100+1 between b.period_from and b.period_to
	LEFT JOIN tco_org_level c ON a.fk_tenant_id = c.fk_tenant_id 
	WHERE c.su_flag = 1 
	AND a.fk_tenant_id = @fk_tenant_id) S
	WHERE NOT EXISTS (SELECT * FROM [tco_application_flag] f WHERE S.fk_tenant_id = f.fk_tenant_id AND S.org_id = f.flag_key_id 
	AND flag_name = 'SP_SU_HOL_PEN_AGA' AND budget_year = @budget_year);
END


IF @updbudgetorforecast = 2

BEGIN
	UPDATE [tco_application_flag] SET flag_status = 1 
	WHERE flag_name = 'SF_SU_HOL_PEN_AGA' AND budget_year = @budget_year AND fk_tenant_id = @fk_tenant_id AND flag_status = 0;

	INSERT INTO [tco_application_flag] (flag_name, flag_key_id, flag_status, fk_tenant_id, budget_year, updated, updated_by)
	SELECT DISTINCT 'SF_SU_HOL_PEN_AGA', S.org_id, 1, S.fk_tenant_id, @budget_year, GETDATE() , @fk_user_id
	FROM
	(SELECT a.fk_tenant_id, org_id = CASE 
		WHEN c.org_level = 1 THEN org_id_1
		WHEN c.org_level = 2 THEN org_id_2	
		WHEN c.org_level = 3 THEN org_id_3	
		WHEN c.org_level = 4 THEN org_id_4
		WHEN c.org_level = 5 THEN org_id_5
		else '' 
		END
	FROM tco_org_hierarchy a
	LEFT JOIN tco_org_version b ON a.fk_tenant_id = b.fk_Tenant_id and (@budget_year)*100+1 between b.period_from and b.period_to
	LEFT JOIN tco_org_level c ON a.fk_tenant_id = c.fk_tenant_id 
	WHERE c.su_flag = 1 
	AND b.active = 1
	AND a.fk_tenant_id = @fk_tenant_id) S
	WHERE NOT EXISTS (SELECT * FROM [tco_application_flag] f WHERE S.fk_tenant_id = f.fk_tenant_id AND S.org_id = f.flag_key_id 
	AND flag_name = 'SF_SU_HOL_PEN_AGA' AND budget_year = @budget_year);
END




RETURN 0
