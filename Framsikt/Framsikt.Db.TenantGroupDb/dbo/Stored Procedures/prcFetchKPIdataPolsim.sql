CREATE OR ALTER PROCEDURE [dbo].[prcFetch<PERSON><PERSON>dataPolsim]
  @kpi_id INT, 
  @budget_year INT,
  @module VARCHAR(25),
  @tenant_id INT,
  @user_id INT,
  @proposal_id uniqueidentifier

  AS


DECLARE @indicator_fund NVARCHAR(20)
DECLARE @kpiId INT = @kpi_id
DECLARE @BudgetYear INT = @budget_year
DECLARE @TenantId INT = @tenant_id
DECLARE @last_updated DATETIME
DECLARE @updated DATETIME 
DECLARE @forecasttype VARCHAR(25)
DECLARE @useforecast INT
DECLARE @forecast_period INT
DECLARE @indicator NVARCHAR(25)
DECLARE @indicatorDebt NVARCHAR(25)
DECLARE @indicator_type INT
DECLARE @tenant_type INT 
DECLARE @use_original INT

SET @use_original = 0

IF (select MIN(param_value) from tco_parameters WHERE param_name = 'KPI_USE_ORG_BUDGET_AS_BASE' AND fk_tenant_id = @TenantId AND active = 1) = 'TRUE'
BEGIN
SET @use_original = 1
END

SET @tenant_type = (SELECT tenant_type_id FROM gco_tenants WHERE pk_id = @TenantId)

IF (select MIN(param_value) from tco_parameters WHERE param_name = 'HIDE_KPIS_IN_POLSIM' AND fk_tenant_id = @TenantId AND active = 1) = 'TRUE'
BEGIN
RETURN
END


SET @forecasttype = (select MIN(param_value) from tco_parameters WHERE param_name = 'KPI_POP_FORECAST_TYPE' AND fk_tenant_id = @TenantId AND active = 1)

IF @forecasttype IS NULL
begin
SET @forecasttype = 'MMMM'
end


IF (select MIN(param_value) from tco_parameters WHERE param_name = 'KPI_USE_MR_FORECAST' AND fk_tenant_id = @TenantId AND active = 1) = 'TRUE'
BEGIN
set @useforecast = 1
END
ELSE
BEGIN
SET @useforecast = 0
END

SET @forecast_period = (
SELECT max(forecast_period) FROM tmr_report_setup_status WHERE fk_tenant_id = @TenantId AND forecast_period / 100 = @BudgetYear-1
AND tab_type = 1 AND status in (2,4))


SET @indicator_type = (SELECT MAX(flag_key_id) FROM tco_application_flag WHERE fk_tenant_id = @TenantId AND budget_year = @BudgetYear AND flag_name =  'KPI_Dataset')

IF @indicator_type IS NULL
BEGIN
	SET @indicator_type = 1
END

IF @indicator_type = 1 AND @tenant_type = 1
BEGIN

SET @indicator = (SELECT indicator_kasse FROM gmd_kpi_setup s WHERE s.kpi_id = @kpi_id)
SET @indicatorDebt = (SELECT indicator_kasse FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_kasse FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type = 2 AND @tenant_type = 1
BEGIN

SET @indicator = (SELECT indicator_konsern FROM gmd_kpi_setup s WHERE s.kpi_id = @kpi_id)
SET @indicatorDebt = (SELECT indicator_konsern FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsern FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type IN (3,4) AND @tenant_type = 1
BEGIN

SET @indicator = (SELECT indicator_konsolidert FROM gmd_kpi_setup s WHERE s.kpi_id = @kpi_id)
SET @indicatorDebt = (SELECT indicator_konsolidert FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsolidert FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type = 1 AND @tenant_type = 2
BEGIN

SET @indicator = (SELECT indicator_kasse_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = @kpi_id)
SET @indicatorDebt = (SELECT indicator_kasse_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_kasse_fylke FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type = 2 AND @tenant_type = 2
BEGIN

SET @indicator = (SELECT indicator_konsern_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = @kpi_id)
SET @indicatorDebt = (SELECT indicator_konsern_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsern_fylke FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type IN (3,4) AND @tenant_type = 2
BEGIN

SET @indicator = (SELECT indicator_konsolidert_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = @kpi_id)
SET @indicatorDebt = (SELECT indicator_konsolidert_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsolidert_fylke FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_fund is null 
begin
SET @indicator_fund = '12142_5'
end


SET @last_updated = (
SELECT MAX(updated) FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId 
AND fk_proposal_id = @proposal_id)

IF 1= 2--datediff (minute, @last_updated, getdate()) <= 1
BEGIN 

print 'Fresh data exists'


SELECT year,type,numerator,denominator, kpi_value FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND type = '3-KPI'
AND fk_proposal_id = @proposal_id
ORDER BY year, type

RETURN

END

DECLARE @budchanges TABLE (pk_change_id INT, fk_tenant_id INT, budget_year INT)

IF @module = 'FP'
BEGIN

INSERT INTO @budchanges (pk_change_id, fk_tenant_id, budget_year)
SELECT pk_change_id, fk_tenant_id, budget_year FROM tfp_budget_changes 
WHERE fk_tenant_id = @TenantId AND budget_year = @BudgetYear AND org_budget_flag = 1

END

ELSE

BEGIN

INSERT INTO @budchanges (pk_change_id, fk_tenant_id, budget_year)
SELECT pk_change_id, fk_tenant_id, budget_year  FROM tfp_budget_changes 
WHERE fk_tenant_id = @TenantId AND budget_year = @BudgetYear

END 

-- Common logic for kpi 5 and 6
DECLARE @oper_accounts TABLE (kostra_account NVARCHAR (25))
DECLARE @inv_accounts TABLE (kostra_account NVARCHAR (25))

INSERT INTO @oper_accounts (kostra_account) VALUES ('1510')
INSERT INTO @inv_accounts (kostra_account) VALUES ('0910'),('0510')

-- Common logic for kpi 1 and 8

DECLARE @kostra_accounts TABLE (kostra_account NVARCHAR (25))
DECLARE @kostra_functions TABLE (kostra_function NVARCHAR (25))

INSERT INTO @kostra_accounts (kostra_account) VALUES ('1550'),('1950')
INSERT INTO @kostra_functions (kostra_function) VALUES ('170'),('171')

DECLARE @budget_table TABLE (fk_tenant_id INT, budget_year INT, line_group_id INT, fk_kostra_account_code varchar(25), fk_kostra_function_code varchar(25), amount dec(18,2))

DECLARE @tbu_trans_accounts TABLE (fk_tenant_id INT, budget_year INT,fk_account_code varchar(25), fk_function_code varchar(25),amount_year_1 dec(18,2))


DECLARE @budchanges_budget TABLE (pk_change_id INT)
DECLARE @proj_transactions_budget TABLE (fk_tenant_id INT, year INT, fk_account_code VARCHAR(25), fk_function_code VARCHAR(25), amount dec(18,2))



IF @useforecast = 0 AND @use_original = 0
BEGIN 

INSERT INTO @tbu_trans_accounts (fk_tenant_id, budget_year, fk_account_code, fk_function_code,amount_year_1)
SELECT @TenantId, @BudgetYear-1, pt.fk_account_code, pt.fk_function_code,sum(pt.amount_year_1)
FROM tbu_trans_detail pt
WHERE pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
GROUP BY pt.fk_account_code, pt.fk_function_code

IF @kpiId IN (5,6)
BEGIN

INSERT INTO @budchanges_budget(pk_change_id)
select pk_change_id from tfp_budget_changes
where fk_tenant_id = @TenantId
and budget_year <= @BudgetYear-1

INSERT INTO @proj_transactions_budget (fk_tenant_id,year, fk_account_code, fk_function_code, amount)
SELECT pt.fk_tenant_id, pt.year, pt.fk_account_code, pt.fk_function_code, sum(pt.amount)
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @BudgetYear-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @BudgetYear-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN @budchanges_budget BC ON PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where PT.fk_tenant_id = @TenantId
and (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and year = @BudgetYear-1
GROUP BY pt.fk_tenant_id,pt.year, pt.fk_account_code, pt.fk_function_code

END


END

IF @useforecast = 0 AND @use_original = 1
BEGIN 

INSERT INTO @tbu_trans_accounts (fk_tenant_id, budget_year, fk_account_code, fk_function_code,amount_year_1)
SELECT @TenantId, @BudgetYear-1, pt.fk_account_code, pt.fk_function_code,sum(pt.amount_year_1)
FROM tbu_trans_detail_original pt
WHERE pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
GROUP BY pt.fk_account_code, pt.fk_function_code

IF @kpiId IN (5,6)
BEGIN

INSERT INTO @budchanges_budget(pk_change_id)
select pk_change_id from tfp_budget_changes
where fk_tenant_id = @TenantId
and budget_year <= @BudgetYear-1
AND org_budget_flag = 1


INSERT INTO @proj_transactions_budget (fk_tenant_id,year, fk_account_code, fk_function_code, amount)
SELECT pt.fk_tenant_id, pt.year, pt.fk_account_code, pt.fk_function_code, sum(pt.amount)
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @BudgetYear-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @BudgetYear-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN @budchanges_budget BC ON PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where PT.fk_tenant_id = @TenantId
and (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and year = @BudgetYear-1
GROUP BY pt.fk_tenant_id,pt.year, pt.fk_account_code, pt.fk_function_code


END


END

IF @useforecast = 1
BEGIN 

INSERT INTO @tbu_trans_accounts (fk_tenant_id, budget_year, fk_account_code, fk_function_code,amount_year_1)
SELECT @TenantId, @BudgetYear-1, pt.fk_account_code, pt.fk_function_code,sum(pt.amount_year_1)
FROM tbu_forecast_transactions pt
WHERE pt.fk_tenant_id = @TenantId
and pt.forecast_period = @forecast_period
GROUP BY pt.fk_account_code, pt.fk_function_code

IF @kpiId IN (5,6)
BEGIN

INSERT INTO @budchanges_budget(pk_change_id)
select pk_change_id from tfp_budget_changes
where fk_tenant_id = @TenantId
and budget_year <= @BudgetYear-1

INSERT INTO @proj_transactions_budget (fk_tenant_id,year, fk_account_code, fk_function_code, amount)
SELECT pt.fk_tenant_id, pt.year, pt.fk_account_code, pt.fk_function_code, sum(pt.amount)
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @BudgetYear-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @BudgetYear-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN @budchanges_budget BC ON PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where PT.fk_tenant_id = @TenantId
and (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and year = @BudgetYear-1
GROUP BY pt.fk_tenant_id,pt.year, pt.fk_account_code, pt.fk_function_code

END


END

IF @kpiId = 1
BEGIN 

DECLARE @calc_table_1 TABLE (fk_tenant_id INT,year INT, kostra_value varchar(25), revenue DECIMAL(18,2), result DECIMAL(18,2), account_corr DECIMAL(18,2), function_corr DECIMAL(18,2))

--Netto driftsresultat i prosent av brutto driftsinntekter
--income


INSERT @calc_table_1
select 
fk_tenant_id = @TenantId
,year = @BudgetYear-1
,kostra_value = ''
,revenue = SUM(amount_year_1)
,result = 0
,account_corr = 0
,function_corr = 0
from @tbu_trans_accounts a 
JOIN tco_accounts b on a.fk_Tenant_id = b.pk_Tenant_id and a.fk_Account_Code = b.pk_account_Code AND a.budget_year BETWEEN DATEPART(year,b.dateFrom) AND DATEPART(year,b.dateTo)
JOIN gmd_reporting_line c on b.fk_kostra_Account_code = c.fk_kostra_Account_code and c.report = '54_OVDRIFT'
where a.fk_tenant_id = @TenantId
and a.budget_year = @BudgetYear-1
and line_group_id = '10'

--Net
INSERT @calc_table_1
select 
fk_tenant_id = @TenantId
,year = @BudgetYear-1
,kostra_value = ''
,revenue = 0
,result = SUM(amount_year_1) 
,account_corr = 0
,function_corr = 0
from @tbu_trans_accounts a
JOIN tco_accounts b on a.fk_Tenant_id = b.pk_Tenant_id and a.fk_Account_Code = b.pk_account_Code AND a.budget_year BETWEEN DATEPART(year,b.dateFrom) AND DATEPART(year,b.dateTo)
JOIN gmd_reporting_line c on b.fk_kostra_Account_code = c.fk_kostra_Account_code and c.report = '54_OVDRIFT'
where a.fk_tenant_id = @TenantId
and a.budget_year = @BudgetYear-1
and line_group_id IN ( '10','20','30','40' )


--Revenue finplan
INSERT @calc_table_1
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_value = ''
,revenue = amount
,result = 0
,account_corr = 0
,function_corr = 0
FROM (
Select  a.fk_tenant_id
, SUM (year_1_amount+change_1_amount) year_1_amount
, SUM (year_2_amount+change_2_amount) year_2_amount
, SUM (year_3_amount+change_3_amount) year_3_amount
, SUM (year_4_amount+change_4_amount) year_4_amount
from tps_operations_propopsal a 
join tco_accounts ac on a.account_code = ac.pk_account_code and a.fk_tenant_id = ac.pk_tenant_id AND @BudgetYear BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
join gmd_reporting_line c on  ac.fk_kostra_account_code = c.fk_kostra_account_code and c.report = '54_OVDRIFT'  
where a.fk_proposal_id = @proposal_id and a.status = 1 and a.fk_tenant_id =  @tenantid and a.fk_action_id != -100 
and c.line_group_id  = 10
group by a.fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--Result finplan
INSERT @calc_table_1
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_value = ''
,revenue = 0
,result = amount
,account_corr = 0
,function_corr = 0
FROM (
Select  a.fk_tenant_id
, SUM (year_1_amount+change_1_amount) year_1_amount
, SUM (year_2_amount+change_2_amount) year_2_amount
, SUM (year_3_amount+change_3_amount) year_3_amount
, SUM (year_4_amount+change_4_amount) year_4_amount
from tps_operations_propopsal a 
join tco_accounts ac on a.account_code = ac.pk_account_code and a.fk_tenant_id = ac.pk_tenant_id AND @BudgetYear BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
join gmd_reporting_line c on  ac.fk_kostra_account_code = c.fk_kostra_account_code and c.report = '54_OVDRIFT'  
where a.fk_proposal_id = @proposal_id and a.status = 1 and a.fk_tenant_id =  @tenantid and a.fk_action_id != -100 
and c.line_group_id IN ( '10','20','30','40' )
group by a.fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt

SET @updated = GETDATE()


DELETE FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND fk_proposal_id = @proposal_id

INSERT INTO [tps_kpi_data_polsim] (fk_tenant_id,fk_proposal_id,budget_year,module,kpi_id,year,type, kostra_code, numerator,denominator,kpi_value,updated,updated_by)

select fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, a.year
, type = '3-KPI'
, kostra_code = @indicator
, numerator = 0
,denominator = 0
,kpi_value = indicator_value
, updated = @updated
, updated_by = @user_id
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year between @BudgetYear - 5 AND @BudgetYear-2

UNION ALL

select  
  fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, year
, type = '3-KPI'
, fk_kostra_account_code = ''
, numerator = SUM(result)
,denominator = SUM(revenue)
,kpi_value = CASE WHEN sum(revenue) != 0 THEN ROUND((SUM(result)/SUM(revenue))*100,1) ELSE 0 END
, updated = @updated
, updated_by = @user_id
from @calc_table_1
GROUP BY fk_tenant_id, year


END

IF @kpiId = 5

BEGIN

PRINT 'Logic for kpi 5 - Debt per inhabitant'



DECLARE @calc_table_5 TABLE (fk_tenant_id INT,year INT, fk_kostra_account varchar(25), change_oper DECIMAL(18,2), change_inv DECIMAL(18,2), inhabitants DECIMAL(18,2), kpi_value DECIMAL(18,6))

--Fetch the inhabitants (same as old)
insert @calc_table_5
select
fk_tenant_id = b.pk_id
,year
,fk_kostra_account = ''
,change_oper = 0
,change_inv = 0
,inhabitants = SUM(forecast)
,kpi_value = 0
from gco_pop_forecast a
JOIN gco_tenants b on a.fk_municipality_id = b.municipality_id
where a.forecast_type = @forecasttype
AND a.age_interval != 'Standard barn'
AND year between @BudgetYear-2 and @BudgetYear+3
AND b.pk_id = @TenantId
GROUP BY b.pk_id, year

--Fetch debt value from kostra (same as old)

insert @calc_table_5
SELECT 
fk_tenant_id = b.pk_id
,a.year
,fk_kostra_account = ''
,change_oper = 0
,change_inv = indicator_value * c.inhabitants
,inhabitants = 0
,kpi_value = indicator_value
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
JOIN @calc_table_5 c on b.pk_id = c.fk_tenant_id and c.year = @BudgetYear-1
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year = @BudgetYear-2


--Fetch change amounts for previous year from TBU for kostra account 1510

insert @calc_table_5
SELECT 
pt.fk_tenant_id
,year = PT.budget_year
,fk_kostra_account = ac.fk_kostra_account_code
,change_oper = SUM(PT.amount_year_1) * (-1)
,change_inv = 0
,inhabitants = 0
,kpi_value = 0
from @tbu_trans_accounts PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
AND ac.fk_kostra_account_code IN (select kostra_account from @oper_accounts)
group by pt.fk_tenant_id, PT.budget_year,ac.fk_kostra_account_code


--Fetch change amounts for previous year from new inv model for kostra accounts 0910 and 0520
insert @calc_table_5
select 
@TenantId
,PT.year  
,fk_kostra_account = ac.fk_kostra_account_code
,change_oper = 0
,change_inv = SUM(amount) * (-1)
,inhabitants = 0
,kpi_value = 0
from @proj_transactions_budget PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN @inv_accounts ia ON ac.fk_kostra_account_code = ia.kostra_account 
GROUP BY pt.year,ac.fk_kostra_account_code
HAVING ABS(SUM(amount))>0


--Fetch change amounts for years in the future from new inv model for kostra accounts 0910 and 0520
insert @calc_table_5

select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @budget_year
					WHEN year = 'year_2_amount' THEN @budget_year+1
					WHEN year = 'year_3_amount' THEN @budget_year+2
					WHEN year = 'year_4_amount' THEN @budget_year+3
					END
,fk_kostra_account_code
,change_oper = 0
,change_inv = amount * (-1)
,inhabitants = 0
,kpi_value = 0
from
(select 
fk_tenant_id
,ac.fk_kostra_account_code
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+change_2_amount)year_2_amount
,SUM(year_3_amount+change_3_amount)year_3_amount
,SUM(year_4_amount+change_4_amount)year_4_amount
from tps_fin_inv_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
AND ac.fk_kostra_account_code IN (select kostra_account from @inv_accounts)
group by pt.fk_tenant_id, ac.fk_kostra_account_code
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt



--Fetch change amounts for years in the future from tps_operations_propopsal for KOSTRA account 15010
insert @calc_table_5
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,fk_kostra_account = fk_kostra_account_code
,change_oper = amount * (-1)
,change_inv = 0
,inhabitants = 0
,kpi_value = 0
FROM (
select 
fk_tenant_id
,ac.fk_kostra_account_code
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+change_2_amount)year_2_amount
,SUM(year_3_amount+change_3_amount)year_3_amount
,SUM(year_4_amount+change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
AND ac.fk_kostra_account_code IN (select kostra_account from @oper_accounts)
group by fk_tenant_id, ac.fk_kostra_account_code
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--Final calculations
DECLARE @result_tab_5 AS TABLE (fk_tenant_id INT, year int, fk_kostra_account varchar(25), change_oper DEC(18,2), change_inv DEC(18,2), inhabitants INT, kpi_value FLOAT) 

INSERT INTO @result_tab_5 (fk_tenant_id, year, fk_kostra_account, change_oper, change_inv, inhabitants, kpi_value) 
select fk_tenant_id,year, fk_kostra_account, SUM(change_oper)change_oper, SUM(change_inv)change_inv, 
SUM(inhabitants)inhabitants, sum(kpi_value)
from @calc_table_5
GROUP BY fk_tenant_id,year, fk_kostra_account



DELETE FROM [tps_kpi_data_polsim]
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND fk_proposal_id = @proposal_id

set @updated = getdate()

INSERT INTO [tps_kpi_data_polsim](fk_tenant_id,fk_proposal_id,budget_year,module,kpi_id,year,kostra_code,type,numerator,denominator,kpi_value,updated,updated_by)
select fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, @BudgetYear
, @module
, @kpi_id
,a.year
, kostra_code = @indicator
,type = '3-KPI'
, numerator = 0
,denominator = 0
,kpi_value = indicator_value
, getdate()
, @user_id
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year between @BudgetYear - 5 AND @BudgetYear-2
UNION ALL
select fk_tenant_id
, fk_proposal_id = @proposal_id
,@BudgetYear
, @module
, @kpi_id
,year = CASE when year = @BudgetYear - 2 THEN @BudgetYear -1 else year end
,fk_kostra_account = ''
,type = CASE when year = @BudgetYear - 2 THEN '1-IB' else '3-KPI' end
, numerator = SUM(SUM(change_oper)+SUM(change_inv)) OVER (partition by fk_tenant_id order by year asc)
,denominator = CASE when year = @BudgetYear - 2 THEN 0 ELSE SUM(inhabitants) END
,kpi_value = CASE when year = @BudgetYear - 2 OR SUM(inhabitants)=0 THEN 0 ELSE SUM(SUM(change_oper)+SUM(change_inv)) OVER (partition by fk_tenant_id order by year asc) / SUM(inhabitants) END
, getdate() 
, @user_id
from @result_tab_5
GROUP BY fk_tenant_id, year
UNION ALL 
SELECT @TenantId
,fk_proposal_id = @proposal_id
,@BudgetYear, @module, @kpi_id, year,fk_kostra_account, type = '2-Change'
,numerator = change_inv+change_oper
,denominator = 0
,kpi_value = 0
,@updated
,@user_id
FROM @result_tab_5
where fk_kostra_account != ''

END

IF @kpiId = 6
BEGIN

--Debt pr revenue / inhab

DECLARE @calc_table_6 TABLE (fk_tenant_id INT,year INT, fk_kostra_account varchar(25), change_oper DECIMAL(18,2), change_inv DECIMAL(18,2), inhabitants DECIMAL(18,2), revenue DECIMAL(18,2))

--Fetch the inhabitants (same as old)
insert @calc_table_6
select
fk_tenant_id = b.pk_id
,year
,fk_kostra_account = ''
,change_oper = 0
,change_inv = 0
,inhabitants = SUM(forecast)
,revenue = 0
from gco_pop_forecast a
JOIN gco_tenants b on a.fk_municipality_id = b.municipality_id
where a.forecast_type = @forecasttype
AND a.age_interval != 'Standard barn'
AND year between @BudgetYear-2 and @BudgetYear+3
AND b.pk_id = @TenantId
GROUP BY b.pk_id, year

--Fetch debt value from kostra (same as old)


insert @calc_table_6
SELECT 
fk_tenant_id = b.pk_id
,a.year
,fk_kostra_account = ''
,change_oper = 0
,change_inv = indicator_value * c.inhabitants
,inhabitants = 0
,revenue = 0
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
JOIN @calc_table_6 c on b.pk_id = c.fk_tenant_id and c.year = @BudgetYear-1
where b.pk_id = @TenantId
and fk_indicator_code = @indicatorDebt
and a.year = @BudgetYear-2


--Fetch change amounts for previous year from TBU for kostra account 1510

insert @calc_table_6
SELECT 
pt.fk_tenant_id
,year = PT.budget_year
,fk_kostra_account = ac.fk_kostra_account_code
,change_oper = SUM(PT.amount_year_1) * (-1)
,change_inv = 0
,inhabitants = 0
,revenue = 0
from @tbu_trans_accounts PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
AND ac.fk_kostra_account_code IN (select kostra_account from @oper_accounts)
group by pt.fk_tenant_id, PT.budget_year, ac.fk_kostra_account_code

--Fetch change amounts for previous year from new inv model for kostra accounts 0910 and 0520
insert @calc_table_6
select 
pt.fk_tenant_id
,PT.year
,fk_kostra_account = ac.fk_kostra_account_code  
,change_oper = 0
,change_inv = SUM(amount) * (-1)
,inhabitants = 0
,revenue = 0
from @proj_transactions_budget PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN @inv_accounts ia ON ac.fk_kostra_account_code = ia.kostra_account 
GROUP BY pt.fk_tenant_id,pt.year,ac.fk_kostra_account_code
HAVING ABS(SUM(amount))>0


--Fetch change amounts for years in the future from new inv model for kostra accounts 0910 and 0520
insert @calc_table_6
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @budget_year
					WHEN year = 'year_2_amount' THEN @budget_year+1
					WHEN year = 'year_3_amount' THEN @budget_year+2
					WHEN year = 'year_4_amount' THEN @budget_year+3
					END
,fk_kostra_account_code
,change_oper = 0
,change_inv = amount * (-1)
,inhabitants = 0
,kpi_value = 0
from
(select 
fk_tenant_id
,ac.fk_kostra_account_code
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+change_2_amount)year_2_amount
,SUM(year_3_amount+change_3_amount)year_3_amount
,SUM(year_4_amount+change_4_amount)year_4_amount
from tps_fin_inv_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = '55_OVINV'
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
AND ac.fk_kostra_account_code IN (select kostra_account from @inv_accounts)
group by pt.fk_tenant_id, ac.fk_kostra_account_code
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--Fetch change amounts for years in the future from tfp_Trans for KOSTRA account 15010
insert @calc_table_6
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,fk_kostra_account = fk_kostra_account_code
,change_oper = amount * (-1)
,change_inv = 0
,inhabitants = 0
,kpi_value = 0
FROM (
select 
fk_tenant_id
,ac.fk_kostra_account_code
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+change_2_amount)year_2_amount
,SUM(year_3_amount+change_3_amount)year_3_amount
,SUM(year_4_amount+change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
AND ac.fk_kostra_account_code IN (select kostra_account from @oper_accounts)
group by fk_tenant_id, ac.fk_kostra_account_code
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--Fetch revenue amounts for revenue KPI
insert @calc_table_6
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @budget_year
					WHEN year = 'year_2_amount' THEN @budget_year+1
					WHEN year = 'year_3_amount' THEN @budget_year+2
					WHEN year = 'year_4_amount' THEN @budget_year+3
					END
,fk_kostra_account = ''
,change_oper = 0
,change_inv = 0
,inhabitants = 0
,revenue = amount * (-1)
FROM (
select 
fk_tenant_id
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+ change_2_amount)year_2_amount
,SUM(year_3_amount+ change_3_amount)year_3_amount
,SUM(year_4_amount+ change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='10'
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
group by fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt

IF @useforecast = 0 
BEGIN

insert @calc_table_6
SELECT 
pt.fk_tenant_id
,year = PT.budget_year
,fk_kostra_account = ''
,change_oper = 0
,change_inv = 0
,inhabitants = 0
,revenue = SUM(PT.amount_year_1)*(-1)
from tbu_trans_detail PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='10'
JOIN tco_user_adjustment_codes adj ON pt.fk_tenant_id = adj.fk_tenant_id and pt.fk_adjustment_code = adj.pk_adj_code and adj.status = 1
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
--AND ac.fk_kostra_account_code IN (select kostra_account from @oper_accounts)
group by pt.fk_tenant_id, PT.budget_year

END


IF @useforecast = 1
BEGIN

insert @calc_table_6
SELECT 
pt.fk_tenant_id
,year = PT.budget_year
,fk_kostra_account = ''
,change_oper = 0
,change_inv = 0
,inhabitants = 0
,revenue = SUM(PT.amount_year_1)*(-1)
from tbu_forecast_transactions PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='10'
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
AND pt.forecast_period = @forecast_period
group by pt.fk_tenant_id, PT.budget_year

END


DECLARE @result_tab_6 AS TABLE (fk_tenant_id INT, year int,fk_kostra_account_code VARCHAR(25),change_oper DEC(18,2), change_inv DEC(18,2), inhabitants INT, revenue dec(18,2), kpi_value FLOAT) 
INSERT INTO @result_tab_6 (fk_tenant_id, year,fk_kostra_account_code,change_oper, change_inv, inhabitants, revenue, kpi_value) 
select fk_tenant_id,year,fk_kostra_account,SUM(change_oper)change_oper, SUM(change_inv)change_inv, SUM(inhabitants)inhabitants, SUM(revenue)revenue, kpi_value = 0
from @calc_table_6
GROUP BY fk_tenant_id,year,fk_kostra_account

SET @updated = getdate() 


DELETE FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND fk_proposal_id = @proposal_id

INSERT INTO [tps_kpi_data_polsim] (fk_tenant_id,fk_proposal_id,budget_year,module,kpi_id,year,type, kostra_code, numerator,denominator,kpi_value,updated,updated_by)

select fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, a.year
, type = '3-KPI'
, kostra_code = @indicator
, numerator = 0
,denominator = 0
,kpi_value = indicator_value
, updated = @updated
, updated_by = @user_id
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year between @BudgetYear - 5 AND @BudgetYear-2
UNION ALL
select fk_tenant_id
, fk_proposal_id = @proposal_id
,@BudgetYear
, @module
, @kpi_id
,year = CASE when year = @BudgetYear - 2 THEN @BudgetYear -1 else year end
, type = CASE WHEN year = @BudgetYear -2 THEN '1-IB' ELSE '3-KPI' END
, kostra_code = CASE WHEN year = @BudgetYear -2 THEN @indicatorDebt ELSE '' END
, numerator = SUM(SUM(change_oper)+SUM(change_inv)) OVER (partition by fk_tenant_id order by year asc)
,denominator = SUM(revenue) 
,kpi_value = CASE WHEN SUM(revenue) = 0 THEN 0 
					ELSE (SUM(SUM(change_oper)+SUM(change_inv)) OVER (partition by fk_tenant_id order by year asc) / SUM(revenue)) *100 END
,@updated
,@user_id
from @result_tab_6
WHERE year > @BudgetYear -3
GROUP BY fk_tenant_id, year
UNION ALL
SELECT @TenantId
, fk_proposal_id = @proposal_id
,@BudgetYear, @module, @kpi_id, year, type = '2-CHANGE',fk_kostra_account_code
,numerator = change_inv+change_oper
,denominator = 0
,kpi_value = 0
,@updated
,@user_id
FROM @result_tab_6
where fk_kostra_account_code != ''


END

IF @kpiId = 7
BEGIN 


DECLARE @calc_table_7 TABLE (fk_tenant_id INT,year INT, kostra_code varchar(25), change_amt DECIMAL(18,2),revenue DECIMAL(18,2))

-- Hente inntekt år-1

INSERT INTO @calc_table_7 (fk_tenant_id, year, kostra_code, change_amt, revenue)
SELECT 
pt.fk_tenant_id
,year = PT.budget_year
,fk_kostra_account = ''
,change_amt = 0
,revenue = SUM(PT.amount_year_1)*(-1)
from @tbu_trans_accounts PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='10'
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
group by pt.fk_tenant_id, PT.budget_year


--Hente brutto inntekt økplan. årene

INSERT INTO @calc_table_7 (fk_tenant_id, year, kostra_code, change_amt, revenue)
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_code = ''
,change_amt = 0
,revenue = amount * (-1)
FROM (
select 
fk_tenant_id
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+ change_2_amount)year_2_amount
,SUM(year_3_amount+ change_3_amount)year_3_amount
,SUM(year_4_amount+ change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='10'
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
group by fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt



--Hente kostra + budsjett for Âr -2 for Â finne IB
INSERT INTO @calc_table_7 (fk_tenant_id, year, kostra_code, change_amt, revenue)
 SELECT 
 b.pk_id as fk_tenant_id
 ,year = a.year
 ,kostra_code = ''
 , change_amt = a.indicator_value * 1000 
 , revenue = 0
 from [gko_kostra_data_corp] a
JOIN gco_tenants b on a.fk_region_code = b.municipality_id
where fk_indicator_code = @indicator_fund
AND b.pk_id = @TenantId
and year = @BudgetYear-2



-- budsjett for Âr -2 for Â finne IB (kostra + budsjett år-2 gir IB)
INSERT INTO @calc_table_7 (fk_tenant_id, year, kostra_code, change_amt, revenue)
 SELECT 
fk_tenant_id = a.fk_tenant_id
 ,year = a.budget_year
 ,kostra_code = ac.fk_kostra_account_code
 , change_amt = sum(a.amount_year_1)
 , revenue = 0
 from @tbu_trans_accounts a
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
where a.budget_year = @BudgetYear-1
and ac.fk_kostra_account_code IN ('1540', '1940')
ANd a.fk_tenant_id = @TenantId
group by a.fk_tenant_id, a.budget_year, ac.fk_kostra_account_code


--Hente overf¯ringer til/fra drift i ¯kplan Ârene

INSERT INTO @calc_table_7 (fk_tenant_id, year, kostra_code, change_amt, revenue)
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,fk_kostra_account = fk_kostra_account_code
,change_amt = amount
,revenue = 0
FROM (
select 
fk_tenant_id
,ac.fk_kostra_account_code
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+ change_2_amount)year_2_amount
,SUM(year_3_amount+ change_3_amount)year_3_amount
,SUM(year_4_amount+ change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
where fk_tenant_id = @TenantId
and budget_year = @budget_year
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
AND ac.fk_kostra_account_code IN ('1540','1940')
group by pt.fk_tenant_id,ac.fk_kostra_account_code
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt

SET @updated = getdate() 

DELETE FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND fk_proposal_id = @proposal_id

INSERT INTO [tps_kpi_data_polsim] (fk_tenant_id,fk_proposal_id,budget_year,module,kpi_id,year,type, kostra_code, numerator,denominator,kpi_value,updated,updated_by)

select fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, a.year
, type = '3-KPI'
, kostra_code = @indicator
, numerator = 0
,denominator = 0
,kpi_value = indicator_value
, updated = @updated
, updated_by = @user_id
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year between @BudgetYear - 5 AND @BudgetYear-2

UNION ALL

select fk_tenant_id=@TenantId
, fk_proposal_id = @proposal_id
,@BudgetYear
, @module
, @kpi_id
,year = CASE when year = @BudgetYear - 2 THEN @BudgetYear -1 else year end
, type = CASE WHEN year = @BudgetYear -2 THEN '1-IB' ELSE '3-KPI' END
, fk_kostra_account_code = CASE WHEN year = @BudgetYear -2 THEN @indicator_fund  ELSE '' END
, numerator = SUM(SUM(change_amt)) OVER (partition by fk_tenant_id order by year asc)
,denominator = SUM(revenue) 
,kpi_value = CASE WHEN SUM(revenue) = 0 THEN 0 
					ELSE ROUND((SUM(SUM(change_amt)) OVER (partition by fk_tenant_id order by year asc) / SUM(revenue)) *100,1) END
,@updated
,@user_id
FROM @calc_table_7
group by year, fk_tenant_id

UNION ALL

select 
fk_tenant_id=@TenantId
, fk_proposal_id = @proposal_id
,@BudgetYear
, @module
, @kpi_id
,year = year
, type = '2-CHANGE'
, fk_kostra_account_code = kostra_code
, numerator = SUM(change_amt)
,denominator = 0
,kpi_value = 0
,@updated
,@user_id
FROM @calc_table_7
WHERE kostra_code != ''
group by year, fk_tenant_id,kostra_code


END

IF @kpiId = 8
BEGIN 

DECLARE @calc_table_8 TABLE (fk_tenant_id INT,year INT, kostra_value varchar(25), revenue DECIMAL(18,2), result DECIMAL(18,2), account_corr DECIMAL(18,2), function_corr DECIMAL(18,2))

INSERT INTO @budget_table (fk_tenant_id, budget_year, line_group_id, fk_kostra_account_code, fk_kostra_function_code, amount)
select 
pt.fk_tenant_id
,pt.budget_year
,line_group_id = rl.line_group_id
,fk_kostra_account_code = ac.fk_kostra_account_code
,fk_kostra_function_code = ISNULL (e.fk_kostra_function_code,'')
,amount = SUM(pt.amount_year_1)
from @tbu_trans_accounts PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' 
LEFT JOIN tco_functions e ON PT.fk_tenant_id = e.pk_tenant_id and PT.fk_function_code = e.pk_Function_code AND PT.budget_year BETWEEN DATEPART(year,e.dateFrom) AND DATEPART(year,e.dateTo)
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
GROUP BY pt.fk_tenant_id, pt.budget_year, rl.line_group_id, ac.fk_kostra_account_code, e.fk_kostra_function_code

--income
INSERT @calc_table_8
select 
fk_tenant_id = @TenantId
,year = @BudgetYear-1
,kostra_value = ''
,revenue = SUM(amount)
,result = 0
,account_corr = 0
,function_corr = 0
from @budget_table a 
where a.fk_tenant_id = @TenantId
and a.budget_year = @BudgetYear-1
and line_group_id = '10'

--Net
INSERT @calc_table_8
select 
fk_tenant_id = @TenantId
,year = @BudgetYear-1
,kostra_value = ''
,revenue = 0
,result = SUM(amount) * -1
,account_corr = 0
,function_corr = 0
from @budget_table a
where a.fk_tenant_id = @TenantId
and a.budget_year = @BudgetYear-1
and line_group_id IN ( '10','20','30','40' )

--Account corr
INSERT @calc_table_8
select 
fk_tenant_id = @TenantId
,year = @BudgetYear-1
,kostra_value = ka.kostra_account
,revenue = 0
,result = 0
,account_corr = SUM(amount) *-1
,function_corr = 0
from @budget_table a
JOIN @kostra_accounts ka ON a.fk_kostra_account_code = ka.kostra_account
where a.fk_tenant_id = @TenantId
and a.budget_year = @BudgetYear-1
GROUP BY ka.kostra_account

--function corr
INSERT @calc_table_8
select 
fk_tenant_id = @TenantId
,year = @BudgetYear-1
,kostra_value = f.kostra_function
,revenue = 0
,result = 0
,account_corr = 0
,function_corr = SUM(amount) 
from @budget_table a
JOIN @kostra_functions f ON a.fk_kostra_function_code = f.kostra_function
where a.fk_tenant_id = @TenantId
and a.budget_year = @BudgetYear-1
GROUP BY f.kostra_function

--Revenue finplan
INSERT @calc_table_8
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_value = ''
,revenue = amount
,result = 0
,account_corr = 0
,function_corr = 0
FROM (
select 
pt.fk_tenant_id
, SUM (year_1_amount+change_1_amount) year_1_amount
, SUM (year_2_amount+change_2_amount) year_2_amount
, SUM (year_3_amount+change_3_amount) year_3_amount
, SUM (year_4_amount+change_4_amount) year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @BudgetYear BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line c on ac.fk_kostra_Account_code = c.fk_kostra_Account_code and c.report = '54_OVDRIFT'
where PT.fk_tenant_id = @TenantId
and PT.budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
and c.line_group_id = '10'
group by pt.fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--Result finplan
INSERT @calc_table_8
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_value = ''
,revenue = 0
,result = amount * -1
,account_corr = 0
,function_corr = 0
FROM (
select 
pt.fk_tenant_id
, SUM (year_1_amount+change_1_amount) year_1_amount
, SUM (year_2_amount+change_2_amount) year_2_amount
, SUM (year_3_amount+change_3_amount) year_3_amount
, SUM (year_4_amount+change_4_amount) year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @BudgetYear BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line c on ac.fk_kostra_Account_code = c.fk_kostra_Account_code and c.report = '54_OVDRIFT'
where PT.fk_tenant_id = @TenantId
and PT.budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
and c.line_group_id IN ( '10','20','30','40' )
group by pt.fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--Account corr finplan
INSERT @calc_table_8
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_value = kostra_account
,revenue = 0
,result = 0
,account_corr = amount *-1
,function_corr = 0
FROM (
select 
pt.fk_tenant_id, ka.kostra_account
, SUM (year_1_amount+change_1_amount) year_1_amount
, SUM (year_2_amount+change_2_amount) year_2_amount
, SUM (year_3_amount+change_3_amount) year_3_amount
, SUM (year_4_amount+change_4_amount) year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @BudgetYear BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN @kostra_accounts ka ON ac.fk_kostra_account_code = ka.kostra_account
where PT.fk_tenant_id = @TenantId
and PT.budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
group by pt.fk_tenant_id, ka.kostra_account
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


--function corr finplan
INSERT @calc_table_8
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,kostra_value = kostra_function
,revenue = 0
,result = 0
,account_corr = 0
,function_corr = amount
FROM (
select 
pt.fk_tenant_id, f.kostra_function
, SUM (year_1_amount+change_1_amount) year_1_amount
, SUM (year_2_amount+change_2_amount) year_2_amount
, SUM (year_3_amount+change_3_amount) year_3_amount
, SUM (year_4_amount+change_4_amount) year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @BudgetYear BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN tco_functions e ON PT.fk_tenant_id = e.pk_tenant_id and PT.function_code = e.pk_Function_code AND PT.budget_year BETWEEN DATEPART(year,e.dateFrom) AND DATEPART(year,e.dateTo)
JOIN @kostra_functions f ON e.fk_kostra_function_code = f.kostra_function
where PT.fk_tenant_id = @TenantId
and PT.budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
group by pt.fk_tenant_id,f.kostra_function
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt

SET @updated = getdate() 

DELETE FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND fk_proposal_id = @proposal_id

INSERT INTO [tps_kpi_data_polsim] (fk_tenant_id,fk_proposal_id,budget_year,module,kpi_id,year,type, kostra_code, numerator,denominator,kpi_value,updated,updated_by)

select fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, a.year
, type = '3-KPI'
, kostra_code = @indicator
, numerator = 0
,denominator = 0
,kpi_value = indicator_value
, updated = @updated
, updated_by = @user_id
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year between @BudgetYear - 5 AND @BudgetYear-2

UNION ALL

select  
  fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, year
, type = '3-KPI'
, kostra_code = ''
, numerator = SUM(result+(account_corr+function_corr))
,denominator = SUM(revenue)*-1
,kpi_value = CASE WHEN SUM(REVENUE) = 0 THEN 0 ELSE  ROUND((SUM(result+(account_corr+function_corr))/SUM(revenue*-1))*100,1) END
, updated = @updated
, updated_by = @user_id
from @calc_table_8
GROUP BY fk_tenant_id, year

UNION ALL

SELECT @TenantId
, fk_proposal_id = @proposal_id
, @BudgetYear, @module, @kpi_id, year, type = '2-CHANGE',kostra_value
,numerator = account_corr+function_corr
,denominator = 0
,kpi_value = 0
,@updated
,@user_id
FROM @calc_table_8
where kostra_value != ''

UNION ALL

select  
  fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, year
, type = '1-IB'
, kostra_code = ''
, numerator = SUM(result)
,denominator = 0
,kpi_value = 0
, updated = @updated
, updated_by = @user_id
from @calc_table_8
GROUP BY fk_tenant_id, year

END

IF @kpiId = 15
BEGIN

DECLARE @calc_table_15 TABLE (fk_tenant_id INT,year INT, revenue DECIMAL(18,2), fin_amt DECIMAL(18,2))

INSERT INTO @budget_table (fk_tenant_id, budget_year, line_group_id, fk_kostra_account_code, fk_kostra_function_code, amount)
select 
pt.fk_tenant_id
,pt.budget_year
,line_group_id = rl.line_group_id
,fk_kostra_account_code = ''
,fk_function_code = ''
,amount = SUM(pt.amount_year_1)
from @tbu_trans_accounts PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code AND @BudgetYear-1 BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id in ('10','30')
where pt.fk_tenant_id = @TenantId
and pt.budget_year = @BudgetYear-1
GROUP BY pt.fk_tenant_id, pt.budget_year, rl.line_group_id


INSERT INTO @calc_table_15 (fk_tenant_id, year,revenue, fin_amt)
SELECT 
fk_tenant_id,
budget_year,
revenue = amount*-1,
fin_amt = 0
FROM @budget_table WHERE line_group_id = '10'

INSERT INTO @calc_table_15 (fk_tenant_id, year,revenue, fin_amt)
SELECT 
fk_tenant_id,
budget_year,
revenue = 0,
fin_amt = amount
FROM @budget_table WHERE line_group_id = '30'


INSERT INTO @calc_table_15 (fk_tenant_id, year,revenue, fin_amt)
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,revenue = amount*-1
,fin_amt =  0
FROM (
select 
fk_tenant_id
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+ change_2_amount)year_2_amount
,SUM(year_3_amount+ change_3_amount)year_3_amount
,SUM(year_4_amount+ change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='10'
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
group by fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt


INSERT INTO @calc_table_15 (fk_tenant_id, year,revenue, fin_amt)
select 
fk_tenant_id
, year =	CASE	WHEN year = 'year_1_amount' THEN @BudgetYear
					WHEN year = 'year_2_amount' THEN @BudgetYear+1
					WHEN year = 'year_3_amount' THEN @BudgetYear+2
					WHEN year = 'year_4_amount' THEN @BudgetYear+3
					END
,revenue = 0
,fin_amt = amount
FROM (
select 
fk_tenant_id
,SUM(year_1_amount+change_1_amount)year_1_amount
,SUM(year_2_amount+ change_2_amount)year_2_amount
,SUM(year_3_amount+ change_3_amount)year_3_amount
,SUM(year_4_amount+ change_4_amount)year_4_amount
from tps_operations_propopsal PT
JOIN tco_accounts ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.account_code = ac.pk_account_code AND @budget_year BETWEEN datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line RL ON ac.fk_kostra_account_code = RL.fk_kostra_account_code and RL.report = '54_OVDRIFT' and RL.line_group_id ='30'
where fk_tenant_id = @TenantId
and budget_year = @BudgetYear
and pt.fk_proposal_id = @proposal_id
and pt.status = 1
and pt.fk_action_id != -100
group by fk_tenant_id
) p
UNPIVOT
	(amount FOR year IN 
		(year_1_amount, year_2_amount, year_3_amount, year_4_amount)
)AS unpvt



SET @updated = getdate() 

DELETE FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND fk_proposal_id = @proposal_id

INSERT INTO [tps_kpi_data_polsim] (fk_tenant_id,fk_proposal_id,budget_year,module,kpi_id,year,type, kostra_code, numerator,denominator,kpi_value,updated,updated_by)
select fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, a.year
, type = '3-KPI'
, kostra_code = @indicator
, numerator = 0
,denominator = 0
,kpi_value = indicator_value
, updated = @updated
, updated_by = @user_id
FROM gko_kostra_data_corp a
JOIN gco_tenants b ON a.fk_region_code = b.municipality_id
where b.pk_id = @TenantId
and fk_indicator_code = @indicator
and a.year between @BudgetYear - 5 AND @BudgetYear-2

UNION ALL

select  
  fk_tenant_id = @TenantId
, fk_proposal_id = @proposal_id
, budget_year = @BudgetYear
, module = @module
, kpi_id = @kpi_id
, year
, type = '3-KPI'
, fk_kostra_account_code = ''
, numerator = SUM(fin_amt)
,denominator = SUM(revenue)
,kpi_value = CASE WHEN SUM(revenue) != 0 THEN  ROUND((SUM(fin_amt)/SUM(revenue))*100,1) ELSE 0 END
, updated = @updated
, updated_by = @user_id
from @calc_table_15
GROUP BY fk_tenant_id, year

END

SELECT year,type,numerator,denominator, kpi_value FROM [tps_kpi_data_polsim] 
WHERE fk_tenant_id = @TenantId
AND module = @module
AND budget_year = @BudgetYear
AND kpi_id = @kpiId
AND type = '3-KPI'
AND fk_proposal_id = @proposal_id
ORDER BY year, type


RETURN 0
