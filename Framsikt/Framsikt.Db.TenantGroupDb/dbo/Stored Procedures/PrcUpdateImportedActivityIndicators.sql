CREATE OR ALTER PROCEDURE [dbo].[PrcUpdateImportedActivityIndicators]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@isServiceIdSetup bit,
	@serviceLevel int
AS
		--Clear all the error information--
	UPDATE tco_stage_activity_indicators_import
	SET 
	framsikt_reference_error = 0,
	external_reference_error = 0 ,
	measurment_criteria_error = 0,
	value_type_error = 0,
	frequency_error = 0,
	aggregation_activity_reporting_error = 0,
	org_level_error = 0,
	org_id_error = 0,
	service_id_error = 0,
	period_1_error = 0,
	period_2_error = 0,
	period_3_error = 0,
	period_4_error  = 0,
	period_5_error  = 0,
	period_6_error = 0,
	period_7_error = 0,
	period_8_error = 0,
	period_9_error = 0,
	period_10_error = 0,
	period_11_error = 0,
	period_12_error = 0,
	error_count = 0
	WHERE tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
		tco_stage_activity_indicators_import.budget_year = @budget_year AND 
		tco_stage_activity_indicators_import.user_id = @user_id;

	-- update columns based on external_reference
	--fk_indicator_code
	update tco_stage_activity_indicators_import set fk_indicator_code = tco_indicator_setup.pk_indicator_code												
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.external_reference is not null AND tco_stage_activity_indicators_import.external_reference <> '');

	--framsikt_reference
	update tco_stage_activity_indicators_import set framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code												
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.external_reference is not null AND tco_stage_activity_indicators_import.external_reference <> '') and
	(tco_stage_activity_indicators_import.framsikt_reference is null or tco_stage_activity_indicators_import.framsikt_reference = '');

	--measurment_criteria
	update tco_stage_activity_indicators_import set measurment_criteria = tco_indicator_setup.measurment_criteria												
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.external_reference is not null AND tco_stage_activity_indicators_import.external_reference <> '') and
	(tco_stage_activity_indicators_import.measurment_criteria is null or tco_stage_activity_indicators_import.measurment_criteria = '');

	--value_type
	update tco_stage_activity_indicators_import set value_type = tco_indicator_setup.value_type												
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.external_reference is not null AND tco_stage_activity_indicators_import.external_reference <> '') and
	(tco_stage_activity_indicators_import.value_type is null or tco_stage_activity_indicators_import.value_type = '');

	--frequency_str
	update tco_stage_activity_indicators_import set frequency_str = tco_indicator_setup.frequency												
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.external_reference is not null AND tco_stage_activity_indicators_import.external_reference <> '') and
	(tco_stage_activity_indicators_import.frequency_str is null or tco_stage_activity_indicators_import.frequency_str = '');

	--aggregation_activity_reporting_str
	update tco_stage_activity_indicators_import set aggregation_activity_reporting_str = tco_indicator_setup.aggregation_activity_reporting												
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.external_reference is not null AND tco_stage_activity_indicators_import.external_reference <> '') and
	(tco_stage_activity_indicators_import.aggregation_activity_reporting_str is null or tco_stage_activity_indicators_import.aggregation_activity_reporting_str = '');


	-- update columns based on framsikt_reference
	--fk_indicator_code
	update tco_stage_activity_indicators_import set fk_indicator_code = tco_indicator_setup.pk_indicator_code
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.framsikt_reference is not null or tco_stage_activity_indicators_import.framsikt_reference <> '');

	--external_reference
	update tco_stage_activity_indicators_import set external_reference = tco_indicator_setup.external_reference
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.framsikt_reference is not null or tco_stage_activity_indicators_import.framsikt_reference <> '') and
	(tco_stage_activity_indicators_import.external_reference is null or tco_stage_activity_indicators_import.external_reference = '');

	--measurment_criteria
	update tco_stage_activity_indicators_import set measurment_criteria = tco_indicator_setup.measurment_criteria
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.framsikt_reference is not null or tco_stage_activity_indicators_import.framsikt_reference <> '') and
	(tco_stage_activity_indicators_import.measurment_criteria is null or tco_stage_activity_indicators_import.measurment_criteria = '');

	--value_type
	update tco_stage_activity_indicators_import set value_type = tco_indicator_setup.value_type
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.framsikt_reference is not null or tco_stage_activity_indicators_import.framsikt_reference <> '') and
	(tco_stage_activity_indicators_import.value_type is null or tco_stage_activity_indicators_import.value_type = '');

	--frequency_str
	update tco_stage_activity_indicators_import set frequency_str = tco_indicator_setup.frequency
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.framsikt_reference is not null or tco_stage_activity_indicators_import.framsikt_reference <> '') and
	(tco_stage_activity_indicators_import.frequency_str is null or tco_stage_activity_indicators_import.frequency_str = '');

	--aggregation_activity_reporting_str
	update tco_stage_activity_indicators_import set aggregation_activity_reporting_str = tco_indicator_setup.aggregation_activity_reporting
	from tco_stage_activity_indicators_import
	inner join tco_indicator_setup on tco_stage_activity_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_activity_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_indicator_setup.indicator_type = 2 and
	(tco_stage_activity_indicators_import.framsikt_reference is not null or tco_stage_activity_indicators_import.framsikt_reference <> '') and
	(tco_stage_activity_indicators_import.aggregation_activity_reporting_str is null or tco_stage_activity_indicators_import.aggregation_activity_reporting_str = '');

	--replace comma(,) with dot(.) in period 1 - 12, only for number type.
	update tco_stage_activity_indicators_import set period_1 = REPLACE(period_1,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_2 = REPLACE(period_2,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_3 = REPLACE(period_3,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_4 = REPLACE(period_4,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_5 = REPLACE(period_5,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_6 = REPLACE(period_6,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_7 = REPLACE(period_7,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_8 = REPLACE(period_8,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_9 = REPLACE(period_9,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_10 = REPLACE(period_10,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_11 = REPLACE(period_11,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

	update tco_stage_activity_indicators_import set period_12 = REPLACE(period_12,',','.')
	from tco_stage_activity_indicators_import
	where
	tco_stage_activity_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_activity_indicators_import.budget_year = @budget_year AND 
	tco_stage_activity_indicators_import.user_id = @user_id and
	tco_stage_activity_indicators_import.value_type in ('n0','##0.0 \%','n1','n2','n3')

RETURN 0
