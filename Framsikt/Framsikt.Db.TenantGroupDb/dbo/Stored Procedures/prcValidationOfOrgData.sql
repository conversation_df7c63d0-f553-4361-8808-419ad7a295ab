CREATE OR ALTER PROCEDURE [dbo].[prcValidationOfOrgData]
	@tenant_id int,
	@org_version VARCHAR(25)
AS
Begin

declare @year int = (select datepart(year, getdate()))

DROP TABLE IF EXISTS #tco_org_validations

CREATE TABLE  #tco_org_validations 
(
[fk_tenant_id] [int] NOT NULL,
[validation_id] [int] NOT NULL,
[error_value] varchar(200) NOT NULL
); 

DECLARE @fp_param_l1 VARCHAR(25) = (SELECT param_value FROM tco_parameters p1 WHERE p1.fk_tenant_id = @tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1)
DECLARE @fp_param_l2 VARCHAR(25) = (SELECT param_value FROM tco_parameters p1 WHERE p1.fk_tenant_id = @tenant_id AND p1.param_name = 'FINPLAN_LEVEL_2' AND p1.active = 1)


SELECT a.fk_tenant_id, b.budget_year, 
fp_level_1_value = convert(varchar(25),NULL),
fp_level_2_value = convert(varchar(25),NULL),
oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,
sv.service_id_1, sv.service_id_2,sv.service_id_3,sv.service_id_4,sv.service_id_5
INTO #hlp_fp_trans_check
FROM tfp_trans_header a
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code and oh.fk_org_version = @org_version
JOIN tco_departments dep ON oh.fk_department_code = dep.pk_department_code AND oh.fk_tenant_id = dep.fk_tenant_id AND dep.status = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
GROUP BY a.fk_tenant_id, b.budget_year, oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,
sv.service_id_1, sv.service_id_2,sv.service_id_3,sv.service_id_4,sv.service_id_5
;

UPDATE h 
SET fp_level_1_value = CASE 
    WHEN @fp_param_l1 = 'org_id_1' THEN org_id_1
    WHEN @fp_param_l1 = 'org_id_2' THEN org_id_2
    WHEN @fp_param_l1 = 'org_id_3' THEN org_id_3
    WHEN @fp_param_l1 = 'org_id_4' THEN org_id_4
    WHEN @fp_param_l1 = 'org_id_5' THEN org_id_5
    WHEN @fp_param_l1 = 'service_id_1' THEN service_id_1
    WHEN @fp_param_l1 = 'service_id_2' THEN service_id_2
    WHEN @fp_param_l1 = 'service_id_3' THEN service_id_3
    WHEN @fp_param_l1 = 'service_id_4' THEN service_id_4
    WHEN @fp_param_l1 = 'service_id_5' THEN service_id_5
  ELSE '' END,
fp_level_2_value = CASE 
    WHEN @fp_param_l2 = 'org_id_1' THEN org_id_1
    WHEN @fp_param_l2 = 'org_id_2' THEN org_id_2
    WHEN @fp_param_l2 = 'org_id_3' THEN org_id_3
    WHEN @fp_param_l2 = 'org_id_4' THEN org_id_4
    WHEN @fp_param_l2 = 'org_id_5' THEN org_id_5
    WHEN @fp_param_l2 = 'service_id_1' THEN service_id_1
    WHEN @fp_param_l2 = 'service_id_2' THEN service_id_2
    WHEN @fp_param_l2 = 'service_id_3' THEN service_id_3
    WHEN @fp_param_l2 = 'service_id_4' THEN service_id_4
    WHEN @fp_param_l2 = 'service_id_5' THEN service_id_5
  ELSE NULL END
FROM #hlp_fp_trans_check h



/*validation id 1*/
/*Dept used in tfp_trans_detail but not in tco_departments*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 1, department_code
FROM tfp_trans_detail a
where a.fk_tenant_id = @tenant_id
AND a.department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
) 
AND a.department_code != '';

/*validation id 2*/
/*Dept used in tbu_trans_detail but not in tco_departments*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 2, department_code
FROM tbu_trans_detail a
where a.fk_tenant_id = @tenant_id
AND a.department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

/*validation id 3*/
/*Dept used in tfp_accounting_data but not in tco_departments*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 3, department_code
FROM tfp_accounting_data a
where a.fk_tenant_id = @tenant_id
AND a.department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

/*validation id 5*/
/*Dept used in tco_org_hierarchy but not in tco_departments*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 5, fk_department_code
FROM tco_org_hierarchy a
where a.fk_tenant_id = @tenant_id and a.fk_org_version = @org_version
AND a.fk_department_code NOT IN 
(SELECT pk_department_code FROM  tco_departments m where a.fk_tenant_id = m.fk_tenant_id
);

/*validation id 6*/
/*Dept used in tco_departments but not in tco_org_hierarchy*/

INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 6, a.pk_department_code
FROM tco_departments a
LEFT JOIN tco_org_hierarchy m ON a.pk_department_code = m.fk_department_code AND a.fk_tenant_id = m.fk_tenant_id
LEFT JOIN tco_org_version ov ON m.fk_org_version= ov.pk_org_version AND m.fk_tenant_id = ov.fk_tenant_id AND a.year_to  BETWEEN OV.period_from/100 and OV.period_to/100  AND  m.fk_org_version = @org_version
where a.fk_tenant_id = @tenant_id
AND m.pk_id is null;



/*validation id 7*/
/*Dept might be duplicate in tco_departments*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 7, a.pk_department_code
FROM tco_departments a
WHERE a.fk_tenant_id = @tenant_id
GROUP BY a.fk_tenant_id, a.pk_department_code, a.year_from, a.year_to
HAVING COUNT(*) > 1;

/*validation id 8*/
/*Dept is duplicate in tco_org_hierarchy*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 8, a.fk_department_code
FROM tco_org_hierarchy a
WHERE a.fk_tenant_id = @tenant_id and a.fk_org_version = @org_version
GROUP BY a.fk_tenant_id, a.fk_department_code
HAVING COUNT(*) > 1;


/*validation id 10*/
/*Project is overlapping for budget year*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct a.fk_tenant_id, 10, a.pk_project_code
FROM tco_projects a
WHERE a.fk_tenant_id = @tenant_id
AND DATEPART(YEAR,getdate()) BETWEEN DATEPART(YEAR, date_from) AND DATEPART(YEAR, date_to)
GROUP BY a.fk_tenant_id, a.pk_project_code
HAVING COUNT(*) > 1


/*validation id 26*/
/*Data missing in tmd_fp_level_defaults*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT  distinct S.fk_tenant_id, 26, 
'Verdi Nivå 1: ' +  S.fp_level_1_value + ',Verdi Nivå 2: ' + S.fp_level_2_value
FROM
(SELECT h.fk_tenant_id, h.budget_year, 
h.fp_level_1_value, h.fp_level_2_value
FROM #hlp_fp_trans_check h) S
WHERE s.fk_tenant_id = @tenant_id
AND S.fp_level_2_value  IS NOT NULL
AND S.fp_level_1_value  IS NOT NULL
AND NOT EXISTS
(SELECT * FROM [dbo].[tmd_fp_level_defaults] T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND S.fp_level_1_value = T.fp_level_1_value
AND S.fp_level_2_value = T.fp_level_2_value
AND T.fk_org_version = @org_version)
GROUP BY
S.fk_tenant_id, S.budget_year, S.fp_level_1_value,S.fp_level_2_value;


/*validation id 26*/
/*Data missing in tmd_fp_level_defaults*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT  distinct S.fk_tenant_id, 26, 
'Verdi Nivå 1: ' +  S.fp_level_1_value
FROM
(SELECT h.fk_tenant_id, h.budget_year, 
h.fp_level_1_value, fp_level_2_value = ''
FROM #hlp_fp_trans_check h) S
WHERE S.fk_tenant_id = @tenant_id
AND NOT EXISTS
(SELECT * FROM [dbo].[tmd_fp_level_defaults] T
WHERE S.fk_tenant_id = T.fk_tenant_id
AND S.fp_level_1_value = T.fp_level_1_value
AND S.fp_level_2_value = T.fp_level_2_value
AND  T.fk_org_version= @org_version)
GROUP BY
S.fk_tenant_id, S.budget_year, S.fp_level_1_value,S.fp_level_2_value;

/*validation id 27*/
/*Invalid department defined in tmd_fp_level_defaults*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct df.fk_tenant_id, 27, df.fk_department_code
FROM tmd_fp_level_defaults df
WHERE df.fk_tenant_id = @tenant_id
and df.fk_org_version = @org_version
AND not exists
(SELECT * FROM 
(
SELECT oh.fk_tenant_id, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fk_department_code, ov.pk_org_version FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.active = 1 and oh.fk_org_version = @org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id) A
WHERE df.fp_level_1_value = a.fp_level_1_value AND df.fk_tenant_id = A.fk_tenant_id
AND df.fk_department_code = A.fk_department_code
AND df.fk_org_version = A.pk_org_version
)
AND df.fk_tenant_id IN (
SELECT fk_tenant_id FROM tco_parameters p1 WHERE p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1 AND p1.param_value like 'org_id%'
AND NOT EXISTS (SELECT * FROM tco_parameters p2 WHERE p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1 AND p2.param_value like 'org_id%'
AND p1.fk_tenant_id = p2.fk_tenant_id)
)
union 
SELECT distinct df.fk_tenant_id, 27, df.fk_department_code
FROM tmd_fp_level_defaults df
WHERE df.fk_tenant_id =@tenant_id
and df.fk_org_version = @org_version
and fp_level_2_value != ''
AND not exists
(SELECT * FROM 
(
SELECT oh.fk_tenant_id, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fk_department_code, ov.pk_org_version FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id) A
WHERE df.fp_level_1_value = a.fp_level_1_value AND df.fp_level_2_value = A.fp_level_2_value AND df.fk_tenant_id = A.fk_tenant_id
AND df.fk_department_code = A.fk_department_code
AND df.fk_org_version = A.pk_org_version
)
AND df.fk_tenant_id IN (SELECT fk_tenant_id FROM tco_parameters p2 WHERE p2.fk_tenant_id = @tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1 AND param_value like 'org_id%')


/*validation id 31*/
/*Account used in tmd_fp_level_defaults is not valid. Budprop will throw an error*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct df.fk_tenant_id, 31, df.fk_account_code
FROM tmd_fp_level_defaults df
WHERE fk_tenant_id = @tenant_id
AND NOT EXISTS (
SELECT * FROM tco_accounts ac WHERE df.fk_account_code = ac.pk_account_code AND df.fk_tenant_id = ac.pk_tenant_id AND AC.isActive = 1)

/*validation id 32*/
/*Department used in tmd_fp_level_defaults is not valid. Budprop will throw an error*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct df.fk_tenant_id, 32, df.fk_department_code 
FROM tmd_fp_level_defaults df
WHERE fk_tenant_id = @tenant_id
AND NOT EXISTS (
SELECT * FROM tco_departments d WHERE df.fk_department_code = d.pk_department_code AND df.fk_tenant_id = d.fk_tenant_id AND d.status = 1)

/*validation id 33*/
/*Function used in tmd_fp_level_defaults is not valid. Budprop will throw an error*/
INSERT INTO #tco_org_validations (fk_tenant_id, validation_id, error_value)
SELECT distinct df.fk_tenant_id, 33, df.fk_function_code
FROM tmd_fp_level_defaults df
WHERE fk_tenant_id = @tenant_id
AND NOT EXISTS (
SELECT * FROM tco_functions fc WHERE df.fk_function_code = fc.pk_Function_code AND df.fk_tenant_id = fc.pk_tenant_id AND fc.isActive = 1)


End

/*delete all the existing validations on tenant level*/
delete from tco_org_validations where fk_tenant_id = @tenant_id and validation_id not between 1001 and 2000

insert into tco_org_validations
select * from #tco_org_validations

-- Fix budalloc setup if depts etc has been changed 



delete df
FROM tmd_acc_defaults df
WHERE df.link_type LIKE 'BUDALLOC%'
AND df.acc_type = 'DEPARTMENT'
AND NOT EXISTS (
SELECT * FROM tco_departments dp 
WHERE dp.status = 1
AND dp.pk_department_code = df.acc_value
AND dp.fk_tenant_id = df.fk_tenant_id)

-- L2

INSERT INTO tmd_acc_defaults (fk_tenant_id,acc_type,acc_value,link_type,link_value,module,active,updated,updated_by, fk_org_version)
SELECT oh.fk_tenant_id,'DEPARTMENT' AS acc_type, MIN(oh.fk_department_code) acc_value,'BUDALLOC_ORG_L2' as link_type,
oh.org_id_2,'FP' as module,1 as active, getdate() as updated, 1002 as updated_by, oh.fk_org_version
FROM tco_org_hierarchy oh
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version=@org_version AND ov.pk_org_version = oh.fk_org_version
JOIN tco_departments df ON oh.fk_department_code = df.pk_department_code AND oh.fk_tenant_id = df.fk_tenant_id AND df.status = 1
WHERE NOT EXISTS (
SELECT * FROM tmd_acc_defaults df
WHERE df.link_type = 'BUDALLOC_ORG_L2'
AND df.fk_tenant_id = oh.fk_tenant_id
AND df.link_value = oh.org_id_2
AND df.fk_org_version = oh.fk_org_version
)
AND org_id_2 != ''
GROUP BY oh.fk_tenant_id,oh.org_id_2,oh.fk_org_version
ORDER BY oh.fk_tenant_id,oh.org_id_2



-- L3

INSERT INTO tmd_acc_defaults (fk_tenant_id,acc_type,acc_value,link_type,link_value,module,active,updated,updated_by, fk_org_version)
SELECT oh.fk_tenant_id,'DEPARTMENT' AS acc_type, MIN(oh.fk_department_code) acc_value,'BUDALLOC_ORG_L3' as link_type,
oh.org_id_3,'FP' as module,1 as active, getdate() as updated, 1002 as updated_by, oh.fk_org_version 
FROM tco_org_hierarchy oh
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version=@org_version AND oh.fk_org_version = ov.pk_org_version
JOIN tco_departments df ON oh.fk_department_code = df.pk_department_code AND oh.fk_tenant_id = df.fk_tenant_id AND df.status = 1
WHERE NOT EXISTS (
SELECT * FROM tmd_acc_defaults df
WHERE df.link_type = 'BUDALLOC_ORG_L3'
AND df.fk_tenant_id = oh.fk_tenant_id
AND df.link_value = oh.org_id_3
AND df.fk_org_version = oh.fk_org_version
)
AND org_id_3 != ''
GROUP BY oh.fk_tenant_id,oh.org_id_3, oh.fk_org_version
ORDER BY oh.fk_tenant_id,oh.org_id_3


-- L4

INSERT INTO tmd_acc_defaults (fk_tenant_id,acc_type,acc_value,link_type,link_value,module,active,updated,updated_by, fk_org_version)
SELECT oh.fk_tenant_id,'DEPARTMENT' AS acc_type, MIN(oh.fk_department_code) acc_value,'BUDALLOC_ORG_L4' as link_type,
oh.org_id_4,'FP' as module,1 as active, getdate() as updated, 1002 as updated_by, oh.fk_org_version 
FROM tco_org_hierarchy oh
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version=@org_version AND ov.pk_org_version = oh.fk_org_version
JOIN tco_departments df ON oh.fk_department_code = df.pk_department_code AND oh.fk_tenant_id = df.fk_tenant_id AND df.status = 1
WHERE NOT EXISTS (
SELECT * FROM tmd_acc_defaults df
WHERE df.link_type = 'BUDALLOC_ORG_L4'
AND df.fk_tenant_id = oh.fk_tenant_id
AND df.link_value = oh.org_id_4
AND df.fk_org_version = oh.fk_org_version
)
AND org_id_4 != ''
GROUP BY oh.fk_tenant_id,oh.org_id_4, oh.fk_org_version
ORDER BY oh.fk_tenant_id,oh.org_id_4


-- L5

INSERT INTO tmd_acc_defaults (fk_tenant_id,acc_type,acc_value,link_type,link_value,module,active,updated,updated_by, fk_org_version)
SELECT oh.fk_tenant_id,'DEPARTMENT' AS acc_type, MIN(oh.fk_department_code) acc_value,'BUDALLOC_ORG_L5' as link_type,
oh.org_id_5,'FP' as module,1 as active, getdate() as updated, 1002 as updated_by, oh.fk_org_version 
FROM tco_org_hierarchy oh
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version=@org_version AND ov.pk_org_version = oh.fk_org_version
JOIN tco_departments df ON oh.fk_department_code = df.pk_department_code AND oh.fk_tenant_id = df.fk_tenant_id AND df.status = 1
WHERE NOT EXISTS (
SELECT * FROM tmd_acc_defaults df
WHERE df.link_type = 'BUDALLOC_ORG_L5'
AND df.fk_tenant_id = oh.fk_tenant_id
AND df.link_value = oh.org_id_5
AND df.fk_org_version = oh.fk_org_version
)
AND org_id_5 != ''
GROUP BY oh.fk_tenant_id,oh.org_id_5, oh.fk_org_version
ORDER BY oh.fk_tenant_id,oh.org_id_5




Go
