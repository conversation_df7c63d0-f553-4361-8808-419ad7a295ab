CREATE OR ALTER PROCEDURE [dbo].[prcSyncHQFinancing]

AS


DECLARE @job_running INT
DECLARE @timestamp datetime2
DECLARE @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @RowsUpdated INT
DECLARE @last_pk_id INT
DECLARE @last_header_actionid INT


CREATE TABLE #TEMP_tfp_inv_header
(   [fk_tenant_id_mother] INT NOT NULL, 
	[pk_inv_action_id_mother] INT NOT NULL, 
	[pk_inv_action_id] INT NOT NULL, 
	fk_tenant_id INT NOT NULL,
	action_name nvarchar(150) NOT NULL,
	budget_year INT NOT NULL,
    [description] UNIQUEIDENTIFIER NOT NULL, 
	[action_type] [int] NOT NULL,
	[line_order] [int] NOT NULL,
	[isManuallyAdded] [int] NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] INT NOT NULL
)

CREATE TABLE #TEMP_tfp_inv_transactions
(   [fk_tenant_id_mother] INT NOT NULL, 
	[pk_inv_action_id_mother] INT DEFAULT 0 NOT NULL, 
	[pk_id_mother] INT DEFAULT 0 NOT NULL, 
	[pk_id] INT NOT NULL, 
	[fk_inv_action_id] int NOT NULL DEFAULT (0),
	[fk_inv_details_id] int NOT NULL,
    [fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [department_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	vat_refund DECIMAL(18, 2) NOT NULL, 
	[year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
	[updated] DATETIME NOT NULL, 
	[type] NVARCHAR(1) NOT NULL,
    [updated_by] INT NOT NULL, 
    [existing_flag] INT NULL, 
	[fk_change_id] int not null,
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
    [fk_prog_code] NVARCHAR(25) DEFAULT 1 NULL
)


CREATE TABLE #TEMP_header_new
(	[pk_id] INT NOT NULL IDENTITY,
	[fk_tenant_id_mother] INT NOT NULL, 
	[pk_inv_action_id_mother] INT NOT NULL, 
	[pk_inv_action_id_source] INT NOT NULL, 
	action_name nvarchar(150) NOT NULL,
	budget_year INT NOT NULL,
    [description] UNIQUEIDENTIFIER NOT NULL, 
	[action_type] [int] NOT NULL,
	[line_order] [int] NOT NULL,
	[isManuallyAdded] [int] NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] INT NOT NULL
)


CREATE TABLE #TEMP_tfp_inv_transactions_new
(   [counter] INT NOT NULL IDENTITY, 
	[fk_tenant_id_mother] INT NOT NULL, 
	[pk_inv_action_id_mother] INT NOT NULL, 
	[pk_id_mother] INT NOT NULL, 
	[pk_id] INT NOT NULL, 
	[fk_inv_action_id] int NOT NULL DEFAULT (0),
	[fk_inv_details_id] int NOT NULL,
    [fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [department_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	vat_refund DECIMAL(18, 2) NOT NULL, 
	[year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
	[updated] DATETIME NOT NULL, 
	[type] NVARCHAR(1) NOT NULL,
    [updated_by] INT NOT NULL, 
    [existing_flag] INT NULL, 
	[fk_change_id] int not null,
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
    [fk_prog_code] NVARCHAR(25) DEFAULT 1 NULL
)


INSERT INTO #TEMP_tfp_inv_header (fk_tenant_id_mother,pk_inv_action_id_mother, pk_inv_action_id,fk_tenant_id,action_name,budget_year,description,action_type,line_order,isManuallyAdded,updated,updated_by)
SELECT DISTINCT DEF.fk_tenant_id, 0 as pk_inv_action_id_mother, h.pk_inv_action_id,h.fk_tenant_id,h.action_name,h.budget_year,h.description,h.action_type,h.line_order,h.isManuallyAdded,h.updated,h.updated_by
FROM tfp_inv_header h
JOIN tfp_inv_transactions d ON h.pk_inv_action_id = d.fk_inv_action_id AND h.fk_tenant_id = d.fk_tenant_id AND fk_investment_id = 0 AND h.budget_year = d.budget_year
JOIN tmd_hq_tenant_definition def ON h.fk_tenant_id = def.tenant_id_child AND h.budget_year = DEF.budget_year

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

INSERT INTO #TEMP_tfp_inv_transactions (fk_tenant_id_mother, pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code)
SELECT DEF.fk_tenant_id, d.pk_id,d.fk_inv_action_id,d.fk_investment_id,d.fk_tenant_id,d.budget_year,d.fk_account_code,d.fk_function_code,d.department_code ,d.vat_rate,d.vat_refund, d.year_1_amount,d.year_2_amount,d.year_3_amount,d.year_4_amount,d.updated,d.type,d.updated_by ,d.fk_project_code,d.free_dim_1,d.fk_change_id,d.free_dim_2,d.free_dim_3,d.free_dim_4,d.fk_inv_details_id,d.fk_alter_code,d.fk_adjustment_code,d.fk_prog_code
FROM tfp_inv_header h
JOIN tfp_inv_transactions d ON h.pk_inv_action_id = d.fk_inv_action_id AND h.fk_tenant_id = d.fk_tenant_id AND fk_investment_id = 0 AND h.budget_year = d.budget_year
JOIN tmd_hq_tenant_definition def ON d.fk_tenant_id = def.tenant_id_child AND d.budget_year = DEF.budget_year





IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

PRINT 'Remove records that should not be considered'

DELETE FROM #TEMP_tfp_inv_transactions WHERE year_1_amount = 0 AND year_2_amount = 0 AND year_3_amount = 0 AND year_4_amount = 0;

DELETE FROM #TEMP_tfp_inv_transactions WHERE fk_inv_action_id IN (SELECT pk_inv_action_id FROM #TEMP_tfp_inv_header WHERE action_type = 2 AND line_order IN (1,80))



IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

UPDATE #TEMP_tfp_inv_transactions SET fk_change_id = a.default_change_id, fk_prog_code = CONVERT(INT,b.municipality_id)
FROM #TEMP_tfp_inv_transactions t,  tmd_hq_tenant_definition a, gco_tenants b
WHERE tenant_id_child = t.fk_tenant_id
AND a.tenant_id_child = b.pk_id
AND a.budget_year = t.budget_year

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

PRINT 'Set accounting values'

UPDATE #TEMP_tfp_inv_transactions SET fk_account_code = ac.fk_kostra_account_code
FROM #TEMP_tfp_inv_transactions t, tco_accounts ac
WHERE t.fk_tenant_id = ac.pk_tenant_id
AND t.fk_account_code = ac.pk_account_code
AND t.budget_year BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo);

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

UPDATE #TEMP_tfp_inv_transactions SET fk_function_code = fc.fk_kostra_function_code
FROM #TEMP_tfp_inv_transactions t, tco_functions fc
WHERE t.fk_tenant_id = fc.pk_tenant_id
AND t.fk_function_code = fc.pk_Function_code
AND t.budget_year BETWEEN datepart(year, fc.dateFrom) AND datepart(year, fc.dateTo);

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

UPDATE #TEMP_tfp_inv_transactions SET department_code = hq.fk_department_code
FROM #TEMP_tfp_inv_transactions t, tmd_hq_departments_mapping hq
WHERE t.fk_tenant_id = hq.tenant_id_source
AND t.department_code = hq.department_code_source

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END


PRINT 'Handle header data'

UPDATE #TEMP_tfp_inv_header SET pk_inv_action_id_mother = m.fk_inv_action_id
FROM #TEMP_tfp_inv_header h, tfp_hq_financing_mapping m
WHERE h.fk_tenant_id = m.tenant_id_source
AND h.pk_inv_action_id = m.fk_inv_action_id_source;

UPDATE #TEMP_tfp_inv_header SET pk_inv_action_id_mother = h2.pk_inv_action_id
FROM #TEMP_tfp_inv_header h1, tfp_inv_header h2
WHERE h1.fk_tenant_id_mother = h2.fk_tenant_id
AND h1.action_type = h2.action_type
AND h1.line_order = h2.line_order
AND h1.isManuallyAdded = h2.isManuallyAdded
AND h1.isManuallyAdded = 0
AND h1.pk_inv_action_id_mother = 0
AND h1.budget_year = h2.budget_year;

INSERT INTO #TEMP_header_new (fk_tenant_id_mother, pk_inv_action_id_mother, pk_inv_action_id_source,action_name,budget_year,description, action_type, line_order, isManuallyAdded, updated, updated_by)
SELECT   fk_tenant_id_mother, pk_inv_action_id_mother,0 as pk_inv_action_id_source, '' AS action_name,budget_year,'00000000-0000-0000-0000-000000000000' as description, action_type, line_order, isManuallyAdded, MAX(updated), 0 AS updated_by
FROM #TEMP_tfp_inv_header
WHERE pk_inv_action_id_mother = 0 
AND isManuallyAdded = 0
GROUP BY fk_tenant_id_mother, pk_inv_action_id_mother,budget_year, action_type, line_order, isManuallyAdded

UPDATE #TEMP_header_new SET action_name = h.action_name, description = NEWID(), updated_by = h.updated_by
FROM #TEMP_header_new n, #TEMP_tfp_inv_header h
WHERE 
n.fk_tenant_id_mother = h.fk_tenant_id_mother
AND n.pk_inv_action_id_mother = h.pk_inv_action_id_mother
AND n.budget_year = h.budget_year
AND n.action_type = h.action_type
AND n.line_order = h.line_order
AND n.isManuallyAdded = h.isManuallyAdded
AND n.updated = h.updated

INSERT INTO #TEMP_header_new (fk_tenant_id_mother, pk_inv_action_id_mother, pk_inv_action_id_source,action_name,budget_year,description, action_type, line_order, isManuallyAdded, updated, updated_by)
SELECT fk_tenant_id_mother, pk_inv_action_id_mother,pk_inv_action_id, action_name,budget_year,description, action_type, line_order, isManuallyAdded, updated, updated_by
FROM #TEMP_tfp_inv_header
WHERE pk_inv_action_id_mother = 0 
AND isManuallyAdded = 1;


PRINT 'START updating header tables'

BEGIN TRANSACTION

SET @last_header_actionid = (SELECT max(pk_inv_action_id) FROM tfp_inv_header)


IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

   
PRINT 'Insert into tfp_inv_header'

INSERT INTO tfp_inv_header (fk_tenant_id, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by,pk_inv_action_id)
SELECT fk_tenant_id_mother, action_name, budget_year, description, action_type, line_order, isManuallyAdded, updated, updated_by, pk_id + @last_header_actionid
FROM #TEMP_header_new


IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

UPDATE #TEMP_header_new SET pk_inv_action_id_mother = pk_id + @last_header_actionid

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
		  ROLLBACK
        RETURN
   END

COMMIT

PRINT 'FINISH updating header tables'


UPDATE #TEMP_tfp_inv_header SET pk_inv_action_id_mother = n.pk_inv_action_id_mother
FROM #TEMP_tfp_inv_header h, #TEMP_header_new n
WHERE h.pk_inv_action_id = n.pk_inv_action_id_source
AND h.fk_tenant_id_mother = n.fk_tenant_id_mother
AND h.isManuallyAdded = n.isManuallyAdded
AND h.isManuallyAdded = 1
AND h.budget_year = n.budget_year
AND h.pk_inv_action_id_mother = 0;

UPDATE #TEMP_tfp_inv_header SET pk_inv_action_id_mother = n.pk_inv_action_id_mother
FROM #TEMP_header_new n, #TEMP_tfp_inv_header h
WHERE 
n.fk_tenant_id_mother = h.fk_tenant_id_mother
AND n.budget_year = h.budget_year
AND n.action_type = h.action_type
AND n.line_order = h.line_order
AND n.isManuallyAdded = h.isManuallyAdded
AND n.isManuallyAdded = 0
AND h.pk_inv_action_id_mother = 0;

UPDATE #TEMP_tfp_inv_transactions SET pk_inv_action_id_mother = h.pk_inv_action_id_mother
FROM #TEMP_tfp_inv_transactions d, #TEMP_tfp_inv_header h
WHERE d.fk_tenant_id = h.fk_tenant_id
AND d.fk_inv_action_id = h.pk_inv_action_id
AND d.budget_year = h.budget_year;

UPDATE #TEMP_tfp_inv_transactions SET pk_id_mother = t.fk_detail_id
FROM #TEMP_tfp_inv_transactions d, tfp_hq_financing_mapping t
WHERE d.pk_id = t.detail_id_source
AND d.fk_tenant_id = t.tenant_id_source
AND t.fk_inv_action_id_source = d.fk_inv_action_id;

-- slettede : finn de som finnes i hq tabellen men ikke lenger er i tfp_inv_transactions...

SELECT m.fk_tenant_id, m.fk_inv_action_id, m.fk_detail_id INTO #TEMP_deleted_rows FROM tfp_hq_financing_mapping m
WHERE NOT EXISTS (SELECT * FROM tfp_inv_transactions t WHERE m.tenant_id_source = t.fk_tenant_id 
AND m.detail_id_source = t.pk_id AND m.fk_inv_action_id_source = t.fk_inv_action_id)


INSERT INTO #TEMP_tfp_inv_transactions_new (fk_tenant_id_mother,pk_inv_action_id_mother, pk_id_mother, pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code)
SELECT fk_tenant_id_mother,pk_inv_action_id_mother, pk_id_mother, pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code
FROM #TEMP_tfp_inv_transactions
WHERE pk_id_mother = 0



PRINT 'START updating detail tables'

BEGIN TRANSACTION

SET @last_pk_id = (SELECT max(pk_id) FROM tfp_inv_transactions)

PRINT 'Insert into tfp_inv_transactions'


INSERT INTO tfp_inv_transactions (pk_id,fk_inv_action_id,fk_investment_id,fk_tenant_id,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type, updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code)
SELECT counter+@last_pk_id, pk_inv_action_id_mother,fk_investment_id, fk_tenant_id_mother,budget_year,fk_account_code,fk_function_code ,department_code ,vat_rate,vat_refund,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated,type,updated_by ,fk_project_code,free_dim_1,fk_change_id,free_dim_2,free_dim_3,free_dim_4,fk_inv_details_id,fk_alter_code,fk_adjustment_code,fk_prog_code
FROM #TEMP_tfp_inv_transactions_new

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

UPDATE #TEMP_tfp_inv_transactions_new SET pk_id_mother = counter+@last_pk_id

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

PRINT 'UPDATE amount on existing rows'

UPDATE tfp_inv_transactions SET year_1_amount = b.year_1_amount, year_2_amount = b.year_2_amount, year_3_amount = b.year_3_amount, year_4_amount = b.year_4_amount, updated = b.updated, updated_by = b.updated_by
FROM tfp_inv_transactions a, #TEMP_tfp_inv_transactions b
WHERE a.fk_tenant_id = b.fk_tenant_id_mother
AND a.pk_id = b.pk_id_mother
AND a.fk_inv_action_id = b.pk_inv_action_id_mother
AND a.budget_year = b.budget_year;


PRINT 'Insert into tfp_hq_financing_mapping'

INSERT INTO tfp_hq_financing_mapping (fk_tenant_id, fk_inv_action_id, fk_detail_id, tenant_id_source, fk_inv_action_id_source, detail_id_source, updated, updated_by)
SELECT DISTINCT fk_tenant_id_mother, pk_inv_action_id_mother, pk_id_mother, fk_tenant_id, fk_inv_action_id, pk_id, GETDATE(),updated_by
FROM #TEMP_tfp_inv_transactions_new 

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

PRINT 'Delete from tfp_inv_transactions'

DELETE D FROM tfp_inv_transactions D JOIN #TEMP_deleted_rows T ON D.fk_tenant_id = T.fk_tenant_id AND D.fk_inv_action_id = T.fk_inv_action_id AND D.pk_id = T.fk_detail_id

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

PRINT 'Delete from tfp_hq_financing_mapping'

DELETE D FROM tfp_hq_financing_mapping D JOIN #TEMP_deleted_rows T ON T.fk_tenant_id = D.fk_tenant_id AND T.fk_inv_action_id = D.fk_inv_action_id AND T.fk_detail_id = D.fk_detail_id

IF @@ERROR <> 0
   BEGIN
        ROLLBACK
		  RAISERROR ('Error occured. Rolling back', 16, 1)
        RETURN
   END

COMMIT


RETURN 0
