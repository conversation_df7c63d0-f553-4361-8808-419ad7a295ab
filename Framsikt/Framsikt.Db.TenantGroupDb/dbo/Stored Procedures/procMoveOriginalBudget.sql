CREATE OR ALTER PROCEDURE [dbo].[procMoveOriginalBudget]
@tenant_id INT,
@budget_year INT,
@jodId INT,
@batchType VARCHAR(50),
@userId INT,
@changeId INT
AS
BEGIN

 --Delete existing rows
 DELETE FROM tfp_stage_newyearactions WHERE budget_year = @budget_year and fk_tenant_id=@tenant_id  and batch_type=@batchType

 --Insert new rows
INSERT INTO tfp_stage_newyearactions(batch_id,
									 new_fk_action_id,
									 batch_type,
									 fk_tenant_id,
									 budget_year,
									 previous_pk_action_id,
									 action_name,
									 consequence,
									 action_type,
									 action_source,
									 line_order,
									 isManuallyAdded,
									 tags,
									 long_description,
									 priority,
									 financial_plan_description,
									 fk_account_code,
									 department_code,
									 function_code,
									 project_code,
									 fk_investment_id,
									 investment_row_id,
									 fk_change_id,
									 free_dim_1,
									 free_dim_2,
									 free_dim_3,
									 free_dim_4,
									 fk_adjustment_code,
									 fk_alter_code,
									 year_1_amount,
									 year_2_amount,
									 year_3_amount,
									 year_4_amount,
									 year_5_amount,
									 year_6_amount,
									 year_7_amount,
									 year_8_amount,
									 year_9_amount,
									 year_10_amount,
									 user_id,
									 updated,
									 updated_by
								 )

						(SELECT	@jodId,
								0,
								@batchType,
								fk_tenant_id,
								@budget_year,
								0,
								'',
								'',
								5 [action_type],
								0,
								0 [line_order],
								0,
								'',
								'',
								0,
								'',
								fk_account_code,
								department_code,
								fk_function_code,
								fk_project_code,
								0,
								0,
								@changeId,
								free_dim_1,
								free_dim_2,
								free_dim_3,
								free_dim_4,
								'',
								'',
								SUM(amount_year_1) [year_1_amount],
								SUM(amount_year_1) [year_2_amount],
								SUM(amount_year_1)[year_3_amount],
								SUM(amount_year_1)[year_4_amount],
								SUM(amount_year_1)[year_5_amount],
								SUM(amount_year_1)[year_6_amount],
								SUM(amount_year_1)[year_7_amount],
								SUM(amount_year_1)[year_8_amount],
								SUM(amount_year_1)[year_9_amount],
								SUM(amount_year_1)[year_10_amount],
								@userId,
								getdate() updated,
								@userId [updated_by]
						 FROM tbu_trans_detail_original tbu 
						 WHERE tbu.budget_year = (@budget_year-1) and tbu.fk_tenant_id=@tenant_id 
						 GROUP BY [fk_tenant_id],[budget_year], [fk_account_code] ,[department_code],[fk_function_code],[fk_project_code],[free_dim_1] ,[free_dim_2] ,[free_dim_3],[free_dim_4])
END
