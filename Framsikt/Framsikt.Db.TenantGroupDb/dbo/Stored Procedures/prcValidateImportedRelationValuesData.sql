CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedRelationValuesData]
	@tenant_id int,
	@user_id int,
	@jobID bigint
	
AS

	   -- clear all the error information--
	IF( @jobID = -1)
		BEGIN
			UPDATE tco_stage_relation_values
			SET
			attribute_value_id_error = 0,
			relation_value_from_error = 0,
			relation_value_to_error = 0,
			year_from_error = 0,
			year_to_error = 0,
			error_count = 0
			WHERE tco_stage_relation_values.fk_tenant_id = @tenant_id AND
			tco_stage_relation_values.user_id = @user_id;
		END
	ELSE
		BEGIN
			UPDATE tco_stage_relation_values
			SET
			attribute_value_id_error = 0,
			relation_value_from_error = 0,
			relation_value_to_error = 0,
			year_from_error = 0,
			year_to_error = 0,
			error_count = 0
			WHERE tco_stage_relation_values.fk_tenant_id = @tenant_id AND
			tco_stage_relation_values.job_id = @jobId;
		END
		
	IF(@jobID = -1)
	  BEGIN
		-- Validate attribute_value_id --
				UPDATE tco_stage_relation_values
				SET    attribute_value_id_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					   (attribute_value_id IS NULL OR attribute_value_id = '' OR
					   attribute_value_id NOT IN
					   (SELECT pk_attribute_id FROM tco_attribute_values WHERE fk_tenant_id = @tenant_id));

		--Validate relation_value_from --
				UPDATE tco_stage_relation_values
				SET    relation_value_from_error = 1, 
					   error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					   (relation_value_from IS NULL OR relation_value_from = '' 
					   OR ISNUMERIC(relation_value_from) = 0
					   OR relation_value_from NOT IN (SELECT pk_department_code FROM tco_departments WHERE fk_tenant_id = @tenant_id));	   

		--Validate relation_value_to--
				UPDATE tco_stage_relation_values
				SET    relation_value_to_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					   (relation_value_to IS NULL OR relation_value_to = '' 
					   OR ISNUMERIC(relation_value_to) = 0
					   OR relation_value_to NOT IN
					   (SELECT pk_department_code FROM tco_departments WHERE fk_tenant_id = @tenant_id));

		----Validate relation_value_from should be less then relation_value_to --
				UPDATE tco_stage_relation_values
				SET    relation_value_from_error = 1, 
					   relation_value_to_error = 1, 
					   error_count = error_count + 2
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					   relation_value_from_error = 0 AND
					   ISNUMERIC(relation_value_to) = 1 AND ISNUMERIC(relation_value_to) = 1 AND
					   CAST(relation_value_from AS INT) > CAST(relation_value_to AS INT);

		--Validate year_from --
				UPDATE tco_stage_relation_values
				SET    year_from_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					   (year_from IS NULL OR year_from ='' OR LEN(year_from) < 4 OR LEN(year_from) > 4 OR  ISNUMERIC(year_from) = 0);

		--Validate year_to --
				UPDATE tco_stage_relation_values
				SET    year_to_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
						user_id = @user_id AND 
						(year_to IS NULL OR year_to ='' OR LEN(year_to) < 4 OR LEN(year_to) > 4 OR  ISNUMERIC(year_to) = 0);
	
		--Validate year_from should be less then year_to --
				UPDATE tco_stage_relation_values
				SET    year_from_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					   year_from_error = 0 AND
					   ISNUMERIC(year_to) = 1 AND
					   CAST(year_from AS INT) > CAST(year_to AS INT);

		--Validate year_to should be greater then year_from --
				UPDATE tco_stage_relation_values
				SET    year_to_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   user_id = @user_id AND 
					    year_to_error = 0 AND
					   ISNUMERIC(year_from) = 1 AND
					   CAST(year_from AS INT) > CAST(year_to AS INT);

		--Validate relation_value_from --

				--Validate relation_value_from with duplicate and overlapping record in the stage table--
				UPDATE sr 
				SET    sr.relation_value_from_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id AND sr.user_id = st.user_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.user_id = @user_id AND
					   sr.relation_value_from_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
		
				--Validate relation_value_from with duplicate and overlapping record with main table--
				UPDATE st
				SET    st.relation_value_from_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.user_id = @user_id AND 
					   st.relation_value_from_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_from AS INT) != CAST(tr.relation_value_from AS INT)) AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
			   
			   

		--Validate relation_value_to --
		
				--Validate relation_value_to with duplicate and overlapping record in the stage table --
				UPDATE sr
				SET    sr.relation_value_to_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id AND sr.user_id = st.user_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.user_id = @user_id AND
					   sr.relation_value_to_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
			   
				--Validate relation_value_to with duplicate and overlapping record with main table--
				UPDATE st
				SET    st.relation_value_to_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.user_id = @user_id AND 
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_to AS INT) !=  CAST(tr.relation_value_to AS INT)) AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    

		--Validate year_from --
				
				--Validate for year_from with relation_value_from for duplicate and overlapping record in the stage table--
				UPDATE sr 
				SET    sr.year_from_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.user_id = st.user_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.user_id = @user_id AND 
					   sr.relation_value_from_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
		
				--Validate for year_from with relation_value_from for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_from_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.user_id = @user_id AND 
					   st.relation_value_from_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_from AS INT) !=  CAST(tr.relation_value_from AS INT)) AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
				--Validate for year_from with relation_value_to for duplicate and overlapping record in the stage table --
				UPDATE sr
				SET    sr.year_from_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.user_id = st.user_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.user_id = @user_id AND 
					   sr.relation_value_to_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
			   
			   
				--Validate for year_from with relation_value_to for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_from_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.user_id = @user_id AND 
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_to AS INT) !=  CAST(tr.relation_value_to AS INT)) AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    

		--Validate year_to --
				
				--Validate for year_to with relation_value_from for duplicate and overlapping record in the stage table--
				UPDATE sr 
				SET    sr.year_to_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.user_id = st.user_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.user_id = @user_id AND 
					   sr.relation_value_from_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
		
				--Validate for year_to with relation_value_from for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_to_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.user_id = @user_id AND 
					   st.relation_value_from_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_from AS INT) !=  CAST(tr.relation_value_from AS INT)) AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
				--Validate for year_to with relation_value_to for duplicate and overlapping record in the stage table--
				UPDATE sr
				SET    sr.year_to_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.user_id = st.user_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.user_id = @user_id AND 
					   sr.relation_value_to_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
			   
			   
				--Validate for year_to with relation_value_to for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_to_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.user_id = @user_id AND 
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_to AS INT) !=  CAST(tr.relation_value_to AS INT)) AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    

	  END
	ELSE
	   BEGIN
				-- Validate attribute_value_id --
				UPDATE tco_stage_relation_values
				SET    attribute_value_id_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   (attribute_value_id IS NULL OR attribute_value_id = '' OR
					   attribute_value_id NOT IN
					   (SELECT pk_attribute_id FROM tco_attribute_values WHERE fk_tenant_id = @tenant_id));

		--Validate relation_value_from --
				UPDATE tco_stage_relation_values
				SET    relation_value_from_error = 1, 
					   error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   (relation_value_from IS NULL OR relation_value_from = '' 
					   OR ISNUMERIC(relation_value_from) = 0
					   OR relation_value_from NOT IN (SELECT pk_department_code FROM tco_departments WHERE fk_tenant_id = @tenant_id));	   

		--Validate relation_value_to--
				UPDATE tco_stage_relation_values
				SET    relation_value_to_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   (relation_value_to IS NULL OR relation_value_to = '' 
					   OR ISNUMERIC(relation_value_to) = 0
					   OR relation_value_to NOT IN
					   (SELECT pk_department_code FROM tco_departments WHERE fk_tenant_id = @tenant_id));

		----Validate relation_value_from should be less then relation_value_to --
				UPDATE tco_stage_relation_values
				SET    relation_value_from_error = 1, 
					   error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   relation_value_from_error = 0 AND
					   ISNUMERIC(relation_value_to) = 1 AND
					   CAST(relation_value_from AS INT) > CAST(relation_value_to AS INT);
			    
		----Validate relation_value_to should be greater then relation_value_from --
				UPDATE tco_stage_relation_values
				SET    relation_value_to_error = 1, 
					   error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   ISNUMERIC(relation_value_from) = 1 AND
					   relation_value_to_error = 0 AND
					   CAST(relation_value_from AS INT) > CAST(relation_value_to AS INT);

		--Validate year_from --
				UPDATE tco_stage_relation_values
				SET    year_from_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   (year_from IS NULL OR year_from ='' OR LEN(year_from) < 4 OR LEN(year_from) > 4 OR  ISNUMERIC(year_from) = 0);

		--Validate year_to --
				UPDATE tco_stage_relation_values
				SET    year_to_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
						job_id = @jobID AND 
						(year_to IS NULL OR year_to ='' OR LEN(year_to) < 4 OR LEN(year_to) > 4 OR  ISNUMERIC(year_to) = 0);
	
		--Validate year_from should be less then year_to --
				UPDATE tco_stage_relation_values
				SET    year_from_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					   year_from_error = 0 AND
					   ISNUMERIC(year_to) = 1 AND
					   CAST(year_from AS INT) > CAST(year_to AS INT);

		--Validate year_to should be greater then year_from --
				UPDATE tco_stage_relation_values
				SET    year_to_error = 1, error_count = error_count + 1
				FROM   tco_stage_relation_values 
				WHERE  fk_tenant_id = @tenant_id AND
					   job_id = @jobID AND 
					    year_to_error = 0 AND
					   ISNUMERIC(year_from) = 1 AND
					   CAST(year_from AS INT) > CAST(year_to AS INT);


		--Validate relation_value_from --

				--Validate relation_value_from with duplicate and overlapping record in the stage table--
				UPDATE sr 
				SET    sr.relation_value_from_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id AND sr.job_id = st.job_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.job_id = @jobID AND
					   sr.relation_value_from_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
		
				--Validate relation_value_from with duplicate and overlapping record with main table--
				UPDATE st
				SET    st.relation_value_from_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.job_id = @jobID AND 
					   st.relation_value_from_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_from AS INT) != CAST(tr.relation_value_from AS INT)) AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
			   
			   

		--Validate relation_value_to --
		
				--Validate relation_value_to with duplicate and overlapping record in the stage table --
				UPDATE sr
				SET    sr.relation_value_to_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id AND sr.job_id = st.job_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.job_id = @jobID AND
					   sr.relation_value_to_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
			   
				--Validate relation_value_to with duplicate and overlapping record with main table--
				UPDATE st
				SET    st.relation_value_to_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.job_id = @jobID AND 
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_to AS INT) != CAST(tr.relation_value_to AS INT)) AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    

		--Validate year_from --
				
				--Validate for year_from with relation_value_from for duplicate and overlapping record in the stage table--
				UPDATE sr 
				SET    sr.year_from_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.job_id = st.job_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.job_id = @jobID AND 
					   sr.relation_value_from_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
		
				--Validate for year_from with relation_value_from for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_from_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.job_id = @jobID AND 
					   st.relation_value_from_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_from AS INT) !=  CAST(tr.relation_value_from AS INT)) AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
				--Validate for year_from with relation_value_to for duplicate and overlapping record in the stage table --
				UPDATE sr
				SET    sr.year_from_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.job_id = st.job_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.job_id = @jobID AND 
					   sr.relation_value_to_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
			   
			   
				--Validate for year_from with relation_value_to for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_from_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.job_id = @jobID AND 
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_to AS INT) !=  CAST(tr.relation_value_to AS INT)) AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    

		--Validate year_to --
				
				--Validate for year_to with relation_value_from for duplicate and overlapping record in the stage table--
				UPDATE sr 
				SET    sr.year_to_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.job_id = st.job_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.job_id = @jobID AND 
					   sr.relation_value_from_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_from AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
		
				--Validate for year_to with relation_value_from for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_to_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.job_id = @jobID AND 
					   st.relation_value_from_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_from AS INT) !=  CAST(tr.relation_value_from AS INT)) AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_from AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
				--Validate for year_to with relation_value_to for duplicate and overlapping record in the stage table--
				UPDATE sr
				SET    sr.year_to_error = 1, 
					   sr.error_count = sr.error_count + 1
				FROM   tco_stage_relation_values sr JOIN
					   tco_stage_relation_values st ON sr.fk_tenant_id = st.fk_tenant_id and sr.job_id = st.job_id
				WHERE  sr.fk_tenant_id = @tenant_id AND
					   sr.job_id = @jobID AND 
					   sr.relation_value_to_error = 0 AND
					   sr.year_from_error = 0 AND
					   sr.year_to_error = 0 AND
					   st.relation_value_from_error = 0 AND
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   sr.pk_id != st.pk_id AND
					   ((sr.attribute_value_id = st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)))
					   OR (sr.attribute_value_id != st.attribute_value_id AND (CAST(sr.relation_value_to AS INT)  BETWEEN  CAST(st.relation_value_from AS INT) AND  CAST(st.relation_value_to AS INT)) AND (CAST(sr.year_from AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT)) AND (CAST(sr.year_to AS INT)  BETWEEN  CAST(st.year_from AS INT) AND  CAST(st.year_to AS INT))));
			   
			   
				--Validate for year_to with relation_value_to for duplicate and overlapping record with main table--
				UPDATE st
				SET    st.year_to_error = 1, 
					   st.error_count = st.error_count + 1
				FROM   tco_stage_relation_values st JOIN
					   tco_relation_values tr ON st.fk_tenant_id = tr.fk_tenant_id AND tr.relation_type = 'DEPARTMENTS'
				WHERE  st.fk_tenant_id = @tenant_id AND
					   st.job_id = @jobID AND 
					   st.relation_value_to_error = 0 AND
					   st.year_from_error = 0 AND
					   st.year_to_error = 0 AND
					   ISNUMERIC(tr.relation_value_from) = 1 AND
					   ISNUMERIC(tr.relation_value_to) = 1 AND
					   ((st.attribute_value_id = tr.attribute_value AND (CAST(st.relation_value_to AS INT) !=  CAST(tr.relation_value_to AS INT)) AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)))
					   OR (st.attribute_value_id != tr.attribute_value AND (CAST(st.relation_value_to AS INT)  BETWEEN  CAST(tr.relation_value_from AS INT) AND  CAST(tr.relation_value_to AS INT)) AND (CAST(st.year_from AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT)) AND (CAST(st.year_to AS INT)  BETWEEN  CAST(tr.year_from AS INT) AND  CAST(tr.year_to AS INT))));
			    
		END
