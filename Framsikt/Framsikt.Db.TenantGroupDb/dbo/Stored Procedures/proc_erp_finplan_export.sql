CREATE OR ALTER PROCEDURE  [dbo].[proc_erp_finplan_export]
@fk_tenant_id INT,
@budget_year INT,
@user_id INT,
@export_id INT OUTPUT
AS

--This conversion to local parameters is done for performance reasons... 
DECLARE @local_tenant_id INT
DECLARE @local_budget_year INT 

SET @local_tenant_id = @fk_tenant_id
SET @local_budget_year = @budget_year

DROP TABLE IF EXISTS #account_list
 
--Fetch accounts to be included
SELECT b.pk_tenant_id, b.pk_account_code 
INTO #account_list
FROM tco_accounts b
JOIN gco_kostra_accounts c on b.fk_kostra_account_code = c.pk_kostra_account_code and c.type = 'investment'
WHERE @local_budget_year between DATEPART(YEAR,b.dateFrom) and DATEPART(YEAR,b.dateTo)
AND b.pk_tenant_id = @local_tenant_id
GROUP BY b.pk_tenant_id, b.pk_account_code

--DECLARE @fk_tenant_id INT = 31
--DECLARE @budget_year INT = 2021
--DECLARE @user_id INT = 1026
DECLARE @previous_export_id INT
DECLARE @new_export_id INT


SET @previous_export_id = 
(
select MAX(pk_export_id) from [dbo].[tfp_erp_finplan_exports]
WHERE fk_tenant_id = @fk_tenant_id
and budget_year = @budget_year
and is_delta = 1
)

SET @new_export_id = (select ISNULL(MAX(pk_export_id)+1,1) from [dbo].[tfp_erp_finplan_exports])
SET @export_id = @new_export_id

INSERT INTO [dbo].[tfp_erp_finplan_exports]
           ([pk_export_id]
           ,[fk_tenant_id]
           ,[budget_Year]
           ,[export_time]
           ,[exported_by]
           ,[is_delta])
SELECT		[pk_export_id] = @new_export_id
           ,[fk_tenant_id] = @fk_tenant_id
           ,[budget_Year] = @budget_year
           ,[export_time] = GETDATE()
           ,[exported_by] = @user_id
           ,[is_delta] = 1


--Insert new snapshot into log table
INSERT INTO [dbo].[tfp_erp_finplan_export_log]

SELECT 
	   fk_export_id = @new_export_id
	  ,fk_tenant_id = td.[fk_tenant_id]
      ,budget_year = td.[budget_year]
	  ,pk_action_id
	  ,action_name = th.description
      ,[fk_account_code]
      ,fk_department_code = [department_code]
      ,fk_function_code = [function_code]
      ,fk_project_code = [project_code]
	  ,[free_dim_1]
      ,[free_dim_2]
      ,[free_dim_3]
      ,[free_dim_4]
      ,amount = SUM([year_1_amount])
      ,[description] = RIGHT(ISNULL(td.description,''),250)
      ,[fk_adjustment_code]
      ,[fk_alter_code]
	  ,td.fk_adj_code
FROM [dbo].[tfp_trans_detail] td
JOIN tfp_trans_header th ON td.fk_tenant_id = th.fk_tenant_id and td.fk_action_id = th.pk_action_id
LEFT JOIN tco_user_adjustment_codes adj on td.fk_tenant_id = adj.fk_tenant_id and td.budget_year = adj.budget_year and td.fk_adj_code = adj.pk_adj_code
where td.fk_tenant_id = @fk_tenant_id
and td.budget_year = @budget_year
and (adj.status is NULL or adj.status = 1)
GROUP BY 
td.[fk_tenant_id]
,td.[budget_year]
,pk_action_id
,th.description
,[fk_account_code]
,[department_code]
,[function_code]
,[project_code]
,[free_dim_1]
,[free_dim_2]
,[free_dim_3]
,[free_dim_4]
,td.description
,[fk_adjustment_code]
,[fk_alter_code]
,td.fk_adj_code


--Fetch investment budget
INSERT INTO [dbo].[tfp_erp_finplan_export_log]

SELECT
 fk_export_id = @new_export_id
,fk_tenant_id
,[budget_year]
,pk_action_id = 0
,action_name = 'Investering'
,[fk_account_code]
,fk_department_code = [department_code]
,fk_function_code 
,fk_project_code
,[free_dim_1]
,[free_dim_2]
,[free_dim_3]
,[free_dim_4]
,amount = SUM(new_model_inv)
,[description] = ISNULL(description,'')
,[fk_adjustment_code]
,[fk_alter_code]
,fk_adj_code = fk_user_adjustment_code
FROM
(
select PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, SUM(pt.amount) as new_model_inv
, PT.description
, @local_budget_year as budget_year
--, fk_adjustment_code = CONVERT(VARCHAR(4),@local_budget_year)+'VBUD'
, PT.fk_adjustment_code -- Oslo må ha med justeringskoden som ligger på inv.budsjettet
, fk_alter_code = ''
, pt.fk_user_adjustment_code
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @local_budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @local_budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN #account_list ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @local_tenant_id
		and budget_year < @local_budget_year
		UNION
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @local_tenant_id
		and budget_year = @local_budget_year
		and org_budget_flag = 1
)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
AND year = @local_budget_year
AND pt.fk_tenant_id = @local_tenant_id
GROUP BY PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, PT.description, /*PT.fk_user_adjustment_code,*/ PT.fk_adjustment_code, PT.fk_alter_code, pt.fk_user_adjustment_code
HAVING ABS(SUM(amount))>0

UNION ALL

select PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code as department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, SUM(pt.amount) as new_model_inv
, PT.description
, @local_budget_year as budget_year
--, fk_adjustment_code = pt.fk_user_adjustment_code
, PT.fk_adjustment_code -- Oslo må ha med justeringskoden som ligger på inv.budsjettet
, PT.fk_alter_code
,pt.fk_user_adjustment_code
from tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @local_budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @local_budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
JOIN #account_list ac ON PT.fk_tenant_id = ac.pk_tenant_id AND PT.fk_account_code = ac.pk_account_code
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
		select fk_tenant_id, pk_change_id from tfp_budget_changes
		where fk_tenant_id = @local_tenant_id
		and budget_year = @local_budget_year
		and org_budget_flag = 0
)  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code and UAD.status = 1
where (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
AND year = @local_budget_year
AND pt.fk_tenant_id = @local_tenant_id
GROUP BY PT.fk_tenant_id,pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4
, PT.description, /*PT.fk_user_adjustment_code,*/ PT.fk_adjustment_code, PT.fk_alter_code, pt.fk_user_adjustment_code
HAVING ABS(SUM(amount))>0
) INV_new
GROUP BY fk_tenant_id,[budget_year],[fk_account_code],[department_code],fk_function_code ,fk_project_code,[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[description],[fk_adjustment_code],[fk_alter_code], fk_user_adjustment_code


--Calculate change amount
select previous_export_id = @previous_export_id
	  ,new_export_id = @new_export_id
	  ,TRANS.fk_tenant_id 
      ,TRANS.[budget_year]
	  ,pk_action_id
	  ,action_name
      ,[fk_account_code]
      ,fk_department_code
      ,fk_function_code 
      ,fk_project_code
	  ,[free_dim_1]
      ,[free_dim_2]
      ,[free_dim_3]
      ,[free_dim_4]
      ,TRANS.[description] 
      ,[fk_adjustment_code]
      ,[fk_alter_code] 
	  ,change =  SUM(new_amount-old_amount)
	  ,fk_attribute_id = ISNULL(uad.fk_attribute_id,'')
FROM
(
	SELECT fk_tenant_id 
		  ,[budget_year]
		  ,pk_action_id
		  ,action_name
		  ,[fk_account_code]
		  ,fk_department_code
		  ,fk_function_code 
		  ,fk_project_code
		  ,[free_dim_1]
		  ,[free_dim_2]
		  ,[free_dim_3]
		  ,[free_dim_4]
		  ,[description] 
		  ,[fk_adjustment_code]
		  ,[fk_alter_code] 
		  ,old_amount =  amount
		  ,new_amount = 0
		  ,fk_adj_code
	FROM [dbo].[tfp_erp_finplan_export_log]
	where fk_export_id = @previous_export_id

	UNION ALL

	SELECT fk_tenant_id 
		  ,[budget_year]
		  ,pk_action_id
		  ,action_name
		  ,[fk_account_code]
		  ,fk_department_code
		  ,fk_function_code 
		  ,fk_project_code
		  ,[free_dim_1]
		  ,[free_dim_2]
		  ,[free_dim_3]
		  ,[free_dim_4]
		  ,[description] 
		  ,[fk_adjustment_code]
		  ,[fk_alter_code] 
		  ,old_amount = 0
		  ,new_amount = amount
		  ,fk_adj_code
	FROM [dbo].[tfp_erp_finplan_export_log]
	where fk_export_id = @new_export_id
) TRANS
LEFT JOIN tco_user_adjustment_codes uad ON TRANS.fk_adj_code = uad.pk_adj_code and TRANS.fk_tenant_id = uad.fk_tenant_id
GROUP BY TRANS.fk_tenant_id 
      ,TRANS.[budget_year]
	  ,pk_action_id
	  ,action_name
      ,[fk_account_code]
      ,fk_department_code
      ,fk_function_code 
      ,fk_project_code
	  ,[free_dim_1]
      ,[free_dim_2]
      ,[free_dim_3]
      ,[free_dim_4]
      ,TRANS.[description] 
      ,[fk_adjustment_code]
      ,[fk_alter_code]
	  ,fk_adj_code
	  ,uad.fk_attribute_id
HAVING  SUM(new_amount-old_amount) != 0
