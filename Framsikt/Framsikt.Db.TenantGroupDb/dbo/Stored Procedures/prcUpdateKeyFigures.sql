CREATE OR ALTER PROCEDURE [dbo].[prcUpdateKeyFigures]
             @tenant_id int = 0,
             @budget_year int,
             @indicator_fund nvarchar(25) = '',
             @phase_id uniqueidentifier = '00000000-0000-0000-0000-000000000000'

AS


DECLARE @ind_net_debt varchar(24) = (select fk_indicator_code from tmd_kpi_setup where fk_tenant_id = @tenant_id and fk_language_id = 'cmn_debt_per_inhabitan')
DECLARE @ib_value_net_debt dec(18,2) 
DECLARE @ib_value_indicator_fund dec(18,2) 
DECLARE @phase_sort_order INT 
DECLARE @default_change_id INT

DECLARE @tenant_type INT 
DECLARE @indicator_type INT

DECLARE @forecasttype VARCHAR(25)

SET @forecasttype = (select MIN(param_value) from tco_parameters WHERE param_name = 'KPI_POP_FORECAST_TYPE' AND fk_tenant_id = @tenant_id AND active = 1)

IF @forecasttype IS NULL
begin
SET @forecasttype = 'MMMM'
end


SET @tenant_type = (SELECT tenant_type_id FROM gco_tenants WHERE pk_id = @tenant_id)

SET @indicator_type = (SELECT MAX(flag_key_id) FROM tco_application_flag WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year AND flag_name =  'KPI_Dataset')

IF @indicator_type IS NULL
BEGIN
	SET @indicator_type = 1
END

IF @indicator_type = 1 AND @tenant_type = 1
BEGIN

SET @ind_net_debt = (SELECT indicator_kasse FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_kasse FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type = 2 AND @tenant_type = 1
BEGIN

SET @ind_net_debt = (SELECT indicator_konsern FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsern FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type IN (3,4) AND @tenant_type = 1
BEGIN

SET @ind_net_debt = (SELECT indicator_konsolidert FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsolidert FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type = 1 AND @tenant_type = 2
BEGIN

SET @ind_net_debt = (SELECT indicator_kasse_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_kasse_fylke FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type = 2 AND @tenant_type = 2
BEGIN

SET @ind_net_debt = (SELECT indicator_konsern_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsern_fylke FROM gmd_kpi_setup where kpi_id = 7)

END

IF @indicator_type IN (3,4) AND @tenant_type = 2
BEGIN

SET @ind_net_debt = (SELECT indicator_konsolidert_fylke FROM gmd_kpi_setup s WHERE s.kpi_id = 5)
SET @indicator_fund = (SELECT indicator2_konsolidert_fylke FROM gmd_kpi_setup where kpi_id = 7)

END

CREATE TABLE #hlp_net_debt (
fk_tenant_id INT NOT NULL,
fk_kostra_account_code varchar(24) NOT NULL,
ib_amount dec(18,2) NOT NULL,
year_minus1 dec(18,2) NOT NULL, 
year_1_amount dec(18,2) NOT NULL, 
year_2_amount dec(18,2) NOT NULL, 
year_3_amount dec(18,2) NOT NULL, 
year_4_amount dec(18,2) NOT NULL)
 
DECLARE @tab_b3  Table (
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
line_group_id INT NOT NULL,
line_group NVARCHAR(255) NOT NULL,
line_item_id INT NOT NULL,
line_item NVARCHAR(255) NOT NULL,
year_1_amount DECIMAL(18,3) NOT NULL,
year_2_amount DECIMAL(18,3) NOT NULL,
year_3_amount DECIMAL(18,3) NOT NULL,
year_4_amount DECIMAL(18,3) NOT NULL
)
 
DECLARE @tab_b2A  Table (
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
line_group_id INT NOT NULL,
line_group NVARCHAR(255) NOT NULL,
line_item_id INT NOT NULL,
line_item NVARCHAR(255) NOT NULL,
fk_program_code NVARCHAR(25) NULL,
year_1_amount DECIMAL(18,3) NOT NULL,
year_2_amount DECIMAL(18,3) NOT NULL,
year_3_amount DECIMAL(18,3) NOT NULL,
year_4_amount DECIMAL(18,3) NOT NULL
)
 
DECLARE @temp_key_figures Table (
    [type] INT NOT NULL,
             [category] NVARCHAR(25) NOT NULL,
    [fk_tenant_id] INT NOT NULL,
             [budget_year] INT NOT NULL,
             [fp_level_1_value] NVARCHAR(25) NOT NULL,
             [fp_level_2_value] NVARCHAR(25) NOT NULL,
    [key_name] NVARCHAR(150) NOT NULL,
    [key_description] NVARCHAR(500) NOT NULL,
    [value_type] NVARCHAR(25) NOT NULL,
    [year_1_value] DECIMAL(18, 3) NOT NULL,
             [year_2_value] DECIMAL(18, 3) NOT NULL,
             [year_3_value] DECIMAL(18, 3) NOT NULL,
    [year_4_value] DECIMAL(18, 3) NOT NULL,
             [divide_by] INT NOT NULL,
             [postfix] NVARCHAR(25),
    [active] BIT NOT NULL,
    [is_editable] BIT NOT NULL,
    [sort_order] INT NOT NULL)
 

 
DECLARE @tenant_table Table (
fk_tenant_id INT NOT NULL,
fk_prog_code NVARCHAR(25) NOT NULL)


DECLARE @change_id  Table (
fk_tenant_id INT NOT NULL,
pk_change_id INT NOT NULL)



 
IF @tenant_id != 0
BEGIN
INSERT INTO @tenant_table (fk_tenant_id, fk_prog_code)
VALUES (@tenant_id,(SELECT MIN(pk_prog_code) FROM tco_inv_program WHERE default_flag = 1 and fk_tenant_id = @tenant_id))
END
 
IF @tenant_id = 0
BEGIN
 
INSERT INTO @tenant_table (fk_tenant_id, fk_prog_code)
SELECT DISTINCT a.fk_tenant_id,  ISNULL(MIN(pk_prog_code),'1')
FROM tco_module_mapping a
left join tco_inv_program b on b.default_flag = 1 and b.fk_tenant_id = a.fk_tenant_id
WHERE a.fk_module_id = 3
GROUP BY a.fk_tenant_id
 
END


IF @phase_id = '00000000-0000-0000-0000-000000000000' or @phase_id is null
BEGIN 

SET @phase_id =
(SELECT param_value FROM tco_parameters WHERE param_name = 'BM_DOCVERSION_KEYFIG' AND fk_tenant_id = 31 AND active = 1
AND TRY_CONVERT (uniqueidentifier, param_value) IS NOT NULL
)
END

IF @phase_id = '00000000-0000-0000-0000-000000000000' or @phase_id is null
BEGIN 

SET @phase_id = (
SELECT top 1 pk_budget_phase_id FROM tco_budget_phase
WHERE fk_tenant_id = @tenant_id AND org_budget_flag = 1
order by sort_order desc)

END

SET @phase_sort_order = (SELECT sort_order FROM tco_budget_phase
WHERE fk_tenant_id = @tenant_id AND org_budget_flag = 1 AND pk_budget_phase_id = @phase_id)


INSERT INTO @change_id (fk_tenant_id, pk_change_id)
SELECT ch.fk_tenant_id, ch.pk_change_id
FROM tfp_budget_changes ch 
WHERE ch.fk_tenant_id = @tenant_id AND ch.budget_year = @budget_year AND org_budget_flag = 1 AND fk_budget_phase_id IN (
SELECT pk_budget_phase_id FROM tco_budget_phase
WHERE fk_tenant_id = @tenant_id AND org_budget_flag = 1 AND sort_order <= @phase_sort_order)



BEGIN 


select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,ISNULL(pt.fk_change_id,0) AS fk_change_id, 
pt.fk_alter_code, pt.fk_adjustment_code,isnull(p.fk_prog_code,'1') AS fk_prog_code,
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function, 
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,
adjustment_code_status = CASE    
WHEN UAD.status = 1 THEN 1
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 1
ELSE 0
END
INTO #inv_hlptab
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year from tfp_budget_changes
        where fk_tenant_id = @tenant_id
        and budget_year < @budget_year
        UNION
        select fk_tenant_id, pk_change_id, org_budget_flag, budget_year from tfp_budget_changes
        where fk_tenant_id = @tenant_id
        and budget_year = @budget_year
    )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
where PT.fk_tenant_id = @tenant_id
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,
UAD.status, UAD.include_in_calculation,BC.budget_year

DELETE FROM #inv_hlptab WHERE adjustment_code_status = 0

	SET @default_change_id = (
	SELECT top 1 pk_change_id FROM tfp_budget_changes ch
	JOIN tco_budget_phase ph ON ch.fk_tenant_id = ph.fk_tenant_id AND ch.fk_budget_phase_id = ph.pk_budget_phase_id
	WHERE ch.fk_tenant_id = @tenant_id AND ch.budget_year = @budget_year AND ch.org_budget_flag = 1
	ORDER BY ph.sort_order, ch.pk_change_id)

    if @default_change_id is null
    begin
    set @default_change_id = 0
    end


	UPDATE a SET a.fk_change_id = @default_change_id
	FROM #inv_hlptab a
	JOIN tfp_budget_changes ch ON ch.fk_tenant_id = a.fk_tenant_id AND a.fk_change_id = ch.pk_change_id and ch.budget_year < @budget_year
	; 

select PT.fk_tenant_id, PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name,@default_change_id as fk_change_id, 
pt.fk_alter_code, pt.fk_adjustment_code,isnull(p.fk_prog_code,'1') AS fk_prog_code,
SUM(pt.amount) as amount, mp.fk_department_code as header_dept, ISNULL(mp.fk_function_code,'') as header_function, 
ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,
adjustment_code_status = CASE    
WHEN UAD.status = 1 THEN 1
WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND BC.budget_year < @budget_year THEN 1
ELSE 0
END
INTO #inv_hlptab_prev_year
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year-1 BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year-1 BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN tco_user_adjustment_codes UAD ON PT.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
JOIN ( --Fetch budget rounds to be included in finplan (no budget changes for current year)
        select fk_tenant_id, pk_change_id, 1 as org_budget_flag, budget_year from tfp_budget_changes
        where fk_tenant_id = @tenant_id
        and budget_year < @budget_year-1
        UNION
        select fk_tenant_id, pk_change_id, org_budget_flag, budget_year from tfp_budget_changes
        where fk_tenant_id = @tenant_id
        and budget_year = @budget_year-1
    )  BC ON PT.fk_tenant_id = BC.fk_tenant_id and PT.fk_change_id = BC.pk_change_id
where PT.fk_tenant_id = @tenant_id
GROUP BY PT.fk_tenant_id,PT.year, mp.pk_main_project_code, mp.main_project_name, mp.inv_status, mp.is_temp, mp.completion_date, 
pt.fk_account_code, pt.fk_department_code, pt.fk_function_code, pt.fk_project_code, p.project_name, pt.fk_change_id, pt.fk_alter_code, pt.fk_adjustment_code,
pt.year,p.fk_prog_code,mp.fk_department_code, mp.fk_function_code,ps.approval_reference, ps.approval_ref_url, ps.original_finish_year, bc.org_budget_flag,
UAD.status, UAD.include_in_calculation, BC.budget_year

DELETE FROM #inv_hlptab_prev_year WHERE adjustment_code_status = 0;

SELECT fk_tenant_id,@budget_year AS budget_year,pk_main_project_code,main_project_name,inv_status,
is_temp,completion_date,fk_account_code,fk_department_code,
fk_function_code,fk_project_code,project_name,fk_change_id,
fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept, header_function,approval_reference, 
approval_ref_url,original_finish_year,
org_bud_last_year = convert(dec(18,2), 0),
rev_bud_last_year = convert(dec(18,2), 0),
year_1_amount = CASE WHEN year = @budget_year AND org_budget_flag = 1 THEN amount else 0 end,
year_2_amount = CASE WHEN year = @budget_year+1 AND org_budget_flag = 1 THEN amount else 0 end,
year_3_amount = CASE WHEN year = @budget_year+2 AND org_budget_flag = 1 THEN amount else 0 end,
year_4_amount = CASE WHEN year = @budget_year+3 AND org_budget_flag = 1 THEN amount else 0 end,
year_5_amount = CASE WHEN year = @budget_year+4 AND org_budget_flag = 1 THEN amount else 0 end,
year_6_amount = CASE WHEN year = @budget_year+5 AND org_budget_flag = 1 THEN amount else 0 end,
year_7_amount = CASE WHEN year = @budget_year+6 AND org_budget_flag = 1 THEN amount else 0 end,
year_8_amount = CASE WHEN year = @budget_year+7 AND org_budget_flag = 1 THEN amount else 0 end,
year_9_amount = CASE WHEN year = @budget_year+8 AND org_budget_flag = 1 THEN amount else 0 end,
year_10_amount = CASE WHEN year = @budget_year+9 AND org_budget_flag = 1 THEN amount else 0 end,
net_cost = CASE WHEN inv_status = 2 OR year = -1 THEN 0 else amount END,
previously_budgeted = CASE WHEN year < @budget_year AND year != -1 THEN amount ELSE 0 END,
approved_cost = CASE WHEN year = -1 THEN amount else 0 end
INTO #inv_hlptab2
 FROM #inv_hlptab

 
 INSERT INTO #inv_hlptab2 (fk_tenant_id,budget_year,pk_main_project_code,main_project_name,inv_status,is_temp,
 completion_date,fk_account_code,fk_department_code,fk_function_code,fk_project_code,project_name,
 fk_change_id,fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept,header_function,
 approval_reference,approval_ref_url,original_finish_year,
 org_bud_last_year,rev_bud_last_year,
 year_1_amount,year_2_amount,year_3_amount,year_4_amount,year_5_amount,year_6_amount,
 year_7_amount,year_8_amount,year_9_amount,year_10_amount,net_cost,previously_budgeted,approved_cost)
SELECT fk_tenant_id,@budget_year AS budget_year,pk_main_project_code,main_project_name,inv_status,
is_temp,completion_date,fk_account_code,fk_department_code,
fk_function_code,fk_project_code,project_name,fk_change_id,
fk_alter_code,fk_adjustment_code,fk_prog_code,header_dept, header_function,approval_reference, 
approval_ref_url,original_finish_year,
org_bud_last_year = CASE WHEN year = @budget_year-1 AND org_budget_flag = 1 THEN amount else 0 end,
rev_bud_last_year = CASE WHEN year = @budget_year-1 THEN amount else 0 end,
year_1_amount = 0,
year_2_amount = 0,
year_3_amount = 0,
year_4_amount = 0,
year_5_amount = 0,
year_6_amount = 0,
year_7_amount = 0,
year_8_amount = 0,
year_9_amount = 0,
year_10_amount = 0,
net_cost = 0,
previously_budgeted = 0,
approved_cost = 0
FROM #inv_hlptab_prev_year


-- Remove b-list actions 

UPDATE #inv_hlptab2 SET org_bud_last_year = 0, rev_bud_last_year = 0,year_1_amount = 0, year_2_amount = 0, year_3_amount = 0, year_4_amount = 0, year_5_amount = 0,
year_6_amount = 0, year_7_amount = 0, year_8_amount = 0, year_9_amount = 0, year_10_amount = 0
WHERE inv_status IN (3,4,5)

END

RAISERROR ('FINISH: Fetch data from investments', 0, 1) WITH NOWAIT
PRINT convert(nvarchar(19),SYSDATETIME())


 
INSERT INTO @tab_b3 (fk_tenant_id, budget_year, line_group_id, line_group, line_item_id, line_item,
year_1_amount, year_2_amount, year_3_amount,year_4_amount)
SELECT h.fk_tenant_id, d.budget_year, l.line_group_id,l.line_group, l.line_item_id,l.line_item,
SUM(d.year_1_amount), SUM(d.year_2_amount),SUM(d.year_3_amount), SUM(d.year_4_amount)
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.fk_tenant_id = d.fk_tenant_id AND h.pk_action_id = d.fk_action_id
JOIN tco_accounts ac ON d.fk_account_code = ac.pk_account_code AND d.fk_tenant_id = ac.pk_tenant_id AND d.budget_year BETWEEN datepart(year, ac.datefrom) AND DATEPART (year, ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '54_OVDRIFT'
JOIN @tenant_table t ON h.fk_tenant_id = t.fk_tenant_id
JOIN @change_id ch ON d.fk_tenant_id = ch.fk_tenant_id AND d.fk_change_id = ch.pk_change_id
WHERE d.budget_year = @budget_year
GROUP BY h.fk_tenant_id, d.budget_year, l.line_group_id,l.line_group, l.line_item_id,l.line_item
 
 
INSERT INTO @tab_b2A (fk_tenant_id, budget_year, line_group_id, line_group, line_item_id, line_item,fk_program_code,
year_1_amount, year_2_amount, year_3_amount,year_4_amount)
SELECT t.fk_tenant_id, t.budget_year, l.line_group_id,l.line_group, l.line_item_id,l.line_item, t.fk_prog_code,
SUM(t.year_1_amount), SUM(t.year_2_amount),SUM(t.year_3_amount), SUM(t.year_4_amount)
FROM #inv_hlptab2 t
JOIN tco_accounts ac ON ac.pk_tenant_id = t.fk_tenant_id AND ac.pk_account_code = t.fk_account_code AND t.budget_year between datepart(year,ac.dateFrom) AND datepart(year,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV'
JOIN @tenant_table t1 ON t1.fk_tenant_id = t.fk_tenant_id
WHERE t.budget_year = @budget_year
GROUP BY  t.fk_tenant_id, t.budget_year, l.line_group_id,l.line_group, l.line_item_id,l.line_item, t.fk_prog_code

PRINT 'Prepare net debt calculation - used for kpi 13 below'

--Changed to user debt pr population indicator because the KPI graphs uses this and there are small differences in the indicators..
DECLARE @population dec(18,2)

SET @population = (
select
inhabitants = SUM(forecast)
from gco_pop_forecast a
JOIN gco_tenants b on a.fk_municipality_id = b.municipality_id
where a.forecast_type = @forecasttype
AND a.age_interval != 'Standard barn'
AND year = @budget_year-1
AND b.pk_id = @tenant_id)


SET @ib_value_net_debt =(

SELECT a.indicator_value*@population
FROM gko_kostra_data_corp a, gco_tenants b
WHERE  a.fk_region_code = b.municipality_id
AND a.fk_indicator_code = @ind_net_debt  
AND a.year = @budget_year-2
AND b.pk_id = @tenant_id)

IF @ib_value_net_debt IS NULL 
BEGIN 
SET @ib_value_net_debt = 0
END

INSERT INTO #hlp_net_debt (fk_tenant_id, fk_kostra_account_code,ib_amount, year_minus1, year_1_amount, year_2_amount, year_3_amount, year_4_amount)
VALUES  (@tenant_id, '',@ib_value_net_debt, 0, 0, 0, 0, 0)


INSERT INTO #hlp_net_debt (fk_tenant_id, fk_kostra_account_code,ib_amount, year_minus1, year_1_amount, year_2_amount, year_3_amount, year_4_amount)
SELECT a.fk_tenant_id, ac.fk_kostra_account_code,0 as ib_amount, sum(a.amount_year_1), 0 as year_1_amount, 0 as year_2_amount, 0 as year_3_amount, 0 as year_4_amount
FROM tbu_trans_detail a
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN datepart(year, ac.dateFrom) AND datepart ( year, ac.dateto)
JOIN tco_user_adjustment_codes c ON a.fk_tenant_id = c.fk_tenant_id and a.fk_adjustment_code = c.pk_adj_code and c.status = 1
WHERE a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year-1
AND ac.fk_kostra_account_code IN ('1510')
GROUP BY a.fk_tenant_id, ac.fk_kostra_account_code


INSERT INTO #hlp_net_debt (fk_tenant_id, fk_kostra_account_code,ib_amount, year_minus1, year_1_amount, year_2_amount, year_3_amount, year_4_amount)
SELECT a.fk_tenant_id, ac.fk_kostra_account_code,0 as ib_amount, 0, SUM(a.year_1_amount), SUM(a.year_2_amount),
SUM(a.year_3_amount), SUM(a.year_4_amount)
FROM tfp_trans_detail a
JOIN tfp_trans_header h ON a.fk_tenant_id = h.fk_tenant_id AND a.fk_action_id = h.pk_action_id
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN datepart(year, ac.dateFrom) AND datepart ( year, ac.dateto)
JOIN @change_id ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.fk_change_id = ch.pk_change_id
WHERE a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
AND ac.fk_kostra_account_code IN ('1510')
GROUP BY a.fk_tenant_id, ac.fk_kostra_account_code

Print 'Fetch investments net debt transactions'


INSERT INTO #hlp_net_debt (fk_tenant_id, fk_kostra_account_code,ib_amount, year_minus1, year_1_amount, year_2_amount, year_3_amount, year_4_amount)
SELECT a.fk_tenant_id, ac.fk_kostra_account_code,0 as ib_amount, SUM(a.rev_bud_last_year), SUM(a.year_1_amount), SUM(a.year_2_amount),
SUM(a.year_3_amount), SUM(a.year_4_amount)
FROM #inv_hlptab2 a
JOIN tco_accounts ac ON a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code AND a.budget_year BETWEEN datepart(year, ac.dateFrom) AND datepart ( year, ac.dateto)
WHERE a.fk_tenant_id = @tenant_id
AND ac.fk_kostra_account_code IN ('0910','0510')
GROUP BY a.fk_tenant_id, ac.fk_kostra_account_code

 
 
 
PRINT 'Find type 1 - total revenue'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP',1, fk_tenant_id, budget_year, '', '', 'Totale driftsinntekter', '', 'n0', ABS(SUM(year_1_amount)), ABS(SUM(year_2_amount)), ABS(SUM(year_3_amount)), ABS(SUM(year_4_amount)), 1000,'',1,0,1
FROM @tab_b3
WHERE line_group_id = 10
GROUP BY fk_tenant_id, budget_year
 
 
PRINT 'Find type 2 - total expence'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 2, fk_tenant_id, budget_year, '', '', 'Totale driftsutgifter', '', 'n0', SUM(year_1_amount), SUM(year_2_amount), SUM(year_3_amount), SUM(year_4_amount), 1000,'',1,0,2
FROM @tab_b3
WHERE line_group_id = 20
GROUP BY fk_tenant_id, budget_year
 
 
PRINT 'Find type 3 - net result'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 3, fk_tenant_id, budget_year, '', '', 'Netto driftsresultat', '', 'n0', SUM(year_1_amount), SUM(year_2_amount), SUM(year_3_amount), SUM(year_4_amount),1000,'', 1,0,3
FROM @tab_b3
WHERE line_group_id IN (10,20,30,40)
GROUP BY fk_tenant_id, budget_year
 
PRINT 'Find type 4 - net result per revenue in percentage'
 
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 4,A.fk_tenant_id, A.budget_year, '', '', 'Driftsresultat i prosent av driftsinntekter', '', '##0.0 \%',
A.year_1_value/B.year_1_value*100, A.year_2_value/B.year_2_value*100, A.year_3_value/B.year_3_value*100, A.year_4_value/B.year_4_value*100,1,'%',
1,0,4
FROM
(SELECT fk_tenant_id, budget_year, SUM(year_1_amount) year_1_value, SUM(year_2_amount) year_2_value, SUM(year_3_amount) year_3_value, SUM(year_4_amount) year_4_value
FROM @tab_b3
WHERE line_group_id IN (10,20,30,40)
GROUP BY fk_tenant_id, budget_year) A,
(SELECT fk_tenant_id, budget_year, SUM(year_1_amount) year_1_value, SUM(year_2_amount) year_2_value, SUM(year_3_amount) year_3_value, SUM(year_4_amount) year_4_value
FROM @tab_b3
WHERE line_group_id = 10
GROUP BY fk_tenant_id, budget_year) B
WHERE A.fk_tenant_id = B.fk_tenant_id AND A.budget_year = B.budget_year
 
 
PRINT 'Find type 5 - total inhabitants'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP',5, S1.fk_tenant_id,@budget_year,'','','Antall innbyggere','','n0', s1.forecast, S2.forecast,S3.forecast,S4.forecast,1, '',1,0,5
 
FROM
 
(SELECT c.fk_tenant_id, SUM(forecast) as forecast FROM gco_pop_forecast a, gco_tenants b, @tenant_table c
WHERE age_interval != 'Standard barn'
AND a.fk_municipality_id = b.municipality_id
AND a.forecast_type = b.forecast_type
AND a.year = @budget_year
AND b.pk_id= c.fk_tenant_id
GROUP BY c.fk_tenant_id) S1,
(SELECT c.fk_tenant_id, SUM(forecast) as forecast FROM gco_pop_forecast a, gco_tenants b, @tenant_table c
WHERE age_interval != 'Standard barn'
AND a.fk_municipality_id = b.municipality_id
AND a.forecast_type = b.forecast_type
AND a.year = @budget_year+1
AND b.pk_id= c.fk_tenant_id
GROUP BY c.fk_tenant_id) S2,
(SELECT c.fk_tenant_id, SUM(forecast) as forecast FROM gco_pop_forecast a, gco_tenants b, @tenant_table c
WHERE age_interval != 'Standard barn'
AND a.fk_municipality_id = b.municipality_id
AND a.forecast_type = b.forecast_type
AND a.year = @budget_year+2
AND b.pk_id= c.fk_tenant_id
GROUP BY c.fk_tenant_id) S3,
(SELECT c.fk_tenant_id, SUM(forecast) as forecast FROM gco_pop_forecast a, gco_tenants b, @tenant_table c
WHERE age_interval != 'Standard barn'
AND a.fk_municipality_id = b.municipality_id
AND a.forecast_type = b.forecast_type
AND a.year = @budget_year+3
AND b.pk_id= c.fk_tenant_id
GROUP BY c.fk_tenant_id) S4
WHERE S1.fk_tenant_id = S2.fk_tenant_id
AND S1.fk_tenant_id = S3.fk_tenant_id
AND S1.fk_tenant_id = S4.fk_tenant_id
 
 
 
 
 
 
 
PRINT 'Find type 6 - renteutgifter'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 6, fk_tenant_id, budget_year, '', '', 'Renteutgifter', '', 'n0', SUM(year_1_amount), SUM(year_2_amount), SUM(year_3_amount), SUM(year_4_amount),1000,'', 1,0,6
FROM @tab_b3
WHERE line_group_id = 30 AND line_item_id = 3040
GROUP BY fk_tenant_id, budget_year

PRINT 'Find type 7 - avdrag'

INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 7, fk_tenant_id, budget_year, '', '', 'Avdrag', '', 'n0', SUM(ABS(year_1_amount)), SUM(ABS(year_2_amount)), SUM(ABS(year_3_amount)), SUM(ABS(year_4_amount)),1000,'', 1,0,7
FROM @tab_b3
WHERE line_group_id = 30 AND line_item_id = 3050
GROUP BY fk_tenant_id, budget_year
 
PRINT 'Find type 8 - Eiendomsskatt'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 8, fk_tenant_id, budget_year, '', '', 'Eiendomsskatt', '', 'n0', SUM(ABS(year_1_amount)), SUM(ABS(year_2_amount)), SUM(ABS(year_3_amount)), SUM(ABS(year_4_amount)),1000,'', 1,0,8
FROM @tab_b3
WHERE line_group_id = 10 AND line_item_id = 1030
GROUP BY fk_tenant_id, budget_year
 
PRINT 'Find type 9 - Netto finans i % av brutto driftsinntekter'

 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 9,A.fk_tenant_id, A.budget_year, '', '', 'Netto finans og avdrag i % av brutto driftsinntekter', '', '##0.0 \%',
A.year_1_value/B.year_1_value*100*(-1), A.year_2_value/B.year_2_value*100*(-1), A.year_3_value/B.year_3_value*100*(-1), A.year_4_value/B.year_4_value*100*(-1),1,'%',
1,0,9
FROM
(SELECT fk_tenant_id, budget_year, SUM(year_1_amount) year_1_value, SUM(year_2_amount) year_2_value, SUM(year_3_amount) year_3_value, SUM(year_4_amount) year_4_value
FROM @tab_b3
WHERE line_group_id IN (30)
GROUP BY fk_tenant_id, budget_year) A,
(SELECT fk_tenant_id, budget_year, SUM(year_1_amount) year_1_value, SUM(year_2_amount) year_2_value, SUM(year_3_amount) year_3_value, SUM(year_4_amount) year_4_value
FROM @tab_b3
WHERE line_group_id = 10
GROUP BY fk_tenant_id, budget_year) B
WHERE A.fk_tenant_id = B.fk_tenant_id AND A.budget_year = B.budget_year

 
PRINT 'Find type 10 - Disposisjonsfond'


SET @ib_value_indicator_fund = (
SELECT a.indicator_value*1000
FROM gko_kostra_data_corp a, gco_tenants b
WHERE  a.fk_region_code = b.municipality_id
AND a.fk_indicator_code = @indicator_fund   
AND a.year = @budget_year-2
AND b.pk_id = @tenant_id)
+(
SELECT sum(amount_year_1) as amount_year_1
FROM tbu_trans_detail d, tco_accounts e
WHERE  e.fk_kostra_account_code IN ('1540', '1940')
AND d.budget_year = @budget_year-1
AND d.fk_tenant_id = e.pk_tenant_id
AND d.fk_account_code = e.pk_account_code
AND d.fk_tenant_id = @tenant_id
AND @budget_year BETWEEN DATEPART(YEAR, e.dateFrom) AND datepart(year, e.dateto))
--+
--(SELECT ISNULL(SUM(h.rev_bud_last_year),0) FROM #inv_hlptab2 h
--JOIN tco_accounts ac ON h.fk_tenant_id = ac.pk_tenant_id AND h.fk_account_code = ac.pk_account_code 
--AND @budget_year BETWEEN DATEPART(YEAR, ac.dateFrom) AND datepart(year, ac.dateto)
--WHERE ac.fk_kostra_account_code IN ('0540', '0940'))

IF @ib_value_indicator_fund IS NULL
BEGIN 
SET @ib_value_indicator_fund = 0
END


 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 10, S.fk_tenant_id, @budget_year, '', '', 'Disposisjonsfond','','n0',
year_1_amount = @ib_value_indicator_fund+ISNULL(SUM(S.year_1_amount),0), 
year_2_amount = @ib_value_indicator_fund+ISNULL(SUM(S.year_1_amount),0)+ISNULL(SUM(S.year_2_amount),0),
year_3_amount = @ib_value_indicator_fund+ISNULL(SUM(S.year_1_amount),0)+ISNULL(SUM(S.year_2_amount),0)+ISNULL(SUM(S.year_3_amount),0),
year_4_amount = @ib_value_indicator_fund+ISNULL(SUM(S.year_1_amount),0)+ISNULL(SUM(S.year_2_amount),0)+ISNULL(SUM(S.year_3_amount),0)+ISNULL(SUM(S.year_4_amount),0),
1000,'', 1,0,10
FROM 
(
--SELECT h.fk_tenant_id,
--year_1_amount = ISNULL(SUM(h.year_1_amount),0), 
--year_2_amount = ISNULL(SUM(h.year_2_amount),0),
--year_3_amount = ISNULL(SUM(h.year_3_amount),0),
--year_4_amount = ISNULL(SUM(h.year_4_amount),0)
--FROM #inv_hlptab2 h
--JOIN tco_accounts ac ON h.fk_tenant_id = ac.pk_tenant_id AND h.fk_account_code = ac.pk_account_code 
--AND @budget_year BETWEEN DATEPART(YEAR, ac.dateFrom) AND datepart(year, ac.dateto)
--AND h.budget_year = @budget_year
--WHERE ac.fk_kostra_account_code IN ('0540', '0940')
--GROUP BY h.fk_tenant_id
--UNION ALL
SELECT d.fk_tenant_id,
year_1_amount = sum(d.year_1_amount),
year_2_amount = sum(d.year_2_amount),
year_3_amount = sum(d.year_3_amount),
year_4_amount = sum(d.year_4_amount)
FROM tfp_trans_detail d, tco_accounts e, @change_id ch
WHERE  e.fk_kostra_account_code IN ('1540', '1940')
AND d.budget_year = @budget_year
AND d.fk_tenant_id = e.pk_tenant_id
AND d.fk_account_code = e.pk_account_code
AND d.fk_tenant_id = @tenant_id
AND @budget_year BETWEEN DATEPART(YEAR, e.dateFrom) AND datepart(year, e.dateto)
AND d.fk_tenant_id = ch.fk_tenant_id AND d.fk_change_id = ch.pk_change_id
GROUP BY d.fk_tenant_id
) S
GROUP BY S.fk_tenant_id
 
PRINT 'Find type 11 - Årlige investeringer'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'INV', 11, fk_tenant_id, budget_year, '', '', 'Årlige investeringer', '', 'n0', SUM(year_1_amount), SUM(year_2_amount), SUM(year_3_amount), SUM(year_4_amount), 1000,'',1,0,11
FROM @tab_b2A
WHERE line_group_id = 10
AND line_item_id = 1010
GROUP BY fk_tenant_id, budget_year
 
PRINT 'Find type 12 - Andel lånefinansiering'
 
INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],
[key_name],[key_description],[value_type],
[year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'INV', 12, A.fk_tenant_id, A.budget_year, '', '', 'Andel lånefinansiering', '','##0.0 \%',
year_1_value = CASE WHEN A.year_1_amount = 0 OR A.year_1_amount IS NULL THEN 0 ELSE B.year_1_amount/A.year_1_amount*100 END,
year_2_value = CASE WHEN A.year_2_amount = 0 OR A.year_2_amount IS NULL THEN 0 ELSE B.year_2_amount/A.year_2_amount*100 END,
year_3_value = CASE WHEN A.year_3_amount = 0 OR A.year_3_amount IS NULL THEN 0 ELSE B.year_3_amount/A.year_3_amount*100 END,
year_4_value = CASE WHEN A.year_4_amount = 0 OR A.year_4_amount IS NULL THEN 0 ELSE B.year_4_amount/A.year_4_amount*100 END,
1,'%',1,0,12
FROM
(SELECT fk_tenant_id, budget_year, SUM(year_1_amount) year_1_amount, SUM(year_2_amount) year_2_amount,
SUM(year_3_amount) year_3_amount, SUM(year_4_amount) year_4_amount
FROM @tab_b2A
WHERE line_group_id IN (20,40)
GROUP BY fk_tenant_id, budget_year
) A,
(SELECT fk_tenant_id, budget_year, SUM(year_1_amount) year_1_amount, SUM(year_2_amount) year_2_amount,
SUM(year_3_amount) year_3_amount, SUM(year_4_amount) year_4_amount
FROM @tab_b2A
WHERE line_group_id = 20
AND line_item_id = 2070
GROUP BY fk_tenant_id, budget_year) B
WHERE A.fk_tenant_id = B.fk_tenant_id AND A.budget_year = B.budget_year
 
 
PRINT 'Find type 13 - Netto lånegjeld'

INSERT INTO @temp_key_figures ([category],[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],[divide_by], postfix,active,[is_editable],[sort_order])
SELECT 'FP', 13, fk_tenant_id, @budget_year, '', '', 'Netto lånegjeld', '', 'n0',  
year_1_amount = SUM(ib_amount) -SUM(year_minus1) -SUM(year_1_amount), 
year_2_amount = SUM(ib_amount) -SUM(year_minus1) -SUM(year_1_amount)- SUM(year_2_amount), 
year_3_amount = SUM(ib_amount) -SUM(year_minus1) -SUM(year_1_amount)- SUM(year_2_amount) - SUM(year_3_amount),
year_4_amount = SUM(ib_amount) -SUM(year_minus1) -SUM(year_1_amount)- SUM(year_2_amount) - SUM(year_3_amount)-SUM(year_4_amount),
1000,'', 1,0,6
FROM #hlp_net_debt
GROUP BY fk_tenant_id
 
PRINT 'Find type 14 - Andel lån'
 
 
print 'Insert new key figures'
 
INSERT INTO tco_key_figures ([category],type, [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value], divide_by,postfix,active,[is_editable],[sort_order], updated)
SELECT category,[type], [fk_tenant_id],[budget_year],[fp_level_1_value],[fp_level_2_value],[key_name],[key_description],[value_type], [year_1_value], [year_2_value],[year_3_value],[year_4_value],divide_by,postfix,[active],[is_editable],[sort_order], GETDATE()
FROM @temp_key_figures a
WHERE not exists (SELECT * FROM tco_key_figures b WHERE a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year AND a.type = b.type)
 
 
print 'Update existing key figures'
 
UPDATE tco_key_figures SET year_1_value =  a.year_1_value, year_2_value = a.year_2_value, year_3_value = a.year_3_value, year_4_value = a.year_4_value, updated = GETDATE()
FROM @temp_key_figures a, tco_key_figures b
WHERE a.fk_tenant_id = b.fk_tenant_id AND a.budget_year = b.budget_year AND a.type = b.type AND b.is_editable = 0
 
 
RETURN 0