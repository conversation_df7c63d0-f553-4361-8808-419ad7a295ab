
CREATE OR ALTER PROCEDURE [dbo].[prcInitializeForecast_investment] 
@forecast_period INT
,@fk_tenant_id INT
,@user_id INT
,@investment_init_type NVARCHAR(25) = ''
,@update_only INT = 0

AS

SET NOCOUNT ON;

DECLARE @budget_year INT
DECLARE @last_period INT
DECLARE @ub_period INT
DECLARE @month_remaining dec(18,6) 
DECLARE @month_ytd dec(18,6)
DECLARE @prev_forecast_period INT
DECLARE @service_level INT
DECLARE @periods_ytd INT
DECLARE @periods_remaining INT
DECLARE @org_version VARCHAR(25) 
DECLARE @inv_org_level INT
DECLARE @unapproved_check INT 
DECLARE @vat_account NVARCHAR(25)

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

DECLARE @change_id_table TABLE (fk_change_id INT, budget_year INT,rebudget_approved	BIT)

SET @periods_ytd= convert(int,SUBSTRING(convert(varchar(6), @forecast_period),5,2))
SET @periods_remaining = 12 - @periods_ytd;

SET @inv_org_level = 2

IF (SELECT COUNT(*) FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1) = 1
BEGIN
SET @inv_org_level = (
SELECT org_level = 
CASE	WHEN param_value = 'org_id_2' THEN 2 
		WHEN param_value = 'org_id_3' THEN 3
		WHEN param_value = 'org_id_4' THEN 4
		WHEN param_value = 'org_id_5' THEN 5 
		ELSE 2 
		END 
FROM tco_parameters WHERE param_name = 'FINPLAN_INVESTMENT_LEVEL' AND fk_tenant_id = @fk_tenant_id AND active = 1)
END


PRINT 'Start procedure '  + convert(varchar(400),GETDATE());

DECLARE @period_table TABLE (pk_id INT IDENTITY NOT NULL,
	[period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL) 

DROP TABLE IF EXISTS #temp_proj_transactions
CREATE TABLE #temp_proj_transactions
(
	   [pk_id] [uniqueidentifier] NOT NULL,
       [trans_id] [uniqueidentifier] NOT NULL,
       [fk_tenant_id] [int] NOT NULL,
       [forecast_period] [int] NOT NULL,
       [fk_account_code] [nvarchar](25) NOT NULL,
       [fk_function_code] [nvarchar](25) NOT NULL,
       [fk_department_code] [nvarchar](25) NOT NULL,
       [fk_main_project_code] [nvarchar](25) NULL,
       [fk_project_code] [nvarchar](25) NOT NULL,
       [free_dim_1] [nvarchar](25) NOT NULL,
       [free_dim_2] [nvarchar](25) NOT NULL,
       [free_dim_3] [nvarchar](25) NOT NULL,
       [free_dim_4] [nvarchar](25) NOT NULL,
       [vat_rate] [decimal](18, 2) NOT NULL,
       [vat_refund] [decimal](18, 2) NOT NULL,
       [year] [int] NOT NULL,
       [amount] [decimal](18, 2) NOT NULL,
       [updated] [datetime] NOT NULL,
       [updated_by] [int] NOT NULL,
       [fk_alter_code] [nvarchar](50) NOT NULL,
       [fk_adjustment_code] [nvarchar](50) NOT NULL,
       [is_vat_row] [bit] NOT NULL DEFAULT ((0)),
       [fk_proj_trans_id] [uniqueidentifier] NULL,
       [description] [nvarchar](max) NULL,
       [is_change] [int] NULL,
	   [inv_status] INT NULL,
       [gl_amount] [decimal](18, 2) NOT NULL,
       [use_budget] INT NOT NULL DEFAULT 0,
       unappr_budch BIT)



DROP TABLE IF EXISTS #inv_ub_table
CREATE TABLE #inv_ub_table
(
    [fk_tenant_id] NVARCHAR(25) NOT NULL, 
    [gl_year] INT NOT NULL,
	[fk_main_project_code] NVARCHAR(25) NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL,  
	[acc_amount] DECIMAL(18, 2) NOT NULL, 
	[bud_amount] DECIMAL(18, 2) NOT NULL, 
    [inv_status] INT NULL
)

SET @budget_year = @forecast_period/100
SET @last_period = (@budget_year*100)+12
SET @ub_period = (@budget_year*100)+13
SET @month_remaining = (12 - CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))/12
SET @month_ytd = (CONVERT(DEC(18,2),SUBSTRING(CONVERT(CHAR(6), @forecast_period),5,2)))
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_report_setup_status WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period and tab_type = 6 and status in (2,4))


IF @forecast_period = @ub_period AND @org_version is null
BEGIN

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period -1 BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)

END

SET @vat_account = (
select MIN(acc_value) from  tmd_acc_defaults b 
WHERE  module = 'INV' and link_type = 'VAT_COST'
AND fk_tenant_id = @fk_tenant_id
AND fk_org_version = @org_version)



BEGIN 

INSERT INTO @change_id_table (fk_change_id,budget_year,rebudget_approved)
		select pk_change_id,budget_year,rebudget_approved from tfp_budget_changes
        where fk_tenant_id = @fk_tenant_id
        and budget_year <= @budget_year


END

BEGIN 

INSERT INTO @period_table (period, fk_tenant_id)
VALUES 
((@budget_year*100)+1, @fk_tenant_id),
((@budget_year*100)+2, @fk_tenant_id),
((@budget_year*100)+3, @fk_tenant_id),
((@budget_year*100)+4, @fk_tenant_id),
((@budget_year*100)+5, @fk_tenant_id),
((@budget_year*100)+6, @fk_tenant_id),
((@budget_year*100)+7, @fk_tenant_id),
((@budget_year*100)+8, @fk_tenant_id),
((@budget_year*100)+9, @fk_tenant_id),
((@budget_year*100)+10, @fk_tenant_id),
((@budget_year*100)+11, @fk_tenant_id),
((@budget_year*100)+12, @fk_tenant_id);

DELETE FROM @period_table WHERE period <= @forecast_period

END


PRINT 'Fetch budget amount ' + convert(varchar(400),GETDATE());

INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount
,unappr_budch)

SELECT 
newid() as pk_id
,newid() as trans_id
,pt.fk_tenant_id
,@forecast_period as forecast_period
,pt.fk_account_code
,pt.fk_function_code
,pt.fk_department_code
,mp.pk_main_project_code as fk_main_project_code
,pt.fk_project_code
,pt.free_dim_1
,pt.free_dim_2
,pt.free_dim_3
,pt.free_dim_4
,0 AS vat_rate
,0 AS vat_refund
,pt.year
,amount = CASE  WHEN UAD.status = 1 THEN PT.amount
                WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND ch.budget_year < @budget_year THEN PT.amount
                ELSE 0
                END
,getdate() as updated
,@user_id as updated_by
,'' as fk_alter_code
,'' as fk_adjustment_code
,is_vat_row,
NULL AS fk_proj_trans_id
,'' AS description
,0 AS is_change
,mp.inv_status
,0 as gl_amount
,unappr_budch = 0
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN @change_id_table ch ON pt.fk_change_id = ch.fk_change_id
JOIN tco_accounts ac ON ac.pk_tenant_id = pt.fk_tenant_id AND ac.pk_account_code = pt.fk_account_code AND @budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV'
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
WHERE PT.fk_tenant_id = @fk_tenant_id 
AND (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and PT.year > 0
AND (from_sync = 0 OR from_sync IS NULL)


IF @forecast_period  IN (@last_period, @ub_period) AND @investment_init_type in ('', 'TotalBudget')
BEGIN 

PRINT 'Fetch gl amount ' + convert(varchar(400),GETDATE());

INSERT INTO #inv_ub_table (
fk_tenant_id
,gl_year
,fk_main_project_code
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,acc_amount
,bud_amount
,inv_status)
SELECT 
a.fk_tenant_id
,gl_year
,fk_main_project_code
,fk_account_code
,fk_department_code = a.department_code
,fk_function_code = a.fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,acc_amount = SUM(a.amount)
,bud_amount = 0
,inv_status
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV'
LEFT JOIN tco_projects p ON p.fk_tenant_id = a.fk_tenant_id AND p.pk_project_code = a.fk_project_code AND a.gl_year BETWEEN datepart(year,p.date_from) AND  datepart(year,p.date_to)
LEFT JOIN tco_main_projects mp ON p.fk_tenant_id = mp.fk_tenant_id AND p.fk_main_project_code = mp.pk_main_project_code AND a.gl_year BETWEEN datepart(year,mp.budget_year_from) AND  datepart(year,mp.budget_year_to)
WHERE a.fk_tenant_id = @fk_tenant_id AND a.gl_year = @budget_year and inv_status != 2
AND (from_sync = 0 OR from_sync IS NULL)
GROUP BY a.fk_tenant_id, p.fk_main_project_code,a.fk_account_code, a.department_code, a.fk_function_code, a.fk_project_code, 
a.free_dim_1, a.free_dim_2, a.free_dim_3, a.free_dim_4,l.line_item_id, a.gl_year, mp.inv_status, mp.pk_main_project_code

END

IF @forecast_period IN (@last_period, @ub_period)
BEGIN

print 'Fetch grouped budget amounts '  + convert(varchar(400),GETDATE());

INSERT INTO #inv_ub_table (
fk_tenant_id
,gl_year
,fk_main_project_code
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,acc_amount
,bud_amount
,inv_status)
SELECT
fk_tenant_id
,gl_year = year
,fk_main_project_code
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,acc_amount = 0
,bud_amount = SUM(amount)
,inv_status
FROM #temp_proj_transactions
where inv_status != 2
and year = @budget_year
GROUP BY 
fk_tenant_id
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,year
,inv_status

UPDATE #inv_ub_table SET inv_status = 1000 WHERE inv_status IS NULL


--Delete forecast for investments that are going to be replaced by acc data

delete from #temp_proj_transactions where inv_status != 2 and year = @budget_year


--Insert acc data for current year

INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount)

select 
pk_id = NEWID()
,trans_id = NEWID()
,fk_tenant_id
,forecast_period = @forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate = 0
,vat_refund = 0
,year = @budget_year
,amount = SUM(acc_amount)
,updated = getdate()
,updated_by = @user_id
,fk_alter_code = ''
,fk_adjustment_code = ''
,is_vat_row = 0
,fk_proj_trans_id = NULL
,description = ''
,is_change = 0
,inv_status
,gl_amount = 0
FROM #inv_ub_table
where inv_status != 2
GROUP BY
fk_tenant_id
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,inv_status
HAVING SUM(acc_amount) <>0


--Insert motpost for year +1 for change against budget

INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount)

select 
pk_id = NEWID()
,trans_id = NEWID()
,fk_tenant_id
,forecast_period = @forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate = 0
,vat_refund = 0
,year = @budget_year+1
,amount = SUM(bud_amount)-SUM(acc_amount)
,updated = getdate()
,updated_by = @user_id
,fk_alter_code = ''
,fk_adjustment_code = ''
,is_vat_row = 0
,fk_proj_trans_id = NULL
,description = ''
,is_change = 0
,inv_status
,gl_amount = 0
FROM #inv_ub_table
where inv_status NOT IN (2,1000)
GROUP BY
fk_tenant_id
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,inv_status
HAVING SUM(bud_amount)-SUM(acc_amount) <>0

END


IF @investment_init_type = 'ActualYtd'
BEGIN
PRINT 'Running ActualYtd ' + convert(varchar(400),GETDATE())

DELETE FROM #temp_proj_transactions;

SELECT 
fk_tenant_id
,period
,fk_account_code
,department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,gl_year
,fk_prog_code
,SUM(amount) AS amount
INTO #TEMP_ACC_DATA
FROM tfp_accounting_data a
JOIN tco_accounts ac ON ac.pk_tenant_id = a.fk_tenant_id AND ac.pk_account_code = a.fk_account_code AND a.gl_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND report = '55_OVINV'
WHERE fk_tenant_id = @fk_tenant_id 
GROUP BY fk_tenant_id, period, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, gl_year, fk_prog_code
HAVING SUM(amount) != 0;


INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount)
SELECT 
newid() as pk_id
,newid() as trans_id
,a.fk_tenant_id
,@forecast_period as forecast_period
,a.fk_account_code
,a.fk_function_code
,a.department_code
, '' as fk_main_project_code
,a.fk_project_code
,a.free_dim_1
,a.free_dim_2
,a.free_dim_3
,a.free_dim_4
,0 as vat_rate
,0 as vat_refund
,gl_year as year
,a.amount
,getdate() as updated
,@user_id as updated_by
,'' as fk_alter_code
,'' as fk_adjustment_code
,0 as is_vat_row
,NULL AS fk_proj_trans_id
,'' AS description
,0 as is_change
, 0 as inv_status
,amount as gl_amount
FROM #TEMP_ACC_DATA a

END


IF @investment_init_type = 'PreviousForecast'
BEGIN
PRINT 'Running PreviousForecast ' + convert(varchar(400),GETDATE())

DELETE FROM #temp_proj_transactions;

SELECT 
fk_tenant_id
,forecast_period
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,SUM(amount) AS amount
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,description
INTO #temp_prev_forecast
FROM tmr_proj_transactions
WHERE fk_tenant_id = @fk_tenant_id
AND forecast_period = @prev_forecast_period
GROUP BY 
fk_tenant_id
,forecast_period
,fk_account_code
,fk_department_code
,fk_function_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,description
HAVING SUM(amount) != 0


INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount)
SELECT 
newid() as pk_id
,newid() as trans_id
,fk_tenant_id
,@forecast_period as forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,'' as fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,getdate() as updated
,@user_id as updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,NULL AS fk_proj_trans_id
,description
,0 as is_change
,0 as inv_status
,amount as gl_amount
FROM #temp_prev_forecast

END


IF @investment_init_type = 'BudgetInclUnapprBudch'
BEGIN
PRINT 'Running BudgetInclUnapprBudch ' + convert(varchar(400),GETDATE())

DELETE FROM #temp_proj_transactions;

INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount
,unappr_budch)

SELECT 
newid() as pk_id
,newid() as trans_id
,pt.fk_tenant_id
,@forecast_period as forecast_period
,pt.fk_account_code
,pt.fk_function_code
,pt.fk_department_code
,mp.pk_main_project_code as fk_main_project_code
,pt.fk_project_code
,pt.free_dim_1
,pt.free_dim_2
,pt.free_dim_3
,pt.free_dim_4
,0 AS vat_rate
,0 AS vat_refund
,pt.year
,amount = CASE  WHEN UAD.status = 1 THEN PT.amount
                WHEN UAD.status = 0 AND UAD.include_in_calculation = 1 AND ch.budget_year < @budget_year THEN PT.amount
                ELSE 0
                END
,getdate() as updated
,@user_id as updated_by
,'' as fk_alter_code
,'' as fk_adjustment_code
,is_vat_row,
NULL AS fk_proj_trans_id
,'' AS description
,0 AS is_change
,mp.inv_status
,0 as gl_amount
,unappr_budch = 0
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN @change_id_table ch ON pt.fk_change_id = ch.fk_change_id
JOIN tco_accounts ac ON ac.pk_tenant_id = pt.fk_tenant_id AND ac.pk_account_code = pt.fk_account_code AND @budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV'
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
WHERE PT.fk_tenant_id = @fk_tenant_id 
AND (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and PT.year > 0
AND (from_sync = 0 OR from_sync IS NULL)


--Fetch unappr amt

INSERT INTO #temp_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_main_project_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,inv_status
,gl_amount
,unappr_budch)

SELECT 
newid() as pk_id
,newid() as trans_id
,pt.fk_tenant_id
,@forecast_period as forecast_period
,pt.fk_account_code
,pt.fk_function_code
,pt.fk_department_code
,mp.pk_main_project_code as fk_main_project_code
,pt.fk_project_code
,pt.free_dim_1
,pt.free_dim_2
,pt.free_dim_3
,pt.free_dim_4
,0 AS vat_rate
,0 AS vat_refund
,pt.year
,amount = CASE  WHEN UAD.status = 0 AND ch.rebudget_approved = 1 AND ch.budget_year = @budget_year THEN PT.amount
                ELSE 0
                END
,getdate() as updated
,@user_id as updated_by
,fk_alter_code = pt.fk_alter_code
,'' as fk_adjustment_code
,is_vat_row
,NULL AS fk_proj_trans_id
,'' AS description
,0 AS is_change
,mp.inv_status
,0 as gl_amount
,unappr_budch = 1
FROM tfp_proj_transactions PT
JOIN tco_projects P on PT.fk_tenant_id = P.fk_tenant_id and PT.fk_project_code = P.pk_project_code and @budget_year BETWEEN DATEPART(YEAR,P.date_from) and DATEPART(YEAR,P.date_to)
LEFT JOIN tco_main_projects MP ON PT.fk_tenant_id = MP.fk_tenant_id and P.fk_main_project_code = MP.pk_main_project_code and @budget_year BETWEEN DATEPART(YEAR,MP.budget_year_from) and DATEPART(YEAR,MP.budget_year_to)
LEFT JOIN tco_main_project_setup PS ON PS.fk_tenant_id = MP.fk_tenant_id AND PS.pk_main_project_code = MP.pk_main_project_code
JOIN @change_id_table ch ON pt.fk_change_id = ch.fk_change_id
JOIN tco_accounts ac ON ac.pk_tenant_id = pt.fk_tenant_id AND ac.pk_account_code = pt.fk_account_code AND @budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN gmd_reporting_line l ON ac.fk_kostra_account_code = l.fk_kostra_account_code AND l.report = '55_OVINV'
JOIN tco_user_adjustment_codes UAD ON pt.fk_tenant_id = UAD.fk_tenant_id and PT.fk_user_adjustment_code = UAD.pk_adj_code --and UAD.status = 1
WHERE PT.fk_tenant_id = @fk_tenant_id 
AND (MP.inv_status = 0 OR MP.inv_status = 1 OR MP.inv_status = 2 OR MP.inv_status = 7 OR MP.inv_status = 8 OR MP.inv_status IS NULL ) --Only include active investments and financing (NULL)
and PT.year > 0
AND (from_sync = 0 OR from_sync IS NULL)

END


DELETE t FROM #temp_proj_transactions t
JOIN tmr_proj_transactions i ON t.fk_project_code = i.fk_project_code AND t.fk_tenant_id = i.fk_tenant_id AND i.is_ISY_import = 1 AND i.forecast_period = @forecast_period AND i.fk_tenant_id = @fk_tenant_id
WHERE t.year >= @budget_year


PRINT 'INSERT INTO tmr_proj_transactions '  + convert(varchar(400),GETDATE());

IF @update_only = 0
BEGIN

DELETE FROM tmr_proj_transactions WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period and is_ISY_import != 1;

INSERT INTO tmr_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,unappr_budch)
SELECT 
pk_id = newid()
,trans_id = NEWID()
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year,amount = SUM(amount)
,updated = getdate()
,updated_by = @user_id
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id = NULL
,description
,is_change
,unappr_budch
FROM #temp_proj_transactions
GROUP BY fk_tenant_id,forecast_period,fk_account_code,fk_function_code,fk_department_code,
fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,year
,fk_alter_code,fk_adjustment_code,is_vat_row,description,is_change,unappr_budch;

END


IF @update_only = 1
BEGIN

DELETE FROM tmr_proj_transactions WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period and is_change != 1 and is_ISY_import != 1;

INSERT INTO tmr_proj_transactions (
pk_id
,trans_id
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount
,updated
,updated_by
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id
,description
,is_change
,unappr_budch)
SELECT 
pk_id = newid()
,trans_id = NEWID()
,fk_tenant_id
,forecast_period
,fk_account_code
,fk_function_code
,fk_department_code
,fk_project_code
,free_dim_1
,free_dim_2
,free_dim_3
,free_dim_4
,vat_rate
,vat_refund
,year
,amount = SUM(amount)
,updated = getdate()
,updated_by = @user_id
,fk_alter_code
,fk_adjustment_code
,is_vat_row
,fk_proj_trans_id = NULL
,description
,is_change
,unappr_budch
FROM #temp_proj_transactions
GROUP BY fk_tenant_id,forecast_period,fk_account_code,fk_function_code,fk_department_code,
fk_project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,vat_rate,vat_refund,year
,fk_alter_code,fk_adjustment_code,is_vat_row,description,is_change,unappr_budch;

END

PRINT 'FINISH ' + convert(varchar(400),GETDATE());
GO
