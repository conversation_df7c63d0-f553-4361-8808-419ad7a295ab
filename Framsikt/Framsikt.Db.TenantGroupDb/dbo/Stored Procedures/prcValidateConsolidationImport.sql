CREATE OR ALTER PROCEDURE [dbo].[prcValidateConsolidationImport]
	@tenant_id int,
	@user_id int,
	@budget_year int ,
	@amountType varchar(100)
AS	

	--Clear all the error information and set default action type --
	UPDATE tcon_stage_consol_import
	SET external_company_id_error = 0, 
		fk_kostra_account_error = 0,
		amount_error = 0,
		fk_kostra_function_code_error = 0,
		period_error = 0,
		error_count = 0
	WHERE tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
		tcon_stage_consol_import.budget_year = @budget_year AND 
		tcon_stage_consol_import.user_id = @user_id AND
		tcon_stage_consol_import.amount_type = @amountType;
		
		
	--Validate period_error ---
	
		SELECT a.years  INTO #tempYears FROM (
		SELECT  CAST(@budget_year as nvarchar(10))+'01' as years  UNION  SELECT CAST(@budget_year as nvarchar(10))+'02' 
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'03' UNION  SELECT CAST(@budget_year as nvarchar(10))+'04'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'05' UNION  SELECT CAST(@budget_year as nvarchar(10))+'06'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'07' UNION  SELECT CAST(@budget_year as nvarchar(10))+'08'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'09' UNION  SELECT CAST(@budget_year as nvarchar(10))+'10'
		UNION  SELECT CAST(@budget_year as nvarchar(10))+'11' UNION  SELECT CAST(@budget_year as nvarchar(10))+'12' 
		)a

		UPDATE tcon_stage_consol_import set period_error = 1, error_count = error_count + 1
		WHERE tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
				tcon_stage_consol_import.budget_year = @budget_year AND 
				tcon_stage_consol_import.user_id = @user_id AND
				tcon_stage_consol_import.amount_type = @amountType AND
				(tcon_stage_consol_import.period_str is NULL OR tcon_stage_consol_import.period_str ='' OR tcon_stage_consol_import.period_str  NOT IN (SELECT years from #tempYears)) ; 

		DROP TABLE #tempYears


	-- Validate Amount column--

		UPDATE tcon_stage_consol_import SET amount_error = 1, error_count = error_count +1
		WHERE amount_str is NULL OR amount_str =''
		AND tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
			tcon_stage_consol_import.budget_year = @budget_year AND 
			tcon_stage_consol_import.user_id = @user_id AND
			tcon_stage_consol_import.amount_type = @amountType;
			
	-- Validate external_company_id column--
	
		SELECT a.external_company_id  INTO #tempExternalId FROM (
		SELECT  external_company_id from tcon_consol_company_setup 
		WHERE tcon_consol_company_setup.fk_tenant_id = @tenant_id AND
		tcon_consol_company_setup.budget_year = @budget_year
		)a
		
		UPDATE tcon_stage_consol_import set external_company_id_error = 1, error_count = error_count + 1
		WHERE tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
				tcon_stage_consol_import.budget_year = @budget_year AND 
				tcon_stage_consol_import.user_id = @user_id AND
				tcon_stage_consol_import.amount_type = @amountType AND
				(tcon_stage_consol_import.external_company_id is NULL OR tcon_stage_consol_import.external_company_id ='' OR tcon_stage_consol_import.external_company_id  NOT IN (SELECT external_company_id from #tempExternalId)) ; 

		DROP TABLE #tempExternalId
		
	-- Validate kostra account column--
	
		SELECT a.pk_kostra_account_code  INTO #tempKostraAccount FROM (
		SELECT  pk_kostra_account_code from gco_kostra_accounts 
		WHERE gco_kostra_accounts.type = 'operations' OR
		gco_kostra_accounts.type = 'investment'
		)a
		
		UPDATE tcon_stage_consol_import set fk_kostra_account_error = 1, error_count = error_count + 1
		WHERE tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
				tcon_stage_consol_import.budget_year = @budget_year AND 
				tcon_stage_consol_import.user_id = @user_id AND
				tcon_stage_consol_import.amount_type = @amountType AND
				(tcon_stage_consol_import.fk_kostra_account is NULL OR tcon_stage_consol_import.fk_kostra_account ='' OR tcon_stage_consol_import.fk_kostra_account  NOT IN (SELECT pk_kostra_account_code from #tempKostraAccount)) ; 

		DROP TABLE #tempKostraAccount
		
	-- Validate kostra function code column--
	
		SELECT a.kostra_function INTO #tempKostraFunction FROM(
		SELECT  pk_kostra_function_code as kostra_function from gmd_kostra_function 
		WHERE @budget_year BETWEEN datepart (year, date_from) AND datepart (year, date_to) AND
		active = 1
		)a
		
		UPDATE tcon_stage_consol_import set fk_kostra_function_code_error = 1, error_count = error_count + 1
		WHERE tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
				tcon_stage_consol_import.budget_year = @budget_year AND 
				tcon_stage_consol_import.user_id = @user_id AND
				tcon_stage_consol_import.amount_type = @amountType AND
				(tcon_stage_consol_import.fk_kostra_function_code is NULL OR tcon_stage_consol_import.fk_kostra_function_code ='' OR tcon_stage_consol_import.fk_kostra_function_code  NOT IN (SELECT kostra_function from #tempKostraFunction)) ; 

		DROP TABLE #tempKostraFunction
		
		--UPDATE Company_id and Company_name--
		
			UPDATE tcon_stage_consol_import set company_name = tcon_consol_company_setup.company_name, fk_company_id = tcon_consol_company_setup.pk_company_id
			from tcon_stage_consol_import
			inner join tcon_consol_company_setup 
			on 
			tcon_stage_consol_import.fk_tenant_id = tcon_consol_company_setup.fk_tenant_id and 
			tcon_stage_consol_import.external_company_id = tcon_consol_company_setup.external_company_id and
			tcon_stage_consol_import.budget_year = tcon_consol_company_setup.budget_year
			where tcon_stage_consol_import.fk_tenant_id = @tenant_id AND 
				tcon_stage_consol_import.budget_year = @budget_year AND 
				tcon_stage_consol_import.user_id = @user_id AND
				tcon_stage_consol_import.amount_type = @amountType
				
RETURN 0

