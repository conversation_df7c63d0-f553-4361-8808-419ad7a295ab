	CREATE OR ALTER PROCEDURE [dbo].[prcPoliticalSimulation1A1B_2020]
	

		@tenant_id int, 
		@budget_year int,
		@user_id int,
		@proposal_id UNIQUEIDENTIFIER 

AS

DECLARE
		@TenantID int = @tenant_id, 
		@BudgetYear int = @budget_year,
		@UserId int = @user_id,
		@ProposalId UNIQUEIDENTIFIER = @proposal_id


DECLARE @org_version VARCHAR(24)= (select pk_org_version FROM tco_org_version WHERE fk_tenant_id = @TenantId AND @BudgetYear * 100 +1 BETWEEN period_from AND period_to)
DECLARE @use_framsikt_1A INT = (SELECT COUNT(*) FROM tco_parameters p WHERE p.fk_tenant_id = @TenantId AND p.param_name = 'POLITICAL_USE_FRAMSIKT1A' AND p.param_value = 'TRUE')

DECLARE @incluce_blist BIT = (SELECT include_blist from  [tps_admin_config] WHERE fk_tenant_id = @TenantId AND budget_year = @BudgetYear)

DECLARE @language VARCHAR(10)

SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @TenantId)


DECLARE @tbl_reporting_line as table (report varchar(50)
, fk_kostra_account_code VARCHAR(25)
, line_group_id INT
, line_group varchar(200)
, line_item_id INT
, line_item varchar(150))


INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_group_id, line_group, line_item_id, line_item)
SELECT rl.report, rl.fk_kostra_account_code, rl.line_group_id, 
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),
line_item_id,
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))
FROM gmd_reporting_line rl
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @TenantId AND tg.Language = @language
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @TenantId AND tI.Language = @language



DROP TABLE IF EXISTS #temp_table_1
DROP TABLE IF EXISTS #temp_table_2
DROP TABLE IF EXISTS #temp_table_2_fr_finplan
DROP TABLE IF EXISTS #tempChangeIds
DROP TABLE IF EXISTS #operations_propopsal
DROP TABLE IF EXISTS #temp_fp_lines
DROP TABLE IF EXISTS #valid_accounts


	select ch.pk_change_id as changeids into #tempChangeIds from [dbo].[tco_budget_phase] ph
	join tfp_budget_changes ch  
	on ph.pk_budget_phase_id = ch.fk_budget_phase_id  AND ph.fk_tenant_id = ch.fk_tenant_id 
	where ph.fk_tenant_id=@TenantId  and ph.council_sugg_flag=1 and ch.budget_year = @BudgetYear


CREATE TABLE #valid_accounts( 
	[pk_id] INT NOT NULL IDENTITY, 
	pk_tenant_id INT NOT NULL,
    pk_account_code NVARCHAR(25) NOT NULL,
    fk_kostra_account_code NVARCHAR(25) NOT NULL)

--CREATE INDEX #valid_accounts_ind_1 on #valid_accounts (pk_account_code)
--CREATE INDEX #valid_accounts_ind_2 on #valid_accounts (fk_kostra_account_code)


INSERT INTO #valid_accounts (pk_tenant_id, pk_account_code, fk_kostra_account_code)

SELECT DISTINCT pk_tenant_id, pk_account_code, fk_kostra_account_code FROM 
(SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @TenantId AND @BudgetYear BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
UNION
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @TenantId AND @BudgetYear-1 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
UNION 
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @TenantId AND @BudgetYear-2 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
) s

CREATE TABLE #temp_table_1
	(fk_tenant_id INT NOT NULL, 
    fk_account_code NVARCHAR(25) NOT NULL, 
	forecast_period INT NULL DEFAULT 0,
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
	free_dim_1 NVARCHAR(25) NOT NULL, 
	free_dim_2 NVARCHAR(25) NOT NULL, 
	free_dim_3 NVARCHAR(25) NOT NULL, 
	free_dim_4 NVARCHAR(25) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_year_minus2 DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	finplan_year_1_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_2_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_3_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_4_amount DECIMAL (18, 2) NOT NULL,
	forecast_amount DECIMAL (18, 2) NOT NULL,
	budget_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,
	budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,
	budget_change DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_action DECIMAL (18, 2) NULL DEFAULT 0,
	forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,
	fk_prog_code NVARCHAR (25)  NULL,
	action_name NVARCHAR(255) NULL,
	action_type INT NULL,
	line_order INT NULL,
	org_budget_flag INT NOT NULL DEFAULT 1,
	inv_fk_org_id NVARCHAR(25)  NULL, 
	inv_org_name NVARCHAR(255)  NULL,
	fk_investment_id INT NULL, 
	fk_portfolio_code NVARCHAR(25) NULL,
	inv_line_group INT NULL,	
	fk_action_id INT NOT NULL DEFAULT 0,
	fk_alter_code NVARCHAR(24) NOT NULL DEFAULT '',
	long_description NVARCHAR(MAX) NULL,
	show_flag INT NOT NULL DEFAULT 0,
	[isblistaction] bit NOT NULL DEFAULT(0),
	fk_main_project_code NVARCHAR(25) NULL
	)

CREATE TABLE #temp_table_2
	(fk_tenant_id INT NOT NULL, 
	budget_year INT NOT NULL,
	forecast_period INT NULL DEFAULT 0,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
	free_dim_1 NVARCHAR(25) NOT NULL, 
	free_dim_2 NVARCHAR(25) NOT NULL, 
	free_dim_3 NVARCHAR(25) NOT NULL, 
	free_dim_4 NVARCHAR(25) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_year_minus2 DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	finplan_year_1_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_2_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_3_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_4_amount DECIMAL (18, 2) NOT NULL,
	forecast_amount DECIMAL (18, 2) NOT NULL,
	budget_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,
	budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,
	budget_change DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_action DECIMAL (18, 2) NULL DEFAULT 0,
	forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,
	[1A_line] bit DEFAULT ((0)) NOT NULL,
	[line_item_id] INT DEFAULT (0) NOT NULL,
	[line_item] NVARCHAR(200) DEFAULT(''),
	[line_group_id] INT DEFAULT (0) NOT NULL,
	[line_group] NVARCHAR(200) DEFAULT(''),
	[fk_prog_code] NVARCHAR (25) NULL DEFAULT '1',
	[acc_type] INT DEFAULT ((0)) NOT NULL,
	[fr_1A_line] bit DEFAULT ((0)) NOT NULL,
	[fr_line_item_id] INT DEFAULT (0) NOT NULL,
	[fr_line_item] NVARCHAR(200) DEFAULT(''),
	[fr_line_group_id] INT DEFAULT (0) NOT NULL,
	[fr_line_group] NVARCHAR(200) DEFAULT(''),
	org_budget_flag INT NOT NULL DEFAULT 1,
	fk_action_id INT NOT NULL DEFAULT 0,
	fk_alter_code NVARCHAR(24) NOT NULL DEFAULT '',
	long_description NVARCHAR(MAX)  NULL,
	show_flag INT NOT NULL DEFAULT 0,
	action_name NVARCHAR(255) NULL,
	[isblistaction] bit NOT NULL DEFAULT(0),
	fk_main_project_code NVARCHAR(25) NULL,
    action_type INT NULL
	)

	

CREATE TABLE #temp_table_2_fr_finplan
	(fk_tenant_id INT NOT NULL, 
	budget_year INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
    actual_amt_year_minus2 DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	finplan_year_1_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_2_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_3_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_4_amount DECIMAL (18, 2) NOT NULL,
	forecast_amount DECIMAL (18, 2) NOT NULL,	
	budget_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,
	budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_ytd_pct DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_forecast_pct DECIMAL (18, 2) NULL DEFAULT 0,
	budget_change DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_action DECIMAL (18, 2) NULL DEFAULT 0,
	forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_incl_dev_action_pct DECIMAL (18, 2) NULL DEFAULT 0,
	[fr_1A_line] bit DEFAULT ((0)) NOT NULL,
	[fr_line_item_id] INT DEFAULT (0) NOT NULL,
	[fr_line_item] NVARCHAR(200) DEFAULT(''),
	[fr_line_group_id] INT DEFAULT (0) NOT NULL,
	[fr_line_group] NVARCHAR(200) DEFAULT(''),
	org_budget_flag INT NOT NULL DEFAULT 1, 
	fk_action_id INT NOT NULL DEFAULT 0,
	fk_alter_code NVARCHAR(24) NOT NULL DEFAULT '',
	long_description NVARCHAR(MAX) NOT NULL DEFAULT '',
	show_flag INT NOT NULL DEFAULT 0,
	action_name NVARCHAR(255) NULL,
	[isblistaction] bit NOT NULL DEFAULT(0),
	fk_main_project_code NVARCHAR(25) NULL,
    action_type INT NULL
	)

CREATE TABLE #operations_propopsal
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [fk_proposal_id] UNIQUEIDENTIFIER NOT NULL, 
    [budget_year] INT NOT NULL, 
	[fk_account_code] NVARCHAR(24) NOT NULL, 
	[fk_department_code] NVARCHAR(24) NOT NULL, 
	[fk_function_code] NVARCHAR(24) NOT NULL, 
    [line_group_id] INT NOT NULL, 
    [line_group] NVARCHAR(200) NOT NULL, 
    [line_item_id] INT NOT NULL, 
    [line_item] NVARCHAR(200) NOT NULL, 
    [fk_action_id] INT NOT NULL, 
    [action_name] NVARCHAR(255) NOT NULL, 
	[org_id] NVARCHAR(25) NOT NULL,
	[org_name] NVARCHAR (255) NOT NULL,
	[gl_amount] DECIMAL(18,2) NOT NULL,
	[org_budget_amount] DECIMAL(18,2) NOT NULL,
	[revised_budget_amount] DECIMAL(18,2) NOT NULL,
    [year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
    [change_1_amount] DECIMAL(18, 2) NOT NULL, 
    [change_2_amount] DECIMAL(18, 2) NOT NULL, 
    [change_3_amount] DECIMAL(18, 2) NOT NULL, 
    [change_4_amount] DECIMAL(18, 2) NOT NULL, 
	[comment] NVARCHAR (4000) NULL,
	[description] NVARCHAR (MAX) NULL,
	[status] bit NUll,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [altercode] NVARCHAR(40) NULL, 
    [show_flag] BIT NULL, 
	[isblistaction] bit NOT NULL DEFAULT(0),
	[kostra_account_code] NVARCHAR(40) NULL,
	[income_flag] int NULL,	
	fk_main_project_code NVARCHAR(25) NULL,
    action_type INT NULL) 

		

INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount, fk_prog_code,
fk_action_id,fk_alter_code,long_description,show_flag,action_name)
SELECT fk_tenant_id, fk_account_code, department_code, fk_function_code,
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4,
'' AS fk_project_code, 0 as actual_amt_year,0 as actual_amt_last_year,sum(amount) AS actual_amt_year_minus2,
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS  revised_bud_amt_year, 0 AS revised_bud_amt_last_year,
0 AS finplan_year_1_amount, 0 AS finplan_year_2_amount, 0 AS  finplan_year_3_amount, 0 AS finplan_year_4_amount,0 AS forecast_amount,
'' as fk_prog_code,-2 as fk_action_id,'-1' as fk_alter_code,'' as long_description,0 as show_flag,'Other actions' as action_name
FROM tfp_accounting_data a
WHERE fk_tenant_id = @TenantId
AND gl_year = @BudgetYear-2
GROUP BY  fk_tenant_id, fk_account_code, department_code, fk_function_code


INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount, fk_prog_code,
fk_action_id,fk_alter_code,long_description,show_flag,action_name)
SELECT fk_tenant_id, fk_account_code, department_code, fk_function_code, 
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4,
'' as fk_project_code,0 as actual_amt_year,0 as actual_amt_last_year,0 AS actual_amt_year_minus2,
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS  revised_bud_amt_year,SUM(amount_year_1) AS revised_bud_amt_last_year,
0 AS finplan_year_1_amount, 0 AS finplan_year_2_amount, 0 AS  finplan_year_3_amount, 0 AS finplan_year_4_amount,0 AS forecast_amount,
'' as fk_prog_code,-2 as fk_action_id,'-1' as fk_alter_code,'' as long_description,0 as show_flag,'Other actions' as action_name
FROM tbu_trans_detail a
WHERE fk_tenant_id = @TenantId
AND budget_year = @BudgetYear-1
GROUP BY  fk_tenant_id, fk_account_code, department_code, fk_function_code
--,fk_project_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.fk_prog_code

INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount, fk_prog_code,
fk_action_id,fk_alter_code,long_description,show_flag,action_name)
SELECT fk_tenant_id, fk_account_code, department_code, fk_function_code,
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4,
'' as fk_project_code, 0 as actual_amt_year,0 as actual_amt_last_year,0 AS actual_amt_year_minus2,
0 as org_bud_amt_year, SUM(amount_year_1) as org_bud_amt_last_year, 0 AS  revised_bud_amt_year,0 AS revised_bud_amt_last_year,
0 AS finplan_year_1_amount, 0 AS finplan_year_2_amount, 0 AS  finplan_year_3_amount, 0 AS finplan_year_4_amount,0 AS forecast_amount, '' as fk_prog_code,
-2 as fk_action_id,'-1' as fk_alter_code,'' as long_description,0 as show_flag,'Other actions' as action_name
FROM tbu_trans_detail_original a
WHERE fk_tenant_id = @TenantId
AND budget_year = @BudgetYear-1
GROUP BY  fk_tenant_id, fk_account_code, department_code, fk_function_code,fk_project_code
--,free_dim_1, free_dim_2, free_dim_3, free_dim_4, a.fk_prog_code


INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount,
action_name, action_type, line_order,fk_action_id,fk_alter_code,long_description,show_flag, fk_main_project_code)
SELECT a.fk_tenant_id, fk_account_code, department_code, function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
project_code,0 as actual_amt_year,0 as actual_amt_last_year,0 AS actual_amt_year_minus2,
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS  revised_bud_amt_year,0 AS revised_bud_amt_last_year,
SUM(a.year_1_amount) AS finplan_year_1_amount, SUM(a.year_2_amount) AS finplan_year_2_amount, SUM(a.year_3_amount) AS  finplan_year_3_amount, 
SUM(a.year_4_amount) AS finplan_year_4_amount,0 AS forecast_amount,
h.description as action_name, h.action_type, h.line_order,
a.fk_action_id,a.fk_alter_code,h.long_description,
show_flag = 0,
fk_main_project_code = ISNULL (p.fk_main_project_code, '')
FROM tfp_trans_detail a
JOIN tfp_trans_header h ON a.fk_tenant_id = h.fk_tenant_id AND a.fk_action_id = h.pk_action_id
JOIN #tempChangeIds bc ON  a.fk_change_id = bc.changeids
LEFT JOIN tco_projects p ON a.project_code = p.pk_project_code AND a.fk_tenant_id = p.fk_tenant_id AND a.budget_year BETWEEN DATEPART (YEAR,p.date_from) AND DATEPART(YEAR,p.date_to)
--LEFT JOIN tfp_temp_header th ON h.fk_tenant_id = th.fk_tenant_id AND h.pk_action_id = th.fk_action_id
WHERE a.fk_tenant_id = @TenantId
AND a.budget_year = @BudgetYear
GROUP BY  a.fk_tenant_id, fk_account_code, department_code,function_code,project_code, 
h.description, h.action_type, h.line_order, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
a.fk_action_id,a.fk_alter_code,h.long_description, p.fk_main_project_code


-- Remove blist actions from table
DELETE FROM #temp_table_1 WHERE fk_action_id IN (
SELECT d.fk_action_id
FROM #temp_table_1 d
JOIN tfp_temp_header th ON d.fk_action_id = th.fk_action_id AND d.fk_tenant_id = th.fk_tenant_id
JOIN tfp_temp_detail td ON th.pk_temp_id = td.fk_temp_id AND th.fk_tenant_id = td.fk_tenant_id AND td.budget_year = @budget_year
JOIN #tempChangeIds bc ON  td.fk_change_id = bc.changeids
GROUP BY d.fk_action_id, d.fk_action_id
HAVING   SUM(d.finplan_year_1_amount) = 0 
AND SUM(d.finplan_year_2_amount) = 0
AND SUM(d.finplan_year_3_amount) = 0
AND SUM(d.finplan_year_4_amount) = 0)

IF @incluce_blist = 1 
begin


INSERT INTO #temp_table_1 (fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
fk_project_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount,
action_name, action_type, line_order,fk_action_id,fk_alter_code,long_description,show_flag,isblistaction, fk_main_project_code)
SELECT a.fk_tenant_id, fk_account_code, department_code, function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
project_code,0 as actual_amt_year,0 as actual_amt_last_year,0 AS actual_amt_year_minus2,
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 AS  revised_bud_amt_year,0 AS revised_bud_amt_last_year,
SUM(a.year_1_amount) AS finplan_year_1_amount, SUM(a.year_2_amount) AS finplan_year_2_amount, SUM(a.year_3_amount) AS  finplan_year_3_amount, 
SUM(a.year_4_amount) AS finplan_year_4_amount,0 AS forecast_amount,
h.description as action_name, h.action_type, h.line_order,
a.fk_temp_id as fk_action_id,a.fk_alter_code,h.long_description,1 as show_flag, 1 as isblistaction, ISNULL(p.fk_main_project_code,'')
FROM tfp_temp_detail a
JOIN tfp_temp_header h ON a.fk_tenant_id = h.fk_tenant_id AND a.fk_temp_id = h.pk_temp_id AND h.is_parked_action = 0
JOIN #tempChangeIds bc ON  a.fk_change_id = bc.changeids
LEFT JOIN tco_projects p ON a.fk_tenant_id = p.fk_tenant_id AND a.project_code = p.pk_project_code AND a.budget_year BETWEEN DATEPART(YEAR,p.date_from) AND DATEPART(YEAR,p.date_to)
WHERE a.fk_tenant_id = @TenantId
AND a.budget_year = @BudgetYear
AND h.action_type != 60
AND (a.forecast_period = 0 OR a.forecast_period is null or a.forecast_period = @BudgetYear*100)
GROUP BY  a.fk_tenant_id, fk_account_code, department_code,function_code,project_code, 
h.description, h.action_type, h.line_order, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
a.fk_temp_id,a.fk_alter_code,h.long_description, p.fk_main_project_code

end

INSERT INTO #temp_table_2 (fk_tenant_id,budget_year, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount, org_budget_flag,
fk_action_id,fk_alter_code,long_description,show_flag,action_name, isblistaction, action_type, fk_main_project_code)
SELECT fk_tenant_id, @BudgetYear, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
sum(actual_amt_year),sum(actual_amt_last_year),SUM(actual_amt_year_minus2),
sum(org_bud_amt_year), sum(org_bud_amt_last_year), sum(revised_bud_amt_year),sum(revised_bud_amt_last_year),
sum(finplan_year_1_amount), sum(finplan_year_2_amount), sum(finplan_year_3_amount), sum(finplan_year_4_amount),sum(forecast_amount),
org_budget_flag,fk_action_id,fk_alter_code,long_description,show_flag,action_name, isblistaction, action_type, fk_main_project_code
FROM  #temp_table_1 
GROUP BY fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, org_budget_flag,free_dim_1, free_dim_2, free_dim_3, free_dim_4,
fk_action_id,fk_alter_code,long_description,show_flag,action_name, isblistaction, action_type, fk_main_project_code;



CREATE INDEX #ind_1_temp_table_2 ON #temp_table_2 (fk_account_code, fk_department_code, fk_function_code, fk_tenant_id);

CREATE INDEX #ind_2_temp_table_2 ON #temp_table_2 ([1A_line], line_group_id);

CREATE INDEX #ind_3_temp_table_2 ON #temp_table_2 (acc_type);

CREATE INDEX #ind_4_temp_table_2 ON #temp_table_2 ([fr_1A_line], fr_line_group_id);

CREATE INDEX #ind_5_temp_table_2 ON #temp_table_2 (fk_account_code, fk_tenant_id, budget_year);



UPDATE #temp_table_2 SET acc_type = 1 
FROM #temp_table_2 a, #valid_accounts b, gco_kostra_accounts c
WHERE 
a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --bug 73280 AND a.budget_year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)
AND b.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'operations'


PRINT 'delete investments accounts'
DELETE FROM #temp_table_2 WHERE acc_type != 1


IF @use_framsikt_1A = 0
BEGIN

PRINT 'Update 1a'

UPDATE #temp_table_2 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN #valid_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code --AND a.budget_year --BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND a.fk_department_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_DEPARTMENTS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = '54_DRIFTA' 

UPDATE #temp_table_2 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN #valid_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code --AND a.budget_year --BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND a.fk_function_code IN (SELECT e.param_value FROM tco_parameters e WHERE e.param_name = 'FP_CENTRAL_FUNCTIONS' AND e.fk_tenant_id = a.fk_tenant_id AND e.active = 1)
AND c.line_group_id = 10
WHERE  c.report = '54_DRIFTA' 


UPDATE #temp_table_2 SET [1A_line] = 1, line_group_id = c.line_group_id, line_group = c.line_group, line_item_id = c.line_item_id, line_item = c.line_item 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN #valid_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code --AND a.budget_year --BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND c.line_group_id != 10
WHERE  c.report = '54_DRIFTA' 


IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = '1B_EXLC_FINANCE_ROWS' AND active = 1 AND param_value = 'TRUE'
AND fk_tenant_id = @TenantId) >=1
BEGIN

UPDATE #temp_table_2 SET [1A_line] = 0, line_group_id = 0, line_group = '', line_item_id = 0, line_item = '' 
FROM  @tbl_reporting_line c 
LEFT OUTER JOIN #valid_accounts b ON b.fk_kostra_account_code = c.fk_kostra_account_code
LEFT OUTER JOIN  #temp_table_2 a ON a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --AND a.year BETWEEN datepart(year, b.datefrom) AND DATEPART(YEAR,b.dateTo)
AND a.fk_tenant_id = b.pk_tenant_id
AND b.fk_kostra_account_code = c.fk_kostra_account_code
AND c.line_group_id = 12
AND a.fk_department_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
AND a.fk_function_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_FUNCTIONS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
WHERE  c.report = '54_DRIFTB' 

END

INSERT INTO #temp_table_2 (fk_tenant_id,budget_year, fk_account_code, fk_department_code, fk_function_code, 
free_dim_1, free_dim_2, free_dim_3, free_dim_4, actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount, org_budget_flag,
[1A_line], line_group_id, line_group, line_item_id, line_item)
SELECT  
ac.pk_tenant_id,@BudgetYear, MIN(ac.pk_account_code), '0000' as fk_department_code, '0000' as fk_function_code, 
'' as free_dim_1, '' as free_dim_2, '' as free_dim_3, '' as free_dim_4, 
0 as actual_amt_year,0 as actual_amt_last_year,0 as actual_amt_year_minus2,
0 as org_bud_amt_year, 0 as org_bud_amt_last_year, 0 as revised_bud_amt_year,0 as revised_bud_amt_last_year,
0 as finplan_year_1_amount, 0 as finplan_year_2_amount, 0 as finplan_year_3_amount, 0 as finplan_year_4_amount,
0 as forecast_amount, 1 as org_budget_flag,
1 as [1A_line], rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item
FROM @tbl_reporting_line rl 
JOIN #valid_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND ac.pk_tenant_id = @TenantId --AND @BudgetYear BETWEEN DATEPART(YEAR,ac.dateFrom) AND datepart(year, ac.dateto) 
WHERE report = '54_DRIFTA'
AND NOT EXISTS (
SELECT * FROM #temp_table_2 bf WHERE bf.fk_tenant_id = @TenantId AND bf.budget_year = @BudgetYear
AND bf.line_group_id = rl.line_group_id AND bf.line_item_id = rl.line_item_id
)
group by ac.pk_tenant_id, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item

END


IF @use_framsikt_1A > 0
BEGIN
Print 'Logic for framsikt 1a'


	UPDATE #temp_table_2
	SET [fr_1A_line] = 1, fr_line_group_id = ls.action_type, fr_line_item_id = ls.line_order, fr_line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 0;

	UPDATE #temp_table_2
	SET [fr_1A_line] = 1, fr_line_group_id = ls.action_type, fr_line_item_id = ls.line_order, fr_line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 5;	
	
	UPDATE #temp_table_2
	SET [fr_1A_line] = 1, fr_line_group_id = ls.action_type, fr_line_item_id = ls.line_order, fr_line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 4;	
	
	UPDATE #temp_table_2
	SET [fr_1A_line] = 1, fr_line_group_id = ls.action_type, fr_line_item_id = ls.line_order, fr_line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year	
	AND ls.priority = 3;	
	
	UPDATE #temp_table_2
	SET [fr_1A_line] = 1, fr_line_group_id = ls.action_type, fr_line_item_id = ls.line_order, fr_line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year	
	AND ls.priority = 2;	
	
	UPDATE #temp_table_2
	SET [fr_1A_line] = 1, fr_line_group_id = ls.action_type, fr_line_item_id = ls.line_order, fr_line_item = ls.action_name
	FROM #temp_table_2 imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 1;


UPDATE #temp_table_2 set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'revenues_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 1

UPDATE #temp_table_2 set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'centralexpenses_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 100

UPDATE #temp_table_2 set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'financial_income_expenses_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 2

UPDATE #temp_table_2 set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'provisions_others_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 3

UPDATE #temp_table_2 set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE 
ls.context  = 'BudgetManagement'
AND ls.language = @language
AND ls.Id = 'bm_transfered_to_investments_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 4

UPDATE #temp_table_2 SET fr_line_group_id = fr_line_group_id * 1000
WHERE [fr_1A_line] = 1 AND fr_line_group_id IN (2,3,4);


UPDATE #temp_table_2 SET acc_type = 1 
FROM #temp_table_2 a, #valid_accounts b, gco_kostra_accounts c
WHERE 
a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --AND a.year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)
AND b.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'operations'

UPDATE #temp_table_2 SET acc_type = 2 
FROM #temp_table_2 a, #valid_accounts b, gco_kostra_accounts c
WHERE 
a.fk_account_code = b.pk_account_code AND a.fk_tenant_id = b.pk_tenant_id --AND a.year BETWEEN DATEPART(YEAR,b.dateFrom) AND DATEPART(YEAR,b.dateTo)
AND b.fk_kostra_account_code = c.pk_kostra_account_code AND c.type = 'investment'



INSERT INTO #temp_table_2_fr_finplan 
(fk_tenant_id,budget_year, fk_account_code, fk_department_code, fk_function_code, actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount,
fr_1A_line, fr_line_group_id, fr_line_group, fr_line_item_id, fr_line_item, org_budget_flag,fk_action_id,fk_alter_code,long_description,show_flag,action_name,
isblistaction)
SELECT fk_tenant_id, @BudgetYear, fk_account_code, fk_department_code, fk_function_code, 
sum(actual_amt_year),sum(actual_amt_last_year),SUM(actual_amt_year_minus2),
sum(org_bud_amt_year), sum(org_bud_amt_last_year), sum(revised_bud_amt_year),sum(revised_bud_amt_last_year),
sum(finplan_year_1_amount), sum(finplan_year_2_amount), sum(finplan_year_3_amount), sum(finplan_year_4_amount),sum(forecast_amount),
fr_1A_line = CASE WHEN action_type IN (1,2,3,4,100) THEN 1 ELSE 0 END, 
action_type as fr_line_group_id, '' as fr_line_group, line_order as fr_line_item_id, action_name as fr_line_item, org_budget_flag,
fk_action_id,fk_alter_code,ISNULL(long_description,''),show_flag,action_name, isblistaction
FROM  #temp_table_1 WHERE action_type IS NOT NULL
GROUP BY fk_tenant_id, fk_account_code, fk_department_code, fk_function_code, line_order, action_name, action_type, org_budget_flag,
fk_action_id,fk_alter_code,long_description,show_flag,action_name, isblistaction;

-- Fetch budget and accounting data from temp_table_2 so that this use the line setup to connect to the finplan line. change logic as part of 107780

INSERT INTO #temp_table_2_fr_finplan 
(fk_tenant_id, budget_year,fk_account_code, fk_department_code, fk_function_code,actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount,
fr_1A_line, fr_line_group_id, fr_line_group, fr_line_item_id, fr_line_item, 
org_budget_flag,fk_action_id,fk_alter_code,long_description,show_flag,action_name,isblistaction)
SELECT a.fk_tenant_id,a.budget_year,a.fk_account_code, a.fk_department_code, a.fk_function_code,a.actual_amt_year,a.actual_amt_last_year,a.actual_amt_year_minus2,
a.org_bud_amt_year, a.org_bud_amt_last_year, a.revised_bud_amt_year,a.revised_bud_amt_last_year,
0 as finplan_year_1_amount, 0 as finplan_year_2_amount, 0 as finplan_year_3_amount, 0 as finplan_year_4_amount,a.forecast_amount,
a.fr_1A_line, a.fr_line_group_id, a.fr_line_group, a.fr_line_item_id, a.fr_line_item,
org_budget_flag,fk_action_id,fk_alter_code, ISNULL(long_description,''),show_flag,action_name,isblistaction
FROM #temp_table_2 a
WHERE  a.acc_type = 1

UPDATE #temp_table_2_fr_finplan set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'revenues_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 1

UPDATE #temp_table_2_fr_finplan set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'centralexpenses_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 100

UPDATE #temp_table_2_fr_finplan set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'financial_income_expenses_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 2

UPDATE #temp_table_2_fr_finplan set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE ls.context = 'Common'
AND ls.language = @language
AND ls.Id = 'provisions_others_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 3

UPDATE #temp_table_2_fr_finplan set fr_line_group = (SELECT ISNULL(lst.Description, ls.Description) FROM vw_gco_language_strings ls
JOIN gco_tenants t ON  ls.tenant_type = t.tenant_type_id
LEFT JOIN gco_language_string_overrides_tenant lst ON ls.Id = lst.Id AND t.pk_id = lst.fk_tenant_id AND ls.Language = lst.Language
WHERE 
ls.context  = 'BudgetManagement'
AND ls.language = @language
AND ls.Id = 'bm_transfered_to_investments_text'
AND t.pk_id = @TenantId)
WHERE [fr_1A_line] = 1 AND fr_line_group_id = 4

UPDATE #temp_table_2_fr_finplan SET fr_line_group_id = fr_line_group_id * 1000
WHERE [fr_1A_line] = 1 AND fr_line_group_id IN (2,3,4);


/* this logic was replaced as part of bug 107780. 


SELECT DISTINCT fr_1A_line, fr_line_group_id, fr_line_group,fr_line_item_id, fr_line_item 
INTO #temp_fp_lines 
FROM #temp_table_2_fr_finplan
WHERE fr_1A_line = 1


INSERT INTO #temp_table_2_fr_finplan 
(fk_tenant_id,budget_year, fk_account_code, fk_department_code, fk_function_code, actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount,
fr_1A_line, fr_line_group_id, fr_line_group, fr_line_item_id, fr_line_item,
fk_action_id,fk_alter_code,long_description,show_flag,action_name, isblistaction)
SELECT a.fk_tenant_id,a.budget_year, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.actual_amt_year,a.actual_amt_last_year,a.actual_amt_year_minus2,
a.org_bud_amt_year, a.org_bud_amt_last_year, a.revised_bud_amt_year,a.revised_bud_amt_last_year,
0 as finplan_year_1_amount, 0 as finplan_year_2_amount, 0 as finplan_year_3_amount, 0 as finplan_year_4_amount,a.forecast_amount,
h.fr_1A_line, h.fr_line_group_id, h.fr_line_group, h.fr_line_item_id, h.fr_line_item,
fk_action_id,fk_alter_code,COALESCE(long_description,''),show_flag,action_name, isblistaction
FROM #temp_table_2 a
JOIN #temp_fp_lines h ON a.fr_1A_line = h.fr_1A_line AND a.fr_line_group_id = h.fr_line_group_id AND a.fr_line_item_id = h.fr_line_item_id
WHERE a.fr_1A_line = h.fr_1A_line AND h.fr_line_group_id = a.fr_line_group_id AND a.fr_line_item_id = h.fr_line_item_id


INSERT INTO #temp_table_2_fr_finplan 
(fk_tenant_id,budget_year, fk_account_code, fk_department_code, fk_function_code, actual_amt_year,actual_amt_last_year,actual_amt_year_minus2,
org_bud_amt_year, org_bud_amt_last_year, revised_bud_amt_year,revised_bud_amt_last_year,
finplan_year_1_amount, finplan_year_2_amount, finplan_year_3_amount, finplan_year_4_amount,forecast_amount,
fr_1A_line, fr_line_group_id, fr_line_group, fr_line_item_id, fr_line_item,
fk_action_id,fk_alter_code,long_description,show_flag,action_name, isblistaction)
SELECT a.fk_tenant_id,a.budget_year, a.fk_account_code, a.fk_department_code, a.fk_function_code, a.actual_amt_year,a.actual_amt_last_year,a.actual_amt_year_minus2,
a.org_bud_amt_year, a.org_bud_amt_last_year, a.revised_bud_amt_year,a.revised_bud_amt_last_year,
0 as finplan_year_1_amount, 0 as finplan_year_2_amount, 0 as finplan_year_3_amount, 0 as finplan_year_4_amount,a.forecast_amount,
a.fr_1A_line, a.fr_line_group_id, a.fr_line_group, a.fr_line_item_id, a.fr_line_item,
fk_action_id,fk_alter_code,isnull(long_description,''),show_flag,action_name, isblistaction
FROM #temp_table_2 a
WHERE a.fr_1A_line = 0 AND a.acc_type = 1
*/

END

IF @use_framsikt_1A = 0 
BEGIN
	INSERT #operations_propopsal
	(fk_tenant_id,fk_proposal_id,budget_year,fk_account_code,fk_department_code,fk_function_code, line_group_id,line_group,line_item_id,line_item,fk_action_id,action_name,org_id,org_name,gl_amount,
	org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
	change_4_amount,comment,description,status,updated,updated_by,show_flag )
	Select fk_tenant_id,@ProposalId,budget_year,fk_account_code,fk_department_code,fk_function_code, line_group_id,line_group,line_item_id,line_item,0,'',0,'',
	sum(actual_amt_year_minus2),sum(org_bud_amt_last_year),sum(revised_bud_amt_last_year),
	sum(finplan_year_1_amount),sum(finplan_year_2_amount),
	sum(finplan_year_3_amount),sum(finplan_year_4_amount),0,0,0,0,'' as comments,'' as description,1,getutcdate(),@UserId ,0
	from #temp_table_2 WHERE [1A_line]=1 AND isblistaction = 0
	GROUP BY fk_tenant_id,budget_year,fk_account_code,fk_department_code,fk_function_code, line_group_id,line_group,line_item_id,line_item

	

	INSERT #operations_propopsal
		(fk_tenant_id,fk_proposal_id,budget_year,fk_account_code,fk_department_code,fk_function_code,
		line_group_id,line_group,line_item_id,line_item,
		fk_action_id,action_name,alterCode,org_id,org_name,gl_amount,
		org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
		change_4_amount,comment,description,status,updated,updated_by,show_flag,isblistaction, action_type,fk_main_project_code )
  

		
SELECT fk_tenant_id,@ProposalId,budget_year,fk_account_code,fk_department_code,fk_function_code,0,0,0,0,
fk_action_id, action_name, fk_alter_code,aggregate_id as org_id,aggregate_name as org_name,
sum(actual_amt_year_minus2),sum(org_bud_amt_last_year),sum(revised_bud_amt_last_year),
sum(finplan_year_1_amount),sum(finplan_year_2_amount),sum(finplan_year_3_amount),sum(finplan_year_4_amount),0,0,0,0,
'',long_description,status= Case when isblistaction = 1 then 0 else 1 end,getutcdate(),@UserId,show_flag,isblistaction, action_type,fk_main_project_code

FROM

(
SELECT 
a.fk_tenant_id,a.budget_year, 2 as doc_type,a.fk_account_code, a.fk_department_code, a.fk_function_code,a.fk_action_id,
a.fk_alter_code, ISNULL(a.action_name,'') as action_name, a.long_description,a.show_flag,
sum(actual_amt_year) AS actual_amt_year,sum(actual_amt_last_year) AS actual_amt_last_year,sum(actual_amt_year_minus2) as actual_amt_year_minus2,
sum(org_bud_amt_year) AS org_bud_amt_year, sum(org_bud_amt_last_year) AS org_bud_amt_last_year, 
sum(revised_bud_amt_year) AS revised_bud_amt_year,sum(revised_bud_amt_last_year) AS revised_bud_amt_last_year,
sum(finplan_year_1_amount) AS finplan_year_1_amount, 
sum(finplan_year_2_amount) AS finplan_year_2_amount, sum(finplan_year_3_amount) AS finplan_year_3_amount, 
sum(finplan_year_4_amount) AS finplan_year_4_amount,sum(forecast_amount) AS forecast_amount,

aggregate_id = 
CASE	WHEN p1.param_value = 'org_id_1' THEN ISNULL(oh.org_id_1,'ZZ')
		WHEN p1.param_value = 'org_id_2' THEN ISNULL(oh.org_id_2,'ZZ')
		WHEN p1.param_value = 'org_id_3' THEN ISNULL(oh.org_id_3,'ZZ')
		WHEN p1.param_value = 'org_id_4' THEN ISNULL(oh.org_id_4,'ZZ')
		WHEN p1.param_value = 'org_id_5' THEN ISNULL(oh.org_id_5,'ZZ')
		WHEN p1.param_value = 'service_id_1' THEN ISNULL(sv.service_id_1,'')
		WHEN p1.param_value = 'service_id_2' THEN ISNULL(sv.service_id_2,'')
		WHEN p1.param_value = 'service_id_3' THEN ISNULL(sv.service_id_3,'')
		WHEN p1.param_value = 'service_id_4' THEN ISNULL(sv.service_id_4,'')
		WHEN p1.param_value = 'service_id_5' THEN ISNULL(sv.service_id_5,'')	
ELSE org_id_2
END,
aggregate_name = 
CASE	WHEN p1.param_value = 'org_id_1' THEN ISNULL(oh.org_name_1,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_2' THEN ISNULL(oh.org_name_2,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_3' THEN ISNULL(oh.org_name_3,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_4' THEN ISNULL(oh.org_name_4,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_5' THEN ISNULL(oh.org_name_5,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_1' THEN ISNULL(sv.service_name_1,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_2' THEN ISNULL(sv.service_name_2,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_3' THEN ISNULL(sv.service_name_3,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_4' THEN ISNULL(sv.service_name_4,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_5' THEN ISNULL(sv.service_name_5,'Ukjent tjeneste')
ELSE org_name_2 
END, isblistaction, a.action_type, a.fk_main_project_code
FROM #temp_table_2 a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version AND ov.pk_org_version=@org_version
LEFT JOIN tco_service_values sv ON a.fk_function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id
LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
JOIN #valid_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id --AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
WHERE [1A_line] = 0 AND acc_type = 1 AND ac.fk_kostra_account_code NOT IN ('1580','1980') 
GROUP BY A.fk_tenant_id,a.budget_year,sv.service_id_1, sv.service_id_2, sv.service_id_3, sv.service_id_4, sv.service_id_5,
sv.service_name_1, sv.service_name_2, sv.service_name_3, sv.service_name_4, sv.service_name_5,
oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,
oh.org_name_1, oh.org_name_2, oh.org_name_3, oh.org_name_4, oh.org_name_5, p1.param_value,
a.fk_account_code, a.fk_department_code, a.fk_function_code,
a.fk_alter_code, a.action_name, a.long_description,a.show_flag,a.fk_action_id, a.isblistaction, a.action_type, a.fk_main_project_code

) s
GROUP BY fk_tenant_id,budget_year, doc_type, aggregate_id, aggregate_name,fk_account_code, fk_department_code, fk_function_code, fk_action_id, action_name,
fk_alter_code,long_description,show_flag,isblistaction, action_type,fk_main_project_code


UPDATE o SET o.show_flag = c.show_flag
FROM #operations_propopsal o, tco_fp_alter_codes c
WHERE o.fk_tenant_id = c.fk_tenant_id
AND o.altercode = c.pk_alter_code

-- new code for adding the 54_driftb - transactions



SELECT * 
INTO #rep_line_1B
FROM @tbl_reporting_line
WHERE report = '54_DRIFTB'

IF (SELECT COUNT(*) FROM vw_tco_parameters WHERE param_name = '1B_EXLC_FINANCE_ROWS' AND active = 1 AND param_value = 'TRUE'
AND fk_tenant_id = @TenantId) >=1
BEGIN
PRINT 'Delete from repline'
DELETE FROM #rep_line_1B WHERE line_group_id = 12
END

INSERT #operations_propopsal
		(fk_tenant_id,fk_proposal_id,budget_year,fk_account_code,fk_department_code,fk_function_code,
		line_group_id,line_group,line_item_id,line_item,
		fk_action_id,action_name,alterCode,org_id,org_name,gl_amount,
		org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
		change_4_amount,comment,description,status,updated,updated_by,show_flag,isblistaction )
  

		
SELECT fk_tenant_id,@ProposalId,budget_year,fk_account_code,fk_department_code,fk_function_code,0,0,0,0,
-100 as fk_action_id, action_name, fk_alter_code,aggregate_id as org_id,aggregate_name as org_name,
sum(actual_amt_year_minus2),sum(org_bud_amt_last_year),sum(revised_bud_amt_last_year),
sum(finplan_year_1_amount),sum(finplan_year_2_amount),sum(finplan_year_3_amount),sum(finplan_year_4_amount),0,0,0,0,
'',long_description,1 as status,getutcdate(),@UserId,0 as show_flag,0 as isblistaction

FROM

(
SELECT 
a.fk_tenant_id,a.budget_year, 2 as doc_type,a.fk_account_code, a.fk_department_code, a.fk_function_code,a.fk_action_id,
a.fk_alter_code, ISNULL(a.action_name,'') as action_name, a.long_description,a.show_flag,
sum(actual_amt_year) AS actual_amt_year,sum(actual_amt_last_year) AS actual_amt_last_year,sum(actual_amt_year_minus2) as actual_amt_year_minus2,
sum(org_bud_amt_year) AS org_bud_amt_year, sum(org_bud_amt_last_year) AS org_bud_amt_last_year, 
sum(revised_bud_amt_year) AS revised_bud_amt_year,sum(revised_bud_amt_last_year) AS revised_bud_amt_last_year,
sum(finplan_year_1_amount) AS finplan_year_1_amount, 
sum(finplan_year_2_amount) AS finplan_year_2_amount, sum(finplan_year_3_amount) AS finplan_year_3_amount, 
sum(finplan_year_4_amount) AS finplan_year_4_amount,sum(forecast_amount) AS forecast_amount,

aggregate_id = 
CASE	WHEN p1.param_value = 'org_id_1' THEN isnull(oh.org_id_1,'ZZ')
		WHEN p1.param_value = 'org_id_2' THEN isnull(oh.org_id_2,'ZZ')
		WHEN p1.param_value = 'org_id_3' THEN isnull(oh.org_id_3,'ZZ')
		WHEN p1.param_value = 'org_id_4' THEN isnull(oh.org_id_4,'ZZ')
		WHEN p1.param_value = 'org_id_5' THEN isnull(oh.org_id_5,'ZZ')
		WHEN p1.param_value = 'service_id_1' THEN isnull(sv.service_id_1,'ZZ')
		WHEN p1.param_value = 'service_id_2' THEN isnull(sv.service_id_2,'ZZ')
		WHEN p1.param_value = 'service_id_3' THEN isnull(sv.service_id_3,'ZZ')
		WHEN p1.param_value = 'service_id_4' THEN isnull(sv.service_id_4,'ZZ')
		WHEN p1.param_value = 'service_id_5' THEN isnull(sv.service_id_5,'ZZ')	
ELSE org_id_2
END,
aggregate_name = 
CASE	WHEN p1.param_value = 'org_id_1' THEN isnull(oh.org_name_1,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_2' THEN isnull(oh.org_name_2,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_3' THEN isnull(oh.org_name_3,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_4' THEN isnull(oh.org_name_4,'Ukjent tjeneste')
		WHEN p1.param_value = 'org_id_5' THEN isnull(oh.org_name_5,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_1' THEN isnull(sv.service_name_1,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_2' THEN isnull(sv.service_name_2,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_3' THEN isnull(sv.service_name_3,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_4' THEN isnull(sv.service_name_4,'Ukjent tjeneste')
		WHEN p1.param_value = 'service_id_5' THEN isnull(sv.service_name_5,'Ukjent tjeneste')
ELSE org_name_2 
END, isblistaction
FROM #temp_table_2 a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id  and oh.fk_org_version = @org_version
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version AND ov.pk_org_version=@org_version
LEFT JOIN tco_service_values sv ON a.fk_function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id
LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
JOIN #valid_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id --AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
JOIN #rep_line_1B rl ON rl.report = '54_DRIFTB' AND rl.fk_kostra_account_code = ac.fk_kostra_account_code
WHERE a.[1A_line] = 1 AND isblistaction = 0
AND a.fk_department_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_DEPARTMENTS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
AND a.fk_function_code NOT IN (SELECT pc.param_value FROM tco_parameters pc WHERE pc.param_name =  'FP_CENTRAL_FUNCTIONS' AND pc.active = 1 AND pc.fk_tenant_id = a.fk_tenant_id)
GROUP BY A.fk_tenant_id,a.budget_year,sv.service_id_1, sv.service_id_2, sv.service_id_3, sv.service_id_4, sv.service_id_5,
sv.service_name_1, sv.service_name_2, sv.service_name_3, sv.service_name_4, sv.service_name_5,
oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,
oh.org_name_1, oh.org_name_2, oh.org_name_3, oh.org_name_4, oh.org_name_5, p1.param_value,
a.fk_account_code, a.fk_department_code, a.fk_function_code,
a.fk_alter_code, a.action_name, a.long_description,a.show_flag,a.fk_action_id, a.isblistaction
) s
GROUP BY fk_tenant_id,budget_year, doc_type, aggregate_id, aggregate_name,fk_account_code, fk_department_code, fk_function_code, fk_action_id, action_name,
fk_alter_code,long_description,show_flag,isblistaction

-- new code end



end


IF @use_framsikt_1A = 1 
BEGIN
	INSERT #operations_propopsal
	(fk_tenant_id,fk_proposal_id,budget_year,fk_account_code,fk_department_code,fk_function_code, line_group_id,line_group,line_item_id,line_item,fk_action_id,action_name,org_id,org_name,gl_amount,
	org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
	change_4_amount,comment,description,status,updated,updated_by,show_flag )
	Select fk_tenant_id,@ProposalId,budget_year,fk_account_code,fk_department_code,fk_function_code, fr_line_group_id,fr_line_group,fr_line_item_id,fr_line_item,0,'',0,'',
	sum(actual_amt_year_minus2),sum(org_bud_amt_last_year),sum(revised_bud_amt_last_year),
	sum(finplan_year_1_amount),sum(finplan_year_2_amount),
	sum(finplan_year_3_amount),sum(finplan_year_4_amount),0,0,0,0,'' as comments,'' as description,1,getutcdate(),@UserId ,0
	from #temp_table_2_fr_finplan WHERE [fr_1A_line]=1 AND isblistaction = 0
	GROUP BY fk_tenant_id,budget_year,fk_account_code,fk_department_code,fk_function_code, fr_line_group_id,fr_line_group,fr_line_item_id,fr_line_item

	

	INSERT #operations_propopsal
		(fk_tenant_id,fk_proposal_id,budget_year,fk_account_code,fk_department_code,fk_function_code,
		line_group_id,line_group,line_item_id,line_item,
		fk_action_id,action_name,alterCode,org_id,org_name,gl_amount,
		org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
		change_4_amount,comment,description,status,updated,updated_by,show_flag,isblistaction, action_type,fk_main_project_code )
  

		
SELECT fk_tenant_id,@ProposalId,budget_year,fk_account_code,fk_department_code,fk_function_code,0,0,0,0,
fk_action_id, action_name, fk_alter_code,aggregate_id as org_id,aggregate_name as org_name,
sum(actual_amt_year_minus2),sum(org_bud_amt_last_year),sum(revised_bud_amt_last_year),
sum(finplan_year_1_amount),sum(finplan_year_2_amount),sum(finplan_year_3_amount),sum(finplan_year_4_amount),0,0,0,0,
'',long_description,status= Case when isblistaction = 1 then 0 else 1 end,getutcdate(),@UserId,show_flag,isblistaction, action_type,fk_main_project_code

FROM

(
SELECT 
a.fk_tenant_id,a.budget_year, 2 as doc_type,a.fk_account_code, a.fk_department_code, a.fk_function_code,a.fk_action_id,
a.fk_alter_code, ISNULL(a.action_name,'') as action_name, a.long_description,a.show_flag,
sum(actual_amt_year) AS actual_amt_year,sum(actual_amt_last_year) AS actual_amt_last_year,sum(actual_amt_year_minus2) as actual_amt_year_minus2,
sum(org_bud_amt_year) AS org_bud_amt_year, sum(org_bud_amt_last_year) AS org_bud_amt_last_year, 
sum(revised_bud_amt_year) AS revised_bud_amt_year,sum(revised_bud_amt_last_year) AS revised_bud_amt_last_year,
sum(finplan_year_1_amount) AS finplan_year_1_amount, 
sum(finplan_year_2_amount) AS finplan_year_2_amount, sum(finplan_year_3_amount) AS finplan_year_3_amount, 
sum(finplan_year_4_amount) AS finplan_year_4_amount,sum(forecast_amount) AS forecast_amount,

aggregate_id = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
		WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5	
ELSE org_id_2
END,
aggregate_name = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_name_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_name_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_name_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_name_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_name_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_name_1
		WHEN p1.param_value = 'service_id_2' THEN sv.service_name_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_name_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_name_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_name_5
ELSE org_name_2 
END, isblistaction, action_type,fk_main_project_code
FROM #temp_table_2_fr_finplan a
JOIN tco_org_hierarchy oh ON a.fk_department_code = oh.fk_department_code AND a.fk_tenant_id = oh.fk_tenant_id
JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version AND ov.pk_org_version=@org_version
LEFT JOIN tco_service_values sv ON a.fk_function_code = sv.fk_function_code AND a.fk_tenant_id = sv.fk_tenant_id
LEFT JOIN tco_parameters p1 ON p1.param_name = 'SERVICE_1A_1B' AND p1.fk_tenant_id = a.fk_tenant_id
JOIN #valid_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id --AND a.budget_year BETWEEN DATEPART(YEAR,ac.dateFrom) AND DATEPART(YEAR,ac.dateTo)
WHERE [fr_1A_line] = 0 AND ac.fk_kostra_account_code NOT IN ('1580','1980') 
GROUP BY A.fk_tenant_id,a.budget_year,sv.service_id_1, sv.service_id_2, sv.service_id_3, sv.service_id_4, sv.service_id_5,
sv.service_name_1, sv.service_name_2, sv.service_name_3, sv.service_name_4, sv.service_name_5,
oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5,
oh.org_name_1, oh.org_name_2, oh.org_name_3, oh.org_name_4, oh.org_name_5, p1.param_value,
a.fk_account_code, a.fk_department_code, a.fk_function_code,
a.fk_alter_code, a.action_name, a.long_description,a.show_flag,a.fk_action_id, a.isblistaction, a.action_type,a.fk_main_project_code

) s
GROUP BY fk_tenant_id,budget_year, doc_type, aggregate_id, aggregate_name,fk_account_code, fk_department_code, fk_function_code, fk_action_id, action_name,
fk_alter_code,long_description,show_flag,isblistaction, action_type,fk_main_project_code


UPDATE o SET o.show_flag = c.show_flag
FROM #operations_propopsal o, tco_fp_alter_codes c
WHERE o.fk_tenant_id = c.fk_tenant_id
AND o.altercode = c.pk_alter_code


end

-- Remove deleted items from proposal (this should be covered by the change_id filtering and not necesary bug 72203)
--DELETE p FROM #operations_propopsal p
--JOIN tfp_delete_header dh ON p.fk_tenant_id = dh.fk_tenant_id AND p.fk_action_id = dh.fk_action_id
--WHERE p.fk_action_id != 0
--AND p.line_group_id = 0

-- update kostra account codes based on accounts for a given tenant
UPDATE o SET o.kostra_account_code = c.fk_kostra_account_code FROM #operations_propopsal o, #valid_accounts c
WHERE o.fk_tenant_id = c.pk_tenant_id AND o.fk_account_code = c.pk_account_code

-- update income flag based on kostra account codes
UPDATE o SET o.income_flag = c.income_flag FROM #operations_propopsal o, gco_kostra_accounts c
WHERE  o.kostra_account_code = c.pk_kostra_account_code

INSERT tps_operations_propopsal
	(fk_tenant_id,fk_proposal_id,budget_year,line_group_id,line_group,line_item_id,line_item,fk_action_id,action_name,org_id,org_name,gl_amount,
	org_budget_amount,revised_budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,change_1_amount,change_2_amount,change_3_amount,
	change_4_amount,comment,description,status,updated,updated_by,show_flag
	,account_code,department_code,function_code,income_flag,altercode,isblistaction, action_type,fk_main_project_code)
SELECT fk_tenant_id,fk_proposal_id,budget_year,line_group_id,line_group,line_item_id,line_item,fk_action_id,action_name,org_id,org_name,
	SUM(gl_amount),SUM(org_budget_amount),SUM(revised_budget_amount),SUM(year_1_amount),SUM(year_2_amount),SUM(year_3_amount),SUM(year_4_amount),
	SUM(change_1_amount),SUM(change_2_amount),SUM(change_3_amount),
	SUM(change_4_amount),comment,description,status,updated,updated_by,show_flag
	,fk_account_code,fk_department_code,fk_function_code,COALESCE(income_flag, 0) ,altercode,isblistaction, action_type,fk_main_project_code
FROM #operations_propopsal
group by fk_tenant_id,fk_proposal_id,budget_year,line_group_id,line_group,line_item_id,line_item,
fk_action_id,action_name,org_id,org_name,comment,description,status,updated,updated_by,show_flag,
fk_account_code,fk_department_code,fk_function_code,income_flag,altercode,isblistaction, action_type,fk_main_project_code

RETURN 0
