CREATE OR ALTER PROCEDURE procLockEmploymentData
@tenant_id INT,
@budget_year INT,
@udtDepartmentCode udtDepartmentCodes readonly
AS
BEGIN

SET NOCOUNT ON

--Delete from add on original table
  DELETE ad
  FROM tbu_employments_add_on_original ad
  JOIN tbu_employments_original emp ON emp.pk_employment_id = ad.fk_employment_id AND ad.fk_tenant_id = emp.fk_tenant_id AND emp.budget_year = @budget_year and emp.fk_tenant_id=@tenant_id  
  JOIN @udtDepartmentCode udt ON emp.fk_department_code = udt.department_code
 WHERE ad.fk_tenant_id = @tenant_id 

   DELETE ad
  FROM tbu_employments_add_on_original ad
 WHERE ad.fk_tenant_id = @tenant_id AND fk_employment_id IN
(SELECT pk_employment_id FROM tbu_employments emp
JOIN @udtDepartmentCode udt ON emp.fk_department_code = udt.department_code
WHERE emp.fk_tenant_id=@tenant_id and emp.budget_year = @budget_year) 
AND  ad.fk_tenant_id=@tenant_id   

 --Delete existing rows in tbu_employments_original
 DELETE emp 
 FROM tbu_employments_original emp 
 JOIN @udtDepartmentCode udt ON emp.fk_department_code = udt.department_code
 WHERE emp.budget_year = @budget_year and emp.fk_tenant_id=@tenant_id  

  DELETE emp 
 FROM tbu_employments_original emp where pk_employment_id IN 
(SELECT pk_employment_id FROM tbu_employments emp
JOIN @udtDepartmentCode udt ON emp.fk_department_code = udt.department_code
WHERE emp.fk_tenant_id=@tenant_id and emp.budget_year = @budget_year) 
AND emp.budget_year = @budget_year and emp.fk_tenant_id=@tenant_id  



INSERT INTO tbu_employments_original(
								 pk_employment_id,
								 fk_tenant_id, 
                                 budget_year,
                                 fk_res_id,
                                 res_name,
								 fk_emp_type_id,
								 fk_position_id,
								 hjemmel_id,
								 pension_type,
								 fk_account_code,
								 fk_department_code,
								 fk_function_code,
								 fk_project_code,
								 free_dim_1,
								 free_dim_2,
								 free_dim_3,
								 free_dim_4,
								 amount_existing_salary,
								 amount_salary_month,
								 amount_salary_year,
								 start_period,
								 end_period,
								 position_pct,
								 seniority_date,
								 yearly_budget,
								 amount_pension,
								 amount_holiday,
								 amount_aga_salary,
								 amount_aga_pension,
								 amount_aga_holiday,
								 external_reference,
								 updated,
								 updated_by,
								 fk_holiday_type_id,
								 comments,
								 fk_add_on_comments,
								 change_flag,
								 salary_step
                                )

						(SELECT	 pk_employment_id,
								 fk_tenant_id, 
                                 budget_year,
                                 fk_res_id,
                                 res_name,
								 fk_emp_type_id,
								 fk_position_id,
								 hjemmel_id,
								 pension_type,
								 fk_account_code,
								 fk_department_code,
								 fk_function_code,
								 fk_project_code,
								 free_dim_1,
								 free_dim_2,
								 free_dim_3,
								 free_dim_4,
								 amount_existing_salary,
								 amount_salary_month,
								 amount_salary_year,
								 start_period,
								 end_period,
								 position_pct,
								 seniority_date,
								 yearly_budget,
								 amount_pension,
								 amount_holiday,
								 amount_aga_salary,
								 amount_aga_pension,
								 amount_aga_holiday,
								 external_reference,
								 getdate()  as updated,
								 updated_by,
								 fk_holiday_type_id,
								 comments,
								 fk_add_on_comments,
								 change_flag,
								 salary_step
FROM tbu_employments emp
JOIN @udtDepartmentCode udt ON emp.fk_department_code = udt.department_code
WHERE emp.fk_tenant_id=@tenant_id and emp.budget_year = @budget_year and emp.delete_flag = 0) 


INSERT INTO tbu_employments_add_on_original(
								fk_tenant_id,
								fk_employment_id,
								ext_add_code,
								add_on_name,
								fk_account_code,
								fk_department_code,
								fk_function_code,
								fk_project_code,
								free_dim_1,
								free_dim_2,
								free_dim_3,
								free_dim_4,
								original_amount,
								amount_month,
								amount_year,
								start_period,
								end_period,
								pension_flag,
								holiday_flag,
								--social_expense_flag,
								tax_flag,
								updated,
								updated_by
                                )

						(SELECT	ad.fk_tenant_id,
								ad.fk_employment_id,
								ad.ext_add_code,
								ad.add_on_name,
								ad.fk_account_code,
								ad.fk_department_code,
								ad.fk_function_code,
								ad.fk_project_code,
								ad.free_dim_1,
								ad.free_dim_2,
								ad.free_dim_3,
								ad.free_dim_4,
								ad.original_amount,
								ad.amount_month,
								ad.amount_year,
								ad.start_period,
								ad.end_period,
								ad.pension_flag,
								ad.holiday_flag,
								---social_expense_flag,
								ad.tax_flag,
								getdate() as updated,
								ad.updated_by
						FROM tbu_employments_add_on ad  
						JOIN tbu_employments emp ON emp.pk_employment_id = ad.fk_employment_id AND ad.fk_tenant_id = emp.fk_tenant_id AND emp.budget_year = @budget_year and emp.fk_tenant_id=@tenant_id and emp.delete_flag = 0
						JOIN @udtDepartmentCode udt ON emp.fk_department_code = udt.department_code
						WHERE ad.fk_tenant_id = @tenant_id)

SET NOCOUNT OFF 

END

GO
