CREATE OR ALTER PROCEDURE [dbo].[prcInitializeMRActivity]
	@forecast_period INT,  @fk_tenant_id INT,  @user_id INT 
AS
	
DECLARE @budget_year INT
DECLARE @prev_forecast_period INT
DECLARE @org_version VARCHAR(25) 
DECLARE @language VARCHAR(10)

SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @fk_tenant_id)
SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @forecast_period BETWEEN period_from AND period_to AND fk_tenant_id = @fk_tenant_id)
SET @prev_forecast_period = (SELECT max(forecast_period) FROM tmr_calendar WHERE fk_tenant_id = @fk_tenant_id AND forecast_period < @forecast_period)
SET @budget_year = @forecast_period/100

PRINT 'START: Copy activity from result table ' + convert(varchar(400),GETDATE());

DELETE FROM tmr_activity WHERE fk_tenant_id = @fk_tenant_id AND forecast_period = @forecast_period;


DECLARE @param_value_1 varchar(50) = (SELECT param_value FROM vw_tco_parameters WHERE param_name = 'FINPLAN_LEVEL_1' AND active = 1 AND fk_tenant_id = @fk_tenant_id)
DECLARE @param_value_2 varchar(50) = (SELECT param_value FROM vw_tco_parameters WHERE param_name = 'FINPLAN_LEVEL_2' AND active = 1 AND fk_tenant_id = @fk_tenant_id)
--select * from tmr_activity WHERE fk_tenant_id = @tenant_id

DROP TABLE IF EXISTS  #helptab_activity


SELECT pk_id,fk_tenant_id, fk_indicator_code, org_id, org_level, service_id, budget_year 
INTO #helptab_activity
from tco_activity_indicator
WHERE fk_tenant_id = @fk_tenant_id AND budget_year = @budget_year



DROP TABLE IF EXISTS  #helptab_result

SELECT h.pk_id as fk_activity_id,rs.fk_tenant_id,s.measurment_criteria as activity_name,@forecast_period as forecast_period,
period_1,period_2,period_3,period_4,period_5,period_6,period_7,period_8,period_9,period_10,period_11,period_12,
getdate() AS updated, @user_id as updated_by,rs.total_year
INTO #helptab_result
FROM tmd_indicator_results rs
JOIN #helptab_activity h ON rs.fk_indicator_id = h.fk_indicator_code AND rs.org_id = h.org_id AND rs.org_level = h.org_level AND rs.service_id = h.service_id
AND rs.budget_year = h.budget_year AND rs.fk_tenant_id = h.fk_tenant_id
JOIN tco_indicator_setup s ON rs.fk_indicator_id = s.pk_indicator_code AND rs.fk_tenant_id = s.fk_tenant_id AND s.value_type != 'text'

UPDATE #helptab_result SET period_1 = '0' WHERE period_1 = ''
UPDATE #helptab_result SET period_2 = '0' WHERE period_2 = ''
UPDATE #helptab_result SET period_3 = '0' WHERE period_3 = ''
UPDATE #helptab_result SET period_4 = '0' WHERE period_4 = ''
UPDATE #helptab_result SET period_5 = '0' WHERE period_5 = ''
UPDATE #helptab_result SET period_6 = '0' WHERE period_6 = ''
UPDATE #helptab_result SET period_7 = '0' WHERE period_7 = ''
UPDATE #helptab_result SET period_8 = '0' WHERE period_8 = ''
UPDATE #helptab_result SET period_9 = '0' WHERE period_9 = ''
UPDATE #helptab_result SET period_10 = '0' WHERE period_10 = ''
UPDATE #helptab_result SET period_11 = '0' WHERE period_11 = ''
UPDATE #helptab_result SET period_12 = '0' WHERE period_12 = ''
UPDATE #helptab_result SET total_year = '0' WHERE total_year = ''


UPDATE #helptab_result SET period_1 = REPLACE(period_1,',','.') WHERE period_1 like '%,%'
UPDATE #helptab_result SET period_2 = REPLACE(period_2,',','.') WHERE period_2 like '%,%'
UPDATE #helptab_result SET period_3 = REPLACE(period_3,',','.') WHERE period_3 like '%,%'
UPDATE #helptab_result SET period_4 = REPLACE(period_4,',','.') WHERE period_4 like '%,%'
UPDATE #helptab_result SET period_5 = REPLACE(period_5,',','.') WHERE period_5 like '%,%'
UPDATE #helptab_result SET period_6 = REPLACE(period_6,',','.') WHERE period_6 like '%,%'
UPDATE #helptab_result SET period_7 = REPLACE(period_7,',','.') WHERE period_7 like '%,%'
UPDATE #helptab_result SET period_8 = REPLACE(period_8,',','.') WHERE period_8 like '%,%'
UPDATE #helptab_result SET period_9 = REPLACE(period_9,',','.') WHERE period_9 like '%,%'
UPDATE #helptab_result SET period_10 = REPLACE(period_10,',','.') WHERE period_10 like '%,%'
UPDATE #helptab_result SET period_11 = REPLACE(period_11,',','.') WHERE period_11 like '%,%'
UPDATE #helptab_result SET period_12 = REPLACE(period_12,',','.') WHERE period_12 like '%,%'
UPDATE #helptab_result SET total_year = REPLACE(total_year,',','.') WHERE total_year like '%,%'

PRINT 'Insert into tmr_activity'

INSERT INTO tmr_activity (fk_activity_id,fk_tenant_id,activity_name,forecast_period,period_1,period_2,period_3,period_4,period_5,period_6,period_7,period_8,period_9,period_10,period_11,period_12,updated,updated_by,total_year)
SELECT fk_activity_id,rs.fk_tenant_id,rs.activity_name,forecast_period,
CONVERT(DEC(18,3),rs.period_1),CONVERT(DEC(18,3),rs.period_2),CONVERT(DEC(18,3),rs.period_3), CONVERT(DEC(18,3),rs.period_4), CONVERT(DEC(18,3),rs.period_5),CONVERT(DEC(18,3),rs.period_6),CONVERT(DEC(18,3),rs.period_7),
CONVERT(DEC(18,3),rs.period_8),CONVERT(DEC(18,3),rs.period_9),CONVERT(DEC(18,3),rs.period_10),CONVERT(DEC(18,3),rs.period_11),CONVERT(DEC(18,3),rs.period_12),updated, updated_by,CONVERT(DEC(18,3),rs.total_year)
FROM #helptab_result rs 


PRINT 'FINISH: Copy activity from result table ' + convert(varchar(400),GETDATE());


RETURN 0
