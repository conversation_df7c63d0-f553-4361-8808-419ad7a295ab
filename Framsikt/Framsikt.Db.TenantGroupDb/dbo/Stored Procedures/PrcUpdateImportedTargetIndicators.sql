CREATE OR ALTER PROCEDURE [dbo].[PrcUpdateImportedTargetIndicators]
	@tenant_id int,
	@user_id int,
	@budget_year int,
	@isServiceIdSetup bit,
	@serviceLevel int
AS
	--update framsikt_reference based on external reference
	update tco_stage_target_indicators_import set framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	from tco_stage_target_indicators_import
	inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_target_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	(tco_stage_target_indicators_import.external_reference is not null or tco_stage_target_indicators_import.external_reference <> '') and
	(tco_stage_target_indicators_import.framsikt_reference is null or tco_stage_target_indicators_import.framsikt_reference = '') and
	tco_stage_target_indicators_import.error_count = 0;

	--update external_reference based on framsikt reference
	update tco_stage_target_indicators_import set external_reference = tco_indicator_setup.external_reference
	from tco_stage_target_indicators_import
	inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_target_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	(tco_stage_target_indicators_import.framsikt_reference is not null or tco_stage_target_indicators_import.framsikt_reference <> '') and
	(tco_stage_target_indicators_import.external_reference is null or tco_stage_target_indicators_import.external_reference = '') and
	tco_stage_target_indicators_import.error_count = 0;

	--update fk_indicator_code based on framsikt reference
	update tco_stage_target_indicators_import set fk_indicator_code = tco_indicator_setup.pk_indicator_code
	from tco_stage_target_indicators_import
	inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_target_indicators_import.framsikt_reference = tco_indicator_setup.fk_framsikt_indicator_code
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	--tco_indicator_setup.indicator_type = 2 and
	(tco_stage_target_indicators_import.framsikt_reference is not null or tco_stage_target_indicators_import.framsikt_reference <> '') and
	(tco_stage_target_indicators_import.fk_indicator_code is null) and
	tco_stage_target_indicators_import.error_count = 0;

	--update fk_indicator_code based on external reference
	update tco_stage_target_indicators_import set fk_indicator_code = tco_indicator_setup.pk_indicator_code
	from tco_stage_target_indicators_import
	inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
								   and tco_stage_target_indicators_import.external_reference = tco_indicator_setup.external_reference
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	--tco_indicator_setup.indicator_type = 2 and
	(tco_stage_target_indicators_import.external_reference is not null or tco_stage_target_indicators_import.external_reference <> '') and
	(tco_stage_target_indicators_import.fk_indicator_code is null) and
	tco_stage_target_indicators_import.error_count = 0;

	-- update columns based on fk indicator code
	-- fk_target_id, fk_target_detail_id, fk_target_distribution_id
	update tco_stage_target_indicators_import set fk_target_id = tco_targets_distribution.fk_target_id, 
												  fk_target_detail_id = tfp_effect_target_detail.pk_id,
												  fk_target_distribution_id = tco_targets_distribution.pk_target_distribution_id
    from tco_stage_target_indicators_import
	inner join tco_targets_distribution on tco_targets_distribution.fk_tenant_id = tco_stage_target_indicators_import.fk_tenant_id and
									tco_targets_distribution.org_id = tco_stage_target_indicators_import.org_id and
									tco_targets_distribution.org_level = tco_stage_target_indicators_import.org_level and
									tco_targets_distribution.service_id = tco_stage_target_indicators_import.service_id 
	inner join tco_targets on tco_targets.pk_target_id = tco_targets_distribution.fk_target_id and
								tco_targets.fk_tenant_id = tco_targets_distribution.fk_tenant_id	
    inner join tfp_effect_target_detail on tco_targets_distribution.fk_tenant_id = tfp_effect_target_detail.fk_tenant_id
									   and tco_targets_distribution.fk_target_id = tfp_effect_target_detail.fk_target_id
									   and tco_targets_distribution.pk_target_distribution_id = tfp_effect_target_detail.fk_target_distribution_id
                                       and tco_stage_target_indicators_import.fk_indicator_code = tfp_effect_target_detail.fk_indicator_code
    inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
                                  and tco_stage_target_indicators_import.fk_indicator_code = tco_indicator_setup.pk_indicator_code
    where
    tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
    tco_stage_target_indicators_import.budget_year = @budget_year AND 
    tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_id_error = 0 and 
	tco_targets.budget_year = @budget_year AND
	tco_stage_target_indicators_import.org_level_str_error = 0 and 
	tco_stage_target_indicators_import.service_id_error = 0 and 
    (tco_stage_target_indicators_import.fk_indicator_code is not null) and
	tco_stage_target_indicators_import.error_count = 0;

	-- update measure_finance_plan column
	update tco_stage_target_indicators_import set measure_finance_plan = tco_targets.target_name    
    from tco_stage_target_indicators_import
	inner join tco_targets_distribution on tco_targets_distribution.fk_tenant_id = tco_stage_target_indicators_import.fk_tenant_id and
									tco_targets_distribution.org_id = tco_stage_target_indicators_import.org_id and
									tco_targets_distribution.org_level = tco_stage_target_indicators_import.org_level and
									tco_targets_distribution.service_id = tco_stage_target_indicators_import.service_id 
	inner join tco_targets on tco_targets.pk_target_id = tco_targets_distribution.fk_target_id and
								tco_targets.fk_tenant_id = tco_targets_distribution.fk_tenant_id	
    inner join tfp_effect_target_detail on tco_targets_distribution.fk_tenant_id = tfp_effect_target_detail.fk_tenant_id
									   and tco_targets_distribution.fk_target_id = tfp_effect_target_detail.fk_target_id
									   and tco_targets_distribution.pk_target_distribution_id = tfp_effect_target_detail.fk_target_distribution_id
                                       and tco_stage_target_indicators_import.fk_indicator_code = tfp_effect_target_detail.fk_indicator_code
    inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
                                  and tco_stage_target_indicators_import.fk_indicator_code = tco_indicator_setup.pk_indicator_code
    where
    tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
    tco_stage_target_indicators_import.budget_year = @budget_year AND 
    tco_stage_target_indicators_import.user_id = @user_id and
	tco_stage_target_indicators_import.org_id_error = 0 and 
	tco_targets.budget_year = @budget_year AND
	tco_stage_target_indicators_import.org_level_str_error = 0 and 
	tco_stage_target_indicators_import.service_id_error = 0 and 
    (tco_stage_target_indicators_import.measure_finance_plan is null or tco_stage_target_indicators_import.measure_finance_plan = '') and
	tco_stage_target_indicators_import.error_count = 0;

	-- update measurment_criteria column
	update tco_stage_target_indicators_import set measurment_criteria = tco_indicator_setup.measurment_criteria    
    from tco_stage_target_indicators_import
    inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id
                                    and tco_stage_target_indicators_import.fk_indicator_code = tco_indicator_setup.pk_indicator_code
    where
    tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
    tco_stage_target_indicators_import.budget_year = @budget_year AND 
    tco_stage_target_indicators_import.user_id = @user_id and
    (tco_stage_target_indicators_import.measurment_criteria is null or tco_stage_target_indicators_import.measurment_criteria = '') and
	tco_stage_target_indicators_import.error_count = 0;

	--Validate measurement_criteria
	update tco_stage_target_indicators_import set measurment_criteria_error = 1, 
													error_count = error_count + 1
	where
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	tco_stage_target_indicators_import.user_id = @user_id and
	(tco_stage_target_indicators_import.measurment_criteria is null or tco_stage_target_indicators_import.measurment_criteria = '');

	-- update indicator_value to empty if its null
	update tco_stage_target_indicators_import set indicator_value = ''
	from tco_stage_target_indicators_import
	inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id and
										tco_stage_target_indicators_import.fk_indicator_code = tco_indicator_setup.pk_indicator_code
	where 
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	(tco_stage_target_indicators_import.indicator_value is null) and 
	tco_stage_target_indicators_import.user_id = @user_id 

	-- validate indicator_value
	update tco_stage_target_indicators_import set indicator_value_error = 1, error_count = error_count + 1
	from tco_stage_target_indicators_import
	inner join tco_indicator_setup on tco_stage_target_indicators_import.fk_tenant_id = tco_indicator_setup.fk_tenant_id and
										tco_stage_target_indicators_import.fk_indicator_code = tco_indicator_setup.pk_indicator_code
	where 
	tco_stage_target_indicators_import.fk_tenant_id = @tenant_id AND 
	tco_stage_target_indicators_import.budget_year = @budget_year AND 
	(tco_stage_target_indicators_import.indicator_value is not null and tco_stage_target_indicators_import.indicator_value <> '') and 
	tco_stage_target_indicators_import.user_id = @user_id and 
	(		
		(tco_indicator_setup.value_type = 'text' and len(tco_stage_target_indicators_import.indicator_value) > 50)
		or
		(tco_indicator_setup.value_type in ('n0','##0.0 \%','n1','n2','n3') and TRY_PARSE(tco_stage_target_indicators_import.indicator_value as decimal) is null)		 		
	)

RETURN 0
