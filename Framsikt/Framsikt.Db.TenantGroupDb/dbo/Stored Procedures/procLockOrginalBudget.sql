CREATE OR ALTER PROCEDURE procLockOrginalBudget
@tenant_id INT,
@budget_year INT,
@adjusmntCode VARCHAR(250),
@updatedBy int,
@updated datetime,
@udtDepartmentCode udtDepartmentCodes readonly
AS
BEGIN


SET NOCOUNT ON


DECLARE @continue INT
DECLARE @rowcount INT

	BEGIN
	SET  @continue = 1

	WHILE @continue = 1
	BEGIN
		PRINT 'Rows deleted ' + convert(varchar(50),GETDATE())
		SET ROWCOUNT 10000
		BEGIN TRANSACTION
				
	 DELETE tbu FROM tbu_trans_detail_original tbu
	 JOIN @udtDepartmentCode dep ON tbu.department_code = dep.department_code
	 WHERE fk_tenant_id=@tenant_id and budget_year = @budget_year 
		
		SET @rowcount = @@rowcount 
		COMMIT
		PRINT GETDATE()
		IF @rowcount = 0
		BEGIN
			SET @continue = 0
		END
	END

	END

SET ROWCOUNT 0




INSERT INTO tbu_trans_detail_original(
								 pk_id,
								 action_type, 
                                 allocation_pct,
                                 amount_year_1,
                                 budget_type,
                                 budget_year,
                                 bu_trans_id,
                                 department_code,
                                 [description],
                                 fk_account_code,
                                 fk_employment_id,
                                 fk_function_code,
                                 fk_key_id,
                                 fk_pension_type,
                                 fk_project_code,
                                 fk_tenant_id,
                                 free_dim_1,
                                 free_dim_2,
                                 free_dim_3,
                                 free_dim_4,
                                 holiday_flag,
                                 line_order,
                                 period,
                                 resource_id,
                                 tax_flag,
                                 total_amount,
                                 updated,
                                 updated_by,
								 fk_adjustment_code)

						(SELECT	 pk_id,
								 action_type, 
                                 allocation_pct,
                                 amount_year_1,
                                 budget_type,
                                 budget_year,
                                 bu_trans_id,
                                 dep.department_code,
                                 [description],
                                 fk_account_code,
                                 fk_employment_id,
                                 fk_function_code,
                                 fk_key_id,
                                 fk_pension_type,
                                 fk_project_code,
                                 fk_tenant_id,
                                 free_dim_1,
                                 free_dim_2,
                                 free_dim_3,
                                 free_dim_4,
                                 holiday_flag,
                                 line_order,
                                 period,
                                 resource_id,
                                 tax_flag,
                                 total_amount,
                                 @updated,
                                 @updatedBy,
								 @adjusmntCode 
						 FROM tbu_trans_detail tbu	
				JOIN @udtDepartmentCode dep ON tbu.department_code = dep.department_code
					WHERE tbu.fk_tenant_id=@tenant_id and tbu.budget_year = @budget_year )
;

update tbu 
set fk_adjustment_code=@adjusmntCode, updated = @updated, updated_by = @updatedBy 
FROM tbu_trans_detail tbu	
JOIN @udtDepartmentCode dep ON tbu.department_code = dep.department_code
WHERE tbu.fk_tenant_id=@tenant_id and tbu.budget_year = @budget_year 
;





SET NOCOUNT OFF 

END

