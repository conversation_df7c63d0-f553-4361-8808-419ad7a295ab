CREATE OR ALTER PROCEDURE [dbo].[prcDeleteTfpTransDetailTfpInvFromTbuTransDetail]
	@tenantId int,
	@budgetYear int,
	@finplanWithSalaryAccounts bit,
	@finplanWithoutSalaryAccounts bit,
	@investments bit
AS
begin
	if(@investments = 1 and @finplanWithSalaryAccounts = 0 and @finplanWithoutSalaryAccounts = 0)
	begin
		delete from tbu_trans_detail
		where fk_tenant_id = @tenantId
		and budget_year = @budgetYear
		and fk_account_code IN (
		select pk_account_code from tco_accounts
		where pk_tenant_id = @tenantId
		and fk_kostra_account_code IN (
		select fk_kostra_account_code from gmd_reporting_line
		where report = 'B2A'))

	end
	else if(@investments = 0 and @finplanWithSalaryAccounts = 1)
	begin
		delete from tbu_trans_detail
		where fk_tenant_id = @tenantId
		and budget_year = @budgetYear
		and fk_account_code NOT IN (
		select pk_account_code from tco_accounts
		where pk_tenant_id = @tenantId
		and fk_kostra_account_code IN (
		select fk_kostra_account_code from gmd_reporting_line
		where report = 'B2A'))
	end
	else if(@investments = 0 and @finplanWithoutSalaryAccounts = 1)
	begin
		delete from tbu_trans_detail
		where fk_tenant_id = @tenantId
		and budget_year = @budgetYear
		and fk_account_code NOT IN 
		(
			select pk_account_code from tco_accounts
			where pk_tenant_id = @tenantId
			and fk_kostra_account_code IN (
			select fk_kostra_account_code from gmd_reporting_line
			where report = 'B2A')
		)
		and fk_account_code not in 
		(
			select acc_value from tmd_acc_defaults where fk_tenant_id = @tenantId and link_type = 'STAFF_PLANNING' and acc_type = 'ACCOUNT' and module = 'BU'
			union
			select fk_account_code from tmd_pension_type where fk_tenant_id = @tenantId
			union
			select fk_account_code_aga from tmd_pension_type where fk_tenant_id = @tenantId
		)
	end
	else if(@investments = 1 and @finplanWithSalaryAccounts = 1)
	begin
		delete from tbu_trans_detail
		where fk_tenant_id = @tenantId
		and budget_year = @budgetYear
	end
	else if(@investments = 1 and @finplanWithoutSalaryAccounts = 1)
	begin
		delete from tbu_trans_detail
		where fk_tenant_id = @tenantId
		and budget_year = @budgetYear
		and fk_account_code not in 
		(
			select acc_value from tmd_acc_defaults where fk_tenant_id = @tenantId and link_type = 'STAFF_PLANNING' and acc_type = 'ACCOUNT' and module = 'BU'
			union
			select fk_account_code from tmd_pension_type where fk_tenant_id = @tenantId
			union
			select fk_account_code_aga from tmd_pension_type where fk_tenant_id = @tenantId
		)
	end
end
