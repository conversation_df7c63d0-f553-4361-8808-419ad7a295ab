CREATE OR ALTER PROCEDURE [dbo].[prcimportAccountingDetailsFromStaging]  
 @TenantID int,  
 @UserID int ,  
 @budgetYear int,  
 @jobID bigint,  
 @importintoAccoutingData bit,
 @importintoAccoutingDataDetail bit,
 @updateJobIdInStgTable bit 
AS  
BEGIN   
  
 DECLARE @recordCount int

   IF(@updateJobIdInStgTable=1)
 BEGIN 
update tbu_stage_accounting_import
set job_Id=@jobID
where [user_id]=@UserID and fk_tenant_id=@TenantID and gl_year=@budgetYear
 END

 IF ((@importintoAccoutingData = 0 AND @importintoAccoutingDataDetail =0) OR (@importintoAccoutingData = 1 AND @importintoAccoutingDataDetail = 1))
 BEGIN
	  
	   SET @recordCount = (SELECT COUNT(1) FROM tbu_stage_accounting_import WHERE [user_id] = @UserID and fk_tenant_id = @TenantID and gl_year = @budgetYear AND job_Id =@jobID )

	   INSERT tfp_accounting_data  
	   (fk_tenant_id,fk_account_code,department_code,fk_function_code,fk_project_code,asset_code,  
	   free_dim_1,free_dim_2,free_dim_3,free_dim_4,  
	   description,gl_year,period,fk_prog_code,amount,updated,updated_by)    
	   SELECT  fk_tenant_id,account_code,department_code,function_code, project_code,'' as assetCode,  
	   ISNULL(free_dim_1,''),ISNULL(free_dim_2,''),ISNULL(free_dim_3,''),ISNULL(free_dim_4,''),'',gl_year,period,prog_code,  
	   sum(amount),getutcdate() as updated ,@userid as updated_by   
	   from tbu_stage_accounting_import WHERE fk_tenant_id = @TenantID and gl_year = @budgetYear  
	   AND job_Id =@jobID
	   group by fk_tenant_id,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,gl_year,period,prog_code
  
	   UPDATE tco_job_status Set  steps_completed= steps_completed + @recordCount where pk_id= @jobID   and fk_tenant_id = @TenantID  
  
	   INSERT tfp_accounting_data_detail  
	   (fk_tenant_id,transaction_id,fk_account_code,department_code,fk_function_code,  
	   fk_project_code,asset_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,description,  
	   gl_year,period,fk_prog_code,amount,tax_code,supplier_id,supplier_name,customer_id,customer_name,  
	   transaction_date,erp_user,last_update,ext_inv_ref,invoice_date,updated,updated_by, resource)    
	   SELECT fk_tenant_id,transaction_id,account_code,department_code,function_code,  
	   project_code,'' as asset_code,ISNULL(free_dim_1,''),ISNULL(free_dim_2,''),ISNULL(free_dim_3,''),ISNULL(free_dim_4,''),description,  
	   gl_year,period,prog_code,amount,tax_code,supplier_id,supplier_name,customer_id,customer_name,  
	   transaction_date,erp_user,last_update,ext_inv_ref,invoice_date,getutcdate(),@userid, resource    
	   from   tbu_stage_accounting_import   
	   WHERE fk_tenant_id = @TenantID and gl_year = @budgetYear  
	   AND job_Id =@jobID
  
	   UPDATE tco_job_status Set  steps_completed= steps_completed + @recordCount where pk_id= @jobID   and fk_tenant_id = @TenantID  
  
 END

 IF (@importintoAccoutingData = 1 AND @importintoAccoutingDataDetail =0)
 BEGIN
	SET @recordCount = (SELECT COUNT(1) FROM tbu_stage_accounting_import WHERE [user_id] = @UserID and fk_tenant_id = @TenantID and gl_year = @budgetYear AND job_Id =@jobID  )

	INSERT tfp_accounting_data  
	(fk_tenant_id,fk_account_code,department_code,fk_function_code,fk_project_code,asset_code,  
	free_dim_1,free_dim_2,free_dim_3,free_dim_4,  
	description,gl_year,period,fk_prog_code,amount,updated,updated_by)    
	SELECT  fk_tenant_id,account_code,department_code,function_code, project_code,'' as assetCode,  
	ISNULL(free_dim_1,''),ISNULL(free_dim_2,''),ISNULL(free_dim_3,''),ISNULL(free_dim_4,''),description,gl_year,period,prog_code,  
	amount,getutcdate() as updated ,@userid as updated_by   
	from tbu_stage_accounting_import WHERE  fk_tenant_id = @TenantID and gl_year = @budgetYear  
	AND job_Id =@jobID
  
	UPDATE tco_job_status Set  steps_completed= steps_completed + @recordCount where pk_id= @jobID   and fk_tenant_id = @TenantID  
 END

 IF (@importintoAccoutingData = 0 AND @importintoAccoutingDataDetail =1)
 BEGIN
		
	   SET @recordCount = (SELECT COUNT(1) FROM tbu_stage_accounting_import WHERE [user_id] = @UserID and fk_tenant_id = @TenantID and gl_year = @budgetYear AND job_Id =@jobID)
  
	   INSERT tfp_accounting_data_detail  
	   (fk_tenant_id,transaction_id,fk_account_code,department_code,fk_function_code,  
	   fk_project_code,asset_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,description,  
	   gl_year,period,fk_prog_code,amount,tax_code,supplier_id,supplier_name,customer_id,customer_name,  
	   transaction_date,erp_user,last_update,ext_inv_ref,invoice_date,updated,updated_by, resource)    
	   SELECT fk_tenant_id,transaction_id,account_code,department_code,function_code,  
	   project_code,'' as asset_code,ISNULL(free_dim_1,''),ISNULL(free_dim_2,''),ISNULL(free_dim_3,''),ISNULL(free_dim_4,''),description,  
	   gl_year,period,prog_code,amount,tax_code,supplier_id,supplier_name,customer_id,customer_name,  
	   transaction_date,erp_user,last_update,ext_inv_ref,invoice_date,getutcdate(),@userid, resource    
	   from   tbu_stage_accounting_import   
	   WHERE fk_tenant_id = @TenantID and gl_year = @budgetYear  
	  AND job_Id =@jobID
  
	   UPDATE tco_job_status Set  steps_completed= steps_completed + @recordCount where pk_id= @jobID   and fk_tenant_id = @TenantID  
 END

END
