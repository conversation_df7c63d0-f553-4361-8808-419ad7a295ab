CREATE OR ALTER PROCEDURE [dbo].[prcAutoAccessOrgRole]

AS

SELECT * INTO #users FROM tco_users

CREATE UNIQUE CLUSTERED INDEX [#ind_users]
ON [dbo].[#users] ([pk_id],[IsActive])


SELECT fk_user_id, tenant_id 
INTO #user_role FROM [tco_auth_user_role_mapping] a
JOIN gco_tenants t ON a.tenant_id = t.pk_id
JOIN vw_tco_parameters p ON a.tenant_id = p.fk_tenant_id AND p.param_name = 'ADMIN_ADD_ACCESS_DEPARTMENT' AND param_value = 'TRUE'
JOIN #users u ON a.fk_user_id = u.pk_id AND u.IsActive = 1
WHERE a.fk_role_id IN (1,2)
GROUP BY fk_user_id, tenant_id 


SELECT mp.tenant_id, MIN(us.pk_id) as system_user_id INTO #system_users
FROM tco_users us 
JOIN tco_auth_user_role_mapping mp ON us.pk_id = fk_user_id 
WHERE user_name like '%system%'
GROUP BY mp.tenant_id

SELECT * into #org_role
FROM tco_user_orgrole
WHERE 1= 2

CREATE UNIQUE CLUSTERED INDEX #IND_org_role ON #org_role (fk_user_id,fk_org_id,hierarchy_level,fk_tenant_id,fk_org_version )

INSERT INTO #org_role (fk_tenant_id,fk_org_id,fk_org_version,hierarchy_level,fk_user_id,isManager,isBuddelegate,isBelonging,updated,updated_by)
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_1,oh.fk_org_version,1,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
union all 
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_2,oh.fk_org_version,2,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_2 is not null AND oh.org_id_2 != ''
union all
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_3,oh.fk_org_version,3,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_3 is not null AND oh.org_id_3 != ''
union all
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_4,oh.fk_org_version,4,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_4 is not null AND oh.org_id_4 != ''
union all
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_5,oh.fk_org_version,5,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_5 is not null AND oh.org_id_5 != ''
union all
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_6,oh.fk_org_version,6,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh 
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_6 is not null AND oh.org_id_6 != ''
union all
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_7,oh.fk_org_version,7,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_7 is not null AND oh.org_id_7 != ''
union all
SELECT DISTINCT oh.fk_tenant_id,oh.org_id_8,oh.fk_org_version,8,ur.fk_user_id,0,1,0,getdate(),1002
FROM tco_org_hierarchy oh
JOIN #user_role ur ON oh.fk_tenant_id = ur.tenant_id
where oh.org_id_8 is not null AND oh.org_id_8 != ''

DELETE t FROM #org_role t
JOIN tco_user_orgrole o ON t.fk_tenant_id = o.fk_tenant_id AND t.fk_org_id = o.fk_org_id 
AND t.hierarchy_level = o.hierarchy_level AND t.fk_org_version = o.fk_org_version AND t.fk_user_id = o.fk_user_id


UPDATE uo SET uo.updated_by = su.system_user_id
FROM #org_role uo
JOIN #system_users su ON uo.fk_tenant_id = su.tenant_id
WHERE uo.updated_by = 1002

INSERT INTO tco_user_orgrole (fk_tenant_id,fk_org_id,fk_org_version,hierarchy_level,fk_user_id,isManager,isBuddelegate,isBelonging,updated,updated_by)
SELECT fk_tenant_id,fk_org_id,fk_org_version,hierarchy_level,fk_user_id,isManager,isBuddelegate,isBelonging,updated,updated_by 
FROM #org_role


RETURN 0
