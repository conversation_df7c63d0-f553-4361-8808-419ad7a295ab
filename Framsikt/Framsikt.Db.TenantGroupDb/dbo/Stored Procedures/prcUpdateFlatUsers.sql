
CREATE OR ALTER PROCEDURE [dbo].[prcUpdateFlatUsers]

@fk_user_id INT

AS

CREATE TABLE #hlptab1(
    [fk_user_id] INT NOT NULL, 
    [first_name] NVARCHAR(50) NOT NULL, 
    [last_name] NVARCHAR(50) NOT NULL,
	[IsActive] BIT NOT NULL
)


INSERT INTO #hlptab1
SELECT 
pk_id
,first_name
,last_name
,IsActive
FROM tco_users
GROUP BY pk_id, first_name, last_name, IsActive


--START MERGE OPERATION--

MERGE flat_users AS TARGET
USING #hlptab1 AS SOURCE
ON SOURCE.fk_user_id = TARGET.fk_user_id
WHEN NOT MATCHED BY TARGET THEN 
	INSERT (
	fk_user_id
	,first_name
	,last_name
	,IsActive
	,updated
	,updated_by)
	VALUES
	(SOURCE.fk_user_id
	,SOURCE.first_name
	,SOURCE.last_name
	,SOURCE.IsActive
	,GETDATE()
	,@fk_user_id
	)

	WHEN MATCHED THEN UPDATE SET

	 TARGET.first_name = SOURCE.first_name
	,TARGET.last_name = SOURCE.last_name
	,TARGET.IsActive = SOURCE.IsActive

	WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO
