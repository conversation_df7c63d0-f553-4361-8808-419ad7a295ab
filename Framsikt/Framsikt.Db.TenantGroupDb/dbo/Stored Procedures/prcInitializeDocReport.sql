

/*

SEEE<PERSON> LIKE THIS IS NOT IN USE. AND HAS BEEN REPLACED BY [prcInitializeFinplanDocument]



*/





CREATE OR ALTER PROCEDURE [dbo].[prcInitializeDocReport]

       -- Add the parameters for the stored procedure here
AS

DECLARE @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @timestamp datetime2
DECLARE @data_updated INT
DECLARE @current_year INT
DECLARE @next_year INT
DECLARE @continue INT
DECLARE @rowcount INT

DECLARE @changed_table TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);


DECLARE @buddoc1 TABLE(
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_change_id] [int] NOT NULL,
	[fp_level_1_value] [nvarchar](25) NULL,
	[fp_level_2_value] [nvarchar](25) NULL,
	[action_type] [int] NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[pk_action_id] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[service_id_1] [nvarchar](25) NULL,
	[service_name_1] [nvarchar](100) NULL,
	[service_id_2] [nvarchar](25) NULL,
	[service_name_2] [nvarchar](100) NULL,
	[service_id_3] [nvarchar](25) NULL,
	[service_name_3] [nvarchar](100) NULL,
	[service_id_4] [nvarchar](25) NULL,
	[service_name_4] [nvarchar](100) NULL,
	[service_id_5] [nvarchar](25) NULL,
	[service_name_5] [nvarchar](100) NULL,
	[org_id_1] [nvarchar](25) NOT NULL,
	[org_name_1] [nvarchar](100) NOT NULL,
	[org_id_2] [nvarchar](25) NOT NULL,
	[org_name_2] [nvarchar](100) NOT NULL,
	[org_id_3] [nvarchar](25) NOT NULL,
	[org_name_3] [nvarchar](100) NOT NULL,
	[org_id_4] [nvarchar](25) NOT NULL,
	[org_name_4] [nvarchar](100) NOT NULL,
	[org_id_5] [nvarchar](25) NOT NULL,
	[org_name_5] [nvarchar](100) NOT NULL,
	[org_shortname_1] [nvarchar](25) NOT NULL,
	[org_shortname_2] [nvarchar](25) NOT NULL,
	[org_shortname_3] [nvarchar](25) NOT NULL,
	[org_shortname_4] [nvarchar](25) NOT NULL,
	[org_shortname_5] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[budget_amount] [decimal](18, 2) NOT NULL,
	[year_1_amount] [decimal](18, 2) NOT NULL,
	[year_2_amount] [decimal](18, 2) NOT NULL,
	[year_3_amount] [decimal](18, 2) NOT NULL,
	[year_4_amount] [decimal](18, 2) NOT NULL,
	[description] [nvarchar](max) NULL,
	[gl_amount] [decimal](18, 3) DEFAULT 0 NOT NULL)

	
DECLARE @buddoc2 TABLE(
	[pk_id] [uniqueidentifier] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_change_id] [int] NOT NULL,
	[fp_level_1_value] [nvarchar](25) NULL,
	[fp_level_2_value] [nvarchar](25) NULL,
	[action_type] [int] NOT NULL,
	[fk_alter_code] [nvarchar](25) NOT NULL,
	[pk_action_id] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[service_id_1] [nvarchar](25) NULL,
	[service_name_1] [nvarchar](100) NULL,
	[service_id_2] [nvarchar](25) NULL,
	[service_name_2] [nvarchar](100) NULL,
	[service_id_3] [nvarchar](25) NULL,
	[service_name_3] [nvarchar](100) NULL,
	[service_id_4] [nvarchar](25) NULL,
	[service_name_4] [nvarchar](100) NULL,
	[service_id_5] [nvarchar](25) NULL,
	[service_name_5] [nvarchar](100) NULL,
	[org_id_1] [nvarchar](25) NOT NULL,
	[org_name_1] [nvarchar](100) NOT NULL,
	[org_id_2] [nvarchar](25) NOT NULL,
	[org_name_2] [nvarchar](100) NOT NULL,
	[org_id_3] [nvarchar](25) NOT NULL,
	[org_name_3] [nvarchar](100) NOT NULL,
	[org_id_4] [nvarchar](25) NOT NULL,
	[org_name_4] [nvarchar](100) NOT NULL,
	[org_id_5] [nvarchar](25) NOT NULL,
	[org_name_5] [nvarchar](100) NOT NULL,
	[org_shortname_1] [nvarchar](25) NOT NULL,
	[org_shortname_2] [nvarchar](25) NOT NULL,
	[org_shortname_3] [nvarchar](25) NOT NULL,
	[org_shortname_4] [nvarchar](25) NOT NULL,
	[org_shortname_5] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[gl_amount] [decimal](18, 3) DEFAULT 0 NOT NULL,
	[budget_amount] [decimal](18, 2) NOT NULL,
	[year_1_amount] [decimal](18, 2) NOT NULL,
	[year_2_amount] [decimal](18, 2) NOT NULL,
	[year_3_amount] [decimal](18, 2) NOT NULL,
	[year_4_amount] [decimal](18, 2) NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL,
UNIQUE CLUSTERED (fk_tenant_id,budget_year,fk_change_id,action_type,
fk_alter_code,pk_action_id,fk_account_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
fk_department_code,fk_function_code))





select @current_year = (SELECT datepart(year, getdate()));
select @next_year = @current_year + 1;

SET NOCOUNT ON


BEGIN 
	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@current_year FROM tco_module_mapping WHERE fk_module_id = 3;

	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@next_year  FROM tco_module_mapping WHERE fk_module_id = 3;
END



--SELECT * FROM @changed_table;

BEGIN
UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'BUDDOCFULL';
END 

BEGIN

select @timestamp = sysdatetime();
PRINT 'Process started at ' + convert(nvarchar(19),@timestamp);


RAISERROR ('START: Insert from FINPLAN', 0, 1) WITH NOWAIT;

-- FRA ØKONOMIPLAN

BEGIN

INSERT INTO @buddoc1 (fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount,budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,[description])
select th.fk_tenant_id, td.budget_year, td.fk_change_id,fp_level_1_value = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

fp_level_2_value = 
CASE	WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

th.action_type, td.fk_alter_code, th.pk_action_id, td.fk_account_code,td.project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,td.department_code,
td.function_code as fk_function_code, 0 as gl_amount,
0 AS budget_amount, SUM(td.year_1_amount) AS year_1_amount,SUM(td.year_2_amount) AS year_2_amount,SUM(td.year_3_amount) AS year_3_amount,SUM(td.year_4_amount) AS year_4_amount,
'' as description
from tfp_trans_header th
inner join tfp_trans_detail td on th.fk_tenant_id = td.fk_tenant_id and th.pk_action_id = td .fk_action_id
JOIN @changed_table ch ON td.fk_tenant_id = ch.fk_tenant_id AND td.budget_year = ch.budget_year 
left outer join tco_service_values sv on th.fk_tenant_id = sv.fk_tenant_id AND td.function_code = sv.fk_function_code
LEFT JOIN tco_org_version ov ON td.fk_tenant_id = ov.fk_Tenant_id and (td.budget_year)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on td.fk_tenant_id = oh.fk_Tenant_id and td.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT JOIN tco_parameters p1 ON p1.param_name = 'FINPLAN_LEVEL_1' AND p1.fk_tenant_id = td.fk_tenant_id AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.param_name = 'FINPLAN_LEVEL_2' AND p2.fk_tenant_id = td.fk_tenant_id AND p2.active = 1
WHERE 1=1 
GROUP BY th.fk_tenant_id, td.budget_year,td.fk_change_id,th.action_type, td.fk_alter_code, th.pk_action_id, td.fk_account_code,
td.project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,td.department_code,
td.function_code, p1.param_value, p2.param_value
;

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Insert FINPLAN at ' + convert(nvarchar(19),@timestamp);

RAISERROR ('START: Insert from org budget', 0, 1) WITH NOWAIT;

BEGIN

-- FRA BUDSJETT 

INSERT INTO @buddoc1 (fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount, budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,description)
select  tb.fk_tenant_id, tb.budget_year+1 as budget_year, 0 as fk_change_id, fp_level_1_value = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

fp_level_2_value = 
CASE	WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

tb.action_type, '' as fk_alter_code, 0 as pk_action_id, tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,tb.department_code,
tb.fk_function_code, 0 AS gl_amount,
sum(amount_year_1) AS budget_amount, 0 as year_1_amount,0 AS year_2_amount,0 year_3_amount,0 AS year_4_amount, ''  as description
from [tbu_trans_detail_original] tb
JOIN @changed_table ch ON tb.fk_tenant_id = ch.fk_tenant_id AND tb.budget_year = ch.budget_year -1
left outer join tco_service_values sv on tb.fk_tenant_id = sv.fk_tenant_id AND tb.fk_function_code = sv.fk_function_code
LEFT JOIN tco_org_version ov ON tb.fk_tenant_id = ov.fk_Tenant_id and (tb.budget_year+1)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on tb.fk_tenant_id = oh.fk_Tenant_id and tb.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT  JOIN tco_accounts ac ON tb.fk_tenant_id = ac.pk_tenant_id AND tb.fk_account_code = ac.pk_account_code
LEFT JOIN gco_kostra_accounts k ON ac.fk_kostra_account_code = k.pk_kostra_account_code
LEFT JOIN tco_parameters p1 ON p1.param_name = 'FINPLAN_LEVEL_1' AND p1.fk_tenant_id = tb.fk_tenant_id AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.param_name = 'FINPLAN_LEVEL_2' AND p2.fk_tenant_id = tb.fk_tenant_id AND p2.active = 1
--left outer join tco_functions tf on th.fk_tenant_id = tf.pk_tenant_id AND td.function_code = tf.pk_function_code
WHERE 1=1 
AND tb.fk_tenant_id NOT IN (SELECT fk_tenant_id FROM tco_parameters WHERE param_name = 'BMDOC_USE_REVISED' AND param_value = 'TRUE' and active = 1)
AND oh.fk_org_version IN (SELECT pk_org_version FROM tco_org_version a WHERE  a.active = 1 AND a.fk_tenant_id = oh.fk_tenant_id)
AND k.type = 'operations'
AND amount_year_1 != 0
GROUP BY tb.fk_tenant_id, tb.budget_year,tb.action_type, tb.fk_account_code,
tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,
oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,tb.department_code,
tb.fk_function_code, p1.param_value, p2.param_value
;

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END


select @timestamp = sysdatetime();
PRINT 'FINISH: Insert org budget at ' + convert(nvarchar(19),@timestamp);

RAISERROR ('START: Insert from revised budget', 0, 1) WITH NOWAIT;

BEGIN 

INSERT INTO @buddoc1 (fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount, budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,description)
select  tb.fk_tenant_id, tb.budget_year+1 as budget_year, 0 as fk_change_id, fp_level_1_value = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

fp_level_2_value = 
CASE	WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

tb.action_type, '' as fk_alter_code, 0 as pk_action_id, tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,tb.department_code,
tb.fk_function_code, 0 as gl_amount,
sum(amount_year_1) AS budget_amount, 0 as year_1_amount,0 AS year_2_amount,0 year_3_amount,0 AS year_4_amount,'' AS description
from [tbu_trans_detail] tb
JOIN @changed_table ch ON tb.fk_tenant_id = ch.fk_tenant_id AND tb.budget_year = ch.budget_year -1
left outer join tco_service_values sv on tb.fk_tenant_id = sv.fk_tenant_id AND tb.fk_function_code = sv.fk_function_code
LEFT JOIN tco_org_version ov ON tb.fk_tenant_id = ov.fk_Tenant_id and (tb.budget_year+1)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on tb.fk_tenant_id = oh.fk_Tenant_id and tb.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT  JOIN tco_accounts ac ON tb.fk_tenant_id = ac.pk_tenant_id AND tb.fk_account_code = ac.pk_account_code
LEFT JOIN gco_kostra_accounts k ON ac.fk_kostra_account_code = k.pk_kostra_account_code
LEFT JOIN tco_parameters p1 ON p1.param_name = 'FINPLAN_LEVEL_1' AND p1.fk_tenant_id = tb.fk_tenant_id AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.param_name = 'FINPLAN_LEVEL_2' AND p2.fk_tenant_id = tb.fk_tenant_id AND p2.active = 1
--left outer join tco_functions tf on th.fk_tenant_id = tf.pk_tenant_id AND td.function_code = tf.pk_function_code
WHERE 1=1 
AND tb.fk_tenant_id IN (SELECT fk_tenant_id FROM tco_parameters WHERE param_name = 'BMDOC_USE_REVISED' AND param_value = 'TRUE' and active = 1)
AND k.type = 'operations'
AND amount_year_1 != 0
GROUP BY tb.fk_tenant_id, tb.budget_year,tb.action_type, tb.fk_account_code,
tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,
oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,tb.department_code,
tb.fk_function_code,  p1.param_value, p2.param_value
;

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Insert revised budget at ' + convert(nvarchar(19),@timestamp)


RAISERROR ('START: Insert accounting data', 0, 1) WITH NOWAIT;


-- FRA REGNSKAP 

BEGIN

INSERT INTO @buddoc1 (fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount, budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,description)
select  tb.fk_tenant_id, tb.gl_year+2 as budget_year, 0 as fk_change_id, fp_level_1_value = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

fp_level_2_value = 
CASE	WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

5 as action_type, '' as fk_alter_code, 0 as pk_action_id, tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,tb.department_code,
tb.fk_function_code, SUM(amount) AS gl_amount,
0 AS budget_amount, 0 as year_1_amount,0 AS year_2_amount,0 year_3_amount,0 AS year_4_amount,'' AS description
from tfp_accounting_data tb
JOIN @changed_table ch ON tb.fk_tenant_id = ch.fk_tenant_id AND tb.gl_year = ch.budget_year -2
left outer join tco_service_values sv on tb.fk_tenant_id = sv.fk_tenant_id AND tb.fk_function_code = sv.fk_function_code
LEFT JOIN tco_org_version ov ON tb.fk_tenant_id = ov.fk_Tenant_id and (tb.gl_year+2)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on tb.fk_tenant_id = oh.fk_Tenant_id and tb.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
LEFT  JOIN tco_accounts ac ON tb.fk_tenant_id = ac.pk_tenant_id AND tb.fk_account_code = ac.pk_account_code
LEFT JOIN gco_kostra_accounts k ON ac.fk_kostra_account_code = k.pk_kostra_account_code
LEFT JOIN tco_parameters p1 ON p1.param_name = 'FINPLAN_LEVEL_1' AND p1.fk_tenant_id = tb.fk_tenant_id AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.param_name = 'FINPLAN_LEVEL_2' AND p2.fk_tenant_id = tb.fk_tenant_id AND p2.active = 1
--left outer join tco_functions tf on th.fk_tenant_id = tf.pk_tenant_id AND td.function_code = tf.pk_function_code
WHERE 1=1 
AND k.type = 'operations'
AND amount != 0
GROUP BY tb.fk_tenant_id, tb.gl_year, tb.fk_account_code,
tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,
oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,tb.department_code,
tb.fk_function_code,  p1.param_value, p2.param_value
;


IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Insert accounting data at ' + convert(nvarchar(19),@timestamp);


END

BEGIN 

RAISERROR ('START: Aggregating data into buddoc2', 0, 1) WITH NOWAIT;


INSERT INTO @buddoc2 (pk_id,fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount, budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,updated_by,updated)

SELECT NEWID() AS pk_id,fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,SUM(gl_amount), SUM(budget_amount),SUM(year_1_amount),SUM(year_2_amount),SUM(year_3_amount),SUM(year_4_amount),
 1002 AS updated_by, GETDATE() AS updated
FROM @buddoc1
GROUP BY fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Aggregating data into buddoc2 ' + convert(nvarchar(19),@timestamp);

BEGIN

RAISERROR ('START: Updating twh_buddoc_reports', 0, 1) WITH NOWAIT;

  MERGE [twh_buddoc_reports] AS T  
    USING  @buddoc2 AS S
ON  (T.fk_tenant_id = S.fk_tenant_id AND T.budget_year = S.budget_year AND T.fk_change_id=S.fk_change_id AND T.action_type=S.action_type
AND T.fk_alter_code=S.fk_alter_code AND T.pk_action_id=S.pk_action_id AND T.fk_account_code= S.fk_account_code AND T.fk_project_code=S.fk_project_code 
AND T.free_dim_1=S.free_dim_1 AND T.free_dim_2=S.free_dim_2 AND T.free_dim_3= S.free_dim_3 AND T.free_dim_4=S.free_dim_4
AND T.fk_department_code=S.fk_department_code AND T.fk_function_code=S.fk_function_code)
WHEN MATCHED THEN   
        UPDATE SET fp_level_1_value = S.fp_level_1_value, fp_level_2_value = S.fp_level_2_value, 
		service_id_1 = S.service_id_1,service_name_1=S.service_name_1,service_id_2=S.service_id_2,service_name_2=S.service_name_2,
		service_id_3=S.service_id_3,service_name_3=S.service_name_3,
		service_id_4=S.service_id_4,service_name_4=S.service_name_4,service_id_5=S.service_id_5,service_name_5=S.service_name_5,
		org_id_1=S.org_id_1,org_name_1=S.org_name_1,org_id_2=S.org_id_2,org_name_2=S.org_name_2,org_id_3=S.org_id_3,org_name_3=S.org_name_3,
		org_id_4=S.org_id_4,org_name_4=S.org_name_4,org_id_5=S.org_id_5,org_name_5=S.org_name_5,
		org_shortname_1=S.org_shortname_1,org_shortname_2=S.org_shortname_2,org_shortname_3=S.org_shortname_3,
		org_shortname_4=S.org_shortname_4,org_shortname_5=S.org_shortname_5,
		gl_amount=S.gl_amount, budget_amount=S.budget_amount,
		year_1_amount = S.year_1_amount,year_2_amount=S.year_2_amount,year_3_amount=S.year_3_amount,year_4_amount=S.year_4_amount
WHEN NOT MATCHED BY TARGET
     THEN INSERT 
	 (pk_id, fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount, budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,description, updated, updated_by
	 )
	 VALUES 
	 (NEWID(), S.fk_tenant_id,S.budget_year,S.fk_change_id,S.fp_level_1_value,S.fp_level_2_value,S.action_type,
S.fk_alter_code,S.pk_action_id,S.fk_account_code,S.fk_project_code, S.free_dim_1, S.free_dim_2, S.free_dim_3, S.free_dim_4,
S.service_id_1,S.service_name_1,S.service_id_2,S.service_name_2,S.service_id_3,S.service_name_3,S.service_id_4,S.service_name_4,
S.service_id_5,S.service_name_5,S.org_id_1,S.org_name_1,S.org_id_2,S.org_name_2,S.org_id_3,S.org_name_3,S.org_id_4,S.org_name_4,S.org_id_5,S.org_name_5,
S.org_shortname_1,S.org_shortname_2,S.org_shortname_3,S.org_shortname_4,S.org_shortname_5,S.fk_department_code,
S.fk_function_code,S.gl_amount, S.budget_amount,S.year_1_amount,S.year_2_amount,S.year_3_amount,S.year_4_amount,'', S.updated, S.updated_by
	 )
WHEN NOT MATCHED BY SOURCE
     THEN DELETE;

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Updating [twh_buddoc_reports] ' + convert(nvarchar(19),@timestamp);


BEGIN
UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name = 'BUDDOCFULL';
END

select @timestamp = sysdatetime();
PRINT 'Job ended ok at ' + convert(nvarchar(19),@timestamp)

GO


