CREATE OR ALTER PROCEDURE [dbo].[prc_update_budalloc_accountview]

	@tenant_id int,
	@budget_year int,
	@user_id int 

AS


declare	@org_version NVARCHAR(50) = (SELECT min(pk_org_version) FROM tco_org_version WHERE fk_tenant_id = @tenant_id AND @budget_year*100+1 BETWEEN period_from and period_to)


DECLARE @langstring_project NVARCHAR(255) 
DECLARE @param_inv_grouping VARCHAR(255)
DECLARE @default_change_id INT
DECLARE @param_value_18 nvarchar(500)
DECLARE @sub_header_id VARCHAR(25) 
DECLARE @aggregate_id VARCHAR(25) 
DECLARE @sub_header_id_ldip VARCHAR(25) 
DECLARE @aggregate_id_ldip VARCHAR(25) 
DECLARE @timestamp datetime2
DECLARE @last_period INT
DECLARE @language VARCHAR(10)
DECLARE @useChapter INT = 0


DECLARE @report1A varchar(25)
DECLARE @report1B varchar (25)
DECLARE @report3 varchar (25)
DECLARE @report2A varchar (25)

IF (SELECT COUNT(*) FROM tco_attributes WHERE fk_tenant_id = @tenant_id AND attribute_type = 'CHAPTER') >0
BEGIN
SET @useChapter = 1
END

SET @language = (SELECT language_preference FROM gco_tenants WHERE pk_id = @tenant_id)



DECLARE @tbl_reporting_line as table (report varchar(50)
, fk_kostra_account_code VARCHAR(25)
, line_result_id INT NULL
, line_result nvarchar(200) NULL
, line_group_id INT
, line_group varchar(200)
, line_item_id INT
, line_item varchar(150))


INSERT INTO @tbl_reporting_line (report,fk_kostra_account_code, line_result_id, line_result,line_group_id, line_group, line_item_id, line_item)
SELECT rl.report, rl.fk_kostra_account_code,rl.line_result_id, rl.line_result, rl.line_group_id, 
line_group = ISNULL(tg.description, ISNULL(lg.description,rl.line_group)),
line_item_id,
line_item =  ISNULL(ti.description, ISNULL(li.description, line_item))
FROM gmd_reporting_line rl
LEFT JOIN gco_language_strings lg ON lg.ID = rl.lang_string_lgroup AND lg.Language  = @language
LEFT JOIN gco_language_strings li ON li.ID = rl.lang_string_lnitem AND li.Language  = @language
LEFT JOIN gco_language_string_overrides_tenant tg ON tg.ID = rl.lang_string_lgroup AND tg.fk_tenant_id = @tenant_id AND tg.Language = @language
LEFT JOIN gco_language_string_overrides_tenant tI ON tI.ID = rl.lang_string_lnitem AND tI.fk_tenant_id = @tenant_id AND tI.Language = @language

SET @report1A = '54_DRIFTA'
SET @report1B = '54_DRIFTB'
SET @report3  = '54_OVDRIFT'
SET @report2A = '55_OVINV'


if (select count(*) from tco_budform_setup where fk_tenant_id = @tenant_id AND report = '54_DRIFTA') > 0
begin 
    SET @report1A = '54_DRIFTA'
end

if (select count(*) from tco_budform_setup where fk_tenant_id = @tenant_id AND report = '54_OVDRIFT') > 0
begin 
    SET @report3  = '54_OVDRIFT'
end

if (select count(*) from tco_budform_setup where fk_tenant_id = @tenant_id AND report = '55_OVINV') > 0
begin 
    SET @report2A = '55_OVINV'
end


select @timestamp = sysdatetime();
PRINT 'START PROCESSING at ' + convert(nvarchar(19),@timestamp)


CREATE TABLE #temp_table_1
	(fk_tenant_id INT NOT NULL, 
	budget_year INT NOT NULL,
	fk_action_id INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    fk_project_code NVARCHAR(25) NOT NULL, 
	free_dim_1 NVARCHAR(25) NOT NULL, 
	free_dim_2 NVARCHAR(25) NOT NULL, 
	free_dim_3 NVARCHAR(25) NOT NULL, 
	free_dim_4 NVARCHAR(25) NOT NULL, 
	fk_adj_code NVARCHAR(25) NOT NULL,
	fk_change_id NVARCHAR(50) NOT NULL,
	fk_bud_auth_code NVARCHAR(8) NOT NULL,
	fk_attribute_id NVARCHAR(25) NOT NULL,
	revised_bud_amt_year DECIMAL (18, 2) NOT NULL,
	finplan_year_1_amount DECIMAL (18, 2) NOT NULL,
	org_budget_flag INT NOT NULL DEFAULT 1,
	[1A_line] bit DEFAULT ((0)) NOT NULL,
	[line_item_id] INT DEFAULT (0) NOT NULL,
	[line_item] NVARCHAR(200) DEFAULT(''),
	[line_group_id] INT DEFAULT (0) NOT NULL,
	[line_group] NVARCHAR(200) DEFAULT(''))


CREATE TABLE #valid_accounts( 
	[pk_id] INT NOT NULL IDENTITY, 
	pk_tenant_id INT NOT NULL,
    pk_account_code NVARCHAR(25) NOT NULL,
    fk_kostra_account_code NVARCHAR(25) NOT NULL)

INSERT INTO #valid_accounts (pk_tenant_id, pk_account_code, fk_kostra_account_code)

SELECT DISTINCT pk_tenant_id, pk_account_code, fk_kostra_account_code FROM 
(SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @budget_year BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
UNION
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @budget_year-1 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
UNION 
SELECT pk_tenant_id,pk_account_code, fk_kostra_account_code
FROM tco_accounts WHERE pk_tenant_id = @tenant_id AND @budget_year-2 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
) s

-- Logic to see if original budget is locked for a tenant @bud_lock = 0 means budget is locked


DECLARE @bud_lock_level INT = (
SELECT min(org_level) FROM tco_org_level WHERE fk_tenant_id = @tenant_id AND fk_org_version = @org_version AND fp_flag = 1)

DECLARE @bud_lock INT

SET @bud_lock = 

(select sum(antall) from 

(SELECT count(distinct oh.org_id_1) as antall FROM tco_org_hierarchy oh WHERE NOT EXISTS (
SELECT * FROM tco_application_flag f WHERE f.flag_name = 'LOCK_ORIGINAL_BUDGET'
AND f.budget_year = @budget_year AND oh.org_id_1 = f.flag_key_id AND oh.fk_tenant_id = f.fk_tenant_id
) AND oh.fk_org_version = @org_version AND oh.fk_tenant_id = @tenant_id AND @bud_lock_level = 1

union all 

SELECT count(distinct oh.org_id_2) as antall FROM tco_org_hierarchy oh WHERE NOT EXISTS (
SELECT * FROM tco_application_flag f WHERE f.flag_name = 'LOCK_ORIGINAL_BUDGET'
AND f.budget_year = @budget_year AND oh.org_id_2 = f.flag_key_id AND oh.fk_tenant_id = f.fk_tenant_id
) AND oh.fk_org_version = @org_version AND oh.fk_tenant_id = @tenant_id AND @bud_lock_level = 2

union all 

SELECT count(distinct oh.org_id_3) as antall FROM tco_org_hierarchy oh WHERE NOT EXISTS (
SELECT * FROM tco_application_flag f WHERE f.flag_name = 'LOCK_ORIGINAL_BUDGET'
AND f.budget_year = @budget_year AND oh.org_id_3 = f.flag_key_id AND oh.fk_tenant_id = f.fk_tenant_id
) AND oh.fk_org_version = @org_version AND oh.fk_tenant_id = @tenant_id AND @bud_lock_level = 3


union all 

SELECT count(distinct oh.org_id_4) as antall FROM tco_org_hierarchy oh WHERE NOT EXISTS (
SELECT * FROM tco_application_flag f WHERE f.flag_name = 'LOCK_ORIGINAL_BUDGET'
AND f.budget_year = @budget_year AND oh.org_id_4 = f.flag_key_id AND oh.fk_tenant_id = f.fk_tenant_id
) AND oh.fk_org_version = @org_version AND oh.fk_tenant_id = @tenant_id AND @bud_lock_level = 4

union all 

SELECT count(distinct oh.org_id_5) as antall FROM tco_org_hierarchy oh WHERE NOT EXISTS (
SELECT * FROM tco_application_flag f WHERE f.flag_name = 'LOCK_ORIGINAL_BUDGET'
AND f.budget_year = @budget_year AND oh.org_id_5 = f.flag_key_id AND oh.fk_tenant_id = f.fk_tenant_id
) AND oh.fk_org_version = @org_version AND oh.fk_tenant_id = @tenant_id AND @bud_lock_level = 5

) S
)


IF (SELECT count(*) FROM tco_parameters  WHERE param_name = 'LOCK_ORIGINAL_BUDGET'  AND fk_tenant_id = @tenant_id AND param_value = convert(varchar(6),@budget_year))
> 0 

BEGIN 
SET @bud_lock = 0
END


-- Begin fetching data ---


INSERT INTO #temp_table_1
	(fk_tenant_id, 
	budget_year,
	fk_action_id,
    fk_account_code, 
    fk_department_code, 
    fk_function_code, 
    fk_project_code, 
	free_dim_1, 
	free_dim_2, 
	free_dim_3, 
	free_dim_4, 
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	fk_attribute_id,
	revised_bud_amt_year,
	finplan_year_1_amount,
	org_budget_flag)
SELECT 
	a.fk_tenant_id, 
	a.budget_year,
	fk_action_id = 0,
    a.fk_account_code, 
    a.department_code, 
    a.fk_function_code, 
    a.fk_project_code, 
	a.free_dim_1, 
	a.free_dim_2, 
	a.free_dim_3, 
	a.free_dim_4, 
	a.fk_adjustment_code,
	change_id = '',
	fk_bud_auth_code= '',
	fk_attribute_id = '',
	revised_bud_amt_year = SUM(a.amount_year_1),
	finplan_year_1_amount = 0,
	org_budget_flag = 1
FROM tbu_trans_detail_original a
WHERE a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
GROUP BY  a.fk_tenant_id, 
	a.budget_year,
    a.fk_account_code, 
    a.department_code, 
    a.fk_function_code, 
    a.fk_project_code, 
	a.free_dim_1, 
	a.free_dim_2, 
	a.free_dim_3, 
	a.free_dim_4, 
	a.fk_adjustment_code

INSERT INTO #temp_table_1
	(fk_tenant_id, 
	budget_year,
	fk_action_id,
    fk_account_code, 
    fk_department_code, 
    fk_function_code, 
    fk_project_code, 
	free_dim_1, 
	free_dim_2, 
	free_dim_3, 
	free_dim_4, 
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	fk_attribute_id,
	revised_bud_amt_year,
	finplan_year_1_amount,
	org_budget_flag)
SELECT 
	a.fk_tenant_id, 
	a.budget_year,
	a.fk_action_id,
    a.fk_account_code, 
    a.department_code, 
    a.fk_function_code, 
    a.fk_project_code, 
	a.free_dim_1, 
	a.free_dim_2, 
	a.free_dim_3, 
	a.free_dim_4, 
	a.fk_adjustment_code,
	change_id = ISNULL(bc.approval_reference,'INGEN'),
	ISNULL(c.fk_bud_auth_code,'INGEN'),
	ISNULL(c.fk_attribute_id,'INGEN'),
	revised_bud_amt_year = SUM(a.amount_year_1),
	finplan_year_1_amount = 0,
	org_budget_flag = 0
FROM tbu_trans_detail a
JOIN tco_user_adjustment_codes c ON c.fk_tenant_id=a.fk_tenant_id and a.fk_adjustment_code = c.pk_adj_code 
and c.status=1 and c.budget_year=a.budget_year AND c.is_original_flag = 0 AND fk_change_id > 0
LEFT JOIN tfp_budget_changes BC ON c.fk_tenant_id = bc.fk_tenant_id AND c.fk_change_id = bc.pk_change_id
WHERE a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
GROUP BY  a.fk_tenant_id, 
	a.budget_year,
	a.fk_action_id,
    a.fk_account_code, 
    a.department_code, 
    a.fk_function_code, 
    a.fk_project_code, 
	a.free_dim_1, 
	a.free_dim_2, 
	a.free_dim_3, 
	a.free_dim_4, 
	a.fk_adjustment_code,
	c.fk_bud_auth_code,
	c.fk_change_id,
	bc.approval_reference, 
	c.fk_attribute_id


INSERT INTO #temp_table_1
	(fk_tenant_id, 
	budget_year,
	fk_action_id,
    fk_account_code, 
    fk_department_code, 
    fk_function_code, 
    fk_project_code, 
	free_dim_1, 
	free_dim_2, 
	free_dim_3, 
	free_dim_4, 
	fk_adj_code,
	fk_change_id,
	fk_bud_auth_code,
	fk_attribute_id,
	revised_bud_amt_year,
	finplan_year_1_amount,
	org_budget_flag)
select 
a.fk_tenant_id, 
	a.budget_year,
	a.fk_action_id,
    a.fk_account_code, 
    a.department_code, 
    a.function_code, 
    a.project_code, 
	a.free_dim_1, 
	a.free_dim_2, 
	a.free_dim_3, 
	a.free_dim_4, 
	ISNULL(c.pk_adj_code,'INGEN'),
	change_id = ISNULL(bc.approval_reference,''),
	ISNULL(c.fk_bud_auth_code,'INGEN'),
	ISNULL(c.fk_attribute_id, 'INGEN'),
	revised_bud_amt_year = 0,
	finplan_year_1_amount = SUM(a.year_1_amount),
	org_budget_flag = bc.org_budget_flag
FROM tfp_trans_detail a
JOIN tfp_trans_header h ON a.fk_tenant_id = h.fk_tenant_id AND a.fk_action_id = h.pk_action_id
JOIN tfp_budget_changes bc ON a.fk_tenant_id = bc.fk_tenant_id AND a.fk_change_id = bc.pk_change_id 
LEFT JOIN tco_user_adjustment_codes c ON a.fk_tenant_id = c.fk_tenant_id AND a.fk_adj_code = c.pk_adj_code 
AND c.budget_year=a.budget_year AND c.is_original_flag = 0
WHERE a.fk_tenant_id = @tenant_id
AND a.budget_year = @budget_year
AND (c.pk_adj_code is null or c.[status] = 1)
GROUP BY
a.fk_tenant_id, 
	a.budget_year,
	a.fk_action_id,
    a.fk_account_code, 
    a.department_code, 
    a.function_code, 
    a.project_code, 
	a.free_dim_1, 
	a.free_dim_2, 
	a.free_dim_3, 
	a.free_dim_4, 
	a.fk_adj_code,
	a.fk_change_id,
	c.fk_bud_auth_code,
	bc.org_budget_flag,
	bc.approval_reference,
	c.pk_adj_code,
	c.fk_attribute_id


UPDATE #temp_table_1 SET fk_bud_auth_code = 'INGEN' WHERE fk_bud_auth_code= '' AND fk_adj_code != ''

DELETE FROM #temp_table_1 
WHERE revised_bud_amt_year = 0 AND finplan_year_1_amount = 0

UPDATE #temp_table_1 SET fk_adj_code = 'INGEN' WHERE fk_adj_code = '' AND org_budget_flag = 0
UPDATE #temp_table_1 SET fk_change_id = 'INGEN' WHERE fk_change_id = '' AND org_budget_flag = 0
UPDATE #temp_table_1 SET fk_bud_auth_code = 'INGEN' WHERE fk_bud_auth_code = '' AND org_budget_flag = 0
UPDATE #temp_table_1 SET fk_attribute_id = 'INGEN' WHERE fk_attribute_id = '' AND org_budget_flag = 0

DELETE FROM [twh_budalloc_data_accountview] WHERE  fk_tenant_id = @tenant_id AND budget_year = @budget_year


INSERT INTO [twh_budalloc_data_accountview](
fk_tenant_id,
budget_year,
org_budget_flag,
[line_result_id], 
[line_result],     
[line_group_id], 
[line_group],     
[line_item_id], 
[line_item],
fk_adj_code,
fk_change_id,
fk_bud_auth_code,
fk_attribute_id,
revised_bud_amt_year,
finplan_year_1_amount,
updated,
updated_by,
fk_department_code)
SELECT 
a.fk_tenant_id,
a.budget_year,
a.org_budget_flag,	
rl.[line_result_id], 
rl.[line_result],     
rl.[line_group_id], 
rl.[line_group],     
rl.[line_item_id], 
rl.[line_item],
a.fk_adj_code,
a.fk_change_id,
a.fk_bud_auth_code,
a.fk_attribute_id,
SUM(a.revised_bud_amt_year),
SUM(a.finplan_year_1_amount),
updated = GETDATE(),
updated_by = @user_id,
fk_department_code = a.fk_department_code
FROM #temp_table_1 a 
JOIN #valid_accounts ac ON a.fk_account_code = ac.pk_account_code
JOIN @tbl_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = @report3
GROUP BY 
a.fk_tenant_id,
a.budget_year,
a.org_budget_flag,
rl.[line_result_id], 
rl.[line_result],     
rl.[line_group_id], 
rl.[line_group],     
rl.[line_item_id], 
rl.[line_item],
a.fk_adj_code,
a.fk_change_id,
a.fk_bud_auth_code,
a.fk_attribute_id,
a.fk_department_code


drop table if exists #rep_line_1B
drop table if exists #temp_table_1
drop table if exists #valid_accounts

RETURN 0

GO
