CREATE OR ALTER PROCEDURE [dbo].[prcValidateImportedBudgets]
	@tenant_id int,
	@user_id int,
	@budget_year int ,
	@isPeriodicKey bit ,
	@isLockBudget bit,
	@adjustmentCode nvarchar (16)
AS	 
 
    --Bug 145236: IMPORT - import YB - periodic key in template is not kept after upload to screen (stage table)
	--update tbu_stage_budget_import set periodicKey = b.fk_key_id
	--from tbu_stage_budget_import a
	--left outer join tco_accounts b on a.tenant_id = b.pk_tenant_id and a.account_code = b.pk_account_code
	--where a.tenant_id = @tenant_id
	--and a.user_id = @user_id
	--and a.budget_year = @budget_year
	--and @budget_year between datepart(yyyy,b.dateFrom) and datepart(yyyy, b.dateTo)
	--and b.fk_key_id is not null
	
	SELECT 
	user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4,sum(totalbudgetamount) totalbudgetamount,
	comments,period,periodicKey, adjustment_code,alter_code,service_area_code into #temp   from tbu_stage_budget_import where tenant_id=@tenant_id AND user_id = @user_id AND budget_year = @budget_year
	group by   
	user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4 ,
	comments,period,periodicKey,adjustment_code,alter_code,service_area_code


	DELETE FROM tbu_stage_budget_import where tenant_id=@tenant_id AND user_id = @user_id AND budget_year = @budget_year

	INSERT tbu_stage_budget_import (user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4 ,
	comments,period,periodicKey,adjustment_code,alter_code,totalbudgetamount,service_area_code)
	SELECT  user_id,tenant_id,budget_year,account_code,department_code,function_code,project_code,free_dim_1,free_dim_2,free_dim_3,free_dim_4 ,
	comments,period,periodicKey,adjustment_code,alter_code,totalbudgetamount,service_area_code  from #temp

	DROP TABLE #temp


	--Clear all the error information and set default action type --
	UPDATE tbu_stage_budget_import
	SET account_code_error = 0, 
		department_code_error = 0,
		function_code_error = 0,
		project_code_error = 0,
		free_dim_1_error = 0,
		free_dim_2_error = 0,
		free_dim_3_error = 0,
		free_dim_4_error = 0, 
		periodicKey_error = 0,
		alter_code_error = 0,
		adjustment_code_error = 0,
		period_error = 0,
		totalbudgetamount_error = 0,
		comments_error = 0,
		error_count = 1,
		service_area_code_error = 0
	WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year AND 
		tbu_stage_budget_import.user_id = @user_id;

	 
	 --Setting  isPeriodicKeyZero to 1 if periodic value is 0 --
	UPDATE tbu_stage_budget_import SET isPeriodicKeyZero = 1 
	WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
	tbu_stage_budget_import.budget_year = @budget_year AND 
	tbu_stage_budget_import.user_id = @user_id and tbu_stage_budget_import.periodicKey = '0';

	--Validate account codes for empty--
	UPDATE tbu_stage_budget_import
	SET account_code_error = 1, error_count = error_count + 1 
	FROM tbu_stage_budget_import 	
	WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND (tbu_stage_budget_import.account_code = '' 
		or tbu_stage_budget_import.account_code IS NULL) ;
	
	--Validate account codes--
	UPDATE tbu_stage_budget_import
	SET account_code_error = 1, error_count = error_count + 1 
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_accounts acc on 
		tbu_stage_budget_import.tenant_id = acc.pk_tenant_id AND 
		tbu_stage_budget_import.account_code = acc.pk_account_code AND 				
		tbu_stage_budget_import.budget_year BETWEEN datepart (year, acc.dateFrom) AND datepart (year, acc.dateTo) AND
		acc.isActive = 1 AND	
		tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year AND 
		tbu_stage_budget_import.user_id = @user_id  
	WHERE acc.pk_account_code IS NULL AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND tbu_stage_budget_import.account_code != '' 
		AND tbu_stage_budget_import.account_code IS NOT NULL  ;

	--Validate account codes--
	UPDATE tbu_stage_budget_import
	SET account_code_error = 1, error_count = error_count + 1 
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_accounts acc on tbu_stage_budget_import.tenant_id = acc.pk_tenant_id AND tbu_stage_budget_import.account_code = acc.pk_account_code 
	left outer join gco_kostra_accounts c on acc.fk_kostra_account_code = c.pk_kostra_account_code		
	WHERE  tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND tbu_stage_budget_import.account_code != '' 
		AND tbu_stage_budget_import.account_code IS NOT NULL 
		AND tbu_stage_budget_import.budget_year BETWEEN datepart (year, acc.dateFrom) AND datepart (year, acc.dateTo) AND
		acc.isActive = 1 and
		acc.pk_account_code IS not NULL and
		c.type <> 'operations';

	--Validate department codes for empty--
	--UPDATE tbu_stage_budget_import
	--SET department_code_error = 1, error_count = error_count + 1
	--FROM tbu_stage_budget_import 
	--WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
	--	tbu_stage_budget_import.budget_year = @budget_year  AND
	--	tbu_stage_budget_import.user_id = @user_id  
	--	AND (tbu_stage_budget_import.department_code = '' 
	--	or tbu_stage_budget_import.department_code IS NULL);

	--Validate department codes --
	--UPDATE tbu_stage_budget_import
	--SET department_code_error = 1, error_count = error_count + 1
	--FROM tbu_stage_budget_import 
	--LEFT JOIN tco_departments DEPTS ON 
	--	tbu_stage_budget_import.tenant_id = DEPTS.fk_tenant_id AND 
	--	tbu_stage_budget_import.department_code = DEPTS.pk_department_code AND 
	--	tbu_stage_budget_import.budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to AND
	--	DEPTS.[status] = 1 AND
	--	tbu_stage_budget_import.tenant_id = @tenant_id AND 
	--	tbu_stage_budget_import.budget_year = @budget_year AND 
	--	tbu_stage_budget_import.user_id = @user_id  AND
	--	@budget_year BETWEEN DEPTS.year_from AND DEPTS.year_to
	--WHERE DEPTS.pk_department_code IS NULL AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
	--	tbu_stage_budget_import.budget_year = @budget_year  AND
	--	tbu_stage_budget_import.user_id = @user_id  
	--	AND tbu_stage_budget_import.department_code != '' 
	--	AND tbu_stage_budget_import.department_code IS NOT NULL  ;

	--Validate Functions for empty--
	UPDATE tbu_stage_budget_import
	SET function_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND (tbu_stage_budget_import.function_code = '' 
		or tbu_stage_budget_import.function_code IS NULL);

 
	--Validate Functions--
	UPDATE tbu_stage_budget_import
	SET function_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_functions fn on 
		tbu_stage_budget_import.tenant_id = fn.pk_tenant_id AND 
		tbu_stage_budget_import.function_code = fn.pk_Function_code AND 		
		tbu_stage_budget_import.budget_year BETWEEN datepart (year, fn.dateFrom) AND datepart (year, fn.dateTo) AND
		fn.isActive = 1 AND
		tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year AND 
		tbu_stage_budget_import.user_id = @user_id  
	WHERE fn.pk_Function_code IS NULL AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND tbu_stage_budget_import.function_code != '' 
		AND tbu_stage_budget_import.function_code IS NOT NULL  ;

	--Validate Projects--

	UPDATE tbu_stage_budget_import
	SET project_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_projects prj on 
		tbu_stage_budget_import.tenant_id = prj.fk_tenant_id ANd
		tbu_stage_budget_import.project_code = prj.pk_project_code  AND 		
		tbu_stage_budget_import.budget_year BETWEEN datepart (year, prj.date_from) AND datepart (year, prj.date_to) AND
		prj.active = 1
	WHERE prj.pk_project_code IS NULL
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND tbu_stage_budget_import.project_code != '' 
		AND tbu_stage_budget_import.project_code IS NOT NULL  


-- Validate free dim 1 --

	UPDATE tbu_stage_budget_import
	SET free_dim_1_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_budget_import.free_dim_1 AND 
		tbu_stage_budget_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_1'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND tbu_stage_budget_import.free_dim_1 != '' 
		AND tbu_stage_budget_import.free_dim_1 IS NOT NULL;  

-- Validate free dim 2 --

	UPDATE tbu_stage_budget_import
	SET free_dim_2_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_budget_import.free_dim_2 AND 
		tbu_stage_budget_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_2'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id   
		AND tbu_stage_budget_import.free_dim_2 != '' 
		AND tbu_stage_budget_import.free_dim_2 IS NOT NULL;  

-- Validate free dim 3 --

	UPDATE tbu_stage_budget_import
	SET free_dim_3_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_budget_import.free_dim_3 AND 
		tbu_stage_budget_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_3'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id   
		AND tbu_stage_budget_import.free_dim_3 != '' 
		AND tbu_stage_budget_import.free_dim_3 IS NOT NULL;  

-- Validate free dim 4 --

	UPDATE tbu_stage_budget_import
	SET free_dim_4_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_free_dim_values frdm on frdm.free_dim_code = tbu_stage_budget_import.free_dim_4 AND 
		tbu_stage_budget_import.tenant_id = frdm.fk_tenant_id  AND frdm.free_dim_column = 'free_dim_4'
	WHERE frdm.free_dim_column IS NULL
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id   
		AND tbu_stage_budget_import.free_dim_4 != '' 
		AND tbu_stage_budget_import.free_dim_4 IS NOT NULL; 


-- commented Validate adjustment codes as part of story 29793
-- Validate adjustment codes--
--if exists (select * from vw_tco_parameters where fk_tenant_id=@tenant_id and param_name='YBUD_USE_ADJCODE')
--begin
--    UPDATE tbu_stage_budget_import 
--	SET adjustment_code_error = 1, error_count = error_count + 1
--	FROM tbu_stage_budget_import 
--	LEFT JOIN tco_adjustment_codes adjc on tbu_stage_budget_import.tenant_id = adjc.fk_tenant_id
--	AND adjc.status = 1 AND  tbu_stage_budget_import.adjustment_code=adjc.pk_adjustment_code
--	WHERE 
--	 tbu_stage_budget_import.tenant_id = @tenant_id AND 
--		tbu_stage_budget_import.budget_year = @budget_year  AND
--		tbu_stage_budget_import.user_id = @user_id  
--		AND (tbu_stage_budget_import.adjustment_code = '' 
--		OR adjc.pk_adjustment_code IS NULL) 
--end
		
-- Validate alter codes--
if exists (select * from vw_tco_parameters where fk_tenant_id=@tenant_id and param_name='YBUD_USE_ALTERCODE')
begin
    UPDATE tbu_stage_budget_import 
	SET alter_code_error = 1, error_count = error_count + 1
	FROM tbu_stage_budget_import 
	LEFT JOIN tco_fp_alter_codes altc on tbu_stage_budget_import.tenant_id = altc.fk_tenant_id
	AND altc.status = 1 AND tbu_stage_budget_import.alter_code=altc.pk_alter_code
	WHERE 
	tbu_stage_budget_import.tenant_id = @tenant_id AND 
		tbu_stage_budget_import.budget_year = @budget_year  AND
		tbu_stage_budget_import.user_id = @user_id  
		AND (tbu_stage_budget_import.alter_code = '' 
		OR altc.pk_alter_code IS NULL)  
end

	IF (@isPeriodicKey = 1)
		BEGIN
		 
			-- UPDATING WITH DEFAULT PeriodicKey VALUES FOR NULL/0/BLANK ENTRIES 

			UPDATE tbu_stage_budget_import set periodicKey = (Select  param_value FROM vw_tco_parameters with(nolock) where param_name='DEFAULT_BUDGET_PER_KEY' and fk_tenant_id=@tenant_id)
			WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
				  tbu_stage_budget_import.budget_year = @budget_year AND 
				  tbu_stage_budget_import.user_id = @user_id AND
				  (tbu_stage_budget_import.periodicKey is NULL OR tbu_stage_budget_import.periodicKey ='') ;

			-- Validate PeriodicKey

			Select PeriodKeys.ID into #tempPeriodicKeys from 
			(select distinct key_id as ID,key_description from tco_periodic_key   where      fixed_flag  = 1 and     fk_tenant_id=0
			UNION ALL
			select distinct key_id,key_description from tco_periodic_key   where      fixed_flag  = 1 and     fk_tenant_id=@tenant_id
			UNION ALL
			select distinct key_id,key_description from tco_periodic_key   where   fixed_flag  <>  1 and     fk_tenant_id=0
			UNION ALL
			select distinct key_id,key_description from tco_periodic_key   where   fixed_flag  <>  1 and     fk_tenant_id=@tenant_id) PeriodKeys

			
			UPDATE tbu_stage_budget_import
			SET periodicKey_error = 1, error_count = error_count + 1
			FROM tbu_stage_budget_import 
			LEFT JOIN #tempPeriodicKeys frdm on Cast (frdm.ID as nvarchar(50)) = Cast (tbu_stage_budget_import.periodicKey  as nvarchar(50))
			where frdm.ID IS NULL AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
			tbu_stage_budget_import.budget_year = @budget_year  AND
			tbu_stage_budget_import.user_id = @user_id
			AND Cast (tbu_stage_budget_import.periodicKey as nvarchar(50)) != '0';

			DROP table #tempPeriodicKeys

		END
	ELSE
		BEGIN

			-- Validate Periods
			
			SELECT a.years  INTO #tempYears FROM (
			SELECT  CAST(@budget_year as nvarchar(10))+'01' as years  UNION  SELECT CAST(@budget_year as nvarchar(10))+'02' 
			UNION  SELECT CAST(@budget_year as nvarchar(10))+'03' UNION  SELECT CAST(@budget_year as nvarchar(10))+'04'
			UNION  SELECT CAST(@budget_year as nvarchar(10))+'05' UNION  SELECT CAST(@budget_year as nvarchar(10))+'06'
			UNION  SELECT CAST(@budget_year as nvarchar(10))+'07' UNION  SELECT CAST(@budget_year as nvarchar(10))+'08'
			UNION  SELECT CAST(@budget_year as nvarchar(10))+'09' UNION  SELECT CAST(@budget_year as nvarchar(10))+'10'
			UNION  SELECT CAST(@budget_year as nvarchar(10))+'11' UNION  SELECT CAST(@budget_year as nvarchar(10))+'12' 
			)a

			UPDATE tbu_stage_budget_import set period_error = 1, error_count = error_count + 1
			WHERE tbu_stage_budget_import.tenant_id = @tenant_id AND 
				  tbu_stage_budget_import.budget_year = @budget_year AND 
				  tbu_stage_budget_import.user_id = @user_id AND
				  (tbu_stage_budget_import.period is NULL OR tbu_stage_budget_import.period ='' OR tbu_stage_budget_import.period  NOT IN (SELECT years from #tempYears)) ; 

			DROP TABLE #tempYears

		END

	-- Check that total amounts is not 0 --

	UPDATE tbu_stage_budget_import SET totalbudgetamount_error = 1, error_count = error_count +1
	WHERE totalbudgetamount is NULL
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
			tbu_stage_budget_import.budget_year = @budget_year AND 
			tbu_stage_budget_import.user_id = @user_id  ;


	-- Check if description is too long --

	UPDATE tbu_stage_budget_import SET comments_error = 1, error_count = error_count +1
	WHERE len(comments) >  255
	AND tbu_stage_budget_import.tenant_id = @tenant_id AND 
			tbu_stage_budget_import.budget_year = @budget_year AND 
			tbu_stage_budget_import.user_id = @user_id  ;

-- Validate service area codes and check for totalamountbudget for revised budget only--
if (@isLockBudget = 1)
	
	BEGIN

		DECLARE @service_org_level VARCHAR(20);
		DECLARE @org_version VARCHAR(20);
		DECLARE @frame_check_role VARCHAR(50);

		SELECT @service_org_level =  param_value from vw_tco_parameters where fk_tenant_id=@tenant_id and param_name='YB_IMPORT_FRAME_CONTROL' and active=1;
		SELECT @frame_check_role =  param_value from vw_tco_parameters where fk_tenant_id=@tenant_id and param_name='YB_IMPORT_FRAME_CONTROL_FOR_ROLE' and active=1;
		SELECT @org_version =  pk_org_version from tco_org_version where fk_tenant_id = @tenant_id AND  @budget_year * 100 + 1 BETWEEN period_from AND period_to;

		IF @service_org_level IS NOT NULL AND @frame_check_role IS NOT NULL
		BEGIN
			IF @service_org_level = 'service_id_2'
				BEGIN
				UPDATE tbu_stage_budget_import 
				SET tbu_stage_budget_import.service_area_code = tsv.service_id_2
				FROM tbu_stage_budget_import 
				INNER JOIN tco_service_values tsv on tbu_stage_budget_import.tenant_id = tsv.fk_tenant_id
				AND tbu_stage_budget_import.function_code=tsv.fk_function_code
				WHERE 
				tbu_stage_budget_import.tenant_id = @tenant_id AND 
				tbu_stage_budget_import.budget_year = @budget_year
				END
			ElSE IF @service_org_level = 'service_id_3'
			BEGIN
				UPDATE tbu_stage_budget_import 
				SET tbu_stage_budget_import.service_area_code = tsv.service_id_3
				FROM tbu_stage_budget_import 
				INNER JOIN tco_service_values tsv on tbu_stage_budget_import.tenant_id = tsv.fk_tenant_id
				AND tbu_stage_budget_import.function_code=tsv.fk_function_code
				WHERE 
				tbu_stage_budget_import.tenant_id = @tenant_id AND 
				tbu_stage_budget_import.budget_year = @budget_year
			END
			ELSE IF @service_org_level = 'org_id_2'
			BEGIN
				UPDATE tbu_stage_budget_import 
				SET tbu_stage_budget_import.service_area_code = toh.org_id_2
				FROM tbu_stage_budget_import 
				INNER JOIN tco_org_hierarchy toh on tbu_stage_budget_import.tenant_id = toh.fk_tenant_id
				AND tbu_stage_budget_import.department_code = toh.fk_department_code
				AND toh.fk_org_version = @org_version
				WHERE 
				tbu_stage_budget_import.tenant_id = @tenant_id AND 
				tbu_stage_budget_import.budget_year = @budget_year
			END	
			ElSE IF @service_org_level = 'org_id_3'
			BEGIN
				UPDATE tbu_stage_budget_import 
				SET service_area_code = toh.org_id_3
				FROM tbu_stage_budget_import 
				LEFT JOIN tco_org_hierarchy toh on tbu_stage_budget_import.tenant_id = toh.fk_tenant_id
				AND tbu_stage_budget_import.department_code = toh.fk_department_code
				AND toh.fk_org_version = @org_version
				WHERE 
				tbu_stage_budget_import.tenant_id = @tenant_id AND 
				tbu_stage_budget_import.budget_year = @budget_year
			END
			ELSE IF @service_org_level = 'org_id_4'
			BEGIN
				UPDATE tbu_stage_budget_import 
				SET tbu_stage_budget_import.service_area_code = toh.org_id_4
				FROM tbu_stage_budget_import 
				INNER JOIN tco_org_hierarchy toh on tbu_stage_budget_import.tenant_id = toh.fk_tenant_id
				AND tbu_stage_budget_import.department_code = toh.fk_department_code
				AND toh.fk_org_version = @org_version
				WHERE 
				tbu_stage_budget_import.tenant_id = @tenant_id AND 
				tbu_stage_budget_import.budget_year = @budget_year
			END	
			ElSE IF @service_org_level = 'org_id_5'
			BEGIN
				UPDATE tbu_stage_budget_import 
				SET tbu_stage_budget_import.service_area_code = toh.org_id_5
				FROM tbu_stage_budget_import 
				INNER JOIN tco_org_hierarchy toh on tbu_stage_budget_import.tenant_id = toh.fk_tenant_id
				AND tbu_stage_budget_import.department_code = toh.fk_department_code
				AND toh.fk_org_version = @org_version
				WHERE 
				tbu_stage_budget_import.tenant_id = @tenant_id AND 
				tbu_stage_budget_import.budget_year = @budget_year
			END
		UPDATE tbu_stage_budget_import
		SET service_area_code_error = 1
		WHERE 
			tbu_stage_budget_import.tenant_id = @tenant_id AND 
			tbu_stage_budget_import.budget_year = @budget_year AND 
			tbu_stage_budget_import.adjustment_code = @adjustmentCode AND
			tbu_stage_budget_import.service_area_code IN (
				SELECT service_area_code
				FROM tbu_stage_budget_import
				WHERE tbu_stage_budget_import.tenant_id = @tenant_id
				  AND tbu_stage_budget_import.budget_year = @budget_year
				  AND tbu_stage_budget_import.adjustment_code = @adjustmentCode
				GROUP BY tbu_stage_budget_import.service_area_code
				HAVING SUM(tbu_stage_budget_import.totalbudgetamount) != 0.00
			);
		END	
	END
RETURN 0