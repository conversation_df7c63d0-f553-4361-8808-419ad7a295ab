
CREATE OR ALTER PROCEDURE procUpdateBudgetLimitsPerService
@tenant_id INT,
@budget_year INT,
@user_id INT,
@org_version NVARCHAR(50),
@udtFinplanValues udtFinplanLevelValues readonly
AS


DECLARE @dep_table TABLE (
fk_account_code nvarchar(25) not null,
fk_department_code nvarchar(25) not null,
fk_function_code nvarchar(25) not null,
fk_tenant_id INT not null)


INSERT INTO @dep_table (fk_account_code, fk_department_code, fk_function_code,fk_tenant_id)
SELECT DISTINCT d.fk_account_code, d.fk_department_code, d.fk_function_code,d.fk_tenant_id
from tmd_fp_level_defaults d, @udtFinplanValues u
WHERE d.fp_level_1_value = u.fp_level1_value
AND d.fp_level_2_value = u.fp_level2_value
AND d.fk_tenant_id = u.fk_tenant_id
AND d.fk_org_version = @org_version

BEGIN
DELETE T FROM tfp_budget_limits T INNER JOIN @udtFinplanValues u ON u.fk_tenant_id = T.fk_tenant_id AND u.fp_level1_value =  T.fp_level_1_value AND u.fp_level2_value = T.fp_level_2_value
WHERE T.budget_year = @budget_year AND T.fk_tenant_id = @tenant_id;

--DELETE T FROM tfp_trans_detail T INNER JOIN @dep_table d ON t.fk_account_code = d.fk_account_code AND
--T.department_code = d.fk_department_code AND T.function_code = d.fk_function_code AND t.fk_tenant_id = d.fk_tenant_id
--WHERE budget_year = @budget_year AND T.fk_tenant_id = @tenant_id AND T.fk_action_id IN (
--SELECT pk_action_id FROM tfp_trans_header WHERE action_type IN (20,30,40,90) AND fk_tenant_id = @tenant_id );

END

BEGIN

INSERT INTO tfp_budget_limits (fk_tenant_id,budget_year,action_type,year_1_limit,year_2_limit,year_3_limit,year_4_limit,updated,updated_by,fp_level_1_value,fp_level_2_value, fk_org_id) 
SELECT  S.fk_tenant_id, S.budget_year, S.action_type, SUM(year_1_amount) as year_1_limit, SUM(year_2_amount) as year_2_limit, 
SUM(year_3_amount) as year_3_limit, SUM(year_4_amount) as year_4_limit,GETDATE() AS updated,@user_id AS updated_by,S.fp_level_1_value,S.fp_level_2_value, '' as fk_org_id
FROM 
(SELECT a.fk_tenant_id, b.budget_year, action_type = 
    CASE WHEN action_type = 9 THEN 90
        WHEN action_type = 21 THEN 20
        WHEN action_type = 31 THEN 30
        WHEN action_type = 41 THEN 40
        end,
b.year_1_amount, b.year_2_amount, b.year_3_amount, year_4_amount, 
fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END 
FROM tfp_trans_header a 
JOIN tfp_trans_detail b ON a.fk_tenant_id = b.fk_tenant_id AND a.pk_action_id = b.fk_action_id
JOIN tco_org_hierarchy oh ON b.fk_tenant_id = oh.fk_tenant_id AND b.department_code = oh.fk_department_code  and oh.fk_org_version = @org_version
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON sv.fk_tenant_id = b.fk_tenant_id AND sv.fk_function_code = b.function_code
WHERE a.fk_tenant_id  = @tenant_id
AND b.budget_year = @budget_year
AND a.action_type IN (9, 21, 31, 41)) S, @udtFinplanValues U
WHERE s.fp_level_1_value = u.fp_level1_value
AND s.fp_level_2_value = u.fp_level2_value
AND s.fk_tenant_id = u.fk_tenant_id
GROUP BY 
S.fk_tenant_id, S.budget_year, S.action_type, S.fp_level_1_value,S.fp_level_2_value;

END


BEGIN

INSERT INTO tfp_budget_limits (fk_tenant_id,budget_year,action_type,year_1_limit,year_2_limit,year_3_limit,year_4_limit,updated,updated_by,fp_level_1_value,fp_level_2_value, fk_org_id) 
SELECT DISTINCT A.fk_tenant_id, budget_year, action_type, 0 as year_1_limit, 0 as year_2_limit, 0 as year_3_limit, 0 as year_4_limit,
GETDATE() AS updated, @user_id as updated_by, fp_level_1_value, fp_level_2_value, '' as fk_org_id
FROM 
(SELECT oh.fk_tenant_id, @budget_year as budget_year,20 as action_type, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version and oh.fk_org_version = @org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
WHERE oh.fk_tenant_id = @tenant_id

union

SELECT oh.fk_tenant_id, @budget_year as budget_year,30 as action_type, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version and oh.fk_org_version = @org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
WHERE oh.fk_tenant_id = @tenant_id

union

SELECT oh.fk_tenant_id, @budget_year as budget_year,40 as action_type, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version and oh.fk_org_version = @org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
WHERE oh.fk_tenant_id = @tenant_id

union

SELECT oh.fk_tenant_id, @budget_year as budget_year,90 as action_type, fp_level_1_value = 
CASE    WHEN p1.param_value = 'org_id_1' THEN org_id_1
        WHEN p1.param_value = 'org_id_2' THEN org_id_2
        WHEN p1.param_value = 'org_id_3' THEN org_id_3
        WHEN p1.param_value = 'org_id_4' THEN org_id_4
        WHEN p1.param_value = 'org_id_5' THEN org_id_5
        WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END, fp_level_2_value = 
CASE    WHEN p2.param_value = 'org_id_1' THEN org_id_1
        WHEN p2.param_value = 'org_id_2' THEN org_id_2
        WHEN p2.param_value = 'org_id_3' THEN org_id_3
        WHEN p2.param_value = 'org_id_4' THEN org_id_4
        WHEN p2.param_value = 'org_id_5' THEN org_id_5
        WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1
        WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
        WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
        WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
        WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5
        ELSE ''
        END FROM tco_org_hierarchy oh
INNER JOIN tco_org_version ov ON ov.fk_tenant_id = oh.fk_tenant_id AND ov.pk_org_version = oh.fk_org_version AND ov.pk_org_version=@org_version and oh.fk_org_version = @org_version
JOIN tco_parameters p1 ON p1.fk_tenant_id = oh.fk_tenant_id AND p1.param_name = 'FINPLAN_LEVEL_1' AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.fk_tenant_id = oh.fk_tenant_id AND p2.param_name = 'FINPLAN_LEVEL_2' AND p2.active = 1
LEFT JOIN tco_service_values sv ON ov.fk_tenant_id = sv.fk_tenant_id
WHERE oh.fk_tenant_id = @tenant_id

) A
WHERE NOT EXISTS (SELECT * FROM tfp_budget_limits b WHERE A.fk_tenant_id = b.fk_tenant_id AND a.action_type = b.action_type
AND a.budget_year = b.budget_year AND a.fp_level_1_value = b.fp_level_1_value AND a.fp_level_2_value = b.fp_level_2_value)
GROUP BY A.fk_tenant_id, budget_year, action_type, fp_level_1_value, fp_level_2_value
ORDER BY A.fk_tenant_id, budget_year, action_type, fp_level_1_value, fp_level_2_value

END
