CREATE OR ALTER PROCEDURE [dbo].[prcInitializeFinplanDocument] @tenant_id INT = 0, @budget_year INT = 0

AS

DECLARE @StartDate datetime2 
DECLARE @EndDate datetime2
DECLARE @TimeUsed varchar(25)
DECLARE @timestamp datetime2
DECLARE @data_updated INT
DECLARE @current_year INT
DECLARE @next_year INT
DECLARE @continue INT
DECLARE @rowcount INT

DECLARE @changed_table TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);


DECLARE @changed_table_org TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);

DECLARE @changed_table_rev TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);

select @current_year = (SELECT datepart(year, getdate()));
select @next_year = @current_year + 1;

SET NOCOUNT ON

IF @budget_year = 0 AND @tenant_id = 0
BEGIN 
	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@current_year-1 FROM [dbo].[tco_module_mapping] WHERE fk_module_id = 3;

	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@current_year FROM [dbo].[tco_module_mapping] WHERE fk_module_id = 3;

	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id,@next_year FROM [dbo].[tco_module_mapping] WHERE fk_module_id = 3;


END


IF @budget_year != 0 AND @tenant_id = 0
BEGIN 
	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	SELECT distinct fk_tenant_id, @budget_year FROM [dbo].[tco_module_mapping] WHERE fk_module_id = 3 order by 1;
END

IF @budget_year != 0 AND @tenant_id != 0
BEGIN 
	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	VALUES (@tenant_id, @budget_year);
END

IF @budget_year = 0 AND @tenant_id != 0
BEGIN 
	INSERT INTO @changed_table (fk_tenant_id, budget_year)
	VALUES (@tenant_id, @current_year),
	(@tenant_id, @next_year)
	;
END


SELECT oh.*, ch.budget_year INTO #org_hierarchy
FROM tco_org_hierarchy oh
JOIN tco_org_version ov ON oh.fk_tenant_id = ov.fk_tenant_id AND oh.fk_org_version = ov.pk_org_version
JOIN @changed_table ch ON ov.fk_tenant_id = ch.fk_tenant_id AND ch.budget_year *100+1 between ov.period_from and ov.period_to

CREATE CLUSTERED INDEX #IND_org_hiearachy ON #org_hierarchy (fk_department_code, fk_tenant_id, budget_year)



INSERT INTO @changed_table_org (fk_tenant_id, budget_year)
SELECT fk_tenant_id, budget_year
FROM @changed_table
WHERE fk_tenant_id NOT IN (
SELECT fk_tenant_id FROM tco_parameters WHERE param_name = 'BMDOC_USE_REVISED' AND param_value = 'TRUE' and active = 1);


INSERT INTO @changed_table_rev (fk_tenant_id, budget_year)
SELECT fk_tenant_id, budget_year
FROM @changed_table
WHERE fk_tenant_id IN (
SELECT fk_tenant_id FROM tco_parameters WHERE param_name = 'BMDOC_USE_REVISED' AND param_value = 'TRUE' and active = 1);



BEGIN
UPDATE [twh_report_job_queue] SET run_flag = 1, updated = GETDATE() WHERE job_name = 'BUDDOCFULL';
END 

IF @tenant_id = 0
begin
Print 'Run script for auto access to users'
	
	exec [prcAutoAccessOrgRole]
end


BEGIN


CREATE TABLE #TEMP_ACC (
fk_tenant_id INT NOT NULL, 
budget_year INT NOT NULL,
fk_change_id INT NOT NULL,
fk_alter_code nvarchar(25) NOT NULL,
action_type INT NOT NULL, 
fk_action_id INT NOT NULL,
fk_account_code nvarchar(25) NOT NULL,
fk_project_code nvarchar(25) NOT NULL, 
free_dim_1 nvarchar(25) NOT NULL, 
free_dim_2 nvarchar(25) NOT NULL, 
free_dim_3 nvarchar(25) NOT NULL, 
free_dim_4 nvarchar(25) NOT NULL,
department_code nvarchar(25) NOT NULL, 
fk_function_code nvarchar(25) NOT NULL, 
description nvarchar(MAX)  NULL, 
gl_amount DEC(18,2) NOT NULL,
budget_amount DEC(18,2) NOT NULL, 
year_1_amount DEC(18,2) NOT NULL,
year_2_amount DEC(18,2) NOT NULL,
year_3_amount DEC(18,2) NOT NULL,
year_4_amount DEC(18,2) NOT NULL)



CREATE NONCLUSTERED INDEX [#temp_ind_1]
ON [dbo].[#TEMP_ACC] ([fk_tenant_id],[department_code])
INCLUDE ([budget_year],[fk_change_id],[fk_alter_code],[action_type],[fk_action_id],[fk_account_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[fk_function_code],[description],[gl_amount],[budget_amount],[year_1_amount],[year_2_amount],[year_3_amount],[year_4_amount])


RAISERROR ('START: Insert accounting data in temp table', 0, 1) WITH NOWAIT;

INSERT INTO #TEMP_ACC (fk_tenant_id, budget_year,fk_change_id,fk_alter_code,action_type, fk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
department_code, fk_function_code, description, gl_amount,
budget_amount, year_1_amount,year_2_amount,
year_3_amount,year_4_amount)
SELECT  tb.fk_tenant_id, ch.budget_year AS budget_year,0 as fk_change_id,convert(nvarchar(25),'') as fk_alter_code, 0 as action_type,0 AS fk_action_id, tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
tb.department_code, tb.fk_function_code, '' as description, SUM(amount) AS gl_amount,
convert(dec(18,2),0) AS budget_amount, convert(dec(18,2),0) as year_1_amount,convert(dec(18,2),0) AS year_2_amount,
convert(dec(18,2),0) year_3_amount,convert(dec(18,2),0) AS year_4_amount
FROM tfp_accounting_data tb
JOIN @changed_table ch ON tb.fk_tenant_id = ch.fk_tenant_id AND tb.gl_year = ch.budget_year -2
GROUP BY tb.fk_tenant_id, tb.gl_year, tb.fk_account_code,tb.fk_project_code, tb.free_dim_1, tb.free_dim_2, tb.free_dim_3, tb.free_dim_4,
tb.department_code, tb.fk_function_code,ch.budget_year


select @timestamp = sysdatetime();
PRINT 'FINISH: Insert accounting data in temp table at ' + convert(nvarchar(19),@timestamp);


RAISERROR ('START: Insert finplan data in temp table', 0, 1) WITH NOWAIT;

INSERT INTO #TEMP_ACC (fk_tenant_id, budget_year,fk_change_id,fk_alter_code,action_type, fk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
department_code, fk_function_code, description, gl_amount,
budget_amount, year_1_amount,year_2_amount,
year_3_amount,year_4_amount)
SELECT  td.fk_tenant_id, ch.budget_year,td.fk_change_id,td.fk_alter_code,th.action_type as action_type, th.pk_action_id, td.fk_account_code,td.project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
td.department_code, td.function_code, SUBSTRING (td.description,1,500), 0 AS gl_amount,
0 AS budget_amount, SUM(year_1_amount),SUM(year_2_amount),
SUM(year_3_amount),SUM(year_4_amount)
FROM tfp_trans_header th
JOIN tfp_trans_detail td on th.fk_tenant_id = td.fk_tenant_id and th.pk_action_id = td .fk_action_id
JOIN @changed_table ch ON td.fk_tenant_id = ch.fk_tenant_id AND td.budget_year = ch.budget_year 
GROUP BY td.fk_tenant_id, ch.budget_year, td.fk_change_id,td.fk_alter_code,th.action_type, th.pk_action_id, td.fk_account_code,td.project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
td.department_code, td.function_code, td.description


select @timestamp = sysdatetime();
PRINT 'FINISH: Insert finplan data in temp table at ' + convert(nvarchar(19),@timestamp);



RAISERROR ('START: Insert org_budget data in temp table', 0, 1) WITH NOWAIT;

INSERT INTO #TEMP_ACC (fk_tenant_id, budget_year,fk_change_id,fk_alter_code,action_type, fk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
department_code, fk_function_code, description, gl_amount,
budget_amount, year_1_amount,year_2_amount,
year_3_amount,year_4_amount)
SELECT  td.fk_tenant_id, ch.budget_year,0 as fk_change_id,'' as fk_alter_code,td.action_type,  0 as pk_action_id, td.fk_account_code,td.fk_project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
td.department_code, td.fk_function_code, '' as description, 0 AS gl_amount,
SUM(amount_year_1) AS budget_amount, 0 as year_1_amount,0 as year_2_amount,
0 as year_3_amount,0 as year_4_amount
FROM tbu_trans_detail_original td
JOIN @changed_table_org ch ON td.fk_tenant_id = ch.fk_tenant_id AND td.budget_year = ch.budget_year -1
GROUP BY td.fk_tenant_id, ch.budget_year,td.action_type, td.fk_account_code,td.fk_project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
td.department_code, td.fk_function_code


select @timestamp = sysdatetime();
PRINT 'FINISH: Insert org_budget data in temp table at ' + convert(nvarchar(19),@timestamp);



RAISERROR ('START: Insert revised data in temp table', 0, 1) WITH NOWAIT;

INSERT INTO #TEMP_ACC (fk_tenant_id, budget_year,fk_change_id,fk_alter_code,action_type, fk_action_id,fk_account_code,fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
department_code, fk_function_code, description, gl_amount,
budget_amount, year_1_amount,year_2_amount,
year_3_amount,year_4_amount)
SELECT  td.fk_tenant_id, ch.budget_year,0 as fk_change_id,'' as fk_alter_code,td.action_type,  0 as pk_action_id, td.fk_account_code,td.fk_project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
td.department_code, td.fk_function_code, '' as description, 0 AS gl_amount,
SUM(amount_year_1) AS budget_amount, 0 as year_1_amount,0 as year_2_amount,
0 as year_3_amount,0 as year_4_amount
FROM tbu_trans_detail td
JOIN @changed_table_rev ch ON td.fk_tenant_id = ch.fk_tenant_id AND td.budget_year = ch.budget_year - 1
GROUP BY td.fk_tenant_id, ch.budget_year,td.action_type, td.fk_account_code,td.fk_project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
td.department_code, td.fk_function_code


select @timestamp = sysdatetime();
PRINT 'FINISH: Insert revised data in temp table at ' + convert(nvarchar(19),@timestamp);



IF @budget_year = 0 AND @tenant_id = 0
BEGIN 

	SELECT * INTO #TEMP_BUDDOC_REPORTS FROM [twh_buddoc_reports] WHERE budget_year < @current_year-1; 

	TRUNCATE TABLE	[twh_buddoc_reports];

END

RAISERROR ('START: Truncating table', 0, 1) WITH NOWAIT;

BEGIN
SET  @continue = 1

WHILE @continue = 1
BEGIN
    PRINT GETDATE()
    SET ROWCOUNT 50000
    BEGIN TRANSACTION
    DELETE T FROM [twh_buddoc_reports] T INNER JOIN @changed_table c ON T.fk_tenant_id = c.fk_tenant_id AND T.budget_year = c.budget_year 
    SET @rowcount = @@rowcount 
    COMMIT
    PRINT GETDATE()
    IF @rowcount = 0
    BEGIN
        SET @continue = 0
    END
END

END

SET ROWCOUNT 0;

select @timestamp = sysdatetime();
PRINT 'FINISH: Truncating table at ' + convert(nvarchar(19),@timestamp);

IF @budget_year = 0 AND @tenant_id = 0
BEGIN 

RAISERROR ('START: Insert from HISTORY', 0, 1) WITH NOWAIT;

INSERT INTO [twh_buddoc_reports] (pk_id, fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount,budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,[description], updated, updated_by)
SELECT pk_id, fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount,budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,[description], updated, updated_by
FROM #TEMP_BUDDOC_REPORTS;

select @timestamp = sysdatetime();
PRINT 'FINISH: Insert from HISTORY ' + convert(nvarchar(19),@timestamp);

END

CREATE INDEX #TEMP_ACC_IND ON #TEMP_ACC (department_code, fk_tenant_id, budget_year)

RAISERROR ('START: Insert into twh_buddoc_reports', 0, 1) WITH NOWAIT;

BEGIN

INSERT INTO [twh_buddoc_reports] (pk_id, fk_tenant_id,budget_year,fk_change_id,fp_level_1_value,fp_level_2_value,action_type,
fk_alter_code,pk_action_id,fk_account_code, fk_project_code, free_dim_1, free_dim_2, free_dim_3, free_dim_4,
service_id_1,service_name_1,service_id_2,service_name_2,service_id_3,service_name_3,service_id_4,service_name_4,
service_id_5,service_name_5,org_id_1,org_name_1,org_id_2,org_name_2,org_id_3,org_name_3,org_id_4,org_name_4,org_id_5,org_name_5,
org_shortname_1,org_shortname_2,org_shortname_3,org_shortname_4,org_shortname_5,fk_department_code,
fk_function_code,gl_amount,budget_amount,year_1_amount,year_2_amount,year_3_amount,year_4_amount,[description], updated, updated_by)
select newid() AS pk_id, td.fk_tenant_id, td.budget_year, td.fk_change_id,fp_level_1_value = 
CASE	WHEN p1.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p1.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p1.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p1.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p1.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p1.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p1.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p1.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p1.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p1.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

fp_level_2_value = 
CASE	WHEN p2.param_value = 'org_id_1' THEN oh.org_id_1
		WHEN p2.param_value = 'org_id_2' THEN oh.org_id_2
		WHEN p2.param_value = 'org_id_3' THEN oh.org_id_3
		WHEN p2.param_value = 'org_id_4' THEN oh.org_id_4
		WHEN p2.param_value = 'org_id_5' THEN oh.org_id_5
		WHEN p2.param_value = 'service_id_1' THEN sv.service_id_1		
		WHEN p2.param_value = 'service_id_2' THEN sv.service_id_2
		WHEN p2.param_value = 'service_id_3' THEN sv.service_id_3
		WHEN p2.param_value = 'service_id_4' THEN sv.service_id_4
		WHEN p2.param_value = 'service_id_5' THEN sv.service_id_5		
	ELSE ''
END,

td.action_type, td.fk_alter_code, td.fk_action_id, td.fk_account_code,td.fk_project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,td.department_code,
td.fk_function_code as fk_function_code, SUM(gl_amount) as gl_amount,
SUM(budget_amount) AS budget_amount, SUM(td.year_1_amount) AS year_1_amount,SUM(td.year_2_amount) AS year_2_amount,SUM(td.year_3_amount) AS year_3_amount,SUM(td.year_4_amount) AS year_4_amount,
td.description, getdate(), 1002
FROM #TEMP_ACC td
JOIN #org_hierarchy oh on td.fk_tenant_id = oh.fk_tenant_id AND td.department_code = oh.fk_department_code AND td.budget_year = oh.budget_year
LEFT JOIN tco_service_values sv on td.fk_tenant_id = sv.fk_tenant_id AND td.fk_function_code = sv.fk_function_code
LEFT JOIN tco_parameters p1 ON p1.param_name = 'FINPLAN_LEVEL_1' AND p1.fk_tenant_id = td.fk_tenant_id AND p1.active = 1
LEFT JOIN tco_parameters p2 ON p2.param_name = 'FINPLAN_LEVEL_2' AND p2.fk_tenant_id = td.fk_tenant_id AND p2.active = 1
GROUP BY td.fk_tenant_id, td.budget_year,td.fk_change_id,td.action_type, td.fk_alter_code, td.fk_action_id, td.fk_account_code,
td.fk_project_code, td.free_dim_1, td.free_dim_2, td.free_dim_3, td.free_dim_4,
sv.service_id_1, sv.service_name_1, sv.service_id_2, sv.service_name_2, sv.service_id_3, sv.service_name_3, sv.service_id_4, sv.service_name_4, sv.service_id_5, sv.service_name_5,
oh.org_id_1,oh.org_name_1,oh.org_id_2,oh.org_name_2,oh.org_id_3,oh.org_name_3,oh.org_id_4,oh.org_name_4,oh.org_id_5 ,oh.org_name_5,
oh.org_shortname_1,oh.org_shortname_2,oh.org_shortname_3,oh.org_shortname_4,oh.org_shortname_5,td.department_code,
td.fk_function_code,td.description, p1.param_value, p2.param_value
;

IF @@ERROR <> 0
   BEGIN
		  RAISERROR ('Error occured. Exiting', 16, 1)
        RETURN
   END

END

select @timestamp = sysdatetime();
PRINT 'FINISH: Insert into twh_buddoc_reports at ' + convert(nvarchar(19),@timestamp);

END

RAISERROR ('START: Update finplan warehouse', 0, 1) WITH NOWAIT;


DECLARE @table_year TABLE (budget_year INT NOT NULL,
period_from INT NOT NULL,
period_to INT NOT NULL
);

DECLARE @table_period TABLE (
fk_tenant_id INT NOT NULL,
budget_year INT NOT NULL,
forecast_period INT NOT NULL
);


IF @budget_year = 0 AND @tenant_id = 0
BEGIN 

INSERT INTO @table_year (budget_year, period_from, period_to)
VALUES (@current_year-1, convert(char(4),@current_year-2)+'00',convert(char(4),@current_year-2)+'12'),
(@current_year, convert(char(4),@current_year-1)+'00',convert(char(4),@current_year-1)+'12'),
(@current_year+1, convert(char(4),@current_year)+'00',convert(char(4),@current_year)+'12')
END

IF @budget_year != 0
BEGIN 

INSERT INTO @table_year (budget_year, period_from, period_to)
VALUES (@budget_year, convert(char(4),@budget_year-1)+'00',convert(char(4),@budget_year-1)+'12')

END


INSERT INTO @table_period (fk_tenant_id, budget_year, forecast_period)
SELECT  a.fk_tenant_id,b.budget_year,max(forecast_period)
FROM tmr_period_setup a, @table_year b
WHERE a.forecast_period BETWEEN b.period_from and period_to
AND status IN (1,2)
GROUP BY b.budget_year, a.fk_tenant_id 
ORDER BY a.fk_tenant_id, b.budget_year;

DELETE FROM @table_period WHERE fk_tenant_id NOT IN (SELECT fk_tenant_id FROM @changed_table)


CREATE TABLE #temp_finplan_warehouse
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL,
	[action_type] [int] NOT NULL,
	[line_order] [int] NOT NULL, 
	[fk_account_code] nvarchar(25) NOT NULL,	
	[fk_department_code] nvarchar(25) NOT NULL,
	[fk_function_code] nvarchar(25) NOT NULL,
	[fk_project_code] nvarchar(25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
    [org_budget_prev_year] DECIMAL(18, 2) NOT NULL,
	[org_budget_curr_year] DECIMAL(18, 2) NOT NULL,
    [revised_budget_prev_year] DECIMAL(18, 2) NOT NULL,
    [revised_budget_this_year] DECIMAL(18, 2) NOT NULL,
    [accounting_amount] DECIMAL(18, 2) NOT NULL,
    [forecast_amount] DECIMAL(18, 2) NOT NULL,
	[updated] DATETIME NOT NULL,
	[updated_by] INT NOT NULL
)


CREATE TABLE #operation_accounts( 
	[pk_id] INT NOT NULL IDENTITY, 
	pk_tenant_id INT NOT NULL,
    pk_account_code NVARCHAR(25) NOT NULL,
    fk_kostra_account_code NVARCHAR(25) NOT NULL)

INSERT INTO #operation_accounts (pk_tenant_id, pk_account_code, fk_kostra_account_code)

SELECT DISTINCT pk_tenant_id, pk_account_code, fk_kostra_account_code FROM 
(SELECT ac.pk_tenant_id,ac.pk_account_code, ac.fk_kostra_account_code
FROM tco_accounts ac
JOIN @changed_table ch ON ac.pk_tenant_id = ch.fk_tenant_id AND ch.budget_year BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
--JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND type = 'operations'
UNION
SELECT ac.pk_tenant_id,ac.pk_account_code, ac.fk_kostra_account_code
FROM tco_accounts ac
JOIN @changed_table ch ON ac.pk_tenant_id = ch.fk_tenant_id AND ch.budget_year-1 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
--JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND type = 'operations'
UNION 
SELECT ac.pk_tenant_id,ac.pk_account_code, ac.fk_kostra_account_code
FROM tco_accounts ac
JOIN @changed_table ch ON ac.pk_tenant_id = ch.fk_tenant_id AND ch.budget_year-2 BETWEEN DATEPART(year, dateFrom) AND DATEPART(year, dateTo)
--JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND type = 'operations'
) s


DECLARE @changed_table_locked TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);

DECLARE @changed_table_unlocked TABLE(
    fk_tenant_id INT  NOT NULL,
	budget_year INT  NOT NULL
);

INSERT INTO @changed_table_locked (fk_tenant_id, budget_year)
SELECT a.fk_tenant_id, a.budget_year FROM @changed_table a
JOIN tco_parameters p ON a.fk_tenant_id = p.fk_tenant_id and a.budget_year = p.param_value and p.param_name = 'LOCK_ORIGINAL_BUDGET' and p.active = 1

INSERT INTO @changed_table_unlocked (fk_tenant_id, budget_year)
SELECT a.fk_tenant_id, a.budget_year FROM @changed_table a
LEFT JOIN tco_parameters p ON a.fk_tenant_id = p.fk_tenant_id and a.budget_year = p.param_value and p.param_name = 'LOCK_ORIGINAL_BUDGET' and p.active = 1
WHERE p.pk_id is null

PRINT 'Fetch if original budget is locked'

INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
org_budget_prev_year, org_budget_curr_year, revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, ch.budget_year, action_type, line_order, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
0 revised_budget_prev_year, SUM(amount_year_1) as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount, getdate(),1002
FROM tbu_trans_detail a
JOIN @changed_table_locked ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year
JOIN tco_user_adjustment_codes ud ON a.fk_tenant_id = ud.fk_tenant_id and a.fk_adjustment_code = ud.pk_adj_code and ud.status = 1
GROUP BY a.fk_tenant_id, ch.budget_year, action_type, line_order, department_code, fk_function_code,fk_project_code, fk_account_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4

PRINT 'Fetch if original budget is not locked'

INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
org_budget_prev_year, org_budget_curr_year, revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, ch.budget_year, action_type, line_order, fk_account_code, department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,
0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
0 revised_budget_prev_year, SUM(amount_year_1) as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount, getdate(),1002
FROM tbu_trans_detail a
JOIN @changed_table_unlocked ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year
GROUP BY a.fk_tenant_id, ch.budget_year, action_type, line_order, department_code, fk_function_code,fk_project_code, fk_account_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4


INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, org_budget_prev_year, org_budget_curr_year,
revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, ch.budget_year, action_type, line_order, fk_account_code, department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
SUM(amount_year_1) as revised_budget_prev_year, 0 as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount, getdate(),1002
FROM tbu_trans_detail a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year -1
JOIN tco_user_adjustment_codes ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id and ud.status = 1
GROUP BY a.fk_tenant_id, ch.budget_year, action_type, line_order, department_code, fk_function_code, fk_project_code,fk_account_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4


INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,org_budget_prev_year, org_budget_curr_year,
revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, ch.budget_year, action_type, line_order,fk_account_code,department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,SUM(amount_year_1) AS org_budget_prev_year, 0 AS org_budget_curr_year,
0 revised_budget_prev_year, 0 as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount, getdate(),1002
FROM tbu_trans_detail_original a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year -1
GROUP BY a.fk_tenant_id, ch.budget_year, action_type, line_order, department_code, fk_function_code, fk_project_code,fk_account_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4

-- New column for original budget this year

INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,org_budget_prev_year, org_budget_curr_year,
revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, ch.budget_year, action_type, line_order,fk_account_code,department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,0 AS org_budget_prev_year, SUM(amount_year_1) AS org_budget_curr_year,
0 revised_budget_prev_year, 0 as revised_budget_this_year, 0 as accounting_amount, 0 as forecast_amount, getdate(),1002
FROM tbu_trans_detail_original a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year
GROUP BY a.fk_tenant_id, ch.budget_year, action_type, line_order, department_code, fk_function_code, fk_project_code,fk_account_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4

--


INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, org_budget_prev_year, org_budget_curr_year,
revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, ch.budget_year, 5 as action_type, 0 as line_order, fk_account_code, a.department_code,fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
0 revised_budget_prev_year, 0 as revised_budget_this_year, SUM(amount) as accounting_amount, 0 as forecast_amount, getdate(),1002
FROM tfp_accounting_data a
JOIN @changed_table ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.gl_year = ch.budget_year -2
GROUP BY a.fk_tenant_id, department_code, fk_function_code, fk_project_code,fk_account_code,ch.budget_year,free_dim_1, free_dim_2, free_dim_3, free_dim_4



INSERT INTO #temp_finplan_warehouse (fk_tenant_id, budget_year, action_type, line_order,fk_account_code, fk_department_code, fk_function_code, fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4,org_budget_prev_year, org_budget_curr_year,
revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT a.fk_tenant_id, b.budget_year, action_type, line_order, fk_account_code,department_code, fk_function_code,fk_project_code,
free_dim_1, free_dim_2, free_dim_3, free_dim_4, 0 AS org_budget_prev_year, 0 AS org_budget_curr_year,
0 revised_budget_prev_year, 0 as revised_budget_this_year, 0 as accounting_amount, SUM(amount_year_1) as forecast_amount, getdate(),1002
FROM tbu_forecast_transactions a
JOIN @table_period b ON a.forecast_period = b.forecast_period AND a.fk_tenant_id = b.fk_tenant_id
GROUP BY a.fk_tenant_id, b.budget_year, action_type, line_order, department_code, fk_function_code,fk_project_code,fk_account_code,free_dim_1, free_dim_2, free_dim_3, free_dim_4


	UPDATE #temp_finplan_warehouse SET action_type = 5 --WHERE action_type NOT IN (1,2,3,4,100);

	UPDATE #temp_finplan_warehouse
	SET line_order = ls.line_order, action_type = ls.action_type
	FROM #temp_finplan_warehouse imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 0;

	UPDATE #temp_finplan_warehouse
	SET line_order = ls.line_order, action_type = ls.action_type
	FROM #temp_finplan_warehouse imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 5;

	UPDATE #temp_finplan_warehouse
	SET line_order = ls.line_order, action_type = ls.action_type
	FROM #temp_finplan_warehouse imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 4;

	UPDATE #temp_finplan_warehouse
	SET line_order = ls.line_order, action_type = ls.action_type
	FROM #temp_finplan_warehouse imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 3;

	UPDATE #temp_finplan_warehouse
	SET line_order = ls.line_order, action_type = ls.action_type
	FROM #temp_finplan_warehouse imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 2;

	UPDATE #temp_finplan_warehouse
	SET line_order = ls.line_order, action_type = ls.action_type
	FROM #temp_finplan_warehouse imp, tmd_finplan_line_setup ls
	WHERE imp.fk_account_code = ls.fk_account_code
	AND imp.fk_department_code BETWEEN ls.fk_department_code_from AND ls.fk_department_code_to
	AND imp.fk_function_code BETWEEN ls.fk_function_code_from AND ls.fk_function_code_to
	AND imp.fk_project_code BETWEEN ls.fk_project_code_from AND ls.fk_project_code_to
	AND imp.free_dim_1 BETWEEN ls.free_dim_1_from AND ls.free_dim_1_to
	AND imp.free_dim_2 BETWEEN ls.free_dim_2_from AND ls.free_dim_2_to
	AND imp.free_dim_3 BETWEEN ls.free_dim_3_from AND ls.free_dim_3_to
	AND imp.free_dim_4 BETWEEN ls.free_dim_4_from AND ls.free_dim_4_to
	AND imp.fk_tenant_id = ls.fk_tenant_id
	AND imp.budget_year = ls.budget_year
	AND ls.priority = 1;

	UPDATE #temp_finplan_warehouse SET free_dim_1 = '' WHERE free_dim_1 = ' ' OR free_dim_1 IS NULL
	UPDATE #temp_finplan_warehouse SET free_dim_2 = '' WHERE free_dim_2 = ' ' OR free_dim_2 IS NULL
	UPDATE #temp_finplan_warehouse SET free_dim_3 = '' WHERE free_dim_3 = ' ' OR free_dim_3 IS NULL
	UPDATE #temp_finplan_warehouse SET free_dim_4 = '' WHERE free_dim_4 = ' ' OR free_dim_4 IS NULL


IF @budget_year != 0 AND @tenant_id != 0

BEGIN

DELETE T FROM [tfp_finplan_warehouse] T WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year;

INSERT INTO [tfp_finplan_warehouse] (fk_tenant_id, budget_year, action_type, line_order, line_group_id, fk_department_code, fk_function_code, free_dim_2,
org_budget_prev_year, org_budget_curr_year, revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT fk_tenant_id, budget_year, action_type, line_order, rl.line_group_id, fk_department_code, fk_function_code, free_dim_2,
sum(org_budget_prev_year), sum(org_budget_curr_year),sum(revised_budget_prev_year), sum(revised_budget_this_year), sum(accounting_amount), sum(forecast_amount), GETDATE () AS updated, 1002 AS updated_by
FROM #temp_finplan_warehouse a
JOIN #operation_accounts ac ON ac.pk_account_code = a.fk_account_code AND a.fk_tenant_id = ac.pk_tenant_id  
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = 'Drift'
WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year
GROUP BY fk_tenant_id, budget_year, action_type, line_order, fk_department_code, fk_function_code, free_dim_2,rl.line_group_id;

PRINT 'Delete and insert into tfp_finplan_warehouse 1'
END

ELSE

BEGIN

DELETE T FROM [tfp_finplan_warehouse] T INNER JOIN @table_year A on a.budget_year = T.budget_year;

INSERT INTO [tfp_finplan_warehouse] (fk_tenant_id, budget_year, action_type, line_order, line_group_id, fk_department_code, fk_function_code, free_dim_2,
org_budget_prev_year,org_budget_curr_year, revised_budget_prev_year, revised_budget_this_year, accounting_amount, forecast_amount, updated, updated_by)
SELECT fk_tenant_id, budget_year, action_type, line_order, rl.line_group_id, fk_department_code, fk_function_code, free_dim_2,sum(org_budget_prev_year), sum(org_budget_curr_year),
sum(revised_budget_prev_year), sum(revised_budget_this_year), sum(accounting_amount), sum(forecast_amount), GETDATE () AS updated, 1002 AS updated_by
FROM #temp_finplan_warehouse a
JOIN #operation_accounts ac ON ac.pk_account_code = a.fk_account_code AND a.fk_tenant_id = ac.pk_tenant_id  
JOIN gmd_reporting_line rl ON ac.fk_kostra_account_code = rl.fk_kostra_account_code AND rl.report = 'Drift'
GROUP BY fk_tenant_id, budget_year, action_type, line_order, fk_department_code, fk_function_code, free_dim_2,rl.line_group_id;

PRINT 'Delete and insert into tfp_finplan_warehouse 2'

END

DROP TABLE #temp_finplan_warehouse;
DROP TABLE #TEMP_ACC;


IF @budget_year = 0 AND @tenant_id = 0
BEGIN 
DROP TABLE #TEMP_BUDDOC_REPORTS;
END

select @timestamp = sysdatetime();
PRINT 'FINISH: Update finplan warehouse at ' + convert(nvarchar(19),@timestamp);



BEGIN
UPDATE [twh_report_job_queue] SET run_flag = 0, updated = GETDATE() WHERE job_name = 'BUDDOCFULL';
END

select @timestamp = sysdatetime();
PRINT 'Job ended ok at ' + convert(nvarchar(19),@timestamp)


GO

