CREATE OR ALTER PROCEDURE [dbo].[prcDeleteUpdateExistingBudgets]
	@TenantID int,
	@isOriginal bit,
	@UserID int ,
	@budgetYear int,
	@jobID int,
	@deleteExistingRecords bit,
	@isUpdateRecord bit ,
	@deleteStagedRecords bit,
	@orgLevel int,
	@orgId nvarchar(30),
	@userAdjCode  nvarchar(30)
AS
BEGIN	

	DECLARE @TenantID_1 int,@isOriginal_1 bit,@UserID_1 int,@budgetYear_1 int,@jobID_1 int,@deleteExistingRecords_1 bit,@isUpdateRecord_1 bit, @orgVersion nvarchar(20), @orgLevel_1 int, @orgId_1 nvarchar(30), @userAdjCode_1 nvarchar(30)
	SET @TenantID_1 = @TenantID
	SET @isOriginal_1 = @isOriginal
	SET @UserID_1 = @UserID
	SET @budgetYear_1 = @budgetYear
	SET @jobID_1 = @jobID
	SET @deleteExistingRecords_1 = @deleteExistingRecords
	SET @isUpdateRecord_1 = @isUpdateRecord
	SET @orgId_1=@orgId
	SET @orgLevel_1=@orgLevel
	SET @userAdjCode_1=@userAdjCode
	
	IF (@isUpdateRecord_1 = 1)
	BEGIN
		IF(@isOriginal_1 = 0)
			BEGIN
				UPDATE ttd SET  ttd.action_type=tfls.action_type,ttd.line_order=tfls.line_order from tbu_trans_detail ttd
				join tmd_finplan_line_setup tfls on ttd.fk_tenant_id=tfls.fk_tenant_id and  ttd.budget_year=tfls.budget_year
				and ttd.fk_account_code=tfls.fk_account_code
				and ttd.department_code between tfls.fk_department_code_from and tfls.fk_department_code_to
				and ttd.fk_function_code between tfls.fk_function_code_from and tfls.fk_function_code_to
				and ttd.fk_project_code between tfls.fk_project_code_from and tfls.fk_project_code_to
				and ttd.free_dim_1 between tfls.free_dim_1_from and tfls.free_dim_1_to
				and ttd.free_dim_2 between tfls.free_dim_2_from and tfls.free_dim_2_to
				and ttd.free_dim_3 between tfls.free_dim_3_from and tfls.free_dim_3_to
				and ttd.free_dim_4 between tfls.free_dim_4_from and tfls.free_dim_4_to
				where ttd.fk_tenant_id=@TenantID_1  and ttd.budget_year = @budgetYear_1
			END
		ELSE
			BEGIN
				UPDATE ttdo SET  ttdo.action_type=tfls.action_type,ttdo.line_order=tfls.line_order from tbu_trans_detail_original ttdo
				join tmd_finplan_line_setup tfls on ttdo.fk_tenant_id=tfls.fk_tenant_id and  ttdo.budget_year=tfls.budget_year
				and ttdo.fk_account_code=tfls.fk_account_code
				and ttdo.department_code between tfls.fk_department_code_from and tfls.fk_department_code_to
				and ttdo.fk_function_code between tfls.fk_function_code_from and tfls.fk_function_code_to
				and ttdo.fk_project_code between tfls.fk_project_code_from and tfls.fk_project_code_to
				and ttdo.free_dim_1 between tfls.free_dim_1_from and tfls.free_dim_1_to
				and ttdo.free_dim_2 between tfls.free_dim_2_from and tfls.free_dim_2_to
				and ttdo.free_dim_3 between tfls.free_dim_3_from and tfls.free_dim_3_to
				and ttdo.free_dim_4 between tfls.free_dim_4_from and tfls.free_dim_4_to
				where ttdo.fk_tenant_id=@TenantID_1   and ttdo.budget_year = @budgetYear_1 
			END
	END	
	
	IF (@deleteStagedRecords = 1)
	BEGIN
			DELETE from tbu_stage_budget_import where
			[user_id] = @UserID_1 AND
			[tenant_id] =@TenantID_1 AND
			[budget_year]=@budgetYear_1 AND
			[adjustment_code] = @userAdjCode_1
	END 
END