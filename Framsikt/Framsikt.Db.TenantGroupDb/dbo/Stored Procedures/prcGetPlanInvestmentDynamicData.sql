CREATE OR ALTER PROCEDURE [dbo].[prcGetPlanInvestmentDynamicData] @fk_tenant_id INT, @planIdList nvarchar(max)
AS 
BEGIN
	DECLARE @Cols AS NVARCHAR(MAX)  
	DECLARE @SelectCols AS NVARCHAR(MAX)
	DECLARE @Query  AS NVARCHAR(MAX) 

	Select a.PlanId,a.PlanName, a.fk_plan_investment_id as PlanInvestmentId,a.PlanInvestmentName,a.InvestmentCost,b.TotalSum,a.year as YearData,a.AmountPerYear 
	,CASE WHEN a.transfer_year IS NULL THEN 0 ELSE 1 END AS IsProcessed into #tempPlanInvestments 
	from (select c.pk_plan_id [PlanId], c.name[PlanName],b.fk_plan_investment_id, a.name as PlanInvestmentName, sum (distinct investment_cost) as InvestmentCost,
	year,sum(amount) as AmountPerYear , d.transfer_year
	from tpl_investments a
	join tpl_investments_detail b on a.pk_plan_investment_id = b.fk_plan_investment_id and a.fk_tenant_id = b.fk_tenant_id
	join tpl_plan_investments b1 on a.pk_plan_investment_id = b1.fk_plan_investment_id and a.fk_tenant_id = b1.fk_tenant_id
	join tpl_plan_template_details b2 on b1.fk_plan_ID = b2.fk_plan_id and b1.fk_tenant_id = b2.fk_tenant_id and b1.fk_node_Id = b2.node_id
	join tpl_plan c on a.fk_masterplan_id = c.pk_plan_id and a.fk_tenant_id = b.fk_tenant_id
	left join tpl_tfp_investment_mapping d on a.pk_plan_investment_id = d.fk_plan_investment_id  and a.fk_tenant_id = d.fk_tenant_id
	where a.fk_tenant_id = @fk_tenant_id and b2.status = 1
	group by c.pk_plan_id,c.name,b.fk_plan_investment_id ,year,a.name, d.transfer_year ) a
	join (select c.pk_plan_id [PlanId], c.name[PlanName],b.fk_plan_investment_id ,  sum(amount) as [TotalSum],   d.transfer_year from tpl_investments a
	join tpl_investments_detail b on a.pk_plan_investment_id = b.fk_plan_investment_id and a.fk_tenant_id = b.fk_tenant_id
	join tpl_plan_investments b1 on a.pk_plan_investment_id = b1.fk_plan_investment_id and a.fk_tenant_id = b1.fk_tenant_id
	join tpl_plan_template_details b2 on b1.fk_plan_ID = b2.fk_plan_id and b1.fk_tenant_id = b2.fk_tenant_id and b1.fk_node_Id = b2.node_id
	join tpl_plan c on a.fk_masterplan_id = c.pk_plan_id and a.fk_tenant_id = b.fk_tenant_id
	left join tpl_tfp_investment_mapping d on a.pk_plan_investment_id = d.fk_plan_investment_id  and a.fk_tenant_id = d.fk_tenant_id
	where a.fk_tenant_id = @fk_tenant_id and b2.status = 1
	group by c.pk_plan_id,c.name,b.fk_plan_investment_id, d.transfer_year ) b
	on a.fk_plan_investment_id = b.fk_plan_investment_id
	where a.PlanId IN (SELECT value FROM STRING_SPLIT(@planIdList,',')) 
	order by a.PlanName,a.PlanInvestmentName
  
	SELECT @Cols = ISNULL(@Cols + ',','')   + QUOTENAME(yearData) FROM (SELECT DISTINCT TOP 20 yearData FROM #tempPlanInvestments) AS years
	SELECT @SelectCols = ISNULL(@SelectCols + ',','') + 'ISNULL(' + QUOTENAME(yearData) + ', 0) AS ' + QUOTENAME(yearData)
						 FROM (SELECT DISTINCT TOP 20 yearData FROM #tempPlanInvestments) AS years
 
	SET @Query = N'SELECT PlanId,PlanName,PlanInvestmentId,PlanInvestmentName,InvestmentCost,TotalSum,IsProcessed,  ' + @SelectCols + N' FROM   
				(  
					SELECT PlanId,PlanName,PlanInvestmentId,PlanInvestmentName,InvestmentCost,TotalSum,IsProcessed,YearData,AmountPerYear
					FROM #tempPlanInvestments    
				) x  
				PIVOT   
				(  
					SUM(AmountPerYear)
					FOR yearData IN (' + @Cols +')  
				) p' 
			
	EXEC SP_EXECUTESQL @Query  
	DROP TABLE  #tempPlanInvestments
END  