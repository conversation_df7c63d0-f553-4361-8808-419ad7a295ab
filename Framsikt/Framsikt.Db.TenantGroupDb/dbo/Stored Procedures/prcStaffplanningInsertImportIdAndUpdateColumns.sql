CREATE OR ALTER PROCEDURE [dbo].[prcStaffplanningInsertImportIdAndUpdateColumns]
(
    @importId uniqueidentifier,
    @tenantId int,
    @budgetYear int,
    @importType varchar(15),
    @importedDate date,
    @status varchar(30),
    @publishDate date,
    @updated datetime,
    @updatedBy int,
    @useSalaryTable int,
    @salaryTableName nvarchar(255)
)
AS
BEGIN
    SET NOCOUNT ON;

    -- Update fk_treatment_code using INNER JOIN
    UPDATE pos
    SET fk_treatment_code = sal.handling_id
    FROM tbu_stage_positions pos
    INNER JOIN [tco_salary_acc_category] sal 
        ON  pos.fk_tenant_id = sal.fk_tenant_id
        AND pos.fk_salary_type = sal.pk_salary_acc_category
    WHERE pos.fk_tenant_id = @tenantId
        AND pos.budget_year = @budgetYear
        AND pos.import_id = @importId;

    -- Update not_to_include for treatment_code = 3
    UPDATE tbu_stage_positions
    SET not_to_include = 1
    WHERE fk_tenant_id = @tenantId
        AND budget_year = @budgetYear
        AND import_id = @importId
        AND fk_treatment_code = 3;

    -- Update salary type description using INNER JOIN
    UPDATE pos
    SET fk_salary_type_desc = sal.category_name
    FROM tbu_stage_positions pos
    INNER JOIN [tco_salary_acc_category] sal 
        ON pos.fk_tenant_id = sal.fk_tenant_id  
        AND pos.fk_salary_type = sal.pk_salary_acc_category
    WHERE pos.fk_tenant_id = @tenantId
        AND pos.budget_year = @budgetYear
        AND pos.import_id = @importId
        AND pos.not_to_include = 0;

    -- Update position name using INNER JOIN
    UPDATE pos
    SET fk_position_name = tp.position_name
    FROM tbu_stage_positions pos
    INNER JOIN tco_positions tp 
        ON pos.fk_tenant_id = tp.fk_tenant_id
        AND pos.fk_position_id = tp.position_id
    WHERE pos.fk_tenant_id = @tenantId
        AND pos.budget_year = @budgetYear
        AND (@budgetYear BETWEEN tp.year_from AND tp.year_to)
        AND pos.import_id = @importId
        AND pos.not_to_include = 0;

    -- Update position ID for specific conditions
    UPDATE tbu_stage_positions
    SET fk_position_id = '99999',
        fk_position_name = 'Tillegg'
    WHERE fk_tenant_id = @tenantId
        AND budget_year = @budgetYear
        AND import_id = @importId
        AND fk_treatment_code = 2
        AND fk_position_id = ''
        AND not_to_include = 0;

    -- Calculate budget year boundaries
    DECLARE @budgetYearStart int = CAST(CONCAT(@budgetYear, '01') AS int),
            @budgetYearEnd int = CAST(CONCAT(@budgetYear, '12') AS int);

    -- Update not_to_include based on period range
    UPDATE tbu_stage_positions
    SET not_to_include = CASE 
        WHEN end_period < @budgetYearStart THEN 1
        WHEN start_period > @budgetYearEnd THEN 1
        ELSE 0
    END
    WHERE fk_tenant_id = @tenantId
        AND budget_year = @budgetYear
        AND import_id = @importId
        AND not_to_include = 0;

    -- Handle employment_id assignment using CTE
    WITH EmploymentIdsBase AS (
        -- First part: Handle records WITH external reference
        SELECT 
            pk_id,
            external_reference,
            NULL as dept_code,
            NULL as func_code,
            NULL as proj_code,
            NULL as dim1,
            NULL as dim2,
            NULL as dim3,
            NULL as dim4,
            NULL as start_per,
            NULL as end_per,
            NULL as res_id,
            fk_treatment_code,
            CASE 
                WHEN fk_treatment_code = '1' THEN 
                    ROW_NUMBER() OVER (
                        PARTITION BY external_reference
                        ORDER BY pk_id
                    )
                ELSE 1
            END AS DuplicateRank,
            DENSE_RANK() OVER (
                ORDER BY 
                    CASE 
                        WHEN external_reference IS NOT NULL AND external_reference <> '' 
                        THEN external_reference 
                        ELSE CAST(pk_id as varchar(50))
                    END
            ) + 
            CASE 
                WHEN fk_treatment_code = '1' THEN 
                    ROW_NUMBER() OVER (
                        PARTITION BY external_reference
                        ORDER BY pk_id
                    ) - 1
                ELSE 0
            END AS GlobalRowNum
        FROM tbu_stage_positions
        WHERE fk_tenant_id = @tenantId
            AND budget_year = @budgetYear
            AND import_id = @importId
            AND not_to_include = 0
            AND external_reference IS NOT NULL 
            AND external_reference <> ''
            and fk_treatment_code in ('1', '2')

        UNION ALL

        -- Second part: Handle records WITHOUT external reference
        SELECT 
            pk_id,
            NULL as external_reference,
            fk_department_code,
            fk_function_code,
            fk_project_code,
            free_dim_1,
            free_dim_2,
            free_dim_3,
            free_dim_4,
            start_period,
            end_period,
            fk_res_id,
            fk_treatment_code,
            CASE 
                WHEN fk_treatment_code = '1' THEN 
                    ROW_NUMBER() OVER (
                        PARTITION BY fk_department_code, fk_function_code, fk_project_code,
                                    free_dim_1, free_dim_2, free_dim_3, free_dim_4,
                                    start_period, end_period, fk_res_id
                        ORDER BY pk_id
                    )
                ELSE 1
            END AS DuplicateRank,
            DENSE_RANK() OVER (
                ORDER BY 
                    fk_department_code, fk_function_code, fk_project_code,
                    free_dim_1, free_dim_2, free_dim_3, free_dim_4,
                    start_period, end_period, fk_res_id
            ) + 
            CASE 
                WHEN fk_treatment_code = '1' THEN 
                    ROW_NUMBER() OVER (
                        PARTITION BY fk_department_code, fk_function_code, fk_project_code,
                                    free_dim_1, free_dim_2, free_dim_3, free_dim_4,
                                    start_period, end_period, fk_res_id
                        ORDER BY pk_id
                    ) - 1
                ELSE 0
            END + (
                SELECT COUNT(DISTINCT external_reference) 
                FROM tbu_stage_positions
                WHERE fk_tenant_id = @tenantId
                    AND budget_year = @budgetYear
                    AND import_id = @importId
                    AND not_to_include = 0
                    AND external_reference IS NOT NULL 
                    AND external_reference <> ''
            ) AS GlobalRowNum
        FROM tbu_stage_positions
        WHERE fk_tenant_id = @tenantId
            AND budget_year = @budgetYear
            AND import_id = @importId
            AND not_to_include = 0
            AND (external_reference IS NULL OR external_reference = '')
            AND fk_treatment_code in ('1', '2')
    )

    -- Update employment_ids
    UPDATE pos
    SET employment_id = base.new_employment_id
    FROM tbu_stage_positions pos
    INNER JOIN (
        SELECT 
            e.pk_id,
            e.GlobalRowNum + COALESCE(
                (SELECT MAX(employment_id)
                FROM tbu_stage_positions 
                WHERE fk_tenant_id = @tenantId 
                AND budget_year = @budgetYear
                AND import_id = @importId
                AND employment_id IS NOT NULL), 0
            ) AS new_employment_id
        FROM EmploymentIdsBase e
    ) base ON pos.pk_id = base.pk_id;

    -- Update salary amounts if using salary table
    IF @useSalaryTable = 1
    BEGIN
        UPDATE pos
        SET amount_salary_year = st.yearly_salary,
            amount_salary_month = st.yearly_salary/12
        FROM tbu_stage_positions pos
        INNER JOIN gco_salary_table st 
            ON st.salary_step = pos.salary_step
            AND st.salary_table_name = @salaryTableName
        WHERE pos.fk_tenant_id = @tenantId
            AND pos.budget_year = @budgetYear
            AND pos.import_id = @importId
            AND pos.not_to_include = 0
            AND pos.fk_treatment_code = 1;
    END

    -- Update final salary calculations
    UPDATE tbu_stage_positions
    SET amount_salary_year = amount_salary_month * 12,
        salary_step = NULL
    WHERE fk_tenant_id = @tenantId
        AND budget_year = @budgetYear
        AND import_id = @importId
        AND not_to_include = 0
        AND fk_treatment_code = 2;

END