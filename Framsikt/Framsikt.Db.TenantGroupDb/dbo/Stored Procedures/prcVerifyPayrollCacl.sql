--EXEC prcVerifyPayrollCalc 15,2018

CREATE OR ALTER PROC [dbo].[prcVerifyPayrollCalc] @tenant_id INT, @budget_year INT

AS

DECLARE @table_name AS varchar(100)
DECLARE @org_version VARCHAR(25) 

SET @org_version =  (SELECT pk_org_version FROM tco_org_version WHERE @budget_year*100+1 BETWEEN period_from AND period_to AND fk_tenant_id = @tenant_id)

SET NOCOUNT ON

SET @table_name = 'TempVerifyPayroll' --+ convert(varchar(11), @tenant_id)

DECLARE @accountstable TABLE
	(fk_tenant_id INT NOT NULL,
	fk_account_code NVARCHAR(25) NOT NULL, 
	type VARCHAR(3) NOT NULL)

DECLARE @checktable TABLE 
	(fk_tenant_id INT NOT NULL,
	budget_year INT NOT NULL,
	fk_account_code NVARCHAR(25) NOT NULL,
	fk_department_code NVARCHAR(25) NOT NULL,
	org_id_2 NVARCHAR(25) NOT NULL,
	org_id_3 NVARCHAR(25) NOT NULL,
	org_id_4 NVARCHAR(25) NOT NULL,
	amount_check DEC(18,2) NOT NULL,
	amount_real DEC(18,2) NOT NULL,
	check_type VARCHAR(3) NOT NULL)


INSERT INTO @accountstable (fk_tenant_id, fk_account_code, type)
select fk_tenant_id, acc_value as fk_account_code, 'AGA' from tmd_acc_defaults
where fk_tenant_id = @tenant_id
and module = 'BU'
and fk_org_version = @org_version
and link_value IN (
'AGA_SAL',
'AGA_HOL')
UNION 
SELECT distinct fk_tenant_id,fk_account_code_aga, 'AGA' FROM tmd_pension_type
where fk_tenant_id = @tenant_id
UNION
SELECT DISTINCT pt.fk_tenant_id, pt.fk_account_code, 'PEN' 
FROM tmd_pension_type pt WHERE pt.fk_tenant_id = @tenant_id AND @budget_year BETWEEN pt.year_from AND pt.year_to


INSERT INTO @checktable (fk_tenant_id, budget_year, fk_account_code, fk_department_code, org_id_2, org_id_3,org_id_4, amount_check, amount_real, check_type)
select  a.fk_tenant_id, a.budget_year, '' as fk_account_code, a.department_code,org_id_2, org_id_3,org_id_4,SUM(amount_year_1*tr.rate) as amount_year_1, 0,'AGA' as check_type 
from tbu_trans_detail a
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_accounts c on a.fk_tenant_id = c.pk_tenant_id and a.fk_account_code = c.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,c.dateFrom) AND DATEPART(YEAR,c.dateTo)
JOIN gmd_reporting_line d on c.fk_kostra_account_code = d.fk_kostra_account_code and report = 'YBUD1' and line_group_id = 3000
JOIN gco_tenants t ON a.fk_tenant_id = t.pk_id
JOIN [gmd_emp_tax_rates] tr on t.municipality_id = tr.fk_municipality_id --AND tr.budget_year = a.budget_year
where a.fk_tenant_id = @tenant_id
and a.budget_year = @budget_year
and fk_employment_id = 0
and tax_flag = 1
GROUP BY a.fk_tenant_id, a.budget_year, a.department_code,org_id_2, org_id_3,org_id_4

Print 'recalculate AGA'


INSERT INTO @checktable (fk_tenant_id, budget_year, fk_account_code, fk_department_code,org_id_2, org_id_3,org_id_4, amount_check, amount_real, check_type)
select  a.fk_tenant_id, a.budget_year, '' as fk_account_code, a.department_code,org_id_2, org_id_3,org_id_4,0,SUM(amount_year_1),'AGA' 
from tbu_trans_detail a
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_accounts c on a.fk_tenant_id = c.pk_tenant_id and a.fk_account_code = c.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,c.dateFrom) AND DATEPART(YEAR,c.dateTo)
JOIN gmd_reporting_line d on c.fk_kostra_account_code = d.fk_kostra_account_code and report = 'YBUD1' and line_group_id = 3000
JOIN @accountstable ad ON  ad.fk_tenant_id = a.fk_tenant_id AND ad.fk_account_code = a.fk_account_code AND ad.type = 'AGA'
where a.fk_tenant_id = @tenant_id
and budget_year = @budget_year
and fk_employment_id = 0
and budget_type = 50
GROUP BY a.fk_tenant_id, a.budget_year, a.department_code,org_id_2, org_id_3,org_id_4

Print 'Get AGA from application'

-- PENSION --

INSERT INTO @checktable (fk_tenant_id, budget_year, fk_account_code, fk_department_code,org_id_2, org_id_3,org_id_4, amount_check, amount_real, check_type)

select a.fk_tenant_id, a.budget_year, pt.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4, SUM(amount_year_1*pt.rate/100),0, 'PEN' 
from tbu_trans_detail a
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_accounts c on a.fk_tenant_id = c.pk_tenant_id and a.fk_account_code = c.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,c.dateFrom) AND DATEPART(YEAR,c.dateTo)
JOIN gmd_reporting_line d on c.fk_kostra_account_code = d.fk_kostra_account_code and report = 'YBUD1' and line_group_id = 3000
JOIN tmd_pension_type pt ON a.fk_pension_type = pt.pension_type AND a.fk_tenant_id = pt.fk_tenant_id AND a.budget_year BETWEEN pt.year_from AND pt.year_to
where a.fk_tenant_id = @tenant_id
and budget_year = @budget_year
and fk_employment_id = 0
--and fk_pension_type = 'KLP18'
GROUP BY a.fk_tenant_id, a.budget_year, pt.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4

 
--ACTUAL PENSION
INSERT INTO @checktable (fk_tenant_id, budget_year, fk_account_code, fk_department_code,org_id_2, org_id_3,org_id_4, amount_check, amount_real, check_type)
select a.fk_tenant_id, a.budget_year, a.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4,0, SUM(amount_year_1), 'PEN' 
from tbu_trans_detail a
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_accounts c on a.fk_tenant_id = c.pk_tenant_id and a.fk_account_code = c.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,c.dateFrom) AND DATEPART(YEAR,c.dateTo)
JOIN gmd_reporting_line d on c.fk_kostra_account_code = d.fk_kostra_account_code and report = 'YBUD1' and line_group_id = 3000
JOIN @accountstable ad ON  ad.fk_tenant_id = a.fk_tenant_id AND ad.fk_account_code = a.fk_account_code AND ad.type = 'PEN'
where a.fk_tenant_id = @tenant_id
and budget_year = @budget_year
and fk_employment_id = 0
and budget_type = 50
GROUP BY a.fk_tenant_id, a.budget_year, a.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4


INSERT INTO @checktable (fk_tenant_id, budget_year, fk_account_code, fk_department_code,org_id_2, org_id_3,org_id_4, amount_check, amount_real, check_type)

select a.fk_tenant_id, a.budget_year, a.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4,SUM(amount_year_1)*0.12,0,'HOL' 
from tbu_trans_detail a
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_accounts c on a.fk_tenant_id = c.pk_tenant_id and a.fk_account_code = c.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,c.dateFrom) AND DATEPART(YEAR,c.dateTo)
JOIN gmd_reporting_line d on c.fk_kostra_account_code = d.fk_kostra_account_code and report = 'YBUD1' and line_group_id = 3000
where a.fk_tenant_id = @tenant_id
and budget_year = @budget_year
and fk_employment_id = 0
and holiday_flag = 1
GROUP BY  a.fk_tenant_id, a.budget_year, a.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4


--Actual Holiday
INSERT INTO @checktable (fk_tenant_id, budget_year, fk_account_code, fk_department_code,org_id_2, org_id_3,org_id_4, amount_check, amount_real, check_type)

select  a.fk_tenant_id, a.budget_year, a.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4,0,SUM(amount_year_1),'HOL' 
from tbu_trans_detail a
JOIN tco_org_version ov ON a.fk_tenant_id = ov.fk_Tenant_id and (a.budget_year)*100+1 between ov.period_from and ov.period_to
JOIN tco_org_hierarchy oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
JOIN tco_accounts c on a.fk_tenant_id = c.pk_tenant_id and a.fk_account_code = c.pk_account_code AND a.budget_year BETWEEN DATEPART(YEAR,c.dateFrom) AND DATEPART(YEAR,c.dateTo)
JOIN gmd_reporting_line d on c.fk_kostra_account_code = d.fk_kostra_account_code and report = 'YBUD1' and line_group_id = 3000
where a.fk_tenant_id = @tenant_id
and budget_year = @budget_year
and fk_employment_id = 0
and fk_account_code NOT IN (SELECT fk_account_code FROM @accountstable WHERE type IN ('AGA','PEN'))
and budget_type = 50
GROUP BY a.fk_tenant_id, a.budget_year, a.fk_account_code, a.department_code, oh.org_id_2, oh.org_id_3, oh.org_id_4



IF OBJECT_ID('tempdb..#variabel_check_table') IS NOT NULL
BEGIN
DROP TABLE #variabel_check_table
END


SELECT a.fk_tenant_id, a.budget_year, fk_account_code,fk_department_code,org_id_2, org_id_3,org_id_4, check_type, 
sum(amount_check) AS amount_check, sum(amount_real) as amount_real,
 sum(amount_real)-sum(amount_check) as discrepency,  (sum(amount_real)-sum(amount_check)) * (tr.rate+1) as discrepency_incl_aga
INTO #variabel_check_table
FROM @checktable a
JOIN gco_tenants t ON a.fk_tenant_id = t.pk_id
JOIN [gmd_emp_tax_rates] tr on t.municipality_id = tr.fk_municipality_id --AND tr.budget_year = a.budget_year
GROUP BY a.fk_tenant_id, a.budget_year, fk_account_code,fk_department_code,org_id_2, org_id_3,org_id_4, check_type, tr.rate

UPDATE #variabel_check_table SET discrepency_incl_aga = discrepency WHERE check_type = 'AGA'


IF NOT EXISTS(SELECT name 
          FROM sysobjects 
          WHERE name = @table_name AND xtype = 'U')

BEGIN
    EXEC (
'SELECT *
INTO '+ @table_name + 
' FROM #variabel_check_table WHERE 1=2'
)

END

DELETE FROM TempVerifyPayroll WHERE fk_tenant_id = @tenant_id AND budget_year = @budget_year;

INSERT INTO TempVerifyPayroll (fk_tenant_id,budget_year,fk_account_code,fk_department_code,org_id_2,org_id_3,org_id_4,check_type,amount_check,amount_real,discrepency,discrepency_incl_aga)
SELECT fk_tenant_id,budget_year,fk_account_code,fk_department_code,org_id_2,org_id_3,org_id_4,check_type,amount_check,amount_real,discrepency,discrepency_incl_aga
FROM #variabel_check_table




EXEC (
'SELECT check_type, org_id_3, sum(amount_check) as amount_check, sum(amount_real) as amount_real, SUM(discrepency) AS discrepency, SUM(discrepency_incl_aga) AS discrepency_incl_aga
FROM ' + @table_name + 
' GROUP BY check_type, org_id_3
HAVING ABS(SUM(discrepency)) >= 1000
ORDER BY check_type, org_id_3'
)
print 'To get details query from table: SELECT * FROM ' + @table_name
GO

