CREATE OR ALTER PROCEDURE procDeleteExistingActionData   
@tenant_id INT,
@budget_year INT,
@udtBatchType udtBatchType readonly
AS
BEGIN          

DELETE FROM tfp_action_change_log WHERE fk_log_id in (SELECT log_id FROM tfp_trans_header WHERE pk_action_id in (SELECT CURR_PK_ID FROM tco_NewYearAction_Audit WHERE budget_year=@budget_year and fk_tenant_id=@tenant_id and batch_type in (select batchType from @udtBatchType)))
DELETE FROM tfp_trans_header WHERE pk_action_id in (SELECT CURR_PK_ID FROM tco_NewYearAction_Audit WHERE budget_year=@budget_year and fk_tenant_id=@tenant_id and batch_type in (select batchType from @udtBatchType))
DELETE FROM tfp_trans_detail WHERE fk_action_id in (SELECT CURR_PK_ID FROM tco_NewYearAction_Audit WHERE budget_year=@budget_year and fk_tenant_id=@tenant_id and batch_type in (select batchType from @udtBatchType))
DELETE FROM tco_NewYearAction_Audit WHERE budget_year=@budget_year and fk_tenant_id=@tenant_id and batch_type in (select batchType from @udtBatchType)

END