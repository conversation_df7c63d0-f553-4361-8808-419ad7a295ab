CREATE TABLE [dbo].[tfp_stage_action_import]
(
    [pk_id]              BIGINT          IDENTITY (1, 1) NOT NULL,
	[user_id]			 INT			 NOT NULL,
	[action_id]		     INT			 NULL DEFAULT 0,
	[action_title]		 NVARCHAR(MAX)   NULL DEFAULT (''),
    [tenant_id]			 INT             NOT NULL,
    [budget_year]        INT             NOT NULL,
	[isManuallyAdded]    INT NOT NULL DEFAULT 0,
    [account_code]		 NVARCHAR (25)   NULL DEFAULT (''),
    [department_code]    NVARCHAR (25)   NULL DEFAULT (''),
    [function_code]      NVARCHAR (25)   NULL DEFAULT (''),
    [project_code]       NVARCHAR (25)   NULL DEFAULT (''),
    [year_1_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_2_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_3_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_4_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
	[year_5_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_6_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_7_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_8_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [year_9_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
	[year_10_amount]      DECIMAL (18, 2) NULL DEFAULT 0,
    [change_id]			 INT             NOT NULL,
    [free_dim_1]         NVARCHAR (25)   DEFAULT (''),
    [free_dim_2]         NVARCHAR (25)   DEFAULT (''),
    [free_dim_3]         NVARCHAR (25)   DEFAULT (''),
    [free_dim_4]         NVARCHAR (25)   DEFAULT (''),
    [description]        NVARCHAR (MAX)  DEFAULT (''),
    [adjustment_code]	 NVARCHAR (25)   DEFAULT (''),
    [alter_code]		 NVARCHAR (25)   DEFAULT (''),
	[action_id_error]		BIT DEFAULT(0),
	[action_title_error]    BIT DEFAULT(0),
	[account_code_error]	BIT DEFAULT(0),
    [department_code_error] BIT DEFAULT(0),
    [function_code_error]   BIT DEFAULT(0),
    [project_code_error]    BIT DEFAULT(0),
	[free_dim_1_error]      BIT DEFAULT(0),
    [free_dim_2_error]      BIT DEFAULT(0),
    [free_dim_3_error]      BIT DEFAULT(0),
    [free_dim_4_error]      BIT DEFAULT(0),
    [adjustment_code_error]	BIT DEFAULT(0),
    [alter_code_error]		BIT DEFAULT(0), 
    [year_1_amount_error]	BIT DEFAULT(0), 
    [year_2_amount_error]	BIT DEFAULT(0), 
    [year_3_amount_error]	BIT DEFAULT(0), 
    [year_4_amount_error]	BIT DEFAULT(0), 
	[year_5_amount_error]	BIT DEFAULT(0),
    [year_6_amount_error]	BIT DEFAULT(0), 
    [year_7_amount_error]	BIT DEFAULT(0), 
    [year_8_amount_error]	BIT DEFAULT(0), 
    [year_9_amount_error]	BIT DEFAULT(0), 
	[year_10_amount_error]	BIT DEFAULT(0),
	[error_count] INT NULL DEFAULT (0), 
    [action_type] INT NOT NULL, 
    [line_order] INT NULL DEFAULT 0,
	[periodicKey] [nvarchar](25) NULL DEFAULT (''), 
	[periodicKey_error] [bit] NULL DEFAULT ((0)),
    [url] NVARCHAR (MAX) DEFAULT(''),
    [url_error]	BIT DEFAULT(0),
    [fk_user_adj_code]	NVARCHAR (25)   DEFAULT ('')
    CONSTRAINT [PK_tfp_stage_action_import] PRIMARY KEY ([pk_id])

)
