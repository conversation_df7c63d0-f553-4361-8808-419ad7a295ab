/*
In the  analysis section there is a "filter" where the users can figure out which other cities they
should be using for comparison. 
This table holds the default values that the different slider parameters have. 

*/


CREATE TABLE [dbo].[gko_slider_values](
	[pk_id] [int] IDENTITY(1,1) NOT NULL,
	[slider] NVARCHAR(50) NOT NULL,
	[title] NVARCHAR(50) NOT NULL,
	[min_pct_slider] [decimal](3, 1) NOT NULL,
	[max_pct_slider] [decimal](3, 1) NOT NULL,
	[min_value] [decimal](18, 2) NOT NULL,
	[max_value] [decimal](18, 2) NOT NULL,
	[display_order] [int] NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL, 
    CONSTRAINT [PK_gko_slider_values] PRIMARY KEY ([pk_id]), 
    /*CONSTRAINT [FK_gko_slider_values_gco_resources] FOREIGN KEY ([title]) REFERENCES gco_language_strings([ID])*/
	/*<PERSON><PERSON> - had to comment this out to fix the a problem*/
) 