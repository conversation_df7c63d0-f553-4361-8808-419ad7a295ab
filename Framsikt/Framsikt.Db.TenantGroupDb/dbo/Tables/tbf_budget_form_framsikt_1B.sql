CREATE TABLE [dbo].[tbf_budget_form_framsikt_1B]
(
	[pk_id] INT NOT NULL PRIMARY KEY IDENTITY,
	fk_tenant_id INT NOT NULL, 
	year INT NOT NULL,
	forecast_period INT NOT NULL DEFAULT 0,
	doc_type INT NOT NULL,
    fk_account_code NVARCHAR(25) NOT NULL, 
    fk_department_code NVARCHAR(25) NOT NULL, 
    fk_function_code NVARCHAR(25) NOT NULL, 
    free_dim_code NVARCHAR(25) NULL,
    actual_amt_year DECIMAL(18, 2) NOT NULL, 
    actual_amt_last_year DECIMAL(18, 2) NOT NULL,
	org_bud_amt_year DECIMAL (18, 2) NOT NULL,
	org_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_year DECIMAL (18, 2) NOT NULL,
	revised_bud_amt_last_year DECIMAL (18, 2) NOT NULL,
	finplan_year_1_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_2_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_3_amount DECIMAL (18, 2) NOT NULL,
	finplan_year_4_amount DECIMAL (18, 2) NOT NULL,
	forecast_amount DECIMAL (18, 2) NOT NULL,
		budget_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_period DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd_prev DECIMAL (18, 2) NULL DEFAULT 0,
	budget_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	accounting_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_ytd DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_forecast DECIMAL (18, 2) NULL DEFAULT 0,
	budget_change DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_action DECIMAL (18, 2) NULL DEFAULT 0,
	forecast_incl_dev DECIMAL (18, 2) NULL DEFAULT 0,
	deviation_incl_dev_action DECIMAL (18, 2) NULL DEFAULT 0,
	[aggregate_id] NVARCHAR(25) NOT NULL,
	[aggregate_name] NVARCHAR(200) DEFAULT(''),
	[fk_adjustment_code] NVARCHAR(25) DEFAULT '' NOT NULL,
	[fk_change_id] INT NOT NULL DEFAULT 0,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL,
	[revised_bud_auth_year] DECIMAL (18, 2) NOT NULL,
	[revised_bud_auth_last_year] DECIMAL (18, 2) NOT NULL
)
go


CREATE INDEX [IX_tbf_budget_form_framsikt_1B_1] ON [dbo].[tbf_budget_form_framsikt_1B] (doc_type, fk_tenant_id, year)
go
