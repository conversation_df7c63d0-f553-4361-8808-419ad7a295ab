/* These are the positions being used by the different tenants in their HR system.

This is being used in Staff Planning
*/

CREATE TABLE [dbo].[tco_positions]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [position_id] NVARCHAR(25) NOT NULL, 
    [position_name] NVARCHAR(150) NOT NULL, 
    [status] INT NOT NULL, 
    [year_from] INT NOT NULL, 
    [year_to] INT NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tco_positions] PRIMARY KEY ([pk_id]) 
)


GO

CREATE UNIQUE INDEX [IX_tco_positions_1] ON [dbo].[tco_positions] ([position_id], [fk_tenant_id], [year_from], [year_to])
go