CREATE TABLE [dbo].[tmr_effect_target_status]
(
   [pk_id] BIGINT NOT NULL IDENTITY,
   [fk_tenant_id] INT NOT NULL,
   [fk_target_distribution_id] UNIQUEIDENTIFIER NOT NULL,
   [fk_target_id] UNIQUEIDENTIFIER NOT NULL, 
   [fk_goal_id] UNIQUEIDENTIFIER NOT NULL,
   [value_type] NVARCHAR(25) NOT NULL,
   [indicator_value] NVARCHAR (100)  NOT NULL,
   [forecast_value] NVARCHAR (100)  NOT NULL DEFAULT(''),
   [status_desc_id_history] UNIQUEIDENTIFIER NOT NULL, 
   [isReported] BIT NOT NULL,
   [forecast_period] INT NOT NULL,
   [updated] DATETIME NOT NULL, 
   [updated_by] INT NOT NULL, 
   [fk_indicator_code] UNIQUEIDENTIFIER not null,
   [fk_target_detail_id] int not null,
   [status_desc] NVARCHAR(MAX) NOT NULL,
   [status] [int] NULL DEFAULT (0),
   [fk_target_ind_id] BIGINT NOT NULL DEFAULT 0,
   [fk_target_ind_id_old] BIGINT NOT NULL DEFAULT 0,
   [target_status_desc] NVARCHAR(MAX) NOT NULL DEFAULT(''),
   [target_status_desc_history] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
   [is_target_reported] BIT NOT NULL DEFAULT(1),
   CONSTRAINT [PK_tmr_effect_target_status] PRIMARY KEY ([pk_id])
)
