CREATE TABLE tbi_checklist_text_history  (
ID int IDENTITY(1,1),
budget_year int NOT NULL,
org_created_at varchar(20) DEFAULT (''),
service_id varchar(20) DEFAULT (''),
fk_tenant_id int NOT NULL,
created_by  int NOT NULL,
created_date datetime NOT NULL,
updated_by int  DEFAULT (0),
updated datetime DEFAULT (''),
status_text_historyid uniqueidentifier NULL,
status_text_history nvarchar(max) NULL,
forecast_period int DEFAULT NULL,
CONSTRAINT [PK_tbi_checklist_text_history] PRIMARY KEY ([ID])
)
