CREATE TABLE [dbo].[tin_import_resources]
(
	[pk_id] INT NOT NULL IDENTITY,
    [fk_tenant_id] INT NOT NULL, 
    [first_name] NVARCHAR(255) NOT NULL, 
    [last_name] NVARCHAR(255) NOT NULL, 
    [resource_id_ERP] NVARCHAR(25) NOT NULL, 
    [start_date] DATE NOT NULL, 
	end_date DATE NOT NULL,
	birth_date DATE NOT NULL,
    [status] INT NOT NULL, 
	import_status INT NOT NULL, 
    [batch_id] NVARCHAR(50) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tin_import_resources] PRIMARY KEY ([pk_id]), 
)

GO

CREATE UNIQUE INDEX [IND_tin_import_resources_1] ON [dbo].[tin_import_resources] (resource_id_ERP, fk_tenant_id, batch_id)
GO
