-- THIS TABLE IS IN USE IN INTEGRATIONS. INFORM INTEGRATION TEAM ABOUT DB CHANGES --


CREATE TABLE [dbo].[tco_functions](pk_id int not null IDENTITY,
	[pk_Function_code] NVARCHAR(25) NOT NULL,
	[pk_tenant_id] [int] NOT NULL,
	[display_name] NVARCHAR(100) NOT NULL,
	[description] NVARCHAR(500) NOT NULL,
	[fk_kostra_function_code] NVARCHAR(25) NOT NULL,
	[isActive] [bit] NOT NULL,
	[dateFrom] [datetime] NOT NULL,
	[dateTo] [datetime] NOT NULL,
	[updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tco_functions] PRIMARY KEY ([pk_id]) 
);


GO

CREATE INDEX [ind_tco_functions_1] ON [dbo].[tco_functions] (pk_function_code, pk_tenant_id)
go

CREATE INDEX [ind_tco_functions_2] ON [dbo].[tco_functions] (pk_tenant_id)
go

CREATE UNIQUE INDEX [ind_tco_functions_3] ON [dbo].[tco_functions] (pk_function_code, pk_tenant_id, isActive, datefrom, dateto)
go
