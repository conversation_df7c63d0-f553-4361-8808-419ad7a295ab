/* This table is storing the financial plan transactions for investments and finance. 

type = i (investment)
type = f (finance)
existing_flag = 1 (marks if rows are from original budget and should not be updated as part of the updates in investments


*/

CREATE TABLE [dbo].[tfp_inv_transactions]
(
	[pk_id] INT NOT NULL, 
	[fk_inv_action_id] int NOT NULL DEFAULT (0),
	[fk_inv_details_id] int NOT NULL,
    [fk_investment_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [department_code] NVARCHAR(25) NOT NULL, 
	[fk_project_code] NVARCHAR(25) NOT NULL, 
	[free_dim_1] NVARCHAR(25) NOT NULL, 
	[free_dim_2] NVARCHAR(25) NOT NULL, 
	[free_dim_3] NVARCHAR(25) NOT NULL, 
	[free_dim_4] NVARCHAR(25) NOT NULL, 
    [vat_rate] DECIMAL(18, 2) NOT NULL, 
	vat_refund DECIMAL(18, 2) NOT NULL, 
	[year_1_amount] DECIMAL(18, 2) NOT NULL, 
    [year_2_amount] DECIMAL(18, 2) NOT NULL, 
    [year_3_amount] DECIMAL(18, 2) NOT NULL, 
    [year_4_amount] DECIMAL(18, 2) NOT NULL, 
	[updated] DATETIME NOT NULL, 
	[type] NVARCHAR(1) NOT NULL,
    [updated_by] INT NOT NULL, 
    [existing_flag] INT NULL, 
	[fk_change_id] int not null,
	[fk_alter_code] nvarchar(50) NOT NULL,
	[fk_adjustment_code] nvarchar(50) NOT NULL,
    [fk_prog_code] NVARCHAR(25) DEFAULT 1 NULL, 
    CONSTRAINT [PK_tfp_inv_transactions] PRIMARY KEY ([pk_id]) 
)

GO

CREATE INDEX [ind_tfp_inv_transactions_1] ON [dbo].[tfp_inv_transactions] (budget_year, fk_tenant_id)
GO

CREATE INDEX [ind_tfp_inv_transactions_2] ON [dbo].[tfp_inv_transactions] ([fk_investment_id])
go

