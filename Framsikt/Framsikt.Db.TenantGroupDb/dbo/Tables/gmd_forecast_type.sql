/*
This table is defining which forecast type is valid. 
The forecast order defines in which order the different types are displayed in drop downs etc.

*/


CREATE TABLE [dbo].[gmd_forecast_type](
	[pk_forecast_type] NVARCHAR(4) NOT NULL,
	[forecast_name] NVARCHAR(50) NOT NULL,
	[forecast_order] [int] NOT NULL,
	[active] [smallint] NOT NULL,
 [updated_by] INT NOT NULL, 
    [updated] DATETIME NOT NULL, 
    CONSTRAINT [PK_gmd_forecast_type] PRIMARY KEY CLUSTERED 
(
	[pk_forecast_type] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF)
)
GO
/****** Object:  Index [IX_gmd_forecast_type_pk_forecast_type]    Script Date: 10/16/2014 11:10:23 AM ******/
CREATE NONCLUSTERED INDEX [IX_gmd_forecast_type_pk_forecast_type] ON [dbo].[gmd_forecast_type]
(
	[pk_forecast_type] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF)