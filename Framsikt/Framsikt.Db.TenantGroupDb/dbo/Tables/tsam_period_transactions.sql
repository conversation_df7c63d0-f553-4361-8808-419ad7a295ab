CREATE TABLE [dbo].[tsam_period_transactions]
(
[pk_id] INT NOT NULL PRIMARY KEY IDENTITY,
	[fk_tenant_id] INT NOT NULL,
	[forecast_period] INT NOT NULL, 
	[budget_year] INT NOT NULL, 
	[fk_action_id] INT NOT NULL,
	[new_fk_action_id] INT NULL,
	fk_account_code NVARCHAR (25) NOT NULL,
	department_code NVARCHAR (25) NOT NULL, 
	fk_function_code NVARCHAR (25) NOT NULL,
	fk_project_code NVARCHAR (25) NOT NULL,
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
	[fk_alter_code] NVARCHAR(25) NOT NULL, 
	[fp_amt] DECIMAL(18, 2) NOT NULL, 
	[final_amt] DECIMAL(18, 2) NOT NULL, 
	[final_description] NVARCHAR (MAX) NOT NULL,
	[prev_withdrawal_amt] DECIMAL(18, 2) NOT NULL,
	[Prev_reversal] DECIMAL(18, 2) NOT NULL, 
	[real_amount] DECIMAL(18, 2) NOT NULL, 
	[yearly_forecast] DECIMAL(18, 2) NOT NULL,
	[status] INT NOT NULL,
	[risk] INT NOT NULL,
	[description_department] NVARCHAR (MAX) NOT NULL,
	[reversal_amt] DECIMAL(18, 2) NOT NULL,
	[partial_reversal_amt] DECIMAL(18, 2) NOT NULL,
	[fund_reversal_amt] DECIMAL(18, 2) NOT NULL,
	[partial_reversal_2_amt] DECIMAL(18, 2) NOT NULL,
	[withdrawal_amt] DECIMAL(18, 2) NOT NULL,
	[reversal_amt_f] DECIMAL(18, 2) NOT NULL,
	[partial_reversal_amt_f] DECIMAL(18, 2) NOT NULL,
	[fund_reversal_amt_f] DECIMAL(18, 2) NOT NULL,
	[partial_reversal_2_amt_f] DECIMAL(18, 2) NOT NULL,
	[withdrawal_amt_f] DECIMAL(18, 2) NOT NULL,
	[description_final] NVARCHAR (MAX) NOT NULL,
	[updated] DATETIME NOT NULL, 
	[updated_by] INT NOT NULL,
	[prev_fund_reversal_amt] DECIMAL(18, 2) NOT NULL,
	[fk_user_adjustment_code] NVARCHAR (25) NULL,
	[internal_description]  nvarchar(max) NULL,
	[fk_adjustment_code_finplan] NVARCHAR(50) NOT NULL DEFAULT ''
)
