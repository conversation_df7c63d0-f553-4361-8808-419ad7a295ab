CREATE TABLE [dbo].[twh_investment_report_data]
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_main_project_code] NVARCHAR(25) NOT NULL, 
    [main_project_name] NVARCHAR(100) NOT NULL, 
    [inv_status] INT NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [project_name] NVARCHAR(250) NOT NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL, 
    [fk_change_id] INT NOT NULL, 
    [year_minus_2_amount] DECIMAL(38, 2) NOT NULL, 
    [year_minus_1_amount] DECIMAL(38, 2) NOT NULL, 
    [year_0_amount] DECIMAL(38, 2) NOT NULL, 
    [year_1_amount] DECIMAL(38, 2) NOT NULL, 
    [year_2_amount] DECIMAL(38, 2) NOT NULL,
    [year_3_amount] DECIMAL(38, 2) NOT NULL, 
    [year_4_amount] DECIMAL(38, 2) NOT NULL, 
    [year_5_amount] DECIMAL(38, 2) NOT NULL,
    [year_6_amount] DECIMAL(38, 2) NOT NULL, 
    [year_7_amount] DECIMAL(38, 2) NOT NULL, 
    [year_8_amount] DECIMAL(38, 2) NOT NULL,
    [year_9_amount] DECIMAL(38, 2) NOT NULL, 
    [year_10_amount] DECIMAL(38, 2) NOT NULL, 
    [year_11_amount] DECIMAL(38, 2) NOT NULL,
    [year_12_amount] DECIMAL(38, 2) NOT NULL, 
    [year_13_amount] DECIMAL(38, 2) NOT NULL, 
    [year_14_amount] DECIMAL(38, 2) NOT NULL,
    [year_15_amount] DECIMAL(38, 2) NOT NULL, 
    [year_16_amount] DECIMAL(38, 2) NOT NULL, 
    [year_17_amount] DECIMAL(38, 2) NOT NULL,
    [year_18_amount] DECIMAL(38, 2) NOT NULL, 
    [year_19_amount] DECIMAL(38, 2) NOT NULL, 
    [actual_amt_year_minus_4] DECIMAL(38, 2) NOT NULL, 
    [actual_amt_year_minus_3] DECIMAL(38, 2) NOT NULL, 
    [actual_amt_year_minus_2] DECIMAL(38, 2) NOT NULL, 
    [actual_amt_year_minus_1] DECIMAL(38, 2) NOT NULL, 
    [actual_amt_year_0] DECIMAL(38, 2) NOT NULL, 
    [owning_dept] NVARCHAR(25) NOT NULL, 
    [owning_function] NVARCHAR(25) NOT NULL, 
    [adjustment_code_status] NVARCHAR(25) NOT NULL, 
    [bud_process] NVARCHAR(25) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
)
