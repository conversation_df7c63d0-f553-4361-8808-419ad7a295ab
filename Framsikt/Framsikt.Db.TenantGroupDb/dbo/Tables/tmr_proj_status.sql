CREATE TABLE [dbo].[tmr_proj_status](
       [pk_id] [bigint] IDENTITY(1,1) NOT NULL,
       [fk_tenant_id] [int] NOT NULL,
       [forecast_period] [int] NOT NULL,
       [level] [nvarchar](25) NOT NULL,
       [level_id] [nvarchar](25) NOT NULL,
       [fk_main_project_code] [nvarchar](25) NOT NULL,
       [est_finish_quarter] [int] NOT NULL,
       [status_desc] [nvarchar](max) NOT NULL,
       [status_desc_id_history] [uniqueidentifier] NOT NULL,
       [status] [int] NOT NULL DEFAULT ((0)),
       [risk] [int] NULL DEFAULT ((0)),
       [fin_status] [int] NOT NULL DEFAULT ((0)),
       [quality] [int] NOT NULL DEFAULT ((0)),
       [is_reported] [bit] NOT NULL,
       [reported_status] [int] NOT NULL DEFAULT ((0)),
       [new_deadline] [datetime] NULL,
       [updated] [datetime] NOT NULL,
       [updated_by] [int] NOT NULL,
[is_ISY_import] BIT NOT NULL DEFAULT 0, 
    CONSTRAINT [PK_tmr_proj_status] PRIMARY KEY CLUSTERED 
(
       [pk_id] ASC
))

GO

