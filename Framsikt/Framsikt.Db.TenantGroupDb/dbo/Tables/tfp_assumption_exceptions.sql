CREATE TABLE [dbo].[tfp_assumption_exceptions]
(
	[pk_id] UNIQUEIDENTIFIER NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
	[fk_account_code]   NVARCHAR (25)   NOT NULL,
    [department_code]   NVARCHAR (25)   NOT NULL,
    [function_code]     <PERSON>VA<PERSON><PERSON>R (25)   NOT NULL,
    [project_code]      NVARCHAR (25)   NOT NULL,
	[free_dim_1]        NVARCHAR (25)   NOT NULL,
    [free_dim_2]        NVARCHAR (25)   NOT NULL,
    [free_dim_3]        NVARCHAR (25)   NOT NULL,
    [free_dim_4]        NVARCHAR (25)   NOT NULL,
	[rate]				DECIMAL(18, 3)	NOT NULL, 
	[comments]			nvarchar(500) NOT NULL,
    [updated]           DATETIME        NOT NULL,
    [updated_by]        INT             NOT NULL,
    CONSTRAINT [PK_tfp_assumption_exceptions] PRIMARY KEY ([pk_id]) 
)
