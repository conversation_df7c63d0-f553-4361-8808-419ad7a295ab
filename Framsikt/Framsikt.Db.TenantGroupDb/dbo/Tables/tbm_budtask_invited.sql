/* This table connects invited users to a budget task and service area from Budget management screen.

*/


CREATE TABLE [dbo].[tbm_budtask_invited]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_bud_task_id] INT NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [fk_user_id] INT NOT NULL, 
    [fk_org_id] varchar(5) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tbm_budtask_invited] PRIMARY KEY ([pk_id]) 
)

GO

CREATE UNIQUE INDEX [IND_tbm_budtask_invited_1] ON [dbo].[tbm_budtask_invited] (fk_bud_task_id, fk_tenant_id, fk_user_id, fk_org_id)
