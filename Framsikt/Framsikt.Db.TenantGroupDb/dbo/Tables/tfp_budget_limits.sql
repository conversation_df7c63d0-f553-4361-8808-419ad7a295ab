CREATE TABLE [dbo].[tfp_budget_limits]
(
	[pk_id] INT NOT NULL  IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fp_level_1_value] NVARCHAR(25) NOT NULL, 
    [fp_level_2_value] NVARCHAR(25) NOT NULL,  
	[action_type] INT NOT NULL,
    [year_1_limit] DECIMAL(18, 2) NOT NULL, 
    [year_2_limit] DECIMAL(18, 2) NOT NULL, 
    [year_3_limit] DECIMAL(18, 2) NOT NULL, 
    [year_4_limit] DECIMAL(18, 2) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [fk_org_id] NVARCHAR(25) NULL, 
    CONSTRAINT [PK_tfp_budget_limits] PRIMARY KEY ([pk_id])
)

GO

CREATE UNIQUE INDEX [IND_tfp_budget_limits_1] ON [dbo].[tfp_budget_limits] (action_type, [fp_level_1_value], [fp_level_2_value], budget_year, [fk_tenant_id])
GO
