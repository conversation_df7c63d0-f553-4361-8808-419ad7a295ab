
CREATE TABLE flat_org_hierarchy_dep
(
[pk_id] INT IDENTITY(1,1) PRIMARY KEY CLUSTERED,
[pk_org_version] varchar(25) NOT NULL,
[fk_tenant_id] int NOT NULL,
[budget_year] int NOT NULL,
[org_id_1] nvarchar(25) NOT NULL,
[org_name_1] nvarchar(125) NOT NULL,
[org_id_2] nvarchar(25) NOT NULL,
[org_name_2] nvarchar(125) NOT NULL,
[org_id_3] nvarchar(25) NOT NULL,
[org_name_3] nvarchar(125) NOT NULL,
[org_id_4] nvarchar(25) NOT NULL,
[org_name_4] nvarchar(125) NOT NULL,
[org_id_5] nvarchar(25) NOT NULL,
[org_name_5] nvarchar(125) NOT NULL,
[fk_department_code] nvarchar(25) NOT NULL,
[department_name] nvarchar(125) NOT NULL,
[org_id_6] nvarchar(50) NULL,
[org_name_6] nvarchar(200) NULL,
[org_shortname_6] nvarchar(50) NULL,
[org_id_7] nvarchar(50) NULL,
[org_name_7] nvarchar(200) NULL,
[org_shortname_7] nvarchar(50) NULL,
[org_id_8] nvarchar(50) NULL,
[org_name_8] nvarchar(200) NULL,
[org_shortname_8] nvarchar(50) NULL,
[department_setup_missing] INT NOT NULL, 
[updated] DATETIME NOT NULL, 
[updated_by] INT NOT NULL
)