-- THIS TABLE IS IN USE IN INTEGRATIONS. INFORM INTEGRATION TEAM ABOUT DB CHANGES --

CREATE TABLE [dbo].[tco_departments]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [pk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [department_name] NVARCHAR(125) NOT NULL, 
    [status] INT NOT NULL, 
    [year_from] INT NOT NULL, 
    [year_to] INT NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tco_departments] PRIMARY KEY ([pk_id]) 
)


GO

CREATE INDEX [IX_tco_departments_1] ON [dbo].[tco_departments] ([pk_department_code], [fk_tenant_id], [year_from], [year_to])
go
