/* Table for defining tenant specific setup

*/

CREATE TABLE [dbo].[tco_parameters]
(
	[pk_id] INT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [param_name] NVARCHAR(50) NOT NULL, 
    [param_value] NVARCHAR(100) NOT NULL, 
    [fk_language_id] NVARCHAR(50) NOT NULL, 
	active INT NOT NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL,
)
Go
CREATE UNIQUE INDEX [ind_tco_parameters_1]  ON tco_parameters (param_name, param_value, fk_tenant_id)

go


