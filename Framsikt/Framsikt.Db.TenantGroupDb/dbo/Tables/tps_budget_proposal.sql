CREATE TABLE [dbo].[tps_budget_proposal]
(
	[pk_proposal_Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY, 
    [proposal_name] varchar(100) NULL,
	--[isActive] bit NULL CONSTRAINT [DF_tps_budget_proposal_isActive]  DEFAULT ((0)),
	[budget_year] int NULL,
	[isDataPopulated] bit NULL CONSTRAINT [DF_tps_budget_proposal_isDataPopulated]  DEFAULT ((0)),
	[updated_date] datetime NULL,
	[updated_by] int NULL,
	[created_by] int NULL,
	[fk_tenant_id] int NULL,
    [action_description_id] UNIQUEIDENTIFIER NOT NULL DEFAULT ('00000000-0000-0000-0000-000000000000'), 
	[investment_description_id] UNIQUEIDENTIFIER NOT NULL DEFAULT ('00000000-0000-0000-0000-000000000000'), 
	[is_published] bit not null default 0,
	[publish_template_id] int not null default 0, 
    [introduction_description_id] UNIQUEIDENTIFIER NOT NULL DEFAULT ('00000000-0000-0000-0000-000000000000'), 
    [introduction_description] NVARCHAR(MAX) NULL,
	[budgetphase_id] UNIQUEIDENTIFIER NOT NULL DEFAULT ('00000000-0000-0000-0000-000000000000'),
	[created_date] DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP

)