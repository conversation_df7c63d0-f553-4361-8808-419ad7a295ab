create table tco_publish_review_parent(
	[pk_id] [int] IDENTITY(1,1) NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[template_id] [int] NOT NULL,
	[baseTemplateId] [int] NOT NULL,
	[tree_type] [nvarchar](50) NOT NULL,
	[name] [nvarchar](500) NULL,
	[shortName] [nvarchar](250) NULL,
	[isGlobal] [bit] NULL,
	[isDefault] [bit] NULL,
	[budgetYear] [int] NOT NULL,
	[kostraTemplateId] [uniqueidentifier] NOT NULL,
	[includeInternalDesc] [bit] NOT NULL,
	[budgetPhaseId] [uniqueidentifier] NOT NULL,
	[showOnlyModified] [bit] NULL,
	[displayOnlyTables] [bit] NULL,
	[displayIndicatorOption] [nvarchar](250) NULL,
	[forecastPeriod] [int] NULL,
	[publishTemplateId] [int] NULL,
	[version] [int] NULL,
	[level1Exists] [bit] NULL,
	[level2Exists] [bit] NULL,
	[partialSection] [int] NULL,
	[showDelegatedTasks] [bit] NULL,
	[config_id] [int] NOT NULL,
	[processId] [uniqueidentifier] NOT NULL
)
