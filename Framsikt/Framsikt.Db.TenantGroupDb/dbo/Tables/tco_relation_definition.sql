CREATE TABLE [dbo].[tco_relation_definition]
(
	[pk_id] UNIQUEIDENTIFIER NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [attribute_type] NVARCHAR(25) NOT NULL, 
	[relation_type] NVARCHAR(25) NOT NULL, 
	[status] INT NOT NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [flag] BIT,
    [relation_name] NVARCHAR(25) NOT NULL,
    [attribute_type_name] NVARCHAR(25) NOT NULL,
    [relation_type_from_name] NVARCHAR(25) NOT NULL,
    [relation_type_to_name] NVARCHAR(25) NOT NULL,
    [rule_type] INT NOT NULL DEFAULT 0,
    CONSTRAINT [PK_tco_relation_definition] PRIMARY KEY ([pk_id]) 
)

GO

CREATE UNIQUE INDEX [IND_tco_relation_definition_1] ON [dbo].[tco_relation_definition] ([attribute_type], [relation_type], [fk_tenant_id])
go
