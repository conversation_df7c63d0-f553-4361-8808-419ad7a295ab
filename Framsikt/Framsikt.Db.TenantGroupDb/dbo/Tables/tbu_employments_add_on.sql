/* This table is being used by staff planning

Here is the addons stored that by the fk_employment_id column is linked to the staff planning table
tbu employments. 

*/

CREATE TABLE [dbo].[tbu_employments_add_on]
(
	[pk_id] INT NOT NULL IDENTITY, 
	[fk_tenant_id] INT NOT NULL,
    [fk_employment_id] BIGINT NOT NULL, 
    [ext_add_code] NVARCHAR(25) NOT NULL, 
    [add_on_name] NVARCHAR(150) NOT NULL, 
	[fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
	original_amount DECIMAL(18,2) NOT NULL,
    [amount_month] DECIMAL(18, 2) NOT NULL, 
    [amount_year] DECIMAL(18, 2) NOT NULL, 
    [start_period] INT NOT NULL, 
    [end_period] INT NOT NULL, 
    [pension_flag] INT NOT NULL, 
	[holiday_flag] INT NOT NULL,
	[social_expense_flag] int not null,
	[tax_flag] INT NOT NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL,
	[amount_pension] [decimal](18, 2) NOT NULL DEFAULT ((0)),
	[amount_aga_pension] [decimal](18, 2) NOT NULL DEFAULT ((0)),
	[amount_aga_salary] [decimal](18, 2) NOT NULL DEFAULT ((0)),
	[amount_aga_holiday] [decimal](18, 2) NOT NULL DEFAULT ((0)),
	[fk_salary_acc_category] NVARCHAR(50) NOT NULL DEFAULT(''), 
    [fk_add_on_id] NVARCHAR(25) NOT NULL DEFAULT '',
	CONSTRAINT [PK_tbu_employments_add_on] PRIMARY KEY ([pk_id])
)

GO

CREATE INDEX [IND_tbu_employments_add_on_1] ON [dbo].[tbu_employments_add_on] (fk_employment_id, fk_tenant_id)
go
CREATE NONCLUSTERED INDEX IX_tbu_employments_add_on_fk_tenant_id_fk_employment_id
    ON tbu_employments_add_on (fk_tenant_id, fk_employment_id);   
GO  

