CREATE TABLE [dbo].[tpl_plan]
(
	[pk_plan_id] uniqueidentifier NOT NULL PRIMARY KEY,
	[name] nvarchar(300) NOT NULL,
	[short_name] nvarchar(32) NULL,
	[fk_plan_type_id] uniqueidentifier NOT NULL,
	[fk_plan_stategy_id] uniqueidentifier NOT NULL,
	[fk_plan_category_id] int NOT NULL,
	[state_funds] bit Default(0),
	[parent_plan_id] uniqueidentifier NOT NULL,
	[fk_status_id] int NULL,
	[created_by] int NOT NULL,
	[created_date] datetime NOT NULL,
	[updated_by] int NOT NULL DEFAULT (0),
	[updated] datetime NOT NULL,
	[fk_tenant_id] int NOT NULL,
	[contenttypesused] NVARCHAR(500) NOT NULL DEFAULT '',
	[usage] nvarchar(20) NOT NULL,
	[publish_template_id] int not null default 0,
	[is_old_plan] BIT NOT NULL DEFAULT(0) ,
	[display_number] BIT NOT NULL DEFAULT(0),
	[start_year] int not null default 0,
	[treated_case] nvarchar(255) null,
    [date_processed] datetime  null,
    [link_to_casefile] nvarchar(255) null,
	[is_soft_delete] bit not null Default(0),
	[fk_strategy_task_id] uniqueidentifier NOT NULL DEFAULT ('00000000-0000-0000-0000-000000000000'),
	[display_widget] bit NOT NULL DEFAULT(0),
	[max_tree_depth] int NOT NULL DEFAULT(0),
	[isTableGroup] bit NOT NULL DEFAULT(0),
	[valid_from] int not null default 0,
	[valid_to] int not null default 0,
	[planned_revision] int not null default 0,
	[is_included_in_fp_doc] bit not null  DEFAULT (1),
	[plan_category_type] nvarchar(500)  DEFAULT ('')
)