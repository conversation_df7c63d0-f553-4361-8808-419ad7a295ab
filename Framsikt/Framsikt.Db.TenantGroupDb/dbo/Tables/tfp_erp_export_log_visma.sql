CREATE TABLE [dbo].[tfp_erp_export_log_visma]
(
    [export_id]          BIGINT          NOT NULL,
    [fk_tenant_id]       INT             NOT NULL,
	[company]			 NVARCHAR(100)   DEFAULT ('') NOT NULL,
	[region]			 NVARCHAR(100)   DEFAULT ('') NOT NULL,
	[description]        NVARCHAR (700) NOT NULL,
	[budget_year]        INT             NOT NULL,
    [fk_account_code]    NVARCHAR (25)   NOT NULL,
    [department_code]    NVARCHAR (25)   NOT NULL,
    [fk_function_code]   NVARCHAR (25)   NOT NULL,
    [fk_project_code]    NVARCHAR (25)   NOT NULL,
    [free_dim_1]         NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [free_dim_2]         NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [free_dim_3]         NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [free_dim_4]         NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [free_dim_5]         NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [free_dim_6]         NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [fk_adjustment_code] NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [fk_alter_code]      NVARCHAR (25)   DEFAULT ('') NOT NULL,
    [revised_budget]     DECIMAL (18, 2) NOT NULL,
	fk_action_id		 INT NOT NULL,
    [jan]				 DECIMAL (18, 2) NOT NULL,
    [feb]				 DECIMAL (18, 2) NOT NULL,
    [mar]				 DECIMAL (18, 2) NOT NULL,
    [apr]				 DECIMAL (18, 2) NOT NULL,
    [may]				 DECIMAL (18, 2) NOT NULL,
    [jun]				 DECIMAL (18, 2) NOT NULL,
    [jul]				 DECIMAL (18, 2) NOT NULL,
    [aug]				 DECIMAL (18, 2) NOT NULL,
    [sep]				 DECIMAL (18, 2) NOT NULL,
    [oct]				 DECIMAL (18, 2) NOT NULL,
    [nov]				 DECIMAL (18, 2) NOT NULL,
    [dec]				 DECIMAL (18, 2) NOT NULL,
	[pk_id]             int identity(1,1) primary key
)

