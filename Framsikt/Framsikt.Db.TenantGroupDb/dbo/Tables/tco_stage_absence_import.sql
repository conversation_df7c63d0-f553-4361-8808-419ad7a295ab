CREATE TABLE [dbo].[tco_stage_absence_import](
	[pk_id] [int] IDENTITY(1,1) NOT NULL primary key,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[user_id] [int] NOT NULL,
	[period] [int] NOT NULL,
	[department_code] [nvarchar](50) NOT NULL,
	[function_code] [nvarchar](50) NULL,
	[workinghours] [decimal](9,2) NULL,
	[shortabshours] [decimal](9,2) NULL,
	[longabshours] [decimal](9,2) NULL,
	[dateofbirth] [nvarchar](25) NULL,
	[gender] [nvarchar](25) NULL,
	[budget_year_error] [bit] NULL,
	[period_error] [bit] NULL,
	[department_code_error] [bit] NULL,
	[function_code_error] [bit] NULL,
	[workinghours_error] [bit] NULL,
	[shortabshours_error] [bit] NULL,
	[longabshours_error] [bit] NULL,
	[dateofbirth_error] [bit] NULL,
	[gender_error] [bit] NULL,
	[error_count] [int] NULL,
	[job_id] [bigint] not null default (0))