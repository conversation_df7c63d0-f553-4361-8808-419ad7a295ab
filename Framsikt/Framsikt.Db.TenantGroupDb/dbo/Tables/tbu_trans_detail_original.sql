
CREATE TABLE [dbo].[tbu_trans_detail_original]
(
	[pk_id] [uniqueidentifier] not null, 
    [bu_trans_id] UNIQUEIDENTIFIER NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [action_type] INT NOT NULL, 
    [line_order] INT NOT NULL, 
	fk_account_code NVARCHAR (25) NOT NULL,
	department_code NVARCHAR (25) NOT NULL, 
	fk_function_code NVARCHAR (25) NOT NULL,
	fk_project_code NVARCHAR (25) NOT NULL,
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
	resource_id NVARCHAR (25) NOT NULL,
	fk_employment_id BIGINT NOT NULL,
	description NVARCHAR (255) NOT NULL,
    [budget_year] INT NOT NULL, 
    [period] INT NOT NULL, 
    [budget_type] INT NOT NULL, 
    [amount_year_1] DECIMAL(18, 2) NOT NULL,
	fk_key_id INT NOT NULL,
    [allocation_pct] DECIMAL(18, 10) NOT NULL, 
    [total_amount] DECIMAL(18, 2) NOT NULL, 
    [tax_flag] INT NOT NULL, 
    [holiday_flag] INT NOT NULL, 
	[fk_pension_type] [nvarchar](12) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [fk_prog_code] NVARCHAR(25) NULL, 
	[fk_investment_id] [int] NOT NULL DEFAULT ((0)),
	[fk_main_project_code] [nvarchar](50) NULL,
	[fk_portfolio_code] [nvarchar](50) NULL,
	[fk_adjustment_code] NVARCHAR(25) NOT NULL, 
    [fk_alter_code] NVARCHAR(25) NOT NULL, 
    CONSTRAINT [PK_tbu_trans_detail_original] PRIMARY KEY ([pk_id])
)

GO

CREATE INDEX [IND_tbu_trans_detail_original_1] ON [dbo].[tbu_trans_detail_original] (fk_account_code, budget_year, fk_tenant_id)
GO
CREATE INDEX [IND_tbu_trans_detail_original_2] ON [dbo].[tbu_trans_detail_original] (action_type, line_order, budget_year, fk_tenant_id)
GO
CREATE  INDEX [IND_tbu_trans_detail_original_3] ON [dbo].[tbu_trans_detail_original] (fk_account_code, department_code, fk_function_code, fk_project_code, free_dim_1, fk_tenant_id, budget_year)
go