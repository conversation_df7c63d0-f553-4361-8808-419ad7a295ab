CREATE TABLE [dbo].[tmr_stage_action_import]
(
	pk_id bigint IDENTITY(1,1) NOT NULL,
	fk_tenant_id INT NOT NULL,
	user_id INT NOT NULL,
	forecast_period int not null,
	budget_year int not null,
	action_id int NULL DEFAULT 0,
	action_title nvarchar(MAX) NULL DEFAULT (''),
	account_code nvarchar(25) NULL DEFAULT (''),
	department_code nvarchar(25) NULL DEFAULT (''),
	function_code nvarchar(25) NULL DEFAULT (''),
	project_code nvarchar(25) NULL DEFAULT (''),
	free_dim_1 nvarchar(25) NULL DEFAULT (''),
	free_dim_2 nvarchar(25) NULL DEFAULT (''),
	free_dim_3 nvarchar(25) NULL DEFAULT (''),
	free_dim_4 nvarchar(25) NULL DEFAULT (''),
	year_1_amount decimal (18, 2) NULL DEFAULT 0,
    year_2_amount DECIMAL (18, 2) NULL DEFAULT 0,
    year_3_amount DECIMAL (18, 2) NULL DEFAULT 0,
    year_4_amount DECIMAL (18, 2) NULL DEFAULT 0,
	year_5_amount  DECIMAL (18, 2) NULL DEFAULT 0,
	alter_code nvarchar (25)  DEFAULT (''),
	description nvarchar(max) NULL DEFAULT (''),
	periodicKey nvarchar(25) NULL DEFAULT (''), 
	[adjustment_code] NVARCHAR (25)  DEFAULT (''),
	[action_id_error]		BIT DEFAULT(0),
	[action_title_error]    BIT DEFAULT(0),
	[account_code_error]	BIT DEFAULT(0),
    [department_code_error] BIT DEFAULT(0),
    [function_code_error]   BIT DEFAULT(0),
    [project_code_error]    BIT DEFAULT(0),
	[free_dim_1_error]      BIT DEFAULT(0),
    [free_dim_2_error]      BIT DEFAULT(0),
    [free_dim_3_error]      BIT DEFAULT(0),
    [free_dim_4_error]      BIT DEFAULT(0),
    [adjustment_code_error]	BIT DEFAULT(0),
    [alter_code_error]		BIT DEFAULT(0), 
    [year_1_amount_error]	BIT DEFAULT(0), 
    [year_2_amount_error]	BIT DEFAULT(0), 
    [year_3_amount_error]	BIT DEFAULT(0), 
    [year_4_amount_error]	BIT DEFAULT(0), 
	[year_5_amount_error]	BIT DEFAULT(0),
	[periodicKey_error] BIT DEFAULT(0),
	[error_count] INT NULL DEFAULT (0),
	[action_type] INT NOT NULL, 
	[line_order] INT NULL DEFAULT 0,
	[change_id]	INT NOT NULL DEFAULT -1,
	[isManuallyAdded]  INT NOT NULL DEFAULT 0,
	[isCentralAccount] BIT DEFAULT(0),
	CONSTRAINT [PK_tmr_stage_action_import] PRIMARY KEY (pk_id)
)
