/* This table is being used in KOSTRA COST REDUCTION TABLE. Defines the production indicators per reporting area that
are visible drilldown/mouser icon in screen. 
*/

CREATE TABLE [dbo].[gko_prod_indicators]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_report_area_code] NVARCHAR(25) NOT NULL, 
	[fk_region_code] NVARCHAR(25) NOT NULL,
	[budget_year] INT NOT NULL,
	[title] NVARCHAR(255) NOT NULL,
	[weight_value] DECIMAL(18, 3) NOT NULL, 
	[value] DECIMAL(18, 3) NOT NULL, 
	[total_index_value] DECIMAL(18, 3) NOT NULL, 
    [active] INT NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_gko_prod_indicators] PRIMARY KEY ([pk_id]) 
)

GO

CREATE INDEX [IND_gko_prod_indicators_1] ON [dbo].[gko_prod_indicators] (fk_region_code, fk_report_area_code, budget_year)
go

CREATE INDEX [IND_gko_prod_indicators_2] ON [dbo].[gko_prod_indicators] (fk_region_code, fk_report_area_code, budget_year, total_index_value)
go

CREATE INDEX [IND_gko_prod_indicators_3] ON [dbo].[gko_prod_indicators] (fk_region_code, total_index_value)
go