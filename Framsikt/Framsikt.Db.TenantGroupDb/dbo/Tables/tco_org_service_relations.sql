CREATE TABLE [dbo].[tco_org_service_relations]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [org_id_1] NVARCHAR(25) NOT NULL, 
    [org_id_2] NVARCHAR(25) NOT NULL, 
    [org_id_3] NVARCHAR(25) NOT NULL, 
    [org_id_4] NVARCHAR(25) NOT NULL, 
    [org_id_5] NVARCHAR(25) NOT NULL, 
	[fk_function_code] NVARCHAR(25) NOT NULL, 
	[function_name] NVARCHAR(100) NOT NULL, 
    [service_id_1] NVARCHAR(25) NOT NULL, 
	[service_id_2] NVARCHAR(25) NOT NULL, 
	[service_id_3] NVARCHAR(25) NOT NULL, 
	[service_id_4] NVARCHAR(25) NOT NULL, 
	[service_id_5] NVARCHAR(25) NOT NULL,
    [service_name_1] NVARCHAR(100) NOT NULL,
    [service_name_2] NVARCHAR(100) NOT NULL,
    [service_name_3] NVARCHAR(100) NOT NULL,
    [service_name_4] NVARCHAR(100) NOT NULL,
    [service_name_5] NVARCHAR(100) NOT NULL, 
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tco_org_service_relations] PRIMARY KEY ([pk_id]) 
)
