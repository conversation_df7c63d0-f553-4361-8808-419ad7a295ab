CREATE TABLE [dbo].[tco_climate_action_detail]
(
  pk_id int NOT NULL PRIMARY KEY IDENTITY,
  fk_climate_action_id INT NOT NULL,
  fk_tenant_id INT NOT NULL,
  budget_year INT NOT NULL,
  fk_climate_sector_id INT,
  fk_climate_source_id INT,
  fk_emission_type_id INT,
  reduction_year1_val DECIMAL(18, 2) NOT NULL,
  reduction_year2_val DECIMAL(18, 2) NOT NULL,
  reduction_year3_val DECIMAL(18, 2) NOT NULL,
  reduction_year4_val DECIMAL(18, 2) NOT NULL,
  reduction_year5_val DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_year6_val DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_year7_val DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_year8_val DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_year9_val DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_year10_val DECIMAL(18, 2) NOT NULL DEFAULT(0),
  updated DATETIME,
  updated_by INT,
  free_dim_1 NVARCHAR (25)  NULL,
  free_dim_2 NVARCHAR (25)  NULL,
  free_dim_3 NVARCHAR (25)  NULL,
  free_dim_4 NVARCHAR (25) NULL,
  fk_department_code NVARCHAR (25) DEFAULT ('') NOT NULL,
  fk_function_code NVARCHAR (25) DEFAULT ('') NOT NULL,
  fk_project_code NVARCHAR (25) DEFAULT ('') NOT NULL,
  long_term_reduction DECIMAL(18, 2) NOT NULL default 0,
  reduction_prevYear1_val  DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_prevYear2_val  DECIMAL(18, 2) NOT NULL DEFAULT(0),
  reduction_quantity int NOT NULL DEFAULT(0)
  FOREIGN KEY (fk_climate_action_id) REFERENCES tco_climate_action_header(pk_climate_action_id)
)
