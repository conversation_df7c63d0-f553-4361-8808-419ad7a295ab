
create table tsa_assessment_delegation
(
	[pk_id] int not null identity(1,1) primary key,
	[fk_tenant_id] [int] NOT NULL,
	[fk_assessment_id] uniqueidentifier NOT NULL,
	[org_id] varchar(50) not null,
	[org_level] int not null,
	[service_id] varchar(50) not null,
	[service_level] int not null,
	[updated_by] INT NOT NULL,
	[updated] [datetime] NOT NULL,
	[is_owner] [Bit] null,
	[assessment_status] int NULL,
	[is_visible] [int] DEFAULT 1 NULL,
	[assessment_desc] varchar(max) NULL,
	[assessment_owner_desc] varchar(max) NULL,
	[is_owner_delegated_to] bit null,
	[assessment_desc_copy_updated] varchar(50) NOT NULL default '',
	[assessment_desc_copy_updated_by] INT NOT NULL default 0,
	[assessment_desc_log_id] UNIQUEIDENTIFIER DEFAULT NEWID() NOT NULL,
	[assessment_owner_desc_log_id] UNIQUEIDENTIFIER DEFAULT NEWID() NOT NULL,
)