CREATE TABLE tmr_planreport_status(
	[pk_id] int IDENTITY(1,1) Primary Key,
	[fk_plan_id] uniqueidentifier NOT NULL,
	[fk_tenant_id] int NOT NULL,
    [forecast_period] INT NOT NULL,
	[org_id] varchar(20) NOT NULL DEFAULT (''),
	[org_level] int NOT NULL DEFAULT (0),
	[service_id] varchar(20) NOT NULL DEFAULT (''),
	[plan_status_desc] NVARCHAR(MAX) NOT NULL DEFAULT '',
	[plan_stdesc_id] uniqueidentifier NOT NULL,
    [status] INT NOT NULL DEFAULT 0,
    [risk] INT NOT NULL DEFAULT 0,
	[is_published] BIT NOT NULL DEFAULT(0),
	[updated_by] int NOT NULL DEFAULT (0),
	[updated] datetime NOT NULL,
	FOREIGN KEY ([fk_plan_id]) REFERENCES tpl_plan([pk_plan_id]) 
)
