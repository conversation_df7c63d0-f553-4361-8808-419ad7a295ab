CREATE TABLE [dbo].[tfp_finplan_warehouse]
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL,
	[action_type] [int] NOT NULL,
	[line_order] [int] NOT NULL, 
	[fk_department_code] nvarchar(25) NOT NULL,
	[fk_function_code] nvarchar(25) NOT NULL,
	[free_dim_2] nvarchar(25) NOT NULL DEFAULT '',
    [org_budget_prev_year] DECIMAL(18, 2) NOT NULL,
	[org_budget_curr_year] DECIMAL(18, 2) NOT NULL,
    [revised_budget_prev_year] DECIMAL(18, 2) NOT NULL,
    [revised_budget_this_year] DECIMAL(18, 2) NOT NULL,
    [accounting_amount] DECIMAL(18, 2) NOT NULL,
    [forecast_amount] DECIMAL(18, 2) NOT NULL,
	[updated] DATETIME NOT NULL,
	[updated_by] INT NOT NULL, 
    [line_group_id] INT NOT NULL
)

GO

CREATE INDEX [IX_tfp_finplan_warehouse_1] ON [dbo].[tfp_finplan_warehouse] ([action_type], [line_order], [budget_year], [fk_tenant_id])
GO
