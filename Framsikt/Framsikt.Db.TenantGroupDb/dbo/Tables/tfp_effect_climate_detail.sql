CREATE TABLE [dbo].[tfp_effect_climate_detail]
(
	[pk_id] INT NOT NULL identity(1,1),
	[fk_climate_id] INT NOT NULL, 
	[fk_tenant_id] INT NOT NULL, 
	fk_indicator_code UNIQUEIDENTIFIER NOT NULL,
	[indicator_strategy] NVARCHAR(MAX) NULL,
	[main_climate_value]    NVARCHAR (100)  NOT NULL,
	[kostra_year1]      NVARCHAR (100)  NOT NULL,
	[kostra_year2]      NVARCHAR (100)  NOT NULL,
	[kostra_year3]      NVARCHAR (100)  NOT NULL,
	[kostra_year4]      NVARCHAR (100)  NOT NULL,
	[expected_value]  NVARCHAR (100)  NOT NULL,
	[year_1_value]         NVARCHAR (100)  NOT NULL,
    [year_2_value]         NVARCHAR (100)  NOT NULL,
    [year_3_value]         NVARCHAR (100)  NOT NULL,
    [year_4_value]         NVARCHAR (100)  NOT NULL,
	[acceptable_from] DECIMAL(18, 3) NOT NULL,
	[acceptable_to] DECIMAL(18, 3) NOT NULL,
	[wished_value] NVARCHAR(1) NOT NULL,
	[indicator_source] NVARCHAR(50) NOT NULL,
	updated  DATETIME NOT  NULL,
    udpated_by INT NOT  NULL, 
    [kostra_avg] NVARCHAR(100) NOT NULL, 
    [country_avg] NVARCHAR(100) NOT NULL,
	[asss_avg] NVARCHAR(100) NULL,
	[is_reported] BIT NOT NULL,
	[sort_order] INT NOT NULL  DEFAULT 0, 
	[is_delegated_indicator] BIT NOT NULL DEFAULT 0,
	[indicator_type] INT NOT NULL DEFAULT 0, 
    [is_bplan_indicator] BIT NOT NULL DEFAULT 0,
	[indicator_desc_id] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
	[budget_year] [int] NOT NULL,
	[org_id] [nvarchar](20) NOT NULL,
	[org_level] [int] NOT NULL,
	[service_id] [nvarchar](20) NOT NULL,
	[service_level] [int] NOT NULL,
	[attribute_id] [varchar](50) NOT NULL DEFAULT ''
)