CREATE TABLE [dbo].[tpl_plan_template_details]
(
	[pk_id] INT NOT NULL IDENTITY(1,1) PRIMARY KEY,
	[fk_plan_id] uniqueidentifier NOT NULL,
	[node_id] uniqueidentifier NOT NULL,
	[node_text] nvarchar(255) NOT NULL,
	[node_type] nvarchar(100) NOT NULL,
	[node_name] nvarchar(500) NOT NULL,
	[node_order] int NOT NULL DEFAULT(0),
	[parent_node_id] uniqueidentifier NOT NULL,
	[created_by] int NOT NULL,
	[created_date] datetime NOT NULL,
	[updated_by] int NOT NULL DEFAULT (0),
	[updated] datetime NOT NULL,
	[fk_tenant_id] int NOT NULL,
	[status] bit Default(0) NULL, -- soft delete feature
	[node_planned_description] nvarchar(max) NULL,
	[node_description] nvarchar(max) NULL,
	[usage_type] int NOT NULL,
	FOREIGN KEY ([fk_plan_id]) REFERENCES tpl_plan([pk_plan_id])
)