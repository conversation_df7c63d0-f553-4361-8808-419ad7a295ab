CREATE TABLE [dbo].[tmd_org_kostrafunc_mapping](
	[pk_id] [int] IDENTITY(1,1) NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[fk_kostra_function_code] NVARCHAR(12) NOT NULL,
	[fk_org_id] NVARCHAR(25) NOT NULL,
	[active] [smallint] NOT NULL,
	[date_from] [date] NOT NULL,
	[date_to] [date] NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] NVARCHAR(50) NOT NULL, 
    CONSTRAINT [PK_tmd_org_kostrafunc_mapping] PRIMARY KEY ([pk_id], [fk_tenant_id])
)
GO
/****** Object:  Index [ind_tmd_org_kostrafunc_mapping_1]    Script Date: 10/16/2014 11:10:20 AM ******/
CREATE NONCLUSTERED INDEX [ind_tmd_org_kostrafunc_mapping_1] ON [dbo].[tmd_org_kostrafunc_mapping]
(
	[fk_org_id] ASC,
	[fk_tenant_id] ASC,
	[active] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF)
GO
/****** Object:  Index [ind_tmd_org_kostrafunc_mapping_2]    Script Date: 10/16/2014 11:10:23 AM ******/
CREATE NONCLUSTERED INDEX [ind_tmd_org_kostrafunc_mapping_2] ON [dbo].[tmd_org_kostrafunc_mapping]
(
	[fk_kostra_function_code] ASC,
	[fk_tenant_id] ASC,
	[active] ASC
)WITH ( STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF)
GO
/****** Object:  Index [IX_tmd_org_kostrafunc_mapping_fk_kostra_function_code]    Script Date: 10/16/2014 11:10:23 AM ******/
CREATE NONCLUSTERED INDEX [IX_tmd_org_kostrafunc_mapping_fk_kostra_function_code] ON [dbo].[tmd_org_kostrafunc_mapping]
(
	[fk_kostra_function_code] ASC
)WITH ( STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF)
GO
/****** Object:  Index [IX_tmd_org_kostrafunc_mapping_fk_org_id]    Script Date: 10/16/2014 11:10:23 AM ******/
CREATE NONCLUSTERED INDEX [IX_tmd_org_kostrafunc_mapping_fk_org_id] ON [dbo].[tmd_org_kostrafunc_mapping]
(
	[fk_org_id] ASC
)WITH ( STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF)
GO
/****** Object:  Index [IX_tmd_org_kostrafunc_mapping_fk_tenant_id]    Script Date: 10/16/2014 11:10:23 AM ******/
CREATE NONCLUSTERED INDEX [IX_tmd_org_kostrafunc_mapping_fk_tenant_id] ON [dbo].[tmd_org_kostrafunc_mapping]
(
	[fk_tenant_id] ASC
)WITH ( STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF)