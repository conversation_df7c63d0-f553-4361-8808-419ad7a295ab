CREATE TABLE [dbo].[tmr_accounting_warehouse]
(
	[pk_id] BIGINT NOT NULL PRIMARY KEY IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [gl_year] INT NOT NULL, 
    [type] INT NOT NULL, 
    [monthrep_l1_value] NVARCHAR(25) NOT NULL, 
    [monthrep_l2_value] NVARCHAR(25) NOT NULL, 
    [service_id] NVARCHAR(25) NOT NULL, 
    [service_name] NVARCHAR(255) NOT NULL, 
    [acc_group_code] NVARCHAR(25) NOT NULL, 
    [acc_group_name] NVARCHAR(255) NOT NULL, 
    [amount] DECIMAL(18, 2) NOT NULL
)

GO

CREATE INDEX [IX_tmr_accounting_warehouse_1] ON [dbo].[tmr_accounting_warehouse] ([monthrep_l1_value], [fk_tenant_id])
go
CREATE INDEX [IX_tmr_accounting_warehouse_2] ON [dbo].[tmr_accounting_warehouse] ([monthrep_l1_value],[monthrep_l2_value], [fk_tenant_id])
go
CREATE INDEX [IX_tmr_accounting_warehouse_3] ON [dbo].[tmr_accounting_warehouse] ([monthrep_l1_value], [gl_year],[fk_tenant_id])
go





