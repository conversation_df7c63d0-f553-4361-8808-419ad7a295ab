CREATE TABLE [dbo].[twh_forecast_report]
(
    [fk_tenant_id] INT NOT NULL, 
    [forecast_period] INT NOT NULL, 
	[budget_year] INT NOT NULL,
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [account_name] NVARCHAR(125) NULL, 
    [department_code] NVARCHAR(25) NOT NULL, 
    [department_name] NVARCHAR(125) NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [function_name] NVARCHAR(125) NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
    [project_name] NVARCHAR(125) NULL, 
    [free_dim_1] NVARCHAR(25) NOT NULL, 
    [free_dim_2] NVARCHAR(25) NOT NULL, 
    [free_dim_3] NVARCHAR(25) NOT NULL, 
    [free_dim_4] NVARCHAR(25) NOT NULL, 
    [free_dim_1_name] NVARCHAR(125) NULL, 
    [free_dim_2_name] NVARCHAR(125) NULL, 
    [free_dim_3_name] NVARCHAR(125) NULL, 
    [free_dim_4_name] NVARCHAR(125) NULL, 
    [resource_id] NVARCHAR(25) NULL, 
    [resource_name] NVARCHAR(512) NULL, 
    [description] NVARCHAR(255) NOT NULL, 
    [period] INT NOT NULL, 
    [periodic_key] NVARCHAR(50) NULL, 
    [allocation_pct] DECIMAL(10, 7) NOT NULL, 
    [org_id_1] NVARCHAR(25) NULL, 
    [org_name_1] NVARCHAR(125) NULL, 
    [org_id_2] NVARCHAR(25) NULL, 
    [org_name_2] NVARCHAR(125) NULL, 
    [org_id_3] NVARCHAR(25) NULL, 
    [org_name_3] NVARCHAR(125) NULL, 
    [org_id_4] NVARCHAR(25) NULL, 
    [org_name_4] NVARCHAR(125) NULL, 
    [org_id_5] NVARCHAR(25) NULL, 
    [org_name_5] NVARCHAR(125) NULL, 
    [service_id_1] NVARCHAR(25) NULL, 
    [service_name_1] NVARCHAR(100) NULL, 
    [service_id_2] NVARCHAR(25) NULL, 
    [service_name_2] NVARCHAR(100) NULL, 
    [service_id_3] NVARCHAR(25) NULL, 
    [service_name_3] NVARCHAR(100) NULL, 
    [service_id_4] NVARCHAR(25) NULL, 
    [service_name_4] NVARCHAR(100) NULL, 
    [service_id_5] NVARCHAR(25) NULL, 
    [service_name_5] NVARCHAR(100) NULL,
    [fk_position_id] NVARCHAR(25) NULL, 
    [position_name] NVARCHAR(150) NULL,
    [action_name] [nvarchar](255) NOT NULL,
    [level_1_id] [nvarchar](25) NOT NULL,
    [level_1_description] [nvarchar](100) NOT NULL,
    [level_2_id] [nvarchar](25) NOT NULL,
    [level_2_description] [nvarchar](100) NOT NULL,
    [level_3_id] [nvarchar](25) NOT NULL,
    [level_3_description] [nvarchar](100) NOT NULL,
    [level_4_id] [nvarchar](25) NOT NULL,
    [level_4_description] [nvarchar](100) NOT NULL,
    [forecast_amount] DECIMAL(18, 2) NOT NULL, 
    [new_forecast] DECIMAL(18, 2) NOT NULL, 
    [risk_amount] DECIMAL(18, 2) NOT NULL, 
    [bud_amt_year] DECIMAL(18, 2) NOT NULL, 
    [bud_amt_ytd] DECIMAL(18, 2) NOT NULL, 
    [bud_amt_period] DECIMAL(18, 2) NOT NULL, 
    [actual_amt_year] DECIMAL(18, 2) NOT NULL, 
    [actual_amt_ytd] DECIMAL(18, 2) NOT NULL, 
    [actual_amt_period] DECIMAL(18, 2) NOT NULL, 
    [tech_amt_budget] DECIMAL(18, 2) NOT NULL, 
    [tech_amt_running] DECIMAL(18, 2) NOT NULL, 
    [org_bud_amt_year] DECIMAL (18, 2) NOT NULL, 
    [actual_amt_last_year] DECIMAL(18, 2) NOT NULL, 
    [actual_amt_last_ytd] DECIMAL(18, 2) NOT NULL, 
    [budget_change_amount] DECIMAL(18, 2) NOT NULL, 
    [risk_org_level] NVARCHAR(50) NOT NULL, 
    [report_source] VARCHAR NOT NULL, 
    [deviation_amount] DECIMAL(18, 2) NOT NULL DEFAULT 0, 
    [forecast_amt_incl_actions] DECIMAL(18, 2) NOT NULL DEFAULT 0, 
    [dev_amt_incl_actions] DECIMAL(18, 2) NOT NULL DEFAULT 0
)

GO

CREATE INDEX [IX_twh_forecast_report_1] ON [dbo].[twh_forecast_report] (fk_tenant_id, forecast_period)
go


CREATE  CLUSTERED INDEX [IX_twh_forecast_report_2] ON [dbo].[twh_forecast_report] ([fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4])
go




CREATE INDEX [IX_twh_forecast_report_3] ON [dbo].[twh_forecast_report] ([fk_account_code], [department_code], [fk_function_code], [free_dim_1], [fk_project_code], [forecast_period], [fk_tenant_id], [report_source])
