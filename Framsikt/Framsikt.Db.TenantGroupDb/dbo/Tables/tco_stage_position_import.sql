CREATE TABLE [dbo].[tco_stage_position_import](
	[pk_id] [bigint] IDENTITY(1,1) NOT NULL primary key,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[user_id] [int] NOT NULL,
	[fk_employee_id] [nvarchar](10) NULL,
	[fk_position_id] [nvarchar](50) NULL,
	[gender] [nvarchar](25) NULL,
	[birth_date] [nvarchar](50) NULL,
	[fk_account_code] [nvarchar](25) NULL,
	[fk_department_code] [nvarchar](25) NULL,
	[fk_function_code] [nvarchar](25) NULL,
	[fk_project_code] [nvarchar](25) NULL,
	[free_dim_1] nvarchar(25) NULL,
	[free_dim_2] nvarchar(25) NULL,
	[free_dim_3] nvarchar(25) NULL,
	[free_dim_4] nvarchar(25) NULL,
	[public_position_type_value] nvarchar(50) NULL,
	[public_position_code_name] nvarchar(100) NULL,
	[position_percentage] nvarchar(50) NULL,
	[presence_percentage] nvarchar(50) NULL,
	[position_start_date] nvarchar(50) NULL,
	[position_end_date] nvarchar(50) NULL,
	[position_type_name] [nvarchar](50) NULL,
	[position_type_value] [nvarchar](2) NULL,
	[position_code_name] [nvarchar](100) NULL,
	[position_code] [nvarchar](50) NULL,
	[from_date] [nvarchar](50) NULL,
	[to_date] [nvarchar](50) NULL,
	[yearly_salary] [nvarchar](25) NULL,
	[seniority_date] [nvarchar](50) NULL,
	[name] [nvarchar](100) NOT NULL,
	[salary_step] NVARCHAR(25) NULL,
	[fk_salary_type] NVARCHAR(25) NULL,
	[fk_salary_type_desc] NVARCHAR(300) NULL,
	[fk_pension_type] NVARCHAR(25) NULL,
	[external_reference] NVARCHAR(255) NULL,
	[fk_tax_rate] NVARCHAR(25) NULL,
	[monthly_salary] NVARCHAR(25) NULL,
	[leave_from_date] NVARCHAR(25) NULL,
	[leave_percentage] NVARCHAR(25) NULL,
	[leave_to_date] NVARCHAR(25) NULL,
	[leave_type] NVARCHAR(100) NULL,
	[fk_employee_id_error] [bit] NULL,
	[fk_position_id_error] [bit] NULL,
	[gender_error] [bit] NULL,
	[birth_date_error] [bit] NULL,
	[fk_account_code_error] [bit] NULL,
	[fk_department_code_error] [bit] NULL,
	[fk_function_code_error] [bit] NULL,
	[fk_project_code_error] [bit] NULL,
	[free_dim_1_error] [bit] NULL,
	[free_dim_2_error] [bit] NULL,
	[free_dim_3_error] [bit] NULL,
	[free_dim_4_error] [bit] NULL,
	[public_position_code_name_error] [bit] NULL,
	[position_percentage_error] [bit] NULL,
	[presence_percentage_error] [bit] NULL,
	[position_start_date_error] [bit] NULL,
	[position_end_date_error] [bit] NULL,
	[position_type_name_error] [bit] NULL,
	[position_type_value_error] [bit] NULL,
	[position_code_name_error] [bit] NULL,
	[position_code_error] [bit] NULL,
	[from_date_error] [bit] NULL,
	[to_date_error] [bit] NULL,
	[yearly_salary_error] [bit] NULL,
	[seniority_date_error] [bit] NULL,
	[name_error] [bit] NULL,
	[public_position_type_value_error] [bit] NULL,
	[error_count] [int] NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL,
	[job_id] [bigint] not null default (0),
	[salary_step_error] [bit] NULL,
	[fk_salary_type_error] [bit] NULL,
	[fk_salary_type_desc_error] [bit] NULL,
	[fk_pension_type_error] [bit] NULL,
	[external_reference_error] [bit] NULL,
	[fk_tax_rate_error] [bit] NULL,
	[monthly_salary_error] [bit] NULL,
	[leave_from_date_error] [bit] NULL,
	[leave_percentage_error] [bit] NULL,
	[leave_to_date_error] [bit] NULL,
	[leave_type_error] [bit] NULL)