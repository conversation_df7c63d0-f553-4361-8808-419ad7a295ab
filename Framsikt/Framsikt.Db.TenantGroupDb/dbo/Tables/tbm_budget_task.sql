/* table for storing basic information about a budget task
status 0- inactive, 1 - active, 2-published, 3-completed

*/

CREATE TABLE [dbo].[tbm_budget_task]
(
	[pk_bud_task_id] INT NOT NULL IDENTITY , 
	fk_tenant_id INT NOT NULL, 
    [task_name] NVARCHAR(150) NOT NULL, 
	budget_year int not null,
    [issued] DATE NOT NULL, 
    [deadline] DATE NOT NULL, 
    [fk_aztable_description] UNIQUEIDENTIFIER NOT NULL, 
    [serv_area_budg_pct] DECIMAL(18, 2) NOT NULL, 
    [upd_inv_flag] INT NOT NULL, 
    [bal_budg_flag] INT NOT NULL, 
    [sa_desc_flag] INT NOT NULL, 
    [upd_challenge_flag] INT NOT NULL, 
    [upd_kostra_flag] INT NOT NULL, 
    [upd_activity_flag] INT NOT NULL, 
    [def_blist_flag] INT NOT NULL, 
    [su_detail_budget_flag]  INT NOT NULL,
    [su_staff_planing_flag]  INT NOT NULL,
    [su_periodic_flag]       INT NOT NULL,
    [status] INT NOT NULL, 
	[approver_id] INT not null,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tbm_budget_task] PRIMARY KEY ([pk_bud_task_id]) 
)

GO

CREATE INDEX [IND_tbm_budget_task_1] ON [dbo].[tbm_budget_task] (status, fk_tenant_id)
