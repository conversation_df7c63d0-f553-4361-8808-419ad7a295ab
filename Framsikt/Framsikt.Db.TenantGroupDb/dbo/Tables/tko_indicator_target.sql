/* This table is being used in the kostra analysis screen to set the target red line for the four KPI indicators in the overview screen. 

If no data is available for the indicator in this table the logic will be that the application tries to find it in the gko_indicator_target table

*/

CREATE TABLE [dbo].[tko_indicator_target]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_indicator_code] NVARCHAR(25) NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [target_value] DECIMAL(18, 2) NOT NULL, 
    CONSTRAINT [PK_tko_indicator_target] PRIMARY KEY ([pk_id]), 
    CONSTRAINT [FK_tko_indicator_target_gco_tenants] FOREIGN KEY ([fk_tenant_id]) REFERENCES [gco_tenants]([pk_id]), 
    CONSTRAINT [FK_tko_indicator_target_gmd_kostra_lables] FOREIGN KEY ([fk_indicator_code]) REFERENCES [gmd_kostra_lables]([pk_indicator_code]) 
)

GO

CREATE UNIQUE INDEX [ind_tko_indicator_target_1] ON [dbo].[tko_indicator_target] (fk_indicator_code, fk_tenant_id)
