/* This table is used in the analysis section for population statistics and is connecting
the report area to the different age intervals. 

*/

CREATE TABLE [dbo].[gko_population_indicator] (
    [pk_pop_indicator_id]    NVARCHAR (25) NOT NULL,
    [pop_indicator_name]     NVARCHAR (50) NOT NULL,
    [fk_reporting_area_code] NVARCHAR (25) NOT NULL,
    [active]                 SMALLINT     NOT NULL,
	tenant_type INT NOT NULL,
    [updated]                DATETIME     NOT NULL,
    [updated_by]             INT          NOT NULL,
    CONSTRAINT [PK_gko_population_indicator] PRIMARY KEY CLUSTERED ([pk_pop_indicator_id] ASC), 
    CONSTRAINT [FK_gko_population_indicator_gmd_reporting_areas] FOREIGN KEY ([fk_reporting_area_code]) REFERENCES [gmd_reporting_areas]([pk_report_area_code])
);

