CREATE TABLE [dbo].[tmr_calendar]
(
	[pk_id] INT NOT NULL IDENTITY, 
    [fk_tenant_id] INT NOT NULL, 
    [forecast_period] INT NOT NULL, 
    [org_level] INT NOT NULL, 
    [deadline] DATETIME NOT NULL, 
    [finstatus_flag] BIT NOT NULL, 
    [goal_flag] BIT NOT NULL, 
    [strategy_flag] BIT NOT NULL DEFAULT(0), 
    [plan_flag] BIT NOT NULL DEFAULT(0), 
    [finplanaction_flag] BIT NOT NULL, 
    [investment_flag] BIT NOT NULL, 
    [sickleave_flag] BIT NOT NULL, 
	[proposed_bud_adj_flag] BIT NOT NULL,
	[sunitbusiplan_flag] BIT NOT NULL,
	[sunitbusiplanTask_flag] BIT NOT NULL DEFAULT(0),
	[textdesc_flag] BIT NOT NULL,
	[activity_Flag] BIT NOT NULL,
	[checklist_flag] BIT DEFAULT(0) NOT NULL,
	[kostra_flag] BIT DEFAULT(0) NOT NULL,
	[planstrategy_flag] BIT DEFAULT(0) NOT NULL,
	[focusarea_flag] BIT NOT NULL  DEFAULT(0),
    [covidreport_flag] BIT NOT NULL  DEFAULT(0),
    [planprocess_flag] BIT NOT NULL  DEFAULT(0),
    [manyears_flag] BIT NOT NULL DEFAULT(0),
    [equality_flag] BIT NOT NULL DEFAULT(0),
    [tenFactor_flag] BIT NOT NULL DEFAULT(0),
    [financial_kpi_flag] BIT NOT NULL  DEFAULT(0),
    [text_sync_flag] BIT NOT NULL  DEFAULT(0),
    [sync_tree_flag] BIT NOT NULL  DEFAULT(0),
    [climate_indicators_flag] BIT NOT NULL  DEFAULT(0),
    [finstatus_ddate] DATETIME NOT NULL,
    [plan_ddate] DATETIME NOT NULL,
    [strategy_ddate] DATETIME NOT NULL,
    [goal_ddate] DATETIME NOT NULL,
    [kpi_ddate] DATETIME NOT NULL,
    [finplanaction_ddate] DATETIME NOT NULL,
    [investment_ddate] DATETIME NOT NULL,
    [verbal_ddate] DATETIME NOT NULL,
    [sickleave_ddate] DATETIME NOT NULL,
    [employment_ddate] DATETIME NOT NULL,
    [proposed_bud_adj_ddate] DATETIME NOT NULL,
    [sunitbusiplan_ddate] DATETIME NOT NULL,
    [activity_ddate] DATETIME NOT NULL,
    [textdesc_ddate] DATETIME NOT NULL,
    [checklist_ddate] DATETIME NOT NULL,
    [kostra_ddate] DATETIME NOT NULL,
    [planstrategy_ddate] DATETIME NOT NULL,
    [sunitbusiplanTask_ddate] DATETIME NOT NULL,
    [focusarea_ddate] DATETIME NOT NULL,
    [covidreport_ddate] DATETIME NOT NULL,
    [planprocess_ddate] DATETIME NOT NULL,
    [manyears_ddate] DATETIME NULL,
    [equality_ddate] DATETIME NULL,
    [tenFactor_ddate] DATETIME NULL,
    [financial_kpi_ddate] DATETIME NOT NULL,
    [text_sync_date] DATETIME NOT NULL DEFAULT '1900-01-01',
    [sync_tree_date] DATETIME NOT NULL DEFAULT '1900-01-01',
    [climate_indicators_ddate] DATETIME NOT NULL DEFAULT '1900-01-01',
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    [operational_Main_Proj_flag] bit NOT NULL default 0,
     [operational_MP_ddate] DATETIME NOT NULL DEFAULT '1900-01-01',
    CONSTRAINT [PK_tmr_calendar] PRIMARY KEY ([pk_id]) 
)
