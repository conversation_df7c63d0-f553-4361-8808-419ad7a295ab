CREATE TABLE [dbo].[tin_import_emp_add_ons]
(
	[pk_id] BIGINT NOT NULL IDENTITY ,
	[fk_tenant_id] INT NOT NULL,
    [external_reference] nvarchar(25) NOT NULL, 
    [ext_add_code] NVARCHAR(25) NOT NULL, 
    [add_on_name] NVARCHAR(150) NOT NULL, 
	[fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
    [amount_month] DECIMAL(18, 2) NOT NULL, 
    [amount_year] DECIMAL(18, 2) NOT NULL, 
    [start_period] INT NOT NULL, 
    [end_period] INT NOT NULL, 
    [pension_flag] INT NOT NULL, 
	holiday_flag INT NOT NULL,
	import_status int not null,
	batch_id nvarchar(50) NOT NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
    CONSTRAINT [PK_tin_import_emp_add_ons] PRIMARY KEY ([pk_id])
)



GO

CREATE UNIQUE INDEX [IND_tin_import_emp_add_ons_1] ON [dbo].[tin_import_emp_add_ons] (ext_add_code, external_reference, fk_tenant_id, batch_id)
