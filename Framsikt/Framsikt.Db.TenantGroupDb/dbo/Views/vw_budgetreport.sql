
CREATE OR ALTER VIEW [dbo].[vw_budgetreport] AS

SELECT
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.budget_year
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ISNULL(r.reference_id,'')
,resource_name = ISNULL(em.res_name, '')
,description = CONVERT(NVARCHAR(700), a.description)
,period = CONVERT (VARCHAR(6),a.period)
,fk_key_id = CONVERT(VARCHAR(10),a.fk_key_id)
,periodic_key = '' 
,org_id_1	= oh.org_id_1	
,org_name_1 = oh.org_name_1	
,org_id_2	= oh.org_id_2	
,org_name_2 = oh.org_name_2	
,org_id_3	= oh.org_id_3	
,org_name_3	= oh.org_name_3	
,org_id_4	= oh.org_id_4	
,org_name_4	= oh.org_name_4	
,org_id_5	= oh.org_id_5	
,org_name_5	= oh.org_name_5	
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = a.amount_year_1
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ISNULL(em.fk_position_id,'')
,position_name	= ISNULL(po.position_name,'')
,position_pct	= CONVERT(VARCHAR(10),ISNULL(em.position_pct,0))
,fk_adjustment_code = CASE WHEN ud.prefix_adjcode IS NULL THEN ISNULL(a.fk_adjustment_code,'') WHEN ud.prefix_adjcode = '' THEN ISNULL(a.fk_adjustment_code,'') ELSE ud.prefix_adjcode END
,adjustment_status	= CASE WHEN ud.status = 0 THEN 'Åpen' ELSE 'Lukket' END
,adjustment_description = CONVERT(NVARCHAR(500),ud.description)
,adjustment_prefix = LEFT(CASE WHEN ud.prefix_adjcode IS NULL THEN ISNULL(a.fk_adjustment_code,'') WHEN ud.prefix_adjcode = '' THEN ISNULL(a.fk_adjustment_code,'') ELSE ud.prefix_adjcode END,2)
,adjustment_code_name = ISNULL(tac.description,'')
,fk_alter_code = a.fk_alter_code
,alter_description = fac.alter_description
,employment_comments = ISNULL(em.comments,'')
,type = 'rev_bud'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,a.updated)
,updated_by = ISNULL( u.first_name + ' ' + u.last_name,'')
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ISNULL(ud.fk_bud_auth_code, '')
,case_ref = ISNULL(ud.case_ref, '')
,long_description = ISNULL(ud.long_description, '')
,fk_attribute_id = ISNULL(ud.fk_attribute_id, '')
FROM [tbu_trans_detail] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.budget_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.budget_year = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.budget_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.budget_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tbu_employments] em ON a.fk_tenant_id = em.fk_tenant_id AND a.fk_employment_id = em.pk_employment_id and a.budget_year = em.budget_year 
LEFT JOIN [tco_resources] r ON a.fk_tenant_id = r.fk_tenant_id AND em.fk_res_id = r.pk_res_id 
LEFT JOIN [tco_positions] po ON a.fk_tenant_id = po.fk_tenant_id AND em.fk_position_id = po.position_id 
LEFT JOIN [tco_user_adjustment_codes] ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id 
LEFT JOIN [tco_fp_alter_codes] fac ON a.fk_tenant_id = fac.fk_tenant_id and a.fk_alter_code = fac.pk_alter_code
LEFT JOIN [tco_adjustment_codes] tac ON ud.fk_tenant_id = tac.fk_tenant_id and ud.prefix_adjCode = tac.pk_adjustment_code
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code
LEFT JOIN [flat_users] u ON a.updated_by = u.fk_user_id

UNION ALL

/****************   FETCH FOR original_budget   ****************/
SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.budget_year
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ISNULL(r.reference_id,'')
,resource_name = ISNULL(em.res_name, '')
,description = ''
,period = CONVERT (VARCHAR(6),a.period)
,fk_key_id = CONVERT(VARCHAR(10),0)
,periodic_key = ''
,org_id_1	= oh.org_id_1
,org_name_1 = oh.org_name_1
,org_id_2	= oh.org_id_2
,org_name_2 = oh.org_name_2
,org_id_3	= oh.org_id_3
,org_name_3	= oh.org_name_3
,org_id_4	= oh.org_id_4
,org_name_4	= oh.org_name_4
,org_id_5	= oh.org_id_5
,org_name_5	= oh.org_name_5
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = a.amount_year_1
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ISNULL(em.fk_position_id,'')
,position_name	= ISNULL(po.position_name,'')
,position_pct	= CONVERT(VARCHAR(10),ISNULL(em.position_pct,0))
,fk_adjustment_code = CASE WHEN ud.prefix_adjcode IS NULL THEN ISNULL(a.fk_adjustment_code,'') WHEN ud.prefix_adjcode = '' THEN ISNULL(a.fk_adjustment_code,'') ELSE ud.prefix_adjcode END
,adjustment_status	= CASE WHEN ud.status = 0 THEN 'Åpen' ELSE 'Lukket' END
,adjustment_description = CONVERT(NVARCHAR(500),ud.description)
,adjustment_prefix = LEFT(CASE WHEN ud.prefix_adjcode IS NULL THEN ISNULL(a.fk_adjustment_code,'') WHEN ud.prefix_adjcode = '' THEN ISNULL(a.fk_adjustment_code,'') ELSE ud.prefix_adjcode END,2)
,adjustment_code_name = ISNULL(tac.description,'')
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ISNULL(em.comments,'')
,type = 'bud_original'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tbu_trans_detail_original] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.budget_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.budget_year = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.budget_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.budget_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tbu_employments] em ON a.fk_tenant_id = em.fk_tenant_id AND a.fk_employment_id = em.pk_employment_id and a.budget_year = em.budget_year 
LEFT JOIN [tco_resources] r ON a.fk_tenant_id = r.fk_tenant_id AND em.fk_res_id = r.pk_res_id 
LEFT JOIN [tco_positions] po ON a.fk_tenant_id = po.fk_tenant_id AND em.fk_position_id = po.position_id 
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code
LEFT JOIN [tco_user_adjustment_codes] ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id 
LEFT JOIN [tco_adjustment_codes] tac ON ud.fk_tenant_id = tac.fk_tenant_id and ud.prefix_adjCode = tac.pk_adjustment_code
UNION ALL

/****************   FETCH FOR finplan   ****************/

SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.budget_year
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ''
,resource_name = ''
,description = CONVERT(NVARCHAR(MAX), a.description)
,period = CONVERT (VARCHAR(6),a.budget_year*100+1)
,fk_key_id = CONVERT(VARCHAR(10), 0)
,periodic_key = ''
,org_id_1	= oh.org_id_1
,org_name_1 = oh.org_name_1
,org_id_2	= oh.org_id_2
,org_name_2 = oh.org_name_2
,org_id_3	= oh.org_id_3
,org_name_3	= oh.org_name_3
,org_id_4	= oh.org_id_4
,org_name_4	= oh.org_name_4
,org_id_5	= oh.org_id_5
,org_name_5	= oh.org_name_5
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = a.year_1_amount
,fp_year_2_amount = a.year_2_amount
,fp_year_3_amount = a.year_3_amount
,fp_year_4_amount = a.year_4_amount
,finplan_changes_year_1 = CASE WHEN bc.org_budget_flag = 1 THEN a.year_1_amount ELSE 0 END
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ''
,position_name	= ''
,position_pct	= CONVERT(VARCHAR(10),0)
,fk_adjustment_code = ''
,adjustment_status	= 'Lukket'
,adjustment_description = ''
,adjustment_prefix =''
,adjustment_code_name = ''
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ''
,type = 'finplan'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,a.updated)
,updated_by = ISNULL( u.first_name + ' ' + u.last_name,'')
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tfp_trans_detail] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.budget_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.budget_year = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.budget_year = sv.budget_year and a.function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.project_code = pj.pk_project_code AND a.budget_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tfp_budget_changes] bc ON a.fk_tenant_id = bc.fk_tenant_id AND a.fk_change_id = bc.pk_change_id 
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code
LEFT JOIN [flat_users] u ON a.updated_by = u.fk_user_id
UNION ALL

/****************   FETCH FOR rev_bud_prev_year   ****************/

SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.budget_year + 1
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ISNULL(r.reference_id,'')
,resource_name = ISNULL(em.res_name, '')
,description = CONVERT(NVARCHAR(700), a.description)
,period = CONVERT (VARCHAR(6),a.period + 100)
,fk_key_id = CONVERT(VARCHAR(10),a.fk_key_id)
,periodic_key = '' 
,org_id_1	= oh.org_id_1
,org_name_1 = oh.org_name_1
,org_id_2	= oh.org_id_2
,org_name_2 = oh.org_name_2
,org_id_3	= oh.org_id_3
,org_name_3	= oh.org_name_3
,org_id_4	= oh.org_id_4
,org_name_4	= oh.org_name_4
,org_id_5	= oh.org_id_5
,org_name_5	= oh.org_name_5
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = a.amount_year_1
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ISNULL(em.fk_position_id,'')
,position_name	= ISNULL(po.position_name,'')
,position_pct	= CONVERT(VARCHAR(10),ISNULL(em.position_pct,0))
,fk_adjustment_code = CASE WHEN ud.prefix_adjcode IS NULL THEN ISNULL(a.fk_adjustment_code,'') WHEN ud.prefix_adjcode = '' THEN ISNULL(a.fk_adjustment_code,'') ELSE ud.prefix_adjcode END
,adjustment_status	= CASE WHEN ud.status = 0 THEN 'Åpen' ELSE 'Lukket' END
,adjustment_description = CONVERT(NVARCHAR(500),ud.description)
,adjustment_prefix = LEFT(CASE WHEN ud.prefix_adjcode IS NULL THEN ISNULL(a.fk_adjustment_code,'') WHEN ud.prefix_adjcode = '' THEN ISNULL(a.fk_adjustment_code,'') ELSE ud.prefix_adjcode END,2)
,adjustment_code_name = ISNULL(tac.description,'')
,fk_alter_code = a.fk_alter_code
,alter_description = fac.alter_description
,employment_comments = ISNULL(em.comments,'')
,type = 'rev_bud_prev_year'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ISNULL(ud.fk_bud_auth_code, '')
,case_ref = ISNULL(ud.case_ref, '')
,long_description = ISNULL(ud.long_description, '')
,fk_attribute_id = ISNULL(ud.fk_attribute_id, '')
FROM [tbu_trans_detail] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.budget_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.budget_year+1 = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.budget_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.budget_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tbu_employments] em ON a.fk_tenant_id = em.fk_tenant_id AND a.fk_employment_id = em.pk_employment_id and a.budget_year = em.budget_year 
LEFT JOIN [tco_resources] r ON a.fk_tenant_id = r.fk_tenant_id AND em.fk_res_id = r.pk_res_id 
LEFT JOIN [tco_positions] po ON a.fk_tenant_id = po.fk_tenant_id AND em.fk_position_id = po.position_id 
LEFT JOIN [tco_user_adjustment_codes] ud ON a.fk_adjustment_code = ud.pk_adj_code AND a.fk_tenant_id = ud.fk_tenant_id 
LEFT JOIN [tco_fp_alter_codes] fac ON a.fk_tenant_id = fac.fk_tenant_id and a.fk_alter_code = fac.pk_alter_code
LEFT JOIN [tco_adjustment_codes] tac ON ud.fk_tenant_id = tac.fk_tenant_id and ud.prefix_adjCode = tac.pk_adjustment_code
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code
UNION ALL
/****************   FETCH FOR cost_budget   ****************/

SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.budget_year
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ''
,resource_name = ''
,description = ''
,period = CONVERT (VARCHAR(6),a.budget_year*100+1)
,fk_key_id = CONVERT(VARCHAR(10),0)
,periodic_key = ''
,org_id_1	= oh.org_id_1	
,org_name_1 = oh.org_name_1	
,org_id_2	= oh.org_id_2	
,org_name_2 = oh.org_name_2	
,org_id_3	= oh.org_id_3	
,org_name_3	= oh.org_name_3	
,org_id_4	= oh.org_id_4	
,org_name_4	= oh.org_name_4	
,org_id_5	= oh.org_id_5	
,org_name_5	= oh.org_name_5	
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = a.amount_year_1
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ''
,position_name	= ''
,position_pct	= CONVERT(VARCHAR(10),0)
,fk_adjustment_code = ''
,adjustment_status	= 'Lukket'
,adjustment_description = ''
,adjustment_prefix = ''
,adjustment_code_name = ''
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ''
,type = 'bud_cost'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tbu_trans_detail_cost] a 
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.budget_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.budget_year = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.budget_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.budget_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1'
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2'
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3'
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4'
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code
UNION ALL

/****************   FETCH FOR accounting_prev_year   ****************/

SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.gl_year+1
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ''
,resource_name = ''
,description = ''
,period = CONVERT (VARCHAR(6),a.period + 100)
,fk_key_id = CONVERT(VARCHAR(10),0)
,periodic_key = '' 
,org_id_1	= oh.org_id_1	
,org_name_1 = oh.org_name_1	
,org_id_2	= oh.org_id_2	
,org_name_2 = oh.org_name_2	
,org_id_3	= oh.org_id_3	
,org_name_3	= oh.org_name_3	
,org_id_4	= oh.org_id_4	
,org_name_4	= oh.org_name_4	
,org_id_5	= oh.org_id_5	
,org_name_5	= oh.org_name_5	
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = a.amount
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ''
,position_name	= ''
,position_pct	= CONVERT(VARCHAR(10),0)
,fk_adjustment_code = ''
,adjustment_status	= 'Lukket'
,adjustment_description = ''
,adjustment_prefix = ''
,adjustment_code_name = ''
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ''
,type = 'acc_prev_year'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tfp_accounting_data] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.gl_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.gl_year + 1  = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.gl_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.gl_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.gl_year = fav.budget_year AND a.department_code = fav.fk_department_code
UNION ALL
/****************   FETCH FOR accounting_current_year   ****************/

SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.gl_year
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ''
,resource_name = ''
,description = ''
,period = CONVERT (VARCHAR(6),a.period)
,fk_key_id = CONVERT(VARCHAR(10),0)
,periodic_key = '' 
,org_id_1	= oh.org_id_1
,org_name_1 = oh.org_name_1
,org_id_2	= oh.org_id_2
,org_name_2 = oh.org_name_2
,org_id_3	= oh.org_id_3
,org_name_3	= oh.org_name_3
,org_id_4	= oh.org_id_4
,org_name_4	= oh.org_name_4
,org_id_5	= oh.org_id_5
,org_name_5	= oh.org_name_5
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = a.amount
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ''
,position_name	= ''
,position_pct	= CONVERT(VARCHAR(10),0)
,fk_adjustment_code = ''
,adjustment_status	= 'Lukket'
,adjustment_description = ''
,adjustment_prefix = ''
,adjustment_code_name = ''
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ''
,type = 'acc_current_year'
,accounting_year_minus_2 = 0
,original_budget_prev_year = 0
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tfp_accounting_data] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.gl_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.gl_year = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.gl_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.gl_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.gl_year = fav.budget_year AND a.department_code = fav.fk_department_code
UNION ALL

/****************   FETCH FOR accounting year minus 2   ****************/

SELECT 
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.gl_year+2
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ''
,resource_name = ''
,description = ''
,period = CONVERT (VARCHAR(6),a.period + 200)
,fk_key_id = CONVERT(VARCHAR(10),0)
,periodic_key = '' 
,org_id_1	= oh.org_id_1	
,org_name_1 = oh.org_name_1	
,org_id_2	= oh.org_id_2	
,org_name_2 = oh.org_name_2	
,org_id_3	= oh.org_id_3	
,org_name_3	= oh.org_name_3	
,org_id_4	= oh.org_id_4	
,org_name_4	= oh.org_name_4	
,org_id_5	= oh.org_id_5	
,org_name_5	= oh.org_name_5	
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ''
,position_name	= ''
,position_pct	= CONVERT(VARCHAR(10),0)
,fk_adjustment_code = ''
,adjustment_status	= 'Lukket'
,adjustment_description = ''
,adjustment_prefix = ''
,adjustment_code_name = ''
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ''
,type = 'acc_year_minus_2'
,accounting_year_minus_2 = a.amount
,original_budget_prev_year = 0
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tfp_accounting_data] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.gl_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.gl_year+2 = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.gl_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.gl_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.gl_year = fav.budget_year AND a.department_code = fav.fk_department_code
UNION ALL

/****************   FETCH FOR original_budget previous year   ****************/
SELECT
 fk_tenant_id = a.fk_tenant_id
,budget_year = a.budget_year+1
,fk_account_code = a.fk_account_code
,account_name = ISNULL(ac.account_name,'')
,department_code = a.department_code
,department_name = ISNULL(oh.department_name,'')
,function_code = a.fk_function_code
,function_name = ISNULL(sv.function_name,'')
,fk_project_code = a.fk_project_code
,project_name = ISNULL(pj.project_name, '')
,free_dim_1 = a.free_dim_1
,free_dim_1_name = ISNULL(f1.description, '')
,free_dim_2 = a.free_dim_2
,free_dim_2_name = ISNULL(f2.description, '')
,free_dim_3 = a.free_dim_3
,free_dim_3_name = ISNULL(f3.description, '')
,free_dim_4 = a.free_dim_4
,free_dim_4_name = ISNULL(f4.description, '')
,resource_id = ISNULL(r.reference_id,'')
,resource_name = ISNULL(em.res_name, '')
,description = ''
,period = CONVERT (VARCHAR(6),a.period+100)
,fk_key_id = CONVERT(VARCHAR(10),0)
,periodic_key = '' 
,org_id_1	= oh.org_id_1	
,org_name_1 = oh.org_name_1	
,org_id_2	= oh.org_id_2	
,org_name_2 = oh.org_name_2	
,org_id_3	= oh.org_id_3	
,org_name_3	= oh.org_name_3	
,org_id_4	= oh.org_id_4	
,org_name_4	= oh.org_name_4	
,org_id_5	= oh.org_id_5	
,org_name_5	= oh.org_name_5	
,service_id_1= ISNULL(sv.service_id_1,'')
,service_name_1 = ISNULL(sv.service_name_1,'')
,service_id_2 = ISNULL(sv.service_id_2,'')
,service_name_2 = ISNULL(sv.service_name_2,'')
,service_id_3 = ISNULL(sv.service_id_3,'')
,service_name_3 = ISNULL(sv.service_name_3,'')
,service_id_4 = ISNULL(sv.service_id_4,'')
,service_name_4 = ISNULL(sv.service_name_4,'')
,service_id_5 = ISNULL(sv.service_id_5,'')
,service_name_5 = ISNULL(sv.service_name_5,'')
,attribute_value = ISNULL (fav.attribute_id, '')
,attribute_name = ISNULL (fav.attribute_name, '')
,org_desc_3 = oh.org_id_3 + ' - ' + oh.org_name_3
,service_desc_2 = ISNULL(sv.service_id_2 + ' - ' + sv.service_name_2,'')
,original_budget = 0
,revised_budget = 0
,cost_calc_budget = 0
,fp_year_1_amount = 0
,fp_year_2_amount = 0
,fp_year_3_amount = 0
,fp_year_4_amount = 0
,finplan_changes_year_1 = 0
,revised_budget_prev_year = 0
,accounting_prev_year = 0
,accounting_current_year = 0
,acc_type_code			= ISNULL(CONVERT(NVARCHAR(25),rl.line_group_id), '')
,acc_type_description	= ISNULL(rl.line_group, '')
,acc_group_code			= ISNULL (CONVERT(NVARCHAR(25),rl.line_item_id), '')
,acc_group_description	= ISNULL (rl.line_item, '')
,fk_kostra_account_code = ac.fk_kostra_account_code
,kostra_account_description = ISNULL (ac.kostra_account_name, '')
,fk_kostra_function_code = ISNULL(sv.fk_kostra_function_code, '')
,kostra_function_description = ISNULL (sv.kostra_function_name, '')
,fk_position_id	= ISNULL(em.fk_position_id,'')
,position_name	= ISNULL(po.position_name,'')
,position_pct	= CONVERT(VARCHAR(10),ISNULL(em.position_pct,0))
,fk_adjustment_code = ''
,adjustment_status	= 'Lukket'
,adjustment_description = ''
,adjustment_prefix = ''
,adjustment_code_name = ''
,fk_alter_code = ''
,alter_description = ''
,employment_comments = ISNULL(em.comments,'')
,type = 'bud_original_prev_year'
,accounting_year_minus_2 = 0
,original_budget_prev_year = a.amount_year_1
,updated = CONVERT(date,CONVERT(datetime,0))
,updated_by = ''
,level_1_id_description = ISNULL (trl.level_1_id + ' - ' + trl.level_1_description, '')
,level_2_id_description = ISNULL (trl.level_2_id + ' - ' + trl.level_2_description, '')
,level_3_id_description = ISNULL (trl.level_3_id + ' - ' + trl.level_3_description, '')
,level_4_id_description = ISNULL (trl.level_4_id + ' - ' + trl.level_4_description, '')
,fk_bud_auth_code = ''
,case_ref = ''
,long_description = ''
,fk_attribute_id = ''
FROM [tbu_trans_detail_original] a
JOIN [flat_accounts] ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id AND a.budget_year = ac.budget_year  AND ac.account_type = 'operations'
JOIN [flat_org_hierarchy_dep] oh on a.fk_tenant_id = oh.fk_Tenant_id and a.department_code = oh.fk_department_code and a.budget_year+1 = oh.budget_year
LEFT JOIN [gmd_reporting_line] rl ON rl.fk_kostra_account_code = ac.fk_kostra_account_code AND rl.report  = 'L2'
LEFT JOIN [flat_function_service_values] sv ON a.fk_tenant_id = sv.fk_tenant_id and a.budget_year = sv.budget_year and a.fk_function_code = sv.pk_function_code
LEFT JOIN [flat_projects] pj ON a.fk_tenant_id = pj.fk_tenant_id and a.fk_project_code = pj.pk_project_code AND a.budget_year = pj.budget_year
LEFT JOIN [tco_free_dim_values] f1 ON a.free_dim_1 = f1.free_dim_code AND a.fk_tenant_id = f1.fk_tenant_id AND f1.free_dim_column = 'free_dim_1' 
LEFT JOIN [tco_free_dim_values] f2 ON a.free_dim_2 = f2.free_dim_code AND a.fk_tenant_id = f2.fk_tenant_id AND f2.free_dim_column = 'free_dim_2' 
LEFT JOIN [tco_free_dim_values] f3 ON a.free_dim_3 = f3.free_dim_code AND a.fk_tenant_id = f3.fk_tenant_id AND f3.free_dim_column = 'free_dim_3' 
LEFT JOIN [tco_free_dim_values] f4 ON a.free_dim_4 = f4.free_dim_code AND a.fk_tenant_id = f4.fk_tenant_id AND f4.free_dim_column = 'free_dim_4' 
LEFT JOIN [tbu_employments] em ON a.fk_tenant_id = em.fk_tenant_id AND a.fk_employment_id = em.pk_employment_id and a.budget_year = em.budget_year 
LEFT JOIN [tco_resources] r ON a.fk_tenant_id = r.fk_tenant_id AND em.fk_res_id = r.pk_res_id 
LEFT JOIN [tco_positions] po ON a.fk_tenant_id = po.fk_tenant_id AND em.fk_position_id = po.position_id 
LEFT JOIN [tmd_reporting_line] trl ON a.fk_tenant_id = trl.fk_tenant_id AND a.fk_account_code = trl.fk_account_code AND trl.report = 'MNDRAPP'
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code
GO
