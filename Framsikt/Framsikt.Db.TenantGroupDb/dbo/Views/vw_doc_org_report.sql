CREATE OR ALTER VIEW vw_doc_org_report  
as  
  
SELECT a.fk_tenant_id, a.budget_year, a.fp_level_1_value, a.fp_level_2_value,  
org_id = CASE  
WHEN p.param_value = 'org_id_1' THEN a.org_id_1  
WHEN p.param_value = 'org_id_2' THEN a.org_id_2  
WHEN p.param_value = 'org_id_3' THEN a.org_id_3  
WHEN p.param_value = 'org_id_4' THEN a.org_id_4  
WHEN p.param_value = 'org_id_5' THEN a.org_id_5  
ELSE org_id_2 END,  
org_name =  
CASE  
WHEN p.param_value = 'org_id_1' THEN a.org_name_1  
WHEN p.param_value = 'org_id_2' THEN a.org_name_2  
WHEN p.param_value = 'org_id_3' THEN a.org_name_3  
WHEN p.param_value = 'org_id_4' THEN a.org_name_4  
WHEN p.param_value = 'org_id_5' THEN a.org_name_5  
ELSE org_name_2  
END  
,gl_amount = sum(a.gl_amount) 
,budget_amount = 0  
,revised_budget_amount = 0  
,year_1_amount = 0  
,year_2_amount = 0  
,year_3_amount = 0  
,year_4_amount = 0  
,a.fk_change_id  
,attribute_id = ISNULL(fav.attribute_id, '')  
FROM [twh_buddoc_reports] a  
JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND a.budget_year-2 BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo)  
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'  
LEFT JOIN tfp_budget_changes ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year AND a.fk_change_id = ch.pk_change_id AND ch.org_budget_flag  = 1  
LEFT JOIN tco_parameters p ON p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'DOC_ORG_REPORT' AND p.active = 1  
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.fk_department_code = fav.fk_department_code  
WHERE ch.org_budget_flag = 1 OR a.fk_change_id = 0  
AND a.gl_amount != 0
 GROUP BY a.fk_tenant_id, a.budget_year, p.param_value, fp_level_1_value, fp_level_2_value,
    a.org_id_1, a.org_id_2, a.org_id_3, a.org_id_4, a.org_id_5, a.org_name_1, a.org_name_2, a.org_name_3, a.org_name_4, a.org_name_5,  
	a.fk_change_id, fav.attribute_id
     
UNION ALL  
  
SELECT a.fk_tenant_id, 
budget_year = a.budget_year+1,   
fp_level_1_value = CASE  
WHEN tp.param_value = 'org_id_2' THEN toh.org_id_2  
WHEN tp.param_value = 'org_id_1' THEN toh.org_id_1  
WHEN tp.param_value = 'org_id_3' THEN toh.org_id_3  
WHEN tp.param_value = 'service_id_1' THEN tsv.service_id_1  
WHEN tp.param_value = 'service_id_2' THEN tsv.service_id_2  
WHEN tp.param_value = 'service_id_3' THEN tsv.service_id_3  
ELSE org_id_2 END,   
fp_level_2_value = CASE  
WHEN tp2.param_value = 'org_id_3' THEN toh.org_id_3  
WHEN tp2.param_value = 'service_id_2' THEN tsv.service_id_2  
WHEN tp2.param_value = 'org_id_2' THEN toh.org_id_2  
WHEN tp2.param_value = 'org_id_4' THEN toh.org_id_4  
WHEN tp2.param_value = 'org_id_5' THEN toh.org_id_5  
WHEN tp2.param_value = 'service_id_1' THEN tsv.service_id_1  
WHEN tp2.param_value = 'service_id_3' THEN tsv.service_id_3  
ELSE '' END,  
org_id = CASE  
WHEN p.param_value = 'org_id_1' THEN toh.org_id_1  
WHEN p.param_value = 'org_id_2' THEN toh.org_id_2  
WHEN p.param_value = 'org_id_3' THEN toh.org_id_3  
WHEN p.param_value = 'org_id_4' THEN toh.org_id_4  
WHEN p.param_value = 'org_id_5' THEN toh.org_id_5  
ELSE org_id_2 END,  
org_name =  
CASE  
WHEN p.param_value = 'org_id_1' THEN toh.org_name_1  
WHEN p.param_value = 'org_id_2' THEN toh.org_name_2  
WHEN p.param_value = 'org_id_3' THEN toh.org_name_3  
WHEN p.param_value = 'org_id_4' THEN toh.org_name_4  
WHEN p.param_value = 'org_id_5' THEN toh.org_name_5  
ELSE org_name_2  
END  
,gl_amount = 0  
,budget_amount = sum(a.amount_year_1)
,revised_budget_amount = 0  
,year_1_amount = 0  
,year_2_amount = 0  
,year_3_amount = 0  
,year_4_amount = 0  
,fk_change_id = 0  
,attribute_id = ISNULL(fav.attribute_id, '')  
FROM tbu_trans_detail_original a  
JOIN flat_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id  AND a.budget_year = ac.budget_year  
JOIN flat_org_hierarchy_dep toh ON a.fk_tenant_id = toh.fk_tenant_id and a.department_code = toh.fk_department_code AND a.budget_year+1 = toh.budget_year  
JOIN flat_function_service_values tsv ON a.fk_function_code = tsv.pk_function_code AND a.fk_tenant_id = tsv.fk_tenant_id AND a.budget_year = tsv.budget_year  
LEFT JOIN tco_parameters p ON p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'DOC_ORG_REPORT' AND p.active = 1  
JOIN tco_parameters tp ON a.fk_tenant_id = tp.fk_tenant_id and tp.param_name = 'FINPLAN_LEVEL_1' AND tp.active = 1  
LEFT JOIN tco_parameters tp2 ON a.fk_tenant_id = tp2.fk_tenant_id and tp2.param_name = 'FINPLAN_LEVEL_2' AND tp2.active = 1  
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code  
  GROUP BY a.fk_tenant_id, a.budget_year, p.param_value, tp.param_value, tp2.param_value,p.param_value,
    toh.org_id_1, toh.org_id_2, toh.org_id_3, toh.org_id_4, toh.org_id_5, toh.org_name_1, toh.org_name_2,
	toh.org_name_3, toh.org_name_4, toh.org_name_5,  tsv.service_id_1  ,tsv.service_id_2  ,tsv.service_id_3  ,
	fav.attribute_id

UNION ALL  
  
SELECT a.fk_tenant_id, budget_year = a.budget_year+1,   
fp_level_1_value = CASE  
WHEN tp.param_value = 'org_id_2' THEN toh.org_id_2  
WHEN tp.param_value = 'org_id_1' THEN toh.org_id_1  
WHEN tp.param_value = 'org_id_3' THEN toh.org_id_3  
WHEN tp.param_value = 'service_id_1' THEN tsv.service_id_1  
WHEN tp.param_value = 'service_id_2' THEN tsv.service_id_2  
WHEN tp.param_value = 'service_id_3' THEN tsv.service_id_3  
ELSE org_id_2 END,   
fp_level_2_value = CASE  
WHEN tp2.param_value = 'org_id_3' THEN toh.org_id_3  
WHEN tp2.param_value = 'service_id_2' THEN tsv.service_id_2  
WHEN tp2.param_value = 'org_id_2' THEN toh.org_id_2  
WHEN tp2.param_value = 'org_id_4' THEN toh.org_id_4  
WHEN tp2.param_value = 'org_id_5' THEN toh.org_id_5  
WHEN tp2.param_value = 'service_id_1' THEN tsv.service_id_1  
WHEN tp2.param_value = 'service_id_3' THEN tsv.service_id_3  
ELSE '' END,  
org_id = CASE  
WHEN p.param_value = 'org_id_1' THEN toh.org_id_1  
WHEN p.param_value = 'org_id_2' THEN toh.org_id_2  
WHEN p.param_value = 'org_id_3' THEN toh.org_id_3  
WHEN p.param_value = 'org_id_4' THEN toh.org_id_4  
WHEN p.param_value = 'org_id_5' THEN toh.org_id_5  
ELSE org_id_2 END,  
org_name =  
CASE  
WHEN p.param_value = 'org_id_1' THEN toh.org_name_1  
WHEN p.param_value = 'org_id_2' THEN toh.org_name_2  
WHEN p.param_value = 'org_id_3' THEN toh.org_name_3  
WHEN p.param_value = 'org_id_4' THEN toh.org_name_4  
WHEN p.param_value = 'org_id_5' THEN toh.org_name_5  
ELSE org_name_2  
END  
,gl_amount = 0  
,budget_amount = 0  
,revised_budget_amount = sum(a.amount_year_1)
,year_1_amount = 0  
,year_2_amount = 0  
,year_3_amount = 0  
,year_4_amount = 0  
,fk_change_id = 0  
,attribute_id = ISNULL(fav.attribute_id, '')  
FROM tbu_trans_detail a  
JOIN flat_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.fk_tenant_id  AND a.budget_year = ac.budget_year  
JOIN flat_org_hierarchy_dep toh ON a.fk_tenant_id = toh.fk_tenant_id and a.department_code = toh.fk_department_code AND a.budget_year+1 = toh.budget_year  
JOIN flat_function_service_values tsv ON a.fk_function_code = tsv.pk_function_code AND a.fk_tenant_id = tsv.fk_tenant_id AND a.budget_year = tsv.budget_year  
LEFT JOIN tco_parameters p ON p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'DOC_ORG_REPORT' AND p.active = 1  
JOIN tco_parameters tp ON a.fk_tenant_id = tp.fk_tenant_id and tp.param_name = 'FINPLAN_LEVEL_1' AND tp.active = 1  
LEFT JOIN tco_parameters tp2 ON a.fk_tenant_id = tp2.fk_tenant_id and tp2.param_name = 'FINPLAN_LEVEL_2' AND tp2.active = 1  
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.department_code = fav.fk_department_code  
JOIN tco_user_adjustment_codes tuac ON a.fk_tenant_id = tuac.fk_tenant_id and a.fk_adjustment_code = tuac.pk_adj_code and a.budget_year = tuac.budget_year and tuac.status = 1  
  GROUP BY a.fk_tenant_id, a.budget_year, p.param_value, tp.param_value, tp2.param_value,p.param_value,
    toh.org_id_1, toh.org_id_2, toh.org_id_3, toh.org_id_4, toh.org_id_5, toh.org_name_1, toh.org_name_2,
	toh.org_name_3, toh.org_name_4, toh.org_name_5,  tsv.service_id_1  ,tsv.service_id_2  ,tsv.service_id_3  ,
	fav.attribute_id

UNION ALL  
  
SELECT a.fk_tenant_id, a.budget_year, a.fp_level_1_value, a.fp_level_2_value,  
org_id = CASE  
WHEN p.param_value = 'org_id_1' THEN a.org_id_1  
WHEN p.param_value = 'org_id_2' THEN a.org_id_2  
WHEN p.param_value = 'org_id_3' THEN a.org_id_3  
WHEN p.param_value = 'org_id_4' THEN a.org_id_4  
WHEN p.param_value = 'org_id_5' THEN a.org_id_5  
ELSE org_id_2 END,  
org_name =  
CASE  
WHEN p.param_value = 'org_id_1' THEN a.org_name_1  
WHEN p.param_value = 'org_id_2' THEN a.org_name_2  
WHEN p.param_value = 'org_id_3' THEN a.org_name_3  
WHEN p.param_value = 'org_id_4' THEN a.org_name_4  
WHEN p.param_value = 'org_id_5' THEN a.org_name_5  
ELSE org_name_2  
END  
,gl_amount = 0  
,budget_amount = 0  
,revised_budget_amount = 0  
,year_1_amount = sum(a.year_1_amount) 
,year_2_amount = sum(a.year_2_amount)  
,year_3_amount = sum(a.year_3_amount)  
,year_4_amount = sum(a.year_4_amount)  
,a.fk_change_id  
,attribute_id = ISNULL(fav.attribute_id, '')  
FROM [twh_buddoc_reports] a  
JOIN tco_accounts ac ON a.fk_account_code = ac.pk_account_code AND a.fk_tenant_id = ac.pk_tenant_id AND a.budget_year BETWEEN datepart(year, ac.dateFrom) AND datepart(year, ac.dateTo)  
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code AND ka.type = 'operations'  
LEFT JOIN tfp_budget_changes ch ON a.fk_tenant_id = ch.fk_tenant_id AND a.budget_year = ch.budget_year AND a.fk_change_id = ch.pk_change_id AND ch.org_budget_flag  = 1  
LEFT JOIN tco_parameters p ON p.fk_tenant_id = a.fk_tenant_id AND p.param_name = 'DOC_ORG_REPORT' AND p.active = 1  
LEFT JOIN [flat_attribute_values] fav ON a.fk_tenant_id = fav.fk_tenant_id AND a.budget_year = fav.budget_year AND a.fk_department_code = fav.fk_department_code  
WHERE ch.org_budget_flag = 1 OR a.fk_change_id = 0  
GROUP BY a.fk_tenant_id, a.budget_year, a.fp_level_1_value, a.fp_level_2_value, p.param_value, 
    a.org_id_1, a.org_id_2, a.org_id_3, a.org_id_4, a.org_id_5, a.org_name_1, a.org_name_2,
	a.org_name_3, a.org_name_4, a.org_name_5, a.fk_change_id,
	fav.attribute_id
GO

  
