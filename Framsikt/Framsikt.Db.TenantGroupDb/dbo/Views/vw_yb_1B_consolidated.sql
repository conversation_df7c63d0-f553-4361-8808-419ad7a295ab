CREATE OR ALTER VIEW [dbo].[vw_yb_1B_consolidated]
	AS 

SELECT a.fk_tenant_id
, budget_year = a.year
, sum_level = CASE when a.free_dim_code = '' OR a.free_dim_code is NULL THEN 'Kasse' ELSE 'Foretak' END
, aggregate_id = CASE when a.free_dim_code = '' OR a.free_dim_code is NULL THEN  a.aggregate_id ELSE a.free_dim_code END
, aggregate_name = CASE when a.free_dim_code = '' OR a.free_dim_code is NULL THEN  a.aggregate_name ELSE ISNULL(d.description, 'Firma mangler') END
, a.org_bud_amt_year
, a.revised_bud_amt_year
, a.org_bud_amt_last_year
, a.revised_bud_amt_last_year
, a.actual_amt_last_year
FROM [tbf_budget_form_1B] a
LEFT JOIN tco_free_dim_values d ON a.free_dim_code = d.free_dim_code AND a.fk_tenant_id = d.fk_tenant_id AND d.free_dim_column = 'free_dim_2'
WHERE a.doc_type = 3
GO