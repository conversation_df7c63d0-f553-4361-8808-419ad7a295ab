CREATE OR ALTER VIEW [dbo].[vw_tfp_sync_warehouse_budget]
	AS 


SELECT a.fk_tenant_id, a.budget_year, a.sub_tenant_id, 
org_main= SUM(a.org_main),
revised_main = SUM(a.revised_main),
revised_sub = SUM(a.revised_sub),
deviation = SUM(a.revised_main)-SUM(a.revised_sub),
budget_main = SUM(a.budget_main),
budget_sub = SUM(a.budget_sub),
deviation_budget = SUM(a.budget_main)-SUM(a.budget_sub)
FROM 
(
SELECT s.fk_tenant_id,d.budget_year,s.sub_tenant_id, 
org_main = CASE WHEN ch.org_budget_flag = 1 THEN sum(d.year_1_amount) ELSE 0 END,
revised_main = case when d.fk_adj_code = '' then sum(d.year_1_amount)
					when d.fk_adj_code = adj.pk_adj_code then sum(d.year_1_amount)
					else 0
					end,
revised_sub = convert(dec(18,2),0),
budget_main = convert(dec(18,2),0),
budget_sub = convert(dec(18,2),0)
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN tco_org_version ov ON d.fk_tenant_id = ov.fk_tenant_id AND d.budget_year*100+1 BETWEEN ov.period_from AND ov.period_to
JOIN tco_org_hierarchy oh ON d.fk_tenant_id = oh.fk_tenant_id AND d.department_code = oh.fk_department_code AND oh.fk_org_version = ov.pk_org_version
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND oh.org_id_3 = s.org_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
JOIN  tfp_budget_changes ch ON d.fk_tenant_id = ch.fk_tenant_id AND d.fk_change_id = ch.pk_change_id
LEFT JOIN tco_user_adjustment_codes adj on h.fk_tenant_id = adj.fk_tenant_id and d.fk_adj_code = adj.pk_adj_code and adj.status = 1
GROUP BY s.fk_tenant_id,d.budget_year,s.sub_tenant_id, ch.org_budget_flag, d.fk_adj_code, adj.pk_adj_code

union all

SELECT s.fk_tenant_id,d.budget_year, d.fk_tenant_id,
org_main = convert(dec(18,2),0),
revised_main = convert(dec(18,2),0),
revised_sub = case when d.fk_adj_code = '' then sum(d.year_1_amount)
					when d.fk_adj_code = adj.pk_adj_code then sum(d.year_1_amount)
					else 0
					end,
budget_main = convert(dec(18,2),0),
budget_sub = convert(dec(18,2),0)
FROM tfp_trans_header h
JOIN tfp_trans_detail d ON h.pk_action_id = d.fk_action_id AND h.fk_tenant_id = d.fk_tenant_id
JOIN  tfp_budget_changes ch ON d.fk_tenant_id = ch.fk_tenant_id AND d.fk_change_id = ch.pk_change_id
JOIN (SELECT fk_tenant_id, sub_tenant_id FROM  tco_sync_company_setup GROUP BY fk_tenant_id, sub_tenant_id) s ON s.sub_tenant_id = h.fk_tenant_id 
LEFT JOIN tco_user_adjustment_codes adj on h.fk_tenant_id = adj.fk_tenant_id and d.fk_adj_code = adj.pk_adj_code and adj.status = 1
GROUP BY s.fk_tenant_id,d.fk_tenant_id,d.budget_year,d.fk_adj_code,adj.pk_adj_code

UNION ALL

SELECT s.fk_tenant_id,d.budget_year, s.sub_tenant_id,
org_main = convert(dec(18,2),0),
revised_main = convert(dec(18,2),0),
revised_sub = convert(dec(18,2),0),
budget_main = sum(d.amount_year_1),
budget_sub = convert(dec(18,2),0)
FROM tbu_trans_detail d
JOIN tco_org_version ov ON d.fk_tenant_id = ov.fk_tenant_id AND d.budget_year*100+1 BETWEEN ov.period_from AND ov.period_to
JOIN tco_org_hierarchy oh ON d.fk_tenant_id = oh.fk_tenant_id AND d.department_code = oh.fk_department_code AND oh.fk_org_version = ov.pk_org_version
JOIN tco_sync_company_setup s ON s.org_id = oh.org_id_3 AND s.fk_tenant_id = oh.fk_tenant_id AND oh.org_id_3 = s.org_id AND s.org_level = 3 AND s.fk_org_version = oh.fk_org_version AND s.sync_flag != 0
JOIN tco_user_adjustment_codes adj ON d.fk_tenant_id = adj.fk_tenant_id AND d.fk_adjustment_code = adj.pk_adj_code and adj.status = 1
GROUP BY s.fk_tenant_id,s.sub_tenant_id,d.budget_year

UNION ALL

SELECT s.fk_tenant_id,d.budget_year, d.fk_tenant_id,
org_main = convert(dec(18,2),0),
revised_main = convert(dec(18,2),0),
revised_sub = convert(dec(18,2),0),
budget_main = convert(dec(18,2),0),
budget_sub = sum(d.amount_year_1)
FROM tbu_trans_detail d
JOIN (SELECT fk_tenant_id, sub_tenant_id FROM  tco_sync_company_setup GROUP BY fk_tenant_id, sub_tenant_id) s ON s.sub_tenant_id = d.fk_tenant_id 
JOIN tco_user_adjustment_codes adj ON d.fk_tenant_id = adj.fk_tenant_id AND d.fk_adjustment_code = adj.pk_adj_code and adj.status = 1
GROUP BY s.fk_tenant_id,d.fk_tenant_id,d.budget_year
) A

GROUP BY a.fk_tenant_id, a.budget_year, a.sub_tenant_id
GO
