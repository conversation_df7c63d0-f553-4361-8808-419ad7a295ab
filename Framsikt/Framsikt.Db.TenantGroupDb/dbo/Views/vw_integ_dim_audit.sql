CREATE OR ALTER VIEW vw_integ_dim_audit as
select fk_tenant_id, dimension_type,
    action= CASE
        WHEN action = 'INSERT' OR action like 'Case%I' OR action = 'NEW' THEN '1 - Ny verdi opprettet'
        WHEN action like 'Case%U' AND value_new IN ('1', '0') THEN '2 - Status endret'
        WHEN action = 'UPDATE' AND value_new IN ('1', '0') THEN '2 - Status endret'
        WHEN action like 'Case%U' AND ISDATE(value_new)=1 THEN '3 - Dato endret'
        WHEN action = 'UPDATE' AND value_new  NOT IN ('1', '0') THEN '4 - Navn endret'
        WHEN action = 'CHANGES' or action = 'OTHER' THEN '4 - Navn endret'
        WHEN action = 'Case2_D' THEN '5 - Kode byttet ut pga. gyldighet'
        else action
    END,
    dimension_code, value_new, value_old, Updated, BatchId
from integ_dimension_import_audit