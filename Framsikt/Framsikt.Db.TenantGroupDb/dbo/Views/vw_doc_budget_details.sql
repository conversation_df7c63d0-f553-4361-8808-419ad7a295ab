CREATE OR ALTER VIEW [dbo].[vw_doc_budget_details]
	AS 

SELECT fk_tenant_id,budget_year,aggregate_id,department_code,department_name,income_flag,fk_account_code,description,
SUM(accounting_data) AS accounting_data,SUM(budget_current) AS budget_current,SUM(budget_last_year) AS budget_last_year
FROM 
(SELECT td.fk_tenant_id, td.budget_year, aggregate_id = oh.org_id_2, td.department_code,dp.department_name,
ka.income_flag, td.fk_account_code, ac.description, 0 as accounting_data, 
td.amount_year_1 as budget_current, 0 as budget_last_year
FROM tbu_trans_detail td
JOIN tco_accounts ac ON td.fk_account_code = ac.pk_account_code AND td.fk_tenant_id = ac.pk_tenant_id
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code
JOIN tco_departments dp ON td.department_code = dp.pk_department_code AND td.fk_tenant_id = dp.fk_tenant_id AND td.budget_year BETWEEN dp.year_from AND dp.year_to
LEFT JOIN tco_org_version ov ON td.fk_tenant_id = ov.fk_Tenant_id and (td.budget_year)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on td.fk_tenant_id = oh.fk_Tenant_id and td.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
WHERE ka.type = 'operations'
UNION ALL
SELECT td.fk_tenant_id, td.budget_year+1, aggregate_id = oh.org_id_2, td.department_code,dp.department_name,
ka.income_flag, td.fk_account_code, ac.description, 
0 as accounting_data, 0 as budget_current,td.amount_year_1 as budget_last_year
FROM tbu_trans_detail td
JOIN tco_accounts ac ON td.fk_account_code = ac.pk_account_code AND td.fk_tenant_id = ac.pk_tenant_id
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code
JOIN tco_departments dp ON td.department_code = dp.pk_department_code AND td.fk_tenant_id = dp.fk_tenant_id AND td.budget_year BETWEEN dp.year_from AND dp.year_to
LEFT JOIN tco_org_version ov ON td.fk_tenant_id = ov.fk_Tenant_id and (td.budget_year+1)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on td.fk_tenant_id = oh.fk_Tenant_id and td.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
WHERE ka.type = 'operations'
UNION ALL
SELECT td.fk_tenant_id, td.gl_year + 2, aggregate_id = oh.org_id_2, td.department_code,dp.department_name,
ka.income_flag, td.fk_account_code, ac.description, td.amount as accounting_data, 0 as budget_current,
0 as budget_last_year
FROM tfp_accounting_data td
JOIN tco_accounts ac ON td.fk_account_code = ac.pk_account_code AND td.fk_tenant_id = ac.pk_tenant_id
JOIN gco_kostra_accounts ka ON ac.fk_kostra_account_code = ka.pk_kostra_account_code
JOIN tco_departments dp ON td.department_code = dp.pk_department_code AND td.fk_tenant_id = dp.fk_tenant_id AND td.gl_year BETWEEN dp.year_from AND dp.year_to
LEFT JOIN tco_org_version ov ON td.fk_tenant_id = ov.fk_Tenant_id and (td.gl_year+2)*100+1 between ov.period_from and ov.period_to
LEFT JOIN tco_org_hierarchy oh on td.fk_tenant_id = oh.fk_Tenant_id and td.department_code = oh.fk_department_code and ov.pk_org_version = oh.fk_org_version
WHERE ka.type = 'operations'
) S

GROUP BY fk_tenant_id,budget_year,aggregate_id,department_code,department_name,income_flag,fk_account_code,description
