CREATE OR ALTER VIEW [dbo].[vw_doc_investments_blist]

AS

SELECT 
a.fk_tenant_id
,budget_year
,fk_prog_code as fk_program_code
,program_code_description as description
,sub_header_code
,sub_header_description
,sum_code,sum_level
,sum_code_2
,sum_description_2
,sub_level_sa_code
,sub_level_sa_description
,fp_level_1_value
,fp_level_1_name
,fp_level_2_value
,fp_level_2_name
,type
,a.pk_main_project_code as pk_investment_id
,a.main_project_name as investment_name
,a.fk_department_code
,a.fk_function_code
,gross_cost
,financed_amount
,net_cost
,inv_year_1_amount as year_1_amount
,inv_year_2_amount as year_2_amount
,inv_year_3_amount as year_3_amount
,inv_year_4_amount as year_4_amount
,inv_year_5_amount as year_5_amount
,inv_year_6_amount as year_6_amount
,inv_year_7_amount as year_7_amount
,inv_year_8_amount as year_8_amount
,inv_year_9_amount as year_9_amount
,inv_year_10_amount as year_10_amount
,sum_finplan
,finished_year
,display_proj_columns
,fk_change_id
,a.inv_status
,approved_cost
,approval_reference
,approval_ref_url
,fin_year_1_amount
,fin_year_2_amount
,fin_year_3_amount
,fin_year_4_amount
,fin_total_amount
,previously_budgeted
,inv_phase = ISNULL(ph.description,'')
,oe_flag
,dynamic_gr_1=blist_gr_1
,dynamic_gr_2=blist_gr_2
,cost_estimate_p50
,mp.sync_status
FROM [tfp_inv_document_data] a
LEFT JOIN tco_investment_phase ph ON a.fk_tenant_id = ph.fk_tenant_id AND a.fk_investment_phase_id = ph.pk_investment_phase_id
LEFT JOIN tco_main_projects mp ON a.fk_tenant_id = mp.fk_tenant_id AND a.pk_main_project_code = mp.pk_main_project_code AND a.budget_year BETWEEN DATEPART(YEAR, mp.budget_year_from) AND DATEPART(YEAR, mp.budget_year_to)
WHERE a.inv_status = 3

GO