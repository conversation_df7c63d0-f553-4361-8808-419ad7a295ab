CREATE OR ALTER VIEW [dbo].[vw_mr_investment_graph_accounting]
AS 

SELECT fk_tenant_id,gl_year,period,type,level_1_id,level_1_name,investment_id,
investment_Name, org_l1_value, org_l1_name,
displayNextLevel, org_id_dept, fk_org_version, SUM(gl_amount) as gl_amount
FROM 
(SELECT td.fk_tenant_id, td.gl_year, td.period,   
type = CASE WHEN rl.line_item_id = 500 THEN 'Investering' else 'Finansiering' end,  
level_1_id = CASE WHEN rl.line_item_id != 500 THEN CONVERT(NVARCHAR(25), rl.line_item_id)  
   ELSE sv.service_id_2 END,   
level_1_name = CASE WHEN rl.line_item_id != 500 THEN rl.line_item  
   ELSE sv.service_name_2 END,   
investment_id = CASE WHEN rl.line_item_id != 500 THEN CONVERT(NVARCHAR(25), rl.line_item_id)  
   ELSE ISNULL(CONVERT(NVARCHAR(25),i.pk_investment_id), 'XX') END,  
investment_Name = CASE WHEN rl.line_item_id != 500 THEN CONVERT(NVARCHAR(25), rl.line_item_id)  
   ELSE ISNULL(mp.main_project_name, 'Uten prosjekt') END,  
org_l1_value = CASE WHEN rl.line_item_id != 500 THEN CONVERT(NVARCHAR(25), rl.line_item_id)  
   WHEN p2.param_value = '2' THEN ISNULL(i.fk_org_id, CASE WHEN p6.param_value = 'org_id_2' THEN oh.org_id_2  WHEN p6.param_value = 'org_id_3' THEN oh.org_id_3 WHEN p6.param_value = 'org_id_4' THEN oh.org_id_4 ELSE oh.org_id_2 END )  
   ELSE  oh.org_id_2 END,  
org_l1_name = CASE WHEN rl.line_item_id != 500 THEN rl.line_item  
   WHEN p2.param_value = '2' THEN ISNULL(i.org_name, CASE WHEN p6.param_value = 'org_id_2' THEN oh.org_name_2  WHEN p6.param_value = 'org_id_3' THEN oh.org_name_3 WHEN p6.param_value = 'org_id_4' THEN oh.org_name_4 ELSE oh.org_name_2 END)  
   ELSE  oh.org_name_2  END,  
displayNextLevel =  ISNULL(p5.param_value,1),  
org_id_dept = oh.org_id_2,oh.fk_org_version,
SUM(td.amount) as gl_amount  
FROM gmd_reporting_line rl  
 JOIN tco_accounts ac ON rl.fk_kostra_account_code = ac.fk_kostra_account_code  
 JOIN tfp_accounting_data td ON ac.pk_account_code = td.fk_account_code AND ac.pk_tenant_id= td.fk_tenant_id  
 LEFT JOIN tco_projects p ON td.fk_tenant_id = p.fk_tenant_id AND td.fk_project_code = p.pk_project_code AND td.gl_year BETWEEN DATEPART (year, p.date_from) AND DATEPART (year, p.date_to)  
 LEFT JOIN (SELECT DISTINCT a.fk_tenant_id, a.pk_investment_id, a.fk_main_project_code,b.budget_year, yc.fk_org_id, yc.org_name FROM tco_investments a, tco_investment_detail b, tco_inv_budgetyear_config yc WHERE a.fk_tenant_id = b.fk_tenant_id AND a.pk_investment_id = b.fk_investment_id AND b.fk_investment_id = yc.fk_investment_id AND b.fk_tenant_id = yc.fk_tenant_id AND b.budget_year = yc.budget_year ) i   
 ON i.fk_tenant_id = p.fk_tenant_id AND i.budget_year = td.gl_year AND i.fk_main_project_code = p.fk_main_project_code  AND i.fk_main_project_code != ''
 LEFT JOIN tco_main_projects mp ON mp.fk_tenant_id = p.fk_tenant_id AND mp.pk_main_project_code = p.fk_main_project_code AND td.gl_year BETWEEN DATEPART(YEAR, mp.budget_year_from) AND datepart(year,mp.budget_year_to)  
 LEFT JOIN tco_service_values sv ON td.fk_function_code = sv.fk_function_code AND td.fk_tenant_id = sv.fk_tenant_id  
--LEFT JOIN tco_org_version ov ON td.fk_tenant_id = ov.fk_Tenant_id
LEFT JOIN tco_org_hierarchy oh on td.fk_tenant_id = oh.fk_Tenant_id and td.department_code = oh.fk_department_code --and ov.pk_org_version = oh.fk_org_version
 LEFT JOIN tco_parameters p2 ON td.fk_tenant_id = p2.fk_tenant_id AND p2.param_name = 'BMDOC_2B_ORG_SUMMARY' AND p2.active = 1  
 LEFT JOIN tco_parameters p3 ON p3.fk_tenant_id = td.fk_tenant_id AND p3.param_name = 'INVGRAPH_FIN_NEED_ITEMID' AND p3.active = 1  
 LEFT JOIN tco_parameters p4 ON p4.fk_tenant_id = td.fk_tenant_id AND p4.param_name = 'INVGRAPH_FIN_NEED_AS_INV' AND p4.active = 1  
 LEFT JOIN tco_parameters p5 ON p5.fk_tenant_id = td.fk_tenant_id AND p5.param_name = 'MR_INVGRAPH_DISPLAY_LEVEL' AND p5.active = 1  
 LEFT JOIN tco_parameters p6 ON p6.fk_tenant_id = td.fk_tenant_id AND p6.param_name = 'BMDOC_2B_INV_PROG_LVL' AND p6.active = 1  
 WHERE rl.report = 'B2A'    
GROUP BY td.fk_tenant_id, td.gl_year, rl.line_group_id, rl.line_group, rl.line_item_id, rl.line_item, td.period,sv.service_id_2, oh.org_id_2,i.pk_investment_id, 
sv.service_name_2,oh.org_name_2, mp.main_project_name, p2.param_value, i.fk_org_id, i.org_name ,p5.param_value, oh.fk_org_version ,
oh.org_id_3, oh.org_name_3,oh.org_id_4, oh.org_name_4,p6.param_value) S

GROUP BY fk_tenant_id,gl_year,period,type,level_1_id,level_1_name,investment_id,
investment_Name,org_l1_value,org_l1_name,displayNextLevel,org_id_dept,fk_org_version

GO