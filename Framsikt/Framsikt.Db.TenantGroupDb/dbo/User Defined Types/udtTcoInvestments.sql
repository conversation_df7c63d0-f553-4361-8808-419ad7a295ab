DROP PROC IF EXISTS prcInsertIntoTcoInvestments
GO

DROP TYPE IF EXISTS [dbo].[udtTcoInvestments]  
GO

CREATE TYPE [dbo].[udtTcoInvestments] AS TABLE
(
	[fk_tenant_id] [int] NOT NULL,
	[caption_name] [nvarchar](200) NOT NULL,
	[status] [int] NULL,
	[self_cost_flag] [int] NULL,
	[budget_year_established] [int] NOT NULL,
	[start_year] [int] NOT NULL,
	[completion_date] [date] NOT NULL,
	[completion_pct ] [decimal](18, 2) NOT NULL,
	[approval] [nvarchar](50) NULL,
	[approval_date] [date] NULL,
	[responsible] [int] NULL,
	[fk_org_id] [nvarchar](25) NOT NULL,
	[org_name] [nvarchar](100) NOT NULL,
	[fk_project_id ] [nvarchar](25) NULL,
	[project_phase] [nvarchar](25) NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL,
	[fk_fdv_codes] [nvarchar](25) NULL,
	[unit_value] [decimal](18, 2) NULL,
	[previously_budgeted] [decimal](18, 2) NOT NULL,
	[previously_financing] [decimal](18, 2) NOT NULL,
	[monthrep_flag] [bit] NOT NULL,
	[fk_main_project_code] NVARCHAR(25) NOT NULL,
	[fk_portfolio_code] NVARCHAR(25) NOT NULL,
	[tags] NVARCHAR(4000)  NOT NULL,
	[goalId] uniqueidentifier NOT NULL,
	[original_finish_year] INT NOT NULL
)
GO