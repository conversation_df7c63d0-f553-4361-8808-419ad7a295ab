DROP PROC IF EXISTS prcInsertIntoTbuForecastTransactions
GO

DROP TYPE IF EXISTS [dbo].[udtTbuForecastTransactionsType]  
GO

create TYPE [dbo].[udtTbuForecastTransactionsType] AS TABLE(
    [pk_id] [uniqueidentifier] not null, 
    [forecast_period] [int] NOT NULL,
    [bu_trans_id] [uniqueidentifier] NOT NULL, 
    [fk_tenant_id] INT NOT NULL, 
    [action_type] INT NOT NULL, 
    [line_order] INT NOT NULL, 
	fk_account_code NVARCHAR (25) NOT NULL,
	department_code NVARCHAR (25) NOT NULL, 
	fk_function_code NVARCHAR (25) NOT NULL,
	fk_project_code NVARCHAR (25) NOT NULL,
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
	resource_id NVARCHAR (25) NOT NULL,
	fk_employment_id BIGINT NOT NULL,
	description NVARCHAR (255) NOT NULL,
    [budget_year] INT NOT NULL, 
    [period] INT NOT NULL, 
    [budget_type] INT NOT NULL, 
    [amount_year_1] DECIMAL(18, 2) NOT NULL, 
    [amount_year_2] DECIMAL(18, 2) NOT NULL, 
    [amount_year_3] DECIMAL(18, 2) NOT NULL, 
    [amount_year_4] DECIMAL(18, 2) NOT NULL, 
	fk_key_id INT NOT NULL,
	[allocation_pct] DECIMAL(9, 3) NOT NULL, 
    [total_amount] DECIMAL(18, 2) NOT NULL,
	[updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL,    
	[tax_flag] [int] NOT NULL,
	[holiday_flag] [int] NOT NULL,
	[fk_pension_type] [nvarchar](12) NULL,
	[fk_prog_code] [nvarchar](25) NULL
)
GO