DROP PROC IF EXISTS prcInsertIntoTfpTransHeader
GO

DROP TYPE IF EXISTS [dbo].[udtTfpTransHeader]  
GO

CREATE TYPE [dbo].[udtTfpTransHeader] AS TABLE
(
	[pk_action_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[description] [nvarchar](150) NOT NULL,
	[consequence] [nvarchar](max) NOT NULL,
	[start_date] [date] NOT NULL,
	[action_type] [int] NOT NULL,
	[action_source] [int] NOT NULL,
	[line_order] [int] NOT NULL,
	[isManuallyAdded] [int] NOT NULL,
	[title] [nvarchar](100) NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL,
	[fk_area_id] [int] NULL,
	[tag] [nvarchar](max) NULL,
	[long_description] [nvarchar](max) NULL,
	[priority] [int] NULL,
	[display_financial_plan_flag] [bit] NOT NULL,
	[display_description_apendix_flag] [bit] NOT NULL,
	[fk_cat_id] [uniqueidentifier] NOT NULL,
	[financial_plan_description] [nvarchar](max) NOT NULL,
	[consequence_flag] [bit] NOT NULL,
	[different_external_description_flag] [bit] NOT NULL,
	[change_text_flag] [bit] NOT NULL,
	[tags] [nvarchar](400) NULL,
	[monthly_report_flag] [bit] NOT NULL,
	[previous_pk_action_id] [int] NULL,
	[finished_date] [date] NULL
)
GO