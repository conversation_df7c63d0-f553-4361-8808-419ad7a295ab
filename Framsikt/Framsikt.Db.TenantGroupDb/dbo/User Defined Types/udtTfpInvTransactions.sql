DROP PROC IF EXISTS prcInsertIntoTfpInvTransactions
GO

DROP TYPE IF EXISTS [dbo].[udtTfpInvTransactions]  
GO

CREATE TYPE [dbo].[udtTfpInvTransactions] AS TABLE
(
	[pk_id] [int] NOT NULL,
	[fk_inv_action_id] [int] NOT NULL ,
	[fk_investment_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_function_code ] [nvarchar](25) NOT NULL,
	[department_code ] [nvarchar](25) NOT NULL,
	[vat_rate] [decimal](18, 2) NOT NULL,
	[vat_refund] [decimal](18, 2) NOT NULL,
	[year_1_amount] [decimal](18, 2) NOT NULL,
	[year_2_amount] [decimal](18, 2) NOT NULL,
	[year_3_amount] [decimal](18, 2) NOT NULL,
	[year_4_amount] [decimal](18, 2) NOT NULL,
	[updated] [datetime] NOT NULL,
	[type] [nvarchar](1) NOT NULL,
	[updated_by ] [int] NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[fk_change_id] [int] NOT NULL ,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[fk_inv_details_id] [int] NOT NULL,
	[fk_alter_code] [nvarchar](50) NOT NULL,
	[fk_adjustment_code] [nvarchar](50) NOT NULL,
	[fk_prog_code] [nvarchar](25) NULL
)
GO