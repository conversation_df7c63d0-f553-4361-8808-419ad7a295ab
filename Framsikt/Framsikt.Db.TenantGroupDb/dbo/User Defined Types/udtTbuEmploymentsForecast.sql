DROP PROC IF EXISTS prcInsertIntoTbuEmploymentForecast
GO

DROP TYPE IF EXISTS [dbo].[udtTbuEmploymentsForecast]  
GO

create TYPE [dbo].[udtTbuEmploymentsForecast] AS TABLE(
    [pk_employment_id] BIGINT NOT NULL, 
	[forecast_period] INT NOT NULL,
    [fk_tenant_id] INT NOT NULL, 
    [budget_year] INT NOT NULL, 
    [fk_res_id] INT NOT NULL, 
    [res_name] NVARCHAR(511) NOT NULL, 
    [fk_emp_type_id] INT NOT NULL, 
    [fk_position_id] NVARCHAR(25) NOT NULL, 
	[hjemmel_id] NVARCHAR(25) NOT NULL, 
    [pension_type] NVARCHAR(25) NOT NULL, 
    [fk_account_code] NVARCHAR(25) NOT NULL, 
    [fk_department_code] NVARCHAR(25) NOT NULL, 
    [fk_function_code] NVARCHAR(25) NOT NULL, 
    [fk_project_code] NVARCHAR(25) NOT NULL, 
	free_dim_1 NVARCHAR(25) NOT NULL,
	free_dim_2 NVARCHAR (25) NOT NULL,
	free_dim_3 NVARCHAR (25) NOT NULL,
	free_dim_4 NVARCHAR (25) NOT NULL,
    [amount_existing_salary] DECIMAL(18, 2) NOT NULL, 
    [amount_salary_month] DECIMAL(18, 2) NOT NULL, 
    [amount_salary_year] DECIMAL(18, 2) NOT NULL, 
    [start_period] INT NOT NULL, 
    [end_period] INT NOT NULL, 
    [position_pct] DECIMAL(18, 3) NOT NULL, 
    [seniority_date] DATE NOT NULL, 
    [yearly_budget] DECIMAL(18, 2) NOT NULL, 
    [amount_pension] DECIMAL(18, 2) NOT NULL, 
    [amount_holiday] DECIMAL(18, 2) NOT NULL, 
    [amount_aga_salary] DECIMAL(18, 2) NOT NULL, 
	[amount_aga_pension] DECIMAL(18, 2) NOT NULL, 
	[amount_aga_holiday] DECIMAL(18, 2) NOT NULL, 
	external_reference NVARCHAR(255) NOT NULL,
    [updated] DATETIME NOT NULL, 
    [updated_by] INT NOT NULL, 
	[fk_holiday_type_id] int not null,
	comments NVARCHAR(500) NOT NULL,
	[fk_add_on_comments] [uniqueidentifier]  NULL,
	[change_flag] bit not null,
	[forecast_change_flag] bit not null 
)
GO