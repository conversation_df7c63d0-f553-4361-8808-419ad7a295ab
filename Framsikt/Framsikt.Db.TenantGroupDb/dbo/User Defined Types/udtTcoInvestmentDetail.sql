DROP PROC IF EXISTS prcInsertIntoTcoInvestmentDetails
GO

DROP TYPE IF EXISTS [dbo].[udtTcoInvestmentDetail]  
GO

CREATE TYPE [dbo].[udtTcoInvestmentDetail] AS TABLE
(
	[fk_investment_id] [int] NOT NULL,
	[fk_tenant_id] [nvarchar](25) NOT NULL,
	[description] [nvarchar](500) NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[fk_department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[vat_rate] [decimal](18, 2) NOT NULL,
	[vat_refund] [decimal](18, 2) NOT NULL,
	[year_1_amount] [decimal](18, 2) NOT NULL,
	[year_2_amount] [decimal](18, 2) NOT NULL,
	[year_3_amount] [decimal](18, 2) NOT NULL,
	[year_4_amount] [decimal](18, 2) NOT NULL,
	[year_5_amount] [decimal](18, 2) NOT NULL,
	[year_6_amount] [decimal](18, 2) NOT NULL,
	[year_7_amount] [decimal](18, 2) NOT NULL,
	[year_8_amount] [decimal](18, 2) NOT NULL,
	[year_9_amount] [decimal](18, 2) NOT NULL,
	[year_10_amount] [decimal](18, 2) NOT NULL,
	[type] [nvarchar](2) NOT NULL,
	[updated] [datetime] NOT NULL,
	[updated_by] [int] NOT NULL,
	[existing_flag] [int] NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[budget_year] [int] NOT NULL,
	[fk_alter_code] [nvarchar](50) NOT NULL,
	[fk_adjustment_code] [nvarchar](50) NOT NULL,
	[fk_change_id] [int] NOT NULL,
	[inv_program] [nvarchar](100) NOT NULL,
	[approved_cost] [decimal](18,2)
)
GO