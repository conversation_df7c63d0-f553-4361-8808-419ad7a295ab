--/*
--Post-Deployment Script Template							
----------------------------------------------------------------------------------------
-- This file contains SQL statements that will be appended to the build script.		
-- Use SQLCMD syntax to include a file in the post-deployment script.			
-- Example:      :r .\myfile.sql								
-- Use SQLCMD syntax to reference a variable in the post-deployment script.		
-- Example:      :setvar TableName MyTable							
--               SELECT * FROM [$(TableName)]					
----------------------------------------------------------------------------------------
--*/


--/* Tables with out foreign keys*/
----------------------------------------------------------------------------------------
--/*Production Scripts*/
----------------------------------------------------------------------------------------
--:r .\scripts\populate_gco_kostra_accounts.sql
--:r .\scripts\populate_gco_language_strings.sql
--:r .\scripts\populate_gco_municipalities.sql
--:r .\scripts\populate_gco_region_codes.sql
--:r .\scripts\populate_gmd_action_types.sql
--:r .\scripts\populate_gmd_main_menu.sql
--:r .\scripts\populate_gmd_sub_menu.sql

----------------------------------------------------------------------------------------
--/*Staging Scripts*/
----------------------------------------------------------------------------------------
--:r .\scripts\populate_tco_accounts.sql
--:r .\scripts\populate_tco_functions.sql
--/*:r .\scripts\populate_tco_org_hierarchy.sql*/
--:r .\scripts\populate_tco_org_level.sql
--:r .\scripts\populate_tco_org_structure.sql
--:r .\scripts\populate_tmd_demographic_mapping.sql
--:r .\scripts\populate_tmd_forecast_type.sql
--:r .\scripts\populate_tmd_org_kostrafunc_mapping.SQL
--:r .\scripts\populate_tmd_pop_ind_mapping.SQL

--/*Tables with foreign keys*/
----------------------------------------------------------------------------------------
--/*Production Scripts*/
----------------------------------------------------------------------------------------
--:r .\scripts\populate_gco_tenants.sql
--:r .\scripts\populate_gmd_kostra_function.sql


--:r .\scripts\populate_gmd_age_intervals_mapping.sql
--:r .\scripts\populate_gmd_assessment_areas.sql
--:r .\scripts\populate_gmd_forecast_type.sql
--:r .\scripts\populate_gmd_reporting_areas.sql
--:r .\scripts\populate_gmd_kostra_lables.sql
--:r .\scripts\populate_gmd_indicator_defaults.sql

--:r .\scripts\populate_gmd_pop_indicator_intervals.sql

--:r .\scripts\populate_gmd_reporting_line.sql

----------------------------------------------------------------------------------------
--/*Staging Scripts*/
----------------------------------------------------------------------------------------
