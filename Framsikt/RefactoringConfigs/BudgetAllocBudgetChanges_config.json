{"sourceFile": "C:\\Users\\<USER>\\source\\repos\\146314\\Framsikt\\Framsikt.BL\\BudgetAllocBudgetChanges.cs", "destinationFolder": "C:\\Users\\<USER>\\source\\repos\\146314\\Framsikt\\Framsikt.BL\\BudgetAllocBudgetChanges", "newNamespace": "Framsikt.BL", "mainPartialClassName": "BudgetAllocBudgetChanges.Core.cs", "mainInterface": "", "partialClasses": [{"fileName": "BudgetAllocBudgetChanges.Initialization.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "List<KeyValuePair>", "name": "GetAdjustmentCodes", "arguments": ["string userId", "int budgetYear"]}, {"accessor": "public", "async": true, "returnType": "Task<List<KeyValuePair>>", "name": "GetAdjustmentCodesAsync", "arguments": ["string userId", "int budgetYear"]}, {"accessor": "public", "returnType": "List<KeyValuePair>", "name": "Get<PERSON>udgetChanges", "arguments": ["string userId", "int budgetYear"]}, {"accessor": "public", "async": true, "returnType": "Task<List<KeyValuePair>>", "name": "GetBudgetChangesAsync", "arguments": ["string userId", "int budgetYear"]}]}, {"fileName": "BudgetAllocBudgetChanges.GridData.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "JArray", "name": "GetGridData", "arguments": ["string userId", "BudAllocBudChngReqObj obj"]}, {"accessor": "public", "async": true, "returnType": "Task<JArray>", "name": "GetGridDataAsync", "arguments": ["string userId", "BudAllocBudChngReqObj obj"]}, {"accessor": "public", "returnType": "JArray", "name": "GetGridColumns", "arguments": ["string userId", "BudAllocBudChngReqObj obj"]}, {"accessor": "public", "async": true, "returnType": "Task<JArray>", "name": "GetGridColumnsAsync", "arguments": ["string userId", "BudAllocBudChngReqObj obj"]}, {"accessor": "public", "returnType": "JObject", "name": "CreateColumn", "arguments": ["string field", "string title", "int width", "string attributesStyle", "string headerAttributesStyle", "bool encoded", "string template", "string format", "bool hidden", "bool filterable", "string headerTemplate"]}]}, {"fileName": "BudgetAllocBudgetChanges.AdjustmentCodes.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "JArray", "name": "AdjustmentCodesData", "arguments": ["string userId", "BudAllocBudChngReqObj obj", "List<tbf_budget_form_framsikt_1A> framsikt1ADataAll", "List<tbf_budget_form_framsikt_1B> framsikt1BDataAll", "List<BudAllocBudChange> framsikt1AData", "List<BudAllocBudChange> framsikt1BData", "List<BudAllocBudChange> framsikt1BDataOrg", "List<tco_accounts> accounts", "List<gmd_reporting_line> gmdReportingLine", "List<AttributeDepartmentMap> chapter_values"]}, {"accessor": "public", "async": true, "returnType": "Task<JArray>", "name": "AdjustmentCodesDataAsync", "arguments": ["string userId", "BudAllocBudChngReqObj obj", "List<tbf_budget_form_framsikt_1A> framsikt1ADataAll", "List<tbf_budget_form_framsikt_1B> framsikt1BDataAll", "List<BudAllocBudChange> framsikt1AData", "List<BudAllocBudChange> framsikt1BData", "List<BudAllocBudChange> framsikt1BDataOrg", "List<tco_accounts> accounts", "List<gmd_reporting_line> gmdReportingLine", "List<AttributeDepartmentMap> chapter_values"]}]}, {"fileName": "BudgetAllocBudgetChanges.BudgetChangesData.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "JArray", "name": "BudgetChangesData", "arguments": ["string userId", "BudAllocBudChngReqObj obj", "List<tbf_budget_form_framsikt_1A> framsikt1ADataAll", "List<tbf_budget_form_framsikt_1B> framsikt1BDataAll", "List<BudAllocBudChange> framsikt1AData", "List<BudAllocBudChange> framsikt1BData", "List<BudAllocBudChange> framsikt1BDataOrg", "List<tco_accounts> accounts", "List<gmd_reporting_line> gmdReportingLine", "List<AttributeDepartmentMap> chapter_values"]}, {"accessor": "public", "async": true, "returnType": "Task<JArray>", "name": "BudgetChangesDataAsync", "arguments": ["string userId", "BudAllocBudChngReqObj obj", "List<tbf_budget_form_framsikt_1A> framsikt1ADataAll", "List<tbf_budget_form_framsikt_1B> framsikt1BDataAll", "List<BudAllocBudChange> framsikt1AData", "List<BudAllocBudChange> framsikt1BData", "List<BudAllocBudChange> framsikt1BDataOrg", "List<tco_accounts> accounts", "List<gmd_reporting_line> gmdReportingLine", "List<AttributeDepartmentMap> chapter_values"]}]}, {"fileName": "BudgetAllocBudgetChanges.ActionDetails.cs", "interface": "", "methods": [{"accessor": "public", "async": true, "returnType": "Task<JObject>", "name": "GetActionAdjCodeDetailsForCentralDepts", "arguments": ["string userId", "ActionAdjCodeDetailInput input"]}, {"accessor": "public", "async": true, "returnType": "Task<List<CentralDeptSummaryAmntHelper>>", "name": "GetSummaryAmtsForCentralDepts", "arguments": ["string userId", "int budgetYear", "List<ActionAdjCodeDetailHelper> actionDetailData"]}, {"accessor": "public", "async": true, "returnType": "Task<bool>", "name": "SaveActionAllocateStatus", "arguments": ["string userId", "SaveActionAllocateStatus obj"]}, {"accessor": "public", "async": true, "returnType": "Task<ActionStatusDetailHelper>", "name": "GetActionDetailsForBudAlloc", "arguments": ["string userId", "GetBudAllocActionDetailsObj obj"]}, {"accessor": "public", "async": true, "returnType": "Task<List<KeyValuePairString>>", "name": "GetBudgetAllocationTypeDropdown", "arguments": ["string userId"]}, {"accessor": "public", "async": true, "returnType": "Task<List<KeyValuePairString>>", "name": "GetBudgetAuthCodeDropdown", "arguments": ["string userId", "int budgetYear", "string pageId"]}, {"accessor": "public", "async": true, "returnType": "Task<Tuple<List<List<ActionDetailHelper>>, string, string>>", "name": "GetBudgetAllocationDetailForAction", "arguments": ["string userId", "int actionId", "string adjustmentCode", "int budgetYear", "string pageId", "int changeId"]}, {"accessor": "private", "returnType": "ActionDetailHelper", "name": "GetCentralDeptdata", "arguments": ["List<tfp_trans_detail> transDetail", "int Id"]}, {"accessor": "public", "async": true, "returnType": "Task<string>", "name": "SaveBudgetAllocationDetail", "arguments": ["string userId", "int budgetYear", "int actionId", "int changeId", "PageIdType pageId", "List<ActionDetailHelper> gridData", "bool isImport", "int Id", "bool updateTbu"]}, {"accessor": "public", "async": true, "returnType": "Task<List<string>>", "name": "GetHeaderInfo", "arguments": ["string userId", "int budgetYear", "List<List<ActionDetailHelper>> data"]}, {"accessor": "private", "returnType": "ActionWithBudgetChange", "name": "FormatInputToReUseOldServices", "arguments": ["int changeId", "int actionId", "int rowId", "int budgetYear", "ActionDetailHelper saveInput", "List<tfp_trans_detail> detailRows", "bool isDelete", "bool isImport", "int multiply"]}, {"accessor": "public", "async": true, "returnType": "Task<string>", "name": "DeleteActionDetailRow", "arguments": ["string userId", "int changeId", "int budgetYear", "List<ActionDetailHelper> inputLst"]}]}, {"fileName": "BudgetAllocBudgetChanges.ImportExport.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "ImportFileInfoHelper", "name": "ValidateImportColumns", "arguments": ["string userId", "DataSet dataSet", "JArray columns", "string selectedColumns"]}, {"accessor": "public", "async": true, "returnType": "Task<ImportFileInfoHelper>", "name": "ValidateImportColumnsAsync", "arguments": ["string userId", "DataSet dataSet", "JArray columns", "string selectedColumns"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "UploadImportFile", "arguments": ["string userId", "FormData input", "Stream ms"]}, {"accessor": "private", "returnType": "string", "name": "GetImportFileBlobPath", "arguments": ["int tenantId", "PageIdType pageId", "int budgetYear", "int actionId", "string mainProjCode", "string fileName"]}, {"accessor": "public", "async": true, "returnType": "Task<ImportFileInfoHelper>", "name": "ImportActionDetailRows", "arguments": ["string userId", "int budgetYear", "ImportInputHelper input"]}, {"accessor": "public", "async": true, "returnType": "Task<List<KeyValueDropDownData>>", "name": "GetProjectCodeData", "arguments": ["string userId", "int tenantId", "int usageType", "int budgetYear", "string projectCode"]}, {"accessor": "public", "returnType": "List<ActionDetailHelper>", "name": "FormatImportData", "arguments": ["string userId", "DataSet actionDataSet", "ValidationData validData", "bool update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "List<string> selectedColumns"]}, {"accessor": "private", "returnType": "string", "name": "FormatNode", "arguments": ["DataRow row", "int columnNumber", "bool allowNull"]}, {"accessor": "public", "async": true, "returnType": "Task<ImportErrorFileInfo>", "name": "ValidateActionDataSetForImportAsync", "arguments": ["string userId", "int budgetYear", "DataSet dsExcel", "MemoryStream importFile", "ImportInputHelper input", "ValidationData validData"]}, {"accessor": "public", "async": true, "returnType": "Task<ActionValidationResult>", "name": "ValidateDataSetAsync", "arguments": ["string userId", "DataSet actionDataSet", "ValidationData validData"]}, {"accessor": "private", "returnType": "List<string>", "name": "ValidateColumnLookup", "arguments": ["DataSet actionDataSet", "int columnNum", "IDictionary<string, string> validData", "IDictionary<string, clsLanguageString> langStrings", "bool allowNulls=false"]}, {"accessor": "public", "returnType": "List<string>", "name": "ValidateColumnNumericType", "arguments": ["DataSet actionDataSet", "int columnNum", "Dictionary<string, clsLanguageString> langStrings"]}, {"accessor": "public", "returnType": "MemoryStream", "name": "ExportAction", "arguments": ["ExportInputHelper dataToExport"]}, {"accessor": "private", "returnType": "void", "name": "InsertChildRows", "arguments": ["ExportInputHelper adminInvData", "Worksheet sheet"]}, {"accessor": "private", "returnType": "void", "name": "InsertActiveRows", "arguments": ["Worksheet sheet", "JArray coldef", "ActionDetailHelper rowData", "int rowNum"]}, {"accessor": "private", "returnType": "void", "name": "InsertActionHeaderRow", "arguments": ["Workbook wb", "dynamic columns"]}, {"accessor": "public", "async": true, "returnType": "Task<ValidationData>", "name": "GetValidationData", "arguments": ["string userId", "int actionType", "string mainProjCode", "string pageId", "int budgetYear", "List<string> selectedColumns", "bool isInv"]}, {"accessor": "private", "returnType": "string", "name": "Template", "arguments": ["string colName"]}, {"accessor": "private", "returnType": "string", "name": "HeaderTemplate", "arguments": ["string colField", "string colName", "string content"]}, {"accessor": "private", "returnType": "string", "name": "GetActionAllocatedStatus", "arguments": ["int statusId", "UserData userDetails"]}]}]}