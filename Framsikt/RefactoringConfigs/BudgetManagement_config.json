{"sourceFile": "C:\\Users\\<USER>\\source\\repos\\146314\\Framsikt\\Framsikt.BL\\BudgetManagement.cs", "destinationFolder": "C:\\Users\\<USER>\\source\\repos\\146314\\Framsikt\\Framsikt.BL\\BudgetManagement", "newNamespace": "Framsikt.BL", "mainPartialClassName": "BudgetManagement.Core.cs", "mainInterface": "", "partialClasses": [{"fileName": "BudgetManagement.TemplateTreeManagement1.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "PublishTemplateHelper", "name": "GetTreeForBmMasterTemplate", "arguments": ["string userId", "int budgetYear", "bool isBudgetProposalDoc=false", "bool isBudgetPropTexts=false", "bool isDocExportorWebPublish=false", "Guid? budgetPhaseId=null"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTemplateHelper>", "name": "GetTreeForBmMasterTemplateAsync", "arguments": ["string userId", "int budgetYear", "bool isBudgetProposalDoc=false", "bool isBudgetPropTexts=false", "StringBuilder sbPerformance=null", "bool isDocExportorWebPublish=false", "Guid? budgetPhaseId=null"]}, {"accessor": "public", "returnType": "AdminTemplateTreeHelper", "name": "GetBMMasterTemplateTree", "arguments": ["string userID", "int budgetYear"]}, {"accessor": "public", "returnType": "Boolean", "name": "DisplayTwoLevelTabsForBMMasterTemplate", "arguments": ["string userID", "int budgetYear"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTemplateHelper>", "name": "MergeBMAdminTwoLevelTreeData", "arguments": ["string userID", "AdminTemplateTreeHelper adminTreeData"]}, {"accessor": "private", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "ConstructDocConfigOneTree", "arguments": ["string userID", "int budgetYear", "bool isBudgetProposalDoc"]}, {"accessor": "private", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "ConstructDocConfigTwoTree", "arguments": ["string userID", "int budgetYear", "bool isBudgetProposalDoc"]}, {"accessor": "private", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "ConstructDocConfigTwoLevelTree", "arguments": ["string userID", "int budgetYear", "bool isBudgetProposalDoc", "bool isTwoLeveltree"]}, {"accessor": "private", "async": true, "returnType": "Task", "name": "ConstructServiceTableDataForNonServiceIdsAndServiceIds", "arguments": ["string userID", "List<PublishTreeNode> data"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "ConstructSecondLevelTreeForServiceIds", "arguments": ["string userID", "List<clsOrgIdsAndServiceIds> serviceIds", "bool isBudgetProposalDoc"]}]}, {"fileName": "BudgetManagement.TemplateTreeManagement2.cs", "interface": "", "methods": [{"accessor": "private", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "LoopThroughCustomNodesAsync", "arguments": ["string userId", "int budgetYear", "List<PublishTreeNode> exportTree", "IEnumerable<PublishTreeNode> baseTree", "int publishTemplateId", "List<tco_custom_node_master> lstCustomNodesMaster", "List<tco_doc_widget> lstWidgetData"]}, {"accessor": "private", "returnType": "void", "name": "LoopThroughCustomNodes2", "arguments": ["string userId", "List<PublishTreeNode> exportTree", "List<PublishTreeNode> lstCustomNodes"]}, {"accessor": "private", "returnType": "Dictionary<string, string>", "name": "CheckIfCustomNodeDocWidgetAdded", "arguments": ["Dictionary<string, string> parameters", "tco_doc_widget widgetData"]}, {"accessor": "private", "returnType": "void", "name": "CheckIfAnyNodeChecked", "arguments": ["IEnumerable<PublishTreeNodeMin> publishTreeNodes"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "DeleteTemplateByIdAsync", "arguments": ["string userId", "int templateId", "int budgetYear"]}, {"accessor": "public", "returnType": "int", "name": "CreatePublishTemplate", "arguments": ["string userId", "PublishTemplateHelperLazy templateData"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTemplateHelperLazy>", "name": "GetExportTreeMinAsync", "arguments": ["string userId", "int publishTemplateId", "int budgetYear", "bool useCache"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTemplateHelper>", "name": "GetBmExportTreeForMyTextAsync", "arguments": ["string userId", "int publishTemplateId", "int budgetYear"]}, {"accessor": "public", "returnType": "PublishTemplateHelper", "name": "GetExportTree", "arguments": ["string userId", "int publishTemplateId", "int budgetYear", "bool useCache", "bool isDocExportorWebPublish=false"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTemplateHelper>", "name": "GetExportTreeAsync", "arguments": ["string userId", "int publishTemplateId", "int budgetYear", "bool useCache", "bool isDocExportorWebPublish=false"]}]}, {"fileName": "BudgetManagement.BaseTreeFocusArea.cs", "interface": "", "methods": [{"accessor": "private", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "GetBaseTreeAsync", "arguments": ["string userId", "int budgetYear", "int docConfig", "bool useCache", "bool isDocExportorWebPublish=false", "Guid? budgetPhasId=null"]}, {"accessor": "private", "returnType": "PublishTreeNode", "name": "AddFocusAreaSectionNew", "arguments": ["string userId"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddFocusAreaSectionNewAsync", "arguments": ["string userId"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddPlanInfoSectionNewAsync", "arguments": ["string userId"]}, {"accessor": "private", "returnType": "PublishTreeNode", "name": "AddFocusAreaChildNodes", "arguments": ["string userId", "int budgetYear"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddFocusAreaChildNodesAsync", "arguments": ["string userId", "int budgetYear"]}, {"accessor": "private", "returnType": "void", "name": "ApplyFocusAreaMaterTemplate", "arguments": ["List<PublishTreeNode> baseTree", "PublishTemplateHelper updatedTemplate", "PublishTreeNode focusAreaSection"]}]}, {"fileName": "BudgetManagement.OrgTreesTemplateUpdates.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "IDictionary<Guid, IEnumerable<PublishTreeNode>>", "name": "GetOrgTreesForMasterTemplate", "arguments": ["string userId", "PublishTemplateHelper masterTemplate", "PublishTreeType publishTreeType"]}, {"accessor": "public", "async": true, "returnType": "Task<IDictionary<Guid, IEnumerable<PublishTreeNode>>>", "name": "GetOrgTreesForMasterTemplateAsync", "arguments": ["string userId", "PublishTemplateHelper masterTemplate", "PublishTreeType publishTreeType"]}, {"accessor": "public", "async": true, "returnType": "Task<IDictionary<Guid, IEnumerable<PublishTreeNode>>>", "name": "GetOrgTreesForAdminMasterTemplateAsync", "arguments": ["string userId", "PublishTemplateHelper masterTemplate", "PublishTreeType publishTreeType", "Guid budgetPhaseId"]}, {"accessor": "public", "returnType": "void", "name": "UpdateMasterTemplate", "arguments": ["string userId", "PublishTemplateHelper templateData"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "UpdateMasterTemplateAsync", "arguments": ["string userId", "PublishTemplateHelper templateData"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "UpdateCustomNodeInternalDescAsync", "arguments": ["string userId", "string nodeId", "string internalDesc", "string title"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "updateFocusAreaCustomNodeAsync", "arguments": ["string userId", "string nodeId", "string internalDesc", "string title"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "UpdateReportingWidgetNodeDataAsync", "arguments": ["string userId", "Guid nodeId", "int budgetYear", "string nodeTitle"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "UpdateStaticNodeInternalDescAsync", "arguments": ["string userId", "string nodeType", "string internalDesc", "int budgetYear"]}, {"accessor": "public", "returnType": "GetInternalDescPopupData", "name": "GetCustomNodeInternalDesc", "arguments": ["string userId", "string nodeId"]}, {"accessor": "public", "returnType": "GetInternalDescPopupData", "name": "GetReportingWidgetInternalDesc", "arguments": ["string userId", "int budgetYear", "Guid nodeId"]}, {"accessor": "public", "returnType": "GetInternalDescPopupData", "name": "GetStaticNodeInternalDesc", "arguments": ["string userId", "PublishTreeNode node", "int budgetYear"]}]}, {"fileName": "BudgetManagement.TreeNodeOperations.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "PublishTreeNode", "name": "GetServiceAreaNode", "arguments": ["string userId", "List<clsOrgIdsAndServiceIds> orgAndServiceIds", "clsOrgIdsAndServiceIds orgId", "int budgetYear", "List<DocTreeActionHelper> nonFilteredDeletedData", "MasterTemplateContent masterTemplateContent", "int orgLevel"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "GetServiceAreaNodeAsync", "arguments": ["string userId", "List<clsOrgIdsAndServiceIds> orgAndServiceIds", "clsOrgIdsAndServiceIds orgId", "int budgetYear", "List<DocTreeActionHelper> nonFilteredDeletedData", "MasterTemplateContent masterTemplateContent", "int orgLevel"]}, {"accessor": "public", "returnType": "Dictionary<string, string>", "name": "GetLimitCode", "arguments": ["dynamic newActionNames"]}, {"accessor": "public", "returnType": "PublishTreeNode", "name": "AddTreeNode", "arguments": ["string userId", "string id", "string text", "string type"]}, {"accessor": "public", "returnType": "PublishTreeNode", "name": "AddTreeNode", "arguments": ["string userId", "string id", "string text", "string type", "bool isDraggable", "string masterNodeId"]}, {"accessor": "public", "returnType": "PublishTreeNode", "name": "AddTreeNode", "arguments": ["string userId", "string id", "string text", "string type", "bool isDraggable"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddTreeNodeAsync", "arguments": ["string userId", "string id", "string text", "string type"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddTreeNodeAsync", "arguments": ["string userId", "string id", "string text", "string type", "bool isDraggable", "string masterNodeId"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddTreeNodeAsync", "arguments": ["string userId", "string id", "string text", "string type", "bool isDraggable"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddTreeNodeAsync", "arguments": ["string userId", "string id", "string text", "string type", "bool isDisabled", "bool isDraggable", "string masterNodeId"]}, {"accessor": "private", "async": true, "returnType": "Task", "name": "InitlizedataUserRolesAndActivities", "arguments": ["string userId"]}, {"accessor": "public", "returnType": "PublishTreeNode", "name": "AddStaticTreeNode", "arguments": ["string userId", "string id", "string type", "bool isDraggable"]}, {"accessor": "public", "returnType": "PublishTreeNode", "name": "AddStaticTreeNode", "arguments": ["string userId", "string id", "string type"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddStaticTreeNodeAsync", "arguments": ["string userId", "string id", "string type", "bool isDisabled", "bool isDraggable"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddStaticTreeNodeAsync", "arguments": ["string userId", "string id", "string type", "bool isDraggable"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddStaticTreeNodeAsync", "arguments": ["string userId", "string id", "string type", "bool isDraggable", "string budgetYear"]}, {"accessor": "public", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddStaticTreeNodeAsync", "arguments": ["string userId", "string id", "string type"]}, {"accessor": "private", "returnType": "PublishTreeNode", "name": "AddStaticTreeNode", "arguments": ["string userId", "string id", "string type", "bool isDisabled", "bool isDraggable"]}, {"accessor": "private", "returnType": "PublishTreeNode", "name": "AddStaticTreeNodeInternal", "arguments": ["string userId", "string id", "string type", "bool isDisabled", "bool isDraggable", "string budgetYear"]}, {"accessor": "private", "async": true, "returnType": "Task<PublishTreeNode>", "name": "AddStaticTreeNodeInternalAsync", "arguments": ["string userId", "string id", "string type", "bool isDisabled", "bool isDraggable", "string budgetYear"]}, {"accessor": "public", "returnType": "PublishTreeNode", "name": "AddTreeNodeFromDoc", "arguments": ["string id", "string text", "string nodeType", "bool expand", "bool isChecked"]}]}, {"fileName": "BudgetManagement.BudgetOverviewData1.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "dynamic", "name": "GetConsequenceAdjustedBudgetOverview", "arguments": ["string userId", "int budgetYear", "string budgetPhaseId", "bool divideByMillions=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetConsequenceAdjustedBudgetOverviewAsync", "arguments": ["string userId", "int budgetYear", "string budgetPhaseId", "bool divideByMillions=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetConsequenceAdjustedBudgetOverviewAsync", "arguments": ["string userId", "SimulatorHelper simulatorData", "string budgetPhaseId", "bool divideByMillions=false"]}, {"accessor": "private", "returnType": "decimal", "name": "GetNetRestultPercentage", "arguments": ["List<ConsequenceOverViewHelper> Budgetdata", "int year", "bool divideByMillions"]}, {"accessor": "public", "returnType": "string", "name": "GetPctTotalGrossExpenses", "arguments": ["string userId", "int budgetYear", "string orgId", "string serviceId", "string orgIdText", "string serviceIdText"]}, {"accessor": "public", "returnType": "string", "name": "GetBudgetByService", "arguments": ["string userId", "int budgetYear", "string orgId", "string serviceId", "bool isWebHelper", "bool isChapterSetup=false"]}, {"accessor": "private", "returnType": "List<BudgetByServiceDataHelper>", "name": "GetFunctionsDataSet", "arguments": ["string UserId", "int BudgetYear", "string orgId", "string serviceId"]}, {"accessor": "private", "returnType": "List<BudgetByServiceDataHelper>", "name": "GetChapterDataSet", "arguments": ["string UserId", "int BudgetYear", "string orgId", "string serviceId"]}, {"accessor": "private", "returnType": "List<BudgetByServiceDataHelper>", "name": "GetDepartmentsDataSet", "arguments": ["string UserId", "int BudgetYear", "string orgId", "string serviceId", "string budgetPhaseId"]}, {"accessor": "public", "returnType": "string", "name": "GetBudgetByDepartment", "arguments": ["string userId", "int budgetYear", "string orgId", "string serviceId", "bool isWebHelper", "string budgetPhaseId"]}, {"accessor": "public", "returnType": "void", "name": "WriteToDocDelReqQueue", "arguments": ["string docUrl", "string userId"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "WriteToDocDelReqQueueAsync", "arguments": ["string docUrl", "string userId"]}]}, {"fileName": "BudgetManagement.BudgetOverviewData2A.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "dynamic", "name": "GetServiceAreaBudgetData", "arguments": ["string yearSelected", "string userId", "int BudgetYear"]}, {"accessor": "public", "returnType": "dynamic", "name": "GetServiceAreaBudgetData", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataForBudgetPhaseAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}]}, {"fileName": "BudgetManagement.BudgetOverviewData2B.cs", "interface": "", "methods": [{"accessor": "public", "returnType": "dynamic", "name": "GetServiceAreaBudgetDataBasedOnFunctionView", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataBasedOnFunctionViewAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}, {"accessor": "public", "returnType": "dynamic", "name": "GetServiceAreaBudgetDataBasedOnDynamicOrgStructure", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool isChapterSetup", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataBasedOnDynamicOrgStructureAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool isChapterSetup", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataBasedOnDynamicOrgStructureForBudgetPhaseAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool isChapterSetup", "bool splitData=false"]}]}, {"fileName": "BudgetManagement.BudgetDataSpecificViews.cs", "interface": "", "methods": [{"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataBasedOnFunctionViewNewAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataBasedOnFunctionViewNewForBudgetPhaseAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear", "bool splitData=false"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "DeleteFromFilterBlobAsync", "arguments": ["string path"]}, {"accessor": "public", "returnType": "string", "name": "InsertJsonRequest", "arguments": ["string jsonData", "string userId", "string serviceUnitCode"]}, {"accessor": "public", "async": true, "returnType": "Task<string>", "name": "InsertJsonRequestAsync", "arguments": ["string jsonData", "string userId", "string serviceUnitCode"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "InsertFilterConditionInAzureAsync", "arguments": ["int userpkId", "string tenantId", "string serviceUnitId", "string filterJson"]}, {"accessor": "public", "returnType": "IEnumerable<ServiceAreaByBudget>", "name": "GetServiceAreaBudgetDataForDocument", "arguments": ["string yearSelected", "string userId", "int budgetYear", "string budgetPhaseId", "bool showOnlyModified"]}, {"accessor": "public", "async": true, "returnType": "Task<IEnumerable<ServiceAreaByBudget>>", "name": "GetServiceAreaBudgetDataForDocumentAsync", "arguments": ["string yearSelected", "string userId", "int budgetYear", "string budgetPhaseId", "bool showOnlyModified"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataFor1LevelAsync", "arguments": ["string yearSelected", "string userId", "string langPref", "int budgetYear"]}, {"accessor": "public", "async": true, "returnType": "Task<dynamic>", "name": "GetServiceAreaBudgetDataFor2LevelAsync", "arguments": ["string yearSelected", "string orgId", "string userId", "string langPref", "int budgetYear"]}, {"accessor": "private", "returnType": "IEnumerable<PublishTreeNode>", "name": "RemoveCustomNodes", "arguments": ["List<PublishTreeNode> treeData"]}]}, {"fileName": "BudgetManagement.SummarySettingsOperations.cs", "interface": "", "methods": [{"accessor": "public", "async": true, "returnType": "Task<JObject>", "name": "GetBListSummary", "arguments": ["string userId", "SimulatorHelper simulatedData", "bool divideByMillions=false"]}, {"accessor": "public", "async": true, "returnType": "Task<JObject>", "name": "GetFinPlanSummaryAsync", "arguments": ["string userId", "SimulatorHelper simulatedData", "bool divideByMillions=false"]}, {"accessor": "public", "async": true, "returnType": "Task<JArray>", "name": "GetCustomNodeSettingsAsync", "arguments": ["string userId", "int budgetYear", "int level", "PublishTreeType publishTreeType", "int forecastPeriod"]}, {"accessor": "private", "async": true, "returnType": "Task<JArray>", "name": "FormatCustomNodeSettingsAsync", "arguments": ["string userId", "int budgetYear", "int level", "UserData userDetails", "TenantDBContext dbContext", "PublishTemplateHelper defTemplate", "Dictionary<string, clsLanguageString> langStringValues", "List<TextNodesHelper> textNodeInfoLst", "PublishTreeType publishTreeType"]}, {"accessor": "private", "returnType": "Dictionary<string, string>", "name": "CheckIfDocWidgetAddedByNodeType", "arguments": ["string userId", "string nodeType", "Guid nodeId", "int budgetYear", "PublishTreeType publishTreeType", "Dictionary<Guid, tco_doc_widget> widgetDataDict"]}, {"accessor": "public", "async": true, "returnType": "Task<Dictionary<Guid, string>>", "name": "GetBudgetPhasesAsync", "arguments": ["string userId"]}, {"accessor": "private", "async": true, "returnType": "Task<List<ConsequenceOverViewHelper>>", "name": "GetFinPlanData", "arguments": ["string userId", "List<int> lineGroupId", "int budgetYear"]}, {"accessor": "private", "async": true, "returnType": "Task<List<ConsequenceOverViewHelper>>", "name": "GetBListAsync", "arguments": ["string userId", "List<int> lineGroupId", "int budgetYear", "SimulatorHelper simulatedData"]}, {"accessor": "private", "returnType": "List<PublishTreeNode>", "name": "sortFocusArea", "arguments": ["List<PublishTreeNode> focusAreaItem", "List<PublishTreeNode> treeDataItem"]}]}, {"fileName": "BudgetManagement.MiscellaneousOperations.cs", "interface": "", "methods": [{"accessor": "public", "async": true, "returnType": "Task", "name": "SaveCustomNodeMapingAsync", "arguments": ["string userId", "CustomNodeSettings inputData"]}, {"accessor": "public", "async": true, "returnType": "Task<List<ServiceAreaKeyValuePair>>", "name": "GetOrgServiceAreaDropDownDataAsync", "arguments": ["string userID", "int budgetYear"]}, {"accessor": "public", "returnType": "string", "name": "GetServiceAreaParentNodeType", "arguments": ["int docConfig"]}, {"accessor": "public", "returnType": "List<PublishTreeNode>", "name": "AddSelectAllNode", "arguments": ["string userID", "List<PublishTreeNode> treeData"]}, {"accessor": "public", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "AddSelectAllNodeAsync", "arguments": ["string userID", "List<PublishTreeNode> treeData"]}, {"accessor": "private", "returnType": "List<PublishTreeNode>", "name": "GetLevel2Data", "arguments": ["List<PublishTreeNode>? treeNodeData"]}, {"accessor": "public", "returnType": "TemplateCharacteristicsHelper", "name": "GetTemplateCharaHelper", "arguments": ["int tenantId", "int templateId", "int budgetYear"]}, {"accessor": "public", "async": true, "returnType": "Task<TemplateCharacteristicsHelper>", "name": "GetTemplateCharaHelperAsync", "arguments": ["int tenantId", "int templateId", "int budgetYear"]}, {"accessor": "public", "returnType": "string", "name": "SaveTemplateCharaHelper", "arguments": ["int tenantId", "int templateId", "int budgetYear", "TemplateCharacteristicsHelper inputObj"]}, {"accessor": "public", "returnType": "PublishTemplateHelper", "name": "GetTemplateDetails", "arguments": ["int tenantId", "int templateId"]}, {"accessor": "public", "async": true, "returnType": "Task<List<PublishTreeNode>>", "name": "GenerateStaticNodeMapping", "arguments": ["string userId", "int budgetYear"]}, {"accessor": "private", "static": true, "returnType": "void", "name": "AddMappings", "arguments": ["UserData userDetails", "int budgetYear", "List<NodeContainer> flatTreeData", "int currentLevel", "List<tco_static_node_budphase_mapping> staticNodes", "List<tco_static_node_budphase_mapping> staticNodeMappings", "List<tco_budget_phase> budgetPhaseList", "HashSet<string> existingNodeIds"]}, {"accessor": "public", "async": true, "returnType": "Task<JArray>", "name": "GetBMCustomNodeSettingsAsync", "arguments": ["string userId", "int budgetYear", "int level", "PublishTreeType publishTreeType", "Guid budgetPhaseId", "bool showStaticNodes", "PublishTemplateHelper defTemplate", "bool showNonDocVersionNodes"]}, {"accessor": "private", "returnType": "Dictionary<string, Task<string>>", "name": "GetBudgetPhaseTextByNodeIdTask", "arguments": ["List<NodesHelper> textNodeIdLst", "int tenantId", "int templateId", "int level", "PublishTreeType publishTreeType", "Guid budgetPhaseId"]}, {"accessor": "private", "returnType": "bool", "name": "GetStaticNodeSetup", "arguments": ["List<tco_static_node_budphase_mapping> staticNodes", "string nodeType", "KeyValuePair<Guid, string> budPhase"]}, {"accessor": "private", "returnType": "bool", "name": "GetCustomNodeWidgetSetup", "arguments": ["List<tco_custom_node_budphase_mapping> customNodes", "Guid nodeId", "KeyValuePair<Guid, string> budPhase"]}, {"accessor": "public", "async": true, "returnType": "Task", "name": "SaveNodeOrderingByBudgetphase", "arguments": ["string userId", "NodeReOrdering inputData"]}, {"accessor": "public", "async": true, "returnType": "Task<IDictionary<Guid, OrgSelectionStaticNodes>>", "name": "GetOrgTreesForMasterTemplateStaticNodes", "arguments": ["string userId", "PublishTemplateHelper masterTemplate"]}, {"accessor": "public", "async": true, "returnType": "Task<IDictionary<Guid, OrgSelectionStaticNodes>>", "name": "GetOrgTreesForAdminMasterTemplateStaticNodes", "arguments": ["string userId", "PublishTemplateHelper masterTemplate", "Guid budgetPhaseId"]}, {"accessor": "private", "async": true, "returnType": "Task", "name": "ProcessNodes", "arguments": ["IEnumerable<NodeContainer> nodes", "int level", "IDictionary<Guid, OrgSelectionStaticNodes> orgTrees", "int masterTemplateId", "int tenantId", "Guid budgetPhaseId"]}]}]}