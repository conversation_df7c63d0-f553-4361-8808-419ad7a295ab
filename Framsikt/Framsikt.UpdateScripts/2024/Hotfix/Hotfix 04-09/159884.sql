

INSERT INTO tco_progress_status (fk_tenant_id, status_id, status_description, negative_flag, type, active, updated, updated_by, finished_flag, image_path, fk_public_status_id, import_flag, monthrep_flag, is_local)
SELECT
fk_tenant_id = t.pk_id
,status_id = 1
,status_description = 'Lav'
,negative_flag = 0
,type = 'MONTHREP_INV_RISK'
,active = 1
,updated = GETDATE()
,updated_by = 1132
,finished_flag = 0
,image_path = ''
,fk_public_status_id = 0
,import_flag = 0
,monthrep_flag = 0
,is_local = 0
FROM gco_tenants t
WHERE NOT EXISTS (select*from tco_progress_status a where a.type = 'MONTHREP_INV_RISK' and t.pk_id = a.fk_tenant_id and a.status_id = 1)


INSERT INTO tco_progress_status (fk_tenant_id, status_id, status_description, negative_flag, type, active, updated, updated_by, finished_flag, image_path, fk_public_status_id, import_flag, monthrep_flag, is_local)
SELECT
fk_tenant_id = t.pk_id
,status_id = 2
,status_description = 'Middels'
,negative_flag = 0
,type = 'MONTHREP_INV_RISK'
,active = 1
,updated = GETDATE()
,updated_by = 1132
,finished_flag = 0
,image_path = ''
,fk_public_status_id = 0
,import_flag = 0
,monthrep_flag = 0
,is_local = 0
FROM gco_tenants t
WHERE NOT EXISTS (select*from tco_progress_status a where a.type = 'MONTHREP_INV_RISK' and t.pk_id = a.fk_tenant_id and a.status_id = 2)


INSERT INTO tco_progress_status (fk_tenant_id, status_id, status_description, negative_flag, type, active, updated, updated_by, finished_flag, image_path, fk_public_status_id, import_flag, monthrep_flag, is_local)
SELECT
fk_tenant_id = t.pk_id
,status_id = 3
,status_description = 'Høy'
,negative_flag = 0
,type = 'MONTHREP_INV_RISK'
,active = 1
,updated = GETDATE()
,updated_by = 1132
,finished_flag = 0
,image_path = ''
,fk_public_status_id = 0
,import_flag = 0
,monthrep_flag = 0
,is_local = 0
FROM gco_tenants t
WHERE NOT EXISTS (select*from tco_progress_status a where a.type = 'MONTHREP_INV_RISK' and t.pk_id = a.fk_tenant_id and a.status_id = 3)

