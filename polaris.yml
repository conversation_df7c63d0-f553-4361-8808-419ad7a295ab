version: "1"
project:
  name: Framsikt
  branch: origin/main
  revision:
    name: ${scm.git.commit}
    date: ${scm.git.commit.date}
  properties:
    GROUP: 3tbb8ccdm11h74eq2s3ien1rv8
    PSC_ID: "1215"
capture:
  build:
    cleanCommands:
    - shell: [dotnet, clean, Framsikt/FramsiktWebRole.Core.sln]
    buildCommands:
    - shell: [dotnet, build, Framsikt/FramsiktWebRole.Core.sln]
  fileSystem:
    javascript:
      files:
      - directory: ${project.projectDir}
      - excludeRegex: node_modules|bower_components|vendor|Scripts|dompurify|^[\s\S]*-old.js$
    typescript:
      files:
      - directory: ${project.projectDir}
analyze:
   # if mode is local, then the results only gets partially submitted, the "Last snapshot submitted" doesn't get updated for some reason
  #  this will lead to 300 points in the security maturity index, so please leave it as central
  mode: central
  coverity:
    # This should increase the quality of the scan only available from Coverity 2021.01
    usebudaforjavadotnet: enable

    cov-analyze: [
      "--disable-default",  # --disable-default - since this removes the quality checkers, they don't count towards the security maturity index, so no need to scan for them
      "--webapp-security",  # --webapp-security - to enable web application security checkers again
      "--security",         # --security - to enable whatever security checkers that are not web application related
      "--disable", "CSRF"   # --disable CSRF - disable Cross Site Request Forgery, it has way too many false positives for it to be of much use
      ]
install:
  coverity:
    version: default
serverUrl: https://visma.polaris.synopsys.com
